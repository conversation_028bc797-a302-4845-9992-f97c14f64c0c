/*
 * @Author: <PERSON>
 * @Date: 2021-12-14 15:18:11
 * @Last Modified by: <PERSON>
 * @Last Modified time: 2021-12-14 15:36:57
 */

// 导入封装好的axios实例
import request from './request'
import store from '@/store'
import qs from 'qs'
function getUserCode() {
  let userCode = {}
  if (store.getters.isLogin) {
    const userInfo = store.state.loginInfo
    userCode = {
      hospitalCode: userInfo.user.hospitalCode,
      unitCode: userInfo.user.unitCode
    }
    return userCode
  }
}
// type = info为所有信息，为code或者其他则为只要code
function getIMESUserCode(type = 'info') {
  let userCode = {}
  if (store.getters.isLogin) {
    // const userInfo = store.state.imesLoginInfo
    userCode = {
      unitCode: 'BJSYYGLJ',
      hospitalCode: 'BJSJTY'
    }
    const userInfon = {
      userId: 'd6aeca8128fa400c8f60f9c69402d0b3',
      userName: '系统管理员',
      roleCode: 'BJSJTY_systemAdminCode_IMES_CORE',
      officeCode: '8afe0f36c9ea4e46a35f2c00bc2929cb',
      sysIdentity: 'systemAdminCode',
      departmentAssetAdminCode: '8afe0f36c9ea4e46a35f2c00bc2929cb',
      moduleIdentity: 'IMES_CORE',
      sysCome: 2,
      userType: 1,
      sysFlag: 'imes',
      permissionsType: 1,
      onOff: 1
    }
    if (type === 'info') {
      return { ...userCode, ...userInfon }
    } else {
      return userCode
    }
  }
}
// 使用公共方法获取提交公共参数
function getCommonData(commonType) {
  let commonData = {}
  if (commonType === 'ihcrs') {
    commonData = getUserCode()
  } else if (commonType === 'imes') {
    commonData = getIMESUserCode('info')
  } else if (commonType === 'imesCode') {
    commonData = getIMESUserCode('code')
  } else if (commonType === 'plan') {
    commonData = {}
  }
  // 中文编码
  var reg = /[\u4e00-\u9fa5]/
  for (const item in commonData) {
    if (reg.test(commonData[item])) {
      commonData[item] = encodeURIComponent(commonData[item])
    }
  }
  return commonData
}
const http = {
  /**
   * methods: 请求
   * @param url 请求地址
   * @param params 请求参数
   */
  get(url, params) {
    const userCode = getUserCode()
    const config = {
      method: 'get',
      url: url,
      body: {
        ...params,
        ...userCode
      },
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded;charset=utf8'
      }
    }
    // if (params) config.params = params
    return request(config)
  },
  getRequest(url, params, headers = {}) {
    const userCode = getUserCode()
    const config = {
      method: 'get',
      url: url,
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded;charset=utf8',
        ...userCode,
        ...headers,
        path: 'iemc2'
      }
    }
    config.params = { ...params, ...userCode }
    return request(config)
  },
  // commonType用来判断走资产还是网关的authweb的公共参数
  getQueryQS(url, params = {}, headers = {}, commonType = 'ihcrs') {
    const userCode = getCommonData(commonType)
    Object.assign(params, userCode)
    const config = {
      method: 'get',
      url: url + '?' + qs.stringify(params),
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded;charset=utf8',
        ...headers,
        ...userCode
      }
    }
    return request(config)
  },
  post(url, params) {
    const userCode = getUserCode()
    const config = {
      method: 'post',
      url: url,
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded;charset=utf8'
      }
    }
    config.data = { ...params, ...userCode }
    return request(config)
  },
  postFile(url, params) {
    const config = {
      method: 'post',
      url: url,
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded;charset=utf8'
      }
    }
    config.data = params
    return request(config)
  },
  // commonCode用来判断是否需要走公共参数 也可用来根据false走资产的公共参数
  postRequest(url, params, headers = {}, commonType = 'ihcrs') {
    const userCode = getCommonData(commonType)
    const config = {
      method: 'post',
      url: url,
      headers: {
        ...headers,
        ...userCode,
        path: 'iemc2'
      }
    }
    config.data = { ...params, ...userCode }
    return request(config)
  },
  // commonCode用来判断是否需要走公共参数 也可用来根据false走资产的公共参数
  postParamsQS(url, params, headers = {}, commonType = 'ihcrs') {
    const userCode = getCommonData(commonType)
    const config = {
      method: 'post',
      url: url,
      headers: {
        ...headers
      }
    }
    config.data = qs.stringify({ ...params, ...userCode })
    return request(config)
  },
  postParamsRequest(url, params, headers = {}) {
    const userCode = getUserCode()
    // const userCode = {
    //   hospitalCode: 'bjsjty',
    //   unitCode: 'bjsyyglj'
    // }
    const config = {
      method: 'post',
      url: url,
      headers: {
        ...userCode,
        ...headers
      }
    }
    config.data = { ...params, ...userCode }
    return request(config)
  },
  requestPost(url, params, headers = {}, commonType = 'ihcrs') {
    const userCode = getCommonData(commonType)
    const config = {
      method: 'post',
      url: url,
      headers: {
        'Content-Type': 'application/json',
        ...headers,
        ...userCode
      }
    }
    config.data = { ...params, ...userCode }
    return request(config)
  },
  requestPostBlob(url, params, headers = {}, commonType = 'ihcrs') {
    const userCode = getCommonData(commonType)
    const config = {
      method: 'post',
      url: url,
      headers: {
        'Content-Type': 'application/json',
        ...headers,
        ...userCode
      },
      responseType: 'blob'
    }
    config.data = { ...params, ...userCode }
    return request(config)
  },
  loginRequest(url, params, headers = {}) {
    const config = {
      method: 'post',
      url: url,
      headers: {
        'Content-Type': 'application/json',
        ...headers
      }
    }
    if (params) config.data = params
    return request(config)
  },
  blobPost(url, params) {
    const userCode = getUserCode()
    const config = {
      method: 'post',
      url: url,
      headers: {
        'Content-Type': 'application/json'
      },
      responseType: 'blob'
    }
    config.data = { ...params, ...userCode }
    return request(config)
  },
  postFormData(url, params, headers) {
    const userCode = getUserCode()
    const formdata = new FormData()
    Object.assign(params, userCode)
    for (var item in params) {
      formdata.append(item, params[item] || '')
    }
    const config = {
      method: 'post',
      url: url,
      headers: {
        'Content-Type': 'application/json;charset=UTF-8',
        ...headers
      },
      data: formdata
    }
    return request(config)
  },
  websocketService(url) {
    return new WebSocket(url)
  },
  postQueryQS(url, params, headers = {}, commonType = 'ihcrs') {
    const userCode = getCommonData(commonType)
    const config = {
      method: 'post',
      url: url + '?' + qs.stringify({...params, ...userCode}),
      headers: {
        'Content-Type': 'application/json',
        ...headers,
        ...userCode
      }
    }
    return request(config)
  }
}
// 导出
export default http
