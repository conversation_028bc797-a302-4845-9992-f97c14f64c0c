<template>
  <div class="equipmentAntiTheft">
    <div ref="circle_btn" @click="collapseHeight" :class="collapseFlag ? 'circle-btn-top' : 'circle-btn-bottom'" class="circle-btn circle-btn-top"></div>
    <div ref="collapseHeight" class="bottom_table">
      <div class="table_left">
        <div class="top_title">
          <span class="dialog-title">服务于空间的设备</span>
        </div>
        <div class="table_left_content">
          <el-table
            class="table_list"
            :data="tableData"
            height="100%"
            :cell-style="$tools.setCell(3)"
            :header-cell-style="$tools.setHeaderCell(3)"
            style="width: 100%"
            element-loading-background="rgba(0, 0, 0, 0.2)"
          >
            <el-table-column align="center" label="操作" width="160">
              <template slot-scope="scope">
                <span style="color: #5188fc;text-decoration: underline;cursor: pointer;" @click="viewMap(scope.row,0)">查看地图</span>
                <!-- <span style="color: #5188fc;text-decoration: underline;cursor: pointer;margin-left: 10px;" @click="viewMap(scope.row,1)">移动轨迹</span> -->
              </template>
            </el-table-column>
            <el-table-column prop="statusName" align="center" show-overflow-tooltip label="资产状态"></el-table-column>
            <el-table-column prop="useStatusName" align="center" show-overflow-tooltip label="使用状态"></el-table-column>
            <el-table-column prop="assetsNumber" align="center" show-overflow-tooltip label="资产编码"></el-table-column>
            <el-table-column prop="assetsName" align="center" show-overflow-tooltip label="资产名称"></el-table-column>
            <el-table-column prop="model" align="center" show-overflow-tooltip label="规格型号"></el-table-column>
            <el-table-column prop="money" align="center" show-overflow-tooltip label="资产原值"></el-table-column>
            <el-table-column prop="supplierName" align="center" show-overflow-tooltip label="供应商"></el-table-column>
            <el-table-column prop="useDepartmentName" align="center" show-overflow-tooltip label="使用科室"></el-table-column>
            <el-table-column prop="buyDate" align="center" show-overflow-tooltip label="启用日期"></el-table-column>
            <el-table-column prop="warranties" align="center" show-overflow-tooltip label="维保到期"></el-table-column>
            <el-table-column prop="manufacturerName" align="center" show-overflow-tooltip label="生成厂家"></el-table-column>
            <el-table-column prop="brandName" align="center" show-overflow-tooltip label="品牌"></el-table-column>
            <el-table-column prop="storageLocation" align="center" show-overflow-tooltip label="存放位置"></el-table-column>
          </el-table>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { getSearchLabelCard, getAssetsPosition } from '@/utils/equipmentApi'
export default {
  name: 'equipmentAntiTheft',
  data() {
    return {
      collapseFlag: true,
      tableData: []
    }
  },
  computed: {},
  created() {},
  mounted() {
    try {
      window.chrome.webview.hostObjects.sync.bridge.TableVis(true)
    } catch {}
    this.getList()
  },
  methods: {
    getList() {
      var data = {
        type: 'position',
        deleteFlag: 0,
        offSet: 1,
        pageSize: 100,
        name: ''
      }
      getSearchLabelCard(data).then(res => {
        this.tableData = res.data.data.list
      })
    },
    collapseHeight() {
      this.$nextTick(() => {
        try {
          window.chrome.webview.hostObjects.sync.bridge.TableVis(!this.collapseFlag)
        } catch {}
        if (this.collapseFlag) {
          this.collapseFlag = !this.collapseFlag
          this.$refs.circle_btn.style.top = '90%'
          this.$refs.collapseHeight.style.height = '0'
          this.$refs.collapseHeight.style.padding = '0'
        } else {
          this.collapseFlag = !this.collapseFlag
          this.$refs.circle_btn.style.top = 'calc(60% + 7px)'
          this.$refs.collapseHeight.style.height = '40%'
          this.$refs.collapseHeight.style.padding = '2px'
        }
      })
    },
    // 查看地图
    viewMap(row, isClickType) {
      try {
        getAssetsPosition({ assetsId: row.assetsId }).then(res => {
          const data = {
            // 资产设备id
            id: row.assetsId,
            // 0查看地图1移动轨迹
            isClickType: isClickType,
            // 位置id
            locationId: res.data.data.gridId || row.gridId,
            locationCode: res.data.data.gridCode || row.gridCode
          }
          console.log('data=>', data)
          window.chrome.webview.hostObjects.sync.bridge.AntiTheftClick(JSON.stringify(data))
        })
      } catch (errpr) {}
    }
  }
}
</script>

<style lang="scss" scoped>
.equipmentAntiTheft{
  width: 100%;
  height: 100%;
  position: relative;
  .bottom_table {
    transition: height 0.3s linear;
    overflow: hidden;
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 100%;
    height: 40%;
    background: #031553 url('~@/assets/images/center/mask-bottom-bg.png') no-repeat;
    background-size: 100% 100%;
    padding: 2px;
    box-sizing: border-box;
    display: flex;
    .table_left {
      flex: 1;
    }
    .table_left_content {
      width: 100%;
      height: calc(100% - 2.5rem);
      ::v-deep .el-table {
        tr {
          background: center;
        }
        .el-table__header thead th {
          background: rgba(46, 73, 137, 0.1) !important;
        }
        div.el-table__fixed-body-wrapper,
        div.el-table__body-wrapper {
          background: rgba(2, 19, 82, 0.2);
        }
      }
    }
  }
  .top_title {
    height: 2.5rem;
    line-height: 2.5rem;
    padding-left: 0.9375rem;
    .dialog-title {
      color: #fff;
      font-family: PingFangSC-Medium, PingFang SC;
    }

    .dialog-title::before {
      content: '';
      display: inline-block;
      width: 2px;
      height: 16px;
      background: #ffe3a6;
      margin-right: 10px;
      vertical-align: middle;
    }
  }
  .circle-btn {
    transition: top 0.3s linear;
    position: absolute;
    top: calc(60% + 8px);
    left: 50%;
    width: 26px !important;
    height: 26px !important;
    padding: 0px;
    cursor: pointer;
    margin: auto 0;
    z-index: 100;
  }
  .circle-btn-top {
    background: url('~@/assets/images/center/btn-fold.png') no-repeat;
    background-size: 100% 100%;
    transform: rotate(90deg);
  }
  .circle-btn-bottom {
    background: url('~@/assets/images/center/btn-fold.png') no-repeat;
    background-size: 100% 100%;
    transform: rotate(-90deg);
  }
}
</style>
