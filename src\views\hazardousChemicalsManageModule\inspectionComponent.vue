<template>
  <div class="inspectionComponent">
    <div class="module-container" style="height: calc(30%)">
      <div class="module-header">
        <div class="title-left">
          <p class="title-left-text">巡检任务统计</p>
        </div>
        <div class="title-right">
          <el-dropdown trigger="click" @command="(e) => handleDeployCommand(e)">
            <span class="el-dropdown-link"> {{ dateTypeName }} <i class="el-icon-arrow-down"></i> </span>
            <el-dropdown-menu slot="dropdown">
              <el-dropdown-item v-for="(item, index) in dateList" :key="index" :command="item.val"
                                :class="{ isBjxl: dateType == item.val }">{{ item.name }}</el-dropdown-item>
            </el-dropdown-menu>
          </el-dropdown>
          <div class="icon-box">
            <img src="../../assets/images/order_more.png" class="order-more" @click="allTableChange" />
          </div>
        </div>
      </div>
      <div class="module-content chartContent">
        <div id="pie_rankStatistics" style="width: 100%; height: 100%"></div>
      </div>
    </div>
    <div class="module-container" style="height: calc(30%)">
      <div class="module-header">
        <div class="title-left">
          <p class="title-left-text">巡查任务类型</p>
        </div>
      </div>
      <div class="module-content " style="height: calc(100% - 44px)">
        <div id="xj_taskTypeEchart" style="height: 100%; overflow: hidden"></div>
      </div>
    </div>
    <div class="module-container" style="height: calc(40%)">
      <div class="module-header">
        <div class="title-left">
          <p class="title-left-text">巡查任务状态</p>
        </div>
        <div class="title-right">
          <div class="icon-box">
            <img src="../../assets/images/filter.png" class="filter" @click="showCheckGroup = !showCheckGroup" />
            <div v-show="showCheckGroup" v-scrollbarHover class="panel-s">
              <el-checkbox-group v-model="checkTypeList" fill="#52FFFC" @change="checkBoxChanged">
                <el-checkbox v-for="item in typeList" :key="item.value" :label="item.label"></el-checkbox>
              </el-checkbox-group>
            </div>
          </div>
          <div>
            <img src="../../assets/images/order_more.png" class="order-more" @click="allTableChange" />
          </div>
        </div>
      </div>
      <div class="module-content" style="height: calc(100% - 44px)">
        <el-table
          v-el-table-infinite-scroll="tableLoadMore" v-loading="tableLoading" class="table-center-transfer"
          :data="tableData" height="100%"
          :cell-style="{ padding: ' 8px', backgroundColor: 'transparent', border: 'none', padding: '3px' }"
          :header-cell-style="{ background: '#8591CE26!important', color: '#8BDDF5FF', padding: '4px 8px', fontWeight: 'bold' }"
          style="width: 100%"
          element-loading-background="rgba(0, 0, 0, 0.2)"
          @row-dblclick="toView"
        >
          <el-table-column prop="taskName" label="任务名称" show-overflow-tooltip></el-table-column>
          <el-table-column prop="cycleType" width="50" label="周期" show-overflow-tooltip></el-table-column>
          <el-table-column prop="distributionTeamName" width="80" label="巡检部门" show-overflow-tooltip></el-table-column>
          <el-table-column prop="taskStatus" width="80" label="完成状态" show-overflow-tooltip></el-table-column>
        </el-table>
      </div>
    </div>
    <inspectionDetail
      ref="inspectionDetail"
      systemType="xjrw"
      taskType="insp"
      :dataInfo="taskkDetailObj"
      @closeDialog="closeInspPointDialog"
    ></inspectionDetail>
  </div>
</template>
<script >
import * as echarts from 'echarts'
import dayjs from 'dayjs'
import inspectionDetail from '../spaceManage/sysTypeComponent/components/inspectionDetail.vue'
export default {
  name: 'inspectionComponent',
  components: {
    inspectionDetail
  },
  props: {
    roomData: {
      type: Object,
      default: () => { }
    }
  },
  data() {
    return {
      showCheckGroup: false,
      sortType: '',
      ssmCodes: [],
      tableLoading: false,
      tableData: [
        {
          id: '1818610477058244612',
          taskName: '重点区域巡查20241214第10次',
          cycleRole: 12,
          cycleType: '每日',
          distributionTeamName: '保安部',
          executeStartTime: '',
          executeEndTime: '',
          taskStatus: '已完成'
        },
        {
          id: '1818610477075021841',
          taskName: '重点区域巡查20241214第11次',
          cycleRole: 12,
          cycleType: '每日',
          distributionTeamName: '保安部',
          executeStartTime: '',
          executeEndTime: '',
          taskStatus: '已完成'
        },
        {
          id: '1818610477091799075',
          taskName: '重点区域巡查20241214第12次',
          cycleRole: 12,
          cycleType: '每日',
          distributionTeamName: '保安部',
          executeStartTime: '',
          executeEndTime: '',
          taskStatus: '已完成'
        },
        {
          id: '1818610476911443982',
          taskName: '重点区域巡查20241214第13次',
          cycleRole: 12,
          cycleType: '每日',
          distributionTeamName: '保安部',
          executeStartTime: '',
          executeEndTime: '',
          taskStatus: '已完成'
        }
      ],
      pagination: {
        total: 0,
        pageSize: 15,
        pageNo: 1
      },
      checkTypeList: ['全部'],
      typeList: [
        {
          label: '全部',
          value: ''
        }
      ],
      dateTypeName: '本月',
      dateType: 'month',
      dateList: [
        { name: '今日', val: 'day', startTime: dayjs().format('YYYY-MM-DD'), endTime: dayjs().format('YYYY-MM-DD') },
        { name: '本周', val: 'week', startTime: dayjs().startOf('week').format('YYYY-MM-DD'), endTime: dayjs().endOf('week').format('YYYY-MM-DD') },
        { name: '本月', val: 'month', startTime: dayjs().startOf('month').format('YYYY-MM-DD'), endTime: dayjs().endOf('month').format('YYYY-MM-DD') },
        { name: '本年', val: 'year', startTime: dayjs().startOf('year').format('YYYY-MM-DD'), endTime: dayjs().endOf('year').format('YYYY-MM-DD') },
        { name: '自定义', val: 'custom', startTime: '', endTime: '' }
      ],
      taskkDetailObj: {},
      taskDetailShow: false
    }
  },
  watch: {
    // 监听模型层级数据变化
    roomData: {
      handler(newVal, oldVal) {
        this.initData()
      },
      deep: true
    }
  },
  mounted() {
    this.sortType = this.roomData.ssmType < 4 ? 'jz' : this.roomData.ssmType == 4 ? 'lc' : 'mk'
    // 初始化调用
    this.initData()
  },
  methods: {
    closeInspPointDialog() {
      try {
        window.chrome.webview.hostObjects.sync.bridge.ShowDialog(true)
      } catch (error) {}
    },
    // 任务详情
    toView(row) {
      this.taskDetailShow = true
      this.taskkDetailObj = JSON.parse(JSON.stringify(row))
      this.taskkDetailObj.cycleType = 6
      this.taskkDetailObj.taskStatus = 1
      this.$refs.inspectionDetail.hiddenDangerDetailsListShow = true
      this.$refs.inspectionDetail.GetTaskPointReleaseList(row.id, 'insp')
      try {
        window.chrome.webview.hostObjects.sync.bridge.GetSwichFloorIsShow(true)
        window.chrome.webview.hostObjects.sync.bridge.ShowDialog(true)
      } catch (error) {}
    },
    // 时间切换
    handleCommand(val) {
      const obj = this.dateList.find((el) => el.val === val)
      this.dateType = val
      this.dateTypeName = obj.name
    },
    // 状态筛选过滤
    checkBoxChanged() {
      this.checkTypeList = [this.checkTypeList[this.checkTypeList.length - 1]]
      this.showCheckGroup = false
      const codeList = []
      this.typeList.forEach((item) => {
        if (this.checkTypeList[0] == item.label) {
          codeList.push(item.value)
        }
      })
      // this.pagination.pageNo = 1
    },
    tableLoadMore() {
      if (this.pagination.total > this.pagination.pageNo * this.pagination.pageSize) {
        this.pagination.pageNo++

      }
    },
    initData() {
      this.sortType = this.roomData.ssmType < 4 ? 'jz' : this.roomData.ssmType == 4 ? 'lc' : 'mk'
      this.ssmCodes = this.roomData.ssmCodes?.split(',') || []
      this.initClosePie()
      this.getTaskTypeData()
    },
    /** 初始化闭合饼图 */
    initClosePie() {
      var chartDom = document.getElementById('pie_rankStatistics')
      var myChart = echarts.init(chartDom)
      var option
      let chartData = [
        { value: 1, name: '已完成', percentage: '20%' },
        { value: 1, name: '进行中', percentage: '20%' },
        { value: 1, name: '未开始', percentage: '20%' },
        { value: 1, name: '   超时', percentage: '20%' }
      ]
      const xdata = Array.from(chartData, ({ name }) => name)
      option = {
        tooltip: {
          trigger: 'item'
        },
        legend: {
          // 取消legend和echarts的联动
          selectedMode: false,
          orient: 'vartical',
          x: 'left',
          top: 'center',
          left: '50%',
          bottom: '0%',
          data: xdata,
          itemWidth: 12,
          itemHeight: 8,
          itemGap: 16,
          textStyle: {
            color: '#B3C2DD' //  字体颜色
          },
          formatter: function (name) {
            var oa = chartData
            for (var i = 0; i < chartData.length; i++) {
              if (name === oa[i].name) {
                // '{name|' +name + '  ' + oa[i].value.percentage + '%' + '}\n{value|' + params.data.value + '}'
                return ' ' + name + '    ' + oa[i].value + '    ' + oa[i].percentage
              }
            }
          }
        },
        series: [
          {
            name: '',
            type: 'pie',
            radius: '70%',
            center: ['25%', '50%'],
            data: chartData,
            label: {
              show: false
            },
            emphasis: {
              label: {
                show: false
              }
            }

          }
        ]
      }
      myChart.resize()
      myChart.clear()
      option && myChart.setOption(option)
      window.addEventListener('resize', () => {
        myChart.resize()
      })
    },
    // 巡检任务类型
    getTaskTypeData() {
      let data = [
        {
          id: '1',
          name: '防火巡查',
          value: 1
        }, {
          id: '2',
          name: '安防巡查',
          value: 1
        }, {
          id: '3',
          name: '设备巡查',
          value: 1
        }, {
          id: '4',
          name: '库房巡查',
          value: 1
        }
      ]
      // 基于准备好的dom，初始化echarts实例
      let EChart = echarts.init(document.getElementById('xj_taskTypeEchart'))
      //   将data数据中得value值从大到小排序，然后按顺序将value放入dataValue数组中 name放入dataName数组中 name和value一一对应
      data.sort(function (a, b) {
        return a.value - b.value
      })
      const dataName = Array.from(data, (item) => {
        item.name = item.name || '未命名'
        const match = item.name.match(this.regex)
        const name = item.name
        return {
          value: name,
          textStyle: {
            color: '#FFF'
          }
        }
      })
      // 配置参数
      let config = {
        legend: {},
        grid: {
          top: '5%',
          left: '3%',
          right: '8%',
          bottom: '0%',
          containLabel: true
        },
        xAxis: {
          type: 'value',
          show: false
        },
        yAxis: [
          {
            type: 'category',
            data: dataName,
            axisTick: {
              show: false // 不显示坐标轴刻度线
            },
            axisLine: {
              show: false // 不显示坐标轴线
            },
            splitLine: {
              // 网格线
              show: false
            },
            axisLabel: { textStyle: { color: '#fff', fontSize: '14' } }
          },
          {
            type: 'category', // 坐标轴类型
            // inverse: true, // 是否是反向坐标轴
            axisTick: {
              show: false // 不显示坐标轴刻度线
            },
            axisLine: {
              show: false // 不显示坐标轴线
            },
            splitLine: {
              // 网格线
              show: false
            },
            axisLabel: {
              textStyle: {
                color: '#FFFFFFCC',
                fontSize: '14'
              },
              formatter: (value) => {
                return value + '个'
              }
            },
            data: data
          }
        ],
        series: [
          {
            type: 'bar',
            stack: 'total',
            label: {
              show: false
            },
            emphasis: {
              focus: 'series'
            },
            data: data,
            barWidth: 8,
            itemStyle: {
              normal: {
                // barBorderRadius: [15, 15, 15, 15],
                color: function (params) {
                  // 通过返回值的下标一一对应将颜色赋给柱子上
                  return '#0A84FF'
                }
              },
              // 鼠标移入改变颜色
              emphasis: {
                color: '#FFCA64FF'
              }
            },
            showBackground: true, // 显示背景色
            backgroundStyle: {
              color: '#384156'
            }
          }
        ],
        dataZoom: [
          {
            yAxisIndex: [0, 1],
            orient: 'vertical',
            show: data.length > 5,
            type: 'slider',
            start: 100,
            // end: 100,
            width: 8,
            left: '99%',
            borderColor: 'rgba(43,48,67,.1)',
            fillerColor: '#6580b8',
            zoomLock: true,
            showDataShadow: false,
            backgroundColor: 'rgba(43,48,67,.1)',
            showDetail: false,
            // realtime: true,
            filterMode: 'filter',
            handleIcon: 'M-9.35,34.56V42m0-40V9.5m-2,0h4a2,2,0,0,1,2,2v21a2,2,0,0,1-2,2h-4a2,2,0,0,1-2-2v-21A2,2,0,0,1-11.35,9.5Z',
            handleStyle: {
              color: '#6580b8',
              borderColor: '#6580b8'
            },
            maxValueSpan: 4,
            minValueSpan: 4,
            brushSelect: false
          },
          {
            type: 'inside',
            show: false,
            yAxisIndex: [0, 1],
            height: 8
          }
        ]
      }
      // 设置参数
      EChart.setOption(config)
      // 先移除点击事件 解决点击事件重复绑定
      EChart.getZr().off('click')
      // 点击事件
      EChart.getZr().on('click', (params) => {
        var pointInPixel = [params.offsetX, params.offsetY]
        if (EChart.containPixel('grid', pointInPixel)) {
          // 获取当前点击的索引值
          // 注意：若柱状图为纵向则获取x轴的索引，若柱状图为横向则需获取y轴的索引(见下方注释)
          // var xIndex=EChart.convertFromPixel({seriesIndex:0},[params.offsetX, params.offsetY])[0];
          // var xData=config.xAxis.data[xIndex];//当前点击柱子的数据
          var yIndex = EChart.convertFromPixel({ seriesIndex: 0 }, [params.offsetX, params.offsetY])[1]
          var yData = config.yAxis[1].data[yIndex] // 当前点击柱子的数据
          // 新增变量 判断当前点击柱状图是选中还是取消选中
          let isSelected = false
          dataName.map((e, i) => {
            // 选中的设置选中色
            if (i == yIndex && e.textStyle.color != '#FFCA64FF') {
              e.textStyle.color = '#FFCA64FF'
            } else {
              // 选中已选中的则为取消选中
              if (i == yIndex && e.textStyle.color == '#FFCA64FF') {
                isSelected = true
              }
              // 其他的设为默认色
              e.textStyle.color = '#FFF'
            }
          })
          config.yAxis[0].data = JSON.parse(JSON.stringify(dataName))
          config.dataZoom[0].start = (yIndex / dataName.length) * 100
          EChart.setOption(config)
        }
      })
    },
    allTableChange() {

    }
  }
}
</script>

  <style lang="scss" scoped>
@import "./style/module.scss";
.inspectionComponent {
  width: 100%;
  height: 100%;
  .el-dropdown-link {
    color: #fff !important;
    margin-left: 10px;
  }
  .title-left {
    padding-left: 20px;
  }
  .chartContent {
    padding: 16px 0;
    height: calc(100% - 44px);
    display: flex;
    width: 100%;
  }
}
.module-header {
  width: 100%;
  // padding-left: 50px;
  background: url("~@/assets/images/qhdsys/bg-bt.png") no-repeat;
}
.order-more {
  width: 24px;
  height: 24px;
  margin-right: 0 !important;
  margin-left: 8px;
}
.filter {
  width: 24px;
  height: 24px;
}
.icon-box {
  margin-right: 0;
  display: flex;
  align-items: center;
  position: relative;
  .panel-s {
    position: absolute !important;
    right: 0 !important;
    top: 28px !important;
    background-color: #374b79 !important;
    z-index: 9 !important;
    min-height: 20px !important;
    overflow: auto;
    .el-checkbox-group {
      display: flex !important;
      flex-direction: column;
      align-items: flex-end;
      padding: 8px;
      margin-right: 0 !important;
      .el-checkbox {
        width: 100%;
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-right: 0;
        & + .el-checkbox {
          margin-top: 5px;
        }
        .el-checkbox__label {
          color: #a3a9c0 !important;
        }
      }
    }
  }
}
::v-deep .el-checkbox__input.is-checked span.el-checkbox__inner {
  background: url("@/assets/images/<EMAIL>") !important;
  background-size: 100% 100% !important;
}
::v-deep span.el-checkbox__inner {
  border-color: #c5fff9 !important;
}
::v-deep .el-checkbox__input.is-checked + .el-checkbox__label {
  color: #8bddf5;
}
</style>
