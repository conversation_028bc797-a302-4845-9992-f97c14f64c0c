<template>
  <div class="viewContent">
    <template v-for="(card, index) in chartsList">
      <ModuleCard :key="index" :title="card.title" :style="{ height: `calc(100% / ${chartsList.length})`, minHeight: '25%' }">
        <div slot="content" style="height: 98%">
          <div class="search-data">
            <el-dropdown trigger="click" @command="(e) => handleCommand(e, card)">
              <span class="el-dropdown-link"> {{ card.dateTypeName }} <i class="el-icon-caret-bottom"></i> </span>
              <el-dropdown-menu slot="dropdown">
                <el-dropdown-item v-for="(item, index) in dateList" :key="index" :command="item.val" :class="{ isBjxl: card.dateType == item.val }">{{ item.name }}</el-dropdown-item>
              </el-dropdown-menu>
            </el-dropdown>
            <el-date-picker
              v-model="card.dateRange"
              class="datePickerInput"
              popper-class="date-style"
              unlink-panels
              type="daterange"
              value-format="yyyy-MM-dd"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              :disabled="card.dateType !== 'custom'"
              :picker-options="{
                firstDayOfWeek: 1
              }"
              @change="(e) => dataPickerValChange(e, card)"
              @focus="setWPFBgShow()"
              @blur="setWPFBgHide()"
            >
            </el-date-picker>
          </div>
          <div :id="card.domId" :ref="card.domId" style="width: 98%; height: calc(100% - 40px)"></div>
        </div>
      </ModuleCard>
    </template>
  </div>
</template>
<script>
import dayjs from 'dayjs'
import { getElevatorFaultData } from '@/utils/elevatorApi'
import { doorOpenedCntList, floorCountList} from '@/utils/newIot'
import * as echarts from 'echarts'
export default {
  props: {
    roomData: {
      type: Object,
      default: () => { }
    }
  },
  data() {
    return {
      chartsList: [
        {
          title: '开门次数排行',
          dateTypeName: '今日',
          dateType: 'day',
          startTime: '',
          endTime: '',
          dateRange: [],
          domId: 'openDoorNum',
          parameterIds: '2634',
          color: ['#2784E9', 'rgba(10,63,166,0)'],
          unit: '次',
          chart: null,
          fn: doorOpenedCntList
        },
        {
          title: '困人报警排行',
          dateTypeName: '今日',
          dateType: 'day',
          startTime: '',
          endTime: '',
          dateRange: [],
          domId: 'elevatorTrapped',
          parameterIds: 'personTrapped',
          color: ['#F4DB67', 'rgba(244,219,103,0)'],
          unit: '层',
          chart: null,
          fn: getElevatorFaultData
        },
        {
          title: '卡层报警排行',
          dateTypeName: '今日',
          dateType: 'day',
          startTime: '',
          endTime: '',
          dateRange: [],
          domId: 'seizingFloor',
          parameterIds: 'stuck',
          color: ['#F4DB67', 'rgba(244,219,103,0)'],
          unit: '层',
          chart: null,
          fn: getElevatorFaultData
        },
        {
          title: '运行楼层排行',
          dateTypeName: '今日',
          dateType: 'day',
          startTime: '',
          endTime: '',
          dateRange: [],
          domId: 'workingFloor',
          parameterIds: '2700',
          color: ['#F4DB67', 'rgba(244,219,103,0)'],
          unit: '层',
          chart: null,
          fn: floorCountList
        }
      ],
      dateList: [
        { name: '今日', val: 'day', startTime: dayjs().format('YYYY-MM-DD'), endTime: dayjs().format('YYYY-MM-DD') },
        { name: '本周', val: 'week', startTime: dayjs().startOf('week').format('YYYY-MM-DD'), endTime: dayjs().endOf('week').format('YYYY-MM-DD') },
        { name: '本月', val: 'month', startTime: dayjs().startOf('month').format('YYYY-MM-DD'), endTime: dayjs().endOf('month').format('YYYY-MM-DD') },
        { name: '本年', val: 'year', startTime: dayjs().startOf('year').format('YYYY-MM-DD'), endTime: dayjs().endOf('year').format('YYYY-MM-DD') },
        { name: '自定义', val: 'custom', startTime: '', endTime: '' }
      ]
    }
  },
  mounted() {
    this.chartsList.forEach((el) => {
      el.dateRange = [this.dateList[0].startTime, this.dateList[0].endTime]
      el.startTime = this.dateList[0].startTime
      el.endTime = this.dateList[0].endTime
    })
    this.initCharts()
  },
  methods: {
    // 时间切换
    handleCommand(val, item) {
      item.dateRange = [] // 处理上一次选择显示错乱的问题
      const obj = this.dateList.find((el) => el.val === val)
      item.dateType = val
      item.dateTypeName = obj.name
      if (val === 'custom') {
        item.dateRange = [item.startTime, item.endTime]
      } else {
        item.dateRange = [obj.startTime, obj.endTime] // 页面回显
        item.startTime = obj.startTime // 页面回显
        item.endTime = obj.endTime // 页面回显
      }
      if (item.startTime && item.endTime) {
        this.getChartsData(item)
      }
    },
    dataPickerValChange(val, item) {
      item.startTime = val[0]
      item.endTime = val[1]
      this.handleCommand('custom', item)
    },
    initCharts() {
      this.chartsList.forEach((el) => {
        this.getChartsData(el)
      })
    },
    // 获取卡层/困人
    getChartsData(chartInfo) {
      if (chartInfo.parameterIds === 'personTrapped' || chartInfo.parameterIds === 'stuck') {
        const data = {
          projectCode: 'DTXT',
          parameterIds: chartInfo.parameterIds,
          startTime: chartInfo.startTime,
          endTime: chartInfo.endTime
        }
        chartInfo.fn(data).then((res) => {
          if (res.data.code === '200') {
            res.data.data.forEach((el) => {
              el.name = el.surveyName
              // el.value = el.count
            })
            this.setRankingBarEcharts(res.data.data, chartInfo)
          }
        })
      } else if (chartInfo.domId == 'openDoorNum' || chartInfo.domId == 'workingFloor') {
        let params = {
          dateType: chartInfo.dateType,
          sTime: chartInfo.startTime,
          eTime: chartInfo.endTime
        }
        chartInfo.fn(params).then(res => {
          if (res.data.code == 200) {
            if (res.data.result && res.data.result.liftTotalList) {
              res.data.result.liftTotalList.forEach((el) => {
                if (chartInfo.domId == 'openDoorNum') {
                  el.name = el.liftName,
                  el.count = el.doorOpenedCntSum
                } else {
                  el.name = el.liftName,
                  el.count = el.floorCountSum
                }
              })
            }
            this.setRankingBarEcharts(res.data.result.liftTotalList, chartInfo)
          }
        })
      }
    },

    // 电梯4个状态数据echarts
    setRankingBarEcharts (data, chartInfo) {
      let dom = document.getElementById(chartInfo.domId)
      if (chartInfo.chart) {
        chartInfo.chart.dispose()
      }
      chartInfo.chart = echarts.init(dom)
      let option = {}
      if (data && data.length) {
        option = {
          yAxis: [
            {
              type: 'category',
              position: 'left', // 左侧 Y 轴
              axisLabel: {
                color: '#fff'
              },
              axisLine: {
                show: false // 显示 Y 轴线
              },
              axisTick: {
                show: false // 显示 Y 轴刻度
              },
              data: data.map((item) => item.name)
            },
            {
              type: 'category',
              position: 'right', // 右侧 Y 轴
              axisLabel: {
                color: '#fff'
              },
              axisLine: {
                show: false // 显示 Y 轴线
              },
              axisTick: {
                show: false // 显示 Y 轴刻度
              },
              data: data.map((el) => {
                return {
                  value: el.count
                }
              })
            }
          ],
          xAxis: {
            type: 'value'
          },
          grid: {
            left: '2%',
            right: '5%',
            top: '13%',
            bottom: '0%',
            containLabel: true
          },
          series: [
            {
              data: data.map((el) => {
                return {
                  value: el.count
                }
              }),
              type: 'bar',
              barWidth: 10,
              barGap: '10%',
              itemStyle: {
                color: '#8BDDF5'
              }
            }
          ],
          dataZoom: [
            {
              yAxisIndex: [0, 1],
              orient: 'vertical',
              show: true,
              type: 'slider',
              startValue: 0,
              // end: 100,
              width: 8,
              left: '99%',
              borderColor: 'rgba(43,48,67,.1)',
              fillerColor: '#6580b8',
              zoomLock: true,
              showDataShadow: false,
              backgroundColor: 'rgba(43,48,67,.1)',
              showDetail: false,
              // realtime: true,
              filterMode: 'filter',
              handleIcon:
                'path://M-9.35,34.56V42m0-40V9.5m-2,0h4a2,2,0,0,1,2,2v21a2,2,0,0,1-2,2h-4a2,2,0,0,1-2-2v-21A2,2,0,0,1-11.35,9.5Z',
              handleStyle: {
                color: '#6580b8',
                borderColor: '#6580b8'
              },
              maxValueSpan: 4,
              minValueSpan: 4,
              brushSelect: false
            },
            {
              type: 'inside', // 内置型数据区域缩放组件
              yAxisIndex: [0, 1], // 对应的 Y 轴索引
              start: 0, // 数据窗口范围的起始百分比
              end: 50 // 数据窗口范围的结束百分比
            }
          ]
        }
      } else {
        option = {
          title: {
            text: '暂无数据',
            x: 'center',
            y: 'center',
            textStyle: {
              fontSize: 14,
              fontWeight: 'normal',
              color: '#999'
            }
          }
        }
      }
      // 随着屏幕大小调节图表
      window.addEventListener('resize', () => {
        chartInfo.chart.resize()
      })
      chartInfo.chart.setOption(option)
      // 添加柱状图鼠标点击事件
      chartInfo.chart.on('click', (params) => {
        const clickedIndex = params.dataIndex
        // 修改柱状图颜色
        option.series[0].data.forEach((el) => {
          el.itemStyle = null
        })
        option.series[0].data[clickedIndex].itemStyle = {
          color: '#FFCA64' // 修改为指定颜色
        }
        chartInfo.chart.setOption(option) // 更新图表配置
      })
    },
    setWPFBgShow() {
      try {
        window.chrome.webview.hostObjects.sync.bridge.DateMask(true)
      } catch (error) {}
    },
    setWPFBgHide() {
      try {
        window.chrome.webview.hostObjects.sync.bridge.DateMask(false)
      } catch (error) {}
    }
  }
}
</script>
<style lang="scss" scoped>
.viewContent {
  width: 100%;
  height: 100%;
}
.search-data {
  display: flex;
  background: rgba(133, 145, 206, 0.15);
  padding: 0px 10px;
  ::v-deep .el-dropdown {
    padding: 7px 6px;
    .el-dropdown-link {
      font-size: 14px;
      font-weight: 500;
      color: #8bddf5;
      line-height: 16px;
      position: relative;
      cursor: pointer;
    }
    .el-dropdown-link::after {
      content: '';
      position: absolute;
      width: 1px;
      height: 12px;
      background: rgba(133, 145, 206, 0.5);
      top: 50%;
      right: -6px;
      transform: translateY(-50%);
    }
  }
  ::v-deep .datePickerInput {
    flex: 1;
    padding: 8px 10px;
    height: 16px;
    box-sizing: content-box;
    background: none;
    border: none;
    .el-input__icon,
    .el-range-separator {
      line-height: 16px;
      color: #b0e3fa;
    }
    .el-range-input {
      background: none;
      color: #a4afc1;
    }
  }
}
</style>
