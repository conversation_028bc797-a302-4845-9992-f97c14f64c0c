<template>
  <div class="deviceParamHistory">
    <div class="search-box">
      <el-select v-model="queryParam.harvesterIds" placeholder="请选择传感器" collapse-tags filterable multiple clearable popper-class="new-select">
        <el-option v-for="item in harvesterList" :key="item.harvesterId" :label="item.harvesterName" :value="item.harvesterId"> </el-option>
      </el-select>
      <el-select v-model="queryParam.paramIds" placeholder="请选择参数" filterable clearable popper-class="new-select">
        <el-option v-for="item in paramList" :key="item.paramId" :label="item.paramName" :value="item.paramId"> </el-option>
      </el-select>
      <el-date-picker
        v-model="dateRange"
        type="daterange"
        unlink-panels
        popper-class="date-style"
        range-separator="至"
        start-placeholder="开始日期"
        end-placeholder="结束日期"
        value-format="yyyy-MM-dd"
        @change="changeDataEvent"
      >
      </el-date-picker>
      <div class="date-type">
        <p v-for="item in dateTypeList" :key="item.value" class="type-item" :class="{'active-type': dateType == item.value}" @click="activeTabEvent(item.value)">{{ item.name }}</p>
      </div>
    </div>
    <div style="flex: 1;">
      <el-table
        v-loading="tableLoading"
        :data="tableData"
        height="calc(100%)"
        :cell-style="$tools.setCell(3)"
        :header-cell-style="$tools.setHeaderCell(3)"
        style="width: 100%"
        element-loading-background="rgba(0, 0, 0, 0.2)"
      >
        <el-table-column prop="harvesterName" label="传感器名称" show-overflow-tooltip></el-table-column>
        <el-table-column prop="parameterName" label="参数名称"></el-table-column>
        <el-table-column prop="time" label="更新时间" width="180"></el-table-column>
        <el-table-column prop="parameterValue" label="更新数值"></el-table-column>
        <!-- <el-table-column prop="parameterException" label="参数状态">
          <div slot-scope="scope" :style="{ color: scope.row.parameterException == 0 ? '#61E29D' : scope.row.parameterException == 1 ? '#FF2D55' : '#999' }">
            <span class="alarmStatusIcon" :style="{ background: scope.row.parameterException == 0 ? '#61E29D' : scope.row.parameterException == 1 ? '#FF2D55' : '#999' }"></span>
            {{ scope.row.parameterException == 0 ? '正常' : scope.row.parameterException == 1 ? '异常' : '离线' }}
          </div>
        </el-table-column> -->
        <!-- <el-table-column prop="ispUpperThreshold" label="参数范围">
          <span slot-scope="scope">
            {{(scope.row.ispDownThreshold || '') + ' - ' + (scope.row.ispUpperThreshold || '')}}
          </span>
        </el-table-column> -->
        <el-table-column label="操作" width="100">
          <template slot-scope="scope">
            <span style="color: #FFCA64;cursor: pointer;" @click="paramDetail(scope.row)">查看</span>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <paramDetails v-if="isParamDetails" :dialogShow="isParamDetails" :paramData="paramData" @paramDetailsClose="() => isParamDetails = false" />
  </div>
</template>

<script>
import { GetParamDataHistoryList, GetSelectBySurveyCode } from '@/utils/centerScreenApi'
import moment from 'moment'
moment.locale('zh-cn')
export default {
  name: 'deviceParamHistory',
  components: {
    paramDetails: () => import('./paramDetails.vue')
  },
  props: {
    deviceData: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      isParamDetails: false,
      tableLoading: false,
      tableData: [],
      dateRange: [],
      queryParam: {
        harvesterIds: [],
        paramIds: ''
      },
      dateType: 1,
      dateTypeList: [
        { name: '今天', value: 1 },
        { name: '近7天', value: 2 },
        { name: '近30天', value: 3 }
      ],
      harvesterList: [], // 传感器；列表
      paramList: [], // 参数列表
      paramData: {}
    }
  },
  watch: {
    'queryParam.harvesterIds'(val) {
      this.queryParam.paramIds = ''
      this.getSelectBySurveyCode()
      this.getParamDataHistoryList()
    },
    'queryParam.paramIds'(val) {
      this.getParamDataHistoryList()
    }
  },
  created() {
    this.getSelectBySurveyCode()
    this.activeTabEvent(1)
  },
  methods: {
    paramDetail(row) {
      this.isParamDetails = true
      this.paramData = row
      console.log(row)
    },
    getSelectBySurveyCode() {
      let params = {
        surveyCode: this.deviceData.surveyCode,
        harvesterId: this.queryParam.harvesterIds.join(',')
      }
      GetSelectBySurveyCode(params).then(res => {
        if (res.data.code == 200) {
          if (!params.harvesterId) this.harvesterList = res.data.data.harvesterList
          this.paramList = res.data.data.paramList
        }
      })
    },
    changeDataEvent() {
      this.getParamDataHistoryList()
    },
    activeTabEvent(val) {
      const dateList = {
        1: [moment().startOf('day').format('YYYY-MM-DD'), moment().format('YYYY-MM-DD')],
        2: [moment().subtract(6, 'days').format('YYYY-MM-DD'), moment().format('YYYY-MM-DD')],
        3: [moment().subtract(29, 'days').format('YYYY-MM-DD'), moment().format('YYYY-MM-DD')]
      }
      this.dateType = val
      this.dateRange = dateList[val]
      this.getParamDataHistoryList()
    },
    getParamDataHistoryList() {
      const param = {
        projectCode: this.deviceData.projectCode,
        surveyCode: this.deviceData.surveyCode,
        startTime: this.dateRange[0],
        endTime: this.dateRange[1],
        harvesterIds: this.queryParam.harvesterIds,
        paramIds: this.queryParam.paramIds ? this.queryParam.paramIds.split(',') : []
      }
      this.tableLoading = true
      GetParamDataHistoryList(param).then(res => {
        this.tableLoading = false
        if (res.data.code == 200) {
          this.tableData = res.data.data
        }
      })
    }
  }
}

</script>

<style lang="scss" scoped>
.deviceParamHistory {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  .search-box {
    padding: 8px 0px 24px 0px;
    display: flex;
    align-items: center;
    .date-type {
      margin-left: 15px;
      display: flex;
      .type-item {
        cursor: pointer;
        padding: 8px 12px;
        font-weight: 400;
        font-size: 14px;
        color: #B0E3FA;
        margin-right: 8px;
        border: 1px solid transparent;
        background: linear-gradient( 90deg, rgba(10,132,255,0) 0%, rgba(10,132,255,0) 100%), rgba(255,255,255,0.1);
      }
      .active-type {
        color: #8BDDF5;
        background: linear-gradient( 90deg, rgba(10,132,255,0.4) 0%, rgba(10,132,255,0) 100%), rgba(255,255,255,0.1);
        border: 1px solid;
        border-image: radial-gradient(circle, rgba(171, 240, 255, 1), rgba(226, 254, 255, 1), rgba(132, 196, 203, 1), rgba(48, 151, 166, 0)) 1 1;
      }
    }
    ::v-deep(.el-select) {
      margin-right: 16px;
      .el-input {
        .el-input__inner {
          height: 35px;
          border: 1px solid rgba(133, 145, 206, 0.5);
          border-radius: 4px;
        }
        .el-input__icon {
          line-height: 35px;
        }
      }
    }
  }
  ::v-deep(.el-date-editor) {
    width: 300px;
    height: 35px;
    background-color: #14233e;
    border-color: rgba(133, 145, 206, 0.5);
    .el-input__icon {
      transform: translateY(-2px);
    }
    .el-range-input {
      background-color: #14233e;
      color: rgba(255, 255, 255, 0.8);
    }
    .el-range-separator {
      color: rgba(255, 255, 255, 0.8);
      transform: translateY(-2px);
    }
  }
}
</style>
