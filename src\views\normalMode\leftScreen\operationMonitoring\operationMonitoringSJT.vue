<template>
  <div class="content">
    <div class="eahart-list">
      <div class="echarts-left">
        <div class="bg-title" style="margin-top: 0">UPS实时监测</div>
        <div class="bg-content" style="padding: 0.625rem 0 0.9375rem 0">
          <el-carousel ref="carousel" class="carousel" :interval="10000" arrow="always">
            <el-carousel-item v-for="(item, index) in groupedArray" :key="index" :loop="false">
              <div v-for="(v, i) in item" :key="i" style="padding: 0 40px">
                <div class="list">
                  <div class="left">
                    <img :src="require('@/assets/images/peace/left-operation-host.png')" />
                    <span>{{ v.name }}</span>
                  </div>
                  <div class="progress">
                    <div v-if="v.type && v.type === 'UPS'" style="padding: 0px 30px">
                      <progress-bar :stateList="v.list" />
                    </div>
                    <div v-else class="list-data">
                      <div v-for="(data, h) in v.parameterList" :key="h" class="data-data">
                        <div v-for="(parameterList, g) in data" :key="g">
                          <span>{{ parameterList.parameterName || '' }}：</span>
                          <span>{{ (parameterList.parameterValue || '') + ' ' + (parameterList.parameterUnit || '') }}</span>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </el-carousel-item>
          </el-carousel>
        </div>
      </div>
      <div class="echarts-center">
        <div class="bg-title" style="margin-top: 0">智能楼宇监测</div>
        <div class="bg-content">
          <div v-for="item in monitorStateList" :key="item.menuName" class="content-item">
            <div class="subtitle">{{ item.menuName }}</div>
            <div class="item-main">
              <div class="main-left" @click="goDetailList(item)">
                <img :src="item.img" />
              </div>
              <div class="main-right">
                <div class="right-text">
                  <p class="right-title">总数</p>
                  <p>
                    <span class="right-value">{{ item.count }}</span>
                    <span class="right-unit">{{ item.menuName === '照明监测' ? '路' : '部' }}</span>
                  </p>
                </div>
                <div class="right-text">
                  <p class="right-title">运行中</p>
                  <p>
                    <span class="right-value">{{ item.normalCount }}</span>
                    <span class="right-unit">{{ item.menuName === '照明监测' ? '路' : '部' }}</span>
                  </p>
                </div>
                <div class="right-text" style="margin-top: 20px">
                  <p class="right-title">停止</p>
                  <p>
                    <span class="right-value">{{ item.stopCount }}</span>
                    <span class="right-unit">{{ item.menuName === '照明监测' ? '路' : '部' }}</span>
                  </p>
                </div>
                <div class="right-text" style="margin-top: 20px">
                  <p class="right-title">故障</p>
                  <p>
                    <span class="right-value right-value-red">{{ item.faultCount }}</span>
                    <span class="right-unit" style="color: #ff5454">{{ item.menuName === '照明监测' ? '路' : '部' }}</span>
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="echarts-right">
        <div class="echarts-right-top">
          <div class="bg-title" style="padding-top: 0.2rem; margin-top: 0px">实时报警分析</div>
          <div class="bg-content">
            <div class="content-left"></div>
            <div class="content-right">
              <div v-for="item in warnAnalysis" :key="item.name" class="right-item">
                <p class="right-title">{{ item.name }}</p>
                <div class="right-value">
                  <p class="right-num">{{ item.count || 0 }}</p>
                  <p v-if="item.timeType != 3" class="right-ratio">
                    <span style="color: #fff; margin-right: 3px">环比</span>
                    <span :style="{ color: item.ringRatioType == 1 ? '#00F872' : '#FF5454', fontStyle: 'oblique' }">{{ item.ringRatio }}</span>
                    <img v-if="item.ringRatioType == 1" src="@/assets/images/peace/ratio-down.png" />
                    <img v-else src="@/assets/images/peace/ratio-up.png" />
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="echarts-right-bottom">
          <div class="bg-title" style="padding-top: 0.2rem">报警来源分析</div>
          <div class="bg-content">
            <div id="alarmSourceEchart"></div>
          </div>
        </div>
      </div>
    </div>
    <div class="content-bottom">
      <div class="bottom-left">
        <div class="bg-title" style="margin-top: 0">医用气体实时监测</div>
        <div class="bg-content" style="padding: 0">
          <div class="gas-left right-line">
            <div class="dept-monitor"><span></span>医用气体实时监测</div>
            <div class="gas-left-list">
              <div v-for="item in medicalGasList" :key="item.surveyEntityCode" class="list-item" @click="viewRealisticScenery">
                <div class="item-left">
                  <p class="left-title"></p>
                  <p class="left-value"></p>
                </div>
                <div class="item-right">
                  <p class="right-title">{{ item.surveyEntityName }}</p>
                  <p v-for="(v, i) in item.parameterList" :key="i" class="right-value">
                    <span class="right-name">{{ v.parameterName }}：</span>
                    <span class="right-num">{{ (v.parameterValue || '-') + ' ' + (v.parameterUnit || '') }}</span>
                  </p>
                </div>
              </div>
            </div>
          </div>
          <div class="gas-center right-line">
            <div class="dept-monitor"><span></span>氧气站环境监测</div>
            <div class="gas-list">
              <div
                v-for="(item, index) in envParams"
                :key="index"
                class="gas-list-item"
                :class="{ 'right-line': index !== envParams.length - 1 }"
                :style="{ width: 'calc(100% /' + envParams.length + ')' }"
              >
                <span class="item-value"
                >{{ item?.value ?? '-' }} <i>{{ item.ispUnitName || '' }}</i>
                </span>
                <span class="item-name">{{ item.ispParamName }}</span>
              </div>
            </div>
            <div class="dept-monitor"><span></span>科室用氧压力监测</div>
            <div style="padding: 0px 22px 0px 22px">
              <progress-bar :stateList="deptStateList" :cMax="76" :cMin="12" />
            </div>
            <div class="gas-group">
              <div
                v-for="(val, key, index) in monitoringListGroup"
                :key="key"
                class="gas-group-item"
                :class="{ 'right-line': index !== Object.keys(monitoringListGroup).length - 1 }"
                :style="{ width: 'calc(100% /' + Object.keys(monitoringListGroup).length + ')' }"
              >
                <div class="dept-monitor" :style="{ width: index == 0 ? '100%' : '' }"><span></span>{{ key }}</div>
                <div class="gas-list">
                  <div v-for="(v, i) in val" :key="i" class="gas-list-item">
                    <span class="item-name">{{ v.surveyEntityName }}</span>
                    <span class="item-value"
                    >{{ v.parameterList[0]?.parameterValue ?? '-' }} <i>{{ v.parameterList[0]?.parameterUnit ?? '' }}</i>
                    </span>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div class="gas-right">
            <div class="dept-monitor" style="margin: 10px 0px 0px 0px"><span></span>负压机房监测</div>
            <div class="gas-list">
              <div
                v-for="(item, index) in machineRoom.negativePressureList.parameterList"
                :key="index"
                class="gas-list-item"
                :class="{ 'right-line': index !== machineRoom.negativePressureList.parameterList.length - 1 }"
                :style="{ width: 'calc(100% /' + machineRoom.negativePressureList.parameterList.length + ')' }"
              >
                <span class="item-value"
                >{{ item?.parameterValue ?? '-' }} <i>{{ item?.parameterUnit ?? '' }}</i>
                </span>
                <span class="item-name">{{ item?.parameterName }}</span>
              </div>
            </div>
            <div class="dept-monitor" style="margin: 10px 0px 0px 0px"><span></span>科室负压监测</div>
            <div style="padding: 0px 22px">
              <progress-bar :stateList="machineRoom.negativePressure" />
            </div>

            <div class="dept-monitor" style="margin: 10px 0px 0px 0px"><span></span>压缩空气机房</div>
            <div class="gas-list">
              <div
                v-for="(item, index) in machineRoom.compressedAirList.parameterList"
                :key="index"
                class="gas-list-item"
                :class="{ 'right-line': index !== machineRoom.compressedAirList.parameterList.length - 1 }"
                :style="{ width: 'calc(100% /' + machineRoom.compressedAirList.parameterList.length + ')' }"
              >
                <span class="item-value"
                >{{ item?.parameterValue ?? '-' }} <i>{{ item?.parameterUnit ?? '' }}</i>
                </span>
                <span class="item-name">{{ item?.parameterName }}</span>
              </div>
            </div>
            <div class="dept-monitor" style="margin: 10px 0px 0px 0px"><span></span>科室压缩空气监测</div>
            <div style="padding: 0px 22px">
              <progress-bar :stateList="machineRoom.compressedAir" />
            </div>
          </div>
        </div>
      </div>
      <div class="bottom-right">
        <div class="bg-title warn-message-title" style="margin-top: 0">
          <span>实时报警分析</span>
          <span @click="warnListRouter">更多<i class="el-icon-arrow-right"></i></span>
        </div>
        <div class="bg-content" style="padding: 0">
          <el-table
            v-loading="tableLoading"
            stripe
            :data="tableData"
            height="calc(100%)"
            :cell-style="$tools.setCell(3)"
            :header-cell-style="$tools.setHeaderCell(3)"
            style="width: 100%"
            element-loading-background="rgba(0, 0, 0, 0.2)"
          >
            <el-table-column fixed prop="alarmSource" show-overflow-tooltip label="报警来源"></el-table-column>
            <el-table-column fixed prop="incidentName" show-overflow-tooltip label="事件类型"></el-table-column>
            <el-table-column fixed prop="alarmLevel" show-overflow-tooltip label="报警等级">
              <span slot-scope="scope" :style="{ color: alarmLevelItem[scope.row.alarmLevel].color }">
                {{ alarmLevelItem[scope.row.alarmLevel].text }}
              </span>
            </el-table-column>
            <el-table-column :width="120" fixed prop="devicePosition" show-overflow-tooltip label="报警处理状态">
              <span slot-scope="scope">
                {{ scope.row.alarmStatus == 0 ? '未处理' : scope.row.alarmStatus == 1 ? '处理中' : '已关闭' }}
              </span>
            </el-table-column>
            <el-table-column fixed prop="alarmStartTime" show-overflow-tooltip label="报警时间"></el-table-column>
          </el-table>
        </div>
      </div>
    </div>
    <div v-if="searchTableShow">
      <searchComponentList ref="searchComponentList" :dialogData="tableCompenentData" :dialogShow="searchTableShow" @configCloseDialog="() => {searchTableShow = false}" />
    </div>
  </div>
</template>
<script>
import {
  GetPoliceInfoByApp,
  GetAlarmSourceCount,
  GetAlarmRecordAll,
  getRealMonitoringList,
  GetMenuGroup,
  getParamStateNum,
  GetSurveyStatusByImhCode,
  getRealMonitoringListNew,
  GetGasEnvParamNew,
  GetRealMonitoringListGasGroup
} from '@/utils/peaceLeftScreenApi'
import * as echarts from 'echarts'
// import $ from 'jquery'
import progressBar from '../components/progressBar.vue'
import searchComponentList from './components/seaechTableSJT.vue'
export default {
  components: {
    progressBar,
    searchComponentList
  },
  data() {
    return {
      groupedArray: [
        // {
        //   id: 0,
        //   url: require('@/assets/images/peace/usp.png'),
        //   name: 'UPS主机',
        //   type: 'UPS',
        //   list: [
        //     { value: stateData.normalCount, name: '正常', lineColor: '#FFE3A6', color: '#7EAEF9' },
        //     { value: stateData.offLineCount, name: '离线', lineColor: '#7EAEF9', color: '#7EAEF9' },
        //     { value: stateData.faultCount, name: '异常', lineColor: '#FF5454', color: '#FF5454' }
        //   ]
        // },
        // {
        //   id: 1,
        //   url: require('@/assets/images/peace/center-item-zhaoming.png'),
        //   name: '1#主机',
        //   status: '放电模式'
        // },
        // {
        //   id: 2,
        //   url: require('@/assets/images/peace/center-item-zhaoming.png'),
        //   name: '2#主机',
        //   status: '220v'
        // },
        // {
        //   id: 3,
        //   url: require('@/assets/images/peace/center-item-zhaoming.png'),
        //   name: '3#主机',
        //   status: '放电模式'
        // }
      ],
      medicalGasList: [],
      deptStateList: [],
      monitorStateList: [],
      tableData: [],
      warnAnalysis: [],
      envParams: [],
      monitoringListGroup: [],
      machineRoom: {
        compressedAir: [],
        negativePressure: [],
        compressedAirList: {},
        negativePressureList: {}
      },
      tableLoading: false,
      timer: null,
      alarmLevelItem: {
        0: { text: '通知', color: '#3562DB' },
        1: { text: '一般', color: '#3562DB' },
        2: { text: '紧急', color: '#FF9435' },
        3: { text: '重要', color: '#FA403C' }
      },
      searchTableShow: false,
      tableCompenentData: {}
    }
  },
  mounted() {
    // this.groupedArray = this.group(this.groupedArray, 3)
    // this.grouped2Array = this.group(this.grouped2Array, 2)
    this.search()
    this.timer = setInterval(() => {
      setTimeout(() => {
        this.search()
      }, 0)
    }, 1000 * timerNum)
    this.$once('hook:beforeDestroy', () => {
      clearInterval(this.timer)
      this.timer = null
    })
  },
  methods: {
    // 智能楼宇  配电详情list
    goDetailList(val) {
      Object.assign(this.tableCompenentData, {
        title: '智能楼宇运行监测',
        height: 'calc(100% - 120px)',
        queryInfo: val
      })
      this.searchTableShow = true
      console.log(this.tableCompenentData, this.searchTableShow)
    },
    // 查看实景
    viewRealisticScenery() {
      try {
        window.chrome.webview.hostObjects.sync.bridge.EquipmentRealView('Yqxt')
      } catch (error) {}
    },
    search() {
      this.getSurveyStatusByImhCode()
      this.getPoliceInfoByApp()
      this.getAlarmSourceCount()
      this.getTableData()
      this.getMenuGroup()
      this.getRealMonitoringListNew()
      this.getEnvParam()
      this.getParamStateNum()
      this.getRealMonitoringListGasGroup()
      this.getMachineRoom()
    },
    // 获取ups统计信息
    getSurveyStatusByImhCode() {
      GetSurveyStatusByImhCode({ projectCode: '01f4bd80e5c060809aae72c7470e8be30' }).then((res) => {
        const data = res.data
        if (data.code === '200') {
          this.getHosUseMonitoringData(data.data)
        } else {
          // this.$message({
          //   message: data.msg,
          //   type: 'warning'
          // })
        }
      })
    },
    // 轮播图数据分组
    group(array, subGroupLength) {
      let index = 0
      const newArray = []
      while (index < array.length) {
        newArray.push(array.slice(index, (index += subGroupLength)))
      }
      return newArray
    },
    percentageFil(value, total) {
      if (value) {
        return (
          value /
          total.reduce((prev, curr) => {
            return Number(prev) + Number(curr)
          })
        )
      } else {
        return 0
      }
    },
    format() {
      return ''
    },
    // 获取 报警统计数据
    getPoliceInfoByApp() {
      const newArr = ['本日新增', '本周新增', '本月新增', '本年新增']
      GetPoliceInfoByApp().then((res) => {
        const data = res.data
        if (data.code === '200') {
          this.warnAnalysis = data.data.countInfo
          this.warnAnalysis.forEach((item) => {
            item.name = newArr[item.timeType]
          })
        } else {
          // this.$message({
          //   message: data.msg,
          //   type: 'warning'
          // })
        }
      })
    },
    // 获取 报警来源分析数据
    getAlarmSourceCount() {
      GetAlarmSourceCount({ timeType: 3 }).then((res) => {
        const data = res.data
        if (data.code === '200') {
          // 报警来源分析
          this.getAlarmSourceEchart(data.data)
        } else {
          // this.$message({
          //   message: data.msg,
          //   type: 'warning'
          // })
        }
      })
    },
    // 获取列表数据
    getTableData() {
      const params = {
        pageNo: 1,
        pageSize: 50,
        timeOrType: ''
      }
      this.tableLoading = true
      GetAlarmRecordAll(params).then((res) => {
        const data = res.data
        this.tableLoading = false
        if (data.code === '200') {
          this.tableData = data.data.records
        } else {
          // this.$message({
          //   message: data.msg,
          //   type: 'warning'
          // })
        }
      })
    },
    getAlarmSourceEchart(list) {
      const getchart = echarts.init(document.getElementById('alarmSourceEchart'))
      getchart.resize()
      var scaleData = list
      var xdata = list.map((item) => {
        return item.projectName
      })
      var data = []
      var color = ['rgba(31, 250, 255, 0.3)', 'rgba(255, 227, 166, 0.28)', 'rgba(147, 130, 255, 0.26)', 'rgba(0, 248, 114, 0.24)', 'rgba(46, 119, 251, 0.3)']
      var borderColor = ['rgba(31, 250, 255, 1)', 'rgba(255, 227, 166, 1)', 'rgba(147, 130, 255, 1)', 'rgba(0, 248, 114, 1)', 'rgba(46, 119, 251, 1)']
      for (var i = 0; i < scaleData.length; i++) {
        data.push({
          value: scaleData[i].count,
          name: scaleData[i].projectName,
          itemStyle: {
            normal: {
              borderWidth: 1,
              shadowBlur: 200,
              borderColor: borderColor[i],
              color: color[i],
              label: {
                normal: { show: true }
              }
            }
          }
        })
      }
      var seriesObj = [
        {
          name: '',
          type: 'pie',
          clockWise: false,
          center: ['25%', '50%'],
          radius: ['55%', '80%'],
          hoverAnimation: true,
          label: {
            normal: {
              show: false,
              position: 'center',
              formatter: '{text|{b}}\n{c} ({d}%)',
              rich: {
                value: {
                  padding: 5,
                  align: 'center',
                  verticalAlign: 'middle',
                  fontSize: 20
                },
                label: {
                  align: 'center',
                  verticalAlign: 'middle',
                  fontSize: 12
                }
              }
            },
            emphasis: {
              show: true,
              textStyle: {
                fontSize: '12',
                color: '#fff'
              }
            }
          },
          data: data
        }
      ]
      const option = {
        backgroundColor: 'rgba(128, 128, 128, 0)',
        legend: {
          orient: 'vertical',
          type: 'scroll',
          x: 'left',
          top: 'center',
          left: '50%',
          bottom: '0%',
          data: xdata,
          itemWidth: 8,
          itemHeight: 8,
          itemGap: 16,
          pageTextStyle: {
            color: '#fff'
          },
          textStyle: {
            //  fontSize: 18,//字体大小
            color: '#B3C2DD' //  字体颜色
          },
          formatter: function (name) {
            var oa = option.series[0].data
            var num = oa.reduce((sum, e) => sum + e.value, 0)
            for (var i = 0; i < option.series[0].data.length; i++) {
              if (name === oa[i].name) {
                return ' ' + name + ' (' + oa[i].value + '件)     ' + ((oa[i].value / num) * 100).toFixed(2) + '%'
              }
            }
          }
        },
        series: seriesObj
      }
      getchart.setOption(option)
      // 随着屏幕大小调节图表
      window.addEventListener('resize', () => {
        getchart.resize()
      })
    },
    // 列表跳转
    warnListRouter() {
      try {
        window.chrome.webview.hostObjects.sync.bridge.LeftMenuSwitch('IsCheckedYJ')
      } catch (error) {}
      this.$router.push({
        path: '/EmergencyDisposal',
        query: {
          pageType: 2,
          sourcePath: '/operationMonitoring'
        }
      })
    },
    getMachineRoom() {
      const params = {
        userName: 'admin',
        userId: 2,
        isHistory: 0,
        page: 1,
        pageSize: 4,
        projectCode: '73e7aab447b34971b9ae6d8dae034aa3',
        entityMenuCode: '544b815ef8204319a7db5e4c13a87905,4f5fc4c264d2416ca7428d5479aa83dc'
      }
      getRealMonitoringListNew(params).then((res) => {
        const data = res.data
        if (data.code === '200') {
          const compressedAir = data.data.statusList.find((v) => v.menuCode === '544b815ef8204319a7db5e4c13a87905')
          const negativePressure = data.data.statusList.find((v) => v.menuCode === '4f5fc4c264d2416ca7428d5479aa83dc')
          this.machineRoom.compressedAirList = data.data.dataList.find((v) => (v.surveyEntityName === '压缩') !== -1)
          this.machineRoom.negativePressureList = data.data.dataList.find((v) => v.surveyEntityName.indexOf('负压') !== -1)
          this.machineRoom.compressedAir = [
            { value: compressedAir.normalCount, name: '正常', lineColor: '#FFE3A6', color: '#7EAEF9' },
            { value: compressedAir.offlineCount, name: '离线', lineColor: '#7EAEF9', color: '#7EAEF9' },
            { value: compressedAir.faultCount, name: '异常', lineColor: '#FF5454', color: '#FF5454' }
          ]
          this.machineRoom.negativePressure = [
            { value: negativePressure.normalCount, name: '正常', lineColor: '#FFE3A6', color: '#7EAEF9' },
            { value: negativePressure.offlineCount, name: '离线', lineColor: '#7EAEF9', color: '#7EAEF9' },
            { value: negativePressure.faultCount, name: '异常', lineColor: '#FF5454', color: '#FF5454' }
          ]
        } else {
          // this.$message({
          //   message: data.message,
          //   type: 'warning'
          // })
        }
      })
    },

    getRealMonitoringListNew() {
      const params = {
        userName: 'admin',
        userId: 2,
        isHistory: 0,
        page: 1,
        pageSize: 2,
        projectCode: '73e7aab447b34971b9ae6d8dae034aa3',
        surveyCode: '032149eef10e4362946d9d62c37c9ce9,ffeb580e09d04a829815fa1e24a9a668'
      }
      getRealMonitoringListNew(params).then((res) => {
        const data = res.data
        if (data.code === '200') {
          data.data?.dataList?.forEach((item) => {
            item.totalNum = item.parameterList[item.parameterList.map((v) => v.parameterId).indexOf(35)]?.parameterValue ?? 0
            item.parameterList = item.parameterList.filter((v) => v.parameterId !== 35)
          })
          this.medicalGasList = data.data.dataList
        } else {
          // this.$message({
          //   message: data.msg,
          //   type: 'warning'
          // })
        }
      })
    },
    // 获取 实时监测数据
    getHosUseMonitoringData(stateData) {
      const params = {
        userName: 'admin',
        userId: 2,
        isHistory: 0,
        page: 1,
        pageSize: 20,
        projectCode: '01f4bd80e5c060809aae72c7470e8be30',
        surveyCode: ''
      }
      getRealMonitoringList(params).then((res) => {
        const data = res.data
        if (data.code === '200') {
          const upsList = []
          if (data.data) {
            upsList.push({
              id: 0,
              type: 'UPS',
              name: 'UPS主机',
              list: [
                { value: stateData.normalCount, name: '正常', lineColor: '#FFE3A6', color: '#7EAEF9' },
                { value: stateData.offLineCount, name: '离线', lineColor: '#7EAEF9', color: '#7EAEF9' },
                { value: stateData.faultCount, name: '异常', lineColor: '#FF5454', color: '#FF5454' }
              ]
            })
            data.data.list &&
              data.data.list.forEach((item) => {
                upsList.push({
                  id: item.surveyEntityCode,
                  name: item.surveyEntityName,
                  parameterList: [this.group(item.parameterList, 2)[0], this.group(item.parameterList, 2)[1]]
                })
              })
          }
          this.groupedArray = this.group(upsList, 3)
        } else {
          // this.$message({
          //   message: data.msg,
          //   type: 'warning'
          // })
        }
      })
    },
    getRealMonitoringListGasGroup() {
      const params = {
        page: 1,
        pageSize: 20,
        projectCode: '73e7aab447b34971b9ae6d8dae034aa3',
        entityMenuCode: '9b6b3b5f47de48278fcfa4ff0c857216'
      }
      GetRealMonitoringListGasGroup(params).then((res) => {
        const data = res.data
        if (data.code === '200') {
          this.monitoringListGroup = data.data
        } else {
          // this.$message({
          //   message: data.msg,
          //   type: 'warning'
          // })
        }
      })
    },
    // 获取 科室状态监测
    getParamStateNum() {
      const params = {}
      getParamStateNum(params).then((res) => {
        const data = res.data
        if (data.code === '200') {
          if (data.data.length) {
            this.deptStateList = [
              { value: data.data[0]?.zstate ?? 0, name: '正常', lineColor: '#FFE3A6', color: '#7EAEF9' },
              { value: data.data[0]?.lstate ?? 0, name: '离线', lineColor: '#7EAEF9', color: '#7EAEF9' },
              { value: data.data[0]?.estate ?? 0, name: '异常', lineColor: '#FF5454', color: '#FF5454' }
            ]
          }
        } else {
          // this.$message({
          //   message: data.msg,
          //   type: 'warning'
          // })
        }
      })
    },
    // 获取 站房数据
    getEnvParam() {
      const params = {
        // page: 1,
        // pageSize: 3,
        menuCode: '6eab069b59f54891a0fbacb342c2d1e3'
      }
      GetGasEnvParamNew(params).then((res) => {
        const data = res.data
        if (data.code === '200') {
          this.envParams = data.data?.parameters ?? []
        } else {
          // this.$message({
          //   message: data.msg,
          //   type: 'warning'
          // })
        }
      })
    },
    // 获取空调分组以及照明分组
    getMenuGroup() {
      const requestParams = {
        projectCode: '00f4ad80e5c04809aae72c7470e8be28,fb78bc9c7e5311ec8e4f000c2912d8ca'
      }
      const newArr = [
        { name: '新风', img: require('@/assets/images/peace/center-item-xinfeng.png'), projectCode: 'fb78bc9c7e5311ec8e4f000c2912d8ca'},
        { name: '送排风', img: require('@/assets/images/peace/center-item-paifeng.png'), projectCode: 'fb78bc9c7e5311ec8e4f000c2912d8ca'},
        { name: '净化', img: require('@/assets/images/peace/center-item-jinghua.png'), projectCode: 'fb78bc9c7e5311ec8e4f000c2912d8ca'},
        { name: '照明', img: require('@/assets/images/peace/center-item-zhaoming.png'), projectCode: '00f4ad80e5c04809aae72c7470e8be28' }
      ]
      GetMenuGroup(requestParams).then((res) => {
        if (res.data.code === '200') {
          res.data.data.forEach((e) => {
            newArr.forEach((v) => {
              if (e.menuName.indexOf(v.name) !== -1) {
                e.img = v.img
                e.projectCode = v.projectCode
              }
            })
          })
          this.monitorStateList = res.data.data
        }
      })
    }
  }
}
</script>
<style lang="scss" scoped>
@import '~@/assets/sino-ui/common/var.scss';
.content {
  position: relative;
  // background-color: #031553;
  background: url('~@/assets/images/qhdsys/bj.png') no-repeat;

  box-sizing: border-box;
  width: 100%;
  height: 100%;
  padding: 1.25rem 0.625rem 0.625rem 0.625rem;
  .eahart-list {
    width: 100%;
    height: 50%;
    display: flex;
    justify-content: space-between;
    .echarts-left {
      width: 29.3%;
      background: url('~@/assets/images/qhdsys/left-operation-bg-50.png') no-repeat;
      background-size: 100% 100%;
      padding: 4px;
      box-sizing: border-box;
      ::v-deep .el-carousel__item {
        padding: 10px;
        box-sizing: border-box;
        color: #fff;
        display: flex;
        flex-direction: column;
        justify-content: space-evenly;
      }
    }
    .echarts-center {
      width: 40%;
      height: 100%;
      background: url('~@/assets/images/qhdsys/left-operation-bg-60.png') no-repeat;
      background-size: 100% 100%;
      padding: 4px;
      box-sizing: border-box;
      .bg-content {
        padding: 16px 32px;
        align-items: center;
        flex-wrap: wrap;
        justify-content: space-between;
      }
      .content-item {
        width: calc(50% - 25px);
        .item-main {
          display: flex;
          padding-left: 20px;
          align-items: center;
          margin-top: 10px;
          .main-left {
            cursor: pointer;
            width: 59px;
            height: 68px;
            background: url('~@/assets/images/peace/center-item-bgd.png');
            position: relative;
            img {
              position: absolute;
              bottom: 15%;
              left: calc(50% - 12px);
              width: 30px;
              height: 41px;
              animation: jump 1s ease-out infinite alternate-reverse;
            }
          }
          .main-right {
            display: flex;
            flex-wrap: wrap;
            flex: 1;
            padding-left: 40px;
            .right-text {
              width: 50%;
            }
            .right-title {
              font-size: 14px;
              font-weight: 400;
              color: #7eaef9;
              line-height: 16px;
              margin-bottom: 8px;
            }
            .right-value {
              font-size: 17px;
              font-weight: bold;
              color: #ffffff;
              line-height: 20px;
              text-shadow: 0px 0px 10px #0081ff;
            }
            .right-value-red {
              color: #ff5454 !important;
              text-shadow: none !important;
            }
            .right-unit {
              margin-left: 4px;
              font-size: 12px;
              font-weight: 400;
              color: #ffffff;
              line-height: 14px;
            }
          }
        }
      }
    }
    .echarts-right {
      width: 30%;
      .echarts-right-top {
        background: url('~@/assets/images/qhdsys/left-operation-bg-right.png') no-repeat;
        background-size: 100% 100%;
        width: 100%;
        height: calc(50% - 5px);
        .bg-content {
          padding: 10px 0px 10px 21px;
          align-items: center;
        }
        .content-left {
          width: 135px;
          height: 131px;
          background: url('~@/assets/images/peace/alarm-left-bgm.png') no-repeat;
        }
        .content-right {
          height: 100%;
          margin-left: 24px;
          flex: 1;
          display: flex;
          flex-wrap: wrap;
          align-content: space-around;
          .right-item {
            width: 50%;
          }
          .right-title {
            font-size: 16px;
            font-weight: 500;
            color: #7eaef9;
            line-height: 19px;
          }
          .right-value {
            margin-top: 3px;
            display: flex;
            align-items: center;
          }
          .right-num {
            font-size: 26px;
            font-weight: bold;
            color: #ffffff;
            line-height: 30px;
            font-style: oblique;
          }
          .right-ratio {
            margin-left: 10px;
            padding-left: 7px;
            font-size: 14px;
            font-weight: 400;
            height: 17px;
            line-height: 17px;
            background: linear-gradient(90deg, rgba(106, 143, 211, 0.41) 0%, rgba(106, 143, 211, 0) 94%);
            img {
              margin-left: 2px;
              width: 6px;
              height: 9px;
            }
          }
        }
      }
      .echarts-right-bottom {
        background: url('~@/assets/images/qhdsys/left-operation-bg-right.png') no-repeat;
        background-size: 100% 100%;
        width: 100%;
        height: 50%;
        margin-top: 5px;
        .case-anim-icon {
          position: absolute;
          top: calc(50% - 1.3rem);
          left: calc(25% - 0.5rem);
          width: 2rem;
          height: 2rem;
          background: url('~@/assets/images/peace/icon-warn.png') no-repeat;
          background-size: 100% 100%;
        }
      }
    }
  }
  .content-bottom {
    width: 100%;
    height: calc(50% - 10px);
    margin-top: 10px;
    display: flex;
    .bottom-left {
      width: calc(70% - 5px);
      background: url('~@/assets/images/qhdsys/left-operation-bg-bottom.png') no-repeat;
      background-size: 100% 100%;
      padding: 4px;
      box-sizing: border-box;
      margin-right: 5px;
      z-index: 1;
      .dept-monitor {
        height: 30px;
        line-height: 30px;
        margin: 10px 0;
        font-size: 16px;
        font-family: PingFangSC-Medium, PingFang SC;
        color: #ffffff;
        > span {
          display: inline-block;
          width: 14px;
          height: 14px;
          background: url('~@/assets/images/peace/arrow-right.png') no-repeat;
          background-size: 100% 100%;
          margin-right: 6px;
        }
      }
      .gas-left {
        width: 30%;
        display: flex;
        flex-direction: column;
        padding: 14px 10px 10px 45px;
        .gas-left-list {
          flex: 1;
          .list-item {
            cursor: pointer;
            height: 50%;
            display: flex;
            align-items: center;
            .item-left {
              height: 110px;
              width: 74px;
              text-align: center;
              background: url('~@/assets/images/peace/gas_bgd.png') no-repeat center;
              background-size: 74px 110px;
            }
            .left-title {
              margin-top: 5px;
              font-size: 12px;
              color: #cfe1f7;
              line-height: 14px;
            }
            .left-value {
              margin-top: 4px;
              font-size: 20px;
              color: #ffffff;
              line-height: 23px;
            }
            .item-right {
              flex: 1;
              margin-left: 30px;
            }
            .right-title {
              height: 24px;
              color: #fff;
              padding-left: 8px;
              line-height: 24px;
              font-size: 14px;
              background: linear-gradient(90deg, rgba(29, 80, 175, 0.58) 0%, rgba(29, 80, 175, 0) 40%);
              border-radius: 0px 0px 0px 0px;
            }
            .right-name {
              font-size: 14px;
              font-weight: 400;
              color: #7eaef9;
              line-height: 16px;
            }
            .right-value {
              margin-top: 10px;
            }
            .right-num {
              font-size: 16px;
              font-weight: 500;
              color: #ffffff;
              line-height: 19px;
            }
          }
        }
      }
      .gas-list {
        display: flex;
        flex-wrap: wrap;
        justify-content: center;
        .gas-list-item {
          display: flex;
          flex-direction: column;
          align-items: center;
          .item-value {
            font-size: 17px;
            font-weight: bold;
            color: #ffffff;
            line-height: 20px;
            i {
              font-size: 12px;
            }
          }
          .item-name {
            font-size: 14px;
            margin-top: 4px;
            color: #7eaef9;
          }
        }
      }
      .gas-center {
        width: 40%;
        padding: 14px 36px 10px 36px;
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        .gas-group {
          display: flex;
          justify-content: space-between;
          .gas-group-item {
            display: flex;
            flex-direction: column;
            align-items: center;
          }
        }
      }
      .gas-right {
        width: 30%;
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        padding: 14px 36px 10px 36px;
      }
      .right-line {
        position: relative;
        &::before {
          content: '';
          position: absolute;
          width: 1px;
          height: 80%;
          background: #2756b2;
          top: 50%;
          right: 0;
          transform: translateY(-50%);
        }
      }
      .right-content-line {
        position: relative;
        &::before {
          content: '';
          position: absolute;
          width: 1px;
          height: 80%;
          background: #384d81;
          top: 50%;
          right: 0;
          transform: translateY(-50%);
        }
      }
    }
    .bottom-right {
      width: 30%;
      background: url('~@/assets/images/qhdsys/left-operation-bg-50.png') no-repeat;
      background-size: 100% 100%;
      padding: 4px;
      box-sizing: border-box;
      div.el-table {
        border: none !important;
      }
    }
  }
  .subtitle {
    font-size: 16px;
    font-weight: 500;
    color: #ffffff;
    line-height: 19px;
    padding-left: 32px;
    background: url('~@/assets/images/peace/subtitle-arrow.png') no-repeat left center / 20px 14px;
  }
  .bg-title {
    // margin-top: 5px;
    height: 2.5rem;
    line-height: 2rem;
    color: #d4e3f9;
    padding-left: 3rem;
    font-family: TRENDS;
  }
  .warn-message-title {
    display: flex;
    justify-content: space-between;
    :last-child {
      margin-right: 10px;
      font-size: 14px;
      font-family: PingFang-SC-Medium, PingFang-SC;
      color: #7eaef9;
      cursor: pointer;
    }
  }
  .bg-content {
    position: relative;
    box-sizing: border-box;
    padding: 10px 10px 15px 10px;
    width: 100%;
    height: calc(100% - 2.5rem);
    display: flex;
    #alarmSourceEchart {
      width: 100%;
      height: 100%;
      position: relative;
      z-index: 2;
    }
    .alarm-bg {
      width: 12.8rem;
      height: calc(120% - 1.5rem);
      position: absolute;
      top: -5%;
      left: 2.6rem;
      background: url('~@/assets/images/peace/left-operation-warn-bg.png') no-repeat;
      background-size: 100% 100%;
    }
  }
  .list {
    width: 100%;
    display: flex;
    .left {
      display: flex;
      flex-direction: column;
      text-align: center;
      width: 20%;
      img {
        width: 90%;
        margin-left: 5%;
        margin-bottom: 5px;
      }
      span {
        font-size: 13px;
        font-family: PingFang-SC-Medium, PingFang-SC;
        color: #7eaef9;
        text-overflow: ellipsis;
        white-space: nowrap;
        overflow: hidden;
      }
    }
    .progress {
      width: 75%;
      margin-left: 5%;
      background: url('~@/assets/images/peace/left-operation-border.png') no-repeat;
      background-size: 100% 100%;
      display: flex;
      flex-direction: column;
      justify-content: space-around;
      .list-data {
        width: 100%;
        height: 70%;
        display: flex;
        flex-direction: column;
        justify-content: space-around;
        .data-data {
          display: flex;
          // justify-content: space-between;
          padding-left: 20px;
          div {
            flex: 1;
            text-align: left;
            font-size: 14px;
            font-family: PingFang-SC-Medium, PingFang-SC;
            span:last-child {
              color: #7eaef9;
            }
          }
        }
      }
    }
  }
  ::v-deep .carousel {
    width: 100%;
    height: 100%;
    .el-carousel__indicators {
      display: none;
    }
    .el-carousel__container {
      height: 100%;
    }
    .el-carousel__arrow {
      width: 30px;
      height: 64px;
      background-color: #163274;
      color: #5996f9;
      border-radius: 0;
      font-size: 24px;
    }
    .el-carousel__arrow:hover {
      background-color: #373e5f;
      color: #eed6a0;
    }
    .el-carousel__arrow--left {
      left: -1px;
    }
    .el-carousel__arrow--right {
      right: -1px;
    }
  }
}
::v-deep .el-table--striped .el-table__body tr.el-table__row--striped td {
  background-color: rgba(168, 172, 171, 0.08) !important; /* def2ff f2faff */
}
</style>
