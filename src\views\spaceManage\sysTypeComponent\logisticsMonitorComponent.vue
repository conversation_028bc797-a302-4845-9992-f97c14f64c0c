<template>
  <div class="logisticsMonitorComponent">
    <ModuleCard title="机器人状态" class="module-container" style="height: 24%">
      <div slot="content" v-loading="statisticsEchartsLoading" class="module-content" style="height: 100%">
        <div v-if="workOrderStatisticsShow" style="width: 100%; height: 100%" class="chartsContent">
          <div id="workOrderStatisticsEcharts" ref="workOrderStatisticsEcharts"></div>
        </div>
        <div v-else class="center-center">暂无数据</div>
      </div>
    </ModuleCard>
    <ModuleCard title="设备列表" style="height: calc(76%)" class="container-box">
      <div slot="content" v-loading="tableLoading" v-infinite-scroll="tableLoadMore" style="height: calc(100% - 10px)">
        <div class="realTime-module">
          <div v-for="(item, index) in tableData" :key="index" class="realTime-module-box">
            <div class="rtm-header-box" :style="{borderColor: item.alarmStatus == 1 ? '#FF2D55' : 'rgba(133,145,206,0.5)'}" @click="tabledblClick(item)">
              <div class="rtm-header-title">
                <p>
                  <span>{{ item.assetsName }}</span>
                  <span :style="{
                    color: item.alarmStatus == 1 ? '#FF2D55' : (item.onlineStatus == 1 ? '#61E29D' : '#D4DEEC'),
                    background: item.alarmStatus == 1 ? 'rgba(255, 45, 85, .2)' : (item.onlineStatus == 1 ? 'rgba(97, 226, 157, .2)' : 'rgba(212, 222, 236, .2)')
                  }">{{ item.alarmStatus == 1 ? '报警' : (item.onlineStatus == 1 ? '在线' : '离线') }}</span>
                </p>
              </div>
              <div class="rtm-header-info">
                <p class="info-item">
                  <span style="color: #C4F3FE;">机器人状态：</span>
                  <span>{{ item.robotStatus }}</span>
                </p>
                <p class="info-item">
                  <span style="color: #C4F3FE;">电量：</span>
                  <span>{{ item.electric }}</span>
                </p>
                <p class="info-item">
                  <!-- <span style="color: #C4F3FE;">设备类型：</span>
                  <span>{{ item.deviceTypeName }}</span> -->
                  <span style="color: #C4F3FE;">当前位置：</span>
                  <span>{{ item.locationName }}</span>
                </p>
              </div>
            </div>
          </div>
        </div>
        <div v-if="!tableData.length" class="center-center">暂无数据</div>
      </div>
    </ModuleCard>
  </div>
</template>

<script>
import * as echarts from 'echarts'
import { GetFactoryCodeByAgv, GetGeneralFunctionByAgv, GetMonitoringItemsList } from '@/utils/spaceManage'
export default {
  name: 'logisticsMonitorComponent',
  props: {
    roomData: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      workOrderStatisticsShow: true,
      statisticsEchartsLoading: false,
      tableLoading: false,
      tableData: [],
      pageParams: {
        total: 0,
        pageSize: 15,
        pageNo: 1
      }
    }
  },
  watch: {
    roomData: {
      handler: function(val) {
        this.pageParams.pageNo = 1
        if (val.projectCode && val.categoryCode) {
          this.getFactoryCodeByAgv()
          this.getMonitoringItemsList()
        }
      },
      deep: true
      // immediate: true
    }
  },
  mounted() {
    if (this.roomData.projectCode && this.roomData.categoryCode) {
      this.getFactoryCodeByAgv()
      this.getMonitoringItemsList()
    }
  },
  methods: {
    tabledblClick(row) {
      // return
      this.$emit('roomEvent', {
        type: 'move',
        assetId: row.id,
        assetName: row.assetsName,
        modelCode: row.modelCode,
        spaceLocation: row.spaceLocation
      })
      // try {
      //   window.chrome.webview.hostObjects.sync.bridge.EquipmentRealView(JSON.stringify({
      //     assetsId: row.id,
      //     DeviceName: row.assetsName,
      //     DeviceCode: row.modelCode,
      //     spaceLocation: row.spaceLocation
      //   }))
      // } catch (error) {}
    },
    // 获取机器人网关id
    getFactoryCodeByAgv() {
      this.statisticsEchartsLoading = true
      GetFactoryCodeByAgv().then(res => {
        if (res.data.code == 200) {
          this.getGeneralFunctionByAgv(res.data.data)
        }
      })
    },
    // 获取机器人统计
    getGeneralFunctionByAgv(deviceId) {
      let params = {
        deviceId,
        functionId: 'robotStatus',
        params: {}
      }
      GetGeneralFunctionByAgv(params).then(res => {
        this.statisticsEchartsLoading = false
        if (res.data.status == 200) {
          let data = res.data.result[0]
          this.$nextTick(() => {
            this.initEcharts(data.total, [
              { name: '空闲', value: data.idling },
              { name: '工作中', value: data.working },
              { name: '充电中', value: data.charging },
              { name: '呼叫中', value: data.calling },
              { name: '离线', value: data.offline },
              { name: '故障', value: data.failing }
            ])
          })
        }
      }).catch(err => {
        this.statisticsEchartsLoading = false
      })
    },
    // 获取物流机器人列表
    getMonitoringItemsList() {
      let params = {
        sysOfCode: this.roomData.projectCode,
        sysOf1Code: this.roomData.categoryCode,
        page: this.pageParams.pageNo,
        pageSize: this.pageParams.pageSize
      }
      this.tableLoading = true
      GetMonitoringItemsList(params).then(res => {
        this.tableLoading = false
        if (res.data.code == 200) {
          if (this.pageParams.pageNo == 1) this.tableData = []
          let data = res.data.data
          data.records.forEach(item => {
            if (item.iotPropertyList.length) {
              item.robotStatus = item.iotPropertyList.find(v => v.metadataTag == 'robotStatus')?.valueText ?? '-'
              item.electric = item.iotPropertyList.find(v => v.metadataTag == 'electric')?.valueText ?? '-'
              item.locationName = item.iotPropertyList.find(v => v.metadataTag == 'lastPositionName')?.valueText ?? '-'
            }
          })
          this.tableData = this.tableData.concat(data.records)
          this.pageParams.total = data.total
        }
      }).catch(err => {
        this.tableLoading = false
      })
    },
    tableLoadMore() {
      if (this.pageParams.total > this.pageParams.pageNo * this.pageParams.pageSize) {
        this.pageParams.pageNo++
        this.getMonitoringItemsList()
      }
    },
    initEcharts(total, data) {
      const getchart = echarts.init(this.$refs.workOrderStatisticsEcharts)
      // 计算总数
      const color = [
        '#8fe7ea',
        '#74e2a0',
        '#f2d988',
        '#e38e6f',
        '#b3c2dd',
        '#ff5454'
      ]
      // 创建两个系列：一个用于显示饼图，一个用于显示中心文字
      const option = {
        backgroundColor: '',
        legend: {
          selectedMode: false,
          icon: 'circle',
          orient: 'vartical',
          x: 'left',
          top: 'center',
          left: '52%',
          bottom: '0%',
          data: data.map((item) => item.name),
          itemWidth: 8,
          itemHeight: 8,
          itemGap: 8,
          textStyle: {
            color: '#B3C2DD'
          },
          formatter: function (name) {
            const item = data.find((item) => item.name === name)
            return ' ' + name + '     ' + item.value
          }
        },
        series: [
          {
            name: '机器人状态',
            type: 'pie',
            clockWise: false,
            center: ['30%', '50%'],
            radius: ['58%', '70%'],
            emphasis: {
              scale: false,
              itemStyle: {
                borderWidth: 0,
                shadowBlur: 0
              }
            },
            label: {
              show: false
            },
            data: data.map((item, index) => ({
              name: item.name,
              value: item.value,
              itemStyle: {
                normal: {
                  borderWidth: 0,
                  shadowBlur: 0,
                  color: color[index]
                }
              }
            }))
          },
          {
            name: '中心文字',
            type: 'pie',
            clockWise: false,
            center: ['30%', '50%'],
            radius: 0,
            hoverAnimation: false, // 禁用鼠标悬浮动画
            emphasis: {
              // 禁用鼠标悬浮高亮效果
              scale: false,
              itemStyle: {
                color: 'transparent'
              }
            },
            label: {
              normal: {
                show: true,
                position: 'center',
                formatter: [`{value|${total}}`, '{label|设备总数}'].join('\n'),
                rich: {
                  value: {
                    color: '#fff',
                    fontSize: 24,
                    padding: [0, 0, 10, 0],
                    fontFamily: 'DIN'
                  },
                  label: {
                    color: '#B3C2DD',
                    fontSize: 14,
                    padding: [0, 0, 0, 0]
                  }
                }
              }
            },
            itemStyle: {
              // 添加这个配置
              normal: {
                color: 'transparent',
                borderWidth: 0,
                shadowBlur: 0
              }
            },
            zlevel: 2, // 将中心文字置于最上层
            data: [{ value: 100 }]
          }
        ]
      }

      getchart.clear()
      getchart.setOption(option)

      // 随着屏幕大小调节图表
      window.addEventListener('resize', () => {
        getchart.resize()
      })

      // 默认高亮第一个
      getchart.dispatchAction({
        type: 'highlight',
        seriesIndex: 0,
        dataIndex: 0
      })

      getchart.on('mouseover', () => {
        getchart.dispatchAction({
          type: 'downplay',
          seriesIndex: 0,
          dataIndex: 0
        })
      })

      getchart.on('mouseout', () => {
        getchart.dispatchAction({
          type: 'highlight',
          seriesIndex: 0,
          dataIndex: 0
        })
      })
    }
  }
}
</script>

<style lang="scss" scoped>
// 隐藏滚动条
::-webkit-scrollbar {
  display: none;
}
%flex-styles {
  display: flex;
  align-items: center;
}
@import "../style/module.scss";
.logisticsMonitorComponent {
  width: 100%;
  height: 100%;
  overflow-y: auto;
  #workOrderStatisticsEcharts {
    width: 100%;
    height: 100%;
    position: relative;
    z-index: 2;
  }
  .realTime-module {
    .realTime-module-box {
      background: #8591ce26;
      margin-bottom: 10px;
      .rtm-header-box {
        padding: 16px;
        cursor: pointer;
        border: 1px solid;
        .rtm-header-title {
          @extend %flex-styles;
          justify-content: space-between;
          margin-bottom: 16px;
          font-size: 15px;
          span:last-child{
            margin-left: 10px;
            font-size: 14px;
            padding: 3px 8px;
            border-radius: 100px;
            line-height: 16px;
            display: inline-block;
          }
        }
        .rtm-header-info {
          font-size: 14px;
          display: flex;
          .info-item {
            width: calc(100% / 3);
            span {
              display: block;
            }
            span:last-child {
              margin-top: 8px;
              width: 100%;
              overflow:hidden;
              text-overflow:ellipsis;
              white-space:nowrap;
            }
          }
        }
      }
      .rtm-table-box {
        border: 1px solid rgba(133,145,206,0.5);
        border-top: none;
      }
    }
  }
}
</style>
