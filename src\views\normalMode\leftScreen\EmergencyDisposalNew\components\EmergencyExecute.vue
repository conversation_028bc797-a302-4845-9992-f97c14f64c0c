<template>
  <div class='component EmergencyExecute'>
    <EmergencyExecuteBaseInfo table-height="165px" />
    <el-form class="EmergencyExecute__form" ref="formRef" :model="formModel" :rules="rules" label-width="100px"
      label-suffix=":">
      <div class="EmergencyExecute__wrapper EmergencyExecute__wrapper--upload">
        <el-form-item label="现场记录文件" prop="fileData">
          <FileUploadCard :file-data.sync="formModel.fileData" :alarmId="alarmId" />
        </el-form-item>
      </div>
      <div class="EmergencyExecute__wrapper">
        <el-form-item label="处理结果">
          <TextTagArea :tag="extraData.tag" v-model="formModel.remark" :limit="100" />
        </el-form-item>
        <el-form-item label="处理人" style="margin-top: -10px;">
          <EmergencyUserSelect v-model="formModel.createUser" />
        </el-form-item>
      </div>
    </el-form>
  </div>
</template>


<script>
import TextTagArea from "./TextTagArea.vue";
import FileUploadCard from "./FileUploadCard.vue"
import EmergencyExecuteBaseInfo from "./EmergencyExecuteBaseInfo.vue";
import EmergencyUserSelect from "./EmergencyUserSelect.vue";

export default {
  name: 'EmergencyExecute',
  components: {
    TextTagArea,
    FileUploadCard,
    EmergencyExecuteBaseInfo,
    EmergencyUserSelect
  },
  props: {
    extraData: {
      type: Object,
      default: () => { }
    },
    alarmId: String
  },
  computed: {
    detail: function () {
      return this.$parent.detail || {}
    },
  },
  data() {
    return {
      remark: '',
      formModel: {
        fileData: [],
        remark: '',
        createUser: []
      },
      rules: {

      }
    }
  },
  filters: {
    defaultVal: function (val) {
      return val || "-"
    },
  },
  mounted() {
    const userInfo = this.$store.state.loginInfo.user;
    this.formModel.createUser = [
      {
        id: userInfo.id,
        staffNumber: userInfo.staffNumber,
        staffName: userInfo.staffName
      }
    ]
  },
  methods: {
    /** 表单校验，成功返回表单数据 */
    validate() {
      return this.$refs.formRef
        .validate()
        .then(() => {
          const [user] = this.formModel.createUser;
          if (!user) throw '请选择操作人';
          let createName = user.staffName;
          if (user.staffNumber) {
            createName += `(${user.staffNumber})`
          }
          const res = {
            remark: this.extraData.tag + this.formModel.remark,
            operationUrl: JSON.stringify(this.formModel.fileData),
            createName
          }
          return res;
        })
    },
  }
}


</script>

<style lang='scss' scoped>
.component.EmergencyExecute {
  display: flex;
  flex-flow: column nowrap;
  height: 100%;
  overflow: hidden;

  .content-head {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px;
    height: 52px;
  }


  .description {
    &__content {
      display: flex;
      flex-flow: row wrap;

      >li {
        flex-basis: 50%;
        display: flex;
        flex-flow: row nowrap;
        margin-top: 24px;

        &.full-row {
          flex-basis: 100%;
        }

        >div:first-of-type {
          color: #B0E3FA;
          width: 100px;
          text-align: right;

          &::after {
            content: '：';
          }

        }

        >div:last-child {
          width: 0;
          flex: 1;
          overflow: hidden;
          white-space: nowrap;
          text-overflow: ellipsis;
        }
      }
    }

    &__imgs {
      display: flex;
      flex-flow: row wrap;
    }

    &__img {
      height: 100px;
      width: 100px;
      overflow: hidden;
      margin: 0 10px 10px 0;

      >img {
        height: 100%;
        width: 100%;
        object-fit: cover;
        object-position: center;
      }
    }
  }

  .EmergencyExecute {
    &__description {
      flex: 1;
      background: rgba(133, 145, 206, 0.15);
      overflow: hidden;
      display: flex;
      flex-flow: column nowrap;
      padding-bottom: 16px;

      &__content {
        padding: 0 16px;
        overflow: auto;
      }

      &__table {
        margin-top: 24px;

        &__tag {
          display: flex;
          align-items: center;

          >img {
            height: 16px;
            width: 16px;
            margin-right: 2px;
          }

          &--finish {
            >span {
              color: #61E29D;
            }
          }

          &--incomplete {
            >span {
              color: #FF2D55;
            }
          }
        }
      }
    }

    &__form {
      ::v-deep .el-form-item__label {
        color: #B0E3FA;
      }

      .el-form-item:last-of-type {
        margin-bottom: 16px;
      }
    }

    &__wrapper {
      background: rgba(133, 145, 206, 0.15);
      margin-top: 16px;
      padding: 16px 16px 1px;

      &--upload {
        height: 200px;

        >.el-form-item {
          height: 168px;
          overflow: hidden;

          ::v-deep .el-form-item__content {
            height: 100%;
            overflow: hidden;
          }
        }
      }
    }

    &__comment {
      margin-top: 16px;
      padding: 16px 0;

      .description__content {
        >li:first-child {
          >div:first-child {
            padding-top: 9px;
          }
        }
      }
    }
  }
}
</style>
