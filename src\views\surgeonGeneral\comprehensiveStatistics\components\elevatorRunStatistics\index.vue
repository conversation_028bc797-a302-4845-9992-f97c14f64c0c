<template>
  <div class="echarts-rightTop" >
    <BgTitle @moreClick="showDetail">
      电梯统计
    </BgTitle>
    <div class="bg-content">
      <LeftTopContent   @showDetail="showDetail"/>
      <LeftBottomContent  @itemClick="showDetail"/>

    </div>
    <OpenDoorRecord v-if="visible" :dialogShow="visible"  @openDoorRecordClose="visible = false" />
  </div>
</template>
<script>
import BgTitle from '../../components/common/bgTitle'
import LeftTopContent from './leftTopContent.vue'
import LeftBottomContent from './leftBottomContent.vue'
import OpenDoorRecord from '@/views/elevator/components/openDoorAnalysis/openDoorRecord.vue'
export default {
  components: {
    BgTitle,
    LeftTopContent,
    LeftBottomContent,
    OpenDoorRecord
  },
  data() {
    return {

      visible: false,
      currentItem: {}
    }
  },

  mounted () {
  },

  methods: {

    showDetail (data) {
      this.currentItem = {
        ...data,
        elevatorId: this.elevatorId,
        date: this.dateType,
        dateRange: this.initDate(this.dateType)
      }
      // this.visible = true
    }

  }
}
</script>
<style lang="scss" scoped>
.echarts-rightTop {
  height: 100%;
  width: calc(33% - 8px);
  background: url("~@/assets/images/bg-content1.png") no-repeat;
  background-size: 100% 100%;
  box-sizing: border-box;
  margin-top: 16px;
  .bg-content {
    position: relative;
    box-sizing: border-box;
    padding: 10px;
    width: 100%;
    height: calc(100% - 44px);
    display: flex;
    flex-wrap: wrap;
  }
  .dropdown-title{
    color: #fff;
    cursor: pointer;
  }
}
.statistics_item {
  width: calc(100% - 4px);
  height: calc(50% - 4px);
  // border: 1px solid #fff;
  margin-right: 8px;
  &:nth-child(even) {
    margin-right: 0;
  }
}

.item_title {
  font-weight: 500;
  font-size: 16px;
  color: #b0e3fa;
  margin-bottom: 10px;
}
:deep(.el-dropdown .el-popper .el-dropdown-menu) {
  max-height: 400px;
  overflow-y: auto;
}
</style>
