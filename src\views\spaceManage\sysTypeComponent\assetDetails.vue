<!-- 资产详情 -->
<template>
  <div class="assetDetails">
    <div class="module-container" style="height: calc(100%); background: rgba(133,145,206,0.15);margin-bottom: 16px;">
      <div class="info-header clear">
        <p class="info-header-text fl">基础信息</p>
      </div>
      <div class="module-content info-list" style="height: calc(100% - 44px)">
        <div v-for="item in infoList" :key="item.label" class="info-list-item">
          <p class="item-label">{{ item.label }}：</p>
          <p v-if="item.key =='assetsStatus'" class="item-value" :style="{color: deviceStatusObj[detailsInfo[item.key]]?.color}">
            {{ deviceStatusObj[detailsInfo[item.key]]?.label ?? '---' }}
          </p>
          <p v-else class="item-value">{{ detailsInfo[item.key] || '---' }}</p>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { GetMonitorAssetInfo } from '@/utils/spaceManage'
export default {
  name: 'assetDetails',
  props: {
    roomData: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      infoList: [
        {label: '设备名称', key: 'assetsName'},
        {label: '设备类型', key: 'sysOf1Name'},
        {label: '设备位置', key: 'spaceLocationName'},
        {label: '设备状态', key: 'assetsStatus'},
        {label: '责任部门', key: 'useDeptName'},
        {label: '安全责任人', key: 'principalName'},
        {label: '责任人电话', key: 'principalPhone'}
      ],
      detailsInfo: {},
      deviceStatusObj: {
        1: {label: '正常', color: '#61E29D'},
        2: {label: '离线', color: '#86909C'},
        3: {label: '异常', color: '#FF2D55'}
      }
    }
  },
  computed: {

  },
  created() {
    this.getMonitorAssetInfo()
  },
  methods: {
    // 获取资产详情
    getMonitorAssetInfo() {
      GetMonitorAssetInfo({assetsId: this.roomData.deviceId}).then(res => {
        if (res.data.code == 200) {
          this.detailsInfo = res.data.data
        } else {
          this.detailsInfo = {}
        }
      })
    }
  }
}

</script>

<style lang="scss" scoped>
@import '../style/module.scss';
.assetDetails {
  padding: 5px 0px 0px 0px;
  .module-header {
    padding-left: 30px;
    padding-right: 10px;
    width: 100%;
    background: url("@/assets/images/qhdsys/bg-bt.png") no-repeat;
    background-size: contain;
  }
  .info-header {
    padding: 6px 8px;
    line-height: 22px;
    background: rgba(133,145,206,0.15);
    .info-header-text {
      font-weight: 500;
      font-size: 15px;
      color: #FFFFFF;
    }
  }
  .info-list {
    padding: 0px 16px;
    .info-list-item {
      display: flex;
      .item-label {
        font-weight: 400;
        font-size: 14px;
        color: #B0E3FA;
        line-height: 30px;
        width: 85px;
      }
      .item-value {
        flex: 1;
        font-weight: 400;
        font-size: 14px;
        color: #FFFFFF;
        line-height: 30px;
      }
    }
  }
  .title-right {
    font-size: 14px;
    align-items: center;
  }
}
</style>
