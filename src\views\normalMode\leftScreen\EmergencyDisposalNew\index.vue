<template>
  <!-- 应急处置-主页面 -->
  <div class="content emergencyDisposalNew" :class="screenSmall ? 'screen_small' : screenBack ? 'screen_back' : ''">
    <div v-if="pageType === 2" class="title">
      <div @click="gobackToDevice"><i class="el-icon-arrow-left"></i>返回</div>
    </div>
    <el-form :model="searchFrom" class="search-form" :class="{ collapsed }" inline>
      <el-form-item>
        <el-input v-model.trim="searchFrom.searchKey" placeholder="报警对象/ID" style="width: 180px;"
                  clearable @keyup.enter.native.stop="searchForm"></el-input>
      </el-form-item>
      <el-form-item>
        <el-select v-model="searchFrom.projectCode" placeholder="报警系统" multiple collapse-tags clearable
                   @change="getAlarmTypeOptions" @clear="() => {
                     searchFrom.alarmType = []
                   }
                   ">
          <el-option v-for="item in alarmSourceOptions" :key="item.projectCode" :label="item.projectName"
                     :value="item.projectCode"> </el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-select v-model="searchFrom.alarmType" placeholder="报警类型" clearable multiple collapse-tags
                   :disabled="searchFrom.projectCode.length !== 1">
          <el-option v-for="item in alarmTypeOption" :key="item.id" :label="item.alarmDictName" :value="item.id">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-date-picker v-model="searchFrom.dataRange" popper-class="date-style" type="daterange" unlink-panels
                        range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期" value-format="yyyy-MM-dd"
                        :picker-options="pickerOptions">
        </el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-select v-model="searchFrom.alarmLevel" placeholder="报警等级" clearable>
          <el-option v-for="item in alarmLevelOptions" :key="item.value" :label="item.label" :value="item.value">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-select v-model="searchFrom.alarmStatus" placeholder="处理状态" clearable>
          <el-option v-for="item in alarmStatusOptions" :key="item.value" :label="item.label" :value="item.value">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-select v-model="searchFrom.alarmAffirm" placeholder="确警类型" clearable>
          <el-option v-for="item in alarmConfirmOptions" :key="item.value" :label="item.label" :value="item.value">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item v-show="!collapsed">
        <el-select ref="treeSelect" v-model="searchFrom.alarmSpaceId" clearable placeholder="报警位置" @clear="handleClear">
          <el-option hidden :value="searchFrom.alarmSpaceId" :label="areaName"> </el-option>
          <el-tree class="search-form-tree" :data="serverSpaces" :props="serverDefaultProps" :load="serverLoadNode" lazy
                   :expand-on-click-node="false" :check-on-click-node="true" @node-click="handleNodeClick">
          </el-tree>
        </el-select>
      </el-form-item>
      <el-form-item>
        <div style="display: inline-block;">
          <el-button class="new-edition" style="background-color: transparent !important"
                     @click="resetForm">重置</el-button>
          <el-button class="new-edition" style="background-color: transparent !important"
                     @click="searchForm">查询</el-button>
          <span class="search-form__collapse-btn" @click="onSearchCollapseClick">{{ collapseText }} <i
            :class="collapseIcon"></i></span>
        </div>
      </el-form-item>
    </el-form>
    <div class="eahart-list">
      <div class="echarts-left">
        <div class="bg-title">报警统计</div>
        <div class="bg-content">
          <div v-for="item in warnAnalysis" :key="item.name" class="right-item">
            <p class="right-title">{{ item.name }}</p>
            <div class="right-value">
              <p class="right-num">{{ item.count || 0 }}</p>
              <p v-if="item.timeType != 3" class="right-ratio">
                <span style="color: #fff; margin-right: 3px">环比</span>
                <span :style="{ color: item.ringRatioType == 1 ? '#00F872' : '#FF5454', fontStyle: 'oblique' }">{{
                  item.ringRatio }}</span>
                <img v-if="item.ringRatioType == 1" src="@/assets/images/peace/ratio-down.png" />
                <img v-else src="@/assets/images/peace/ratio-up.png" />
              </p>
            </div>
          </div>
        </div>
      </div>
      <div class="echarts-center">
        <div class="bg-title">近30日报警走势图</div>
        <div class="bg-content">
          <div id="trendEchart"></div>
        </div>
      </div>
      <div class="echarts-right">
        <!-- <div class="bg-title">报警来源分析</div> -->
        <div class="bg-content" style="height: 100%;">
          <div v-if="warnAnalysisShow" class="center-center">暂无数据</div>
          <div v-else style="width: 100%; height: 100%">
            <div id="alarmSourceEchart"></div>
            <!-- <div class="alarm-bg"></div> -->
            <!-- <div class="case-anim-icon"></div> -->
          </div>
        </div>
      </div>
    </div>
    <div class="batch-control">
      <el-button v-for="(item, index) in batchControlList" :key="index" :disabled="!selectAlarmList.length"
                 class="new-edition" @click="batchControlEvent(item, selectAlarmList)">{{
                   item.label
                 }}</el-button>
    </div>
    <div class="table-list" :class="{ formCollapsed: collapsed }">
      <el-table ref="table" v-loading="tableLoading" :data="tableData" :resizable="false"
                height="calc(100% - 40px)" :cell-style="$tools.setCell(3)" :header-cell-style="$tools.setHeaderCell(3)" style="width: 100%"
                stripe element-loading-background="rgba(0, 0, 0, 0.2)" @selection-change="tableSelectChange"
                @cell-click="onTabCellClick">
        <el-table-column type="selection" width="60" align="center" :selectable="row => canExecute(row)"
                         class-name="table-list__selection"></el-table-column>
        <el-table-column prop="alarmLevel" label="报警等级" width="95px">
          <span slot-scope="scope" :style="{ color: alarmLevelFormatter(scope.row).color }">
            {{ alarmLevelFormatter(scope.row).label }}
          </span>
        </el-table-column>
        <el-table-column prop="alarmId" label="报警ID" show-overflow-tooltip></el-table-column>
        <el-table-column prop="alarmSource" label="报警系统" show-overflow-tooltip></el-table-column>
        <el-table-column prop="alarmType" label="报警类型" show-overflow-tooltip></el-table-column>
        <el-table-column prop="alarmStartTime" label="报警时间" width="175px"></el-table-column>
        <el-table-column prop="completeRegionName" label="报警位置" show-overflow-tooltip></el-table-column>
        <el-table-column prop="alarmObjectName" label="报警对象" show-overflow-tooltip></el-table-column>
        <el-table-column prop="alarmDetails" label="报警描述" show-overflow-tooltip></el-table-column>
        <el-table-column prop="alarmStatus" label="处理状态" width="100px">
          <template #default="{ row }">
            <span :style="{ color: alarmStatusFormatter(row).color }">
              <i :class="alarmStatusFormatter(row).icon"></i>
              {{ alarmStatusFormatter(row).label }}
            </span>
          </template>
        </el-table-column>
        <!-- 0：非经典案例，1：经典案例 -->
        <el-table-column prop="classic" label="" width="50px">
          <el-rate slot-scope="scope" :value="scope.row.classic" :max="1" text-color="#ff9900"
                   @change="onClassicChange(scope.row)">
          </el-rate>
        </el-table-column>
        <el-table-column prop="alarmAffirm" label="警情类型" width="95px" :formatter="alarmAffirmFormatter">
        </el-table-column>
        <el-table-column prop="workCount" label="关联工单" width="100px">
          <template #default="{ row }">
            <span v-if="row.workCount === 0">-</span>
            <span v-else-if="row.workCount === 1" :style="{ color: alarmWorkOrderFormatter(row).color }">
              <i :class="alarmWorkOrderFormatter(row).icon"></i>
              {{ alarmWorkOrderFormatter(row).label }}
            </span>
            <div v-else="row.workCount > 1" class="operationBtn" @click="onViewWorkOrder(row)">
              <span>{{ row.workCount }}</span>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="170px">
          <template #header>
            操作 <el-tooltip placement="right" content="点击查看更多"><span class="table-list__more-data"
                                                                    @click="showAllDataDialog">更多</span></el-tooltip>
          </template>
          <div slot-scope="scope" class="operationBtn">
            <!-- dispatch(派单) confirmAlarm(确警) remark(备注) shielded(屏蔽) close(关闭) viewDetails(查看详情) -->
            <template v-if="scope.row.alarmStatus != 2">
              <!-- 配置了可操作平台才能操作 -->
              <span :disabled="!canExecute(scope.row)" style="margin-right: 10px;"
                    @click="operating('execute', scope.row)">开始处理</span>
            </template>
            <template v-else>
              <span :disabled="scope.row.alarmAffirm == 2" style="margin-right: 10px;"
                    @click="operating('summary', scope.row)">总结</span>
            </template>
            <!-- 已经派单了就不能执行 -->
            <span v-if="!scope.row.workNum" style="margin-right: 10px"
                  @click="operating('dispatch', scope.row)">派单</span>
            <span @click="operating('detail', scope.row)">查看</span>
          </div>
        </el-table-column>
      </el-table>
      <el-pagination :current-page="currentPage" :page-sizes="[15, 20, 30, 40]" :page-size="pageSize"
                     layout="total, sizes, prev, pager, next, jumper" :total="total" class="pagination"
                     @size-change="handleSizeChange" @current-change="handleCurrentChange"></el-pagination>
    </div>

    <!-- 批量确警/演习 -->
    <DialogEmergencyConfirm :visible.sync="confirmDialog.show" v-bind="confirmDialog" @success="initComponentData" />
    <!-- 批量屏蔽 -->
    <DialogEmergencyShield :visible.sync="shieldDialog.show" is-batch v-bind="shieldDialog"
                           @success="initComponentData" />
    <!-- 全部数据弹窗 -->
    <DialogEmergencyFrame :visible.sync="dialogFrame.show" :targetTab="dialogFrame.tab"
                          :alarmId.sync="currentAlarmId" @update:visible="initComponentData" />
  </div>
</template>
<script>
import {
  GetAlarmTrendPc,
  GetAlarmSourceCount,
  getSourceByEmpty,
  getSpaceInfoList,
  getStructureTree,
  GetAllAlarmRecord,
  GetPoliceInfoByApp,
  AlarmAffirm,
  OneKeyDispatch,
  setAlarmRecordToClassics,
  queryFieldsConfigList
} from '@/utils/peaceLeftScreenApi'
import { monitorTypeList } from '@/assets/common/dict.js'
import * as echarts from 'echarts'
import moment from 'moment'
import { alarmAffirmConfig, alarmConfirmOptions, alarmLevelConfig, alarmStatusConfig, alarmWorkOrderConfig, batchControlList, batchControlType, operatePlatform, toDetailTab } from './emergency-constant'

moment.locale('zh-cn')
export default {
  components: {
    DialogEmergencyConfirm: () => import('./components/DialogEmergencyConfirm'),
    DialogEmergencyShield: () => import('./components/DialogEmergencyShield'),
    DialogEmergencyFrame: () => import('./components/DialogEmergencyFrame')
  },
  data() {
    return {
      currentAlarmId: '', // 当前详情展示的报警ID
      dialogFrameShow: false, // 全部数据弹窗
      collapsed: true, // 表单是否折叠
      areaName: '', // 选中 下拉树的name
      alarmSourceOptions: [], // 报警来源
      alarmTypeOption: [], // 事件类型
      serverSpaces: [], // 空间位置
      spaces: [], // 空间位置
      serverDefaultProps: {
        label: 'ssmName',
        isLeaf: 'leaf',
        children: 'children'
      },
      searchFrom: {
        searchKey: '', // 搜索关键字
        projectCode: [], // 报警来源
        alarmType: [], // 报警类型
        alarmLevel: '', // 报警等级
        alarmSpaceId: '', // 空间位置
        dataRange: [], // 时间范围
        alarmStatus: '', // 处理状态
        alarmAffirm: '' // 确警类型
      },
      alarmConfirmOptions, // 确警类型
      alarmLevelOptions: alarmLevelConfig, // 报警等级
      pickerOptions: {
        shortcuts: [
          {
            text: '最近一周',
            onClick(picker) {
              const end = new Date()
              const start = new Date()
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 6)
              picker.$emit('pick', [start, end])
            }
          },
          {
            text: '最近一个月',
            onClick(picker) {
              const end = new Date()
              const start = new Date()
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 30)
              picker.$emit('pick', [start, end])
            }
          }
        ]
      },
      // 警情等级
      alarmLevelItem: {
        0: { text: '通知', color: '#3562DB' },
        1: { text: '一般', color: '#3562DB' },
        2: { text: '紧急', color: '#FF9435' },
        3: { text: '重要', color: '#FA403C' }
      },
      // 报警处理状态表单项
      alarmStatusOptions: alarmStatusConfig,
      // 报警处理状态
      alarmStatusItem: {
        0: { text: '未处理', color: '#FA403C' },
        1: { text: '处理中', color: '#FD9434' },
        2: { text: '已关闭', color: '#999' }
      },
      alarmAffirmItem: { 0: '未确认', 1: '真实报警', 2: '误报', 3: '演练', 4: '调试' },
      timeOrType: '', // 排序 0.按紧急降序  1.按紧急升序  2.按时间降序  3.按时间升序
      tableData: [],
      currentPage: 1,
      pageSize: 15,
      total: 0,
      tableLoading: false,
      warnAnalysis: [],
      warnAnalysisShow: true,
      timer: null,
      screenSmall: false, // 是否是小屏
      screenBack: false, // 是否是返回
      pageType: 1, // 1：默认 2：返回
      batchControlList,
      remrDialog: false, // 备注弹窗
      alarmDetailShow: false, // 报警详情弹窗
      selectAlarmList: [], // 选中的报警列表
      selectAlarmItem: {}, // 选中报警项
      screenSelectItems: [], // 屏蔽列表
      alarmVideoShow: false,
      confirmDialog: {
        show: false, // 批量确警/演习弹窗
        tag: '',
        state: -1,
        ids: '',
        codes: ''
      },
      shieldDialog: {
        show: false, // 屏蔽弹窗
        ids: '',
        objectIds: '',
        types: ''
      },
      dialogFrame: {
        show: false,  // 全部数据
        tab: '' // 目标tab
      },
      actionLoadingStatus: false // 加载状态
    }
  },
  computed: {
    collapseText() {
      return this.collapsed ? '展开' : '收起'
    },
    collapseIcon() {
      return this.collapsed ? 'el-icon-arrow-down' : 'el-icon-arrow-up'
    }
  },
  watch: {
    alarmDetailShow(val) {
      if (!val) {
        this.initComponentData()
      }
    }
  },
  created() {
    // 初始化 根据模块过滤数据
    if (Object.hasOwn(this.$route.query, 'projectCode')) {
      this.searchFrom.projectCode = this.$route.query.projectCode.split(',') || []
      this.screenSmall = true
    } else {
      this.searchFrom.projectCode = []
      this.screenSmall = false
    }
    if (Object.hasOwn(this.$route.query, 'pageType')) {
      this.pageType = 2
      this.screenBack = true
      this.screenSmall = false
      try {
        window.chrome.webview.hostObjects.sync.bridge.SetIsShowShiBei(false)
      } catch (error) { }
    }
    if (Object.hasOwn(this.$route.query, 'dataType')) {
      const date = {
        0: [moment().format('YYYY-MM-DD'), moment().format('YYYY-MM-DD')],
        1: [moment().startOf('isoWeek').format('YYYY-MM-DD'), moment().endOf('isoWeek').format('YYYY-MM-DD')],
        2: [moment().startOf('month').format('YYYY-MM-DD'), moment().endOf('month').format('YYYY-MM-DD')],
        3: [moment().startOf('year').format('YYYY-MM-DD'), moment().endOf('year').format('YYYY-MM-DD')]
      }
      this.searchFrom.dataRange = date[this.$route.query.dataType]
    }
  },
  mounted() {
    this.getAlarmSource()
    this.getTreelist()
    this.search()
    this.startRegularSearch()
    this.$once('hook:beforeDestroy', () => {
      clearInterval(this.timer)
      this.timer = null
    })
  },
  methods: {
    alarmStatusFormatter(row) {
      return alarmStatusConfig.find(x => x.value == row.alarmStatus) || {}
    },
    alarmLevelFormatter(row) {
      return alarmLevelConfig.find(x => x.value == row.alarmLevel) || {}
    },
    alarmAffirmFormatter(row) {
      const item = alarmAffirmConfig.find(x => x.value == row.alarmAffirm) || {}
      return item.label || '-'
    },
    alarmWorkOrderFormatter(row) {
      const type = row.workType || '未派工'
      const item = alarmWorkOrderConfig.find(x => x.label == type) || {}
      return item
    },
    onViewWorkOrder(row) {
      this.currentAlarmId = row.alarmId
      this.dialogFrame.tab = toDetailTab.order
      this.dialogFrame.show = true
    },
    /** 案件可否进行操作 */
    canExecute(row) {
      return /0/.test(row.disposalTerminal ?? '0') && row.alarmStatus !== 2 && !row.alarmAffirm
    },
    startRegularSearch() {
      this.timer = setInterval(() => {
        setTimeout(() => {
          this.search()
        }, 0)
      }, 1000 * timerNum)
    },
    showAllDataDialog() {
      this.currentAlarmId = ''
      this.dialogFrame.tab = ''
      this.dialogFrame.show = true
      // 打开子页面，关闭定时刷新
      clearInterval(this.timer)
    },
    closeDialogFrame() {
      this.dialogFrameShow = false
      // 关闭后重新开始定时刷新
      this.startRegularSearch()
    },
    onSearchCollapseClick() {
      this.collapsed = !this.collapsed
    },
    search() {
      if (this.confirmDialog.show || this.shieldDialog.show || this.dialogFrame.show) return
      this.initComponentData()
      this.getPoliceInfoByApp()
      this.getAlarmTrendPc()
      this.getAlarmSourceCount()
    },
    // 初始化组件数据
    initComponentData(tab = false) {
      this.selectAlarmList = []
      this.selectAlarmItem = {}
      // 初始化报警详情
      if (this.alarmDetailShow && !tab) {
        this.$refs.alarmDetail.getAlarmDetails()
        return
      }
      this.getDataList()
    },
    // 一键派单
    oneKeyDispatch(row) {
      const userInfo = this.$store.state.loginInfo.user
      const param = {
        alarmId: row.alarmId,
        alarmLevel: row.alarmLevel,
        alarmSourceName: row.alarmSource,
        spaceId: row.alarmSpaceId,
        userName: userInfo.staffName,
        userId: userInfo.staffId,
        projectCode: row.projectCode,
        operationSource: operatePlatform.client
      }
      OneKeyDispatch(param).then((res) => {
        if (res.data.code === '200') {
          this.$message({
            message: '已派单',
            type: 'success'
          })
          this.initComponentData()
        } else {
          this.$message.error(res.data.msg || '派单失败')
        }
      })
    },
    // 批量处置按钮
    batchControlEvent(item, arr = this.selectAlarmList) {
      const ids = this.selectAlarmList.map(x => x.alarmId).join()
      const codes = this.selectAlarmList.map(x => x.projectCode).join()
      switch (item.state) {
        case batchControlType.confirm: // 确警
        case batchControlType.maneuver: // 演习
          this.confirmDialog.tag = item.label
          this.confirmDialog.show = true
          this.confirmDialog.state = item.state
          this.confirmDialog.ids = ids
          this.confirmDialog.codes = codes
          break
        case batchControlType.mistake: // 误报处理
          this.doConfirm(`是否将${arr.length}条报警记录确警为误报?`)
            .then(() => {
              this.actionLoadingStatus = true
              const prams = {
                alarmAffirm: batchControlType.mistake,
                alarmId: ids,
                projectCode: codes,
                remark: '误报',
                operationSource: operatePlatform.client
              }
              return AlarmAffirm(prams)
            })
            .then((res) => {
              if (res.data.code == 200) {
                this.$message.success('误报处理成功')
                this.search()
              } else {
                this.$message.error(res.data.msg || '误报处理失败')
              }
            })
            .catch(() => { })
            .finally(() => this.actionLoadingStatus = false)
          break
        case batchControlType.shield: // 屏蔽
          if (arr.some(x => x.alarmStatus === 1)) {
            this.$message.error('包含处理中的报警，不可屏蔽')
            return
          }
          this.shieldDialog.show = true
          this.shieldDialog.ids = ids
          this.shieldDialog.codes = codes
          this.shieldDialog.objectIds = this.selectAlarmList.map(x => x.alarmObjectId).join()
          this.shieldDialog.types = this.selectAlarmList.map(x => x.alarmFleldsConfigId).join()
          break
        default:
          break
      }
    },
    // 子/孙组件流程操作
    operating(type, selectItem) {
      this.selectAlarmItem = selectItem
      if (type === 'dispatch') {
        // dispatch(派单)
        // 派单
        this.doConfirm('确认派发确警工单?')
          .then(() => {
            this.oneKeyDispatch(selectItem)
          })
      } else if (type === 'execute') {
        if (!this.canExecute(selectItem)) {
          return
        }
        //  execute(警单处理)
        this.currentAlarmId = selectItem.alarmId
        this.dialogFrame.tab = toDetailTab.base
        this.dialogFrame.show = true
        this.dispatchEquipmentView(selectItem)
      } else if (type === 'summary') {
        // 误报不处理
        if (selectItem.alarmAffirm == 2) {
          return
        }
        // 总结
        this.currentAlarmId = selectItem.alarmId
        this.dialogFrame.tab = toDetailTab.summary
        this.dialogFrame.show = true
      } else if (type === 'detail') {
        // 查看详情
        this.currentAlarmId = selectItem.alarmId
        this.dialogFrame.tab = toDetailTab.base
        this.dialogFrame.show = true
      }
    },
    // table选择
    tableSelectChange(val) {
      this.selectAlarmList = val
    },
    // table点击
    onTabCellClick(row, column) {
      if (column.property == 'alarmObjectName') {
        this.dispatchEquipmentView(row)
      }
    },
    // 出发关联设备展示
    dispatchEquipmentView(row) {
      const sendData = {
        DeviceCode: row.modelCode,
        assetsId: row.alarmDeviceId,
        assetsName: row.alarmDeviceName,
        spaceCode: row.alarmSpaceStr,
        projectCode: row.projectCode
      }
      this.$tools.alarmDataRowRealView(sendData)
      return
      // 如果关联了设备即跳转设备详情页
      if (row.modelCode) {
        const projectData = monitorTypeList.find(e => e.projectCode === row.projectCode)
        if (!projectData) return
        const params = {
          DeviceCode: row.modelCode,
          menuName: projectData.wpfKey,
          projectCode: projectData.projectCode
        }
        try {
          window.chrome.webview.hostObjects.sync.bridge.EquipmentRealView(JSON.stringify(params))
        } catch (error) { }
      } else {
        this.$message.info('暂未找到报警位置')
      }
    },
    // 获取全部报警记录
    getDataList() {
      const { projectCode, alarmType, alarmLevel, alarmSpaceId, dataRange, searchKey } = this.searchFrom
      const params = {
        timeOrType: this.timeOrType,
        pageNo: this.currentPage,
        pageSize: this.pageSize,
        projectCode: projectCode.toString(),
        alarmType: alarmType.join(),
        alarmLevel,
        alarmSpaceId,
        startTime: dataRange[0],
        endTime: dataRange[1],
        alarmStatus: this.searchFrom.alarmStatus, // 处理状态
        alarmAffirm: this.searchFrom.alarmAffirm // 确警类型
      }
      // 关键字适应字段
      if (/^BJ/.test(searchKey)) {
        params.alarmId = searchKey
      } else {
        params.alarmObjectName = searchKey
      }
      this.tableLoading = true
      GetAllAlarmRecord(params)
        .then((res) => {
          this.tableLoading = false
          if (res.data.code === '200') {
            const data = res.data.data?.records ?? []
            this.tableData = data
            this.total = res.data.data?.total ?? 0
            if (data.length) {
              const currentTime = new Date()
              const givenTime = new Date(this.tableData[0].alarmStartTime)
              const timeDifferenceInSeconds = Math.floor((currentTime - givenTime) / 1000)
              // if (timeDifferenceInSeconds <= timerNum) {
              if (timeDifferenceInSeconds <= 20000) {
                console.log('在30秒内')
                try {
                  window.chrome.webview.hostObjects.sync.bridge.IsPlayAlarmSound(true)
                } catch (error) { }
                const timer = setInterval(() => {
                  try {
                    window.chrome.webview.hostObjects.sync.bridge.IsPlayAlarmSound(false)
                  } catch (error) { }
                  console.log('报警结束')
                  clearInterval(timer)
                }, 3000)
              }
            }
          } else {
            this.tableData = []
            this.total = 0
          }
        })
        .catch(() => {
          this.tableLoading = false
        })
    },
    // 查询
    searchForm() {
      this.currentPage = 1
      this.getDataList()
    },
    // 重置查询
    resetForm() {
      this.handleClear()
      Object.assign(this.searchFrom, {
        projectCode: [], // 报警来源
        incidentType: [], // 事件类型
        alarmLevel: '', // 报警等级
        dataRange: [], // 时间范围
        objectId: '', // 报警对象
        searchKey: '', // 搜索关键字
        alarmStatus: '', // 处理状态
        alarmAffirm: '' // 确警类型
      })
      this.currentPage = 1
      this.pageSize = 15
      this.total = 0
      this.getDataList()
    },
    // 获取服务空间树形结构
    getTreelist() {
      getStructureTree().then((res) => {
        if (res.data.code === 200) {
          this.spaces = res.data.data
          // 增加 懒加载节点
          res.data.data.map((e) => {
            e.leaf = false
          })
          // 转换为树形结构数据
          this.serverSpaces = this.$tools.transData(res.data.data, 'id', 'pid', 'children')
        }
      })
    },
    // 选择下拉树 数据
    handleNodeClick(data) {
      this.searchFrom.alarmSpaceId = data.id
      this.areaName = data.ssmName
      this.$refs.treeSelect.blur()
    },
    // 服务空间树结构点击获取自己及父节点ID并请求数据实现懒加载
    spaceTreeChange(value, resolve) {
      const child = this.$tools.ListTree(this.spaces, value.pid)
      child.push(value.id)
      const treeId = child.toString()
      const data = {
        current: 1,
        functionDictId: '',
        simCode: treeId,
        size: 999
      }
      getSpaceInfoList(data).then((res) => {
        if (res.data.code === 200) {
          if (typeof resolve === 'function') {
            var treeNodeData = JSON.parse(JSON.stringify(res.data.data.records))
            treeNodeData.map((e) => {
              e.leaf = true
            })
            resolve(treeNodeData)
          }
        }
      })
    },
    // 下拉树懒加载
    serverLoadNode(node, resolve) {
      console.log(node.level)
      if (node.level === 0) {
        return resolve(node.data)
      } else if (node.level > 0 && node.level < 4) {
        return resolve(node.data.children)
      } else {
        return this.spaceTreeChange(node.data, resolve)
      }
    },
    // 空间数据清除
    handleClear() {
      this.searchFrom.alarmSpaceId = ''
      this.areaName = ''
    },
    // 获取报警来源
    getAlarmSource() {
      getSourceByEmpty({}).then((res) => {
        if (res.data.code === '200') {
          this.alarmSourceOptions = res.data.data
        }
      })
    },
    // 获取事件类型以及报警来源分组
    getAlarmTypeOptions(val) {
      this.searchFrom.alarmType = []
      queryFieldsConfigList({ thirdSystemCode: val.toString() }).then((res) => {
        if (res.data.code === '200') {
          this.alarmTypeOption = res.data.data
        }
      })
    },
    // 获取 报警统计数据
    getPoliceInfoByApp() {
      const newArr = ['本日新增', '本周新增', '本月新增', '本年新增']
      GetPoliceInfoByApp().then((res) => {
        const data = res.data
        if (data.code === '200') {
          this.warnAnalysis = data.data.countInfo
          this.warnAnalysis.forEach((item) => {
            item.name = newArr[item.timeType]
          })
        } else {
          // this.$message({
          //   message: data.msg,
          //   type: 'warning'
          // })
        }
      })
    },
    handleSizeChange(val) {
      this.pageSize = val
      this.pageNo = 1
      this.getDataList()
    },
    handleCurrentChange(val) {
      this.currentPage = val
      this.getDataList()
    },
    // 获取 近30日报警走势图
    getAlarmTrendPc() {
      const params = {
        hourDayOrMouth: 1,
        startTime: moment().subtract(30, 'days').format('YYYY-MM-DD'),
        endTime: moment().format('YYYY-MM-DD')
      }
      GetAlarmTrendPc(params).then((res) => {
        const data = res.data
        if (data.code === '200') {
          this.getTrendEchart(data.data)
        } else {
          // this.$message({
          //   message: data.msg,
          //   type: 'warning'
          // })
        }
      })
    },
    getTrendEchart(list) {
      const getchart = echarts.init(document.getElementById('trendEchart'))
      const xArr = list.map((item) => {
        return item.time
      })
      const value = list.map((item) => {
        return item.list.reduce((a, b) => {
          return a + b.count
        }, 0)
      })
      var color = 'rgba(30, 243, 249'
      var lineY = []
      var data = {
        type: 'line',
        color: color + ')',
        smooth: true,
        areaStyle: {
          normal: {
            color: new echarts.graphic.LinearGradient(
              0,
              0,
              0,
              1,
              [
                {
                  offset: 0,
                  color: color + ', 0.3)'
                },
                {
                  offset: 1,
                  color: color + ', 0)'
                }
              ],
              false
            )
          }
        },
        symbolSize: 5,
        data: value
      }
      lineY.push(data)
      // }
      var option = {
        backgroundColor: 'rgba(128, 128, 128, 0)',
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            lineStyle: {
              color: 'rgb(30, 243, 249)'
            }
          },
          backgroundColor: '#0C2269',
          borderColor: '#5B617C',
          textStyle: {
            color: '#719DE7'
          },
          formatter: '{b0}</br>{c0}'
        },
        grid: {
          top: '14%',
          left: '4%',
          right: '4%',
          bottom: '12%'
        },
        xAxis: {
          type: 'category',
          boundaryGap: false,
          data: xArr,
          axisLabel: {
            textStyle: {
              color: '#50608B'
            },
            formatter: function (params) {
              return params.substring(5)
              // return params.substring(5).replace('-', '.')
            }
          },
          axisLine: {
            lineStyle: {
              color: '#50608B',
              width: 1
            }
          }
        },
        yAxis: {
          type: 'value',
          axisLabel: {
            formatter: '{value}',
            textStyle: {
              color: '#50608B'
            }
          },
          splitLine: {
            lineStyle: {
              color: '#2A4382'
            }
          }
        },
        series: lineY
      }
      getchart.setOption(option)
      // })
      // 随着屏幕大小调节图表
      window.addEventListener('resize', () => {
        getchart.resize()
      })
    },
    // 获取 报警来源分析数据
    getAlarmSourceCount() {
      GetAlarmSourceCount({ timeType: 3 }).then((res) => {
        const data = res.data
        if (data.code === '200') {
          if (data.data.length) {
            this.warnAnalysisShow = false
            this.$nextTick(() => {
              // 报警来源分析
              this.getAlarmSourceEchart(data.data)
            })
          } else {
            this.warnAnalysisShow = true
          }
        } else {
          // this.$message({
          //   message: data.msg,
          //   type: 'warning'
          // })
        }
      })
    },
    getAlarmSourceEchart(list) {
      const getchart = echarts.init(document.getElementById('alarmSourceEchart'))
      getchart.resize()
      var scaleData = list
      var xdata = list.map((item) => {
        return item.projectName
      })
      var data = []
      var color = ['rgba(31, 250, 255, 0.3)', 'rgba(255, 227, 166, 0.28)', 'rgba(147, 130, 255, 0.26)', 'rgba(0, 248, 114, 0.24)', 'rgba(46, 119, 251, 0.3)']
      var borderColor = ['rgba(31, 250, 255, 1)', 'rgba(255, 227, 166, 1)', 'rgba(147, 130, 255, 1)', 'rgba(0, 248, 114, 1)', 'rgba(46, 119, 251, 1)']
      for (var i = 0; i < scaleData.length; i++) {
        data.push({
          value: scaleData[i].count,
          name: scaleData[i].projectName,
          itemStyle: {
            normal: {
              borderWidth: 1,
              shadowBlur: 200,
              borderColor: borderColor[i],
              color: color[i],
              label: {
                normal: { show: true }
              }
            }
          }
        })
      }
      var seriesObj = [
        {
          name: '',
          type: 'pie',
          clockWise: false,
          center: ['25%', '50%'],
          radius: ['40%', '60%'],
          hoverAnimation: true,
          label: {
            normal: {
              show: false,
              position: 'center',
              formatter: '{text|{b}}\n{c} ({d}%)',
              rich: {
                value: {
                  padding: 5,
                  align: 'center',
                  verticalAlign: 'middle',
                  fontSize: 20
                },
                label: {
                  align: 'center',
                  verticalAlign: 'middle',
                  fontSize: 12
                }
              }
            },
            emphasis: {
              show: true,
              textStyle: {
                fontSize: '12',
                color: '#fff'
              }
            }
          },
          data: data
        }
      ]
      const option = {
        backgroundColor: 'rgba(128, 128, 128, 0)',
        legend: {
          orient: 'vartical',
          type: 'scroll',
          x: 'left',
          top: 'center',
          left: '50%',
          bottom: '0%',
          data: xdata,
          itemWidth: 8,
          itemHeight: 8,
          itemGap: 16,
          pageTextStyle: {
            color: '#fff'
          },
          textStyle: {
            //  fontSize: 18,//字体大小
            color: '#B3C2DD' //  字体颜色
          },
          formatter: function (name) {
            var oa = option.series[0].data
            var num = oa.reduce((sum, e) => sum + e.value, 0)
            for (var i = 0; i < option.series[0].data.length; i++) {
              if (name === oa[i].name) {
                return ' ' + name + ' (' + oa[i].value + ')     ' + ((oa[i].value / num) * 100).toFixed(2) + '%'
              }
            }
          }
        },
        series: seriesObj
      }
      getchart.clear()
      getchart.setOption(option)
      // 随着屏幕大小调节图表
      window.addEventListener('resize', () => {
        getchart.resize()
      })
    },
    gobackToDevice() {
      try {
        // 来源是 运行监控
        if (this.$route.query.sourcePath === '/operationMonitoring') {
          window.chrome.webview.hostObjects.sync.bridge.LeftMenuSwitch('IsCheckedYX')
        } else {
          window.chrome.webview.hostObjects.sync.bridge.SetIsShowShiBei(true)
        }
      } catch (error) { }
      this.$router.go(-1)
    },
    /**
     * 设置/取消 经典案例
     * @param {*} value
     * @param {*} row
     */
    onClassicChange(row) {
      const question = row.classic === 1 ? '确定取消经典案例？' : '设置为经典案例？'
      this.doConfirm(question)
        .then(() => {
          return setAlarmRecordToClassics({
            classic: row.classic === 1 ? 0 : 1,
            alarmId: row.alarmId,
            operationSource: operatePlatform.client
          })
        })
        .then((res) => {
          if (res.data.code == 200) {
            const message = row.classic === 1 ? '已取消' : '已设置'
            this.$message.success(message)
            this.search()
          } else {
            this.$message.error(res.data.msg || '操作失败')
          }
        })
        .catch((err) => { console.error(err) })
    },
    /**
     * 页面通用询问框
     * @param msg 询问内容
     */
    doConfirm(msg) {
      return this
        .$confirm(msg, '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          cancelButtonClass: 'new-edition',
          confirmButtonClass: 'new-edition',
          customClass: 'confirm-box-class',
          type: 'warning'
        })
    }
  }
}
</script>
<style lang="scss" scoped>
@import '../../../../assets/sino-ui/common/var.scss';

.screen_small {
  padding-left: 110px !important;
  padding-bottom: 80px !important;
}

.screen_back {
  padding-bottom: 40px !important;
}

html,
.content {
  position: relative;
  // background-color: #031553;
  background: center;
  // background: url('~@/assets/images/qhdsys/bj.png') no-repeat;

  box-sizing: border-box;
  width: 100%;
  height: 100%;
  padding: 1.25rem 0.625rem 0.625rem 0.625rem;

  .title {
    height: 30px;
    // position: relative;
    position: absolute;
    left: 16px;
    top: 10px;

    div {
      cursor: pointer;
      position: absolute;
      top: 5px;
      left: 0;
      width: 80px;
      height: 30px;
      line-height: 30px;
      text-align: center;
      color: #618ad3;
      font-size: 0.875rem;
      white-space: nowrap;
      background-image: url('~@/assets/images/peace/btn-back.png');
      background-size: 100% 100%;
      background-repeat: no-repeat;
    }
  }

  .screen_back {
    .title {
      position: initial;

      div {
        left: 10px;
        top: 10px;
      }
    }
  }

  .search-form {
    height: 6rem;
    width: 100%;

    &.collapsed {
      height: 2.5rem;
    }

    .el-form-item {
      margin-bottom: 12px;
    }

    &__collapse-btn {
      margin-left: 12px;
      color: #fff;
      cursor: pointer;
    }

    &>div {
      margin-right: 10px;
    }

    ::v-deep .el-input__inner {
      background: center;
      border: 1px solid $input-border-color;
      border-radius: 0;
      color: $color;
      height: inherit;
      font-family: PingFangSC-Medium, PingFang SC;
    }

    ::v-deep .el-range-input {
      background: center;
      color: $color;
    }

    ::v-deep .el-range-separator,
    ::v-deep .el-range__icon {
      color: #3769be;
    }
  }

  .eahart-list {
    width: 100%;
    height: 35%;
    margin-top: 0.7rem;
    display: flex;
    justify-content: space-between;

    .echarts-left {
      width: 20%;
      background: url('~@/assets/images/qhdsys/emergency-disposal-l.png') no-repeat;
      background-size: 100% 100%;
      padding: 4px;
      box-sizing: border-box;

      .bg-content {
        display: flex;
        flex-wrap: wrap;
        align-content: space-around;

        // padding-left: 35px;
        .right-item {
          width: 50%;
        }

        .right-title {
          font-size: 16px;
          font-weight: 500;
          color: #7eaef9;
          line-height: 19px;
        }

        .right-value {
          margin-top: 3px;
          display: flex;
          align-items: center;
        }

        .right-num {
          font-size: 26px;
          font-weight: bold;
          color: #ffffff;
          line-height: 30px;
          font-style: oblique;
        }

        .right-ratio {
          margin-left: 10px;
          padding-left: 7px;
          font-size: 14px;
          font-weight: 400;
          height: 17px;
          line-height: 17px;
          background: linear-gradient(90deg, rgba(106, 143, 211, 0.41) 0%, rgba(106, 143, 211, 0) 94%);

          img {
            margin-left: 2px;
            width: 6px;
            height: 9px;
          }
        }
      }
    }

    .echarts-center {
      width: 49%;
      background: url('~@/assets/images/qhdsys/emergency-disposal-center.png') no-repeat;
      background-size: 100% 100%;
      padding: 3px;
      box-sizing: border-box;
    }

    .echarts-right {
      width: 30%;
      background: url('~@/assets/images/qhdsys/emergency-disposa.png') no-repeat;
      background-size: 100% 100%;
      padding: 3px;
      box-sizing: border-box;
    }

    .bg-title {
      // margin-top: 5px;
      height: 2.5rem;
      line-height: 2.5rem;
      color: #d4e3f9;
      padding-left: 3rem;
      font-family: TRENDS;
    }

    .center-center {
      width: 100%;
      height: 100%;
      display: flex;
      align-items: center;
      justify-content: center;
      color: #4d5880;
      font-size: 16px;
    }

    .bg-content {
      position: relative;
      box-sizing: border-box;
      padding: 10px 10px 15px 10px;
      width: 100%;
      height: calc(100% - 2.5rem);
      display: flex;

      #trendEchart,
      #alarmSourceEchart {
        width: 100%;
        height: 100%;
        position: relative;
        z-index: 2;
      }

      .alarm-bg {
        width: 15rem;
        height: 15rem;
        position: absolute;
        top: calc(50% - 7.6rem);
        left: 4.5%;
        background: url('~@/assets/images/peace/left-operation-warn-bg.png') no-repeat;
        background-size: 100% 100%;
      }

      .case-anim-icon {
        position: absolute;
        top: calc(50% - 1rem);
        left: calc(25% - 0.5rem);
        width: 2rem;
        height: 2rem;
        background: url('~@/assets/images/peace/icon-warn.png') no-repeat;
        background-size: 100% 100%;
      }
    }
  }

  .batch-control {
    height: 2.5rem;
    margin-top: 1rem;

    .is-disabled {
      background-image: url('~@/assets/images/qhdsys/btn-bg.png') !important;
    }
  }

  .table-list {
    width: 100%;
    height: calc(65% - 11.5rem);
    margin-top: 0.8rem;

    &.formCollapsed {
      height: calc(65% - 7.5rem);
    }

    &__more-data {
      margin-left: 12px;
      cursor: pointer;
    }

    ::v-deep .el-table th.el-table__cell>.cell {
      white-space: nowrap;
    }
  }
}

::v-deep .el-table--striped .el-table__body tr.el-table__row--striped td {
  background-color: rgba(168, 172, 171, 0.08) !important;
  /* def2ff f2faff */
}

::v-deep .el-switch .el-switch__core::after {
  width: 18px;
  height: 18px;
  top: 0px;
  background-color: #1E3370 !important;
}

::v-deep .el-switch.is-checked .el-switch__core::after {
  left: calc(100% - 1px) !important;
}

::v-deep .is-disabled {
  background-color: transparent !important;
}
</style>
<style lang="scss">
.emergencyDisposalNew {
  .search-form {
    .search-form-tree {
      color: #fff !important;
      background: transparent !important;

      .el-tree-node__content:hover {
        background: transparent !important;

        .el-tree-node__label {
          color: #ffe3a6;
        }
      }
    }
  }

  .table-list {
    .table-list__selection {
      .el-checkbox.is-disabled {
        display: none;
      }
    }
  }
}
</style>
