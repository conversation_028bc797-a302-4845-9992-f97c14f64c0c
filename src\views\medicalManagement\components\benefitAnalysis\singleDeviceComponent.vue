<template>
  <div class="box-view">
    <div class="box-left">
      <div class="box-left-top">
        <div class="recycle">
          <div id="recycleEcharts" style="width: 100%; height: 100%"></div>
        </div>
        <div class="grossIncome">
          <div class="grossIncome-title">
            总收入
          </div>
          <div class="grossIncome-box">
            <div class="grossIncome-content" v-for="(item,index) in statisticalData" :key="index">
              <div class="grossIncome-content-left">
                <p>
                  <span class="item-block" :style="{ background: item.background }"></span><span
                    class="item-label">{{item.name}}</span>
                </p>
                <p>
                  <span class="item-value">{{item.value}}</span> <span class="item-unit">{{item.unit}}</span>
                </p>
              </div>
              <div class="grossIncome-content-right">
                <p class="item-label">同比去年</p>
                <p>
                  <img :src="item.img" alt="">
                  <span class="item-rate" :style="{ color: item.color }">12.3%</span>
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="box-left-center">
        <div id="leftCenterEcharts" style="width: 100%; height: 100%"></div>
      </div>
      <div class="box-left-bottom">
        <div id="leftBottomEcharts" style="width: 100%; height: 100%"></div>
      </div>
    </div>
    <div class="box-center">
      <div class="box-center-top">
        <div id="centerTopEcharts" style="width: 100%; height: 100%"></div>
      </div>
      <div class="box-center-bottom">
        <div id="centerBottomEcharts" style="width: 100%; height: 100%"></div>
      </div>
    </div>
    <div class="box-right">
      <div class="box-right-top">
        <div id="rightTopEcharts" style="width: 100%; height: 100%"></div>
      </div>
      <div class="box-right-center">
        <div id="rightCenterEcharts" style="width: 100%; height: 100%"></div>
      </div>
      <div class="box-right-bottom">
        <div class="useCondition">
          <div id="pie_useCondition" style="width: 100%; height: 100%"></div>
        </div>
        <div class="enableYear">
          <div id="pie_enableYear" style="width: 100%; height: 100%"></div>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import * as echarts from 'echarts'
import "echarts-liquidfill/src/liquidFill.js"; // 在需要水滴图的页面js引入
import downImg from '@/assets/images/qhdsys/down.png'
import upImg from '@/assets/images/qhdsys/up.png'
import flatImg from '@/assets/images/flat.png'

export default {
  name: 'singleDeviceComponent',
  data() {
    return {
      pieIdData: [
        "pie_useCondition",
        "pie_enableYear",
      ],
      statisticalData: [
        {
          name: '总收入',
          value: '123,330.120',
          unit: '元',
          background: '#8BDDF5',
          rate: '12.3%',
          color: '#FF2D55',
          img: upImg
        },
        {
          name: '总支出',
          value: '330.120',
          unit: '元',
          background: '#3CC1FF',
          rate: '12.3%',
          color: '#61E29D',
          img: downImg
        },
        {
          name: '总利润',
          value: '123,000.000',
          unit: '元',
          background: '#0A84FF',
          rate: '12.3%',
          color: '#D6EFF1',
          img: flatImg
        }
      ]
    };
  },
  // 监听全屏缩放 动态改变echarts图表大小
  watch: {
    '$store.state.dialogFullScreenState': {
      handler(val) {
        this.$nextTick(() => {
          const echartsDom = ['recycleEcharts', 'leftCenterEcharts', 'leftBottomEcharts', 'centerTopEcharts', 'centerBottomEcharts', 'rightTopEcharts', 'rightCenterEcharts', 'pie_useCondition', 'pie_enableYear']
          setTimeout(() => {
            echartsDom.forEach((item) => {
              if (document.getElementById(item)) {
                echarts.init(document.getElementById(item)).resize()
              }
            })
          }, 200)
        })
      },
      deep: true
    }
  },
  mounted() {
    this.init()
  },
  methods: {
    //初始化
    init() {
      this.getLeftTop()
      this.getLeftCenter()
      this.getLeftBottom()
      this.getCenterTop()
      this.getCenterBottom()
      this.getRightTop()
      this.getRightCenter()
      this.getRightBottom()
    },
    //投资回收情况、总收入
    getLeftTop() {
      this.getRecycleData()
    },
    //投资回收情况
    getRecycleData() {
      const getchart = echarts.init(document.getElementById('recycleEcharts'))
      let value = 0.52
      const option = {
        title: [
          {
            text: '投资回收情况',
            top: '2%',
            left: '2%',
            textStyle: {
              fontSize: "16px",
              color: '#ffffff',
              fontWeight: "500"
            },
          },
          {
            text: '当前利润',
            left: '48%',
            top: "57%",
            textAlign: 'center',
            textStyle: {
              fontSize: '12',
              fontWeight: '500',
              color: '#06102C',
              textAlign: 'center',
            },
          },
          {
            text: '1,233,372',
            left: '48%',
            top: "69%",
            textAlign: 'center',
            textStyle: {
              fontSize: '18',
              fontWeight: '500',
              color: '#06102C',
              textAlign: 'center',
            },
          },
          {
            text: '投资回收期：5年',
            left: '48%',
            top: "27%",
            textAlign: 'center',
            textStyle: {
              fontSize: '14',
              fontWeight: '400',
              color: '#06102C',
              textAlign: 'center',
            },
          },
          {
            text: '预计还有2年达到盈亏平衡',
            left: '48%',
            top: "40%",
            textAlign: 'center',
            textStyle: {
              fontSize: '12',
              fontWeight: '300',
              color: '#FFFFFF',
              textAlign: 'center',
            },
          },
        ],
        series: [{
          type: 'liquidFill',
          radius: '85%',
          center: ['50%', '56%'],
          color: ['#FFCA64'],
          data: [value], // data个数代表波浪数
          backgroundStyle: {
            borderWidth: 1,
            color: '#8591CE'
          },
          label: {
            normal: {
              formatter: '',
            }
          },
          outline: {
            show: false,
          },
        }]
      }
      getchart.clear()
      getchart.setOption(option)
      // 随着屏幕大小调节图表
      window.addEventListener('resize', () => {
        getchart.resize()
      })
    },
    //近5年费率分布/%
    getLeftCenter() {
      const getchart = echarts.init(document.getElementById('leftCenterEcharts'))
      let xData = ['2020', '2021', '2022', '2023', '2024']
      let yData = [30, 22, 45, 29, 11]
      let y1Data = [26, 12, 53, 34, 43]
      let y2Data = [34, 32, 12, 45, 34]
      const option = {
        title: {
          text: '近5年费率分布/%',
          top: '2%',
          left: '2%',
          textStyle: {
            fontSize: "16px",
            color: '#ffffff',
            fontWeight: "500"
          },
        },
        tooltip: {
          trigger: 'axis',
          backgroundColor: '#0D2169',
          borderColor: '#727382',
          textStyle: {
            color: '#fff'
          },
          axisPointer: {
            type: "shadow",
          },
        },
        legend: {
          top: '15%',
          left: "center",
          itemHeight: 8,
          itemWidth: 8,
          textStyle: {
            "color": "#fff"
          }
        },
        grid: {
          left: '4%',
          right: '8%',
          bottom: '10%',
          top: '32%',
          containLabel: true
        },
        xAxis: {
          data: xData,
          axisLabel: {
            show: true,
            color: "#ffffff",
            interval: 0,
            textStyle: {
              color: 'rgba(255,255,255,0.6)',
              fontSize: 10,
              fontWeight: 500,
            }
          },
          axisLine: {
            lineStyle: {
              color: "rgba(255,255,255,0.2)",
            },
          }
        },
        yAxis: [{
          name: '',
          type: 'value',
          lineStyle: {
            show: "none",
          },
          axisTick: {
            show: false,
          },
          axisLabel: {
            show: true,
            color: "rgba(255,255,255,0.6)",
          },
          splitLine: {
            show: true,
            lineStyle: {
              color: "rgba(255,255,255,0.2)",
              type: 'dashed'
            },
          }
        }, {
          type: 'value',
          lineStyle: {
            show: "none",
          },
          axisTick: {
            show: false,
          },
          axisLine: {
            show: false,
          },
          axisLabel: {
            show: false,
          },
          splitLine: {
            show: false,
          }
        }],
        series: [
          {
            name: "利润率",
            type: "bar",
            barWidth: 10,
            itemStyle: {
              normal: {
                color: "#8BDDF5",
              }
            },
            data: yData
          },
          {
            name: "投资收益率",
            type: "bar",
            barWidth: 10,
            itemStyle: {
              normal: {
                color: "#3CC1FF",
              }
            },
            data: y1Data
          },
          {
            name: "维系费用率",
            type: "bar",
            barWidth: 10,
            itemStyle: {
              normal: {
                color: "#0A84FF",
              }
            },
            data: y2Data
          },
        ]
      }
      getchart.clear()
      getchart.setOption(option)
      // 随着屏幕大小调节图表
      window.addEventListener('resize', () => {
        getchart.resize()
      })
    },
    //支出明细/元
    getLeftBottom() {
      const getchart = echarts.init(document.getElementById('leftBottomEcharts'))
      var borderColor = ['#FFCA64', '#8BDDF5', '#61E29D']
      let dateArr = ['2023-10', '2023-11', '2023-12', '2024-1', '2024-2', '2024-3']
      let echartsData = [
        {
          name: '药品',
          value: [1000.3000, 3300, 6000, 5500, 1600, 3677, 4677, 2666, 3333, 5666]
        }, {
          name: '耗材',
          value: [4000.3060, 2300, 4000, 4500, 5600, 3677, 2677, 1666, 3833, 4666]
        }, {
          name: '人员经费',
          value: [1900.3900, 4300, 5000, 1500, 4600, 3677, 5677, 5666, 4533, 2666]
        }
      ]
      const seriesObj = []
      echartsData.forEach((item, index) => {
        const randomRgbColor = this.$tools.randomRgbColor('array')
        seriesObj.push({
          name: item.name,
          type: 'line',
          smooth: false,
          showAllSymbol: true,
          symbol: 'circle',
          symbolSize: 0,
          itemStyle: {
            color: borderColor[index],
          },
          lineStyle: {
            width: 2,
            color: borderColor[index]
          },
          data: item.value
        })
      })
      const option = {
        title: {
          text: '支出明细/元',
          top: '2%',
          left: '2%',
          textStyle: {
            fontSize: "16px",
            color: '#ffffff',
            fontWeight: "500"
          },
        },
        tooltip: {
          trigger: 'axis',
          backgroundColor: '#0D2169',
          borderColor: '#727382',
          textStyle: {
            color: '#fff'
          }
        },
        legend: {
          show: true,
          data: Array.from(echartsData, ({ name }) => name),
          type: 'scroll',
          orient: 'horizontal',
          left: 'center',
          top: '14%',
          itemGap: 16,
          textStyle: {
            color: '#ffffff'
          }
        },
        grid: {
          left: '5%',
          right: '8%',
          bottom: '18%',
          top: '30%',
          containLabel: true
        },
        xAxis: {
          type: 'category',
          data: dateArr,
          axisTick: {
            show: false,
          },
          splitLine: {
            show: true,
            lineStyle: {
              color: "rgba(255,255,255,0.2)",
              type: 'dashed'
            },
          },
          axisLabel: {
            show: true,
            color: "#ffffff",
            interval: 0,
            textStyle: {
              color: 'rgba(255,255,255,0.5)',
              fontSize: 10,
              fontWeight: 500,
            }
          },
          axisLine: {
            lineStyle: {
              color: "rgba(255,255,255,0.2)",
            },
          }
        },
        yAxis: [{
          name: '',
          type: 'value',
          lineStyle: {
            show: "none",
          },
          axisTick: {
            show: false,
          },
          axisLabel: {
            show: true,
            color: "rgba(255,255,255,0.6)",
          },
          splitLine: {
            show: true,
            lineStyle: {
              color: "rgba(255,255,255,0.2)",
              type: 'dashed'
            },
          }
        }, {
          type: 'value',
          lineStyle: {
            show: "none",
          },
          axisTick: {
            show: false,
          },
          axisLine: {
            show: false,
          },
          axisLabel: {
            show: false,
          },
          splitLine: {
            show: false,
          }
        }],
        series: seriesObj
      }
      getchart.clear()
      getchart.setOption(option)
      // 随着屏幕大小调节图表
      window.addEventListener('resize', () => {
        getchart.resize()
      })
    },
    //收支趋势/元
    getCenterTop() {
      const getchart = echarts.init(document.getElementById('centerTopEcharts'))
      var borderColor = ['#FFCA64', '#8BDDF5', '#61E29D']
      let dateArr = ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月']
      let echartsData = [
        {
          name: '收入',
          value: [1400.5000, 4500, 2300, 3600, 7800, 4500, 5050, 6500, 4300, 4500, 7800, 7900]
        }, {
          name: '支出',
          value: [5400.2000, 3500, 4300, 5600, 5800, 7500, 6500, 5500, 6300, 3500, 4800, 6900]
        }, {
          name: '利润',
          value: [1400.3000, 2500, 5300, 7600, 4800, 5500, 8500, 2500, 5300, 6500, 4800, 5900]
        }
      ]
      const seriesObj = []
      echartsData.forEach((item, index) => {
        const randomRgbColor = this.$tools.randomRgbColor('array')
        seriesObj.push({
          name: item.name,
          type: 'line',
          smooth: false,
          showAllSymbol: true,
          symbol: 'circle',
          symbolSize: 0,
          itemStyle: {
            color: borderColor[index],
          },
          lineStyle: {
            width: 2,
            color: borderColor[index] ?? randomRgbColor[1]
          },
          data: item.value
        })
      })
      const dataZoom = [
        // {
        //   type: 'slider', // 滑动条型数据区域缩放组件
        //   realtime: false, // 拖动时，是否实时更新系列的视图。如果设置为 false，则只在拖拽结束的时候更新。
        //   // start: 0, //数据窗口范围的起始百分比。范围是：0 ~ 100。表示 0% ~ 100%。
        //   // end: 10,  // 数据窗口范围的结束百分比。范围是：0 ~ 100
        //   height: 4,
        //   endValue: 5, // 数据窗口范围的结束数值。如果设置了 dataZoom-inside.end 则 endValue 失效
        //   fillerColor: 'rgba(17, 100, 210, 0.4)', // 滚动条颜色
        //   borderColor: 'rgba(17, 100, 210, 0.1)',
        //   handleSize: 0, // 两边手柄尺寸
        //   showDetail: false, // 拖拽时是否展示滚动条两侧的文字
        //   bottom: '14%', // 组件离容器上侧的距离
        //   zoomLock: true // 是否只平移不缩放
        //   // moveOnMouseMove:true, //开启鼠标移动窗口平移
        //   // zoomOnMouseWheel :true, //开启鼠标滚轮缩放
        // },
        // {
        //   type: 'inside', // 内置型数据区域缩放组件
        //   // start: 0,
        //   // end: 10,
        //   endValue: 11, // 最多12个
        //   zoomOnMouseWheel: false, // 关闭鼠标滚轮缩放
        //   moveOnMouseWheel: true, // 开启鼠标滚轮窗口平移
        //   moveOnMouseMove: true // 开启鼠标移动窗口平移
        // }
      ]
      const option = {
        title: {
          text: '收支趋势/元',
          top: '2%',
          left: '2%',
          textStyle: {
            fontSize: "16px",
            color: '#ffffff',
            fontWeight: "500"
          },
        },
        tooltip: {
          trigger: 'axis',
          backgroundColor: '#0D2169',
          borderColor: '#727382',
          textStyle: {
            color: '#fff'
          },
          axisPointer: {
            // 坐标轴指示器，坐标轴触发有效
            // type: 'shadow' // 默认为直线，可选为：'line' | 'shadow'
          }
        },
        dataZoom: dataZoom,
        legend: {
          show: true,
          data: Array.from(echartsData, ({ name }) => name),
          type: 'scroll',
          orient: 'horizontal',
          left: 'center',
          top: '12%',
          itemGap: 16,
          textStyle: {
            color: '#ffffff'
          }
        },
        grid: {
          left: '6%',
          right: '8%',
          bottom: '8%',
          top: '25%',
          containLabel: true
        },
        xAxis: {
          type: 'category',
          data: dateArr,
          axisTick: {
            show: false,
          },
          splitLine: {
            show: true,
            lineStyle: {
              color: "rgba(255,255,255,0.2)",
              type: 'dashed'
            },
          },
          axisLabel: {
            show: true,
            color: "#ffffff",
            interval: 0,
            textStyle: {
              color: 'rgba(255,255,255,0.5)',
              fontSize: 10,
              fontWeight: 500,
            }
          },
          axisLine: {
            lineStyle: {
              color: "rgba(255,255,255,0.2)",
            },
          }
        },
        yAxis: [{
          name: '',
          type: 'value',
          lineStyle: {
            show: "none",
          },
          axisTick: {
            show: false,
          },
          axisLabel: {
            show: true,
            color: "rgba(255,255,255,0.6)",
          },
          splitLine: {
            show: true,
            lineStyle: {
              color: "rgba(255,255,255,0.2)",
              type: 'dashed'
            },
          }
        }, {
          type: 'value',
          lineStyle: {
            show: "none",
          },
          axisTick: {
            show: false,
          },
          axisLine: {
            show: false,
          },
          axisLabel: {
            show: false,
          },
          splitLine: {
            show: false,
          }
        }],
        series: seriesObj
      }
      getchart.clear()
      getchart.setOption(option)
      // 随着屏幕大小调节图表
      window.addEventListener('resize', () => {
        getchart.resize()
      })
    },
    // 检查人数趋势图
    getCenterBottom() {
      const getchart = echarts.init(document.getElementById('centerBottomEcharts'))
      let xData = ['2023-11月', '2023-12月', '2024-1月', '2024-2月', '2024-4月', '2024-5月', '2024-6月', '2024-7月', '2024-8月']
      let yData = [800, 567, 140, 355, 345, 234, 343, 123, 111]
      let y1Data = [101, 56, 45, 67, 54, 211, 123, 133, 109]
      let y2Data = [60, 50, 55, 43, 56, 23, 25, 32, 41]
      const option = {
        title: {
          text: '检查人数趋势图',
          top: '2%',
          left: '2%',
          textStyle: {
            fontSize: "16px",
            color: '#ffffff',
            fontWeight: "500"
          },
        },
        legend: {
          top: '15%',
          left: "center",
          itemHeight: 8,
          itemWidth: 8,
          textStyle: {
            "color": "#fff"
          }
        },
        tooltip: {
          trigger: 'axis',
          backgroundColor: '#0D2169',
          borderColor: '#727382',
          textStyle: {
            color: '#fff'
          },
          axisPointer: {
            type: "shadow",
          },
        },
        grid: {
          left: '4%',
          right: '3%',
          bottom: '10%',
          top: '32%',
          containLabel: true
        },
        dataZoom: [
          {
            show: true,
            height: 6,
            bottom: "2%",
            right: "3%",
            showDetail: false,
            brushSelect: false,
            backgroundColor: "transparent", // 背景颜色
            showDataShadow: false,
            borderColor: "transparent",
            fillerColor: "#8591CE", //拉动框的颜色
            startValue: 0,
            endValue: 3,
            brushSelect: false,
            handleSize: "60%",
            zoomLock: true,
            // 画一个圆形
            // handleIcon:
            //   "path://M30.9,53.2C16.8,53.2,5.3,41.7,5.3,27.6S16.8,2,30.9,2C45,2,56.4,13.5,56.4,27.6S45,53.2,30.9,53.2z M30.9,3.5M36.9,35.8h-1.3z M27.8,35.8 h-1.3H27L27.8,35.8L27.8,35.8z",
            handleStyle: {
              // 两侧缩放手柄的样式配置。
              borderColor: "#transparent",
              borderWidth: "5",
              shadowBlur: 1,
              background: "#transparent",
              shadowColor: "#transparent",
            },
          },
        ],
        xAxis: {
          data: xData,
          axisLine: {
            show: true, //隐藏X轴轴线
            lineStyle: {
              color: 'rgba(230,247,255,0.3)'
            }
          },
          axisTick: {
            show: true //隐藏X轴刻度
          },
          splitLine: {
            //坐标轴在 grid 区域中的分隔线。
            show: true,
            lineStyle: {
              color: 'rgba(230,247,255,0.2)',
              type: 'dashed'
            },
          },
        },
        yAxis: [
          {
            name: '次',
            nameTextStyle: {
              color: 'rgba(230,247,255,0.6)',
            },
            type: 'value',
            splitNumber: 3,
            axisLabel: {
              textStyle: {
                color: 'rgba(230,247,255,0.6)',
                fontStyle: 'normal',
                fontSize: 12,
              },
            },
            axisLine: {
              show: false,
            },
            axisTick: {
              show: false,
            },
            splitLine: {
              show: true,
              lineStyle: {
                color: 'rgba(230,247,255,0.1)',
                type: 'dashed'
              }
            },
          },
          {
            name: '阳性率',
            nameTextStyle: {
              color: 'rgba(230,247,255,0.6)'
            },
            min: 0,
            splitLine: {
              show: true,
              lineStyle: {
                color: 'rgba(230,247,255,0.1)',
                type: 'dashed'
              }
            },
            axisLine: {
              show: true,
              lineStyle: {
                color: '#233653'
              }
            },
            axisLabel: {
              show: true,
              textStyle: {
                color: ' rgba(230,247,255,0.6)'
              },
              formatter: function (value) {
                return value + '%'
              }
            },
            axisTick: {
              show: false
            }
          }
        ],
        series: [
          {
            name: "检查人次",
            type: "bar",
            barWidth: 10,
            itemStyle: {
              normal: {
                color: "#61E29D",
              }
            },
            data: yData
          },
          {
            name: "检查阳性人次",
            type: "bar",
            barWidth: 10,
            itemStyle: {
              normal: {
                color: "#FFCA64",
              }
            },
            data: y1Data
          },
          {
            name: "阳性率",
            type: "line",
            itemStyle: {
              normal: {
                color: "#FF2D55",
              }
            },
            data: y2Data,
            yAxisIndex: 1
          }
        ]
      }
      getchart.clear()
      getchart.setOption(option)
      // 随着屏幕大小调节图表
      window.addEventListener('resize', () => {
        getchart.resize()
      })
    },
    // 收支明细/元
    getRightTop() {
      const getchart = echarts.init(document.getElementById('rightTopEcharts'))
      let xData = ['2023-11月', '2023-12月', '2024-1月', '2024-2月', '2024-4月', '2024-5月', '2024-6月', '2024-7月', '2024-8月']
      let yData = [1000, 2000, 4400, 2344, 5788, 4566, 3455, 5432, 5677]
      let y1Data = [2000, 1000, 2400, 1344, 2788, 1566, 1455, 2432, 1677]
      let y2Data = [3000, 4000, 1400, 4344, 4788, 2566, 5455, 4432, 4677]
      let y3Data = [1000, 5000, 4400, 5344, 1788, 3566, 2455, 1432, 3677]
      const option = {
        title: {
          text: '收支明细/元',
          top: '2%',
          left: '2%',
          textStyle: {
            fontSize: "16px",
            color: '#ffffff',
            fontWeight: "500"
          },
        },
        legend: {
          top: '15%',
          left: "center",
          itemHeight: 8,
          itemWidth: 8,
          textStyle: {
            "color": "#fff"
          }
        },
        tooltip: {
          trigger: 'axis',
          backgroundColor: '#0D2169',
          borderColor: '#727382',
          textStyle: {
            color: '#fff'
          },
          axisPointer: {
            type: "shadow",
          },
        },
        grid: {
          left: '4%',
          right: '8%',
          bottom: '10%',
          top: '32%',
          containLabel: true
        },
        dataZoom: [
          {
            show: true,
            height: 6,
            bottom: "2%",
            right: "3%",
            showDetail: false,
            brushSelect: false,
            backgroundColor: "transparent", // 背景颜色
            showDataShadow: false,
            borderColor: "transparent",
            fillerColor: "#8591CE", //拉动框的颜色
            startValue: 0,
            endValue: 3,
            brushSelect: false,
            handleSize: "60%",
            zoomLock: true,
            // 画一个圆形
            // handleIcon:
            //   "path://M30.9,53.2C16.8,53.2,5.3,41.7,5.3,27.6S16.8,2,30.9,2C45,2,56.4,13.5,56.4,27.6S45,53.2,30.9,53.2z M30.9,3.5M36.9,35.8h-1.3z M27.8,35.8 h-1.3H27L27.8,35.8L27.8,35.8z",
            handleStyle: {
              // 两侧缩放手柄的样式配置。
              borderColor: "#transparent",
              borderWidth: "5",
              shadowBlur: 1,
              background: "#transparent",
              shadowColor: "#transparent",
            },
          },
        ],
        xAxis: {
          data: xData,
          axisLine: {
            show: true, //隐藏X轴轴线
            lineStyle: {
              color: 'rgba(230,247,255,0.6)'
            }
          },
          axisTick: {
            show: true //隐藏X轴刻度
          },
          axisLabel: {
            show: true,
            textStyle: {
              color: "rgba(230,247,255,0.6)" //X轴文字颜色
            }
          },

        },
        yAxis: [{
          type: "value",
          nameTextStyle: {
            color: "rgba(230,247,255,0.6)"
          },
          splitLine: {
            show: false
          },
          axisTick: {
            show: true
          },
          axisLine: {
            show: true,
            lineStyle: {
              color: 'rgba(230,247,255,0.6)'
            }
          },
          axisLabel: {
            show: true,
            textStyle: {
              color: "rgba(230,247,255,0.6)"
            }
          },
        },
        {
          type: "value",
          gridIndex: 0,
          min: 50,
          max: 100,
          splitNumber: 8,
          splitLine: {
            show: false
          },
          axisLine: {
            show: false
          },
          axisTick: {
            show: false
          },
          axisLabel: {
            show: false
          },
          splitArea: {
            show: true,
            areaStyle: {
              color: ["rgba(250,250,250,0.0)", "rgba(250,250,250,0.05)"]
            }
          }
        }
        ],
        series: [
          {
            name: "检查收入",
            type: "bar",
            barWidth: 10,
            itemStyle: {
              normal: {
                color: "#8BDDF5",
              }
            },
            data: yData
          },
          {
            name: "药品收入",
            type: "bar",
            barWidth: 10,
            itemStyle: {
              normal: {
                color: "#FFCA64",
              }
            },
            data: y1Data
          },
          {
            name: "耗材收入",
            type: "bar",
            barWidth: 10,
            itemStyle: {
              normal: {
                color: "#61E29D",
              }
            },
            data: y2Data
          },
          {
            name: "治疗收入",
            type: "bar",
            barWidth: 10,
            itemStyle: {
              normal: {
                color: "#FF2D55",
              }
            },
            data: y3Data
          }
        ]
      }
      getchart.clear()
      getchart.setOption(option)
      // 随着屏幕大小调节图表
      window.addEventListener('resize', () => {
        getchart.resize()
      })
    },
    //日最高检查量/次
    getRightCenter() {
      const getchart = echarts.init(document.getElementById('rightCenterEcharts'))
      let hours = ['2024-04-08', '2024-04-16', '2024-05-06', '2024-05-028', '2024-06-14'];
      let days = [')0-9:00', ')-10:00', ')-11:30', ')-14:00', ')-15:00', ')-16:30'];
      let data = [
        [0, 0, 10],
        [0, 1, 19],
        [0, 2, 8],
        [0, 3, 24],
        [0, 4, 67],
        [0, 5, 92],

        [1, 0, 58],
        [1, 1, 78],
        [1, 2, 117],
        [1, 3, 48],
        [1, 4, 35],
        [1, 5, 15],

        [2, 0, 123],
        [2, 1, 64],
        [2, 2, 52],
        [2, 3, 72],
        [2, 4, 132],
        [2, 5, 114],

        [3, 0, 58],
        [3, 1, 78],
        [3, 2, 117],
        [3, 3, 48],
        [3, 4, 35],
        [3, 5, 15],

        [4, 0, 92],
        [4, 1, 58],
        [4, 2, 78],
        [4, 3, 117],
        [4, 4, 48],
        [4, 5, 35],
      ];
      const option = {
        title: {
          text: '日最高检查量/次',
          top: '2%',
          left: '2%',
          textStyle: {
            fontSize: "16px",
            color: '#ffffff',
            fontWeight: "500"
          },
        },
        tooltip: {
          position: 'top',
        },
        animation: false,
        grid: {
          left: '4%',
          right: '8%',
          bottom: '3%',
          top: '36%',
          containLabel: true
        },
        xAxis: {
          type: 'category',
          data: hours,
          splitArea: {
            show: true
          },
          axisLabel: {
            color: 'rgba(230,247,255,0.5)',
            fontSize: 12
          },
          axisTick: {
            lineStyle: {
              color: 'rgba(230,247,255,0.5)'
            }
          }
        },
        yAxis: {
          type: 'category',
          data: days,
          splitArea: {
            show: true
          },
          axisLabel: {
            color: 'rgba(230,247,255,0.5)',
            fontSize: 12
          },
          axisTick: {
            show: false
          }
        },
        visualMap: {
          min: 0,
          max: 200,
          calculable: true,
          orient: 'horizontal',
          top: '13%',
          left: 'center',
          itemWidth: 14,
          itemHeight: 114,
          inRange: {
            color: ['#bee8ff', '#0d59b7'],
            symbolSize: [100, 100],
          },
          textStyle: {
            color: 'rgba(230,247,255,0.5)',
          },
        },
        series: [{
          name: '',
          type: 'heatmap',
          data: data,
          label: {
            show: true
          },
          emphasis: {
            itemStyle: {
              shadowBlur: 10,
              shadowColor: 'rgba(0, 0, 0, 1)'
            }
          }
        }]
      }
      getchart.clear()
      getchart.setOption(option)
      // 随着屏幕大小调节图表
      window.addEventListener('resize', () => {
        getchart.resize()
      })
    },
    //使用情况/启用年限
    getRightBottom() {
      this.pieIdData.forEach((item) => {
        this.initPie(item);
      });
    },
    /** 初始化闭合饼图 */
    initPie(item) {
      var chartDom = document.getElementById(item);
      var myChart = echarts.init(chartDom);
      var option;
      let dataObj = {
        pie_useCondition: [
          { value: 848, name: "使用天数", title: '使用情况/天', color: '#8BDDF5' },
          { value: 235, name: "故障天数", title: '使用情况/天', color: '#FF2D55' },
        ],
        pie_enableYear: [
          { value: 535, name: "已用年限", title: '启用年限/年', color: '#8BDDF5' },
          { value: 125, name: "可用年限", title: '启用年限/年', color: '#61E29D' },
        ],
      };
      option = {
        title: {
          text: '日最高检查量/次',
          top: '2%',
          left: '2%',
          textStyle: {
            fontSize: "16px",
            color: '#ffffff',
            fontWeight: "500"
          },
        },
        tooltip: {
          trigger: "item",
        },
        legend: {
          top: "20%",
          left: "center",
          textStyle: {
            color: "#fff",
            fontSize: 12,
          },
          itemWidth: 8,
          itemHeight: 8,
          itemGap: 5,
          selectedMode: false,
        },
        series: [
          {
            name: "",
            type: "pie",
            radius: "50%",
            center: ["50%", "60%"],
            data: dataObj[item],
            label: {
              show: false,
            },
            itemStyle: {
              color: function (param) {
                if (param.dataIndex == 0) {
                  return dataObj[item][0].color
                } else {
                  return dataObj[item][1].color
                }
              }
            },
            emphasis: {
              label: {
                show: false,
              },
            },
          },
        ],
      };
      myChart.resize();
      myChart.clear();
      option && myChart.setOption(option);
      window.addEventListener("resize", () => {
        myChart.resize();
      });
    },
  },
};
</script>
<style lang="scss" scoped>
%box-common-styles {
  flex-shrink: 0;
  box-sizing: border-box;
  background: rgba(53, 98, 219, 0.06);
  border-radius: 2p;
}
.box-view {
  position: relative;
  height: 100%;
  width: 100%;
  box-sizing: border-box;
  display: flex;
  .box-left {
    width: 38%;
    height: 100%;
    display: flex;
    flex-direction: column;
    .box-left-top {
      height: calc((100% / 3));
      display: flex;
      .recycle {
        width: calc(38% - 0.5rem);
        height: 100%;
        @extend %box-common-styles;
      }
      .grossIncome {
        margin-left: 1rem;
        width: calc(62% - 0.5rem);
        height: 100%;
        padding: 12px;
        @extend %box-common-styles;
        &-title {
          font-weight: 500;
          font-size: 16px;
          color: #ffffff;
        }
        &-box {
          height: calc(100% - 20px);
        }
        &-content {
          height: calc((100% / 3) - 8px);
          background: rgba(53, 98, 219, 0.06);
          margin-top: 10px;
          display: flex;
          align-items: center;
        }
        .grossIncome-content-left {
          width: 68%;
          padding: 2px 24px;
          border-right: 1px solid rgba(133, 145, 206, 0.15);
          .item-block {
            display: inline-block;
            width: 12px;
            height: 12px;
            background: #8bddf5;
            border-radius: 2px;
            margin-right: 5px;
          }
          .item-label {
            font-weight: 500;
            font-size: 16px;
            color: #ffffff;
          }
          .item-value {
            font-weight: bold;
            font-size: 20px;
            color: #ffca64;
          }
          .item-unit {
            font-weight: 400;
            font-size: 16px;
            color: #a6afbf;
            margin-left: 4px;
          }
        }
        p:nth-child(2) {
          margin-top: 8px;
        }
        .grossIncome-content-right {
          text-align: center;
          flex: 1;
          padding: 4px 8px;
          .item-rate {
            font-weight: 500;
            font-size: 16px;
            color: #61e29d;
            margin-left: 2px;
          }
          .item-label {
            font-weight: 400;
            font-size: 14px;
            color: #ffffff;
          }
          img {
            width: 19px;
            height: 19px;
            vertical-align: middle;
          }
        }
      }
    }
    .box-left-center {
      @extend %box-common-styles;
      margin: 16px 0;
      height: calc((100% / 3) - 16px);
    }
    .box-left-bottom {
      height: calc((100% / 3) - 15px);
      @extend %box-common-styles;
    }
  }
  .box-center {
    width: calc(32% - 16px);
    height: 100%;
    display: flex;
    margin: 0 16px;
    flex-direction: column;
    .box-center-top {
      @extend %box-common-styles;
      height: calc(50% - 8px);
    }
    .box-center-bottom {
      margin-top: 16px;
      height: calc(50% - 8px);
      @extend %box-common-styles;
    }
  }
  .box-right {
    width: calc(30% - 16px);
    height: 100%;
    display: flex;
    flex-direction: column;
    .box-right-top {
      @extend %box-common-styles;
      height: calc((100% / 3) - 11px);
    }
    .box-right-center {
      @extend %box-common-styles;
      margin: 16px 0;
      height: calc((100% / 3) - 11px);
    }
    .box-right-bottom {
      height: calc((100% / 3) - 10px);
      display: flex;
      .useCondition {
        width: calc(50% - 0.5rem);
        height: 100%;
        @extend %box-common-styles;
      }
      .enableYear {
        margin-left: 1rem;
        width: calc(50% - 0.5rem);
        height: 100%;
        @extend %box-common-styles;
      }
    }
  }
}
</style>
