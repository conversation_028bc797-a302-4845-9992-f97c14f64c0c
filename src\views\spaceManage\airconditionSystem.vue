<template>
  <div class="content">
    <div v-if="NODE_ENV_FLAG">
      <!-- <div style="color: #ffe3a6; margin-left: 100px">{{ WPFmessage }}</div> -->
      <!-- 深圳肿瘤医院测试按钮 -->
      <el-button class="sino-button-sure" @click="szzlyyChange('device')"
      >SZZLYYDeviceData</el-button
      >
      <el-button class="sino-button-sure" @click="szzlyyChange('floor')"
      >SZZLYYFloorData</el-button
      >
      <el-button class="sino-button-sure" @click="szzlyyChange('build')"
      >SZZLYYBuildData</el-button
      >
      <el-button class="sino-button-sure" @click="szzlyyChange('region')"
      >SZZLYYRegionData</el-button
      >
      <el-button class="sino-button-sure" @click="szzlyyChange('menuZLJF')"
      >SZZLYY制冷机房分组1</el-button
      >
      <el-button class="sino-button-sure" @click="szzlyyChange('menuZLJF2')"
      >SZZLYY制冷机房分组2</el-button
      >
      <el-button class="sino-button-sure" @click="szzlyyChange('menuBPD')"
      >SZZLYY变配电分组1</el-button
      >
      <el-button class="sino-button-sure" @click="szzlyyChange('menuBPD2')"
      >SZZLYY变配电分组2</el-button
      >
      <el-button class="sino-button-sure" @click="szzlyyChange('menuBPDDG')"
      >SZZLYY变配电电柜</el-button
      >
      <el-button class="sino-button-sure" @click="szzlyyChange('menuZNZM')"
      >SZZLYY智能照明</el-button
      >
      <!-- 本地214测试 -->
      <!-- <el-button class="sino-button-sure" @click="localTestChange('device')">localDeviceData</el-button>
      <el-button class="sino-button-sure" @click="localTestChange('floor')">localFloorData</el-button>
      <el-button class="sino-button-sure" @click="localTestChange('build')">localBuildData</el-button>
      <el-button class="sino-button-sure" @click="localTestChange('region')">localRegionData</el-button>
      <el-button class="sino-button-sure" @click="localTestChange('menuZLJF')">local制冷机房分组</el-button>
      <el-button class="sino-button-sure" @click="localTestChange('menuBPD')">local变配电分组</el-button>
      <el-button class="sino-button-sure" @click="localTestChange('menuBPDDG')">local变配电电柜</el-button> -->
      <!-- <el-button class="sino-button-sure" @click="localTestChange('menuZNZM')">local智能照明</el-button>
      <el-button class="sino-button-sure" @click="localTestChange('elevatorFloor')">local电梯Floor</el-button>
      <el-button class="sino-button-sure" @click="localTestChange('elevatorBuild')">local电梯Build</el-button>
      <el-button class="sino-button-sure" @click="localTestChange('elevatorRegion')">local电梯Region</el-button> -->
      <!-- <el-button class="sino-button-sure" @click="bjsjtyyChange('region')">BJSJTYYRegion</el-button>
      <el-button class="sino-button-sure" @click="bjsjtyyChange('build')">BJSJTYYBuild</el-button>
      <el-button class="sino-button-sure" @click="bjsjtyyChange('floor')">BJSJTYYFloor</el-button> -->
    </div>
    <div class="sham-content"></div>
    <div ref="collapseHeight" class="right-content">
      <div class="bg-title">
        <!-- 表头 -->
        <div v-scrollMove class="bg-tab">
          <div
            v-for="(item, index) in iemcProjectList"
            :key="index"
            class="tab-div"
            :class="{ 'is-activeTab': activeIemcTab === item.wpfKey }"
            @click="activeIemcTypeEvent(item, {}, true)"
          >
            {{ item.name }}
          </div>
        </div>
        <div class="center-empty"></div>
        <div class="icon-collapse">
          <el-dropdown trigger="click" @command="tabItemCommand">
            <img src="@/assets/images/qhdsys/bg-gd.png" alt="" />
            <el-dropdown-menu slot="dropdown" class="dropdown">
              <el-dropdown-item
                v-for="(item, index) in iemcProjectList"
                :key="index"
                :command="item.wpfKey"
                :class="{ isBjxl: activeIemcTab === item.wpfKey }"
              >{{ item.name }}</el-dropdown-item
              >
            </el-dropdown-menu>
          </el-dropdown>
        </div>
      </div>
      <!-- 制冷机房 氧气站房.... -->
      <div v-if="activeIemcTab != 'Pipeline'" class="bg-content room-info-box">
        <div v-if="activeIemcTab !== 'light'" class="bg-title">
          <div v-if="activeIemcTab !== 'light'" v-scrollMove class="box-type">
            <el-badge
              v-for="(item, index) in roomTypeFilterList"
              :key="index"
              :hidden="item.badgeValue < 1 ? true : item.badgeHidden"
              :value="item.badgeValue"
              :max="99"
            >
              <span
                :id="item.Dom"
                class="tags-item"
                :class="{ type_active: activeType === item.Dom }"
                @click="activeTypeEvent(item.Dom)"
              >{{ item.name }}</span
              >
            </el-badge>
          </div>
          <div v-else v-scrollMove class="box-type">
            <el-badge
              v-for="(item, index) in roomTypeListNew"
              :key="index"
              :hidden="item.badgeHidden"
              :value="item.badgeValue"
              :max="99"
            >
              <span
                v-show="!item.is.includes(activeIemcTab)"
                :id="item.Dom"
                class="tags-item"
                :class="{ type_active: activeType === item.Dom }"
                @click="activeTypeEvent(item.Dom)"
              >{{ item.name }}</span
              >
            </el-badge>
          </div>
          <div
            v-if="roomTypeFilterList.length > 6"
            class="content-icon-collapse"
          >
            <el-dropdown trigger="click" @command="roomTypeCommand">
              <img src="@/assets/images/qhdsys/bg-gd.png" alt="" />
              <el-dropdown-menu slot="dropdown">
                <el-dropdown-item
                  v-for="(item, index) in roomTypeFilterList"
                  :key="index"
                  :command="item.Dom"
                  :class="{ isBjxl: activeType === item.Dom }"
                >{{ item.name }}</el-dropdown-item
                >
              </el-dropdown-menu>
            </el-dropdown>
          </div>
        </div>
        <div class="sys-box">
          <component
            :is="currentComponent"
            :ref="currentComponent"
            :roomData="roomData"
            :assetsList="assetsList"
            type="device"
            :deviceId="deviceIds"
            class="sys-box-content"
            @roomEvent="roomEvent"
            @sendWpfData="setWPFdeviceIdAndTab"
          ></component>
        </div>
      </div>
      <!-- 管线管理 -->
      <div v-if="activeIemcTab == 'Pipeline'" class="bg-content">
        <pipelineManagement v-if="NODE_ENV_HOSPITAL != 'bjsjtyy'" ref="pipelineManagement" :roomData="roomData" />
        <pipelineManagementSjt v-else ref="pipelineManagement" :roomData="roomData" />
      </div>
    </div>
    <template v-if="deviceInfoManagementShow">
      <deviceInfoManagement
        ref="deviceInfoManagement"
        :dialogData="deviceInfo"
        :dialogShow="deviceInfoManagementShow"
        @configCloseDialog="configCloseDeviceDialog"
      ></deviceInfoManagement>
    </template>
    <template v-if="deviceDetailsListShow">
      <deviceDialog
        ref="deviceDetailDialog"
        :dialogData="deviceDetailInfo"
        :dialogShow="deviceDetailsListShow"
        @deviceCloseDialog="() => changeDeviceDetailDialogShow(false)"
      ></deviceDialog>
    </template>
    <!-- 空间明细 -->
    <template v-if="spaceDetailsShow">
      <spaceDetails
        :show="spaceDetailsShow"
        :requestParams="requestParams"
        @closeDialog="hiddenSpaceDetailsDialog"
      />
    </template>
    <!-- 参数详情 -->
    <paramDetails v-if="isParamDetails" :dialogShow="isParamDetails" :paramData="paramData" @paramDetailsClose="paramDetailsClose" />
    <el-dialog

      v-dialogDrag
      :modal="false"
      :visible="alarmDetailVisible"
      custom-class="mainDialog main"
      append-to-body
      :close-on-click-modal="false"
      :before-close="() => alarmDetailVisible = false"
      class="all-table-componentList"
    >
      <EmergencyDetail
        v-if="alarmDetailVisible"
        :alarmId="currentAlarmId"
        :isView="false"
      >
      </EmergencyDetail>
    </el-dialog>
  </div>
</template>

<script>
import {
  getSurveyAssetByProjectCode,
  getTaskEquipmentStatistics,
  getSurveyInfoByModelCode
} from '@/utils/spaceManage'
import { monitorTypeList } from '@/assets/common/dict.js'
import  EmergencyDetail  from '@/views/normalMode/leftScreen/EmergencyDisposalNew/components/EmergencyDetail.vue'
export default {
  inject: ['reload'],
  name: 'airconditionSystem',
  components: {
    fireproof: () => import('./sysTypeComponent/fireproof'),
    floorSpaceComponent: () =>
      import('./sysTypeComponent/floorSpaceComponent.vue'),
    safeComponent: () => import('./sysTypeComponent/safeComponent'),
    moveComponent: () => import('./sysTypeComponent/moveComponent'),
    repairComponent: () => import('./sysTypeComponent/repairComponent'),
    icisComponent: () => import('./sysTypeComponent/icisComponent'),
    upkeepComponent: () => import('./sysTypeComponent/upkeepComponent'),
    ledgerComponent: () => import('./sysTypeComponent/ledgerComponent'),
    energyComponent: () => import('./sysTypeComponent/energyComponent'),
    pipelineManagement: () => import('./pipelineManagement'),
    pipelineManagementSjt: () => import('./pipelineManagementSjt'),
    deviceInfoManagement: () => import('./deviceInfoManagement'),
    lightingComponent: () => import('./sysTypeComponent/lightingComponent'),
    keyAreasComponent: () => import('./sysTypeComponent/keyAreasComponent'),
    deviceDialog: () => import('./sysTypeComponent/components/deviceDetailDialog'),
    spaceDetails: () => import('../centerScreen/intelligentOperation/components/spaceDetails.vue'),
    // 电梯监测
    elevatorMonitorOverview: () => import('../elevator/components/monitorOverview/index'),
    elevatorRecord: () => import('../elevator/components/monitorOverview/components/elevatorRecord'),
    elevatorMonitorStatistics: () => import('../elevator/components/monitorStatistics/index'),
    elevatorMonitorEnergy: () => import('../elevator/components/monitorEnergy/index'),
    openDoorAnalysis: () => import('../elevator/components/openDoorAnalysis/index'),
    stopAnalysis: () => import('../elevator/components/stopAnalysis/index'),
    paramDetails: () => import('../spaceManage/components/components/paramDetails'),
    travelRecord: () => import('./sysTypeComponent/travelRecord'),
    poisonousHempSafe: () => import('./sysTypeComponent/poisonousHempSafe'),
    logisticsTaskComponent: () => import('./sysTypeComponent/logisticsTaskComponent'),
    logisticsMonitorComponent: () => import('./sysTypeComponent/logisticsMonitorComponent'), // 新增物流小车监测组件
    operatinRoomMonitorComponent: () => import('./sysTypeComponent/operatinRoomMonitorComponent'), // 新增手术室监测组件
    warehouseMonitoringComponent: () => import('@/views/hazardousChemicalsManageModule/warehouseMonitoringComponent.vue'),
    chairMonitor: () => import('./sysTypeComponent/chairMonitor'), // 椅位监测
    notDisposeAlarm: () => import('./sysTypeComponent/notDisposeAlarm'), // 没有处置的报警
    moveComponentNew: () => import('./sysTypeComponent/moveComponentNew'), // 新监测
    EmergencyDetail
  },
  data() {
    const NODE_ENV_FLAG = import.meta.env.DEV
    const NODE_ENV_HOSPITAL = __PATH.VUE_APP_HOSPITAL_NODE
    return {
      NODE_ENV_FLAG,
      NODE_ENV_HOSPITAL,
      WPFmessage: '',
      deviceInfo: {},
      deviceDetailInfo: {},
      deviceInfoManagementShow: false,
      deviceDetailsListShow: false,
      spaceDetailsShow: false, // 空间明细弹窗
      activeIemcTab: '', // 当前选中的项目
      activeType: '',
      roomTypeList: [
        {
          Dom: 'move',
          name: '监测',
          component: 'moveComponent',
          index: 1,
          badgeHidden: true,
          badgeValue: 0,
          is: [
            'ElevatorSystem',
            'PoisonousHemp',
            'OperatinRoom',
            'LogisticsVehicle',
            'HazMat',
            'space',
            'KeyAreas',
            'SpaceChair',
            'SecuritySys'
          ]
        },
        {
          Dom: 'logisticsMonitor', // 新增物流小车专用监测配置
          name: '监测',
          component: 'logisticsMonitorComponent',
          index: 1,
          badgeHidden: true,
          badgeValue: 0,
          is: [
            'Kongtiao',
            'Yqxt',
            'Electricity',
            'Ups',
            'light',
            'ASHP',
            'WaterSupply',
            'SecuritySystem',
            'FireAlarmSystem',
            'Pipeline',
            'PoisonousHemp',
            'OperatinRoom',
            'ElevatorSystem',
            'HazMat',
            'Boiler',
            'space',
            'KeyAreas',
            'SpaceChair',
            'SecuritySys'
          // 不包含 LogisticsVehicle，使其在物流小车中显示
          ]
        },
        {
          Dom: 'operatinRoomMonitor', // 手术室监测-监测
          name: '监测',
          component: 'operatinRoomMonitorComponent',
          index: 1,
          badgeHidden: true,
          badgeValue: 0,
          is: [
            'Kongtiao',
            'Yqxt',
            'Electricity',
            'Ups',
            'light',
            'ASHP',
            'WaterSupply',
            'SecuritySystem',
            'FireAlarmSystem',
            'Pipeline',
            'PoisonousHemp',
            'ElevatorSystem',
            'LogisticsVehicle',
            'HazMat',
            'Boiler',
            'space',
            'KeyAreas',
            'SpaceChair',
            'SecuritySys'
          //
          ]
        },
        {
          Dom: 'logisticsTask',
          name: '任务',
          component: 'logisticsTaskComponent',
          index: 8,
          badgeHidden: true,
          badgeValue: 0,
          is: [
            'Kongtiao',
            'Yqxt',
            'Electricity',
            'Ups',
            'light',
            'ASHP',
            'WaterSupply',
            'SecuritySystem',
            'FireAlarmSystem',
            'Pipeline',
            'PoisonousHemp',
            'OperatinRoom',
            'ElevatorSystem',
            'HazMat',
            'Boiler',
            'space',
            'KeyAreas',
            'SpaceChair',
            'SecuritySys'
            // 不包含 LogisticsVehicle，使其显示此选项
          ]
        },
        {
          Dom: 'overviewOfMaterials',
          name: '库房监测',
          component: 'warehouseMonitoringComponent',
          index: 1,
          badgeHidden: true,
          badgeValue: 0,
          is: [
            'Kongtiao',
            'Yqxt',
            'Electricity',
            'Ups',
            'light',
            'ASHP',
            'WaterSupply',
            'SecuritySystem',
            'FireAlarmSystem',
            'Pipeline',
            'PoisonousHemp',
            'OperatinRoom',
            'LogisticsVehicle',
            'ElevatorSystem',
            'Boiler',
            'space',
            'KeyAreas',
            'SpaceChair',
            'SecuritySys'
          // 不包含 LogisticsVehicle，使其在物流小车中显示
          ]
        },
        {
          Dom: 'safe',
          name: '报警',
          component: 'safeComponent',
          index: 0,
          badgeHidden: true,
          badgeValue: 0,
          is: [
            'ElevatorSystem',
            'PoisonousHemp',
            'LogisticsVehicle',
            'space',
            'KeyAreas',
            'SpaceChair',
            'SecuritySys'
          ]
        },
        {
          Dom: 'repair',
          name: '维修',
          component: 'repairComponent',
          index: 2,
          badgeHidden: true,
          badgeValue: 0,
          is: [
            'ElevatorSystem',
            'PoisonousHemp',
            'OperatinRoom',
            'LogisticsVehicle',
            'HazMat',
            'space',
            'KeyAreas',
            'SpaceChair',
            'SecuritySys'
          ]
        },
        {
          Dom: 'icis',
          name: '巡检',
          component: 'icisComponent',
          index: 3,
          badgeHidden: false,
          badgeValue: 0,
          is: [
            'FireAlarmSystem',
            'SecuritySystem',
            'ElevatorSystem',
            'PoisonousHemp',
            'OperatinRoom',
            'LogisticsVehicle',
            'HazMat',
            'space',
            'KeyAreas',
            'SpaceChair',
            'SecuritySys'
          ]
        },
        {
          Dom: 'upkeep',
          name: '保养',
          component: 'upkeepComponent',
          index: 4,
          badgeHidden: false,
          badgeValue: 0,
          is: [
            'FireAlarmSystem',
            'SecuritySystem',
            'ElevatorSystem',
            'PoisonousHemp',
            'OperatinRoom',
            'LogisticsVehicle',
            'HazMat',
            'space',
            'KeyAreas',
            'SpaceChair',
            'SecuritySys'
          ]
        },
        // {
        //   Dom: 'ledger',
        //   name: '台账',
        //   component: 'ledgerComponent',
        //   index: 5
        // },
        {
          Dom: 'energy',
          name: '能耗',
          component: 'energyComponent',
          index: 6,
          badgeHidden: true,
          badgeValue: 0,
          is: [
            'Yqxt',
            'Electricity',
            'Ups',
            'light',
            'ASHP',
            'WaterSupply',
            'FireAlarmSystem',
            'SecuritySystem',
            'ElevatorSystem',
            'PoisonousHemp',
            'OperatinRoom',
            'LogisticsVehicle',
            'HazMat',
            'Boiler',
            'space',
            'KeyAreas',
            'SpaceChair',
            'SecuritySys'
          ]
        },
        {
          Dom: 'fireproof',
          name: '防火分区',
          component: 'fireproof',
          index: 6,
          badgeHidden: true,
          badgeValue: 0,
          is: [
            'Kongtiao',
            'Yqxt',
            'Electricity',
            'Ups',
            'light',
            'ASHP',
            'WaterSupply',
            'SecuritySystem',
            'ElevatorSystem',
            'PoisonousHemp',
            'OperatinRoom',
            'LogisticsVehicle',
            'HazMat',
            'Boiler',
            'space',
            'KeyAreas',
            'SpaceChair',
            'SecuritySys'
          ]
        },
        {
          Dom: 'elevatorMonitorOverview',
          name: '监测总览',
          component: 'elevatorMonitorOverview',
          index: 6,
          badgeHidden: true,
          badgeValue: 0,
          is: [
            'Kongtiao',
            'Yqxt',
            'Electricity',
            'Ups',
            'light',
            'ASHP',
            'WaterSupply',
            'SecuritySystem',
            'FireAlarmSystem',
            'Pipeline',
            'PoisonousHemp',
            'OperatinRoom',
            'LogisticsVehicle',
            'HazMat',
            'Boiler',
            'space',
            'KeyAreas',
            'SpaceChair',
            'SecuritySys'
          ]
        },
        {
          Dom: 'elevatorRecord',
          name: '报警记录',
          component: 'elevatorRecord',
          index: 1,
          badgeHidden: true,
          badgeValue: 0,
          is: [
            'Kongtiao',
            'Yqxt',
            'Electricity',
            'Ups',
            'light',
            'ASHP',
            'WaterSupply',
            'SecuritySystem',
            'FireAlarmSystem',
            'Pipeline',
            'PoisonousHemp',
            'OperatinRoom',
            'LogisticsVehicle',
            'HazMat',
            'Boiler',
            'space',
            'KeyAreas',
            'SpaceChair',
            'SecuritySys'
          ]
        },
        {
          Dom: 'elevatorMonitorStatistics',
          name: '运行统计',
          component: 'elevatorMonitorStatistics',
          index: 6,
          badgeHidden: true,
          badgeValue: 0,
          is: [
            'Kongtiao',
            'Yqxt',
            'Electricity',
            'Ups',
            'light',
            'ASHP',
            'WaterSupply',
            'SecuritySystem',
            'FireAlarmSystem',
            'Pipeline',
            'PoisonousHemp',
            'OperatinRoom',
            'LogisticsVehicle',
            'HazMat',
            'Boiler',
            'space',
            'KeyAreas',
            'SpaceChair',
            'SecuritySys'
          ]
        },
        {
          Dom: 'elevatorMonitorEnergy',
          name: '能耗分析',
          component: 'elevatorMonitorEnergy',
          index: 6,
          badgeHidden: true,
          badgeValue: 0,
          is: [
            'Kongtiao',
            'Yqxt',
            'Electricity',
            'Ups',
            'light',
            'ASHP',
            'WaterSupply',
            'SecuritySystem',
            'FireAlarmSystem',
            'Pipeline',
            'PoisonousHemp',
            'OperatinRoom',
            'LogisticsVehicle',
            'HazMat',
            'Boiler',
            'space',
            'KeyAreas',
            'SpaceChair',
            'SecuritySys',
            'ElevatorSystem'
          ]
        },
        {
          Dom: 'openDoorAnalysis',
          name: '开门分析',
          component: 'openDoorAnalysis',
          index: 6,
          badgeHidden: true,
          badgeValue: 0,
          is: [
            'Kongtiao',
            'Yqxt',
            'Electricity',
            'Ups',
            'light',
            'ASHP',
            'WaterSupply',
            'SecuritySystem',
            'FireAlarmSystem',
            'Pipeline',
            'PoisonousHemp',
            'OperatinRoom',
            'LogisticsVehicle',
            'HazMat',
            'Boiler',
            'space',
            'KeyAreas',
            'SpaceChair',
            'SecuritySys'
          ]
        },
        {
          Dom: 'stopAnalysis',
          name: '停靠分析',
          component: 'stopAnalysis',
          index: 6,
          badgeHidden: true,
          badgeValue: 0,
          is: [
            'Kongtiao',
            'Yqxt',
            'Electricity',
            'Ups',
            'light',
            'ASHP',
            'WaterSupply',
            'SecuritySystem',
            'FireAlarmSystem',
            'Pipeline',
            'PoisonousHemp',
            'OperatinRoom',
            'LogisticsVehicle',
            'HazMat',
            'Boiler',
            'space',
            'KeyAreas',
            'SpaceChair',
            'SecuritySys'
          ]
        },
        { // 毒麻精方-报警
          Dom: 'poisonousHempSafe',
          name: '报警',
          component: 'poisonousHempSafe',
          index: 1,
          badgeHidden: true,
          badgeValue: 0,
          is: [
            'Kongtiao',
            'Yqxt',
            'Electricity',
            'Ups',
            'light',
            'ASHP',
            'WaterSupply',
            'ElevatorSystem',
            'SecuritySystem',
            'FireAlarmSystem',
            'Pipeline',
            'OperatinRoom',
            'LogisticsVehicle',
            'HazMat',
            'Boiler',
            'space',
            'KeyAreas',
            'SpaceChair',
            'SecuritySys'
          ]
        },
        {
          Dom: 'chairMonitor',
          name: '椅位监测',
          component: 'chairMonitor',
          index: 1,
          badgeHidden: true,
          badgeValue: 0,
          is: [
            'Boiler',
            'Kongtiao',
            'Yqxt',
            'Electricity',
            'Ups',
            'light',
            'space',
            'KeyAreas',
            'ASHP',
            'WaterSupply',
            'ElevatorSystem',
            'SecuritySystem',
            'FireAlarmSystem',
            'Pipeline',
            'PoisonousHemp',
            'OperatinRoom',
            'LogisticsVehicle',
            'HazMat',
            'SecuritySys'
          ]
        },
        {
          Dom: 'moveComponentNew',
          name: '设备监测',
          component: 'moveComponentNew',
          index: 1,
          badgeHidden: true,
          badgeValue: 0,
          is: [
            'Boiler',
            'Kongtiao',
            'Yqxt',
            'Electricity',
            'Ups',
            'light',
            'space',
            'KeyAreas',
            'ASHP',
            'WaterSupply',
            'ElevatorSystem',
            'SecuritySystem',
            'FireAlarmSystem',
            'Pipeline',
            'PoisonousHemp',
            'OperatinRoom',
            'LogisticsVehicle',
            'HazMat',
            'SecuritySys'
          ]
        },
        {
          Dom: 'moveComponentNew',
          name: '安防监测',
          component: 'moveComponentNew',
          index: 1,
          badgeHidden: true,
          badgeValue: 0,
          is: [
            'Boiler',
            'Kongtiao',
            'Yqxt',
            'Electricity',
            'Ups',
            'light',
            'KeyAreas',
            'ASHP',
            'WaterSupply',
            'ElevatorSystem',
            'SecuritySystem',
            'FireAlarmSystem',
            'Pipeline',
            'PoisonousHemp',
            'OperatinRoom',
            'LogisticsVehicle',
            'HazMat',
            'SpaceChair',
            'space'
          ]
        },
        { // 空间椅位/监控管理-报警
          Dom: 'notDisposeAlarm',
          name: '报警',
          component: 'notDisposeAlarm',
          index: 1,
          badgeHidden: true,
          badgeValue: 0,
          is: [
            'Boiler',
            'Kongtiao',
            'Yqxt',
            'Electricity',
            'Ups',
            'light',
            'KeyAreas',
            'ASHP',
            'WaterSupply',
            'ElevatorSystem',
            'SecuritySystem',
            'FireAlarmSystem',
            'Pipeline',
            'PoisonousHemp',
            'OperatinRoom',
            'LogisticsVehicle',
            'HazMat',
            'space'
          ]
        }
      ],
      roomTypeListNew: [
        {
          Dom: 'move',
          name: '监测',
          component: 'lightingComponent',
          index: 7,
          badgeHidden: true,
          badgeValue: 0,
          is: ['ElevatorSystem']
        }
      ],
      // projectName: '制冷机房',
      allProjectList: [
        {
          name: '空间台账',
          wpfKey: 'space',
          EN_HOSPITAL_ENV: ['bjsjtyy', 'fjslyy', 'gzzyyy', 'ljxyy', 'syzxyy', 'szdeyy', 'szzlyy']
        },
        {
          name: '锅炉房',
          wpfKey: 'Boiler',
          EN_HOSPITAL_ENV: ['szdeyy', 'szzlyy', 'sjydkqyy', 'fjslyy']
        },
        {
          name: '制冷机房',
          wpfKey: 'Kongtiao',
          EN_HOSPITAL_ENV: ['szdeyy', 'sjydkqyy']
        },
        {
          name: '氧气站房',
          wpfKey: 'Yqxt',
          EN_HOSPITAL_ENV: ['szdeyy', 'fjslyy', 'gzzyyy', 'sjydkqyy']
        },
        {
          name: '配电安全',
          wpfKey: 'Electricity',
          EN_HOSPITAL_ENV: ['szdeyy', 'sjydkqyy']
        },
        {
          name: '应急电源',
          wpfKey: 'Ups',
          EN_HOSPITAL_ENV: ['szdeyy', 'gzzyyy', 'sjydkqyy']
        },
        {
          name: '智能照明',
          wpfKey: 'light',
          EN_HOSPITAL_ENV: ['syzxyy', 'szdeyy', 'gzzyyy', 'sjydkqyy']
        },

        {
          name: '重点区域',
          wpfKey: 'KeyAreas',
          EN_HOSPITAL_ENV: ['bjsjtyy', 'fjslyy', 'gzzyyy', 'ljxyy', 'syzxyy', 'szdeyy', 'szzlyy']
        },
        {
          name: '空间椅位',
          wpfKey: 'SpaceChair',
          EN_HOSPITAL_ENV: ['bjsjtyy', 'fjslyy', 'gzzyyy', 'ljxyy', 'syzxyy', 'szdeyy', 'szzlyy']
        },
        {
          name: '监控管理',
          wpfKey: 'SecuritySys',
          EN_HOSPITAL_ENV: ['bjsjtyy', 'fjslyy', 'gzzyyy', 'ljxyy', 'syzxyy', 'szdeyy', 'szzlyy']
        },
        {
          name: '风冷热泵',
          wpfKey: 'ASHP',
          EN_HOSPITAL_ENV: ['szdeyy', 'gzzyyy', 'sjydkqyy']
        },
        {
          name: '给水排水',
          wpfKey: 'WaterSupply',
          EN_HOSPITAL_ENV: ['szdeyy', 'sjydkqyy']
        },
        {
          name: '电梯系统',
          wpfKey: 'ElevatorSystem',
          EN_HOSPITAL_ENV: ['fjslyy', 'sjydkqyy']
        },
        {
          name: '安防系统',
          wpfKey: 'SecuritySystem',
          EN_HOSPITAL_ENV: ['syzxyy', 'sjydkqyy']
        },
        {
          name: '消防系统',
          wpfKey: 'FireAlarmSystem',
          EN_HOSPITAL_ENV: ['syzxyy', 'gzzyyy', 'sjydkqyy']
        },
        {
          name: '管线管理',
          wpfKey: 'Pipeline',
          EN_HOSPITAL_ENV: ['syzxyy', 'szdeyy', 'gzzyyy', 'sjydkqyy']
        },
        {
          name: '毒麻精放',
          wpfKey: 'PoisonousHemp',
          EN_HOSPITAL_ENV: ['gzzyyy', 'sjydkqyy', 'fjslyy']
        },
        {
          name: '手术室',
          wpfKey: 'OperatinRoom',
          EN_HOSPITAL_ENV: ['gzzyyy', 'sjydkqyy', 'fjslyy']
        },
        {
          name: '物流小车',
          wpfKey: 'LogisticsVehicle',
          EN_HOSPITAL_ENV: ['sjydkqyy', 'fjslyy']
        },
        {
          name: '危化品',
          wpfKey: 'HazMat',
          EN_HOSPITAL_ENV: ['gzzyyy', 'sjydkqyy', 'fjslyy']
        }
      ],
      iemcProjectList: [],
      roomData: {
        tabName: '', // 当前选中的tab
        mathRandom: '', // 用于刷新数据防止子页面监听不到
        electricityLevel: '0', // 配电机房专属字段默认0机房 1电柜
        deviceId: '', // 维修设备id
        localtion: '',
        ssmType: '', // 空间等级：2医院，3区域，4建筑，5楼层，6房间
        ssmCodes: '',
        modelCode: '',
        projectCode: '', // code
        isSpace: '', // 0空间 1设备
        menuCode: '', // 分组Code
        modelCoding: '', // 机柜Code集合
        title: ''
      },
      deviceIds: '', // projectCode对应关联设备ids
      assetsList: [], // projectCode对应关联设备列表
      collapseFlag: true,
      requestParams: {
        projectCode: '',
        spaceId: '', // 空间id
        ssmType: '' // 空间类型
      },
      isParamDetails: false,
      paramData: {},
      // 报警详情
      currentAlarmId: '',
      alarmDetailVisible: false
    }
  },
  computed: {
    currentComponent() {
      if (this.activeIemcTab == 'space') {
        return 'floorSpaceComponent'
      } else if (this.activeIemcTab == 'KeyAreas') {
        return 'keyAreasComponent'
      } else if (this.activeIemcTab == 'light') {
        return 'lightingComponent'
      } else {
        return (
          this.roomTypeList.find((e) => e.Dom === this.activeType)?.component ||
          ''
        )
      }
    },
    roomTypeFilterList() {
      return this.roomTypeList.filter(
        (e) => !e.is.includes(this.activeIemcTab)
      )
    }
  },
  created() {
    if (__PATH.VUE_APP_HOSPITAL_NODE == 'fjslyy') {
      this.roomTypeList.push({
        Dom: 'travelRecord',
        name: '通行记录',
        component: 'travelRecord',
        index: 7,
        badgeHidden: true,
        badgeValue: 0,
        is: [
          'Kongtiao',
          'Yqxt',
          'Electricity',
          'Ups',
          'light',
          'ASHP',
          'WaterSupply',
          'ElevatorSystem',
          'FireAlarmSystem',
          'Pipeline',
          'PoisonousHemp',
          'OperatinRoom',
          'LogisticsVehicle',
          'HazMat',
          'Boiler',
          'KeyAreas',
          'space'
        ]
      })
    }
    this.iemcProjectList = this.allProjectList.filter(
      (e) => !e.EN_HOSPITAL_ENV?.includes(__PATH.VUE_APP_HOSPITAL_NODE)
    )
    let activeTabData = this.iemcProjectList[0]
    let params = {}
    if (Object.hasOwn(this.$route.query, 'tabName')) {
      activeTabData = this.iemcProjectList.find(
        (e) => e.wpfKey === this.$route.query.tabName
      )
      params = this.$route.query
    }
    params.areaData = this.$route.query.areaData
      ? JSON.parse(this.$route.query.areaData)
      : {}
    this.activeIemcTypeEvent(activeTabData, params, false)
  },
  mounted () {
    localStorage.setItem(
      'imesAssetsData',
      JSON.stringify({
        assetsNumber: '',
        id: ''
      })
    )

    // 初始化获取modelCode
    if (Object.hasOwn(this.$route.query, 'modelCode')) {
      // spaceManage?modelCode=BJSJTYY01&ssmCodes=1574997196057620481,1574997196330250241&localtion=01&ssmType=2
      // this.getSurveyAssetByProjectCode({ssmCodes: this.$route.query.ssmCodes}).then(() => {
      let ssmCodeList = this.$route.query.ssmCodes.split(',')
      if (ssmCodeList.length && ssmCodeList[0] != '#') {
        ssmCodeList = ['#', ...ssmCodeList].toString()
      }
      Object.assign(this.roomData, {
        ...this.$route.query,
        ssmCodes: ssmCodeList,
        title: '院区'
      })
      this.activeTabIndex = this.roomData.tabName
      // 四军医需要默认进入空间台账，所以加判断直接调用这个方法
      if (this.activeIemcTab === 'space') {
        this.initRoomInfo()
      }
      // })
    }
    try {
      window.chrome.webview.addEventListener('message', (event) => {
        this.WPFmessage = event.data
        const data = JSON.parse(event.data)
        if (data.type === 'init') {
          // 页面重载
          this.reload()
        } else if (data.type === 'area') {
          // 区域
          data.ssmType = data.ssmType ? Number(data.ssmType) : 3
          // 判断由wpf传递的数据是通过SBMenuBarSwitch发出的，重置二级菜单（SBMenuBarSwitch: 切换一级菜单）
          if (data.callBackBridge == 'SBMenuBarSwitch') {
            if (this.activeIemcTab === 'ElevatorSystem') {
              this.activeType = 'elevatorMonitorOverview'
            } else if (['lightingComponent', 'keyAreasComponent'].includes(this.activeIemcTab)) {
              this.activeType = ''
            } else if (this.activeIemcTab === 'PoisonousHemp') {
              this.activeType = 'poisonousHempSafe'
            } else if (this.activeIemcTab === 'OperatinRoom') {
              this.activeType = 'operatinRoomMonitor'
            } else if (this.activeIemcTab === 'LogisticsVehicle') {
              this.activeType = 'logisticsMonitor'
            } else if (this.activeIemcTab === 'HazMat') {
              this.activeType = 'overviewOfMaterials'
            } else if (this.activeIemcTab === 'SpaceChair') {
              this.activeType = 'chairMonitor'
            } else if (this.activeIemcTab === 'SecuritySys') {
              this.activeType = 'moveComponentNew'
            } else {
              this.activeType = 'move'
            }
          }
          // 如果详情弹窗未关闭 关闭弹窗并执行后续逻辑
          if (this.deviceInfoManagementShow) {
            this.deviceInfoManagementShow = false
            this.collapseFlag = true
            this.$nextTick(() => {
              this.$refs.collapseHeight.style.width = '24.573%'
              this.$refs.collapseHeight.style.padding = '0 25px 10px 35px'
            })
          }
          // 管线事件在子组件中单独处理
          if (this.activeIemcTab == 'Pipeline') return
          const projectInfo = monitorTypeList.find(
            (e) => e.wpfKey === this.activeIemcTab
          )
          // 切换模型层级时，改变层级对应的设备ids
          this.getSurveyAssetByProjectCode({
            projectCode: projectInfo?.projectCode,
            categoryCode: projectInfo?.categoryCode ?? '',
            ssmCodes: data.ssmCodes,
            menuCode: data.menuCode,
            modelCoding: data.modelCoding,
            isSpace: data.isSpace
          }).then((res) => {
            Object.assign(this.roomData, {
              tabName: this.activeIemcTab,
              projectCode: projectInfo?.projectCode,
              categoryCode: projectInfo?.categoryCode ?? '',
              isSpace: data.isSpace || 0,
              menuCode: data.menuCode || '',
              modelCoding: data.modelCoding || '',
              electricityLevel: data.electricityLevel,
              mathRandom: data.mathRandom,
              modelCode: data.modelCode,
              localtion: data.localtion,
              ssmType: data.ssmType,
              ssmCodes: data.ssmCodes,
              title: data.title,
              floorName: data.floorName,
              deviceId: res,
              callBackBridge: data.callBackBridge,
              areaData: data.areaData ? JSON.parse(data.areaData) : {}
            })
            if (this.activeIemcTab == 'light' && data.ssmType == '6') {
              this.requestParams.spaceId = data.ssmCodes?.split(',')?.at(-1)
              this.requestParams.projectCode = projectCode
              this.requestParams.ssmType = data.ssmType
              this.showSpaceDetailsDialog()
            }
            this.setWPFdeviceIdAndTab()
            this.getInspBeExecutedTaskNumber()
          })
        } else if (data.type === 'deviceInfo') {
          // 根据wpf返回的模型id换取设备assetId
          const deviceInfo = this.assetsList.find(
            (e) => e.modelCode === data.deviceId
          )
          this.openDevideDialog(deviceInfo, data.deviceId)
        }  else if (data.type === 'deviceInfoNew') { // 新监测设备视角
          let deviceInfo = {
            assetId: data.deviceId,
            assetName: data.deviceName,
            modelCode: data.deviceId
          }
          if (this.activeIemcTab == 'SpaceChair') {
            if (data.deviceType == 'dept') {
              this.setDeviceInfoEvent({
                ...deviceInfo,
                detailsNav: [{ name: '科室详情', component: 'deptDetails' }]
              })
            } else if (data.deviceType == 'chair') {
              this.setDeviceInfoEvent({
                ...deviceInfo,
                detailsNav: [{ name: '椅位详情', component: 'basicAccountComponent' }]
              })
            } else {
              this.setDeviceInfoEvent({
                ...deviceInfo,
                detailsNav: [
                  { name: '详情', component: 'assetDetails' },
                  {name: '报警', component: 'assetAlarmDetails' }
                ]
              })
            }
          } else if (this.activeIemcTab == 'SecuritySys') {
            this.setDeviceInfoEvent({
              ...deviceInfo,
              detailsNav: [
                { name: '详情', component: 'basicAccountComponent' },
                {name: '报警', component: 'assetAlarmDetails' }
              ]
            })
          } else {
            this.openDevideDialog(deviceInfo, data.deviceId)
          }
        } else if (data.type === 'deviceHistory') {
          this.paramData = data.data
          this.isParamDetails = true
          try {
            window.chrome.webview.hostObjects.sync.bridge.ShowDialog(true)
          } catch (error) {}
        } else if (data.type === 'elevatorAlarm') {
          this.currentAlarmId = data.alarmId
          this.alarmDetailVisible = true
          try {
            window.chrome.webview.hostObjects.sync.bridge.ShowDialog(true)
          } catch (error) {}
        }
      })
    } catch (errpr) {}
    // this.activeTypeEvent('move')
  },
  methods: {
    paramDetailsClose() {
      this.isParamDetails = false
      try {
        window.chrome.webview.hostObjects.sync.bridge.ShowDialog(false)
      } catch (error) {}
    },
    roomEvent(data) {
      if (this.activeIemcTab !== 'light') {
        let deviceInfo = {
          assetId: data.assetId ? data.assetId : data.assetsId,
          assetName: data.assetName,
          modelCode: data.modelCode,
          entityTypeId: data.entityTypeId
        }
        if (data.type === 'move') {
          this.openDevideDialog(deviceInfo)
        } else if (data.type === 'manyMove') {
          deviceInfo.detailsNav = data.detailsNav
          this.setDeviceInfoEvent(deviceInfo)
        }
      }
    },
    activeIemcTypeEvent(item, params = {}, flag) {
      this.activeIemcTab = item.wpfKey
      this.$nextTick(() => {
        var element = document.querySelector('.is-activeTab')
        element.scrollIntoView({ behavior: 'smooth' })
      })
      const projectInfo = monitorTypeList.find((e) => e.wpfKey === this.activeIemcTab)
      Object.assign(this.roomData, {
        ...params,
        tabName: item.wpfKey,
        projectCode: projectInfo?.projectCode,
        categoryCode: projectInfo?.categoryCode ?? ''
      })
      if (item.wpfKey !== 'Pipeline') {
        // 初始化查询项目code对应的设备ids
        let initParams = {}
        if (Object.keys(params).length) {
          initParams = {
            ...params,
            projectCode: projectInfo?.projectCode
          }

          this.getSurveyAssetByProjectCode(initParams).then((res) => {
            Object.assign(this.roomData, {
              ...params,
              tabName: item.wpfKey,
              projectCode: projectInfo?.projectCode,
              categoryCode: projectInfo?.categoryCode ?? '',
              deviceId: res
            })
            // 切换项目时，重置二级菜单

            // if (this.activeIemcTab !== 'light') {
            //   this.activeTypeEvent('move', flag)
            // } else {
            //   this.activeTypeEvent('light', flag)
            //   this.getInspBeExecutedTaskNumber()
            // }
          })
        } else {
        }
        if (item.wpfKey === 'ElevatorSystem') {
          Object.assign(this.roomData, {
            ...params,
            tabName: item.wpfKey,
            projectCode: projectInfo?.projectCode,
            categoryCode: projectInfo?.categoryCode ?? ''
          })
          this.activeTypeEvent('elevatorMonitorOverview', flag)
        } else if (item.wpfKey === 'PoisonousHemp') {
          this.activeTypeEvent('poisonousHempSafe', flag)
        } else if (item.wpfKey === 'OperatinRoom') {
          this.activeTypeEvent('operatinRoomMonitor', flag)
        } else if (item.wpfKey === 'LogisticsVehicle') {
          this.activeTypeEvent('logisticsMonitor', flag) // 物流小车默认显示监测页面
        } else if (item.wpfKey === 'HazMat') {
          this.activeTypeEvent('overviewOfMaterials', flag)
        } else if (item.wpfKey === 'SpaceChair') {
          this.activeTypeEvent('chairMonitor', flag)
        } else if (item.wpfKey === 'SecuritySys') {
          this.activeTypeEvent('moveComponentNew', flag)
        }  else {
          this.activeTypeEvent('move', flag)
        }
        try {
          window.chrome.webview.hostObjects.sync.bridge.SBMenuBarSwitch(
            item.wpfKey,
            'formH5'
          )
        } catch (error) {}
        // 没数据时，不请求接口 等待wpf通过SBMenuBarSwitch接收菜单并通过message传递数据后调用接口
      } else {
        try {
          window.chrome.webview.hostObjects.sync.bridge.SBMenuBarSwitch(
            item.wpfKey,
            'formH5'
          )
        } catch (error) {}
      }
    },
    // 楼层 渲染 空间信息
    initRoomInfo () {
      let index = this.roomTypeList.findIndex(
        (e) => e.Dom === this.activeTabIndex
      )
      index = index === -1 ? 0 : index
      // this.sysTitle = this.roomData.title
      this.activeTypeEvent(this.roomTypeList[index].Dom, index)
    },
    activeTypeEvent(type, flag) {
      this.activeType = type
      if (flag) return
      this.setWPFdeviceIdAndTab()
    },
    configCloseDeviceDialog() {
      this.$nextTick(() => {
        this.deviceInfoManagementShow = false
        setTimeout(() => {
          this.collapseFlag = true
          // 增加mathRandom刷新页面
          this.getSurveyAssetByProjectCode({
            projectCode: this.roomData.projectCode,
            ssmCodes: this.roomData.ssmCodes,
            menuCode: this.roomData.menuCode,
            modelCoding: '',
            isSpace: this.roomData.isSpace
          }).then((res) => {
            Object.assign(this.roomData, {
              tabName: this.activeIemcTab,
              modelCoding: '',
              mathRandom: Math.random(),
              electricityLevel: 0, // 返回的时候使模型回归到机房
              deviceId: res
            })
            // 重置二级菜单
            this.activeTypeEvent(this.activeType)
            this.getInspBeExecutedTaskNumber()
          })
        }, 200)
        this.$refs.collapseHeight.style.width = '24.573%'
        this.$refs.collapseHeight.style.padding = '0 25px 10px 35px'
      })
      localStorage.setItem(
        'imesAssetsData',
        JSON.stringify({
          assetsNumber: '',
          id: ''
        })
      )
    },
    // 关闭设备详情弹窗
    deviceDetailCloseDialog() {
      this.deviceDetailsListShow = false
    },
    changeDeviceDetailDialogShow(flag) {
      this.deviceDetailsListShow = flag
      try {
        window.chrome.webview.hostObjects.sync.bridge.ShowDialog(flag)
        window.chrome.webview.hostObjects.sync.bridge.GetSwichFloorIsShow(flag)
      } catch (error) {}
    },
    // 获取所有设备列表
    getSurveyAssetByProjectCode(initParams = {}) {
      const params =
        initParams.isSpace == '0'
          ? {
            projectCode: initParams.projectCode || this.roomData.projectCode,
            spaceId:
                initParams.ssmCodes?.split(',')?.at(-1) ||
                this.roomData.ssmCodes?.split(',')?.at(-1),
            menuCode: '',
            modelCode: ''
          }
          : {
            projectCode: initParams.projectCode || this.roomData.projectCode,
            menuCode: initParams.menuCode || this.roomData.menuCode,
            modelCode: initParams.modelCoding
          }
      return getSurveyAssetByProjectCode(params)
        .then((res) => {
          if (res.data.code === '200') {
            this.assetsList = res.data.data
            const deviceIdArr = Array.from(
              this.assetsList,
              ({ assetId }) => assetId
            )
            const deviceIds = deviceIdArr.length ? deviceIdArr.toString() : ''
            return deviceIds
          } else {
            this.assetsList = []
            return ''
          }
        })
        .catch(() => {
          this.assetsList = []
          return ''
        })
    },
    // 获取 巡检保养当日待执行任务数量
    getInspBeExecutedTaskNumber() {
      if (this.roomData.deviceId) {
        const params = {
          deviceId: this.roomData.deviceId
        }
        getTaskEquipmentStatistics(params).then((res) => {
          const resData = res.data
          if (resData.code === '200') {
            this.setBadgeValue('icis', resData.data.equipmentDayCount || 0)
            this.setBadgeValue('upkeep', resData.data.unfinishedYearCount || 0)
          } else {
            this.setBadgeValue('icis', 0)
            this.setBadgeValue('upkeep', 0)
          }
        })
      } else {
        this.setBadgeValue('icis', 0)
        this.setBadgeValue('upkeep', 0)
      }
    },
    // 设置二级菜单badge的值
    setBadgeValue(type, value) {
      this.roomTypeList.find((e) => e.Dom === type).badgeValue = value
    },
    // 传递给wpf的数据 包括子组件传递的数据
    setWPFdeviceIdAndTab (emitData = {}) {
      // 判断是否有子组件“初始传递”的数据 例：move没有初始查询所有数据不需要进行传递交互
      const initSendType = [
        'safe',
        'repair',
        'icis',
        'upkeep',
        'energy',
        'elevatorMonitorOverview',
        'elevatorMonitorStatistics',
        'elevatorMonitorEnergy'
      ]
      if (!Object.keys(emitData).length && initSendType.includes(this.activeType)) {
        return
      }
      const sendObj = {
        deviceIds: this.roomData.deviceId,
        electricityLevel: this.roomData.electricityLevel,
        modelCoding: this.roomData.modelCoding,
        ...emitData
      }
      console.log(
        this.activeType,
        sendObj,
        this.roomData.modelCode,
        this.roomData.projectCode,
        'DeviceIdInfo'
      )
      // 通知wpf 二级菜单 项目code对应的设备ids 以及wpf传来的模型层级code
      try {
        window.chrome.webview.hostObjects.sync.bridge.DeviceIdInfo(
          this.activeType,
          JSON.stringify(sendObj),
          this.roomData.modelCode,
          this.roomData.projectCode
          // this.roomData.categoryCode
        )
      } catch (error) {}
    },
    // 一级菜单下拉
    tabItemCommand(val, item) {
      this.activeIemcTab = val
      this.$nextTick(() => {
        var element = document.querySelector('.is-activeTab')
        element.scrollIntoView({ behavior: 'smooth' })
      })
      const projectInfo = monitorTypeList.find((e) => e.wpfKey === this.activeIemcTab)
      Object.assign(this.roomData, {
        tabName: item.wpfKey,
        projectCode: projectInfo?.projectCode,
        categoryCode: projectInfo?.categoryCode ?? ''
      })
      try {
        window.chrome.webview.hostObjects.sync.bridge.SBMenuBarSwitch(
          val,
          'formH5'
        )
      } catch (error) {}
    },
    // 二级菜单下拉
    roomTypeCommand(value) {
      this.activeType = value
      const index = this.roomTypeList.find((item) => item.Dom == value).index
      const ele = document.querySelector('.box-type')
      if (index > 5) {
        ele.scrollLeft = 56
      } else {
        ele.scrollLeft = 0
      }
    },
    // 变配电监测设备详情
    openDeviceDetail(deviceInfo) {
      Object.assign(this.deviceDetailInfo, {
        ...this.roomData,
        surveyCode: deviceInfo.surveyCode,
        modelCode: deviceInfo.modelCode,
        entityTypeId: deviceInfo.entityTypeId
      })
      this.changeDeviceDetailDialogShow(true)
    },
    // 打开设备弹框
    openDevideDialog(deviceInfo, modelCode = '') {
      // 关闭变配电监测设备详情弹窗
      // if (this.deviceDetailsListShow) {
      //   this.changeDeviceDetailDialogShow(false)
      // }
      // 判断deviceInfo是否有modelCode字段并且有值 如果modelCode不在当前assetsList中，通过modelCode换取对应设备及实体信息
      if ((!deviceInfo || !Object.keys(deviceInfo).length) && modelCode) {
        getSurveyInfoByModelCode({ modelCode }).then((res) => {
          const resData = res.data
          if (resData.code === '200' && resData.data?.length) {
            const firstData = resData.data[0]
            deviceInfo = {
              assetId: firstData.assetsId,
              assetName: firstData.assetsName,
              surveyCode: firstData.surveyCode,
              modelCode: modelCode
            }
            this.setDeviceInfoEvent(deviceInfo)
          }
        })
        // this.$message({
        //   message: '未找到该设备信息',
        //   type: 'warning'
        // })
      } else {
        this.setDeviceInfoEvent(deviceInfo)
      }
    },
    setDeviceInfoEvent(deviceInfo) {
      // 额外打开变配电设备详情弹窗
      if (this.activeIemcTab === 'Electricity') {
        // if (this.activeIemcTab === 'Electricity' && this.activeType === 'move') {
        this.openDeviceDetail(deviceInfo)
      }
      Object.assign(this.deviceInfo, {
        ...this.roomData,
        deviceId: deviceInfo.assetId,
        deviceName: deviceInfo.assetName,
        modelCode: deviceInfo.modelCode,
        detailsNav: deviceInfo.detailsNav
      })
      if (this.collapseFlag) {
        this.collapseFlag = !this.collapseFlag
        this.$nextTick(() => {
          this.$refs.collapseHeight.style.width = '0'
          this.$refs.collapseHeight.style.padding = '0'
        })
      }
      this.deviceInfoManagementShow = true
      // setTimeout(() => {
      //   this.WPFmessage = JSON.stringify(this.deviceInfo)
      //   this.$refs.deviceInfoManagement.mergeData('basicAccount')
      // }, 200)
    },
    showSpaceDetailsDialog() {
      this.spaceDetailsShow = true
      try {
        window.chrome.webview.hostObjects.sync.bridge.ShowDialog(true)
      } catch (error) {}
    },
    hiddenSpaceDetailsDialog() {
      this.spaceDetailsShow = false
      try {
        window.chrome.webview.hostObjects.sync.bridge.ShowDialog(false)
        window.chrome.webview.hostObjects.sync.bridge.RoomInfoClose(
          'CloseRoom'
        )
      } catch (error) {}
    },
    szzlyyChange(type) {
      if (type === 'device') {
        // const deviceInfo = {
        //   assetId: '1842195358575230981',
        //   assetName: '医用磁共振成像系统',
        //   modelCode: '0100101_-2200'
        // }
        const deviceInfo = undefined
        this.openDevideDialog(deviceInfo, '0100101_-2200')
        return
      }
      const projectInfo = monitorTypeList.find(
        (e) => e.wpfKey === this.activeIemcTab
      )
      let paramsData = {}
      if (type === 'floor') {
        paramsData = {
          title: '医技楼 > 1F',
          modelCode: 'SINOMIS0100101',
          localtion: '0100101',
          ssmType: 5,
          isSpace: '0',
          ssmCodes:
            '#,1724753192383705090,1724753192417259521,1724753192815718402,1724753192866050049'
        }
      } else if (type === 'build') {
        // this.activeType = 'move'
        paramsData = {
          title: '医技楼',
          modelCode: 'SINOMIS01001',
          localtion: '01001',
          ssmType: 4,
          isSpace: '0',
          ssmCodes:
            '#,1724753192383705090,1724753192417259521,1724753192815718402',
          childList: []
        }
      } else if (type === 'region') {
        // this.activeType = 'move'
        paramsData = {
          title: '院区',
          modelCode: 'SINOMIS01',
          localtion: '01',
          ssmType: 3,
          isSpace: '0',
          ssmCodes: '#,1724753192383705090,1724753192417259521',
          callBackBridge: 'SBMenuBarSwitch',
          areaData: {
            ssmType: '1',
            parentId: '1724753192383705090',
            ssmName: '主院区',
            childList: [
              '1724753192815718402',
              '1724753193000267778',
              '1724753193138679809',
              '1724753193365172225',
              '1731219360110956545'
            ]
          }
        }
      } else if (type === 'menuZLJF') {
        this.activeType = 'move'
        paramsData = {
          title: '院区',
          modelCode: 'SINOMIS01',
          localtion: '01',
          ssmType: 3,
          ssmCodes: '#,1724753192383705090,1724753192417259521',
          menuCode: 'ca6bf326808146b5b37f745027f04666',
          isSpace: '1',
          modelCoding: ''
        }
      } else if (type === 'menuZLJF2') {
        this.activeType = 'move'
        paramsData = {
          title: '院区',
          modelCode: 'SINOMIS01',
          localtion: '01',
          ssmType: 3,
          ssmCodes: '#,1724753192383705090,1724753192417259521',
          menuCode: '08f2e8c491f448a8a024f9c8b41bf023',
          isSpace: '1',
          modelCoding: ''
        }
      } else if (type === 'menuBPD') {
        this.activeType = 'move'
        paramsData = {
          title: '院区',
          modelCode: 'SINOMIS01',
          localtion: '01',
          ssmType: 3,
          ssmCodes: '#,1724753192383705090,1724753192417259521',
          menuCode: '51604a2ca6e144e3a98d48847949d4d3',
          isSpace: '1',
          modelCoding: ''
        }
      } else if (type === 'menuBPD2') {
        this.activeType = 'move'
        paramsData = {
          title: '院区',
          modelCode: 'SINOMIS01',
          localtion: '01',
          ssmType: 3,
          ssmCodes: '#,1724753192383705090,1724753192417259521',
          menuCode: '1ae5ac9502594038ba6ba61233de8bbd',
          isSpace: '1',
          modelCoding: ''
        }
      } else if (type === 'menuBPDDG') {
        this.activeType = 'move'
        paramsData = {
          title: '院区',
          modelCode: 'SINOMIS01',
          localtion: '01',
          ssmType: 3,
          ssmCodes: '#,1724753192383705090,1724753192417259521',
          menuCode: '379f63418e06b6af29e1de4b023a927b',
          isSpace: '1',
          modelCoding:
            '0100503_-455022,0100503_-454774,0100503_-456780,0100503_-455688,0100503_-453814,0100503_-456568,0100503_-454244'
        }
      } else if (type === 'menuZNZM') {
        this.activeType = 'move'
        paramsData = {
          title: '医技楼',
          modelCode: 'SINOMIS01001',
          localtion: '01001',
          ssmType: 5,
          isSpace: '0',
          ssmCodes:
            '#,1574997196833566721,1574997197349466114,1574997197429157890'
        }
        // this.requestParams.spaceId ='1516002471254773761'
        // this.requestParams.ssmType = 6
        // this.requestParams.projectCode = '00f4ad80e5c04809aae72c7470e8be28'
        // this.spaceDetailsShow = true
      }
      this.getSurveyAssetByProjectCode({ projectCode: projectInfo.projectCode, ...paramsData }).then(
        (res) => {
          Object.assign(this.roomData, {
            modelCoding: '',
            projectCode: projectInfo.projectCode,
            categoryCode: projectInfo?.categoryCode ?? '',
            tabName: this.activeIemcTab,
            ...paramsData,
            deviceId: res
          })
          this.setWPFdeviceIdAndTab()
          this.getInspBeExecutedTaskNumber()
        }
      )
    },
    localTestChange(type) {
      if (type === 'device') {
        const deviceInfo = {
          assetId: '1841511891587825672',
          assetName: '低温液体贮槽（1号）',
          modelCode: '0100503_-456780'
        }
        this.openDevideDialog(deviceInfo)
        return
      }
      const projectInfo = monitorTypeList.find(
        (e) => e.wpfKey === this.activeIemcTab
      )
      let paramsData = {}
      if (type === 'floor') {
        paramsData = {
          title: '综合急诊急救楼 > B3',
          modelCode: 'SINOMIS0100101',
          localtion: '0100101',
          ssmType: 5,
          isSpace: '0',
          ssmCodes:
            '#,1574997196057620481,1574997196330250241,1574997196833566721,1574997196959395842'
        }
      } else if (type === 'build') {
        this.activeType = 'move'
        paramsData = {
          title: '综合急诊急救楼',
          modelCode: 'SINOMIS01001',
          localtion: '01001',
          ssmType: 4,
          isSpace: '0',
          ssmCodes:
            '#,1574997196057620481,1574997196330250241,1574997196833566721'
        }
      } else if (type === 'region') {
        this.activeType = 'move'
        paramsData = {
          title: '院区',
          modelCode: 'SINOMIS01',
          localtion: '01',
          ssmType: 3,
          isSpace: '0',
          ssmCodes: '#,1574997196057620481,1574997196330250241'
        }
      } else if (type === 'menuZLJF') {
        this.activeType = 'move'
        paramsData = {
          title: '院区',
          modelCode: 'SINOMIS01',
          localtion: '01',
          ssmType: 3,
          ssmCodes: '#,1724753192383705090,1724753192417259521',
          menuCode: 'ca6bf326808146b5b37f745027f04666',
          isSpace: '1',
          modelCoding: ''
        }
      } else if (type === 'menuBPD') {
        this.activeType = 'move'
        paramsData = {
          title: '院区',
          modelCode: 'SINOMIS01',
          localtion: '01',
          ssmType: 3,
          ssmCodes: '#,1724753192383705090,1724753192417259521',
          menuCode: '379f63418e06b6af29e1de4b023a927b',
          isSpace: '1',
          modelCoding: ''
        }
      } else if (type === 'menuBPDDG') {
        this.activeType = 'move'
        paramsData = {
          title: '院区',
          modelCode: 'SINOMIS01',
          localtion: '01',
          ssmType: 3,
          ssmCodes: '#,1724753192383705090,1724753192417259521',
          menuCode: '379f63418e06b6af29e1de4b023a927b',
          isSpace: '1',
          modelCoding:
            '0100503_-455022,0100503_-454774,0100503_-456780,0100503_-455688,0100503_-453814,0100503_-456568,0100503_-454244'
        }
      } else if (type === 'menuZNZM') {
        this.activeType = 'move'
        paramsData = {
          title: '医技楼',
          modelCode: 'SINOMIS01001',
          localtion: '01001',
          ssmType: 4,
          isSpace: '0',
          ssmCodes: '#,1574997196833566721,1724753192815718402'
          // ssmCodes: '#,1574997196833566721,1574997197349466114,1574997197429157890,1516002504691765249'
        }
      } else if (type === 'elevatorRegion') {
        paramsData = {
          title: '院区',
          modelCode: 'SINOMIS01',
          localtion: '01',
          ssmType: 3,
          isSpace: '0',
          ssmCodes: '1724753192383705090,1724753192417259521',
          callBackBridge: 'SBMenuBarSwitch',
          areaData: {
            ssmType: '1',
            parentId: '1724753192383705090',
            ssmName: '主院区',
            childList: [
              '1724753192815718402',
              '1724753193000267778',
              '1724753193138679809',
              '1724753193365172225',
              '1731219360110956545'
            ]
          }
        }
      } else if (type === 'elevatorBuild') {
        paramsData = {
          title: '医技楼',
          modelCode: 'SINOMIS01001',
          localtion: '01001',
          ssmType: 4,
          isSpace: '0',
          ssmCodes:
            '1724753192383705090,1724753192417259521,1724753192815718402',
          childList: []
        }
      } else if (type === 'elevatorFloor') {
        paramsData = {
          title: '医技楼 > 1F',
          modelCode: 'SINOMIS0100101',
          localtion: '0100101',
          ssmType: 5,
          isSpace: '0',
          ssmCodes:
            '1724753192383705090,1724753192417259521,1724753192815718402,1724753192866050049'
        }
      }
      this.getSurveyAssetByProjectCode({ projectCode: projectInfo.projectCode, ...paramsData }).then(
        (res) => {
          Object.assign(this.roomData, {
            modelCoding: '',
            projectCode: projectInfo.projectCode,
            categoryCode: projectInfo?.categoryCode ?? '',
            tabName: this.activeIemcTab,
            ...paramsData,
            deviceId: res
          })
          this.setWPFdeviceIdAndTab()
          this.getInspBeExecutedTaskNumber()
        }
      )
    },
    bjsjtyyChange(type) {
      const projectInfo = monitorTypeList.find(
        (e) => e.wpfKey === this.activeIemcTab
      )
      let paramsData = {}
      if (type === 'region') {
        paramsData = {
          title: '院区',
          modelCode: 'BJSJTYY01',
          localtion: '01',
          ssmType: 3,
          isSpace: '0',
          ssmCodes: '#,1574997196057620481,1574997196330250241'
        }
      } else if (type === 'build') {
        paramsData = {
          title: '综合急诊急救楼',
          modelCode: 'BJSJTYY01001',
          localtion: '01001',
          ssmType: 4,
          isSpace: '0',
          ssmCodes: '#,1574997196057620481,1574997196330250241,1574997196833566721'
        }
      } else if (type === 'floor') {
        paramsData = {
          title: '综合急诊急救楼 > 6F',
          modelCode: 'BJSJTYY0100109',
          localtion: '0100109',
          ssmType: 5,
          isSpace: '0',
          ssmCodes: '#,1574997196057620481,1574997196330250241,1574997196833566721,1574997197731147778'
        }
      }
      this.getSurveyAssetByProjectCode({ projectCode: projectInfo.projectCode, ...paramsData }).then(
        (res) => {
          Object.assign(this.roomData, {
            modelCoding: '',
            projectCode: projectInfo.projectCode,
            categoryCode: projectInfo?.categoryCode ?? '',
            tabName: this.activeIemcTab,
            ...paramsData,
            deviceId: res
          })
          this.setWPFdeviceIdAndTab()
          this.getInspBeExecutedTaskNumber()
        }
      )
    }
  }
}
</script>

<style lang="scss" scoped>
.content {
  width: 100%;
  height: 100%;
  position: relative;
  .sham-content {
    pointer-events: none;
  }
  .right-content {
    position: absolute;
    top: 0;
    right: 0;
    width: 24.573%;
    height: 100%;
    background: url("~@/assets/images/qhdsys/bg-mask.png") no-repeat;
    background-size: 100% 100%;
    box-sizing: border-box;
    padding: 0 25px 10px 35px;
    transition: width 0.3s linear;
    overflow: hidden;
    .bg-title {
      padding: 0;
      background: rgba(133, 145, 206, 0.15);
      overflow: hidden;
      color: #dceaff;
      font-family: HarmonyOS Sans SC-Regular, HarmonyOS Sans SC;
      flex-shrink: 0;
      // 不可被选中
      -webkit-user-select: none;
      user-select: none;
      display: flex;
      .bg-tab {
        display: flex;
        overflow: hidden;
        box-sizing: border-box;
        .tab-div {
          width: 88px;
          height: 40px;
          flex-shrink: 0;
          line-height: 40px;
          text-align: center;
          font-size: 16px;
          color: #a4acb9;
          background: url("@/assets/images/qhdsys/bg-tab.png") no-repeat;
        }
        .tab-div:hover {
          cursor: pointer;
        }
        .is-activeTab {
          color: #b0e3fa;
          background: url("@/assets/images/qhdsys/bg-tab-xz.png") no-repeat;
        }
      }
      ::v-deep .el-popover {
        width: fit-content;
        min-width: 0;
        background: #374b79;
      }
      .center-empty {
        flex: 1;
        background: url("@/assets/images/qhdsys/bg-tab.png") no-repeat;
        background-size: 100% 100%;
      }
      .icon-collapse {
        width: 48px;
        height: 40px;
        // flex-shrink: 0;
        display: flex;
        justify-content: center;
        align-items: center;
        background: url("@/assets/images/qhdsys/bg-tab.png") no-repeat;
        &:hover {
          cursor: pointer;
        }
        img {
          margin: auto;
        }
        .el-popper .el-dropdown-menu__item {
          font-size: 16px !important;
        }
      }
    }
    .bg-content {
      position: relative;
      box-sizing: border-box;
      width: 100%;
      height: calc(100% - 40px);
      overflow-y: hidden;
      overflow-x: hidden;
    }
    .room-info-box {
      color: #fff;
      display: flex;
      flex-direction: column;
      .bg-title {
        align-items: center;
      }
      .box-type {
        width: calc(100% - 8px);
        // width: calc(100% - 34px);
        padding-top: 13px;
        display: flex;
        overflow: hidden;
        ::v-deep .el-badge {
          flex-shrink: 0;
          .el-badge__content {
            z-index: 1;
            background: #ff2d55;
            border: none;
          }
        }
        // flex-wrap: nowrap;
        // margin: auto;
        // justify-content: center;
        .tags-item {
          cursor: pointer;
          height: 26px;
          line-height: 26px;
          flex-shrink: 0;
          margin-left: 8px;
          // margin-top: 8px;
          padding: 0px 12px;
          background: rgba(255, 255, 255, 0.1)
            linear-gradient(
              90deg,
              rgba(10, 132, 255, 0) 0%,
              rgba(10, 132, 255, 0) 100%
            );
          font-size: 14px;
          font-weight: 400;
          color: #b0e3fa;
          // line-height: 14px;
          border: 1px solid transparent;
        }
        .type_active {
          background: rgba(255, 255, 255, 0.1)
            linear-gradient(
              90deg,
              rgba(10, 132, 255, 0.4) 0%,
              rgba(10, 132, 255, 0) 100%
            );
          border: 1px solid;
          border-image: radial-gradient(
              circle,
              rgba(171, 240, 255, 1),
              rgba(226, 254, 255, 1),
              rgba(132, 196, 203, 1),
              rgba(48, 151, 166, 0)
            )
            1 1;
        }
      }
      .content-icon-collapse {
        margin: auto;
        margin-top: 13px;
        width: 24px;
        height: 24px;
        flex-shrink: 0;
        display: flex;
        // background: url('@/assets/images/qhdsys/bg-tab.png') no-repeat;
        &:hover {
          cursor: pointer;
        }
        img {
          margin: auto;
        }
      }
      .sys-box {
        width: 100%;
        flex: 1;
        max-height: calc(100% - 39px);
        // max-height: 100%;
        .sys-box-content {
          width: 100%;
          height: 100%;
          padding: 5px 0px 0 0px;
          box-sizing: border-box;
        }
      }
    }
  }
}
.el-popper {
  padding: 0;
  padding: 8px 8px 0;
  background: #374b79 !important;
  border: 0;
  .el-dropdown-menu__item {
    width: 78px;
    height: 24px;
    line-height: 24px;
    text-align: center;
    padding: 0;
    background: rgba(255, 255, 255, 0.1)
      linear-gradient(
        90deg,
        rgba(10, 132, 255, 0) 0%,
        rgba(10, 132, 255, 0) 100%
      );
    margin-bottom: 8px;
    font-family: HarmonyOS Sans SC-Regular, HarmonyOS Sans SC;
    font-weight: 400;
    color: #b0e3fa !important;
  }
  .isBjxl {
    background: url("@/assets/images/qhdsys/bj-xl.png") no-repeat;
  }
  .el-dropdown-menu__item:hover {
    color: #b0e3fa !important;
    background: rgba(255, 255, 255, 0.1)
      linear-gradient(
        90deg,
        rgba(10, 132, 255, 0) 0%,
        rgba(10, 132, 255, 0) 100%
      ) !important;
  }
}

.el-popper.dropdown {
  .el-dropdown-menu__item {
    font-size: 16px;
  }
}
.title-left {
  font-size: 16px;
  color: #a4afc1;
}
.title-left span:nth-child(2) {
  margin-left: 16px;
}
.title-detail {
  font-size: 14px;
  color: #8bddf5;
}
.is-activeTab {
  font-weight: bold;
  color: #ffffff;
}
:deep(.mainDialog) {
  width: 80%;
  height: 84vh;
  margin-top: 6vh !important;
  // border: 1px solid #5996f9;
  background-color: transparent !important;
  pointer-events: auto;
  background-image: url("@/assets/images/table-bg.png");
  background-repeat: no-repeat;
  background-size: 100% 100%;
  box-shadow: none;
  .el-dialog__headerbtn {
    transform: translateX(-36px);
    width: 25px;
    height: 25px;
    background-image: url("@/assets/images/close.png");
    background-repeat: no-repeat;
    background-size: 100% 100%;
    .el-dialog__close::before {
      display: none;
    }
  }
  .dialog-title {
    display: inline-block;
    width: 100%;
    text-align: center;
    transform: translateY(-6px);
    color: #cbdeed;
  }
  .dialog-title::before {
    display: none;
  }

  .el-dialog__header {
    background-color: transparent;
  }
  .el-dialog__body {
    padding: 0;
    height: calc(100% - 60px);
    max-height: fit-content;
    overflow-y: hidden;
    background-color: transparent;
    padding: 0px 80px;
    .dialog-content {
      width: 100%;
      height: 100%;
      background-color: transparent;

      .el-table {
        background-color: transparent;
        border: 1px solid #202d4c;
        .el-table__header-wrapper {
          background-color: transparent !important;
        }
        .el-table__body-wrapper {
          background-color: transparent !important;
        }
        tr {
          background-color: transparent !important;
        }
        td {
          background-color: transparent !important;
        }
      }
    }
  }
}
</style>
