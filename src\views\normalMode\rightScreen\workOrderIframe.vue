<template>
  <div class="main">
    <el-dialog
      v-dialogDrag
      :modal="false"
      custom-class="mainDialog main"
      append-to-body
      :visible.sync="workOrderDealShow"
      :close-on-click-modal="false"
      :before-close="closeDialog"
      class="personDialog"
    >
      <template slot="title">
        <span class="dialog-title">{{ '新建' + workTypeName + '工单' }}</span>
        <div class="right-tips"><span class="v-img"></span>{{ workOrderDetail.taskWorkHint }}</div>
      </template>
      <div class="dialog-content">
        <olgMaintenance @save="getSaveCallback" ref="olgMaintenance" dealType="add" routerFrom="iframe" :workOrderDetail="workOrderDetail" v-if="workOrderDetail.olgTaskManagement.workTypeCode === '1'" />
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button class="sino-button-sure" @click="closeDialog">取 消</el-button>
        <el-button class="sino-button-sure" type="primary" @click="savePeople">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>
<script>
import olgMaintenance from './woemOrderType/olgMaintenance.vue'
import { getWorkOrderToAdd } from '@/utils/peaceRightScreenApi'
export default {
  name: 'workOrderIframe',
  components: {
    olgMaintenance
  },
  data() {
    return {
      workOrderDetail: {},
      workTypeCode: '',
      workTypeName: '',
      workOrderDealShow: true
    }
  },
  watch: {},
  created() {
    const query = this.$route.query
    this.workTypeCode = query.workTypeCode
    this.workTypeName = query.workTypeName
    this.getWorkOrderToAdd()
  },
  mounted() {
    // document.documentElement.style.fontSize = 16 + 'px'
    // window.onresize = function () {
    //   document.documentElement.style.fontSize = 16 + 'px'
    //   console.log(document.documentElement.style)
    // }
    try {
      window.chrome.webview.addEventListener('message', (event) => {})
    } catch (errpr) {}
  },
  methods: {
    getWorkOrderToAdd() {
      const params = {
        type: 1,
        workTypeCode: this.workTypeCode,
        workTypeName: this.getWorkTypeLetter(this.workTypeCode)
      }
      getWorkOrderToAdd(params).then((res) => {
        this.workOrderDetail = res.data
      })
    },
    getWorkTypeLetter(code) {
      switch (code) {
        case '1':
          return 'WX'
      }
    },
    // 取消按钮
    closeDialog() {
      // 向父页面发消息
      window.chrome.webview.hostObjects.sync.bridge.CloseWindow()
    },
    // 确认按钮
    savePeople() {
      this.$refs.olgMaintenance.saveForm()
    },
    getSaveCallback(val) {
      // 传输 工单号
      window.chrome.webview.hostObjects.sync.bridge.GetWorkOrderNo(val)
      this.closeDialog()
    }
  }
}
</script>

<style lang="scss" type="text/css" scoped>
// .main {
::v-deep .mainDialog {
  // width: 1000px;
  // height: 700px;
  // width: 100%;
  // height: 100%;
  // margin-top: 0 !important;
  width: 55%;
  height: 80vh;
  background: #031553;
  // margin: 0 !important;
  border: 1px solid #2f5596;
  .dialog-title {
    display: inline-block;
    width: 38%;
  }
  .right-tips {
    width: 50%;
    background: linear-gradient(90deg, rgba(255, 36, 36, 0) 0%, rgba(255, 43, 43, 0.4) 50%, rgba(255, 49, 49, 0) 100%);
    height: 30px;
    line-height: 30px;
    text-align: center;
    color: #fff;
    display: flex;
    justify-content: center;
    .v-img {
      margin: auto 0;
      margin-right: 10px;
      display: inline-block;
      // margin-top: 5px;
      height: 20px;
      width: 20px;
      background: center url('~@/assets/images/peace/volume.png') no-repeat;
      background-size: 100% 100%;
    }
  }
  .el-dialog__body {
    padding: 10px 20px;
    max-height: calc(100% - 130px);
    height: calc(100% - 130px);
    .dialog-content {
      width: 100%;
      height: 100%;
    }
  }
  .el-dialog__footer {
    padding-right: 30px;
  }
}
::v-deep .el-table-column--selection .cell {
  padding-left: 0.875rem;
  padding-right: 0.875rem;
  text-overflow: initial;
}
// }
</style>
