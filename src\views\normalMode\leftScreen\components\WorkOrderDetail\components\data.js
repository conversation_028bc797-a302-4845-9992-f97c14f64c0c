export default {
  reduce: '',
  actual: [],
  olgTaskManagement: {
    id: '7d7d1c69135e4fb288cac2985ac8797b',
    tableName: 'IOMS_BJSCSYGJ.BJZKYX_',
    hospitalCode: 'BJZKYX',
    createBy: {
      id: '1c98c3a13f32466c9dca68a7d178cb31',
      office: {
        sort: 30,
        hasChildren: false,
        type: '2',
        parentId: '0'
      },
      loginFlag: '1',
      userTypes: '0',
      roleNames: '',
      admin: false
    },
    createDate: '2022-04-15 11:25:24',
    updateBy: {
      id: '1c98c3a13f32466c9dca68a7d178cb31',
      office: {
        sort: 30,
        hasChildren: false,
        type: '2',
        parentId: '0'
      },
      loginFlag: '1',
      userTypes: '0',
      roleNames: '',
      admin: false
    },
    updateDate: '2022-04-15 15:40:24',
    workNum: 'WX22041511-2459853',
    workTypeCode: '1',
    workTypeName: '综合维修',
    workSources: '2',
    urgencyDegree: '2',
    sourcesPhone: '4444',
    sourcesFloor: '',
    sourcesDept: '020daf969ac84662a168c3d199d5c6f0',
    sourcesDeptName: '202会议室',
    callerJobNum: '55555',
    callerName: '444',
    callerCode: '',
    questionDescription: '4444',
    designateDeptCode: '0ba947234d5e42158a7f0155a16aa884',
    designateDeptName: '西安班组',
    flowcode: '3',
    flowtype: '已派工',
    createByName: '赵治军',
    createByJobnum: 'B01',
    createByJob: '',
    callId: '',
    typeSources: '1',
    itemDetailName: '',
    itemTypeName: '五官科耳鼻喉眼口腔设备服务类',
    localtionNames: '南楼G座A15B01',
    count: 0,
    disFlowcode: '2',
    disFlowtype: '已派工',
    designatePersonCode: 'cedd657dda934e7aa96f4b02ab73838e,1c9953af65124e1f907a87e4cb356853,da6116c8ad9b4992bfb3b7c37bf08eb3',
    designatePersonName: '院外工人,院外运维管理员,院外班组组长',
    designatePersonPhone: '13888888886,13888888888,13888888887',
    disMessageFlag: '0',
    disDesignateBy: '1c98c3a13f32466c9dca68a7d178cb31',
    disDesignateByName: '赵治军',
    disDesignateDate: 1650008424000,
    disEntryOrdersReason: '',
    disEntryOrdersSolution: '',
    disEntryOrdersBy: '',
    counter: 0,
    itemDetailCode: '',
    itemTypeCode: 'a8813e2b1d3211e88c26000c299663d7',
    appointmentType: '0',
    needPhone: '',
    designateDeptDate: 1650008424000,
    itemServiceName: '',
    itemServiceCode: '',
    type: '1',
    disEntryOrdersReasonCode: '',
    repairWork: '1',
    taskType: '1',
    localtionCodes: 'c9ec1fdd94a34373aaad00305e2a4b49,2230c9c92b024497a774601e69bce33a,74682e59d3734e3c88efa2a7554431c9,0b1f4372624d4fefb455f76c72a3d5a8',
    createByDeptId: 'aaf7ef22a1be4c3eae73d2806ae99bbd',
    createByDeptName: '财务处办公室',
    responseTime: 0.0,
    entryordersFeedbackFlag: '2'
  },
  completePrice: '1',
  kd_time: '2022-04-15 11:25:24',
  urgencyDegree: '一般',
  handle: '0',
  autoPrintSetting: '1',
  type: '1',
  relevanceWorknumFlag: false,
  typeSourceName: '医务报修',
  customizeTaskConfiguration: {
    overtime: 0
  },
  taskDetail: {
    id: '971c0843a60043c8b05f0e04e00de5a3',
    hospitalCode: 'BJZKYX',
    localtion: 'c9ec1fdd94a34373aaad00305e2a4b49,2230c9c92b024497a774601e69bce33a,74682e59d3734e3c88efa2a7554431c9,0b1f4372624d4fefb455f76c72a3d5a8',
    localtionName: '南楼G座A15B01',
    itemTypeCode: 'a8813e2b1d3211e88c26000c299663d7',
    itemTypeName: '五官科耳鼻喉眼口腔设备服务类',
    itemDetailCode: '',
    itemDetailName: '',
    itemServiceCode: '',
    itemServiceName: '',
    transportNum: '0',
    taskId: '7d7d1c69135e4fb288cac2985ac8797b'
  },
  operSource: 'souye',
  wxDetail: [
    [
      '971c0843a60043c8b05f0e04e00de5a3',
      '南楼G座A15B01',
      '五官科耳鼻喉眼口腔设备服务类',
      '',
      'c9ec1fdd94a34373aaad00305e2a4b49,2230c9c92b024497a774601e69bce33a,74682e59d3734e3c88efa2a7554431c9,0b1f4372624d4fefb455f76c72a3d5a8',
      '',
      'a8813e2b1d3211e88c26000c299663d7',
      '',
      '',
      '五官科耳鼻喉眼口腔设备服务类'
    ]
  ],
  taskRecord: [
    {
      id: '5761ff37273441bab918f50cd9d79854',
      createBy: {
        id: '1c98c3a13f32466c9dca68a7d178cb31',
        office: {
          sort: 30,
          hasChildren: false,
          type: '2',
          parentId: '0'
        },
        no: 'B01',
        name: '赵治军',
        loginFlag: '1',
        userTypes: '0',
        roleNames: '',
        admin: false
      },
      createDate: '2022-04-15 11:25:24',
      taskId: '7d7d1c69135e4fb288cac2985ac8797b',
      operationType: '创建工单',
      operationCode: '1',
      callbackVoiceUrl: ''
    },
    {
      id: '49dd6573e417469498ca8e81920c0c26',
      createBy: {
        id: '1c98c3a13f32466c9dca68a7d178cb31',
        office: {
          sort: 30,
          hasChildren: false,
          type: '2',
          parentId: '0'
        },
        no: 'B01',
        name: '赵治军',
        loginFlag: '1',
        userTypes: '0',
        roleNames: '',
        admin: false
      },
      createDate: '2022-04-15 11:25:24',
      taskId: '7d7d1c69135e4fb288cac2985ac8797b',
      operationType: '已受理',
      operationCode: '2',
      designateDeptCode: '82e22f2849b649e5b44a0e59fa6bfa92',
      designateDeptName: '综合维修',
      callbackVoiceUrl: ''
    },
    {
      id: '0eebf0809cd3458d905198fbcf7d3b01',
      createBy: {
        id: '1c98c3a13f32466c9dca68a7d178cb31',
        office: {
          sort: 30,
          hasChildren: false,
          type: '2',
          parentId: '0'
        },
        no: 'B01',
        name: '赵治军',
        loginFlag: '1',
        userTypes: '0',
        roleNames: '',
        admin: false
      },
      createDate: '2022-04-15 11:25:24',
      taskId: '7d7d1c69135e4fb288cac2985ac8797b',
      operationType: '已派工',
      operationCode: '3',
      designateDeptCode: '82e22f2849b649e5b44a0e59fa6bfa92',
      designateDeptName: '综合维修',
      designatePersonCode: 'dab5ed4a775446c8aefa1375b8f2c7ad',
      designatePersonName: '登高',
      designatePersonPhone: '17895632584',
      callbackVoiceUrl: ''
    },
    {
      id: '3d6ea2a7594042cfb77a313ecc838ce3',
      createBy: {
        id: '1c98c3a13f32466c9dca68a7d178cb31',
        office: {
          sort: 30,
          hasChildren: false,
          type: '2',
          parentId: '0'
        },
        loginFlag: '1',
        userTypes: '0',
        roleNames: '',
        admin: false
      },
      createDate: '2022-04-15 15:40:24',
      taskId: '7d7d1c69135e4fb288cac2985ac8797b',
      operationType: '已转派',
      operationCode: '11',
      feedbackExplain: '',
      designateDeptCode: '0ba947234d5e42158a7f0155a16aa884',
      designateDeptName: '西安班组',
      designatePersonCode: 'cedd657dda934e7aa96f4b02ab73838e,1c9953af65124e1f907a87e4cb356853,da6116c8ad9b4992bfb3b7c37bf08eb3',
      designatePersonName: '院外工人,院外运维管理员,院外班组组长',
      designatePersonPhone: '13888888886,13888888888,13888888887',
      callbackVoiceUrl: ''
    }
  ],
  workTypeCodeLength: 1,
  isItem: 'Y',
  complete: '',
  workSourceName: '中心申报',
  disAttachmentUrlList: [{}],
  taskMalfunctionList: []
}
