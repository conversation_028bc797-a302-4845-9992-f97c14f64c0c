<template>
  <div ref="topology" class="scadaManagement preview">
    <!-- <topology v-if="topologyResize" preview="true" :data="scadaData" @mousewheel.prevent /> -->
    <meta2d-vue-preview v-if="JSON.stringify(scadaData) != '{}'" ref="meta2dVuePreview" :onlyPreview="true" :queryData="scadaData" :userInfo="userInfo" :baseUrl="baseUrl" />
    <div class="menus-list" v-if="menuCascaderShow">
      <el-tree
        ref="teamTree"
        class="team-tree"
        :data="menuOptions"
        :props="defaultProps"
        node-key="id"
        :highlight-current="true"
        :default-expanded-keys="expandedTeam"
        @node-click="handleTeamClick"
      ></el-tree>
    </div>
    <!-- <div class="tools">
      <a-button type="primary" @click="onSizeWindow">
        <a-icon type="border" />
        适合窗口
      </a-button>
    </div> -->
  </div>
</template>

<script>
// import Vue from 'vue'
// import meta2d from 'meta2d-vue/meta2d-vue.es.js'
// import 'meta2d-vue/style.css'
// Vue.use(meta2d)
import { getScadaList, getEntityMenuList } from '@/utils/peaceLeftScreenApi'
export default {
  name: 'scadaManagement',
  props: {
    projectCode: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      scadaTimer: null,
      scadaData: {},
      baseUrl: __PATH.VUE_APP_IEMC_API,
      userInfo: {
        avatarUrl: '',
        captcha: '',
        createdAt: '',
        deletedAt: '',
        email: '',
        id: '1',
        phone: '',
        updatedAt: '',
        username: 'admin',
        vip: 1,
        vipExpiry: '',
        isVip: true // 该属性是计算出来的，不是数据库中的
      },
      // locked: 0,
      // showTools: false,
      // topologyResize: false,
      menuCode: '',
      menuCascaderShow: false,
      menuOptions: [],
      expandedTeam: [],
      defaultProps: {
        children: 'children',
        label: 'name',
        value: 'code'
      }
    }
  },
  destroyed() {
    this.$nextTick(() => {
      // document.getElementsByClassName('el-main')[0].style.padding = '0.375rem 1.5rem 1rem 1.5rem'
    })
  },
  created() {
    // 网关需要token 组件获取token通过localStorage
    window.localStorage.setItem('token', 'Bearer ' + this.$store.state.loginInfo.token)
  },
  mounted() {
    // document.getElementsByClassName('el-main')[0].style.padding = '0rem'
    this.getEntityMenuList()
  },
  methods: {
    // 根据模块获取系统列表
    getEntityMenuList() {
      getEntityMenuList({
        projectId: this.projectCode,
        // userId: 2,
        // userName: 'admin'
      }).then((res) => {
        const data = res.data
        if (data.code === '200') {
          if (data.data.length) {
            // 有一条数据 不展示级联
            if (data.data.length > 1) {
              this.menuCascaderShow = true
              this.menuOptions = this.$tools.transData(data.data, 'code', 'parentId', 'children')
              this.menuCode = this.menuOptions[0].code
              this.$nextTick(() => {
                this.$refs.teamTree.setCurrentKey(this.menuOptions[0].id)
              })
            } else {
              this.menuCascaderShow = false
              this.menuOptions = []
              this.menuCode = data.data[0].code
            }
            this.getScadaList()
          } else {
            this.$message.error('暂无关联系统！')
          }
        } else {
          this.$message.error(data.message)
        }
      })
    },
    // 实时监测--scala
    getScadaList() {
      getScadaList({
        projectId: this.projectCode,
        menuCode: this.menuCode
      }).then((res) => {
        const data = res.data
        if (data.data.length > 0) {
          this.scadaData = {
            r: Date.now() + '',
            id: data.data[0]?.isiId
          }
        } else {
          this.scadaData = {}
          this.$message.warning('暂无关联SCADA图纸！')
        }
      })
    },
    // 树状图点击
    handleTeamClick(data) {
      this.menuCode = data.code
      this.$refs.teamTree.setCurrentKey(data.id)
      this.scadaData = {}
      this.getScadaList()
    }
  }
}
</script>
<!-- // 引入css文件 -->
<style lang="scss" scoped>
.scadaManagement {
  height: 100%;
  width: 100%;
  position: relative;
  color: #fff;
  // 隐藏滚动条
  ::-webkit-scrollbar {
    display: none;
  }
  ::v-deep .menus-list {
    width: 200px;
    position: absolute;
    right: 20px;
    top: 20px;
    z-index: 999;
    height: 90vh;
    overflow-y: auto;
    .team-tree {
      background: center !important;
      .el-tree-node{
        .el-tree-node__content:hover {
          background: rgba(255,202,100,0.12) !important;
          .el-tree-node__label {
            color: #FFCA64;
          }
          .el-tree-node__expand-icon  {
            color: #FFCA64;
          }
        }
        .el-tree-node__content {
          height: 36px;
        }
        .el-tree-node__label {
          font-size: 14px;
          color: #B0E3FA;
          font-weight: 400;
          line-height: 16px;
        }
        .is-leaf {
          color: transparent !important;
        }
        .el-tree-node__expand-icon  {
          color: #B0E3FA;
        }
      }
      .is-current >.el-tree-node__content {
          background: rgba(255,202,100,0.12);
          .el-tree-node__expand-icon  {
            color: #FFCA64;
          }
          .el-tree-node__label {
            color: #FFCA64;
          }
        }
      }
  }
}
</style>
