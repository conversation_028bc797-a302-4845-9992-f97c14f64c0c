<template>
  <div class="riskComponent">
    <div class="module-container" style="height: calc(30%)">
      <div class="module-header">
        <div class="title-left title-left-operation">
          <p class="title-left-text">风险等级统计</p>
        </div>
      </div>
      <div class="module-content chartContent">
        <div id="pie_rankStatistics" style="width: 100%; height: 100%"></div>
      </div>
    </div>
    <div class="module-container" style="height: calc(40%)">
      <div class="module-header">
        <div class="title-left">
          <p class="title-left-text">风险点清单</p>
        </div>
        <div class="title-right">
          <div class="icon-box">
            <img src="../../assets/images/filter.png" class="filter" @click="showCheckGroup = !showCheckGroup" />
            <div v-show="showCheckGroup" v-scrollbarHover class="panel-s">
              <el-checkbox-group v-model="checkTypeList" fill="#52FFFC" @change="checkBoxChanged">
                <el-checkbox v-for="item in typeList" :key="item.value" :label="item.label"></el-checkbox>
              </el-checkbox-group>
            </div>
          </div>
          <div>
            <img src="../../assets/images/order_more.png" class="order-more" @click="allTableChange" />
          </div>
        </div>
      </div>
      <div class="module-content" style="height: calc(100% - 44px)">
        <el-table v-el-table-infinite-scroll="tableLoadMore" v-loading="tableLoading" class="table-center-transfer"
                  :data="tableData"
                  height="100%"
                  :cell-style="{ padding: ' 8px', backgroundColor: 'transparent', border: 'none', padding: '3px' }"
                  :header-cell-style="{ background: '#8591CE26!important', color: '#8BDDF5FF', padding: '4px 8px', fontWeight: 'bold' }"
                  style="width: 100%"
                  element-loading-background="rgba(0, 0, 0, 0.2)"
                  @row-dblclick="toView"
        >
          <el-table-column prop="riskNname" label="风险点名称" show-overflow-tooltip></el-table-column>
          <el-table-column prop="riskLevel" label="风险等级" width="90" show-overflow-tooltip></el-table-column>
          <el-table-column prop="dept" label="责任部门" width="90" show-overflow-tooltip></el-table-column>
          <el-table-column prop="person" label="责任人" width="70" show-overflow-tooltip></el-table-column>
        </el-table>
      </div>
    </div>
    <div class="module-container" style="height: calc(30%)">
      <div class="module-header">
        <div class="title-left title-left-operation">
          <p class="title-left-text">风险告知卡</p>
        </div>
        <div class="title-right">
          <img src="../../assets/images/order_more.png" class="order-more" @click="allTableChange" />
        </div>
      </div>
      <div class="module-content" style="height: calc(100% - 44px)">
        <div class="imageBox">
          <img :src="riskImg" @click="viewImage">
        </div>
        <el-image-viewer v-if="showViewer" :on-close="() => showViewer = false" :url-list="[riskImg]" />
      </div>
    </div>
    <riskInspection ref="riskInspection" :rowData="rowData"></riskInspection>
  </div>
</template>
<script >
import ElImageViewer from 'element-ui/packages/image/src/image-viewer'
import riskImg from '../../assets/images/qhdsys/riskImg.jpg'
import * as echarts from 'echarts'
import riskInspection from '../spaceManage/sysTypeComponent/components/riskInspection.vue'
export default {
  name: 'riskComponent',
  components: {
    ElImageViewer,
    riskInspection
  },
  props: {
    roomData: {
      type: Object,
      default: () => { }
    }
  },
  data() {
    return {
      riskImg,
      iconPathList: [], // 图片列表
      showViewer: false, // 图片预览
      showCheckGroup: false,
      sortType: '',
      ssmCodes: [],
      tableLoading: false,
      tableData: [
        {
          id: '2c91512e9391b5e00193ceefc9330290',
          riskNname: '医技楼2楼危化品室',
          riskLevel: '较大风险',
          dept: '科级小组',
          person: '王朋刚'
        },
        {
          id: '2c91512e9391b5e00193ceefc9330290',
          riskNname: '门急诊二楼危化品室',
          riskLevel: '较大风险',
          dept: '科级小组',
          person: '王朋刚'
        },
        {
          id: '2c91512e9391b5e00193ceefc9330290',
          riskNname: '中心实验室',
          riskLevel: '较大风险',
          dept: '科级小组',
          person: '王朋刚'
        },
        {
          id: '2c91512e9391b5e00193ceefc9330290',
          riskNname: '门急诊一楼危化品室',
          riskLevel: '较大风险',
          dept: '科级小组',
          person: '王朋刚'
        }
      ],
      pagination: {
        total: 0,
        pageSize: 15,
        pageNo: 1
      },
      checkTypeList: ['全部'],
      typeList: [
        {
          label: '全部',
          value: ''
        }
      ],
      rowData: {}

    }
  },
  watch: {
    // 监听模型层级数据变化
    roomData: {
      handler(newVal, oldVal) {
        this.initData()
      },
      deep: true
    },
    showViewer(val) {
      try {
        window.chrome.webview.hostObjects.sync.bridge.ShowDialog(val)
      } catch (error) {}
    }
  },
  mounted() {
    this.sortType = this.roomData.ssmType < 4 ? 'jz' : this.roomData.ssmType == 4 ? 'lc' : 'mk'
    // 初始化调用
    this.initData()
  },
  methods: {
    // 风险点详情
    toView(row) {
      this.rowData = row
      this.$refs.riskInspection.getRiskDetail(row.id)
      this.$refs.riskInspection.hiddenDangerDetailsListShow = true
      try {
        window.chrome.webview.hostObjects.sync.bridge.GetSwichFloorIsShow(true)
        window.chrome.webview.hostObjects.sync.bridge.ShowDialog(true)
      } catch (error) {}
    },
    // 查看图片
    viewImage(row) {
      this.showViewer = true
    },
    // 状态筛选过滤
    checkBoxChanged() {
      this.checkTypeList = [this.checkTypeList[this.checkTypeList.length - 1]]
      this.showCheckGroup = false
      const codeList = []
      this.typeList.forEach((item) => {
        if (this.checkTypeList[0] == item.label) {
          codeList.push(item.value)
        }
      })
      // this.pagination.pageNo = 1
    },
    tableLoadMore() {
      if (this.pagination.total > this.pagination.pageNo * this.pagination.pageSize) {
        this.pagination.pageNo++

      }
    },
    initData() {
      this.sortType = this.roomData.ssmType < 4 ? 'jz' : this.roomData.ssmType == 4 ? 'lc' : 'mk'
      this.ssmCodes = this.roomData.ssmCodes?.split(',') || []
      this.initClosePie()
    },
    /** 初始化闭合饼图 */
    initClosePie() {
      var chartDom = document.getElementById('pie_rankStatistics')
      var myChart = echarts.init(chartDom)
      var option
      let chartData = [
        { value: 0, name: '重点风险', percentage: '0%' },
        { value: 4, name: '较大风险', percentage: '100%' },
        { value: 0, name: '一般风险', percentage: '0%' },
        { value: 0, name: '   低风险', percentage: '0%' }
      ]
      const xdata = Array.from(chartData, ({ name }) => name)
      option = {
        tooltip: {
          trigger: 'item'
        },
        legend: {
          // 取消legend和echarts的联动
          selectedMode: false,
          orient: 'vartical',
          x: 'left',
          top: 'center',
          left: '50%',
          bottom: '0%',
          data: xdata,
          itemWidth: 12,
          itemHeight: 8,
          itemGap: 16,
          textStyle: {
            color: '#B3C2DD' //  字体颜色
          },
          formatter: function (name) {
            var oa = chartData
            for (var i = 0; i < chartData.length; i++) {
              if (name === oa[i].name) {
                // '{name|' +name + '  ' + oa[i].value.percentage + '%' + '}\n{value|' + params.data.value + '}'
                return ' ' + name + '    ' + oa[i].value + '    ' + oa[i].percentage
              }
            }
          }
        },
        series: [
          {
            name: '',
            type: 'pie',
            radius: '70%',
            center: ['25%', '50%'],
            data: chartData,
            label: {
              show: false
            },
            // 添加3D效果
            itemStyle: {
              shadowBlur: 30,
              shadowOffsetX: 5,
              shadowOffsetY: 5,
              shadowColor: 'rgba(0, 0, 0, 0.5)'
            },
            emphasis: {
              itemStyle: {
                shadowBlur: 40,
                shadowOffsetX: 10,
                shadowOffsetY: 10,
                shadowColor: 'rgba(0, 0, 0, 0.5)'
              }
            },
            // 设置3D效果的配置
            roseType: 'angle', // 设定为饼图
            animationType: 'scale', // 使用缩放动画
            animationEasing: 'elastic', // 采用弹性动画
            emphasis: {
              label: {
                show: false
              }
            }
          }
        ]
      }
      myChart.resize()
      myChart.clear()
      option && myChart.setOption(option)
      window.addEventListener('resize', () => {
        myChart.resize()
      })
    },
    allTableChange() {

    }
  }
}
</script>

  <style lang="scss" scoped>
@import "./style/module.scss";
.riskComponent {
  width: 100%;
  height: 100%;
  .el-dropdown-link {
    color: #fff !important;
    margin-left: 10px;
  }
  .title-left {
    padding-left: 20px;
  }
  .chartContent {
    padding: 16px 0;
    height: calc(100% - 44px);
    display: flex;
    width: 100%;
  }
  .imageBox {
    height: 100%;
    width: 100%;
    text-align: center;
    padding: 10px 0px 0px 0px;
    img {
      height: 100%;
      cursor: pointer;
    }
  }
}
.module-header {
  width: 100%;
  // padding-left: 50px;
  background: url("~@/assets/images/qhdsys/bg-bt.png") no-repeat;
}
.order-more {
  width: 24px;
  height: 24px;
  margin-right: 0 !important;
  margin-left: 8px;
}
.filter {
  width: 24px;
  height: 24px;
}
.icon-box {
  margin-right: 0;
  display: flex;
  align-items: center;
  position: relative;
  .panel-s {
    position: absolute !important;
    right: 0 !important;
    top: 28px !important;
    background-color: #374b79 !important;
    z-index: 9 !important;
    min-height: 20px !important;
    overflow: auto;
    .el-checkbox-group {
      display: flex !important;
      flex-direction: column;
      align-items: flex-end;
      padding: 8px;
      margin-right: 0 !important;
      .el-checkbox {
        width: 100%;
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-right: 0;
        & + .el-checkbox {
          margin-top: 5px;
        }
        .el-checkbox__label {
          color: #a3a9c0 !important;
        }
      }
    }
  }
}
::v-deep .el-checkbox__input.is-checked span.el-checkbox__inner {
  background: url("@/assets/images/<EMAIL>") !important;
  background-size: 100% 100% !important;
}
::v-deep span.el-checkbox__inner {
  border-color: #c5fff9 !important;
}
::v-deep .el-checkbox__input.is-checked + .el-checkbox__label {
  color: #8bddf5;
}
</style>
