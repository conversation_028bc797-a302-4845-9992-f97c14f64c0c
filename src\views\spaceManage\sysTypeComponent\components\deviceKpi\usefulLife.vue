<template>
  <div class="usefulLife">
    <div id="usefulLife_chart"></div>
  </div>
</template>

<script>
import * as echarts from 'echarts'
import { mockUsefulLifeData } from './mockData.js'
export default {
  name: 'usefulLife',
  data() {
    return {
      usefulLifeData: {
        usedLife: 0,
        usefulLife: 0
      }
    }
  },
  computed: {

  },
  created() {

  },
  mounted() {
    if (__PATH.VUE_APP_HOSPITAL_NODE === 'outernet') {
      this.usefulLifeData = mockUsefulLifeData
    }
    this.$nextTick(() => {
      this.setChart()
    })
  },
  methods: {
    setChart() {
      const getchart = echarts.init(document.getElementById('usefulLife_chart'))
      var colorList = ['#3CC1FF', '#61E29D']
      const option = {
        title: {
          text: '可用年限',
          textStyle: {
            fontSize: 16,
            fontWeight: 'normal',
            color: '#fff'
          },
          x: 20,
          top: 20
        },
        tooltip: {
          trigger: 'item'
        },
        legend: {
          data: ['已用年限', '可用年限'],
          align: 'left',
          top: 70,
          textStyle: {
            color: '#fff'
          },
          itemWidth: 10,
          itemHeight: 10,
          itemGap: 35
        },
        series: [
          {
            type: 'pie',
            center: ['50%', '60%'],
            radius: ['38%', '50%'],
            clockwise: true,
            avoidLabelOverlap: true,
            hoverOffset: 15,
            itemStyle: {
              normal: {
                color: function (params) {
                  return colorList[params.dataIndex]
                }
              }
            },
            label: {
              show: true,
              position: 'outside',
              formatter: '{a|{d}%}',
              rich: {
                a: {
                  color: '#fff',
                  fontSize: 14
                }
              }
            },
            labelLine: {
              normal: {
                length: 20,
                length2: 30,
                lineStyle: {
                  width: 1
                }
              }
            },
            data: [
              {
                name: '已用年限',
                value: this.usefulLifeData.usedLife
              },
              {
                name: '可用年限',
                value: this.usefulLifeData.usefulLife
              }
            ]
          }
        ]
      }
      getchart.clear()
      getchart.setOption(option)
      // 随着屏幕大小调节图表
      window.addEventListener('resize', () => {
        getchart.resize()
      })
    }
  }
}

</script>

<style lang="scss" scoped>
.usefulLife {
  width: 100%;
  height: 100%;
  background: rgba(53, 98, 219, 0.06);
  #usefulLife_chart {
    width: 100%;
    height: 100%;
  }
}
</style>
