<template>
  <div class="sharedAllocationComponent">
    <div class="module-container" style="height: calc(33%)">
      <div class="module-header">
        <div class="title-left title-left-operation">
          <p class="title-left-text">设备调配统计
            <el-dropdown trigger="click" @command="(val) => (tagCurrent = val)">
              <span class="el-dropdown-link"> 全部科室 <i class="el-icon-arrow-down"></i> </span>
              <el-dropdown-menu slot="dropdown">
                <el-dropdown-item :command="'1'" :class="{ isBjxl: tagCurrent == '1' }">全部科室</el-dropdown-item>
              </el-dropdown-menu>
            </el-dropdown>
          </p>
        </div>
        <div class="title-right">
          <el-dropdown trigger="click" @command="(e) => handleDeployCommand(e)">
            <span class="el-dropdown-link"> {{ deployDateTypeName }} <i class="el-icon-arrow-down"></i> </span>
            <el-dropdown-menu slot="dropdown">
              <el-dropdown-item v-for="(item, index) in dateList" :key="index" :command="item.val"
                :class="{ isBjxl: dateType == item.val }">{{ item.name }}</el-dropdown-item>
            </el-dropdown-menu>
          </el-dropdown>
          <div class="icon-box">
            <img src="../../../assets/images/order_more.png" class="order-more" @click="allTableChange" />
          </div>
        </div>
      </div>
      <div class="module-content chartContent">
        <div class="charts-box">
          <div class="charts-box-top">
            <div id="pie_loanable" style="width: 100%; height: 100%"></div>
          </div>
          <div class="charts-box-bottom">
            <span>可借总数</span>
            <span>1,876</span>
          </div>
        </div>
        <div class="charts-box">
          <div class="charts-box-top">
            <div id="pie_lend" style="width: 100%; height: 100%"></div>
          </div>
          <div class="charts-box-bottom">
            <span>借出总数</span>
            <span>1220</span>
          </div>
        </div>
      </div>
    </div>
    <div class="module-container" style="height: calc(37%)">
      <div class="module-header">
        <div class="title-left">
          <p class="title-left-text">调配汇总分析</p>
        </div>
        <div class="title-right">
          <div class="icon-box">
            <img src="../../../assets/images/order_more.png" class="order-more" @click="allTableChange" />
          </div>
        </div>
      </div>
      <div class="module-content" style="height: calc(100% - 48px)">
        <div class="statistics_top_new">
          <div v-for="(item, index) in statisticsData" :key="index" class="statistics_top_item">
            <img :src="item.img" alt="" />
            <div class="statistics_top_content">
              <p class="statistics_top_title">{{ item.name }}</p>
              <div class="font_bg">
                <p class="color_font" :style="{ color: item.key == 'abnormalDeviceTotal' ? '#FF2D55' : '' }">
                  {{ leaseAccountStatistics[item.key] || 0 }}</p>
                <p class="color_unit">{{ item?.unit ?? '' }}</p>
              </div>
              <p class="item-rate">
                <span>环比上月</span>
                <img :src="upImg" alt="">
                <span class="rate">12.3%</span>
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="module-container" style="height: calc(30%)">
      <div class="module-header">
        <div class="title-left">
          <p class="title-left-text" style="cursor: pointer" :style="{ color: isLease == true ? '#a6afbf' : '#fff' }"
            @click="changeLeaseShow(0)">租赁次数</p>
          <p class="title-left-text" style="cursor: pointer; padding-left: 16px"
            :style="{ color: isLease == true ? '#fff' : '#a6afbf' }" @click="changeLeaseShow(1)">
            租赁金额
          </p>
        </div>
        <div class="title-right">
          <el-dropdown trigger="click" @command="(e) => handleCommand(e)">
            <span class="el-dropdown-link"> {{ dateTypeName }} <i class="el-icon-arrow-down"></i> </span>
            <el-dropdown-menu slot="dropdown">
              <el-dropdown-item v-for="(item, index) in dateList" :key="index" :command="item.val"
                :class="{ isBjxl: dateType == item.val }">{{ item.name }}</el-dropdown-item>
            </el-dropdown-menu>
          </el-dropdown>
          <div class="icon-box">
            <img src="../../../assets/images/order_more.png" class="order-more" @click="allTableChange" />
          </div>
        </div>
      </div>
      <div class="module-content" style="height: calc(100% - 20px)">
        <div id="adverseEventEchart" style="height: 100%; overflow: hidden"></div>
      </div>
    </div>
    <!-- 更多 -->
    <dialogFrame :visible="visible" :breadcrumb="breadcrumb" :title="title" @back="back"
      @update:visible="closeDialogFrame">
      <component :is="activeComponent" @openDetailComponent="openDetailComponent"></component>
    </dialogFrame>
  </div>
</template>

<script >
import * as echarts from 'echarts'
import analyzeTimeImg from '@/assets/images/medicalManagement/analyzeTime.png'
import analyzeMoneyiImg from '@/assets/images/medicalManagement/analyzeMoney.png'
import analyzeTotalImg from '@/assets/images/qhdsys/statistics-new.png'
import upImg from '@/assets/images/qhdsys/up.png'
import dayjs from 'dayjs'
export default {
  name: 'sharedAllocationComponent',
  props: {
    roomData: {
      type: Object,
      default: () => { }
    }
  },
  components: {
    dialogFrame: () => import("@/components/common/DialogFrame"),
    leaseList: () => import("@/views/medicalManagement/components/deploy/leaseList.vue"),
    deployStatistics: () => import("@/views/medicalManagement/components/deploy/deployStatistics.vue"),
    deployAssetsList: () => import("@/views/medicalManagement/components/deploy/deployAssetsList.vue"),
    leaseDetail: () => import("@/views/medicalManagement/components/deploy/leaseDetail.vue"),
  },
  watch: {
    // 监听模型层级数据变化
    roomData: {
      handler(newVal, oldVal) {
        this.initData()
      },
      deep: true
    }
  },
  data() {
    return {
      upImg,
      analyzeTimeImg,
      analyzeMoneyiImg,
      analyzeTotalImg,
      sortType: '',
      ssmCodes: [],
      tagCurrent: '',
      dialogFrame: {
        show: false,  // 全部数据
        title: '' // 目标tab
      },
      isLease: false,
      leaseAccountStatistics: {
        total: '18,643',
        totalTime: '1,458',
        totalMoney: '186',
        deptMoney: '960.75',
        averageTime: '186',
        averageMoney: '9,430.90',
      },//租赁数据
      statisticsData: [
        {
          name: '租赁总数',
          key: 'total',
          unit: '次',
          img: analyzeTotalImg
        },
        {
          name: '租赁总时长',
          key: 'totalTime',
          unit: '小时',
          img: analyzeTimeImg
        },
        {
          name: '租赁总额',
          key: 'totalMoney',
          unit: '万元',
          img: analyzeMoneyiImg
        },
        {
          name: '科室结算总金额',
          key: 'deptMoney',
          unit: '万元',
          img: analyzeMoneyiImg
        },
        {
          name: '平均租赁时长',
          key: 'averageTime',
          unit: '小时',
          img: analyzeTimeImg
        },
        {
          name: '平均租赁金额',
          key: 'averageMoney',
          unit: '元',
          img: analyzeMoneyiImg
        }
      ], // 统计数据
      pieIdData: [
        "pie_loanable",
        "pie_lend",
      ],
      deployDateType: 'month',
      deployDateTypeName: '本月',
      dateTypeName: '本月',
      dateType: 'month',
      dateList: [
        { name: '今日', val: 'day', startTime: dayjs().format('YYYY-MM-DD'), endTime: dayjs().format('YYYY-MM-DD') },
        { name: '本周', val: 'week', startTime: dayjs().startOf('week').format('YYYY-MM-DD'), endTime: dayjs().endOf('week').format('YYYY-MM-DD') },
        { name: '本月', val: 'month', startTime: dayjs().startOf('month').format('YYYY-MM-DD'), endTime: dayjs().endOf('month').format('YYYY-MM-DD') },
        { name: '本年', val: 'year', startTime: dayjs().startOf('year').format('YYYY-MM-DD'), endTime: dayjs().endOf('year').format('YYYY-MM-DD') },
        { name: '自定义', val: 'custom', startTime: '', endTime: '' }
      ],
      visible: false,
      breadcrumb: [{ label: "共享调配", name: "deployStatistics" }],
      activeComponent: "deployStatistics",
      title: "共享调配",
    }
  },
  mounted() {
    this.sortType = this.roomData.ssmType < 4 ? 'jz' : this.roomData.ssmType == 4 ? 'lc' : 'mk'
    // 初始化调用
    this.initData()
  },
  methods: {
    // 时间切换
    handleCommand(val) {
      const obj = this.dateList.find((el) => el.val === val)
      this.dateType = val
      this.dateTypeName = obj.name
    },
    //时间切换
    handleDeployCommand(val) {
      const obj = this.dateList.find((el) => el.val === val)
      this.deployDateType = val
      this.deployDateTypeName = obj.name
    },
    openDetailComponent(params) {
      this.activeComponent = params;
      switch (params) {
        case 'deployAssetsList':
          this.breadcrumb.push({ label: "调配资产", name: "deployAssetsList " });
          this.title = '调配资产'
          break;
        case 'leaseList':
          this.breadcrumb.push({ label: "租赁列表", name: "leaseList" });
          this.title = '租赁列表'
          break;
        case 'leaseDetail':
          this.breadcrumb.push({ label: "租赁信息", name: "leaseDetail" });
          this.title = '租赁信息'
          break;
        default:
          break;
      }
    },
    closeDialogFrame() {
      this.visible = false;
      this.activeComponent = "deployStatistics"
      this.title = "共享调配"
      this.breadcrumb = [{ label: "共享调配", name: "deployStatistics" }]
    },
    /** 组件返回上一级 */
    back(name) {
      this.activeComponent = name;
      let arr = [];
      for (let i = 0; i < this.breadcrumb.length - 1; i++) {
        arr.push(this.breadcrumb[i]);
        if (this.breadcrumb[i].name == name) {
          break;
        }
      }
      this.breadcrumb = arr;
      if (this.breadcrumb.length) {
        this.title = this.breadcrumb.find((item) => item.name === name).label;
      }
    },
    initData() {
      this.sortType = this.roomData.ssmType < 4 ? 'jz' : this.roomData.ssmType == 4 ? 'lc' : 'mk'
      this.ssmCodes = this.roomData.ssmCodes?.split(',') || []
      this.getLeaseData()
      this.pieIdData.forEach((item) => {
        this.initClosePie(item);
      });
    },
    /** 初始化闭合饼图 */
    initClosePie(item) {
      var chartDom = document.getElementById(item);
      var myChart = echarts.init(chartDom);
      var option;
      let dataObj = {
        pie_loanable: [
          { value: 423, name: "设备类型1", percentage: '40' },
          { value: 200, name: "设备类型2", percentage: '20' },
          { value: 200, name: "设备类型3", percentage: '20%' },
        ],
        pie_lend: [
          { value: 423, name: "设备类型1", percentage: '40' },
          { value: 200, name: "设备类型2", percentage: '20' },
          { value: 200, name: "设备类型3", percentage: '20' },
        ],
      };
      const xdata = Array.from(dataObj[item], ({ name }) => name)
      option = {
        tooltip: {
          trigger: "item",
        },
        legend: {
          // 取消legend和echarts的联动
          selectedMode: false,
          icon: 'circle',
          orient: 'vartical',
          x: 'left',
          top: 'center',
          left: '50%',
          bottom: '0%',
          data: xdata,
          itemWidth: 8,
          itemHeight: 8,
          itemGap: 16,
          textStyle: {
            color: '#B3C2DD' //  字体颜色
          },
          formatter: function (name) {
            var oa = dataObj[item]
            for (var i = 0; i < dataObj[item].length; i++) {
              if (name === oa[i].name) {
                //'{name|' +name + '  ' + oa[i].value.percentage + '%' + '}\n{value|' + params.data.value + '}'
                return ' ' + name + '\n' + ' ' + oa[i].value + ' ' + oa[i].percentage
              }
            }
          }
        },
        series: [
          {
            name: "",
            type: "pie",
            radius: "50%",
            center: ["25%", "50%"],
            data: dataObj[item],
            label: {
              show: false,
            },
            emphasis: {
              label: {
                show: false,
              },
            },
          },
        ],
      };
      myChart.resize();
      myChart.clear();
      option && myChart.setOption(option);
      window.addEventListener("resize", () => {
        myChart.resize();
      });
    },
    // 租赁次数/租赁金额
    getLeaseData() {
      let data = [
        {
          id: "1",
          name: '呼吸机',
          value: 4000,
        }, {
          id: "2",
          name: '眼科手术器械',
          value: 3000,
        }, {
          id: "3",
          name: '测试',
          value: 2500,
        }, {
          id: "4",
          name: '神经外科手术器械',
          value: 2000,
        }, {
          id: "5",
          name: '基础外科手术器械',
          value: 1500,
        }, {
          id: "6",
          name: '呼吸机',
          value: 1000,
        }
      ]
      // 基于准备好的dom，初始化echarts实例
      let EChart = echarts.init(document.getElementById('adverseEventEchart'))
      //   将data数据中得value值从大到小排序，然后按顺序将value放入dataValue数组中 name放入dataName数组中 name和value一一对应
      data.sort(function (a, b) {
        return a.value - b.value
      })
      const dataName = Array.from(data, (item) => {
        item.name = item.name || '未命名'
        const match = item.name.match(this.regex)
        const name = item.name
        return {
          value: name,
          textStyle: {
            color: '#FFF'
          }
        }
      })
      // 配置参数
      let config = {
        legend: {},
        grid: {
          top: '5%',
          left: '3%',
          right: '8%',
          bottom: '0%',
          containLabel: true
        },
        xAxis: {
          type: 'value',
          show: false
        },
        yAxis: [
          {
            type: 'category',
            data: dataName,
            axisTick: {
              show: false // 不显示坐标轴刻度线
            },
            axisLine: {
              show: false // 不显示坐标轴线
            },
            splitLine: {
              // 网格线
              show: false
            },
            axisLabel: { textStyle: { color: '#fff', fontSize: '14' } }
          },
          {
            type: 'category', // 坐标轴类型
            // inverse: true, // 是否是反向坐标轴
            axisTick: {
              show: false // 不显示坐标轴刻度线
            },
            axisLine: {
              show: false // 不显示坐标轴线
            },
            splitLine: {
              // 网格线
              show: false
            },
            axisLabel: {
              textStyle: {
                color: '#FFFFFFCC',
                fontSize: '14'
              },
              formatter: (value) => {
                return value + `${this.isLease == true ? '元' : '次'}`
              }
            },
            data: data
          }
        ],
        series: [
          {
            type: 'bar',
            stack: 'total',
            label: {
              show: false
            },
            emphasis: {
              focus: 'series'
            },
            data: data,
            barWidth: 8,
            itemStyle: {
              normal: {
                // barBorderRadius: [15, 15, 15, 15],
                color: function (params) {
                  // 通过返回值的下标一一对应将颜色赋给柱子上
                  return '#0A84FF'
                }
              },
              // 鼠标移入改变颜色
              emphasis: {
                color: '#FFCA64FF'
              }
            },
            showBackground: true, // 显示背景色
            backgroundStyle: {
              color: '#384156'
            }
          }
        ],
        dataZoom: [
          {
            yAxisIndex: [0, 1],
            orient: 'vertical',
            show: true,
            type: 'slider',
            start: 100,
            // end: 100,
            width: 8,
            left: '99%',
            borderColor: 'rgba(43,48,67,.1)',
            fillerColor: '#6580b8',
            zoomLock: true,
            showDataShadow: false,
            backgroundColor: 'rgba(43,48,67,.1)',
            showDetail: false,
            // realtime: true,
            filterMode: 'filter',
            handleIcon: 'M-9.35,34.56V42m0-40V9.5m-2,0h4a2,2,0,0,1,2,2v21a2,2,0,0,1-2,2h-4a2,2,0,0,1-2-2v-21A2,2,0,0,1-11.35,9.5Z',
            handleStyle: {
              color: '#6580b8',
              borderColor: '#6580b8'
            },
            maxValueSpan: 4,
            minValueSpan: 4,
            brushSelect: false
          },
          {
            type: 'inside',
            show: false,
            yAxisIndex: [0, 1],
            height: 8
          }
        ]
      }
      // 设置参数
      EChart.setOption(config)
      // 先移除点击事件 解决点击事件重复绑定
      EChart.getZr().off('click')
      // 点击事件
      EChart.getZr().on('click', (params) => {
        var pointInPixel = [params.offsetX, params.offsetY]
        if (EChart.containPixel('grid', pointInPixel)) {
          // 获取当前点击的索引值
          // 注意：若柱状图为纵向则获取x轴的索引，若柱状图为横向则需获取y轴的索引(见下方注释)
          // var xIndex=EChart.convertFromPixel({seriesIndex:0},[params.offsetX, params.offsetY])[0];
          // var xData=config.xAxis.data[xIndex];//当前点击柱子的数据
          var yIndex = EChart.convertFromPixel({ seriesIndex: 0 }, [params.offsetX, params.offsetY])[1]
          var yData = config.yAxis[1].data[yIndex] // 当前点击柱子的数据
          // 新增变量 判断当前点击柱状图是选中还是取消选中
          let isSelected = false
          dataName.map((e, i) => {
            // 选中的设置选中色
            if (i == yIndex && e.textStyle.color != '#FFCA64FF') {
              e.textStyle.color = '#FFCA64FF'
            } else {
              // 选中已选中的则为取消选中
              if (i == yIndex && e.textStyle.color == '#FFCA64FF') {
                isSelected = true
              }
              // 其他的设为默认色
              e.textStyle.color = '#FFF'
            }
          })
          config.yAxis[0].data = JSON.parse(JSON.stringify(dataName))
          config.dataZoom[0].start = (yIndex / dataName.length) * 100
          EChart.setOption(config)
        }
      })
    },
    allTableChange() {
      this.visible = true
      this.title = '共享调配'
    },
    changeLeaseShow(val) {
      this.isLease = val == 1
      this.getLeaseData()
    }
  }
}
</script>

<style lang="scss" scoped>
@import "../../spaceManage/style/module";
.sharedAllocationComponent {
  width: 100%;
  height: 100%;
  .el-dropdown-link {
    color: #fff !important;
    margin-left: 10px;
  }
  .title-left {
    padding-left: 20px;
    .unit {
      font-size: 12px !important;
      color: #ffffff !important;
      font-weight: 400 !important;
    }
  }
  .chartContent {
    padding: 16px 0;
    height: calc(100% - 44px);
    display: flex;
    width: 100%;
    .charts-box {
      width: calc(50% - 0.5rem);
      height: 100%;
      background: rgba(255, 255, 255, 0.05);
      border-radius: 4px;
      .charts-box-top {
        width: 100%;
        height: calc(100% - 60px);
      }
      .charts-box-bottom {
        margin: 0 8px;
        height: 53px;
        border-top: 1px solid rgba(133, 145, 206, 0.5);
        display: flex;
        justify-content: center;
        align-items: center;
        span:nth-child(1) {
          font-weight: 400;
          font-size: 14px;
          color: #ffffff;
        }
        span:nth-child(2) {
          font-weight: bold;
          font-size: 20px;
          color: #ffca64;
          margin-left: 6px;
        }
      }
    }
    .charts-box:nth-child(2) {
      margin-left: 1rem;
    }
  }
  .statistics_top_new {
    height: 100%;
    display: flex;
    flex-wrap: wrap;
    .statistics_top_item {
      height: calc(100% / 3);
      width: 50%;
      // padding: 16px 0;
      display: flex;
      justify-content: center;
      align-items: center;
      img {
        width: 50px;
        height: 50px;
      }
    }
    .statistics_top_content {
      .statistics_top_title {
        padding-left: 20px;
        font-size: 14px;
        font-family: HarmonyOS Sans SC-Regular, HarmonyOS Sans SC;
        font-weight: 400;
        color: #b0e3fa;
        margin-bottom: 6px;
      }
      .font_bg {
        width: 125px;
        height: 32px;
        background: url(./../../../assets/images/qhdsys/Group-new.png) no-repeat
          100%;
        display: flex;
        align-items: center;
        justify-content: center;
        .color_font {
          font-size: 20px;
          font-family: HarmonyOS Sans SC-Bold, HarmonyOS Sans SC;
          font-weight: bold;
          color: #ffca64;
          margin-bottom: 3px;
        }
        .color_unit {
          margin-left: 3px;
          font-size: 14px;
          font-family: HarmonyOS Sans SC-Light, HarmonyOS Sans SC;
          font-weight: 300;
          color: rgba(255, 255, 255, 0.5);
        }
      }
      .item-rate {
        font-weight: 400;
        font-size: 12px;
        margin-top: 4px;
        color: #ffffff;
        img {
          width: 19px;
          height: 19px;
          margin: 0 4px;
          vertical-align: middle;
        }
        .rate {
          font-weight: bold;
          font-size: 12px;
          color: #ff2d55;
        }
      }
    }
  }
}
.module-header {
  width: 100%;
  // padding-left: 50px;
  background: url("~@/assets/images/qhdsys/bg-bt.png") no-repeat;
}
.order-more {
  width: 24px;
  height: 24px;
  margin-right: 0 !important;
  margin-left: 8px;
}
</style>
