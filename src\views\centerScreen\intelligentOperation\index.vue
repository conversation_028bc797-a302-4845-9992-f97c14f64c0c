<template>
  <div class="content">
    <scadaManagement v-if="scadaShow" :projectCode="monitorData.map(v => v.projectCode).join(',')" />
    <environmentalQuality v-if="webglShow && monitorData.projectCode === ''" />
    <airConditioning v-if="webglShow && monitorData.projectName === '空调监测'" />
    <lightingControl v-if="webglShow && monitorData.projectName === '照明监测'" />
    <template v-if="airOverviewShow">
      <airOverview ref="airOverview" :dialogShow="airOverviewShow" @configCloseDialog="configCloseDialog"></airOverview>
    </template>
  </div>
</template>
<script>
import { monitorTypeList } from '@/assets/common/dict.js'
import scadaManagement from './scadaManagement.vue'
import environmentalQuality from './environmentalQuality.vue'
import airConditioning from './airConditioning.vue'
import lightingControl from './lightingControl.vue'
import airOverview from './components/airOverview.vue'

export default {
  name: 'IntelligentOperation',
  components: {
    scadaManagement,
    environmentalQuality,
    lightingControl,
    airConditioning,
    airOverview
  },
  data() {
    return {
      monitorData: {
        name: '',
        projectCode: '',
        projectName: '',
        wpfKey: ''
      },
      scadaShow: false,
      webglShow: true,
      airOverviewShow: false
    }
  },
  created() {
    // 初始化 根据模块过滤数据
    if (Object.hasOwn(this.$route.query, 'tabName')) {
      this.monitorData = monitorTypeList.filter(e => this.$route.query.tabName.split(',').includes(e.wpfKey))
      console.log(11111111, this.monitorData, this.$route.query.tabName)
      this.scadaShow = this.$route.query.type === 'scada'
      this.webglShow = this.$route.query.type === 'webgl'
    }
  },
  mounted() {
    try {
      window.chrome.webview.addEventListener('message', (event) => {
        const data = JSON.parse(event.data)
        if (Object.hasOwn(data, 'hover')) {
          this.airOverviewShow = data.hover
        }
      })
    } catch (error) {}
    // document.getElementsByClassName('el-main')[0].style.padding = this.iframeShow ? '0rem' : '0.375rem 1.5rem 1rem 1.5rem'
    // this.$once('hook:beforeDestroy', () => {
    //   this.$message.close()
    // })
  },
  destroyed() {
    this.$nextTick(() => {
      // document.getElementsByClassName('el-main')[0].style.padding = '0.375rem 1.5rem 1rem 1.5rem'
    })
  },
  methods: {
    configCloseDialog() {
      this.airOverviewShow = false
    }
  }
  // beforeDestroy() {
  //   this.$message.close()
  // }
}
</script>
<style lang="scss" scoped>
.content {
  width: 100%;
  height: 100%;
  color: #fff;
  // padding-top: 24px;
  // display: flex;
  // justify-content: space-between;
  position: relative;
  .change-btn {
    position: absolute;
    top: 10px;
    left: 0px;
    z-index: 10;
  }
}
</style>
