# 组件使用说明

## [DialogFrame](./DialogFrame.vue) - 弹窗

对dialog的一次封装，宽度`80%`高度`80vh`，主要是做背景统一，并且增加了面包屑的功能。
> 如果使用其他尺寸的弹窗，更建议复制样式并更改背景图。

* 属性

| 属性名称       | 值类型                                                                         | 默认值   | 说明                                                       |
|------------|-----------------------------------------------------------------------------|-------|----------------------------------------------------------|
| visible    | boolean                                                                     | false | 父组件通过`:visible.sync` 同步组件的展示状态。                          |
| title      | string                                                                      | 标题    | 组件的标题，非全屏时在中间位置，全屏时在左或者不展示。                              |
| breadcrumb | Array<{name:string,label:string}> <br/>`name`回传的目标值(组件名称)<br/> `label`展示的名称 | []    | 面包屑,如果长度大于1，则出现在左侧。返回按钮+面包屑，点击返回按钮或者非最后一级面包屑则会触发`back`事件 |

* 事件

| 事件名称           | 事件参数   | 描述                       |
|----------------|--------|--------------------------|
| update:visible | false  | 窗口总是点击关闭的时候触发，参数始终为`false` |
| back           | string | 点击面包屑或者返回按钮时触发，参数为目标值    |

* 提供器:

    * sizeChange:`function`  
      当点击dialog的全屏按钮、推出全屏按钮时触发的事件回调，子(孙)组件需要调用该方法进行注册才能接收到响应。
  ```javascript
   export default {
    name:'ComponentA',
    inject:['sizeChange'],
    mounted(){
      // 注册回调
      this.sizeChange(this.onSizeChange)
    },
    beforeDestroy(){
      // 取消注册
      this.sizeChange(this.onSizeChange,false)
    },
    methods:{
      onSizeChange(isFullScreen){
        // 
      } 
    } 
  }
  ```


