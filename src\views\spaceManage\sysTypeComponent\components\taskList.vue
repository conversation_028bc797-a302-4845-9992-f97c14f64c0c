<template>
  <div class="main">
    <el-dialog
      v-dialogDrag
      :modal="false"
      :visible="visible"
      custom-class="mainDialog main"
      append-to-body
      :close-on-click-modal="false"
      :before-close="closeDialog"
      class="all-table-componentList"
    >
      <template slot="title">
        <span class="dialog-title">运行任务</span>
      </template>
      <div class="dialog-content">
        <div class="search-box">
          <div style="margin-right: 16px" class="assetsInput">
            <span>任务时间：</span>
            <el-date-picker
              v-model="dateRange"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              :clearable="false"
              value-format="yyyy-MM-dd"
              @change="timeData"
            >
            </el-date-picker>
          </div>
          <div style="margin-right: 16px" class="assetsInput">
            <span>设备名称：</span>
            <el-select
              v-model="searchForm.robotIds"
              placeholder="请选择设备"
              clearable
            >
              <el-option
                v-for="item in listData"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              >
              </el-option>
            </el-select>
          </div>
          <div style="margin-right: 16px" class="assetsInput">
            <span>任务类型：</span>
            <el-select
              v-model="searchForm.jobType"
              placeholder="请选择任务类型"
            >
              <el-option label="全部" value="0" />
              <el-option label="配送任务" value="10" />
              <el-option label="呼叫任务" value="20" />
              <el-option label="返程任务" value="30" />
              <el-option label="等待任务" value="40" />
              <el-option label="充电任务" value="50" />
              <el-option label="电梯等待区域任务" value="60" />
              <el-option label="管制等待区域任务" value="70" />
              <el-option label="定时消毒任务" value="80" />
              <el-option label="到跨楼宇等待位置任务" value="90" />
              <el-option label="即时消毒任务" value="100" />
              <el-option label="查房任务" value="110" />
              <el-option label="退药任务" value="120" />
              <el-option label="补位任务" value="130" />
              <el-option label="补货任务" value="140" />
              <el-option label="送餐任务" value="150" />
              <el-option label="回收任务" value="160" />
              <el-option label="宣教任务" value="170" />
            </el-select>
          </div>
          <div style="margin-right: 16px" class="assetsInput">
            <span>任务状态：</span>
            <el-select
              v-model="searchForm.jobState"
              placeholder="请选择任务状态"
            >
              <el-option label="全部" value="0" />
              <el-option label="进行中" value="1" />
              <el-option label="已结束" value="2" />
            </el-select>
          </div>
          <div class="search-btn">
            <el-button @click="reset">重置</el-button>
            <el-button @click="search">查询</el-button>
          </div>
        </div>
        <!-- 空间 -->

        <div class="tableContainer">
          <el-table
            v-loading="tableLoading"
            :data="tableData"
            height="100%"
            :cell-style="$tools.setCell(3)"
            :header-cell-style="$tools.setHeaderCell(3)"
            style="width: 100%"
            element-loading-background="rgba(0, 0, 0, 0.2)"
            @row-dblclick="selectConfigRowData"
          >
            <el-table-column
              v-for="(column, index) in tableColumn"
              :key="index"
              height="calc(100% - 40px)"
              :prop="column.prop"
              :label="column.label"
              :min-width="column.minWidth"
              show-overflow-tooltip
            >
              <template slot-scope="scope">
                <table-render
                  v-if="column.render"
                  :sc="scope"
                  :row="scope.row"
                  :render="column.render"
                ></table-render>
                <div v-else-if="!column.formatter">
                  {{ scope.row[column.prop] }}
                </div>
                <div v-else>
                  {{ column.formatter(scope.row) }}
                </div>
              </template>
            </el-table-column>
          </el-table>
        </div>
        <el-pagination
          :current-page="pagination.pageNo"
          :page-sizes="[15, 30, 50, 100]"
          :page-size="pagination.pageSize"
          layout="total, sizes, prev, pager, next, jumper"
          :total="pageTotal"
          class="pagination"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        >
        </el-pagination>
      </div>
    </el-dialog>
    <TaskDetail v-if="detailVisible" :visible="detailVisible" :detail="detail" @close="closeDetail"></TaskDetail>
  </div>
</template>
<script lang="jsx">
import tableRender from '@/views/spaceManage/components/tableRender'
import { taskDeviceList, taskList, factoryCodeByAgv } from '@/utils/spaceManage'
import TaskDetail from './taskDetail.vue'
export default {
  name: 'taskList',
  components: {
    'table-render': tableRender,
    TaskDetail
  },
  props: {
    visible: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      tableColumn: [
        {
          prop: 'robotName',
          label: '机器人名称'
        },
        {
          prop: 'robotModel',
          label: '机器人类型'
        },
        {
          prop: 'startPosition',
          label: '始发地'
        },
        {
          prop: 'endPosition',
          label: '目的地'
        },
        {
          prop: 'startTime',
          label: '开始时间'
        },
        {
          prop: 'acceptState',
          label: '物品接收状态',
          formatter: (row) => {
            return row.acceptState === null ? '-' : this.getJobAcceptStatus(row.acceptState)
          }
        },
        {
          prop: 'jobType',
          label: '任务类型',
          formatter: (row) => {
            return row.jobType === null ? '-' : this.getJobType(row.jobType)
          }
        },
        {
          prop: 'jobState',
          label: '任务状态',
          formatter: (row) => {
            return row.jobState === null ? '-' : this.getJobState(row.jobState)
          }
        },
        {
          prop: 'userName',
          label: '发起人'
        },
        {
          label: '操作',
          render: (h, row) => {
            row.row.type = 'danger'
            return (
              <div class="operationBtn">
                <span onClick={() => this.selectConfigRowData(row.row)}>详情</span>
              </div>
            )
          }
        }
      ],
      tableLoading: false,
      searchForm: {
        robotIds: '', // 设备id
        jobType: '', // 任务类型
        jobState: '', //	0:全部 1:进行中 2:已结束
        startTime: '',
        endTime: ''
      },
      dateRange: [],
      pagination: {
        pageSize: 15,
        pageNo: 1
      },
      tableData: [],
      title: '',
      pageTotal: 0,
      deviceId: '',
      fadeShow: true, // 淡入淡出
      listData: [],
      // 详情弹框
      detailVisible: false,
      detail: {}
    }
  },
  mounted() {
    try {
      window.chrome.webview.hostObjects.sync.bridge.GetSwichFloorIsShow(true)
      window.chrome.webview.hostObjects.sync.bridge.ShowDialog(true)
    } catch (error) {}
    this.getDeviceList()
    this.getFactoryCodeByAgv()
  },
  methods: {
    getJobState(state) {
      switch (state) {
        case 0:
          return '全部'
        case 1:
          return '任务队列中'
        case 2:
          return '任务下发中'
        case 3:
          return '任务执行中'
        case 4:
          return '任务到达'
        case 5:
          return '任务完成'
        case 6:
          return '取消任务'
        case 7:
          return '电量低终止'
        case 8:
          return '无消毒液终止'
        case 9:
          return '新任务终止'
        case 10:
          return '系统设置结束'
        case 11:
          return '资源不存在导致取消任务'
        case 12:
          return '任务过期取消任务'
        case 13:
          return '任务创建失败'
        case 14:
          return 'APP异常'
        case 15:
          return 'App放餐失败'
        case 16:
          return 'APP上报的任务执行超时'
        case 17:
          return '抽水传感器异常终止'
        default:
          return ''
      }
    },
    getJobAcceptStatus(state) {
      switch (state) {
        case 1:
          return '待接收'
        case 2:
          return '已接收'
        case 3:
          return '超时未取'
        case 4:
          return '有退药'
        case 5:
          return '超时未配送'
        case 6:
          return '暂不配送'
        default:
          return ''
      }
    },
    getJobType(state) {
      switch (state) {
        case 0:
          return '全部'
        case 10:
          return '配送任务'
        case 20:
          return '呼叫任务'
        case 30:
          return '返程任务'
        case 40:
          return '等待任务'
        case 50:
          return '充电任务'
        case 60:
          return '电梯等待区域任务'
        case 70:
          return '管制等待区域任务'
        case 80:
          return '定时消毒任务'
        case 90:
          return '到跨楼宇等待位置任务'
        case 100:
          return '即时消毒任务'
        case 110:
          return '查房任务'
        case 120:
          return '退药任务'
        case 130:
          return '补位任务'
        case 140:
          return '补货任务'
        case 150:
          return '送餐任务'
        case 160:
          return '回收任务'
        case 170:
          return '宣教任务'
        default:
          return ''
      }
    },
    timeData(val) {
      this.searchForm.startTime = val[0] + ' 00:00:00'
      this.searchForm.endTime = val[1] + ' 23:59:59'
    },
    // 获取设备列表
    getDeviceList() {
      let params = {
        terms: [
          {
            column: 'systemTypeCode',
            value: 'AGVJQR'
          },
          {
            column: 'deviceTypeCode',
            value: 'WLJQR'
          }
        ]
      }
      this.listData = []
      taskDeviceList(params)
        .then((res) => {
          if (res.data.status == '200') {
            this.listData = res.data.result || []
          }
        })
        .catch(() => {})
    },
    getFactoryCodeByAgv() {
      factoryCodeByAgv().then((res) => {
        if (res.data.code == '200') {
          this.deviceId = res.data.data
          this.getTableData()
        }
      })
    },
    // 品类table列表
    getTableData() {
      this.tableLoading = true
      let data = {
        ...this.searchForm,
        ...this.pagination
      }
      taskList(this.deviceId, 'jobs', data)
        .then((res) => {
          if (res.data.status == 200) {
            // 提取每个对象的jobs中的第一条记录
            this.tableData = res.data.result[0].items.map((item) => {
              return {
                ...item, // 保留原有的item属性
                ...item.jobs[0] // 添加第一条job
              }
            })
            this.pageTotal = res.data.result[0].count
          } else {
            this.$message.error(res.data.message)
          }
        })
        .finally(() => {
          this.tableLoading = false
        })
    },
    handleSizeChange(val) {
      this.pagination.pageSize = val
      this.pagination.pageNo = 1
      this.getTableData()
    },
    search() {
      this.pagination.pageNo = 1
      this.getTableData()
    },
    reset() {
      this.searchForm = {
        robotIds: '', // 设备id
        jobType: '', // 任务类型
        jobState: '', //	0:全部 1:进行中 2:已结束
        startTime: '',
        endTime: ''
      }
      this.dateRange = []
      this.pagination.pageNo = 1
      this.pageTotal = 0
      this.getTableData()
    },
    handleCurrentChange(val) {
      this.pagination.pageNo = val
      this.getTableData()
    },
    // 关闭列表弹框
    closeDialog () {
      this.$emit('close')
    },
    // 打开详情弹框
    selectConfigRowData(row) {
      this.detail = row
      this.detailVisible = true
      try {
        window.chrome.webview.hostObjects.sync.bridge.ShowDialog(true)
      } catch (error) {}
    },
    // 关闭详情弹框
    closeDetail () {
      this.detailVisible = false
    }
  }
}
</script>

<style lang="scss" type="text/css" scoped>
.preview-image {
  z-index: 9999 !important;
  ::v-deep .el-image-viewer__canvas {
    color: #fff;
  }
}
:deep(.mainDialog) {
  width: 70%;
  height: 84vh;
  margin-top: 6vh !important;
  // border: 1px solid #5996f9;
  background-color: transparent !important;
  pointer-events: auto;
  background-image: url("@/assets/images/table-bg.png");
  background-repeat: no-repeat;
  background-size: 100% 100%;
  box-shadow: none;

  .el-dialog__headerbtn {
    transform: translateX(-36px);
    width: 25px;
    height: 25px;
    background-image: url("@/assets/images/close.png");
    background-repeat: no-repeat;
    background-size: 100% 100%;
    .el-dialog__close::before {
      display: none;
    }
  }
  .dialog-title {
    display: inline-block;
    width: 100%;
    text-align: center;
    transform: translateY(-6px);
    color: #cbdeed;
  }
  .dialog-title::before {
    display: none;
  }

  .el-dialog__header {
    background-color: transparent;
  }
  .el-dialog__body {
    padding: 0;
    height: calc(100% - 60px);
    max-height: fit-content;
    overflow-y: hidden;
    background-color: transparent;
    padding: 0px 80px;
    .dialog-content {
      width: 100%;
      height: 100%;
      background-color: transparent;
      .el-table {
        background-color: transparent;
        border: 1px solid #202d4c;
        .el-table__header-wrapper {
          background-color: transparent !important;
        }
        .el-table__body-wrapper {
          background-color: transparent !important;
        }
        tr {
          background-color: transparent !important;
        }
        td {
          background-color: transparent !important;
        }
      }
    }
  }
}

:deep(.search-box) {
  display: flex;
  flex-wrap: wrap;
  // margin-bottom: 12px;
  .riskInput {
    .el-input {
      width: 110px;
    }
    .el-select {
      .el-input__inner {
        width: 110px !important;
      }
    }
  }
  .taskInput {
    .el-input {
      width: 140px;
    }
  }
  .assetsInput {
    .el-input {
      width: 140px;
    }
  }
  .deviceInput {
    width: 150px;
    .el-input {
      width: 100%;
    }
    .el-cascader {
      line-height: 35px;
    }
  }
  .el-button {
    background-image: url("@/assets/images/btn.png");
    background-repeat: no-repeat;
    background-size: 100% 100%;
    height: 35px;
  }
  .search-btn {
    margin-left: auto;
  }
  .el-select {
    .el-input .el-input__inner {
      border-color: rgba(133, 145, 206, 0.5);
      width: 150px;
      height: 35px;
    }
  }
  .el-cascader {
    .el-input__inner {
      height: 35px !important;
    }
  }
  .el-date-editor {
    width: 250px;
    height: 35px;
    background-color: #14233e;
    border-color: rgba(133, 145, 206, 0.5);
    .el-input__icon {
      transform: translateY(-2px);
    }
    .el-range-input {
      background-color: #14233e;
      color: rgba(255, 255, 255, 0.8);
    }
    .el-range-separator {
      color: rgba(255, 255, 255, 0.8);
      transform: translateY(-2px);
    }
  }
  .el-input .el-input__inner {
    border-color: rgba(133, 145, 206, 0.5);
    border-radius: 4px;
    color: rgba(255, 255, 255, 0.8);
  }
}
.search-box .el-input {
  width: 120px;
  height: 35px;
}
.tableContainer {
  height: calc(100% - 150px);
  width: 100%;
  margin-top: 16px;
}
</style>
