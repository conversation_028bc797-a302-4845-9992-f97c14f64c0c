<template>
  <div>
    <el-row>
      <el-col :span="8">
        <div class="item">
          <span class="item-title">资产大类</span>
          <span class="item-content">{{ detailsInfo.资产大类 || '--' }}</span>
        </div>
        <div class="item">
          <span class="item-title">资产小类</span>
          <span class="item-content">{{ detailsInfo.资产小类 || '--' }}</span>
        </div>
        <div class="item">
          <span class="item-title">国标分类</span>
          <span class="item-content">{{ detailsInfo.国标分类 || '--' }}</span>
        </div>
        <div class="item">
          <span class="item-title">68分类</span>
          <span class="item-content">{{ detailsInfo['68分类'] || '--' }}</span>
        </div>
        <div class="item">
          <span class="item-title">资产分类标准</span>
          <span class="item-content">{{ detailsInfo.资产分类标准 || '--' }}</span>
        </div>
        <div class="item">
          <span class="item-title">是否维保</span>
          <span class="item-content">{{ detailsInfo.是否维保 || '--' }}</span>
        </div>
      </el-col>
      <el-col :span="8">
        <div class="item">
          <span class="item-title">是否计量</span>
          <span class="item-content">{{ detailsInfo.是否计量 || '--' }}</span>
        </div>
        <div class="item">
          <span class="item-title">是否防疫</span>
          <span class="item-content">{{ detailsInfo.是否防疫 || '--' }}</span>
        </div>
        <div class="item">
          <span class="item-title">类别属性</span>
          <span class="item-content">{{ detailsInfo.类别属性 || '--' }}</span>
        </div>
        <div class="item">
          <span class="item-title">附属设备</span>
          <span class="item-content">{{ detailsInfo.附属设备 || '--' }}</span>
        </div>
        <div class="item">
          <span class="item-title">特种设备</span>
          <span class="item-content">{{ detailsInfo.特种设备 || '--' }}</span>
        </div>
      </el-col>
      <el-col :span="8">
        <div class="item">
          <span class="item-title">急救生命支持设备</span>
          <span class="item-content">{{ detailsInfo.急救生命支持设备 || '--' }}</span>
        </div>
        <div class="item">
          <span class="item-title">中医诊疗设备</span>
          <span class="item-content">{{ detailsInfo.中医诊疗设备 || '--' }}</span>
        </div>
        <div class="item">
          <span class="item-title">检验类设备</span>
          <span class="item-content">{{ detailsInfo.检验类设备 || '--' }}</span>
        </div>
        <div class="item">
          <span class="item-title">辐射设备</span>
          <span class="item-content">{{ detailsInfo.辐射设备 || '--' }}</span>
        </div>
        <div class="item">
          <span class="item-title">风险等级</span>
          <span class="item-content">{{ detailsInfo.风险等级 || '--' }}</span>
        </div>
      </el-col>
    </el-row>
  </div>
</template>
<script>
export default {
  name: 'CategoryInfo',
  props: ['detailsInfo'],
  data() {
    return {}
  },
  mounted() {
    console.log('123')
  },
  methods: {}
}
</script>

<style scoped lang="scss">
  .item {
    margin-bottom: 12px;
    font-size: 14px;
    display: flex;
    height: 20px;
    line-height: 20px;
    .item-title {
      color: #7EAEF9;
      width: 100px;
    }
    .item-content {
      color: #fff;
    }
  }
</style>
