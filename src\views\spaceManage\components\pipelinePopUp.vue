<template>
  <div class="dialog-content">
    <el-dialog title="管线台账" :visible.sync="pipelineShow" width="60.1042%" :before-close="handleClose" class="dialog-gx" :show-close="false">
      <div class="dialog-right">
        <!-- <div class="dialog-fx" @click="statisticalExport"></div> -->
        <div class="dialog-tc" @click="handleClose"></div>
      </div>
      <div class="dialog-div-gx">
        <el-table :data="tableData" style="width: 100%" row-key="id" :tree-props="{ children: 'children', hasChildren: 'hasChildren' }" lazy :load="load">
          <el-table-column prop="categoryName" label="类型名称" min-width="140" class-name="table-flex" show-overflow-tooltip> </el-table-column>
          <el-table-column prop="modelCode" label="模型编码" min-width="122" show-overflow-tooltip> </el-table-column>
          <el-table-column prop="modelId" label="模型ID" min-width="122" show-overflow-tooltip> </el-table-column>
          <el-table-column prop="categoryLevel" label="层级" min-width="122" show-overflow-tooltip> </el-table-column>
          <el-table-column prop="spacePosition" label="位置" min-width="122" show-overflow-tooltip> </el-table-column>
          <el-table-column prop="specification" label="规格型号" min-width="123" show-overflow-tooltip> </el-table-column>
          <el-table-column prop="material" label="材质" min-width="123" show-overflow-tooltip> </el-table-column>
          <el-table-column prop="manufacturerName" label="生产厂商" min-width="123" show-overflow-tooltip> </el-table-column>
        </el-table>
      </div>
      <div class="dialog-foot-gx">
        <div class="foot-zs">共{{ total }}条</div>
        <div>
          <el-pagination layout="prev, pager, next" :total="total" :page-size="pageSize" @current-change="handleCurrentChange"> </el-pagination>
        </div>
      </div>
      <!-- <span slot="footer" class="dialog-footer">
        <el-button @click="pipelineShow = false">取 消</el-button>
        <el-button type="primary" @click="pipelineShow = false">确 定</el-button>
      </span> -->
    </el-dialog>
  </div>
</template>

<script>
import { listBySpaceCategory } from '@/utils/spaceManage.js'
export default {
  name: '',
  components: {},
  props: {
    pipelineShow: {
      type: Boolean,
      default: false
    },
    roomData: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      tableData: [],
      currentPage: 1,
      pageSize: 15,
      total: 0
    }
  },
  created() {},
  mounted() {
    this.getListBySpaceCategory()
  },
  methods: {
    // 懒加载
    load(tree, treeNode, resolve) {
      let params = {
        current: 1,
        size: 9999,
        spaceModelCode: this.roomData.modelCode, // 模型编码
        parentId: tree.id // 父级id
      }
      this.getCategoryList(params, 'load').then((arr) => {
        resolve(arr)
      })
    },
    // 获取管线列表
    getListBySpaceCategory() {
      let params = {
        current: this.currentPage,
        size: this.pageSize,
        spaceModelCode: this.roomData.modelCode, // 模型编码
        categoryLevel: '1' // 层级
      }
      this.getCategoryList(params, 'get').then((arr) => {
        this.tableData = arr
      })
    },
    // 获取管线列表接口
    getCategoryList(params, type) {
      return listBySpaceCategory(params).then((res) => {
        let { data } = res
        if (data.code === 200) {
          let list = data.data.records
          let arr = []
          list.forEach((item) => {
            arr.push({
              id: item.id,
              parentId: item.parentId,
              categoryName: item.categoryName || '-',
              modelCode: item.modelCode || '-',
              modelId: item.modelId || '-',
              categoryLevel: item.categoryLevel || '-',
              spacePosition: item.spacePosition || '-',
              specification: item.specification || '-',
              material: item.material || '-',
              manufacturerName: item.manufacturerName || '-',
              hasChildren: item.hasChildren
            })
          })
          if (type !== 'load') {
            this.total = data.data.total
          }
          return arr
        }
      })
    },

    // 转换树形结构
    transformTreeData(data, parentIdField, idField, childrenField) {
      const tree = []
      const map = new Map()
      // 将数据存入 map 中
      data.forEach((node) => {
        map.set(node[idField], node)
      })
      // 遍历数据，构建树
      data.forEach((node) => {
        const parentId = node[parentIdField]
        const parent = map.get(parentId)
        if (parent) {
          if (!parent[childrenField]) {
            parent[childrenField] = []
          }
          parent[childrenField].push(node)
        } else {
          tree.push(node)
        }
      })
      return tree
    },
    // 分页
    handleCurrentChange(val) {
      this.currentPage = val
      this.getListBySpaceCategory()
    },
    handleClose() {
      this.$emit('handleClose')
    }
  },
  computed: {},
  watch: {}
}
</script>
<style lang="scss" scoped>
.dialog-content {
  ::v-deep .dialog-gx {
    .el-dialog {
      height: 603px !important;
      background: url('@/assets/images/qhdsys/bg-tc.png') no-repeat;
      .el-dialog__header {
        padding: 11px 20px 10px;
        text-align: center;
        .el-dialog__title {
          height: 20px;
          font-size: 18px;
          font-family: Source Han Sans CN-Regular, Source Han Sans CN;
          font-weight: 400;
          color: #cbdeed;
          line-height: 20px;
        }
      }
    }
    .dialog-div-gx {
      padding: 0 65px;
      .el-table {
        border: 1px solid #203254 !important;
        border: 0;
        .el-table__header-wrapper {
          .el-table__header {
            .has-gutter {
              tr {
                background: rgba(133, 145, 206, 0.15);
                // border-bottom: 2px solid #ffffff;
                th {
                  padding: 0;
                  height: 44px;
                  .cell {
                    font-size: 14px;
                    font-family: HarmonyOS Sans SC-Medium, HarmonyOS Sans SC;
                    font-weight: 500;
                    color: #8bddf5;
                  }
                }
              }
            }
          }
        }
        .el-table__body-wrapper {
          background: transparent;
          height: 360px;
          overflow: hidden;
          overflow-y: auto;
          .el-table__body {
            tbody {
              .el-table__row {
                background: transparent;
                border: 0;
                td {
                  padding: 0;
                  height: 40px;
                  border: 0;
                  .cell {
                    font-size: 14px;
                    font-family: HarmonyOS Sans SC-Regular, HarmonyOS Sans SC;
                    font-weight: 400;
                    color: #ffffff;
                  }
                }
              }
              .table-flex {
                .cell:nth-child(1) {
                  display: flex;
                  align-items: center;
                }
              }
              .el-table__row:nth-child(2n - 1) {
                background: rgba(168, 172, 171, 0.08);
              }
              .el-table__row:hover {
                border: 0;
                opacity: 1;
                cursor: pointer;
                td div {
                  color: rgba(255, 202, 100, 1);
                }
              }
            }
          }
        }
      }
    }
    .dialog-foot-gx {
      padding: 0 65px;
      margin-top: 41px;
      display: flex;
      align-items: center;
      justify-content: space-between;
      .foot-zs {
        font-size: 14px;
        font-family: HarmonyOS Sans SC-Regular, HarmonyOS Sans SC;
        font-weight: 400;
        color: #ffffff;
        line-height: 14px;
      }
      .el-pagination {
        .btn-prev {
          background: transparent;
          .el-icon {
            color: #ffffff;
          }
        }
        .btn-next {
          background: transparent;
          .el-icon {
            color: #ffffff;
          }
        }
      }
    }
    .dialog-right {
      position: absolute;
      top: 17px;
      right: 60px;
      display: flex;
      .dialog-fx {
        width: 36px;
        height: 36px;
        margin-right: 8px;
        background: url('@/assets/images/qhdsys/bg-icon-fx.png') no-repeat;
      }
      .dialog-fx:hover {
        cursor: pointer;
      }
      .dialog-tc {
        width: 36px;
        height: 36px;
        background: url('@/assets/images/qhdsys/bg-icon-gb.png') no-repeat;
      }
      .dialog-tc:hover {
        cursor: pointer;
      }
    }
  }
}
</style>
