<template>
  <div class="alarmListDialog">
    <el-dialog v-dialogDrag :title="dialogTitle" :visible.sync="dialogVisible" width="80%" :before-close="handleClose" custom-class="dialog-url" :show-close="false">
      <div class="dialog-right">
        <div class="dialog-tc" @click="handleClose"></div>
      </div>
      <div class="search-form">
        <el-select
          v-model="searchFrom.projectCode"
          placeholder="报警来源"
          multiple
          collapse-tags
          clearable
          @change="getIncidentAndSourceGroup"
          @clear="
            () => {
              searchFrom.incidentType = ''
            }
          "
        >
          <el-option v-for="item in alarmSourceOptions" :key="item.projectCode" :label="item.projectName" :value="item.projectCode"> </el-option>
        </el-select>
        <el-select v-model="searchFrom.incidentType" placeholder="事件类型" clearable :disabled="!(searchFrom.projectCode.length == 1)">
          <el-option v-for="item in eventTypeOptions" :key="item.incidentType" :label="item.incidentName" :value="item.incidentType"> </el-option>
        </el-select>
        <el-select v-model="searchFrom.alarmLevel" placeholder="报警等级" clearable>
          <el-option v-for="item in alarmLevelOptions" :key="item.value" :label="item.label" :value="item.value"> </el-option>
        </el-select>
        <el-select ref="treeSelect" v-model="searchFrom.alarmSpaceId" clearable placeholder="空间位置" @clear="handleClear">
          <el-option hidden :value="searchFrom.alarmSpaceId" :label="areaName"> </el-option>
          <el-tree
            class="search-form-tree"
            :data="serverSpaces"
            :props="serverDefaultProps"
            :load="serverLoadNode"
            lazy
            :expand-on-click-node="false"
            :check-on-click-node="true"
            @node-click="handleNodeClick"
          >
          </el-tree>
        </el-select>
        <el-date-picker
          v-model="searchFrom.dataRange"
          popper-class="date-style"
          type="daterange"
          unlink-panels
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          value-format="yyyy-MM-dd"
          :picker-options="pickerOptions"
        >
        </el-date-picker>
        <el-button class="sino-button-sure" style="background-color: transparent !important" @click="resetForm">重置</el-button>
        <el-button class="sino-button-sure" style="background-color: transparent !important" @click="searchForm">查询</el-button>
      </div>
      <div class="dialog-div" style="height: calc(100% - 55px - 2.5rem - 34px);">
        <el-table
          ref="table"
          v-loading="tableLoading"
          :data="tableData"
          :resizable="false"
          height="calc(100%)"
          :cell-style="$tools.setCell(3)"
          :header-cell-style="$tools.setHeaderCell(3)"
          style="width: 100%"
          stripe
          element-loading-background="rgba(0, 0, 0, 0.2)"
          @selection-change="tableSelectChange"
          @sort-change="tableSortChange"
        >
          <!-- <el-table-column type="selection" width="60" align="center"></el-table-column> -->
          <el-table-column prop="incidentName" label="事件类型" show-overflow-tooltip></el-table-column>
          <el-table-column prop="alarmObjectName" label="对象" show-overflow-tooltip></el-table-column>
          <el-table-column prop="alarmStartTime" label="报警时间" sortable="custom" width="180">
            <template slot-scope="scope">{{ scope.row.alarmStartTime }}</template>
          </el-table-column>
          <el-table-column prop="alarmLevel" label="报警等级" sortable="custom" width="120">
            <span slot-scope="scope" :style="{ color: alarmLevelItem[scope.row.alarmLevel]?.color ?? '' }">
              {{ alarmLevelItem[scope.row.alarmLevel]?.text ?? '' }}
            </span>
          </el-table-column>
          <el-table-column prop="alarmSpaceName" label="位置" show-overflow-tooltip></el-table-column>
          <el-table-column prop="alarmSource" label="报警来源" show-overflow-tooltip></el-table-column>
          <el-table-column prop="alarmStatus" label="报警处理状态" width="120">
            <div slot-scope="scope" :style="{ color: scope.row.alarmStatus == 0 ? '#FA403C' : scope.row.alarmStatus == 1 ? '#fd9434' : '#999' }">
              {{ scope.row.alarmStatus == 0 ? '未处理' : scope.row.alarmStatus == 1 ? '处理中' : '已关闭' }}
            </div>
          </el-table-column>
          <el-table-column prop="alarmAffirm" label="警情确认" width="100">
            <template slot-scope="scope">{{ alarmAffirmItem[scope.row.alarmAffirm] || '-' }}</template>
          </el-table-column>
          <!-- 0：非经典案例，1：经典案例 -->
          <el-table-column prop="classic" label="经典案例" width="100">
            <span slot-scope="scope">
              {{ scope.row.classic == 1 ? '已存' : '未存' }}
            </span>
          </el-table-column>
          <!-- 0：不屏蔽，1：屏蔽中 -->
          <el-table-column prop="shield" label="屏蔽状态" width="120">
            <div slot-scope="scope" style="display: flex; align-items: center; cursor: pointer; user-select: none">
              <el-switch
                :value="scope.row.shield == 1"
                :width="36"
                active-color="#3a84f6"
                inactive-color="#1c2645"
                @change="
                  (val) => {
                    operating('shielded', scope.row)
                  }
                "
              />
              <!-- <span style="margin-left: 4px">{{ scope.row.shield == 1 ? '已屏蔽' : '未屏蔽' }}</span> -->
            </div>
          </el-table-column>
          <el-table-column label="操作" width="250">
            <div slot-scope="scope" class="operationBtn">
              <!-- dispatch(派单) confirmAlarm(确警) remark(备注) shielded(屏蔽) close(关闭) viewDetails(查看详情) -->
              <span v-if="!scope.row.workNum" style="margin-right: 10px" @click="operating('dispatch', scope.row)">派单</span>
              <span v-if="![1, 2, 3, 4].includes(scope.row.alarmAffirm)" style="margin-right: 10px; color: #ff5454" @click="operating('confirmAlarm', scope.row)">警情确认</span>
              <!-- 已关闭只能查看备注 -->
              <span style="margin-right: 10px" @click="operating('remark', scope.row)">备注</span>
              <!-- 未处理关闭前先走警情确认流程 -->
              <span v-if="scope.row.alarmStatus != 2" style="margin-right: 10px" @click="operating('close', scope.row)">关闭</span>
              <span @click="operating('alarmDetails', scope.row)">查看</span>
            </div>
          </el-table-column>
        </el-table>
      </div>
      <div class="dialog-foot">
        <div class="foot-zs">共{{ total }}条</div>
        <div>
          <el-pagination layout="prev, pager, next" :total="total" :page-size="pageSize" @current-change="handleCurrentChange"> </el-pagination>
        </div>
      </div>
    </el-dialog>
    <screenDialog v-if="scrDialog" :selectItems="screenSelectItems" :visible.sync="scrDialog" />
    <confirmAlarmDialog v-if="showConfirmAlarm" :visible.sync="showConfirmAlarm" :item="selectAlarmItem" />
    <remarkDialog v-if="remrDialog" :visible.sync="remrDialog" :item="selectAlarmItem" />
    <AlarmDetailDialog v-if="alarmDetailShow" ref="alarmDetail" :alarmId="selectAlarmItem.alarmId" :visible.sync="alarmDetailShow" @operating="operating" />
  </div>
</template>

<script>
import {
  getIncidentGroupByProjectCode,
  getSourceByEmpty,
  getSpaceInfoList,
  getStructureTree,
  GetAllAlarmRecord,
  CloseAlarmRecord,
  AlarmAffirm,
  OneKeyDispatch,
  shield
} from '@/utils/peaceLeftScreenApi'
export default {
  name: 'alarmListDialog',
  components: {
    screenDialog: () => import('@/views/normalMode/leftScreen/components/screenDialog.vue'),
    confirmAlarmDialog: () => import('@/views/normalMode/leftScreen/components/confirmAlarmDialog.vue'),
    remarkDialog: () => import('@/views/normalMode/leftScreen/components/remarkDialog.vue'),
    AlarmDetailDialog: () => import('@/views/normalMode/leftScreen/components/AlarmDetailDialog')
  },
  props: {
    dialogVisible: {
      type: Boolean,
      default: false
    },
    modelCode: {
      type: String,
      default: ''
    },
    dialogTitle: {
      type: String,
      default: '报警列表'
    },
    spaceState: {
      type: String,
      default: ''
    },
    initSearchData: {
      type: Object,
      default: () => {
        return {}
      }
    } // 初始化查询数据
  },
  data() {
    return {
      areaName: '', // 选中 下拉树的name
      alarmSourceOptions: [], // 报警来源
      eventTypeOptions: [], // 事件类型
      serverSpaces: [], // 空间位置
      spaces: [], // 空间位置
      serverDefaultProps: {
        label: 'ssmName',
        isLeaf: 'leaf',
        children: 'children'
      },
      searchFrom: {
        projectCode: [], // 报警来源
        incidentType: '', // 事件类型
        alarmLevel: '', // 报警等级
        alarmSpaceId: '', // 空间位置
        dataRange: [], // 时间范围
        entityTypeId: '' // 报警分组
      }, // 搜索条件
      alarmLevelOptions: [
        { value: '0', label: '通知' },
        { value: '1', label: '一般' },
        { value: '2', label: '紧急' },
        { value: '3', label: '重要' }
      ], // 报警等级
      pickerOptions: {
        firstDayOfWeek: 1,
        shortcuts: [
          {
            text: '最近一周',
            onClick(picker) {
              const end = new Date()
              const start = new Date()
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 6)
              picker.$emit('pick', [start, end])
            }
          },
          {
            text: '最近一个月',
            onClick(picker) {
              const end = new Date()
              const start = new Date()
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 30)
              picker.$emit('pick', [start, end])
            }
          }
        ]
      },
      alarmLevelItem: {
        0: { text: '通知', color: '#3562DB' },
        1: { text: '一般', color: '#3562DB' },
        2: { text: '紧急', color: '#FF9435' },
        3: { text: '重要', color: '#FA403C' }
      },
      alarmAffirmItem: { 0: '未确认', 1: '真实报警', 2: '误报', 3: '演练', 4: '调试' },
      timeOrType: '', // 排序 0.按紧急降序  1.按紧急升序  2.按时间降序  3.按时间升序
      tableData: [],
      currentPage: 1,
      pageSize: 15,
      total: 0,
      tableLoading: false,
      batchControlList: [
        // 批量控制
        {
          state: 1,
          label: '关闭'
        },
        {
          state: 2,
          label: '真实报警'
        },
        {
          state: 3,
          label: '误报'
        },
        {
          state: 4,
          label: '演练'
        },
        {
          state: 5,
          label: '调试'
        },
        {
          state: 6,
          label: '屏蔽'
        }
      ],
      scrDialog: false, // 屏蔽弹窗
      showConfirmAlarm: false, // 确警弹窗
      remrDialog: false, // 备注弹窗
      alarmDetailShow: false, // 报警详情弹窗
      selectAlarmList: [], // 选中的报警列表
      selectAlarmItem: {}, // 选中报警项
      screenSelectItems: [] // 屏蔽列表
    }
  },
  computed: {},
  watch: {
    scrDialog(val) {
      if (!val) {
        this.initComponentData()
      }
    },
    showConfirmAlarm(val) {
      if (!val) {
        this.initComponentData()
      }
    },
    remrDialog(val) {
      if (!val) {
        this.initComponentData()
      }
    },
    alarmDetailShow(val) {
      if (!val) {
        this.initComponentData()
      }
    }
  },
  created() {
    Object.assign(this.searchFrom, this.initSearchData)
  },
  mounted() {
    this.getAlarmSource()
    this.getTreelist()
    this.search()
    this.$once('hook:beforeDestroy', () => {
      clearInterval(this.timer)
      this.timer = null
    })
  },
  methods: {
    search() {
      if (this.scrDialog || this.showConfirmAlarm || this.remrDialog || this.alarmDetailShow) return
      this.initComponentData()
    },
    // 初始化组件数据
    initComponentData(tab = false) {
      this.selectAlarmList = []
      this.selectAlarmItem = {}
      // 初始化报警详情
      if (this.alarmDetailShow && !tab) {
        this.$refs.alarmDetail.getAlarmDetails()
        return
      }
      this.getDataList()
    },
    // 一键派单
    oneKeyDispatch(row) {
      const userInfo = this.$store.state.loginInfo.user
      const param = {
        alarmId: row.alarmId,
        alarmLevel: row.alarmLevel,
        alarmSourceName: row.alarmSource,
        incidentName: row.incidentName,
        spaceId: row.alarmSpaceId,
        userName: userInfo.staffName,
        userId: userInfo.staffId,
        projectCode: row.projectCode
      }
      OneKeyDispatch(param).then((res) => {
        if (res.data.code === '200') {
          this.$message({
            message: '已派单',
            type: 'success'
          })
          this.initComponentData()
        } else {
          // this.$message.error(res.data.msg)
        }
      })
    },
    // 确警
    alarmAffirm(status, list) {
      const params = {
        alarmAffirm: status,
        alarmId: list.map((item) => item.alarmId).join(','),
        projectCode: list.map((item) => item.projectCode).join(',')
      }
      AlarmAffirm(params).then((res) => {
        if (res.data.code === '200') {
          this.initComponentData()
        } else {
          // this.$message.error(res.data.msg)
        }
      })
    },
    // 关闭
    closeAlarmRecord(alarmId) {
      CloseAlarmRecord({ alarmId }).then((res) => {
        if (res.data.code === '200') {
          this.initComponentData()
        } else {
          // this.$message.error(res.data.msg)
        }
      })
    },
    // 批量处置按钮
    batchControlEvent(item, arr = this.selectAlarmList) {
      console.log(item.state, '处置状态')
      if (item.state === 1) {
        if (this.selectAlarmList.map((item) => item.alarmStatus).includes(2)) {
          this.$message.error('当前选择报警已被关闭，请重新选择')
          return
        }
        this.$confirm(`是否关闭${arr.length}条报警记录?`, '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          cancelButtonClass: 'sino-button-sure',
          confirmButtonClass: 'sino-button-sure',
          customClass: 'confirm-box-class',
          type: 'warning'
        }).then(() => {
          this.closeAlarmRecord(arr.map((item) => item.alarmId).join(','))
        })
      } else if (item.state === 6) {
        if (this.selectAlarmList.map((item) => item.shield).includes(1)) {
          this.$message.error('当前选择报警已被屏蔽，请重新选择')
          return
        }
        this.scrDialog = !this.scrDialog
        this.screenSelectItems = arr
      } else if ([2, 3, 4, 5].includes(item.state)) {
        if (this.selectAlarmList.map((item) => item.alarmAffirm).includes(1 || 2 || 3 || 4)) {
          this.$message.error('当前选择报警已有警情确认，请重新选择')
          return
        }
        this.$confirm(`是否将${arr.length}条报警记录确警为${item.label}?`, '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          cancelButtonClass: 'sino-button-sure',
          confirmButtonClass: 'sino-button-sure',
          customClass: 'confirm-box-class',
          type: 'warning'
        }).then(() => {
          this.alarmAffirm(item.state - 1, arr)
        })
      }
    },
    // 子/孙组件流程操作
    operating(type, selectItem) {
      // dispatch(派单) confirmAlarm(确警) remark(备注) shielded(屏蔽) close(关闭) viewDetails(查看详情)
      this.selectAlarmItem = selectItem
      console.log(11111, type, selectItem)
      if (type === 'dispatch') {
        // 派单
        this.$confirm('是否确认派发确警工单?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          cancelButtonClass: 'sino-button-sure',
          confirmButtonClass: 'sino-button-sure',
          customClass: 'confirm-box-class',
          type: 'warning'
        }).then(() => {
          this.oneKeyDispatch(selectItem)
        })
      }
      // 确警
      if (type === 'confirmAlarm') {
        this.showConfirmAlarm = !this.showConfirmAlarm
      }
      // 备注
      if (type === 'remark') {
        this.remrDialog = !this.scrDialog
      }
      // 屏蔽
      if (type === 'shielded') {
        if (selectItem.shield === 1) {
          this.$confirm('是否取消屏蔽当前报警?', '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            cancelButtonClass: 'sino-button-sure',
            confirmButtonClass: 'sino-button-sure',
            customClass: 'confirm-box-class',
            type: 'warning'
          }).then(() => {
            shield({
              alarmId: selectItem.alarmId,
              alarmObjectId: selectItem.alarmObjectId,
              incidentType: selectItem.incidentType,
              shield: false
            }).then((res) => {
              if (res.data.code === '200') {
                this.$message({
                  message: '已取消屏蔽',
                  type: 'success'
                })
                this.initComponentData()
              }
            })
          })
        } else {
          this.batchControlEvent({ state: 6, label: '屏蔽' }, [selectItem])
        }
      }
      // 关闭
      // 未处理关闭前先走警情确认流程
      if (type === 'close') {
        if (selectItem.alarmStatus === 0) {
          this.$confirm('当前报警未处理，是否确警后再进行关闭？', '提示', {
            confirmButtonText: '确警',
            cancelButtonText: '取消关闭',
            cancelButtonClass: 'sino-button-sure',
            confirmButtonClass: 'sino-button-sure',
            customClass: 'confirm-box-class',
            type: 'warning'
          }).then(() => {
            this.operating('confirmAlarm', selectItem)
          })
        } else {
          this.$confirm('是否关闭当前报警记录?', '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            cancelButtonClass: 'sino-button-sure',
            confirmButtonClass: 'sino-button-sure',
            customClass: 'confirm-box-class',
            type: 'warning'
          }).then(() => {
            this.closeAlarmRecord(selectItem.alarmId)
          })
        }
      }
      // 报警详情
      if (type === 'alarmDetails') {
        this.alarmDetailShow = !this.alarmDetailShow
      }
      // 工单详情
      if (type === 'workOrderDetails') {
        this.workOderDialogData = []
        // 获取报警详情
        this.getAlarmDetails(selectItem.alarmId)
      }
    },
    // table选择
    tableSelectChange(val) {
      this.selectAlarmList = val
    },
    // table排序
    tableSortChange(column) {
      if (column.prop === 'alarmStartTime') {
        this.timeOrType = column.order ? (column.order === 'ascending' ? 3 : 2) : ''
      } else {
        this.timeOrType = column.order ? (column.order === 'ascending' ? 1 : 0) : ''
      }
      console.log(column)
      this.getDataList()
    },
    // 获取全部报警记录
    getDataList() {
      const { projectCode, incidentType, alarmLevel, alarmSpaceId, dataRange, entityTypeId } = this.searchFrom
      const params = {
        timeOrType: this.timeOrType,
        pageNo: this.currentPage,
        pageSize: this.pageSize,
        projectCode: projectCode.toString(),
        incidentType,
        alarmLevel,
        spaceId: alarmSpaceId,
        startTime: dataRange[0],
        endTime: dataRange[1],
        entityTypeId
      }
      this.tableLoading = true
      GetAllAlarmRecord(params)
        .then((res) => {
          this.tableLoading = false
          if (res.data.code === '200') {
            this.tableData = res.data.data ? res.data.data.records : []
            this.total = res.data.data ? res.data.data.total : 0
          } else {
            this.tableData = []
            this.total = 0
          }
        })
        .catch(() => {
          this.tableLoading = false
        })
    },
    // 查询
    searchForm() {
      console.log(this.searchFrom, '查询条件')
      this.currentPage = 1
      this.getDataList()
    },
    // 重置查询
    resetForm() {
      this.handleClear()
      Object.assign(this.searchFrom, {
        projectCode: [], // 报警来源
        incidentType: '', // 事件类型
        alarmLevel: '', // 报警等级
        dataRange: [], // 时间范围
        objectId: '' // 报警对象
      })
      this.currentPage = 1
      this.pageSize = 15
      this.total = 0
      this.getDataList()
    },
    // 获取服务空间树形结构
    getTreelist() {
      getStructureTree().then((res) => {
        if (res.data.code === 200) {
          this.spaces = res.data.data
          this.areaName = this.searchFrom.alarmSpaceId ? this.spaces.find(v => v.id === this.searchFrom.alarmSpaceId).ssmName : ''
          // 增加 懒加载节点
          res.data.data.map((e) => {
            e.leaf = false
          })
          // 转换为树形结构数据
          this.serverSpaces = this.$tools.transData(res.data.data, 'id', 'pid', 'children')
        }
      })
    },
    // 选择下拉树 数据
    handleNodeClick(data) {
      this.searchFrom.alarmSpaceId = data.id
      this.areaName = data.ssmName
      this.$refs.treeSelect.blur()
    },
    // 服务空间树结构点击获取自己及父节点ID并请求数据实现懒加载
    spaceTreeChange(value, resolve) {
      const child = this.$tools.ListTree(this.spaces, value.pid)
      child.push(value.id)
      const treeId = child.toString()
      const data = {
        current: 1,
        functionDictId: '',
        simCode: treeId,
        size: 999
      }
      getSpaceInfoList(data).then((res) => {
        if (res.data.code === 200) {
          if (typeof resolve === 'function') {
            var treeNodeData = JSON.parse(JSON.stringify(res.data.data.records))
            treeNodeData.map((e) => {
              e.leaf = true
            })
            resolve(treeNodeData)
          }
        }
      })
    },
    // 下拉树懒加载
    serverLoadNode(node, resolve) {
      console.log(node.level)
      if (node.level === 0) {
        return resolve(node.data)
      } else if (node.level > 0 && node.level < 4) {
        return resolve(node.data.children)
      } else {
        return this.spaceTreeChange(node.data, resolve)
      }
    },
    // 空间数据清除
    handleClear() {
      this.searchFrom.alarmSpaceId = ''
      this.areaName = ''
    },
    // 获取报警来源
    getAlarmSource() {
      getSourceByEmpty({}).then((res) => {
        if (res.data.code === '200') {
          this.alarmSourceOptions = res.data.data
        }
      })
    },
    // 获取事件类型以及报警来源分组
    getIncidentAndSourceGroup(val) {
      this.searchFrom.incidentType = ''
      getIncidentGroupByProjectCode({ projectCode: val.toString() }).then((res) => {
        if (res.data.code === '200') {
          this.eventTypeOptions = res.data.data
        }
      })
    },
    // 关闭
    handleClose() {
      this.$emit('dialogVisible')
    },
    handleSizeChange(val) {
      this.pageSize = val
      this.getDataList()
    },
    handleCurrentChange(val) {
      this.currentPage = val
      this.getDataList()
    }
  }
}
</script>
<style lang="scss" scoped>
@import '../../../assets/sino-ui/common/var.scss';
.alarmListDialog {
  position: relative;
  ::v-deep .dialog-url {
    height: 84vh;
    background: url('@/assets/images/table-bg.png') no-repeat center center / 100% 100%;
    margin-top: 6vh !important;
  }
  ::v-deep .el-dialog{
    .el-dialog__header {
      padding: 10px 20px 10px;
      text-align: center;
      .el-dialog__title {
        height: 20px;
        font-size: 18px;
        font-family: Source Han Sans CN-Regular, Source Han Sans CN;
        font-weight: 400;
        color: #cbdeed;
        line-height: 18px;
      }
    }
    .el-dialog__body {
      height: calc(100% - 40px);
    }
  }
  .dialog-div {
    padding: 0 65px;
    ::v-deep .el-table {
      border: 1px solid #203254 !important;
      .el-table__header-wrapper {
        .el-table__header {
          .has-gutter {
            tr {
              background: rgba(133, 145, 206, 0.15);
              // border-bottom: 2px solid #ffffff;
              th {
                padding: 0;
                height: 44px;
                .cell {
                  font-size: 14px;
                  font-family: HarmonyOS Sans SC-Medium, HarmonyOS Sans SC;
                  font-weight: 500;
                  color: #8bddf5;
                }
              }
            }
          }
        }
      }
      .el-table__body-wrapper {
        background: transparent;
        overflow: hidden;
        overflow-y: auto;
        .el-table__body {
          tbody {
            .el-table__row {
              background: transparent;
              border: 0;
              td {
                padding: 0;
                height: 40px;
                border: 0;
                .cell {
                  font-size: 14px;
                  font-family: HarmonyOS Sans SC-Regular, HarmonyOS Sans SC;
                  font-weight: 400;
                  color: #ffffff;
                }
              }
            }
            .el-table__row:nth-child(2n - 1) {
              background: rgba(168, 172, 171, 0.08);
            }
            .el-table__row:hover {
              border: 0;
              opacity: 1;
              // cursor: pointer;
              td div {
                color: rgba(255, 202, 100, 1);
              }
            }
          }
        }
      }
    }
  }
  .dialog-foot {
    padding: 0 65px;
    margin-top: 20px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    .foot-zs {
      font-size: 14px;
      font-family: HarmonyOS Sans SC-Regular, HarmonyOS Sans SC;
      font-weight: 400;
      color: #ffffff;
      line-height: 14px;
    }
    ::v-deep .el-pagination {
      .btn-prev {
        background: transparent;
        .el-icon {
          color: #ffffff;
        }
      }
      .btn-next {
        background: transparent;
        .el-icon {
          color: #ffffff;
        }
      }
    }
  }
  .dialog-right {
    position: absolute;
    top: 17px;
    right: 60px;
    display: flex;
    .dialog-tc {
      width: 36px;
      height: 36px;
      background: url('@/assets/images/qhdsys/bg-icon-gb.png') no-repeat;
    }
    .dialog-tc:hover {
      cursor: pointer;
    }
  }
  .search-form {
    height: 2.5rem;
    padding: 10px 65px 24px;
    box-sizing: content-box;
    & > div {
      margin-right: 10px;
    }
    ::v-deep .el-input__inner {
      background: center;
      border: 1px solid $input-border-color;
      border-radius: 0;
      color: $color;
      height: inherit;
      font-family: PingFangSC-Medium, PingFang SC;
    }
    ::v-deep .el-range-input {
      background: center;
      color: $color;
    }
    ::v-deep .el-range-separator,
    ::v-deep .el-range__icon {
      color: #3769be;
    }
  }
}
::v-deep .el-loading-mask {
  background: rgb(13, 27, 54, 0.9) !important;
}
</style>
<style lang="scss">
.search-form-tree {
  color: #fff !important;
  background: transparent !important;
  .el-tree-node__content:hover {
    background: transparent !important;
    .el-tree-node__label {
      color: #ffe3a6;
    }
  }
}
</style>
