/*
 * @Description:
 */
import router from './index'
import store from '../store/index'
import NProgress from 'nprogress'
import 'nprogress/nprogress.css' // progress bar style
router.beforeEach((to, from, next) => {
  NProgress.start()
  // 未登录增加登录调用
  if (!store.getters.isLogin) {
    const getUserTimer = setInterval(() => {
      console.log(store.getters.isLogin)
      if (store.getters.isLogin) {
        clearInterval(getUserTimer)
        next()
      }
    }, 100)
  } else {
    next()
  }
})
router.afterEach(() => {
  NProgress.done()
})
