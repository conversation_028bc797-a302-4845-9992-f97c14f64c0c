<template>
  <el-dialog
    v-dialogDrag
    :modal="false"
    :visible="visible"
    custom-class="mainDialog main"
    append-to-body
    :close-on-click-modal="false"
    :before-close="closeDia"
    class="all-table-componentList"
  >
    <template slot="title">
      <span class="dialog-title">人流量监测</span>
    </template>
    <div class="dialog-content">
      <div class="search-box">
        <div style="margin-right: 16px" class="taskInput">
          <el-input
            v-model="searchParams.name"
            placeholder="名称"
          ></el-input>
        </div>

        <div style="margin-right: 16px" class="taskInput">
          <!-- <el-select
            v-model="searchParams.spaceId"
            placeholder="所在楼层"
            filterable
            popper-class="new-select"
          >
            <el-option
              v-for="item in floorList"
              :key="item.id"
              :label="item.fullName"
              :value="item.id"
            >
            </el-option>
          </el-select> -->
          <el-select ref="treeSelect" v-model="searchParams.spaceId" popper-class="new-select" clearable placeholder="所在楼层" @clear="treeHandleClear">
            <el-option hidden :value="searchParams.spaceId" :label="regionName"> </el-option>
            <el-tree class="search-form-tree" :data="spaceData" :props="spaceProps" :expand-on-click-node="false" :check-on-click-node="true" @node-click="treeHandleNodeClick" />
          </el-select>
        </div>

        <div class="search-btn">
          <el-button @click="resetSearch">重置</el-button>
          <el-button @click="handleSearch">查询</el-button>
        </div>
      </div>
      <div class="tableContent">
        <el-table
          v-loading="tableLoading"
          :data="tableData"
          height="100%"
          :cell-style="$tools.setCell(3)"
          :header-cell-style="$tools.setHeaderCell(3)"
          style="width: 100%"
          element-loading-background="rgba(0, 0, 0, 0.2)"
        >
          <el-table-column prop="name" label="名称"  show-overflow-tooltip> </el-table-column>
          <el-table-column prop="enterNum" label="进入人次"   show-overflow-tooltip> </el-table-column>
          <el-table-column prop="leaveNum" label="离开人次"   show-overflow-tooltip>

          </el-table-column>
          <el-table-column prop="deviceNum" label="监测设备数"   show-overflow-tooltip> </el-table-column>
          <el-table-column prop="regionName" label="所在楼层" width="350"> </el-table-column>

          <el-table-column prop="isEntryExit" label="出入口" > </el-table-column>
        </el-table>
        <el-pagination
          :current-page="currentPage"
          :page-sizes="[15, 20, 30, 40]"
          :page-size="pageSize"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
          class="pagination"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        ></el-pagination>
      </div>
    </div>
  </el-dialog>
</template>
<script>
import { getStructureTree } from '@/utils/peaceLeftScreenApi'
import { getpersonFlowPageList } from '@/utils/comprehensiveStatistics'
export default {
  props: {
    visible: {
      type: Boolean,
      default: false
    }

  },

  data() {
    return {
      searchParams: {
        name: '',
        spaceId: ''
      },

      tableLoading: false,
      tableData: [],
      currentPage: 1,
      pageSize: 15,
      total: 0,
      spaceData: [], // 空间数据
      regionName: '',
      spaceProps: {
        label: 'ssmName',
        isLeaf: 'leaf',
        children: 'children'
      } // 空间数据配置
    }
  },
  mounted () {
    this.getList()
    this.spaceTreeListFn()
  },
  methods: {
    // 空间数据清除
    treeHandleClear() {
      this.searchParams.spaceId = ''
      this.regionName = ''
    },
    // 选择下拉树 数据
    treeHandleNodeClick(data) {
      this.searchParams.spaceId = data.id
      this.regionName = data.ssmName
      this.$refs.treeSelect.blur()
    },
    spaceTreeListFn() {
      getStructureTree().then((res) => {
        if (res.data.code === 200) {
          this.spaceData = this.$tools.transData(res.data.data, 'id', 'pid', 'children')
        }
      })
    },
    closeDia() {
      this.$emit('close')
    },

    getList () {
      this.tableLoading = true
      let params = {
        ...this.searchParams,
        page: this.currentPage,
        pageSize: this.pageSize
      }
      getpersonFlowPageList(params).then(res => {
        if (res.data.code == 200) {
          this.tableData = res.data.data.records
          this.total = res.data.data.total
        }
      }).finally(() => {
        this.tableLoading = false
      })
    },
    resetSearch () {
      this.searchParams = {
        name: '',
        spaceId: ''
      }
      this.treeHandleClear()
      this.currentPage = 1
      this.getList()
    },
    handleSearch () {
      this.currentPage = 1
      this.getList()
    },
    handleSizeChange(val) {
      this.currentPage = 1
      this.pageSize = val
      this.getList()
    },
    handleCurrentChange(val) {
      this.currentPage = val
      this.getList()
    }
  }
}
</script>
<style lang="scss" scoped>
::v-deep .mainDialog {
  width: 80%;
  height: 84vh;
  margin-top: 6vh !important;
  // border: 1px solid #5996f9;
  background-color: transparent !important;
  pointer-events: auto;
  background-image: url("@/assets/images/table-bg.png");
  background-repeat: no-repeat;
  background-size: 100% 100%;
  box-shadow: none;
  .el-dialog__headerbtn {
    transform: translateX(-36px);
    width: 25px;
    height: 25px;
    background-image: url("@/assets/images/close.png");
    background-repeat: no-repeat;
    background-size: 100% 100%;
    .el-dialog__close::before {
      display: none;
    }
  }
  .dialog-title {
    display: inline-block;
    width: 100%;
    text-align: center;
    transform: translateY(-6px);
    color: #cbdeed;
  }
  .dialog-title::before {
    display: none;
  }

  .el-dialog__header {
    background-color: transparent;
  }
  .el-dialog__body {
    padding: 0;
    height: calc(100% - 60px);
    max-height: fit-content;
    overflow-y: hidden;
    background-color: transparent;
    padding: 0px 80px;
    .dialog-content {
      width: 100%;
      height: 100%;
      background-color: transparent;

      .el-table {
        background-color: transparent;
        border: 1px solid #202d4c;
        .el-table__header-wrapper {
          background-color: transparent !important;
        }
        .el-table__body-wrapper {
          background-color: transparent !important;
        }
        tr {
          background-color: transparent !important;
        }
        td {
          background-color: transparent !important;
        }
      }
    }
  }
}
:deep(.search-box) {
  display: flex;
  flex-wrap: wrap;

  .taskInput {
    .el-input {
      width: 180px;
    }
    .el-cascader {
      line-height: 35px;
      .el-input__inner {
        height: 35px !important;
      }
    }
  }

  .el-button {
    background-image: url("@/assets/images/btn.png");
    background-repeat: no-repeat;
    background-size: 100% 100%;
    height: 35px;
  }
  .search-btn {
    // margin: 10px 0;
    margin-left: auto;
  }
  .el-select {
    .el-input .el-input__inner {
      border-color: rgba(133, 145, 206, 0.5);
      width: 180px;
      height: 35px;
    }
  }

  .el-date-editor {
    width: 300px;
    height: 35px;
    background-color: #14233e;
    border-color: rgba(133, 145, 206, 0.5);
    .el-input__icon {
      transform: translateY(-2px);
    }
    .el-range-input {
      background-color: #14233e;
      color: rgba(255, 255, 255, 0.8);
    }
    .el-range-separator {
      color: rgba(255, 255, 255, 0.8);
      transform: translateY(-2px);
    }
  }
  .el-input .el-input__inner {
    border-color: rgba(133, 145, 206, 0.5);
    border-radius: 4px;
    color: rgba(255, 255, 255, 0.8);
  }
}
.search-box {
  margin: 5px 0 15px;
}

.search-box .el-input {
  // width: 120px;
  height: 35px;
}
.tableContent {
  width: 100%;
  height: calc(100% - 56px - 60px);
}
</style>
<style lang="scss">
.search-form-tree {
  color: #fff !important;
  background: transparent !important;
  .el-tree-node__content:hover {
    background: transparent !important;
    .el-tree-node__label {
      color: #ffe3a6;
    }
  }
}
</style>
