<!--
 * @Author: ycw
 * @Date: 2020-05-29 15:45:15
 * @LastEditors: ycw
 * @LastEditTime: 2020-05-29 17:16:04
 * @Description:
-->
<template>
  <div class="sino-top-nav">
    <div class="nav-tool nav-left-tool">
      <span class="iconfont icon-qianjin" @click="scorll('left')"></span>
    </div>
    <div class="nav-tool nav-middle-tool" ref="scrollMenu">
      <div class="scroll" :style="scorllwidth">
        <span class="tool-menu-bar" v-for="(menu, index) in list" :key="index">
          <div class="menu" :class="[menu.active ? 'active' : '']">
            <span class="menu-text" @click="switchNav(menu)">{{ menu.menuName }}</span>
            <i class="el-icon-close" @click="deleteNav(index, menu.menuHref)"></i>
          </div>
        </span>
      </div>
    </div>
    <div class="nav-tool nav-right-tool">
      <span class="icon-qianjin iconfont" style="transform: rotate(180deg)" @click="scorll('right')"></span>
      <span class="iconfont icon-qingchu hover-style" @click="delAll"></span>
    </div>
  </div>
</template>
<script>
export default {
  data() {
    return {}
  },
  computed: {
    list() {
      return this.$store.state.menuList
    },
    scorllwidth() {
      return { width: 20 * 100 + 'px' }
    }
  },
  methods: {
    scorll() {},
    delAll() {
      this.$store.commit('delAllMenuItem', [])
      this.$router.push('/')
    },
    switchNav(item) {
      this.$router.push('/' + item.menuHref)
      this.$forceUpdate()
    },
    deleteNav(index, name) {
      this.$store.commit('deleteMenuItem', index)
    }
  }
}
</script>
<style lang="scss" scoped>
.active {
  background: #7d93c4 !important;
  color: rgba(255, 255, 255, 1) !important;
}
.hover-style:hover {
  background-color: #d9dad9 !important;
}
.sino-top-nav {
  height: 32px;
  line-height: 32px;
  padding: 6px 6px 0 6px;
  .nav-left-tool {
    width: 48px;
    text-align: center;
    background: rgba(255, 255, 255, 1);
  }
  .nav-middle-tool {
    width: calc(100% - 88px - 44px - 4px);
    .scroll {
      .tool-menu-bar {
        .menu {
          width: 105px;
          display: inline-block;
          margin-left: 6px;
          font-size: 14px;
          text-align: center;
          color: rgba(153, 153, 153, 1);
          background: rgba(255, 255, 255, 1);
          border-radius: 2px;
          i {
            margin-left: 5px;
          }
        }
      }
    }
  }
  .nav-right-tool {
    width: 88px;
    background: rgba(255, 255, 255, 1);
    span {
      width: 44px;
      display: inline-block;
      text-align: center;
    }
  }
  .nav-tool {
    vertical-align: top;
    display: inline-block;
    cursor: pointer;
    color: rgba(153, 153, 153, 1);
    white-space: nowrap;

    .iconfont {
      font-size: 14px;
    }
  }
}
</style>
