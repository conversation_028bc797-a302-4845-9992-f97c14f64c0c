<template>
  <div class="baseInfoComponent">
    <table class="base-table">
      <tr style="width: 100%;" v-for="(item, index) in baseTableInfo" :key="index">
        <div v-if="item.envSign.includes(hospitalNode)" class="base-tr" :class="item.field === 'centerLine' ? 'center-tr' : ''">
          <td class="label-td" :class="item.field === 'centerLine' ? 'center-td' : ''">{{ item.label }}</td>
          <td class="value-td" v-if="item.type === 'img'">
            <el-image style="width: 150px; height: 150px" :src="item.value" :preview-src-list="[item.value]"> </el-image>
          </td>
          <td class="value-td" :class="item.field === 'centerLine' ? 'center-td' : ''" v-else>{{ item.value }}</td>
        </div>
      </tr>
    </table>
  </div>
</template>

<script>
import { baseTableInfo } from '@/assets/common/dict.js'
import { lookUpByModelCode } from '@/utils/spaceManage.js'
export default {
  name: 'baseInfoComponent',
  props: {
    roomData: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      hospitalNode: __PATH.VUE_APP_HOSPITAL_NODE,
      baseTableInfo: baseTableInfo
    }
  },
  mounted() {
    this.getSpaceBaseInfo()
  },
  methods: {
    getSpaceBaseInfo() {
      const params = {
        modelCode: this.roomData.modelCode
      }
      lookUpByModelCode(params).then((res) => {
        const data = res.data
        if (data.code === 200) {
          const resultData = data.data
          this.baseTableInfo.forEach((baseItem) => {
            for (const key in resultData) {
              if (key === baseItem.field) {
                key === 'qrcodeBase64' ? (baseItem.value = 'data:image/png;base64,' + resultData[key]) : (baseItem.value = resultData[key])
              }
            }
          })
          console.log(this.baseTableInfo)
          this.$emit('roomEvent', {
            type: 'base',
            roomName: resultData.localSpaceName
          })
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.baseInfoComponent {
  overflow: hidden;
  .base-table {
    width: 100%;
    table-layout: fixed;
    .base-tr {
      width: 100%;
      td {
        padding: 7px 0;
        ::v-deep .el-image__error {
          background: center;
        }
      }
      .label-td {
        width: 100px;
        text-align: right;
        font-size: 0.875rem;
        font-family: PingFang-SC-Medium, PingFang-SC;
        font-weight: 500;
        color: #7eaef9;
        white-space: nowrap;
        vertical-align: top;
      }
      .value-td {
        padding-left: 20px !important;
        font-size: 0.875rem;
        font-family: PingFang-SC-Medium, PingFang-SC;
        font-weight: 500;
        color: #ffffff;
        word-break: break-all;
      }
    }
    .center-tr {
      border: 1px solid #2e4989;
      .center-td {
        padding: 0;
      }
    }
  }
}
</style>
