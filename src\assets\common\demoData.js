import moment from 'moment'
const nowH = moment().format('H')
const nowD = moment().format('D')
const nowM = moment().format('M')
const demoData = {
  nowDataNum() {
    return {
      day: nowH,
      month: nowD,
      year: nowM
    }
  },
  keyEquipmentList() {
    return {
      '0100502_2645984': {
        day: [
          {
            name: 0,
            value: 77
          },
          {
            name: 1,
            value: 60
          },
          {
            name: 2,
            value: 73
          },
          {
            name: 3,
            value: 61
          },
          {
            name: 4,
            value: 34
          },
          {
            name: 5,
            value: 29
          },
          {
            name: 6,
            value: 17
          },
          {
            name: 7,
            value: 62
          },
          {
            name: 8,
            value: 60
          },
          {
            name: 9,
            value: 48
          },
          {
            name: 10,
            value: 68
          },
          {
            name: 11,
            value: 14
          },
          {
            name: 12,
            value: 14
          },
          {
            name: '13',
            value: 44
          },
          {
            name: '14',
            value: 36
          },
          {
            name: '15',
            value: 46
          },
          {
            name: '16',
            value: 62
          },
          {
            name: '17',
            value: 56
          },
          {
            name: '18',
            value: 74
          },
          {
            name: '19',
            value: 70
          },
          {
            name: '20',
            value: 48
          },
          {
            name: '21',
            value: 68
          },
          {
            name: '22',
            value: 58
          },
          {
            name: '23',
            value: 77
          }
        ],
        month: [
          {
            name: '1',
            value: 254
          },
          {
            name: '2',
            value: 563
          },
          {
            name: '3',
            value: 253
          },
          {
            name: '4',
            value: 262
          },
          {
            name: '5',
            value: 888
          },
          {
            name: '6',
            value: 873
          },
          {
            name: '7',
            value: 882
          },
          {
            name: '8',
            value: 809
          },
          {
            name: '9',
            value: 640
          },
          {
            name: '10',
            value: 311
          },
          {
            name: '11',
            value: 736
          },
          {
            name: '12',
            value: 699
          },
          {
            name: '13',
            value: 705
          },
          {
            name: '14',
            value: 520
          },
          {
            name: '15',
            value: 884
          },
          {
            name: '16',
            value: 877
          },
          {
            name: '17',
            value: 788
          },
          {
            name: '18',
            value: 866
          },
          {
            name: '19',
            value: 459
          },
          {
            name: '20',
            value: 888
          },
          {
            name: '21',
            value: 792
          },
          {
            name: '22',
            value: 557
          },
          {
            name: '23',
            value: 333
          },
          {
            name: '24',
            value: 810
          },
          {
            name: '25',
            value: 761
          },
          {
            name: '26',
            value: 852
          },
          {
            name: '27',
            value: 505
          },
          {
            name: '28',
            value: 343
          },
          {
            name: '29',
            value: 606
          },
          {
            name: '30',
            value: 466
          },
          {
            name: '31',
            value: 505
          }
        ],
        year: [
          {
            name: '1',
            value: 17481
          },
          {
            name: '2',
            value: 16211
          },
          {
            name: '3',
            value: 20157
          },
          {
            name: '4',
            value: 16689
          },
          {
            name: '5',
            value: 23999
          },
          {
            name: '6',
            value: 23153
          },
          {
            name: '7',
            value: 22017
          },
          {
            name: '8',
            value: 18591
          },
          {
            name: '9',
            value: 15140
          },
          {
            name: '10',
            value: 24392
          },
          {
            name: '11',
            value: 21756
          },
          {
            name: '12',
            value: 17050
          }
        ]
      },
      '0100502_2664088': {
        day: [
          {
            name: '0',
            value: 17
          },
          {
            name: '1',
            value: 49
          },
          {
            name: '2',
            value: 30
          },
          {
            name: '3',
            value: 15
          },
          {
            name: '4',
            value: 79
          },
          {
            name: '5',
            value: 67
          },
          {
            name: '6',
            value: 30
          },
          {
            name: '7',
            value: 20
          },
          {
            name: '8',
            value: 70
          },
          {
            name: '9',
            value: 46
          },
          {
            name: '10',
            value: 23
          },
          {
            name: '11',
            value: 72
          },
          {
            name: '12',
            value: 59
          },
          {
            name: '13',
            value: 68
          },
          {
            name: '14',
            value: 41
          },
          {
            name: '15',
            value: 49
          },
          {
            name: '16',
            value: 70
          },
          {
            name: '17',
            value: 35
          },
          {
            name: '18',
            value: 10
          },
          {
            name: '19',
            value: 26
          },
          {
            name: '20',
            value: 16
          },
          {
            name: '21',
            value: 78
          },
          {
            name: '22',
            value: 62
          },
          {
            name: '23',
            value: 72
          }
        ],
        month: [
          {
            name: '1',
            value: 788
          },
          {
            name: '2',
            value: 618
          },
          {
            name: '3',
            value: 734
          },
          {
            name: '4',
            value: 733
          },
          {
            name: '5',
            value: 677
          },
          {
            name: '6',
            value: 586
          },
          {
            name: '7',
            value: 631
          },
          {
            name: '8',
            value: 730
          },
          {
            name: '9',
            value: 692
          },
          {
            name: '10',
            value: 770
          },
          {
            name: '11',
            value: 558
          },
          {
            name: '12',
            value: 316
          },
          {
            name: '13',
            value: 410
          },
          {
            name: '14',
            value: 782
          },
          {
            name: '15',
            value: 626
          },
          {
            name: '16',
            value: 509
          },
          {
            name: '17',
            value: 322
          },
          {
            name: '18',
            value: 285
          },
          {
            name: '19',
            value: 892
          },
          {
            name: '20',
            value: 532
          },
          {
            name: '21',
            value: 506
          },
          {
            name: '22',
            value: 873
          },
          {
            name: '23',
            value: 412
          },
          {
            name: '24',
            value: 318
          },
          {
            name: '25',
            value: 835
          },
          {
            name: '26',
            value: 373
          },
          {
            name: '27',
            value: 398
          },
          {
            name: '28',
            value: 788
          },
          {
            name: '29',
            value: 695
          },
          {
            name: '30',
            value: 739
          },
          {
            name: '31',
            value: 497
          }
        ],
        year: [
          {
            name: '1',
            value: 23434
          },
          {
            name: '2',
            value: 23866
          },
          {
            name: '3',
            value: 15369
          },
          {
            name: '4',
            value: 20796
          },
          {
            name: '5',
            value: 19102
          },
          {
            name: '6',
            value: 24068
          },
          {
            name: '7',
            value: 22291
          },
          {
            name: '8',
            value: 22246
          },
          {
            name: '9',
            value: 22532
          },
          {
            name: '10',
            value: 22677
          },
          {
            name: '11',
            value: 18267
          },
          {
            name: '12',
            value: 15827
          }
        ]
      },
      '0100502_2674536': {
        day: [
          {
            name: '0',
            value: 38
          },
          {
            name: '1',
            value: 28
          },
          {
            name: '2',
            value: 32
          },
          {
            name: '3',
            value: 20
          },
          {
            name: '4',
            value: 26
          },
          {
            name: '5',
            value: 38
          },
          {
            name: '6',
            value: 39
          },
          {
            name: '7',
            value: 41
          },
          {
            name: '8',
            value: 24
          },
          {
            name: '9',
            value: 18
          },
          {
            name: '10',
            value: 39
          },
          {
            name: '11',
            value: 54
          },
          {
            name: '12',
            value: 66
          },
          {
            name: '13',
            value: 39
          },
          {
            name: '14',
            value: 25
          },
          {
            name: '15',
            value: 11
          },
          {
            name: '16',
            value: 37
          },
          {
            name: '17',
            value: 44
          },
          {
            name: '18',
            value: 42
          },
          {
            name: '19',
            value: 14
          },
          {
            name: '20',
            value: 54
          },
          {
            name: '21',
            value: 78
          },
          {
            name: '22',
            value: 51
          },
          {
            name: '23',
            value: 51
          }
        ],
        month: [
          {
            name: '1',
            value: 716
          },
          {
            name: '2',
            value: 498
          },
          {
            name: '3',
            value: 412
          },
          {
            name: '4',
            value: 852
          },
          {
            name: '5',
            value: 854
          },
          {
            name: '6',
            value: 834
          },
          {
            name: '7',
            value: 434
          },
          {
            name: '8',
            value: 843
          },
          {
            name: '9',
            value: 828
          },
          {
            name: '10',
            value: 257
          },
          {
            name: '11',
            value: 270
          },
          {
            name: '12',
            value: 741
          },
          {
            name: '13',
            value: 704
          },
          {
            name: '14',
            value: 403
          },
          {
            name: '15',
            value: 513
          },
          {
            name: '16',
            value: 458
          },
          {
            name: '17',
            value: 782
          },
          {
            name: '18',
            value: 271
          },
          {
            name: '19',
            value: 515
          },
          {
            name: '20',
            value: 417
          },
          {
            name: '21',
            value: 780
          },
          {
            name: '22',
            value: 886
          },
          {
            name: '23',
            value: 687
          },
          {
            name: '24',
            value: 682
          },
          {
            name: '25',
            value: 856
          },
          {
            name: '26',
            value: 368
          },
          {
            name: '27',
            value: 452
          },
          {
            name: '28',
            value: 541
          },
          {
            name: '29',
            value: 440
          },
          {
            name: '30',
            value: 711
          },
          {
            name: '31',
            value: 638
          }
        ],
        year: [
          {
            name: '1',
            value: 20349
          },
          {
            name: '2',
            value: 18572
          },
          {
            name: '3',
            value: 21058
          },
          {
            name: '4',
            value: 20379
          },
          {
            name: '5',
            value: 22386
          },
          {
            name: '6',
            value: 18134
          },
          {
            name: '7',
            value: 19035
          },
          {
            name: '8',
            value: 20246
          },
          {
            name: '9',
            value: 21217
          },
          {
            name: '10',
            value: 24533
          },
          {
            name: '11',
            value: 18980
          },
          {
            name: '12',
            value: 20604
          }
        ]
      },
      '0100502_2639194': {
        day: [
          {
            name: '0',
            value: 53
          },
          {
            name: '1',
            value: 57
          },
          {
            name: '2',
            value: 50
          },
          {
            name: '3',
            value: 35
          },
          {
            name: '4',
            value: 58
          },
          {
            name: '5',
            value: 68
          },
          {
            name: '6',
            value: 12
          },
          {
            name: '7',
            value: 80
          },
          {
            name: '8',
            value: 52
          },
          {
            name: '9',
            value: 63
          },
          {
            name: '10',
            value: 69
          },
          {
            name: '11',
            value: 17
          },
          {
            name: '12',
            value: 16
          },
          {
            name: '13',
            value: 27
          },
          {
            name: '14',
            value: 32
          },
          {
            name: '15',
            value: 47
          },
          {
            name: '16',
            value: 24
          },
          {
            name: '17',
            value: 69
          },
          {
            name: '18',
            value: 56
          },
          {
            name: '19',
            value: 75
          },
          {
            name: '20',
            value: 33
          },
          {
            name: '21',
            value: 77
          },
          {
            name: '22',
            value: 52
          },
          {
            name: '23',
            value: 15
          }
        ],
        month: [
          {
            name: '1',
            value: 300
          },
          {
            name: '2',
            value: 547
          },
          {
            name: '3',
            value: 589
          },
          {
            name: '4',
            value: 544
          },
          {
            name: '5',
            value: 482
          },
          {
            name: '6',
            value: 581
          },
          {
            name: '7',
            value: 469
          },
          {
            name: '8',
            value: 283
          },
          {
            name: '9',
            value: 438
          },
          {
            name: '10',
            value: 763
          },
          {
            name: '11',
            value: 752
          },
          {
            name: '12',
            value: 655
          },
          {
            name: '13',
            value: 481
          },
          {
            name: '14',
            value: 564
          },
          {
            name: '15',
            value: 644
          },
          {
            name: '16',
            value: 318
          },
          {
            name: '17',
            value: 538
          },
          {
            name: '18',
            value: 279
          },
          {
            name: '19',
            value: 480
          },
          {
            name: '20',
            value: 763
          },
          {
            name: '21',
            value: 439
          },
          {
            name: '22',
            value: 622
          },
          {
            name: '23',
            value: 515
          },
          {
            name: '24',
            value: 371
          },
          {
            name: '25',
            value: 356
          },
          {
            name: '26',
            value: 866
          },
          {
            name: '27',
            value: 729
          },
          {
            name: '28',
            value: 280
          },
          {
            name: '29',
            value: 480
          },
          {
            name: '30',
            value: 727
          },
          {
            name: '31',
            value: 465
          }
        ],
        year: [
          {
            name: '1',
            value: 18192
          },
          {
            name: '2',
            value: 17223
          },
          {
            name: '3',
            value: 20585
          },
          {
            name: '4',
            value: 23828
          },
          {
            name: '5',
            value: 19160
          },
          {
            name: '6',
            value: 23214
          },
          {
            name: '7',
            value: 20034
          },
          {
            name: '8',
            value: 23087
          },
          {
            name: '9',
            value: 18860
          },
          {
            name: '10',
            value: 23218
          },
          {
            name: '11',
            value: 18648
          },
          {
            name: '12',
            value: 23520
          }
        ]
      },
      random: {
        day: generateData('day'),
        month: generateData('month'),
        year: generateData('year')
      }
    }
  }
}
function generateData(type) {
  const currentDate = new Date()
  const data = []

  if (type === 'day') {
    for (let hour = 0; hour < 24; hour++) {
      const randomValue = Math.floor(Math.random() * (80 - 10 + 1)) + 10
      data.push({ name: `${hour}`, value: randomValue })
    }
  } else if (type === 'month') {
    const lastDay = new Date(currentDate.getFullYear(), currentDate.getMonth() + 1, 0).getDate()
    for (let day = 1; day <= lastDay; day++) {
      const randomValue = Math.floor(Math.random() * (900 - 250 + 1)) + 250
      data.push({ name: `${day}`, value: randomValue })
    }
  } else if (type === 'year') {
    for (let month = 1; month <= 12; month++) {
      const randomValue = Math.floor(Math.random() * (25000 - 15000 + 1)) + 15000
      data.push({ name: `${month}`, value: randomValue })
    }
  }

  return data
}
export default demoData
