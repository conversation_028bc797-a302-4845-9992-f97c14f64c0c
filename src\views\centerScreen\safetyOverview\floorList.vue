<template>
  <!-- 楼层隐患清单 -->
  <div class="content">
    <div class="title">
      <div @click="backToWPFhome()"><i class="el-icon-arrow-left"></i>返回</div>
    </div>
    <div class="content-left">
      <div class="echarts-center">
        <div class="bg-title">
          <span>部门隐患分析</span>
        </div>
        <div class="bg-content" style="height: calc(100% - 2.5rem)">
          <el-table
            :data="tableData"
            height="100%"
            :cell-style="$tools.setCell(3)"
            v-loading="tableLoading"
            :header-cell-style="$tools.setHeaderCell(3)"
            style="width: 100%"
            element-loading-background="rgba(0, 0, 0, 0.2)"
            @row-dblclick="selectConfigRowData"
          >
            <el-table-column fixed prop="deptName" show-overflow-tooltip label="部门名称"></el-table-column>
            <el-table-column fixed prop="generalCount" show-overflow-tooltip label="一般隐患"></el-table-column>
            <el-table-column fixed prop="greatCount" show-overflow-tooltip label="重点隐患"></el-table-column>
            <el-table-column fixed prop="rectifiedCount" show-overflow-tooltip label="已整改数"></el-table-column>
          </el-table>
        </div>
      </div>
    </div>
    <!-- <el-button class="sino-button-sure" @click="hazardListChange">showDialog</el-button> -->
    <template v-if="hazardListShow">
      <hazardList ref="hazardList" type="danger" :dialogShow="hazardListShow" @configCloseDialog="configCloseDialog"></hazardList>
    </template>
  </div>
</template>

<script>
import { GetDeptDangerStatistics } from '@/utils/centerScreenApi'
import hazardList from './components/hazardList'
export default {
  name: 'floorList',
  components: {
    hazardList
  },
  data() {
    return {
      hazardListShow: false, // 隐患清单
      tableLoading: false,
      tableData: []
    }
  },
  created() {
    if (Object.hasOwn(this.$route.query, 'localtion')) {
      this.getTypeAnalysisTable(this.$route.query.localtion || '')
    }
  },
  mounted() {
    try {
      window.chrome.webview.addEventListener('message', (event) => {
        const data = JSON.parse(event.data)
        if (data.type === 'area') {
          this.getTypeAnalysisTable(data.localtion || '')
        } else if (data.type === 'tag') {
          this.hazardListChange({ placeIds: data.localtion })
        }
      })
    } catch (error) {}
  },
  methods: {
    selectConfigRowData(row) {
      if (!this.hazardListShow) {
        this.hazardListChange({ deptCode: row.deptCode })
        return
      }
      this.$refs.hazardList.getWorkOrderTableData({ deptCode: row.deptCode })
    },
    // 隐患清单
    hazardListChange(params) {
      this.hazardListShow = !this.hazardListShow
      this.$nextTick().then(() => {
        if (this.hazardListShow) {
          this.$nextTick(() => {
            this.$refs.hazardList.getWorkOrderTableData(params)
          })
        }
        try {
          window.chrome.webview.hostObjects.sync.bridge.ShowTable(true)
        } catch (error) {}
      })
    },
    configCloseDialog() {
      this.hazardListShow = false
      try {
        window.chrome.webview.hostObjects.sync.bridge.ShowTable(false)
      } catch (error) {}
    },
    getTypeAnalysisTable(parentId) {
      this.tableLoading = true
      GetDeptDangerStatistics({ parentId }).then((res) => {
        const data = res.data
        this.tableLoading = false
        if (data.code === '200') {
          const arr = data.data
          if (arr.length) {
            this.tableData = data.data
          } else {
            this.tableData = []
          }
        }
      })
    },
    backToWPFhome() {
      try {
        window.chrome.webview.hostObjects.sync.bridge.Back('safetyOverview')
      } catch (error) {}
    }
  }
}
</script>

<style lang="scss" scoped>
.content {
  width: 100%;
  height: 100%;
  position: relative;
  display: flex;
  .title {
    height: 30px;
    position: relative;
    div {
      cursor: pointer;
      position: absolute;
      top: 5px;
      left: 0;
      width: 80px;
      height: 30px;
      line-height: 30px;
      text-align: center;
      color: #618ad3;
      font-size: 0.875rem;
      white-space: nowrap;
      background-image: url(../../../assets/images/peace/btn-back.png);
      background-size: 100% 100%;
      background-repeat: no-repeat;
    }
  }
  .content-left {
    width: 20%;
    height: calc(100% - 35px);
    padding-top: 35px;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    padding-top: 5%;
  }
  .echarts-center {
    width: 100%;
    height: 40%;
    background: url('~@/assets/images/peace/bg-40.png') no-repeat;
    background-size: 100% 100%;
    padding: 2px;
    box-sizing: border-box;
    ::v-deep .el-table {
      border: none !important;
      .el-table__header .el-table__cell {
        padding: 5px 0 !important;
        color: #fff;
      }
      .el-table__body {
        tr {
          background: center;
        }
        td.el-table__cell,
        th.el-table__cell.is-leaf {
          border-right: 2px solid #0a164e;
          border-bottom: 2px solid #0a164e;
          background: rgba(56, 103, 180, 0.2);
          color: #fff;
        }
      }
    }
  }
  .bg-title {
    margin-top: 5px;
    // height: 23%;
    height: 2.5rem;
    line-height: 2.5rem;
    // color: #d4e3f9;
    color: #dceaff;
    padding: 0 1rem 0 3rem;
    font-family: TRENDS;
    /*设置为伸缩容器*/
    display: -webkit-box;
    display: -moz-box;
    display: -ms-flexbox;
    display: -webkit-flex;
    display: flex;
    /*垂直居中*/
    -webkit-box-align: center; /*旧版本*/
    -moz-box-align: center; /*旧版本*/
    -ms-flex-align: center; /*混合版本*/
    -webkit-align-items: center; /*新版本*/
    align-items: center; /*新版本*/
    justify-content: space-between;
  }
  .bg-content {
    position: relative;
    box-sizing: border-box;
    padding: 10px 10px 15px 10px;
    width: 100%;
    height: 77%;
    #dangerStateEcharts,
    #dangerNumEchart,
    #dangerAnalysisEcharts,
    #levelAnalysisEcharts,
    #riskTypeAnalysisEcharts,
    #dayInspectionEcharts,
    #weekInspectionEcharts {
      width: 100%;
      height: 100%;
      z-index: 2;
    }
    .alarm-bg {
      width: 12.5rem;
      height: 12.5rem;
      position: absolute;
      top: calc(50% - 6.4rem);
      left: 0%;
      background: url('~@/assets/images/peace/people-right-top-bg.png') no-repeat;
      background-size: 100% 100%;
    }
  }
}
</style>
