<template>
  <div class="moveComponent">
    <div class="module-container" style="height: calc(22%)">
      <div class="module-header">
        <div class="title-left" style="width: 60%">
          <p class="title-left-text" :class="{ showFloorName: !['Kongtiao', 'Electricity'].includes(roomData.tabName) }">
            <span v-if="!['Kongtiao', 'Electricity'].includes(roomData.tabName)">{{ roomData.title ? `${roomData.title} - ` : '' }}</span
            >设备台账
          </p>
        </div>
        <div class="title-right">
          <div v-if="roomData.projectCode == 'IEMC-Electricity'" class="toggle" style="margin-right: 10px">
            <div :class="isKeyDevice ? 'active-type' : ''" @click="() => (isKeyDevice = !isKeyDevice)">重点设备</div>
          </div>
          <div class="title-detail" @click="allTableChange('assets')">详情</div>
        </div>
      </div>
      <div class="module-content" style="height: calc(100% - 44px)">
        <div class="statistics_top_new">
          <div v-for="(item, index) in statisticsData" :key="index" class="statistics_top_item">
            <img src="@/assets/images/qhdsys/statistics-new.png" alt="" />
            <div class="statistics_top_content">
              <p class="statistics_top_title">{{ item.name }}</p>
              <div class="font_bg">
                <p class="color_font" :style="{ color: item.key == 'abnormalDeviceTotal' ? '#FF2D55' : '' }">{{ deviceAccountStatistics[item.key] || 0 }}</p>
                <p class="color_unit">{{ item?.unit ?? '' }}</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div v-show="!isKeyDevice" class="module-container" style="height: calc(25%)">
      <div class="module-header">
        <div class="title-left">
          <p class="title-left-text">设备状态</p>
        </div>
        <div class="title-right">
          <div class="toggle">
            <!-- 机房模式不根据建筑过滤 不显示设备类型及建筑标签 -->
            <div v-if="roomData.isSpace == '0'" :class="sortType == '1' ? 'active-type' : ''" @click="changeSortType('1')">设备类型</div>
            <div v-if="roomData.ssmType < 5 && roomData.isSpace == '0'" :class="sortType == '2' ? 'active-type' : ''" @click="changeSortType('2')">建筑</div>
          </div>
        </div>
      </div>
      <div class="module-content" style="height: calc(100% - 48px)">
        <div id="EChart" style="height: 100%"></div>
      </div>
    </div>
    <div v-show="!isKeyDevice" class="module-container" style="height: calc(25%)">
      <div class="module-header">
        <div class="title-left">
          <!-- <p class="title-left-icon"></p> -->
          <p class="title-left-text">设备监测 {{ isSelectedTypeName }}</p>
        </div>
        <div class="title-right">
          <div class="icon-box">
            <!-- <img v-if="sortType == 1" src="../../../assets/images/filter.png" class="filter" @click="showCheckGroup = !showCheckGroup" /> -->
            <div v-show="showCheckGroup" class="panel-s">
              <el-checkbox-group v-model="checkList" fill="#52FFFC" @change="checkBoxChanged">
                <el-checkbox v-for="item in flowList" :key="item.value" :label="item.label"></el-checkbox>
              </el-checkbox-group>
            </div>
            <img src="../../../assets/images/order_more.png" class="order-more" @click="allTableChange('entity')" />
          </div>
        </div>
      </div>
      <div class="module-content" style="height: calc(100% - 48px)">
        <el-table
          :key="sortType"
          v-el-table-infinite-scroll="tableLoadMore"
          v-scrollHideTooltip
          v-loading="deviceTableLoading"
          class="table-center-transfer"
          :data="deviceList"
          height="100%"
          :cell-style="{ padding: ' 8px', backgroundColor: 'transparent', border: 'none', padding: '3px' }"
          :header-cell-style="{ background: '#8591CE26!important', color: '#8BDDF5FF', padding: '4px 8px', fontWeight: 'bold' }"
          style="width: 100%"
          element-loading-background="rgba(0, 0, 0, 0.2)"
          @row-click="tableRowClick"
        >
          <!-- 双击会调用两次单击事件 取消双击 在单击事件中判断调用双击事件 -->
          <!-- @row-dblclick="tabledblClick" -->
          <el-table-column v-for="(column, index) in tableColumn" :key="index" :prop="column.prop" :label="column.label" :min-width="column.minWidth" show-overflow-tooltip>
            <template slot-scope="scope">
              <table-render v-if="column.render" :sc="scope" :row="scope.row" :render="column.render"></table-render>
              <div v-else-if="!column.formatter">
                {{ scope.row[column.prop] }}
              </div>
              <div v-else>
                {{ column.formatter(scope.row) }}
              </div>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </div>
    <div v-show="!isKeyDevice" class="module-container" style="height: calc(28%)">
      <div class="module-header">
        <div class="title-left">
          <p class="title-left-text" style="cursor: pointer" :style="{ color: isPipeline == true ? '#a6afbf' : '#fff' }" @click="changePipelineShow(0)">设备显示</p>
          <p v-if="showPipeline" class="title-left-text" style="cursor: pointer; padding-left: 16px" :style="{ color: isPipeline == true ? '#fff' : '#a6afbf' }" @click="changePipelineShow(1)">
            管线显示
          </p>
        </div>
        <div class="title-right">
          <!-- <div class="toggle" style="margin-right: 10px">
            <div :class="isPipeline ? 'active-type' : ''" @click="changePipelineShow">管线显示</div>
          </div> -->
        </div>
      </div>
      <div class="module-content" style="height: calc(100% - 48px)">
        <allCheckbox :visible="!isPipeline" :roomData="sendCheckData" />
        <pipelineSelect :visible="isPipeline" :roomData="pipelineData" />
      </div>
    </div>
    <div v-show="isKeyDevice" class="module-container" style="height: calc(78%)">
      <div class="module-header">
        <div class="title-left">
          <p class="title-left-text">设备列表</p>
        </div>
        <div class="title-right">
          <div class="title-detail" @click="() => (isOpenKeyDevice = !isOpenKeyDevice)">{{ isOpenKeyDevice ? '收起' : '展开' }}</div>
        </div>
      </div>
      <div v-scrollbarHover class="module-content device-list" style="height: calc(100% - 44px)">
        <div v-if="!keyDeviceList || !keyDeviceList.length" class="empty-div"><span>暂无数据</span></div>
        <div v-for="item in keyDeviceList" :key="item.surveyCode" class="device-list-item">
          <div class="item-title">
            <span style="flex: 1; margin-right: 10px">{{ item.surveyName }}</span>
            <span class="title-detail" @click="openDeviceDetail(item.surveyCode)">详情</span>
          </div>
          <div class="item-status">
            <div class="status-item">
              <p class="status-item-name">启停状态</p>
              <p class="status-item-value">{{ item.run }}</p>
            </div>
            <div class="status-item">
              <p class="status-item-name">离线状态</p>
              <p class="status-item-value">{{ item.line }}</p>
            </div>
            <div class="status-item">
              <p class="status-item-name">故障状态</p>
              <p class="status-item-value" :style="{color: item.warn === '报警' ? 'rgb(255, 45, 85)' : 'rgb(97, 226, 157)'}">{{ item.warn }}</p>
            </div>
          </div>
          <div v-show="isOpenKeyDevice" class="item-table">
            <el-table class="bottom-el-table" :data="item.newParamList" height="calc(100%)" style="width: 100%" popper-append-to-body element-loading-background="rgba(0, 0, 0, 0.2)">
              <el-table-column prop="paramName" show-overflow-tooltip label="名称"></el-table-column>
              <el-table-column prop="voltage" show-overflow-tooltip label="电流(A)"></el-table-column>
              <el-table-column prop="current" show-overflow-tooltip label="电压(V)"></el-table-column>
              <el-table-column prop="lineCurrent" show-overflow-tooltip label="线电压(V)"></el-table-column>
            </el-table>
          </div>
        </div>
      </div>
    </div>
    <template v-if="allTableComponentListShow">
      <allTableComponentList ref="allTableComponentList" :dialogData="tableCompenentData" :dialogShow="allTableComponentListShow" :assetsList="assetsList" @configCloseDialog="configCloseTableDialog"></allTableComponentList>
    </template>
    <template v-if="deviceDetailsListShow">
      <deviceDialog ref="deviceDetailDialog" :dialogData="deviceDetailInfo" :dialogShow="deviceDetailsListShow" @deviceCloseDialog="() => changeDeviceDetailDialogShow(false)"></deviceDialog>
    </template>
  </div>
</template>

<script lang="jsx">
import { getMonitorDeviceCount, getGroupByEntityType, querySurveyBySpaceId, querySurveyListByPage, getGasList, queryAllSurveyParamList } from '@/utils/spaceManage'
import { refrigeratorParams, monitorTypeList, hasModelCodeFilterProjectName } from '@/assets/common/dict.js'
import icon_5 from '@/assets/images/icon-5.png'
import icon_6 from '@/assets/images/icon-6.png'
import icon_2 from '@/assets/images/icon-2.png'
import * as echarts from 'echarts'
import allCheckbox from './components/allCheckbox.vue'
import pipelineSelect from './components/pipelineSelect.vue'
import tableRender from '../components/tableRender.vue'
import allTableComponentList from '../components/allTableComponentList.vue'
export default {
  name: 'moveComponent',
  components: {
    allTableComponentList,
    allCheckbox,
    tableRender,
    pipelineSelect,
    deviceDialog: () => import('./components/deviceDetailDialog.vue')
  },
  props: {
    roomData: {
      type: Object,
      default: () => {}
    },
    assetsList: {
      type: Array,
      default: () => {}
    }
  },
  data() {
    return {
      icon_2,
      icon_5,
      icon_6,
      clickCount: 0,
      clickTimer: null,
      sendCheckData: {}, // 给checkbox组件传递的数据
      pipelineData: {}, // 给管线组件传递的数据
      deviceTableLoading: false,
      workOrderStatisticsShow: true,
      dangerStateAnalysisShow: false,
      tagCurrent: 1,
      tableCompenentData: {}, // 一站式弹窗数据
      currentMenuData: {}, // 当前选中的设备类型数据
      deviceAccountStatistics: {}, // 设备台账统计
      deviceTypeList: [], // 设备类型列表
      deviceList: [], // 设备列表
      showCheckGroup: false,
      allTableComponentListShow: false,
      deviceDetailsListShow: false,
      deviceDetailInfo: {},
      checkList: ['全部'],
      flowList: [
        {
          label: '全部',
          value: ''
        },
        {
          label: '正常',
          value: '0'
        },
        {
          label: '异常',
          value: '10'
        },
        {
          label: '离线',
          value: '6'
        }
      ],
      statisticsData: [
        {
          name: '设备总数',
          key: 'deviceTotal',
          unit: '台'
        },
        // {
        //   name: '监测数量',
        //   key: 'deviceMonitorTotal',
        //   unit: '台'
        // },
        {
          name: '离线设备数',
          key: 'offDeviceTotal',
          unit: '台'
        },
        {
          name: '异常未处理',
          key: 'abnormalDeviceTotal',
          unit: '台'
        }
      ], // 统计数据
      tableColumn: [],
      deviceTableColumn: [
        {
          prop: 'surveyName',
          label: '设备名称',
          minWidth: 130
        },
        {
          prop: 'menuName',
          label: '归属系统'
        },
        {
          prop: 'status',
          label: '运行状态',
          render: (h, row) => {
            return (
              <div>
                {row.row.status == '0' && (
                  <div class="table-icon">
                    <img src={icon_5} />
                    <span style="color:#61E29D">正常</span>
                  </div>
                )}
                {row.row.status == '6' && (
                  <div class="table-icon">
                    <img src={icon_6} />
                    <span style="color:#86909C">离线</span>
                  </div>
                )}
                {row.row.status == '10' && (
                  <div class="table-icon">
                    <img src={icon_2} />
                    <span style="color:#FF2D55">异常</span>
                  </div>
                )}
              </div>
            )
          }
        }
      ],
      countyTableColumn: [
        {
          prop: 'spaceName',
          label: '楼层名称'
        },
        {
          prop: 'allCount',
          label: '监测设备数'
        },
        {
          prop: 'normalCount',
          label: '正常数'
        },
        {
          prop: 'abnormalCount',
          label: '报警设备数'
        }
      ],
      refrigeratorParams: refrigeratorParams,
      KTsystemFlag: false,
      sortType: '',
      pagination: {
        total: 0,
        pageSize: 15,
        pageNo: 1
      },
      chartData: [],
      ssmCodes: [],
      checkedValue: '',
      isKeyDevice: false, // 重点设备
      isPipeline: false, // 管线显示
      showPipeline: false, // 是否显示管线
      isOpenKeyDevice: true, // 重要设备展开
      keyDeviceList: [],
      isSelectedTypeName: ''
    }
  },
  watch: {
    // 监听模型层级数据变化
    roomData: {
      handler(newVal, oldVal) {
        // 切换菜单部分缓存数据清空
        if (newVal.callBackBridge === 'SBMenuBarSwitch') {
          this.isSelectedTypeName = ''
          this.sendCheckData = {}
        }
        this.initData()
        this.isPipeline = false
        this.isKeyDevice = false
      },
      deep: true
    }
  },
  mounted() {
    this.sortType = '1'
    this.tableColumn = this.deviceTableColumn
    // 初始化调用
    this.initData()
  },
  methods: {
    // 变配电监测设备详情
    openDeviceDetail(id = '') {
      Object.assign(this.deviceDetailInfo, {
        ...this.roomData,
        surveyCode: id
      })
      this.changeDeviceDetailDialogShow(true)
    },
    changeDeviceDetailDialogShow(flag) {
      this.deviceDetailsListShow = flag
      try {
        window.chrome.webview.hostObjects.sync.bridge.ShowDialog(flag)
        window.chrome.webview.hostObjects.sync.bridge.GetSwichFloorIsShow(flag)
      } catch (error) {}
    },
    // 重点设备
    getKeyDeviceList() {
      this.ssmCodes = this.roomData.ssmCodes?.split(',') || []
      const parmas =
        this.roomData.isSpace == 0
          ? {
            projectCode: this.roomData.projectCode,
            spaceId: this.ssmCodes.at(-1),
            keyDevice: 0
          }
          : {
            projectCode: this.roomData.projectCode,
            menuCode: this.roomData.menuCode,
            modelCode: this.roomData.modelCoding,
            keyDevice: 0
          }
      queryAllSurveyParamList(parmas).then((res) => {
        if (res.data.code === '200') {
          const newArr = res.data.data
          newArr &&
            newArr.forEach((item) => {
              item.newParamList = []
              item.paramList &&
                item.paramList.forEach((v) => {
                  item.newParamList.push({
                    paramName: v.paramName,
                    voltage: v.paramValue[0],
                    current: v.paramValue[1],
                    lineCurrent: v.paramValue[2]
                  })
                })
            })
          this.keyDeviceList = newArr
        }
      })
    },
    initData() {
      this.ssmCodes = this.roomData.ssmCodes?.split(',') || []
      // 传递给子组件的参数提前声明key
      this.sendCheckData = {
        ...this.roomData,
        entityTypeId: '',
        spaceId: this.ssmCodes.at(-1)
      }
      // this.pipelineData = {
      //   ...this.roomData
      // }

      this.showPipeline = ['Yqxt', 'Kongtiao'].includes(this.roomData.tabName)
      this.sortType = '1'
      this.getEntityType()
      this.getAssetsCount()
      if (this.roomData.projectCode == 'IEMC-Electricity') {
        this.getKeyDeviceList()
      }
    },
    // 根据设备类型获取实体
    getEntityType() {
      const parmas =
        this.roomData.isSpace == 0
          ? {
            projectCode: this.roomData.projectCode,
            spaceId: this.ssmCodes.at(-1)
          }
          : {
            projectCode: this.roomData.projectCode,
            menuCode: this.roomData.menuCode,
            modelCode: this.roomData.modelCoding
          }
      getGroupByEntityType(parmas).then((res) => {
        if (res.data.code === '200') {
          this.chartData = res.data.data
          this.pagination.pageNo = 1
          this.getAssetsTypeList()
          this.$nextTick(() => {
            this.getRenderer(res.data.data)
          })
        }
      })
    },
    // 初始化设备数量
    getAssetsCount() {
      const params =
        this.roomData.isSpace == 0
          ? {
            projectCode: this.roomData.projectCode,
            spaceId: this.ssmCodes.at(-1),
            menuCode: '',
            modelCode: ''
          }
          : {
            projectCode: this.roomData.projectCode,
            menuCode: this.roomData.menuCode,
            modelCode: this.roomData.modelCoding,
            spaceId: this.ssmCodes.at(-1)
          }
      getMonitorDeviceCount(params).then((res) => {
        if (res.data.code === '200') {
          this.deviceAccountStatistics = res.data.data
        }
      })
    },
    // 初始化统计表
    getRenderer(data) {
      // 基于准备好的dom，初始化echarts实例
      const EChart = echarts.init(document.getElementById('EChart'))
      const barWidth = 14 /* 进度条宽度 */
      const normalData = [] /* 正常 */
      const abnormalData = [] /* 异常 */
      const offlineData = [] /* 离线 */
      const attaVal = [] /* 进度条数值 */
      const topName = [] /* 进度条名称 */
      data.forEach((item, i) => {
        topName[i] = {
          value: item.entityTypeName,
          textStyle: {
            color: '#FFF'
          }
        }
        attaVal[i] = item.allCount
        normalData[i] = item.normalCount
        abnormalData[i] = item.abnormalCount
        offlineData[i] = item.offLineCount
      })
      // 配置参数
      const config = {
        background: '#ffff',
        tooltip: {
          show: false,
          textStyle: {
            fontSize: 16
          }
        },
        grid: {
          left: '2%',
          right: '10%',
          top: '13%',
          bottom: '0%',
          containLabel: true
        },
        dataZoom: [
          {
            yAxisIndex: [0, 1],
            orient: 'vertical',
            show: true,
            type: 'slider',
            start: 100,
            // end: 100,
            width: 8,
            left: '99%',
            borderColor: 'rgba(43,48,67,.1)',
            fillerColor: '#6580b8',
            zoomLock: true,
            showDataShadow: false,
            backgroundColor: 'rgba(43,48,67,.1)',
            showDetail: false,
            // realtime: true,
            filterMode: 'filter',
            handleIcon: 'M-9.35,34.56V42m0-40V9.5m-2,0h4a2,2,0,0,1,2,2v21a2,2,0,0,1-2,2h-4a2,2,0,0,1-2-2v-21A2,2,0,0,1-11.35,9.5Z',
            handleStyle: {
              color: '#6580b8',
              borderColor: '#6580b8'
            },
            maxValueSpan: 4,
            minValueSpan: 4,
            brushSelect: false
          },
          {
            type: 'inside',
            show: false,
            yAxisIndex: [0, 1],
            height: 8
          }
        ],
        legend: {
          show: true,
          top: '3%',
          itemHeight: 8,
          itemWidth: 8,
          itemGap: 20,
          textStyle: {
            fontSize: 12,
            color: '#fff'
          },
          selectedMode: false
        },
        xAxis: {
          show: false,
          type: 'value'
        },
        yAxis: [
          {
            type: 'category',
            // inverse: true,
            triggerEvent: true,
            axisTick: {
              show: false
            },
            axisLine: {
              show: false
            },
            axisLabel: {
              textStyle: {
                color: '#fff',
                fontSize: 14
              }
            },
            data: topName
          }
        ],
        series: [
          {
            name: '正常',
            stack: 'total',
            type: 'bar',
            barWidth: barWidth,
            data: normalData,
            itemStyle: {
              normal: {
                color: ' rgba(101,234,162,0.6)',
                borderColor: '#2C344C',
                borderWidth: 1
              }
            }
          },
          {
            name: '异常',
            type: 'bar',
            barWidth: barWidth,
            stack: 'total',
            data: abnormalData,
            itemStyle: {
              normal: {
                color: 'rgba(255,45,85,0.6)',
                borderColor: '#2C344C',
                borderWidth: 1
              }
            }
          },
          {
            name: '离线',
            type: 'bar',
            barWidth: barWidth,
            stack: 'total',
            data: offlineData,
            itemStyle: {
              normal: {
                color: 'rgba(212,222,236,0.6)',
                borderColor: '#2C344C',
                borderWidth: 1
              }
            }
          },
          // total
          {
            type: 'bar',
            zlevel: 1,
            barWidth: barWidth,
            barGap: '-100%',
            label: {
              show: true,
              position: 'right',
              distance: 8,
              textStyle: { color: '#DADEE1', fontSize: 14 }
            },
            itemStyle: {
              color: 'transparent'
            },
            data: attaVal
          }
        ]
      }
      if (data.length > 4) {
        data.dataZoom = [
          {
            yAxisIndex: [0],
            orient: 'vertical',
            show: true,
            type: 'slider',
            startValue: 100,
            // end: 100,
            width: 8,
            left: '99%',
            borderColor: 'rgba(43,48,67,.1)',
            fillerColor: '#6580b8',
            zoomLock: true,
            showDataShadow: false,
            backgroundColor: 'rgba(43,48,67,.1)',
            showDetail: false,
            // realtime: true,
            filterMode: 'filter',
            handleIcon: 'M-9.35,34.56V42m0-40V9.5m-2,0h4a2,2,0,0,1,2,2v21a2,2,0,0,1-2,2h-4a2,2,0,0,1-2-2v-21A2,2,0,0,1-11.35,9.5Z',
            handleStyle: {
              color: '#6580b8',
              borderColor: '#6580b8'
            },
            maxValueSpan: 4,
            minValueSpan: 4,
            brushSelect: false
          },
          {
            type: 'inside',
            show: false,
            yAxisIndex: [0],
            height: 8
          }
        ]
      }
      // 设置参数
      EChart.setOption(config)
      // 先移除点击事件 解决点击事件重复绑定
      EChart.off('click')
      // 点击事件
      EChart.on('click', (params) => {
        const index = params.dataIndex
        // 新增变量 判断当前点击柱状图是选中还是取消选中
        let isSelected = false
        let name = ''
        topName.map((e, i) => {
          // 选中的设置选中色
          if (i == index && e.textStyle.color != '#FFCA64FF') {
            e.textStyle.color = '#FFCA64FF'
            name = e.value
          } else {
            // 选中已选中的则为取消选中
            if (i == index && e.textStyle.color == '#FFCA64FF') {
              isSelected = true
            }
            // 其他的设为默认色
            e.textStyle.color = '#FFF'
          }
        })
        config.yAxis.data = JSON.parse(JSON.stringify(topName))
        EChart.setOption(config)
        // 取消选中 则恢复过滤条件
        let paramsId = ''
        this.isSelectedTypeName = ''
        this.pagination.pageNo = 1
        if (this.sortType === '1') {
          // 取消选中 则恢复过滤条件
          if (!isSelected) {
            paramsId = this.chartData[index].entityTypeId || ''
            this.isSelectedTypeName = '- ' + name
          }
          Object.assign(this.sendCheckData, {
            entityTypeId: paramsId,
            spaceId: ''
          })
          // 设备类型
          this.getAssetsTypeList({ entityTypeId: paramsId })
          this.$emit('sendWpfData', { entityTypeId: paramsId })
        } else if (this.sortType === '2') {
          // 取消选中 则恢复过滤条件
          if (!isSelected) {
            paramsId = this.chartData[index].spaceId || this.ssmCodes.at(-1)
            this.isSelectedTypeName = '- ' + name
          }
          Object.assign(this.sendCheckData, {
            spaceId: paramsId || this.ssmCodes.at(-1),
            entityTypeId: ''
          })
          // 建筑
          this.getListGroupData({ spaceId: paramsId })
          // this.$emit('sendWpfData', {spaceId: paramsId})
          if (this.roomData.ssmType < 4) {
            try {
              window.chrome.webview.hostObjects.sync.bridge.WebSwitchingPerspective(paramsId)
            } catch (error) {}
          }
        }
      })
    },
    // 根据建筑查实体类型
    getUnitType() {
      const parmas =
        this.roomData.isSpace == '0'
          ? {
            projectCode: this.roomData.projectCode,
            queryFlag: 1,
            spaceId: this.ssmCodes.at(-1)
          }
          : {
            projectCode: this.roomData.projectCode,
            queryFlag: 1,
            menuCode: this.roomData.menuCode,
            modelCode: this.roomData.modelCoding
          }
      querySurveyBySpaceId(parmas).then((res) => {
        if (res.data.code === '200') {
          this.chartData = res.data.data.map((e) => {
            e.entityTypeName = e.spaceName
            return e
          })
          this.getRenderer(res.data.data)
          this.pagination.pageNo = 1
          this.getListGroupData()
        }
      })
    },
    // 根据设备类型查询列表
    getAssetsTypeList(obj) {
      const params =
        this.roomData.isSpace == '0'
          ? {
            spaceId: this.ssmCodes.at(-1),
            page: this.pagination.pageNo,
            pageSize: this.pagination.pageSize,
            projectCode: this.roomData.projectCode,
            deviceStatus: this.checkedValue || ''
          }
          : {
            menuCode: this.roomData.menuCode,
            modelCode: this.roomData.modelCoding,
            page: this.pagination.pageNo,
            pageSize: this.pagination.pageSize,
            projectCode: this.roomData.projectCode,
            deviceStatus: this.checkedValue || ''
          }
      Object.assign(params, obj)
      getGasList(params).then((res) => {
        if (res.data.code === '200') {
          this.deviceTableLoading = false
          if (this.pagination.pageNo === 1) {
            this.deviceList = []
          }
          this.deviceList = this.deviceList.concat(res.data.data.list)
          this.pagination.total = res.data.data.count
        } else {
          this.deviceTableLoading = false
        }
      })
    },
    // 根据建筑分组查询列表
    getListGroupData(obj) {
      this.deviceTypeTableLoading = true
      const params =
        this.roomData.isSpace == '0'
          ? {
            projectCode: this.roomData.projectCode,
            queryFlag: '2',
            deviceStatus: '',
            spaceId: this.ssmCodes.at(-1),
            page: this.pagination.pageNo,
            pageSize: this.pagination.pageSize
          }
          : {
            projectCode: this.roomData.projectCode,
            queryFlag: '2',
            deviceStatus: '',
            menuCode: this.roomData.menuCode,
            modelCode: this.roomData.modelCoding,
            page: this.pagination.pageNo,
            pageSize: this.pagination.pageSize
          }
      Object.assign(params, obj)
      querySurveyListByPage(params).then((res) => {
        if (res.data.code === '200') {
          this.deviceTableLoading = false
          if (this.pagination.pageNo === 1) {
            this.deviceList = []
          }
          this.deviceList = this.deviceList.concat(res.data.data.list)
          this.pagination.total = res.data.data.count
        } else {
          this.deviceTableLoading = false
        }
      })
    },
    // 根据建筑分组查询
    deviceClick(row) {
      try {
        window.chrome.webview.hostObjects.sync.bridge.GetDeviceName(
          JSON.stringify({
            DeviceID: row.assetId,
            DeviceName: row.imsName,
            Description: row.imhMonitorName
          })
        )
      } catch (error) {}
    },
    // 获取实体分类
    // getEntityTypeData() {
    // const params = {
    //   projectCode: this.roomData.projectCode
    // }
    // deviceDblclick(row) {
    //   // 如果关联了设备即跳转设备详情页
    //   if (row.assetId) {
    //     this.$emit('roomEvent', {
    //       type: 'move',
    //       deviceId: row.assetId,
    //       deviceName: row.assetName
    //     })
    //   } else {
    //     this.$message.warning('该设备未关联资产!')
    //   }
    // },
    changePipelineShow(type) {
      this.isPipeline = type == 1
      if (this.isPipeline) {
        // 深圳肿瘤医院对应管线
        if (__PATH.VUE_APP_HOSPITAL_NODE === 'szzlyy') {
          this.pipelineData = {
            ...this.roomData
          }
        }
      } else {
        this.sendCheckData = {
          ...this.roomData,
          entityTypeId: '',
          spaceId: this.ssmCodes.at(-1)
        }
      }
    },
    customRowClick(row) {
      // 单击和双击不能共同使用

    },
    tableRowClick(row) {
      console.log(this.clickCount)
      this.clickCount++
      if (this.clickCount == 1) {
        this.clickTimer = setTimeout(() => {
          this.clickCount = 0
          // 执行单击操作
          let params = {}
          const viewMonitorData = monitorTypeList.find(e => e.projectCode == row.projectCode)
          const filterName = hasModelCodeFilterProjectName
          const filterMonitorFlag = filterName.some(e => e == viewMonitorData?.projectName)
          // 如果关联了设备即跳转设备详情页
          const device = row.modelCode ? this.assetsList.find((item) => item.modelCode === row.modelCode) : {}
          params = {
            DeviceCode: device?.modelCode,
            DeviceName: device?.assetName,
            assetsId: row.assetId ? row.assetId : device?.assetId,
            projectCode: row?.projectCode,
            spaceCode: row?.regionCode
          }
          console.log(row, params, 'tableRowClick')
          if (viewMonitorData.wpfKey) {
            if (filterMonitorFlag) {
              if (params.DeviceCode) { // 设备
                try {
                  window.chrome.webview.hostObjects.sync.bridge.EquipmentRealView(JSON.stringify(params))
                } catch (error) {}
              } else {
                this.$message.warning('当前设备暂未录入模型编码!')
              }
            } else {
              if (params.assetsId) { // 点位
                try {
                  window.chrome.webview.hostObjects.sync.bridge.EquipmentRealView(JSON.stringify(params))
                } catch (error) {}
              } else {
                this.$message.warning('当前设备暂未关联资产!')
              }
            }
          }
        }, 300)
      } else {
        this.clickCount = 0
        clearTimeout(this.clickTimer)
        this.tabledblClick(row)
      }
    },
    tabledblClick(row) {
      // 安消防特殊判断通过assetsId不通过modelCode
      const specialJudgment = ['SecuritySystem', 'FireAlarmSystem']
      if (specialJudgment.includes(this.roomData.tabName)) {
        if (row.assetId) {
          this.$emit('roomEvent', {
            type: 'move',
            assetId: row.assetId,
            assetName: row.assetName,
            modelCode: row.modelCode,
            entityTypeId: row.entityTypeId
          })
        } else {
          // this.$message.warning('该设备未关联资产!')
        }
        return
      }
      if (row.modelCode || row.assetId) {
        const device = this.assetsList.find((item) => item.modelCode === row.modelCode)
        this.$emit('roomEvent', {
          type: 'move',
          assetId: row.assetId ? row.assetId : device?.assetId,
          assetName: row.assetName ? row.assetName : device?.assetName,
          modelCode: device?.modelCode,
          entityTypeId: row.entityTypeId
        })
      } else {
        // this.$message.warning('该设备未关联资产!')
      }
    },
    tableLoadMore() {
      if (this.pagination.total > this.pagination.pageNo * this.pagination.pageSize) {
        this.pagination.pageNo++
        if (this.sortType == '1') {
          this.getAssetsTypeList({ entityTypeId: this.sendCheckData.entityTypeId })
        } else {
          this.getListGroupData({ spaceId: this.sendCheckData.spaceId })
        }
      }
    },
    changeSortType(sortType) {
      this.sortType = sortType
      Object.assign(this.sendCheckData, {
        entityTypeId: '',
        spaceId: this.ssmCodes.at(-1)
      })
      if (sortType == '1') {
        this.tableColumn = this.deviceTableColumn
        this.getEntityType()
      }
      if (sortType == '2') {
        this.tableColumn = this.countyTableColumn
        this.getUnitType()
      }
    },
    checkBoxChanged() {
      this.checkList = [this.checkList[this.checkList.length - 1]]
      this.showCheckGroup = false
      const codeList = []
      this.flowList.forEach((item) => {
        if (this.checkList[0] == item.label) {
          codeList.push(item.value)
        }
      })
      this.checkedValue = codeList[0]
      this.pagination.pageNo = 1
      this.getAssetsTypeList()
    },
    allTableChange(type) {
      this.allTableComponentListShow = true
      // 如果无数据则填充初始空间过滤数据，有数据则按照当前数据过滤
      // let params = {
      //   entityTypeId: '',
      //   spaceId: this.ssmCodes.at(-1),
      //   deviceId: this.roomData.deviceId
      // }
      const params =
        this.roomData.isSpace == '0'
          ? {
            spaceId: this.ssmCodes.at(-1),
            entityTypeId: ''
          }
          : {
            menuCode: this.roomData.menuCode,
            modelCode: this.roomData.modelCoding,
            entityTypeId: ''
          }
      if (this.sendCheckData.entityTypeId) {
        params.entityTypeId = this.sendCheckData.entityTypeId
      } else if (this.sendCheckData.spaceId) {
        params.spaceId = this.sendCheckData.spaceId
      }
      Object.assign(this.tableCompenentData, {
        title: '设备台账列表',
        ...params,
        isSpace: this.roomData.isSpace,
        projectCode: this.roomData.projectCode,
        deviceId: this.roomData.deviceId,
        type: type,
        height: 'calc(100% - 120px)'
      })
    },
    // 关闭弹窗
    configCloseTableDialog() {
      this.allTableComponentListShow = false
      try {
        window.chrome.webview.hostObjects.sync.bridge.ShowDialog(false)
      } catch (error) {}
    }
  }
}
</script>

<style lang="scss" scoped>
@import '../style/module.scss';
.statistics_top_new {
  height: 130px;
  display: flex;
  flex-wrap: wrap;
  .statistics_top_item {
    height: 50%;
    width: 50%;
    // padding: 16px 0;
    display: flex;
    justify-content: center;
    align-items: center;
    img {
      width: 50px;
      height: 50px;
    }
  }
  .statistics_top_content {
    .statistics_top_title {
      padding-left: 20px;
      font-size: 14px;
      font-family: HarmonyOS Sans SC-Regular, HarmonyOS Sans SC;
      font-weight: 400;
      color: #b0e3fa;
      margin-bottom: 6px;
    }
    .font_bg {
      width: 125px;
      height: 32px;
      background: url(./../../../assets/images/qhdsys/Group-new.png) no-repeat 100%;
      display: flex;
      align-items: center;
      justify-content: center;
      .color_font {
        font-size: 20px;
        font-family: HarmonyOS Sans SC-Bold, HarmonyOS Sans SC;
        font-weight: bold;
        color: #ffca64;
        margin-bottom: 3px;
      }
      .color_unit {
        margin-left: 3px;
        font-size: 14px;
        font-family: HarmonyOS Sans SC-Light, HarmonyOS Sans SC;
        font-weight: 300;
        color: rgba(255, 255, 255, 0.5);
      }
    }
  }
}
.moveComponent {
  width: 100%;
  height: 100%;
  .device-list {
    overflow: auto;
    .device-list-item {
      padding-top: 16px;
      .item-title {
        background: rgba(133, 145, 206, 0.15);
        font-size: 15px;
        font-weight: 500;
        color: #ffffff;
        line-height: 18px;
        padding: 7px 8px;
        display: flex;
        align-items: center;
      }
      .item-status {
        display: flex;
        .status-item {
          width: calc(100% / 3);
          text-align: center;
          padding: 15px;
        }
        .status-item-name {
          font-size: 14px;
          font-weight: 400;
          color: #ffffff;
          line-height: 16px;
        }
        .status-item-value {
          font-size: 16px;
          font-weight: bold;
          color: #ffca64;
          line-height: 19px;
          margin-top: 6px;
        }
        // .status-item:last-child {
        //   .status-item-value {
        //     color: #ff2d55;
        //   }
        // }
      }
    }
    .empty-div {
      width: 100%;
      height: 100%;
      display: flex;
      > span {
        margin: auto;
      }
    }
  }
  .table-icon {
    display: flex;
    align-items: center;
  }
  .table-icon img {
    width: 16px;
    margin-right: 3px;
  }
  .title-left {
    padding-left: 20px;
    .showFloorName {
      width: 100%;
      text-overflow: ellipsis;
      white-space: nowrap;
      overflow: hidden;
    }
  }
  .title-detail {
    cursor: pointer;
    font-size: 14px;
    font-family: HarmonyOS Sans SC-Regular, HarmonyOS Sans SC;
    font-weight: 400;
    color: #8bddf5 !important;
    margin-right: 13px;
  }
  .toggle {
    display: flex;
    cursor: pointer;
    font-size: 14px;
  }
  .toggle > div {
    padding: 4px 10px;
    color: #8bddf5 !important;
    text-align: center;
    background-color: #213251;
    box-sizing: border-box;
    border: 1px solid #213251;
    opacity: 1;
  }
  .active-type {
    background: rgba(255, 255, 255, 0.1) linear-gradient(90deg, rgba(10, 132, 255, 0.4) 0%, rgba(10, 132, 255, 0) 100%);
    border: 1px solid #abf0ff !important;
  }
  .icon-box {
    margin-right: 0;
    display: flex;
    align-items: center;
    position: relative;
  }
  ::v-deep .el-table {
    .el-table__body tr {
      cursor: pointer;
    }
  }
}
// 滚动条的宽度
::v-deep .el-table__body-wrapper::-webkit-scrollbar {
  width: 6px; // 横向滚动条
  height: 6px; // 纵向滚动条 必写
}

// 注意:hover
::v-deep .el-table__body-wrapper:hover::-webkit-scrollbar {
  width: 6px; // 横向滚动条
  height: 6px; // 纵向滚动条 必写
}
::v-deep .el-table__body-wrapper::-webkit-scrollbar-corner,
::v-deep .el-table__body-wrapper::-webkit-scrollbar-track {
  background-color: transparent;
}
.module-header {
  width: 100%;
  // padding-left: 50px;
  background: url('~@/assets/images/qhdsys/bg-bt.png') no-repeat;
}

::v-deep .el-checkbox__input.is-checked span.el-checkbox__inner {
  background: url('@/assets/images/<EMAIL>') !important;
  background-size: 100% 100% !important;
}
::v-deep span.el-checkbox__inner {
  border-color: #c5fff9 !important;
}
::v-deep .el-checkbox__input.is-checked + .el-checkbox__label {
  color: #8bddf5;
}
.order-more {
  width: 24px;
  height: 24px;
  margin-right: 0 !important;
  margin-left: 8px;
}
.filter {
  width: 24px;
  height: 24px;
}
::v-deep .panel-s {
  position: absolute;
  right: 0;
  top: 28px;
  background-color: #374b79;
  padding: 8px;
  z-index: 9;
  height: 120px;
  overflow: auto;
  .el-checkbox-group {
    display: flex;
    flex-direction: column;
    align-items: flex-end;
    margin-right: 0 !important;
    .el-checkbox {
      width: 100%;
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-right: 0;
      margin-bottom: 5px;
      .el-checkbox__label {
        color: #a3a9c0 !important;
      }
      .el-checkbox__input {
        // display: none;
      }
    }
  }
}
</style>
