<template>
  <div class="content">
    <!-- <el-button class="sino-button-sure" @click="assetsListChange">showDialog</el-button> -->
    <div class="right-content" ref="rightContent">
      <div class="echarts-top">
        <div class="bg-title" style="padding: 0 3rem"><span>资产概览</span></div>
        <div class="bg-content" style="height: calc(100% - 2.5rem)">
          <div class="alarm-analysis">
            <div class="case-img">
              <img src="../../../assets/images/peace/histogram.png" />
              <div class="case-num" style="color: #CFE1F7">
                <p style="color: #FFFFFF;">{{ disposeData.value1 }}</p>
                <p style="font-size: 0.75rem; margin-top: 0.8rem">资产总值(万元)</p>
              </div>
              <div class="case-anim-icon"></div>
            </div>
            <div class="case-img">
              <img src="../../../assets/images/peace/histogram.png" />
              <div class="case-num" style="color: #CFE1F7">
                <p style="color: #FFFFFF;">{{ disposeData.value2 }}</p>
                <p style="font-size: 0.75rem; margin-top: 0.8rem">资产总数(个)</p>
              </div>
              <div class="case-anim-icon"></div>
            </div>
            <div class="case-img">
              <img src="../../../assets/images/peace/histogram.png" />
              <div class="case-num" style="color: #FFE3A6">
                <p>{{ disposeData.value3 }}</p>
                <p style="font-size: 0.75rem; margin-top: 0.8rem">资产种类(种)</p>
              </div>
              <div class="case-anim-icon"></div>
            </div>
            <div class="case-img">
              <img src="../../../assets/images/peace/histogram.png" />
              <div class="case-num" style="color: #7BFFC1">
                <p>{{ disposeData.value4 }}</p>
                <p style="font-size: 0.75rem; margin-top: 0.8rem">特种设备(台)</p>
              </div>
              <div class="case-anim-icon"></div>
            </div>
          </div>
          <div class="alarm-analysis">
            <div class="case-img">
              <img src="../../../assets/images/peace/histogram.png" />
              <div class="case-num" style="color: #7BFFC1">
                <p>{{ disposeData.value5 }}</p>
                <p style="font-size: 0.75rem; margin-top: 0.8rem">大型设备(台)</p>
              </div>
              <div class="case-anim-icon"></div>
            </div>
            <div class="case-img">
              <img src="../../../assets/images/peace/histogram.png" />
              <div class="case-num" style="color: #FF7B7B">
                <p>{{ disposeData.value6 }}</p>
                <p style="font-size: 0.75rem; margin-top: 0.8rem">应急资产(台)</p>
              </div>
              <div class="case-anim-icon"></div>
            </div>
            <div class="case-img">
              <img src="../../../assets/images/peace/histogram.png" />
              <div class="case-num" style="color: #1FFAFF">
                <p>{{ disposeData.value7 }}</p>
                <p style="font-size: 0.75rem; margin-top: 0.8rem">供应商数(个)</p>
              </div>
              <div class="case-anim-icon"></div>
            </div>
            <div class="case-img">
              <img src="../../../assets/images/peace/histogram.png" />
              <div class="case-num" style="color: #FFE3A6">
                <p>{{ disposeData.value8 }}</p>
                <p style="font-size: 0.75rem; margin-top: 0.8rem">品牌数量(种)</p>
              </div>
              <div class="case-anim-icon"></div>
            </div>
          </div>
        </div>
      </div>
      <div class="echarts-center">
        <div class="bg-title" style="padding: 0 1rem 0 3rem">
          <span>资产价值分析</span>
        </div>
        <div class="bg-content" style="height: calc(100% - 2.5rem)">
          <div v-if="analysisShow" class="center-center">暂无数据</div>
          <div v-if="!analysisShow" class="basic-bg"></div>
          <div v-if="!analysisShow" id="BasicAnalysisEcharts"></div>
        </div>
      </div>
      <div class="echarts-center">
        <div class="bg-title" style="padding: 0 1rem 0 3rem">
          <span>资产使用年限分析</span>
        </div>
        <div class="bg-content" style="height: calc(100% - 2.5rem)">
          <div v-if="deptTopShow" class="center-center">暂无数据</div>
          <div v-if="!deptTopShow" class="basic-bg"></div>
          <div v-if="!deptTopShow" id="deptProduceEcharts"></div>
        </div>
      </div>
    </div>
    <template v-if="assetsListShow">
      <assetsList ref="assetsList" :dialogShow="assetsListShow" @configCloseDialog="assetsListChange"></assetsList>
    </template>
  </div>
</template>
<script>
import * as echarts from 'echarts'
import { getDepartmentMedicalWasteInfo, getTypeAnalysisInfo, getMedicalWasteDisposalInfo } from '@/utils/centerScreenApi'
import assetsList from './components/assetsList'
export default {
  name: 'rearAssets',
  components: {
    assetsList
  },
  data() {
    return {
      assetsListShow: false,
      disposeData: {
        confirmWeigh: 0,
        issuedWeigh: 0,
        processEndWeigh: 0,
        stockInWeigh: 0,
        timeoutWeigh: 0,
        waitWeigh: 0
      },
      analysisShow: true,
      deptTopShow: true,
      getPiechartTime: null,
      getPiechart2Time: null,
      getPiechart: null,
      getPiechart2: null,
      timer: null,
      location: '',
      ssmType: ''
    }
  },
  mounted() {
    // 初始化 获取定位信息
    if (Object.hasOwn(this.$route.query, 'localtion') || Object.hasOwn(this.$route.query, 'ssmType')) {
      this.location = this.$route.query.localtion || ''
      this.ssmType = this.$route.query.ssmType || ''
    }

    this.search()
    // this.timer = setInterval(() => {
    //   setTimeout(() => {
    //     this.search()
    //   }, 0)
    // }, 1000 * timerNum)
    // this.$once('hook:beforeDestroy', () => {
    //   clearInterval(this.timer)
    //   this.timer = null
    // })

    try {
      window.chrome.webview.addEventListener('message', (event) => {
        const data = JSON.parse(event.data)
        if (data.type === 'tag') {
          this.assetsListChange(data.localtion)
        }
      })
    } catch (error) {}
  },
  methods: {
    assetsListChange(placeIds) {
      this.assetsListShow = !this.assetsListShow
      this.$nextTick().then(() => {
        if (this.assetsListShow) {
          this.$refs.rightContent.style.width = 0
          this.$refs.assetsList.getWorkOrderTableData({ placeIds })
        } else {
          this.$refs.rightContent.style.width = '24%'
        }
        try {
          window.chrome.webview.hostObjects.sync.bridge.ShowTable(this.assetsListShow)
        } catch (error) {}
      })
    },
    search() {
      this.getDepartmentMedicalWasteInfo('week')
      this.getTypeAnalysisInfo('week')
      this.getMedicalWasteDisposalInfo()
    },
    getMedicalWasteDisposalInfo() {
      getMedicalWasteDisposalInfo({}).then((res) => {
        const data = res.data
        if (data.code === '200') {
          // if (data.data.list.length) {
          data.data.list[0] = {
            value1: 120,
            value2: 78,
            value3: 4,
            value4: 5,
            value5: 21,
            value6: 9,
            value7: 4,
            value8: 21
          }
          this.disposeData = data.data.list[0]
          // }
        }
      })
    },
    // 医废基本情况分析
    getTypeAnalysisInfo(type) {
      getTypeAnalysisInfo({ dateType: type }).then((res) => {
        const data = res.data
        if (data.code === '200') {
          // if (data.data.list.length) {
          this.analysisShow = false
          const arr = data.data.list
          this.$nextTick(() => {
            this.departmentEchart(arr)
          })
          // } else {
          //   this.analysisShow = true
          // }
        }
      })
    },
    // 分析echarts
    departmentEchart(arr) {
      arr = [
        { value: 200, name: '1000万（含）' },
        { value: 529, name: '800万-1000万' },
        { value: 701, name: '50万（含）以下' }
      ]
      const total = 1430
      this.getPiechart = echarts.init(document.getElementById('BasicAnalysisEcharts'))
      const data = []
      var color = [
        'rgba(31, 250, 255, 0.3)',
        'rgba(255, 227, 166, 0.28)',
        'rgba(147, 130, 255, 0.26)',
        'rgba(0, 248, 114, 0.24)',
        'rgba(46, 119, 251, 0.3)',
        'rgba(255, 84, 84, 0.3)',
        'rgba(123, 255, 193, 0.3)'
      ]
      var borderColor = ['rgba(31, 250, 255, 1)', 'rgba(255, 227, 166, 1)', 'rgba(147, 130, 255, 1)', 'rgba(0, 248, 114, 1)', 'rgba(46, 119, 251, 1)', 'rgba(255, 84, 84, 1)', 'rgba(123, 255, 193, 1)']
      var xdata = arr.map((item) => {
        return item.name
      })
      for (var i = 0; i < arr.length; i++) {
        data.push({
          name: arr[i].name,
          value: arr[i].value,
          itemStyle: {
            normal: {
              borderWidth: 1,
              shadowBlur: 200,
              borderColor: borderColor[i],
              color: color[i],
              label: {
                normal: { show: true }
              }
            }
          }
        })
      }
      var seriesObj = [
        {
          name: '',
          type: 'pie',
          clockWise: false,
          center: ['50%', '40%'],
          radius: ['40%', '55%'],
          // hoverAnimation: false,
          label: {
            normal: {
              show: false,
              position: 'outside',
              formatter: '{b}\n {c}个\n {d}%',
              textStyle: {
                fontSize: '12',
                color: '#fff'
              }
            },
            emphasis: {
              show: true,
              textStyle: {
                fontSize: '12',
                color: '#fff'
              }
            }
          },
          labelLine: {
            emphasis: {
              show: true,
              lineStyle: {
                color: '#fff'
              }
            }
          },
          data: data
        }
      ]
      const option = {
        backgroundColor: '',
        // backgroundColor: {
        //   type: 'pattern',
        //   repeat: 'no-repeat',
        //   image: require('../../../assets/images/peace/people-right-top-bg.png') // 赋值
        // },
        title: {
          text: '总数量',
          subtext: total + '个',
          textStyle: {
            color: '#CFE1F7',
            fontSize: 12
          },
          subtextStyle: {
            fontSize: 16,
            color: '#FFFFFF'
          },
          x: 'center',
          y: '30%'
        },
        legend: {
          orient: 'horizontal',
          left: 'center',
          bottom: '0',
          data: xdata,
          itemWidth: 8,
          itemHeight: 8,
          itemGap: 16,
          textStyle: {
            color: '#B3C2DD' //  字体颜色
          }
        },
        // formatter: function (name) {
        //   var oa = option.series[0].data
        //   for (var i = 0; i < option.series[0].data.length; i++) {
        //     if (name === oa[i].name) {
        //       return ' ' + name + ' (' + oa[i].value + ')     '
        //     }
        //   }
        // },
        series: seriesObj
      }
      this.getPiechart.setOption(option)
      if (data.length > 1) {
        clearInterval(this.getPiechartTime)
        this.pieEchartSetTimeOut('getPiechart', data)
      }
      // 鼠标移出后默认高亮
      this.getPiechart.on('mouseout', () => {
        clearInterval(this.getPiechartTime)
        this.pieEchartSetTimeOut('getPiechart', data)
      })
      this.getPiechart.clear()
      this.getPiechart.setOption(option)
      // 随着屏幕大小调节图表
      window.addEventListener('resize', () => {
        this.getPiechart.resize()
      })
    },
    pieEchartSetTimeOut(key, data) {
      let index = -1 // 高亮所在下标
      const dataLength = data.length // 当前饼图有多少个扇形
      // 用定时器控制饼图高亮
      this[key + 'Time'] = setInterval(() => {
        // 清除之前的高亮
        this[key].dispatchAction({
          type: 'downplay',
          seriesIndex: 0,
          dataIndex: index
        })
        index = (index + 1) % dataLength
        // 当前下标高亮
        this[key].dispatchAction({
          type: 'highlight',
          seriesIndex: 0,
          dataIndex: index
        })
        if (index > dataLength) {
          index = 0
        }
      }, 3000)
      // 鼠标划入
      this[key].on('mouseover', () => {
        // 停止定时器，清除之前的高亮
        clearInterval(this[key + 'Time'])
        this[key].dispatchAction({
          type: 'downplay',
          seriesIndex: 0,
          dataIndex: index
        })
      })
    },
    // top5
    getDepartmentMedicalWasteInfo(type) {
      getDepartmentMedicalWasteInfo({ dateType: type, currentPage: 1, pageSize: 5 }).then((res) => {
        const data = res.data
        if (data.code === '200') {
          const arr = data.data.list
          // if (arr.length) {
          const sortArr = arr.sort((a, b) => a.totalweigh - b.totalweigh)
          this.deptTopShow = false
          this.$nextTick(() => {
            this.getDeptProduceEcharts(sortArr)
          })
          // } else {
          //   this.deptTopShow = true
          // }
        }
      })
    },
    // top5 echarts
    getDeptProduceEcharts(arr) {
      arr = [
        { value: 200, name: '10年以上' },
        { value: 529, name: '8年-10年' },
        { value: 701, name: '3万（含）以下' }
      ]
      const total = 1430
      this.getPiechart2 = echarts.init(document.getElementById('deptProduceEcharts'))
      const data = []
      var color = [
        'rgba(31, 250, 255, 0.3)',
        'rgba(255, 227, 166, 0.28)',
        'rgba(147, 130, 255, 0.26)',
        'rgba(0, 248, 114, 0.24)',
        'rgba(46, 119, 251, 0.3)',
        'rgba(255, 84, 84, 0.3)',
        'rgba(123, 255, 193, 0.3)'
      ]
      var borderColor = ['rgba(31, 250, 255, 1)', 'rgba(255, 227, 166, 1)', 'rgba(147, 130, 255, 1)', 'rgba(0, 248, 114, 1)', 'rgba(46, 119, 251, 1)', 'rgba(255, 84, 84, 1)', 'rgba(123, 255, 193, 1)']
      var xdata = arr.map((item) => {
        return item.name
      })
      for (var i = 0; i < arr.length; i++) {
        data.push({
          name: arr[i].name,
          value: arr[i].value,
          itemStyle: {
            normal: {
              borderWidth: 1,
              shadowBlur: 200,
              borderColor: borderColor[i],
              color: color[i],
              label: {
                normal: { show: true }
              }
            }
          }
        })
      }
      var seriesObj = [
        {
          name: '',
          type: 'pie',
          clockWise: false,
          center: ['50%', '40%'],
          radius: ['40%', '55%'],
          // hoverAnimation: false,
          label: {
            normal: {
              show: false,
              position: 'outside',
              formatter: '{b}\n {c}个\n {d}%',
              textStyle: {
                fontSize: '12',
                color: '#fff'
              }
            },
            emphasis: {
              show: true,
              textStyle: {
                fontSize: '12',
                color: '#fff'
              }
            }
          },
          labelLine: {
            emphasis: {
              show: true,
              lineStyle: {
                color: '#fff'
              }
            }
          },
          data: data
        }
      ]
      const option = {
        backgroundColor: '',
        // backgroundColor: {
        //   type: 'pattern',
        //   repeat: 'no-repeat',
        //   image: require('../../../assets/images/peace/people-right-top-bg.png') // 赋值
        // },
        title: {
          text: '总数量',
          subtext: total + '个',
          textStyle: {
            color: '#CFE1F7',
            fontSize: 12
          },
          subtextStyle: {
            fontSize: 16,
            color: '#FFFFFF'
          },
          x: 'center',
          y: '30%'
        },
        legend: {
          orient: 'horizontal',
          left: 'center',
          bottom: '0',
          data: xdata,
          itemWidth: 8,
          itemHeight: 8,
          itemGap: 16,
          textStyle: {
            color: '#B3C2DD' //  字体颜色
          }
        },
        // formatter: function (name) {
        //   var oa = option.series[0].data
        //   for (var i = 0; i < option.series[0].data.length; i++) {
        //     if (name === oa[i].name) {
        //       return ' ' + name + ' (' + oa[i].value + ')     '
        //     }
        //   }
        // },
        series: seriesObj
      }
      this.getPiechart2.setOption(option)
      if (data.length > 1) {
        clearInterval(this.getPiechart2Time)
        this.pieEchartSetTimeOut('getPiechart2', data)
      }
      // 鼠标移出后默认高亮
      this.getPiechart2.on('mouseout', () => {
        clearInterval(this.getPiechart2Time)
        this.pieEchartSetTimeOut('getPiechart2', data)
      })
      this.getPiechart2.clear()
      this.getPiechart2.setOption(option)
      // 随着屏幕大小调节图表
      window.addEventListener('resize', () => {
        this.getPiechart2.resize()
      })
    },
    setWPFborder() {
      const json = {
        right: 22,
        bottom: 32
      }
      window.chrome.webview.hostObjects.sync.bridge.SetLocation(JSON.stringify(json))
    }
  }
}
</script>
<style lang="scss" scoped>
.content {
  width: 100%;
  height: 100%;
  position: relative;
  .active {
    background: url('~@/assets/images/center/light-yellow.png') no-repeat;
    background-size: 100% 100%;
    color: #ffe3a6 !important;
    outline: none;
  }
  .center-center {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #4d5880;
    font-size: 16px;
  }
  .sham-content {
    pointer-events: none;
  }
  .right-content {
    position: absolute;
    top: 0;
    right: 0;
    width: 24%;
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    transition: width 0.3s;
    overflow: hidden;
    .echarts-top {
      width: 100%;
      height: 35%;
      background: url('~@/assets/images/peace/emergency-disposal-left.png') no-repeat;
      background-size: 100% 100%;
      padding: 2px;
      box-sizing: border-box;
      .alarm-analysis {
        display: flex;
        justify-content: space-around;
        box-sizing: border-box;
        width: 100%;
        height: 50%;
        .case-img {
          width: 18%;
          height: 85%;
          margin: auto;
          position: relative;
          img {
            width: 100%;
            height: 100%;
          }
          .case-num {
            width: 100%;
            position: absolute;
            text-align: center;
            top: 8%;
            color: #fff;
            font-size: 1.1rem;
            font-family: DIN-Bold, DIN;
            font-weight: bold;
            white-space:nowrap;
            span {
              font-weight: 400;
              font-size: 0.75rem;
            }
          }
          .case-anim-icon {
            position: absolute;
            bottom: 10%;
            left: calc(50% - 1rem);
            width: 2rem;
            height: 2rem;
            background: url('~@/assets/images/peace/icon-weight.png') no-repeat;
            background-size: 100% 100%;
            animation: jump 1s ease-out infinite alternate-reverse;
          }
        }
      }
    }
    .echarts-center {
      width: 100%;
      height: 32.5%;
      background: url('~@/assets/images/peace/emergency-disposal-left.png') no-repeat;
      background-size: 100% 100%;
      padding: 2px;
      box-sizing: border-box;
      .right-search {
        span {
          width: 35px;
        }
      }
    }
    .echarts-bottom {
      width: 100%;
      height: 32.5%;
      background: url('~@/assets/images/peace/emergency-disposal-left.png') no-repeat;
      background-size: 100% 100%;
      padding: 2px;
      .right-search {
        span {
          width: 35px;
        }
      }
    }
  }
  .bg-title {
    margin-top: 5px;
    // height: 23%;
    height: 2.5rem;
    line-height: 2.5rem;
    // color: #d4e3f9;
    color: #dceaff;
    padding: 0 5rem;
    font-family: TRENDS;
    /*设置为伸缩容器*/
    display: -webkit-box;
    display: -moz-box;
    display: -ms-flexbox;
    display: -webkit-flex;
    display: flex;
    /*垂直居中*/
    -webkit-box-align: center; /*旧版本*/
    -moz-box-align: center; /*旧版本*/
    -ms-flex-align: center; /*混合版本*/
    -webkit-align-items: center; /*新版本*/
    align-items: center; /*新版本*/
    justify-content: space-between;
    .right-search {
      span {
        display: inline-block;
        width: 50px;
        height: 30px;
        font-size: 14px;
        text-align: center;
        line-height: 26px;
        margin: 0 4px;
        color: #7eaef9;
        cursor: pointer;
      }
      span:hover {
        background: url('~@/assets/images/center/light-yellow.png') no-repeat;
        background-size: 100% 100%;
        color: #ffe3a6;
      }
      i {
        color: #7eaef9;
      }
    }
  }
  .bg-content {
    position: relative;
    box-sizing: border-box;
    padding: 10px 10px 15px 10px;
    width: 100%;
    height: 77%;
    #BasicAnalysisEcharts,
    #deptProduceEcharts {
      width: 100%;
      height: 100%;
    }
    .basic-bg {
      width: 13.2rem;
      height: 84%;
      position: absolute;
      top: -2%;
      left: 7.3rem;
      background: url('~@/assets/images/peace/people-right-top-bg.png') no-repeat;
      background-size: 100% 100%;
    }
  }
}
</style>
