<template>
  <el-dialog
    v-dialogDrag
    :modal="false"
    :visible="visible"
    custom-class="mainDialog main"
    append-to-body
    :close-on-click-modal="false"
    :before-close="closeDia"
    class="all-table-componentList"
  >
    <template slot="title">
      <span class="dialog-title">全部报警</span>
    </template>
    <template >
      <div class="dialog-content">
        <div class="search-box">
          <div style="margin-right: 16px" class="taskInput">
            <el-input
              v-model="searchParams.alarmObjectName"
              placeholder="请输入设备名称"
            ></el-input>
          </div>
          <div style="margin-right: 16px" class="taskInput">
            <el-select
              v-model="searchParams.projectCode"
              placeholder="报警系统"
              clearable
              @change="projectCodeChange"
            >
              <el-option
                v-for="item in alarmSourceOptions"
                :key="item.dictionaryDetailsCode"
                :label="item.dictionaryDetailsName"
                :value="item.dictionaryDetailsCode"
              >
              </el-option>
            </el-select>
          </div>
          <div style="margin-right: 16px" class="taskInput">
            <el-select
              v-model="searchParams.alarmType"
              placeholder="报警类型"
              filterable
              popper-class="new-select"
            >
              <el-option
                v-for="item in alarmTypeList"
                :key="item.alarmTypeId"
                :label="item.alarmTypeName"
                :value="item.alarmTypeId"
              >
              </el-option>
            </el-select>
          </div>
          <div style="margin-right: 16px" class="taskInput">
            <el-date-picker
              v-model="searchParams.dataRange"
              type="datetimerange"
              range-separator="至"
              start-placeholder="开始时间"
              end-placeholder="结束时间"
              popper-class="date-style"
              value-format="yyyy-MM-dd"
              format="yyyy-MM-dd"
              :clearable="false"
            >
            </el-date-picker>
          </div>
          <div style="margin-right: 16px" class="taskInput">
            <el-select
              v-model="searchParams.alarmLevel"
              placeholder="警情级别"
              filterable
              popper-class="new-select"
            >
              <el-option
                v-for="item in alarmLevelList"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              >
              </el-option>
            </el-select>
          </div>
          <div class="taskInput">
            <el-select
              ref="treeSelect"
              v-model="searchParams.alarmSpaceId"
              clearable
              placeholder="报警位置"
              @clear="handleClear"
            >
              <el-option
                hidden
                :value="searchParams.alarmSpaceId"
                :label="areaName"
              >
              </el-option>
              <el-tree
                class="search-form-tree"
                :data="serverSpaces"
                :props="serverDefaultProps"
                :load="serverLoadNode"
                lazy
                :expand-on-click-node="false"
                :check-on-click-node="true"
                @node-click="handleNodeClick"
              >
              </el-tree>
            </el-select>
          </div>

          <div class="search-btn">
            <el-button @click="resetTaskSearch">重置</el-button>
            <el-button @click="handleTaskSearch">查询</el-button>
          </div>
        </div>
        <div class="tableContent">
          <el-table
            v-loading="tableLoading"
            :data="tableData"
            height="100%"
            :cell-style="$tools.setCell(3)"
            :header-cell-style="$tools.setHeaderCell(3)"
            style="width: 100%"
            element-loading-background="rgba(0, 0, 0, 0.2)"
          >
            <el-table-column prop="alarmObjectName" label="报警对象">
            </el-table-column>
            <el-table-column prop="alarmType" label="报警类型">
            </el-table-column>
            <el-table-column
              prop="completeRegionName"
              label="报警位置"
              show-overflow-tooltip
            >
            </el-table-column>
            <el-table-column prop="alarmStartTime" label="报警时间">
            </el-table-column>
            <el-table-column prop="alarmSource" label="报警来源">
            </el-table-column>
            <el-table-column prop="abc" label="警情级别">
              <template slot-scope="scope">
                <span>{{
                  alarmLevelList.find((el) => el.value == scope.row.alarmLevel)
                    .label
                }}</span>
              </template>
            </el-table-column>
            <el-table-column label="操作">
              <template slot-scope="scope">
                <span type="text" size="small" @click="viewDetail(scope.row)"
                >查看</span
                >
              </template>
            </el-table-column>
          </el-table>
          <el-pagination
            :current-page="currentPage"
            :page-sizes="[15, 20, 30, 40]"
            :page-size="pageSize"
            layout="total, sizes, prev, pager, next, jumper"
            :total="total"
            class="pagination"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          ></el-pagination>
        </div>
      </div>
    </template>

  </el-dialog>
</template>
<script>
import moment from 'moment'
moment.locale('zh-cn')
import {
  getSpaceInfoList,
  getStructureTree,
  GetAllAlarmRecord
} from '@/utils/peaceLeftScreenApi'
import {
  GetClassifyDictList

} from '@/utils/spaceManage'
import { alarmTypeData } from '@/utils/elevatorApi'
export default {
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    params: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  data() {
    return {
      searchParams: {
        alarmObjectName: '', // 搜索关键字
        projectCode: '', // 报警来源
        alarmType: '', // 报警类型
        alarmLevel: '', // 报警等级
        alarmSpaceId: '', // 空间位置
        dataRange: [] // 时间范围
      },

      alarmSourceOptions: [],
      alarmTypeList: [],
      spaceAreaList: [],
      alarmLevelList: [
        { value: '0', label: '通知', color: '#3562DB' },
        { value: '1', label: '一般', color: '#3562DB' },
        { value: '2', label: '紧急', color: '#FF9435' },
        { value: '3', label: '重要', color: '#FA403C' }
      ],
      areaName: '',
      serverDefaultProps: {
        label: 'ssmName',
        isLeaf: 'leaf',
        children: 'children'
      },
      spaces: [],
      serverSpaces: [], // 空间位置
      tableLoading: false,
      tableData: [],
      currentPage: 1,
      pageSize: 15,
      total: 0
    }
  },
  mounted() {
    this.initDate()
    this.getAlarmSource()
    this.getTreeList()
    this.getList()
  },
  methods: {
    initDate() {
      Object.assign(this.searchParams, this.params)
      if (this.params.projectCode) {
        this.getAlarmTypeOptions(this.params.projectCode)
      }
      switch (this.params.dateType) {
        case 'day':
          this.searchParams.dataRange = [
            moment().format('YYYY-MM-DD'),
            moment().format('YYYY-MM-DD')
          ]
          break
        case 'week':
          this.searchParams.dataRange = [
            moment().startOf('isoWeek').format('YYYY-MM-DD'),
            moment().endOf('isoWeek').format('YYYY-MM-DD')
          ]
          break
        case 'month':
          this.searchParams.dataRange = [
            moment().startOf('month').format('YYYY-MM-DD'),
            moment().endOf('month').format('YYYY-MM-DD')
          ]
          break
        case 'year':
          this.searchParams.dataRange = [
            moment().startOf('year').format('YYYY-MM-DD'),
            moment().endOf('year').format('YYYY-MM-DD')
          ]
          break
      }
    },
    // 获取所属系统

    // 获取报警来源
    getAlarmSource() {
      let params = {
        dictionaryCategoryId: 'PRODUCT_CATEGORY',
        level: 1
      }
      GetClassifyDictList(params).then((res) => {
        if (res.data.code === '200') {
          this.alarmSourceOptions = res.data.data
        }
      })
    },

    projectCodeChange(val) {
      this.searchParams.alarmType = ''
      this.getAlarmTypeOptions(val)
    },
    // 获取事件类型以及报警来源分组
    getAlarmTypeOptions(val) {
      let params = {
        projectCode: val
      }
      alarmTypeData(params).then((res) => {
        if (res.data.code === '200') {
          this.alarmTypeList = res.data.data
          this.searchParams.alarmType = this.params.id
        }
      })
    },
    // 获取服务空间树形结构
    getTreeList() {
      getStructureTree().then((res) => {
        if (res.data.code === 200) {
          if (this.params.alarmSpaceId) {
            this.areaName = res.data.data.find(v => v.id == this.params.alarmSpaceId).ssmName
          }
          this.spaces = res.data.data
          // 增加 懒加载节点
          res.data.data.map((e) => {
            e.leaf = false
          })
          // 转换为树形结构数据
          this.serverSpaces = this.$tools.transData(
            res.data.data,
            'id',
            'pid',
            'children'
          )
        }
      })
    },
    handleNodeClick(data) {
      this.searchParams.alarmSpaceId = data.id
      this.areaName = data.ssmName
      this.$refs.treeSelect.blur()
    },
    // 服务空间树结构点击获取自己及父节点ID并请求数据实现懒加载
    spaceTreeChange(value, resolve) {
      const child = this.$tools.ListTree(this.spaces, value.pid)
      child.push(value.id)
      const treeId = child.toString()
      const data = {
        current: 1,
        functionDictId: '',
        simCode: treeId,
        size: 999
      }
      getSpaceInfoList(data).then((res) => {
        if (res.data.code === 200) {
          if (typeof resolve === 'function') {
            var treeNodeData = JSON.parse(
              JSON.stringify(res.data.data.records)
            )
            treeNodeData.map((e) => {
              e.leaf = true
            })
            resolve(treeNodeData)
          }
        }
      })
    },
    // 下拉树懒加载
    serverLoadNode(node, resolve) {
      if (node.level === 0) {
        return resolve(node.data)
      } else if (node.level > 0 && node.level < 4) {
        return resolve(node.data.children)
      } else {
        return this.spaceTreeChange(node.data, resolve)
      }
    },
    // 空间数据清除
    handleClear() {
      this.searchParams.alarmSpaceId = ''
      this.areaName = ''
    },

    closeDia() {
      this.$emit('close')
    },
    getList() {
      const {
        projectCode,
        alarmType,
        alarmLevel,
        alarmSpaceId,
        dataRange,
        alarmObjectName
      } = this.searchParams
      const params = {
        pageNo: this.currentPage,
        pageSize: this.pageSize,
        projectCode: projectCode,
        alarmType: alarmType,
        alarmLevel,
        startTime: dataRange[0],
        endTime: dataRange[1]
      }
      if (this.params.spaceIdList && this.params.spaceIdList.length) {
        params.spaceIdList = this.params.spaceIdList
      } else {
        params.alarmSpaceId = alarmSpaceId
      }
      // 关键字适应字段
      if (/^BJ/.test(alarmObjectName)) {
        params.alarmId = alarmObjectName
      } else {
        params.alarmObjectName = alarmObjectName
      }

      this.tableLoading = true
      GetAllAlarmRecord(params)
        .then((res) => {
          this.tableLoading = false
          if (res.data.code === '200') {
            const data = res.data.data?.records ?? []
            this.tableData = data
            this.total = res.data.data?.total ?? 0
          } else {
            this.tableData = []
            this.total = 0
          }
        })
        .catch(() => {
          this.tableLoading = false
        })
    },
    resetTaskSearch() {
      this.searchParams = {
        alarmObjectName: '', // 搜索关键字
        projectCode: '', // 报警来源
        alarmType: '', // 报警类型
        alarmLevel: '', // 报警等级
        alarmSpaceId: '', // 空间位置
        dataRange: [] // 时间范围
      }
      this.currentPage = 1
      this.getList()
    },
    handleTaskSearch() {
      this.getList()
    },
    handleSizeChange(val) {
      this.currentPage = 1
      this.pageSize = val
      this.getList()
    },
    handleCurrentChange(val) {
      this.currentPage = val
      this.getList()
    },
    viewDetail (row) {
      this.$emit('showDetail', row)
    }
  }
}
</script>
<style lang="scss" scoped>
::v-deep .mainDialog {
  width: 80%;
  height: 84vh;
  margin-top: 6vh !important;
  // border: 1px solid #5996f9;
  background-color: transparent !important;
  pointer-events: auto;
  background-image: url("@/assets/images/table-bg.png");
  background-repeat: no-repeat;
  background-size: 100% 100%;
  box-shadow: none;
  .el-dialog__headerbtn {
    transform: translateX(-36px);
    width: 25px;
    height: 25px;
    background-image: url("@/assets/images/close.png");
    background-repeat: no-repeat;
    background-size: 100% 100%;
    .el-dialog__close::before {
      display: none;
    }
  }
  .dialog-title {
    display: inline-block;
    width: 100%;
    text-align: center;
    transform: translateY(-6px);
    color: #cbdeed;
  }
  .dialog-title::before {
    display: none;
  }

  .el-dialog__header {
    background-color: transparent;
  }
  .el-dialog__body {
    padding: 0;
    height: calc(100% - 60px);
    max-height: fit-content;
    overflow-y: hidden;
    background-color: transparent;
    padding: 0px 80px;
    .dialog-content {
      width: 100%;
      height: 100%;
      background-color: transparent;

      .el-table {
        background-color: transparent;
        border: 1px solid #202d4c;
        .el-table__header-wrapper {
          background-color: transparent !important;
        }
        .el-table__body-wrapper {
          background-color: transparent !important;
        }
        tr {
          background-color: transparent !important;
        }
        td {
          background-color: transparent !important;
        }
      }
    }
  }
}
:deep(.search-box) {
  display: flex;
  flex-wrap: wrap;

  .taskInput {
    .el-input {
      width: 140px;
    }
    .el-cascader {
      line-height: 35px;
      .el-input__inner {
        height: 35px !important;
      }
    }
  }

  .el-button {
    background-image: url("@/assets/images/btn.png");
    background-repeat: no-repeat;
    background-size: 100% 100%;
    height: 35px;
  }
  .search-btn {
    // margin: 10px 0;
    margin-left: auto;
  }
  .el-select {
    .el-input .el-input__inner {
      border-color: rgba(133, 145, 206, 0.5);
      width: 150px;
      height: 35px;
    }
  }

  .el-date-editor {
    width: 300px;
    height: 35px;
    background-color: #14233e;
    border-color: rgba(133, 145, 206, 0.5);
    .el-input__icon {
      transform: translateY(-2px);
    }
    .el-range-input {
      background-color: #14233e;
      color: rgba(255, 255, 255, 0.8);
    }
    .el-range-separator {
      color: rgba(255, 255, 255, 0.8);
      transform: translateY(-2px);
    }
  }
  .el-input .el-input__inner {
    border-color: rgba(133, 145, 206, 0.5);
    border-radius: 4px;
    color: rgba(255, 255, 255, 0.8);
  }
}
.search-box {
  margin: 5px 0 15px;
}

.search-box .el-input {
  // width: 120px;
  height: 35px;
}
.tableContent {
  width: 100%;
  height: calc(100% - 56px - 60px);
}
.search-form-tree {
  color: #fff !important;
  background: transparent !important;

  :deep(.el-tree-node__content:hover) {
    background: transparent !important;

    .el-tree-node__label {
      color: #ffe3a6;
    }
  }
}
</style>
