<template>
  <div class="monitor-details-view">
    <div class="md-header-box">
      <div class="md-header-box-left">
        <div class="status-box">
          <span class="el-icon-warning"></span>
          <span>异常</span>
        </div>
        <div class="position-box" @click="operating('detail')">
          <span>11号楼1层MR1</span>
          <span class="el-icon-arrow-right" style="font-weight: bold"></span>
        </div>
      </div>
      <div class="md-header-box-right">
        <div class="view-nav-box">
          <div
            v-for="(item, index) in navOptions"
            :key="index"
            class="nav-box"
            :class="{ active: index == navIdx }"
            @click="handleNavIdx(index)"
          >
            <div class="nav">
              {{ item.name }}
            </div>
          </div>
        </div>
        <div class="liaison-box">
          <div class="img-div">
            <img
              src="@/assets/images/lxr.png"
              style="width: 100%; height: 100%"
              alt=""
            />
          </div>
          <div class="liaison-box-text">联系人</div>
        </div>
      </div>
    </div>
    <div class="md-content-box" style="height: calc(100% - 50px)">
      <component :is="activeComponent"></component>
    </div>
  </div>
</template>
<script>
export default {
  components: {
    realTimeMonitoring: () => import('./components/realTimeMonitoring'),
    alarmRecord: () => import('./components/alarmRecord.vue')
  },
  data() {
    return {
      activeComponent: 'realTimeMonitoring',
      navIdx: 0,
      navOptions: [
        { name: '实时监测', value: 'el-icon-s-home' },
        { name: '运行分析', value: 'el-icon-s-home' },
        { name: '报表统计', value: 'el-icon-s-home' },
        { name: '报警记录', value: 'el-icon-s-home' },
        { name: '实时定位', value: 'el-icon-s-home' },
        { name: '传感器状态', value: 'el-icon-s-home' }
      ]
    }
  },
  mounted() {},
  methods: {
    /** nav 切换事件 */
    handleNavIdx(idx) {
      this.navIdx = idx
      if (idx == 3) {
        this.activeComponent = 'alarmRecord'
      } else {
        this.activeComponent = 'realTimeMonitoring'
      }
    },
    operating(type, row) {
      switch (type) {
        case 'detail':
          this.$emit('openDetailComponent', 'monitorListIndex')
          break
        default:
          break
      }
    }
  }
}
</script>
<style lang="scss" scoped>
%flex-style-center {
  display: flex;
  align-items: center;
  justify-content: center;
}
.monitor-details-view {
  width: 100%;
  height: 100%;
  padding: 0px 40px;
  box-sizing: border-box;
  .md-header-box {
    @extend %flex-style-center;
    justify-content: space-between;
    margin-bottom: 0.5rem;
  }
  .md-content-box {
    overflow-y: auto;
  }
}
.md-header-box-left {
  @extend %flex-style-center;
  .status-box {
    width: 5rem;
    height: 1.5rem;
    border-radius: 2px;
    @extend %flex-style-center;
    color: #ff2d55;
    padding: 0.3rem 0.3rem;
    background: #ff2d5533;
    margin-right: 0.5rem;
    span:nth-child(1) {
      font-size: 16px;
      margin-right: 0.3rem;
    }
  }
  .position-box {
    font-size: 1.25rem;
    cursor: pointer;
  }
}
.md-header-box-right {
  @extend %flex-style-center;
  .view-nav-box {
    display: flex;
    align-items: center;
    box-sizing: border-box;
    .nav-box {
      height: 2.5rem;
      line-height: 2.5rem;
      width: 8.75rem;
      font-size: 1rem;
      text-align: center;
      color: #a4afc1;
      cursor: pointer;
      box-sizing: border-box;
      border: 1px dashed #26314f;
      border-right: none;
      background: url(@/assets/images/nav-bg.png) no-repeat;
      background-size: cover;
    }
    .nav-box:last-child {
      border-right: 1px dashed #26314f;
    }
    .active {
      color: #b0e3fa;
      background: url(@/assets/images/nav-bg-xz.png) no-repeat;
      background-size: cover;
    }
  }
  .liaison-box {
    margin-left: 1rem;
    @extend %flex-style-center;
    color: #8bddf5;
    cursor: pointer;
    .img-div {
      width: 1.25rem;
      height: 1.25rem;
      margin-right: 0.3rem;
    }
  }
}
</style>