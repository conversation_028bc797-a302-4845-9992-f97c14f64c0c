/*
 * @Author: <PERSON>
 * @Date: 2021-12-14 15:16:55
 * @Last Modified by: <PERSON>
 * @Last Modified time: 2021-12-15 13:50:07
 */

// 导入axios
import axios from 'axios'
import store from '@/store'
// import qs from 'qs'
// 使用element-ui Message做消息提醒
// import { Message } from 'element-ui'
// 1. 创建新的axios实例，
const service = axios.create({
  // 公共接口--这里注意后面会讲
  //   baseURL: '',
  // 超时时间 单位是ms，这里设置了50s的超时时间
  timeout: 5 * 10000
})
// 2.请求拦截器
service.interceptors.request.use(
  (config) => {
    if (store.getters.isLogin) {
      config.headers.Authorization = 'Bearer ' + store.state.loginInfo.token
    }
    return config
  },
  (error) => {
    Promise.reject(error)
  }
)

// 3.响应拦截器
service.interceptors.response.use(
  (response) => {
    // If the API returns a 401 error and the user is logged in, attempt to refresh the token and retry the API call
    if (response.data.code == 401 && store.getters.isLogin) {
      return store.dispatch('login').then(() => {
        // If the token refresh is successful, update the loginInfo in the store and retry the API call
        const config = response.config
        config.headers.Authorization = 'Bearer ' + store.state.loginInfo.token
        return axios(config)
      })
    }
    return Promise.resolve(response)
  },
  (error) => {
    // Message.error('接口似乎走丢了')
    return Promise.reject(error)
  }
)
service.defaults.retry = 1
service.defaults.retryDelay = 1000
service.interceptors.response.use(undefined, function axiosRetryInterceptor(err) {
  var config = err.config
  // If config does not exist or the retry option is not set, reject
  if (!config || !config.retry) return Promise.reject(err)
  // Set the variable for keeping track of the retry count
  config.__retryCount = config.__retryCount || 0
  // Check if we've maxed out the total number of retries
  if (config.__retryCount >= config.retry) {
    // Reject with the error
    return Promise.reject(err)
  }
  // Increase the retry count
  config.__retryCount += 1
  // Create new promise to handle exponential backoff
  var backoff = new Promise(function (resolve) {
    setTimeout(function () {
      resolve()
    }, config.retryDelay || 1)
  })
  // Return the promise in which recalls axios to retry the request
  return backoff.then(function () {
    return service(config)
  })
})
// 4.导入文件
export default service
