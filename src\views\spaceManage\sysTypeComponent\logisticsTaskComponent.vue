<template>
  <div class="logisticsTaskComponent">
    <!-- <ModuleCard title="运送任务总览" class="module-container" :floorName="!['Kongtiao', 'Electricity'].includes(roomData.tabName)? roomData.title: ''" style="height: 24%" :hasExpand="true"> -->
    <ModuleCard title="运送任务总览" class="module-container" style="height: 24%">
      <div slot="title-right" class="title-right">
        <el-dropdown trigger="click" @command="changeDateType">
          <span class="el-dropdown-link">
            {{ dateTagList.find((v) => v.value == dateActive)?.text ?? "" }}
            <i class="el-icon-arrow-down"></i>
          </span>
          <el-dropdown-menu slot="dropdown">
            <el-dropdown-item v-for="item in dateTagList" :key="item.value" :command="item.value" :class="{ isBjxl: dateActive == item.value }">
              {{ item.text }}
            </el-dropdown-item>
          </el-dropdown-menu>
        </el-dropdown>
        <span class="viewMore" @click="showTaskList"></span>
      </div>
      <div slot="content" class="module-content" style="height: 100%">
        <div class="statistics_top_new">
          <div v-for="(item, index) in statisticsData" :key="index" class="statistics_top_item">
            <img src="@/assets/images/qhdsys/statistics-new.png" alt="" />
            <div class="statistics_top_content">
              <p class="statistics_top_title">{{ item.name }}</p>
              <div class="font_bg">
                <p class="color_font" :style="{ color: item.key == 'abnormalDeviceTotal' ? '' : '',}">
                  {{ deviceAccountStatistics[item.key] || 0 }}
                </p>
                <p class="color_unit">{{ item?.unit ?? "" }}</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </ModuleCard>
    <ModuleCard title="配送任务排行" class="module-container" style="height: 24%">
      <div slot="title-right" class="title-right">
        <el-dropdown trigger="click" @command="handleChangeType">
          <span class="el-dropdown-link">
            {{ typeTagList.find((v) => v.value == typeActive)?.text ?? "" }}
            <i class="el-icon-arrow-down"></i>
          </span>
          <el-dropdown-menu slot="dropdown">
            <el-dropdown-item v-for="item in typeTagList" :key="item.value" :command="item.value" :class="{ isBjxl: typeActive == item.value }">
              {{ item.text }}
            </el-dropdown-item>
          </el-dropdown-menu>
        </el-dropdown>
      </div>
      <div slot="content" v-loading="statisticsEchartsLoading" class="module-content" style="height: 100%">
        <div style="width: 100%; height: 100%">
          <div v-if="taskTypeList.length" style="width: 100%; height: 100%; overflow: hidden">
            <div id="TaskTypeStatist" ref="TaskTypeStatist"></div>
          </div>
          <div v-else class="center-center">暂无数据</div>
        </div>
      </div>
    </ModuleCard>
    <ModuleCard title="运送趋势" class="module-container" style="height: 52%">
      <div slot="content" v-loading="carryTrendsEchartsLoading" style="height: 100%">
        <div class="work-order-trend">
          <div class="box-content">
            <div class="trend-box" style="width: 100%; height: 100%">
              <div class="checkbox-contain">
                <!-- <el-checkbox v-model="checkAll" fill="#52FFFC" :indeterminate="isIndeterminate" @change="changeCheckAll">全部任务</el-checkbox> -->
                <el-checkbox-group v-model="checkList" fill="#52FFFC" @change="changeCheckBox">
                  <div v-for="(item, index) in workOrderTypeList.slice(0, 2)" :key="index">
                    <el-checkbox :key="item.id" :label="item.name"></el-checkbox>
                  </div>
                </el-checkbox-group>
                <img src="@/assets/images/checkbox_add.png" @click="showPanel = !showPanel"/>
                <div v-show="showPanel" class="panel">
                  <!-- <el-checkbox v-model="checkAll" fill="#52FFFC" :indeterminate="isIndeterminate" @change="changeCheckAll">全部任务</el-checkbox> -->
                  <el-checkbox-group v-model="checkList" fill="#52FFFC" @change="changeCheckBox">
                    <el-checkbox v-for="item in workOrderTypeList" :key="item.id" :label="item.name"></el-checkbox>
                  </el-checkbox-group>
                </div>
              </div>
              <div id="workOdertTrendEcharts"></div>
            </div>
          </div>
        </div>
      </div>
    </ModuleCard>
    <TaskList v-if="taskListVisible" ref="taskList" :visible="taskListVisible"     @close="closeTaskList"></TaskList>
  </div>
</template>

<script>
import moment from 'moment'
moment.locale('zh-cn')
import { GetFactoryCodeByAgv, GetGeneralFunctionByAgv, GetRobotList, GetMonitoringCarryTrends } from '@/utils/spaceManage'
import * as echarts from 'echarts'
import TaskList from './components/taskList.vue'
export default {
  name: 'logisticsTaskComponent',
  components: {
    TaskList
  },
  props: {
    roomData: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      checkAll: true,
      isIndeterminate: false,
      checkList: [], // 日期范围
      agvGateway: '', // 机器人设备网关
      showPanel: false,
      dateActive: 'month',
      dateRange: [moment().startOf('month').format('YYYY-MM-DD') + ' 00:00:00', moment().format('YYYY-MM-DD') + ' 23:59:59'],
      // dateRange: [moment().startOf('isoWeek').format('YYYY-MM-DD') + ' 00:00:00', moment().endOf('isoWeek').format('YYYY-MM-DD') + ' 23:59:59'],
      typeActive: 'jobTypes',
      dateTagList: [
        { text: '本周', value: 'week' },
        { text: '本月', value: 'month' },
        { text: '本年', value: 'year' }
      ],
      typeTagList: [
        { text: '任务类型', value: 'jobTypes' },
        { text: '发起科室', value: 'startPositions' },
        { text: '接收科室', value: 'positions' }
      ],
      statisticsEchartsLoading: false,
      carryTrendsEchartsLoading: false,
      statisticsData: [
        { name: '今日配送数', key: 'distributeCount', unit: '' },
        { name: '今日平均耗时', key: 'distributeTime', unit: '分钟' },
        { name: '累计配送数', key: 'allDistributeCount', unit: '' },
        { name: '累计运送里程', key: 'allDistance', unit: '米' }
      ],
      deviceAccountStatistics: {
        distributeCount: 0,
        distributeTime: 0,
        allDistributeCount: 0,
        allDistance: 0
      },
      taskTypeList: [],
      workOrderTypeList: [],
      taskListVisible: false
    }
  },
  mounted() {
    this.getFactoryCodeByAgv()
    this.getRobotList()
  },
  methods: {
    // 运送趋势
    getMonitoringCarryTrends() {
      let params = {
        robotIds: this.workOrderTypeList.filter(v => this.checkList.includes(v.name)).map(v => v.id).join(','),
        timeType: this.dateActive == 'year' ? 2 : 1,
        startTime: this.dateRange[0],
        endTime: this.dateRange[1]
      }
      this.carryTrendsEchartsLoading = true
      GetMonitoringCarryTrends(params).then(res => {
        this.carryTrendsEchartsLoading = false
        if (res.data.code == 200) {
          this.$nextTick(() => {
            if (this.dateActive == 'day') {
              this.getStatisticsData(res.data.data.series.map(item => {
                return {
                  name: item.name,
                  value: item.data[0]
                }
              }), 'workOdertTrendEcharts')
            } else {
              this.workOderTrendEchart(res.data.data.xAxis.data, res.data.data.series)
            }
          })
        }
      })
    },
    // 机器人列表
    getRobotList() {
      let params = {
        terms: [
          {column: 'systemTypeCode', 'value': this.roomData.projectCode},
          {column: 'deviceTypeCode', 'value': this.roomData.categoryCode}
        ]
      }
      GetRobotList(params).then(res => {
        if (res.data.status == 200) {
          this.checkList = res.data.result.map(v => v.name)
          this.workOrderTypeList = res.data.result
          this.getMonitoringCarryTrends()
        }
      })
    },
    // 通用功能接口
    getGeneralFunctionByAgv(functionId) {
      let params = {
        deviceId: this.agvGateway,
        functionId,
        params: {
          startTime: this.dateRange[0],
          endTime: this.dateRange[1]
        }
      }
      return new Promise((resolve, reject) => {
        GetGeneralFunctionByAgv(params).then((res) => {
          if (res.data.status == 200) {
            resolve(res.data.result)
          } else {
            reject(res)
          }
        }).catch((err) => {
          reject(err)
        })
      })
    },
    // 获取机器人网关id
    getFactoryCodeByAgv() {
      GetFactoryCodeByAgv().then(async res => {
        if (res.data.code == 200) {
          this.agvGateway = res.data.data
          this.handleChangeType('jobTypes')
          let todayJobData = await this.getGeneralFunctionByAgv('todayJobData').catch()
          let totalJobData = await this.getGeneralFunctionByAgv('totalJobData').catch()
          this.deviceAccountStatistics.distributeCount = todayJobData[0].distributeCount
          this.deviceAccountStatistics.distributeTime = Math.floor(todayJobData[0].distributeTime / 60)
          this.deviceAccountStatistics.allDistributeCount = totalJobData[0].distributeCount
          this.deviceAccountStatistics.allDistance = totalJobData[0].distance
        }
      })
    },
    changeCheckBox() {
      this.getMonitoringCarryTrends()
    },
    changeCheckAll() {
      this.checkList = this.checkAll ? this.workOrderTypeList.map((v) => v.name) : []
      this.getMonitoringCarryTrends()
    },
    workOderTrendEchart(dateArr, echartsData) {
      const getchart = echarts.init(
        document.getElementById('workOdertTrendEcharts')
      )
      var color = [
        'rgba(255, 202, 100, 1)',
        'rgba(60, 193, 255, 1)',
        'rgba(65, 228, 187, 1)'
      ]
      const seriesObj = []
      echartsData.forEach((item, index) => {
        const randomRgbColor = this.$tools.randomRgbColor('array')
        seriesObj.push({
          name: item.name,
          type: 'line',
          smooth: false,
          showAllSymbol: true,
          symbol: 'circle',
          symbolSize: 0,
          itemStyle: {
            color: '#3eb5dd',
            borderColor: '#f1f1f1',
            borderWidth: 1
          },
          lineStyle: {
            normal: {
              width: 2,
              color: color[index] ?? randomRgbColor[1]
            }
          },
          data: item.data
        })
      })
      let dataZoom = [
        {
          type: 'slider', // 滑动条型数据区域缩放组件
          realtime: true, // 拖动时，是否实时更新系列的视图。如果设置为 false，则只在拖拽结束的时候更新。
          // start: 0, //数据窗口范围的起始百分比。范围是：0 ~ 100。表示 0% ~ 100%。
          // end: 10,  // 数据窗口范围的结束百分比。范围是：0 ~ 100
          height: 4,
          endValue: 5, // 数据窗口范围的结束数值。如果设置了 dataZoom-inside.end 则 endValue 失效
          fillerColor: 'rgba(17, 100, 210, 0.4)', // 滚动条颜色
          borderColor: 'rgba(17, 100, 210, 0.1)',
          handleSize: 0, // 两边手柄尺寸
          showDetail: false, // 拖拽时是否展示滚动条两侧的文字
          bottom: '8%', // 组件离容器上侧的距离
          zoomLock: true // 是否只平移不缩放
          // moveOnMouseMove:true, //开启鼠标移动窗口平移
          // zoomOnMouseWheel :true, //开启鼠标滚轮缩放
        },
        {
          type: 'inside', // 内置型数据区域缩放组件
          show: false,
          // start: 0,
          // end: 10,
          endValue: 11, // 最多12个
          zoomOnMouseWheel: false, // 关闭鼠标滚轮缩放
          moveOnMouseWheel: true, // 开启鼠标滚轮窗口平移
          moveOnMouseMove: true // 开启鼠标移动窗口平移
        }
      ]
      let option
      if (dateArr.length) {
        option = {
          tooltip: {
            trigger: 'axis',
            backgroundColor: '#0D2169',
            borderColor: '#727382',
            textStyle: {
              color: '#fff'
            },
            axisPointer: {
              // 坐标轴指示器，坐标轴触发有效
              type: 'shadow' // 默认为直线，可选为：'line' | 'shadow'
            }
          },
          dataZoom: dataZoom,
          legend: {
            show: false,
            data: Array.from(echartsData, ({ name }) => name),
            type: 'scroll',
            orient: 'horizontal',
            left: 'right',
            top: '0',
            itemGap: 16,
            textStyle: {
              color: '#B3C2DD'
            }
          },
          grid: {
            left: '2%',
            right: '0%',
            bottom: '15%',
            top: '5%',
            width: '90%',
            containLabel: true
          },
          xAxis: {
            type: 'category',
            axisLine: {
              show: true,
              lineStyle: {
                color: '#878EA9',
                width: 1,
                type: 'solid'
              }
            },
            axisTick: {
              show: false
            },
            axisLabel: {
              interval: 0
            },
            boundaryGap: false,
            data: dateArr
          },
          yAxis: {
            type: 'value',
            axisLabel: {
              show: true,
              textStyle: {
                color: '#878EA9'
              }
            },
            axisLine: {
              lineStyle: {
                type: 'solid',
                color: '#3862B7',
                width: '1'
              }
            },
            splitLine: {
              lineStyle: {
                color: '#314A89',
                width: 1,
                type: 'solid'
              }
            },
            axisTick: {
              show: false
            }
          },
          series: seriesObj
        }
      } else {
        option = {
          title: {
            text: '暂无数据',
            x: 'center',
            y: 'center',
            textStyle: {
              fontSize: 14,
              fontWeight: 'normal',
              color: '#4d5880'
            }
          }
        }
      }
      getchart.clear()
      getchart.setOption(option)
      // 随着屏幕大小调节图表
      window.addEventListener('resize', () => {
        getchart.resize()
      })
    },
    getStatisticsData(data, domId) {
      domId == 'TaskTypeStatist' ? this.statisticsEchartsLoading = false : ''
      data.sort((a, b) => {
        return a.value - b.value
      })
      const getchart = echarts.init(document.getElementById(domId))
      let option
      if (data.length) {
        option = {
          legend: {},
          grid: {
            top: '0%',
            left: '5%',
            right: '5%',
            bottom: '0%',
            containLabel: true
          },
          xAxis: {
            type: 'value',
            show: false
          },
          yAxis: [
            {
              type: 'category',
              data: data.map((item) => item.name),
              axisTick: {
                show: false // 不显示坐标轴刻度线
              },
              axisLine: {
                show: false // 不显示坐标轴线
              },
              splitLine: {
                // 网格线
                show: false
              },
              axisLabel: { textStyle: { color: '#fff', fontSize: '14' } }
            },
            {
              type: 'category', // 坐标轴类型
              // inverse: true, // 是否是反向坐标轴
              axisTick: {
                show: false // 不显示坐标轴刻度线
              },
              axisLine: {
                show: false // 不显示坐标轴线
              },
              splitLine: {
                // 网格线
                show: false
              },
              axisLabel: {
                textStyle: {
                  color: '#FFFFFFCC',
                  fontSize: '14'
                },
                formatter: (value) => {
                  return value
                }
              },
              data: data.map((item) => item.value)
            }
          ],
          series: [
            {
              type: 'bar',
              stack: 'total',
              label: {
                show: false
              },
              emphasis: {
                focus: 'series'
              },
              data: data,
              barWidth: 8,
              itemStyle: {
                normal: {
                  barBorderRadius: [15, 15, 15, 15],
                  color: function (params) {
                    // 通过返回值的下标一一对应将颜色赋给柱子上
                    return '#0A84FFFF'
                  }
                },
                // 鼠标移入改变颜色
                emphasis: {
                  color: '#FFCA64FF'
                }
              },
              showBackground: true, // 显示背景色
              backgroundStyle: {
                color: '#384156'
              }
            }
          ],
          dataZoom: [
            {
              yAxisIndex: [0, 1],
              orient: 'vertical',
              show: true,
              type: 'slider',
              start: 100,
              // end: 100,
              width: 8,
              left: '99%',
              borderColor: 'rgba(43,48,67,.1)',
              fillerColor: '#6580b8',
              zoomLock: true,
              showDataShadow: false,
              backgroundColor: 'rgba(43,48,67,.1)',
              showDetail: false,
              // realtime: true,
              filterMode: 'filter',
              handleIcon:
                'M-9.35,34.56V42m0-40V9.5m-2,0h4a2,2,0,0,1,2,2v21a2,2,0,0,1-2,2h-4a2,2,0,0,1-2-2v-21A2,2,0,0,1-11.35,9.5Z',
              handleStyle: {
                color: '#6580b8',
                borderColor: '#6580b8'
              },
              maxValueSpan: 4,
              minValueSpan: 4,
              brushSelect: false
            },
            {
              type: 'inside',
              show: false,
              yAxisIndex: [0, 1],
              height: 8
            }
          ]
        }
      } else {
        option = {
          title: {
            text: '暂无数据',
            x: 'center',
            y: 'center',
            textStyle: {
              fontSize: 14,
              fontWeight: 'normal',
              color: '#4d5880'
            }
          }
        }
      }
      getchart.clear()
      getchart.setOption(option)
      // 随着屏幕大小调节图表
      window.addEventListener('resize', () => {
        getchart.resize()
      })
    },
    async changeDateType(val) {
      const date = {
        week: [moment().startOf('isoWeek').format('YYYY-MM-DD') + ' 00:00:00', moment().endOf('isoWeek').format('YYYY-MM-DD') + ' 23:59:59'],
        month: [moment().startOf('month').format('YYYY-MM-DD') + ' 00:00:00', moment().format('YYYY-MM-DD') + ' 23:59:59'],
        year: [moment().startOf('year').format('YYYY-MM-DD') + ' 00:00:00', moment().format('YYYY-MM-DD') + ' 23:59:59']
      }
      this.dateRange = date[val]
      this.dateActive = val
      this.handleChangeType('jobTypes')
      this.getMonitoringCarryTrends()
      let totalJobData = await this.getGeneralFunctionByAgv('totalJobData')
      this.deviceAccountStatistics.allDistributeCount = totalJobData[0].distributeCount
      this.deviceAccountStatistics.allDistance = totalJobData[0].distance
    },
    async handleChangeType(val) {
      this.typeActive = val
      this.statisticsEchartsLoading = true
      let jobTypes = await this.getGeneralFunctionByAgv(val)
      this.taskTypeList = jobTypes.length ? jobTypes[0] : []
      this.$nextTick(() => {
        this.getStatisticsData(this.taskTypeList.map(item => {
          return {
            name: item?.jobTypeStr ?? item.positionName,
            value: item?.jobCount ?? item.distributeJobCount
          }
        }), 'TaskTypeStatist')
      })
    },
    showTaskList () {
      this.taskListVisible = true
      try {
        window.chrome.webview.hostObjects.sync.bridge.ShowDialog(true)
      } catch (error) {}
    },
    closeTaskList () {
      this.taskListVisible = false
      try {
        window.chrome.webview.hostObjects.sync.bridge.ShowDialog(false)
      } catch (error) {}
    }
  }
}
</script>

<style lang="scss" scoped>
@import "../style/module.scss";
.logisticsTaskComponent {
  width: 100%;
  height: 100%;
}
.statistics_top_new {
  height: 130px;
  display: flex;
  flex-wrap: wrap;
  .statistics_top_item {
    height: 50%;
    width: 50%;
    // padding: 16px 0;
    display: flex;
    justify-content: center;
    align-items: center;
    img {
      width: 50px;
      height: 50px;
    }
  }
  .statistics_top_content {
    .statistics_top_title {
      padding-left: 20px;
      font-size: 14px;
      font-family: HarmonyOS Sans SC-Regular, HarmonyOS Sans SC;
      font-weight: 400;
      color: #b0e3fa;
      margin-bottom: 6px;
    }
    .font_bg {
      width: 125px;
      height: 32px;
      background: url(./../../../assets/images/qhdsys/Group-new.png) no-repeat
        100%;
      display: flex;
      align-items: center;
      justify-content: center;
      .color_font {
        font-size: 20px;
        font-family: HarmonyOS Sans SC-Bold, HarmonyOS Sans SC;
        font-weight: bold;
        color: #ffca64;
        margin-bottom: 3px;
      }
      .color_unit {
        margin-left: 3px;
        font-size: 14px;
        font-family: HarmonyOS Sans SC-Light, HarmonyOS Sans SC;
        font-weight: 300;
        color: rgba(255, 255, 255, 0.5);
      }
    }
  }
}
.el-dropdown-link {
  font-size: 14px;
  font-weight: 300;
  color: #ffffff;
  line-height: 16px;
  .el-icon-arrow-down {
    font-size: 12px;
  }
}
#TaskTypeStatist {
  width: 100%;
  height: 100%;
}
.work-order-trend {
  width: 100%;
  height: calc(100%);
}
.box-content {
  width: 100%;
  // height: calc(100% - 30px);
  height: calc(100%);
  position: relative;
  box-sizing: border-box;
  padding: 10px 10px 0px 10px;
  .pie_background {
    left: 26%;
  }
  #workOdertTypeEcharts,
  #workOdertTrendEcharts {
    position: relative;
    width: 100%;
    height: calc(100% - 20px);
    z-index: 2;
  }
  ::v-deep .el-table {
    border: none !important;
    .el-table__header-wrapper {
      .cell {
        padding-left: 0;
        padding-right: 0;
        text-align: center;
        white-space: nowrap;
      }
    }
    .el-table__body-wrapper {
      td.el-table__cell div {
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
      }
    }
    .el-table__body {
      tr {
        background: center;
      }
      td.el-table__cell,
      th.el-table__cell.is-leaf {
        border-right: 2px solid #0a164e;
        border-bottom: 2px solid #0a164e;
        background: rgba(56, 103, 180, 0.2);
        color: #fff;
      }
    }
  }
}
.trend-box {
  width: 100%;
  height: 100%;
  position: relative;
  .checkbox-contain {
    display: flex;
    justify-content: flex-end;
    & > .el-checkbox-group .el-checkbox {
      margin-left: 5px;
    }
    // position: relative;
    img {
      width: 18px;
      height: 18px;
      margin-left: 12px;
      cursor: pointer;
    }
    .panel {
      position: absolute;
      right: 0;
      top: 20px;
      background-color: #374b79;
      padding: 0 8px 8px;
      z-index: 9;
      max-height: calc(100% - 30px);
      overflow: auto;
      .el-checkbox-group {
        display: flex;
        flex-direction: column;
        align-items: flex-end;
      }
      .el-checkbox {
        width: 100%;
        display: flex;
        // justify-content: space-between;
        align-items: center;
        margin-right: 0;
        margin-top: 5px;
      }
    }
  }
}
.viewMore {
  display: inline-block;
  width: 24px;
  height: 24px;
  margin-left: 10px;
  background: url('@/assets/images/qhdsys/bg-gd.png') no-repeat center / 100% 100%;
}
.title-right{
  display: flex;
  align-items: center;
}
.el-checkbox-group {
  display: flex;
  justify-content: end;
}
</style>

