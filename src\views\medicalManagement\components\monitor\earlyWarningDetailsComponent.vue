<template>
  <div class="early-warning-continer">
    <ModuleCard title="预警记录" style="height: 100%">
      <div slot="title-right">
        <div class="chart-title-box">
          <div class="chart-title"></div>
          <div class="chart-btn">
            <div class="dropdown-div"></div>
            <div class="chart-icon">
              <img src="@/assets/images/order_more.png" class="order-more" />
            </div>
          </div>
        </div>
      </div>
      <div slot="content" style="height: 100%">
        <div class="chart-div" style="height: calc(100% - 10px)">
          <el-table
            v-el-table-infinite-scroll="tableLoadMore"
            v-scrollHideTooltip
            v-loading="deviceTableLoading"
            class="table-center-transfer"
            :data="tableData"
            height="100%"
            :cell-style="{
              padding: ' 4px',
              backgroundColor: 'transparent',
              border: 'none',
              padding: '3px',
            }"
            :header-cell-style="{
              background: '#8591CE26!important',
              color: '#8BDDF5FF',
              padding: '4px 4px',
              fontWeight: 'bold',
            }"
            style="width: 100%"
            element-loading-background="rgba(0, 0, 0, 0.2)"
          >
            <el-table-column
              prop="imsName"
              show-overflow-tooltip
              label="设备名称"
              width="85"
            ></el-table-column>
            <el-table-column
              prop="date"
              show-overflow-tooltip
              label="报警时间"
              width="90"
            ></el-table-column>
            <el-table-column
              prop="val"
              show-overflow-tooltip
              label="报警参数值"
              width="120"
            ></el-table-column>
            <el-table-column
              prop="scope"
              show-overflow-tooltip
              label="正常范围"
              width="100"
            ></el-table-column>
          </el-table>
        </div>
      </div>
    </ModuleCard>
  </div>
</template>
<script>
export default {
  data() {
    return {
      deviceTableLoading: false,
      tableData: [
        {
          imsName: "设备",
          date: "2024/10/25 12:14",
          val: "设备件温度(34度)",
          scope: "200~423",
        },
        {
          imsName: "设备",
          date: "2024/10/25 12:14",
          val: "氦压机入水温度(23度)",
          scope: "200~423",
        },
        {
          imsName: "设备",
          date: "2024/10/25 12:14",
          val: "氦压机入水温度(23度)",
          scope: "200~423",
        },
        {
          imsName: "设备",
          date: "2024/10/25 12:14",
          val: "氦压机入水温度(23度)",
          scope: "200~423",
        },
        {
          imsName: "设备",
          date: "2024/10/25 12:14",
          val: "氦压机入水温度(23度)",
          scope: "200~423",
        },
        {
          imsName: "设备",
          date: "2024/10/25 12:14",
          val: "氦压机入水温度(23度)",
          scope: "200~423",
        },
        {
          imsName: "设备",
          date: "2024/10/25 12:14",
          val: "氦压机入水温度(23度)",
          scope: "200~423",
        },
      ],
    };
  },
  mounted() {},
  methods: {
    tableLoadMore() {
      if (
        this.pagination.total >
        this.pagination.pageNo * this.pagination.pageSize
      ) {
        this.pagination.pageNo++;
        if (this.sortType == "1") {
          //   this.getAssetsTypeList({
          //     entityTypeId: this.sendCheckData.entityTypeId,
          //   });
        } else {
          //   this.getListGroupData({ spaceId: this.sendCheckData.spaceId });
        }
      }
    },
  },
};
</script>
<style lang="scss" scoped>
%flex-styles {
  display: flex;
  align-items: center;
}
.early-warning-continer {
  height: 100%;
}
.chart-icon {
  cursor: pointer;
  @extend %flex-styles;
  .order-more {
    width: 24px;
    height: 24px;
    margin-right: 0 !important;
  }
}
</style>