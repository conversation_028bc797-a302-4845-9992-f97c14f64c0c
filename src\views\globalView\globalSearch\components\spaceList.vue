<template>
  <div style="height: 100%">
    <div class="search-box">
      <div style="margin-right: 10px" class="deviceInput">
        <!-- <span>设备名称：</span> -->
        <el-input
          v-model="params.spaceName"
          placeholder="请输入空间名称"
          clearable
          @clear="reset"
        ></el-input>
      </div>
      <div class="search-btn">
        <el-button @click="reset">重置</el-button>
        <el-button @click="handleSearch">查询</el-button>
      </div>
    </div>
    <div class="tableContent">
      <el-table
        v-loading="tableLoading"
        :data="tableData"
        height="100%"
        :cell-style="$tools.setCell(3)"
        :header-cell-style="$tools.setHeaderCell(3)"
        style="width: 100%"
        element-loading-background="rgba(0, 0, 0, 0.2)"
        @row-click="tableRowClick"
      >
        <el-table-column
          prop="localSpaceName"
          label="空间名称"
          show-overflow-tooltip
        >
        </el-table-column>
        <el-table-column
          prop="buildName"
          label="楼栋"
          show-overflow-tooltip
        >
        </el-table-column>
        <el-table-column
          prop="floorName"
          label="楼层"
          show-overflow-tooltip
        >
        </el-table-column>
        <el-table-column
          prop="dmName"
          label="部门"
          show-overflow-tooltip
        >
        </el-table-column>
        <el-table-column
          prop="functionDictName"
          label="用途"
          show-overflow-tooltip
        >
        </el-table-column>
        <el-table-column prop="area" label="建筑面积(m²)" show-overflow-tooltip>
        </el-table-column>
        <el-table-column prop="useArea" label="使用面积(m²)" show-overflow-tooltip>
        </el-table-column>
        <el-table-column prop="publicArea" label="公区面积(m²)" show-overflow-tooltip>
        </el-table-column>

      </el-table>
    </div>
    <el-pagination
      :current-page="currentPage"
      :page-sizes="[15, 20, 30, 40]"
      :page-size="pageSize"
      layout="total, sizes, prev, pager, next, jumper"
      :total="total"
      class="pagination"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
    ></el-pagination>

  </div>
</template>
<script>
import { getSpaceInfoPageByModelCode } from '@/utils/spaceManage'

export default {
  components: {
  },
  props: {
    globalParams: {
      type: Object,
      default: () => {
        return {
          areaCode: '',
          searchContent: '',
          list: [],
          total: 0
        }
      }
    }
  },
  data() {
    return {

      tableData: [],
      tableLoading: false,
      currentPage: 1,
      pageSize: 15,
      total: 0,
      params: {
        spaceName: ''
      }

    }
  },
  mounted() {
    this.total = this.globalParams.total
    this.tableData = this.globalParams.list
    this.params.spaceName = this.globalParams.searchContent
  },
  methods: {
    getList() {
      let params = {
        size: this.pageSize,
        current: this.currentPage,
        spaceName: this.params.spaceName
      }
      this.tableLoading = true
      getSpaceInfoPageByModelCode(params).then((res) => {
        if (res.data.code == '200') {
          this.tableData = res.data.data.records
          this.total = res.data.data.total

        }
      }).finally(() => {
        this.tableLoading = false
      })
    },
    reset () {
      this.params.spaceName = ''
      this.currentPage = 1
      this.getList()
    },
    handleSearch () {
      this.currentPage = 1
      this.getList()
    },

    handleSizeChange (val) {
      this.pageSize = val
      this.currentPage = 1
      this.getList()
    },
    handleCurrentChange (val) {
      this.currentPage = val
      this.getList()
    },
    // 列表单击，如果是设备，需要定位设备模型位置,
    tableRowClick (row) {
      try {
        window.chrome.webview.hostObjects.sync.bridge.OpenRoomAndChangeColor(row.modelCode)
        this.closeDialog()
      } catch (error) {}
    },
    closeDialog () {
      this.$emit('close')
    }
  }
}
</script>
<style lang="scss" scoped>
.tableContent {
  height: calc(100% - 130px);
}
:deep(.search-box) {
  display: flex;
  flex-wrap: wrap;
  margin-bottom: 12px;

  .deviceInput {
    width: 250px;
    .el-input {
      width: 100%;
      height: 35px;
    }
    .el-cascader {
      line-height: 35px;
    }
  }
  .el-button {
    background-image: url("@/assets/images/btn.png");
    background-repeat: no-repeat;
    background-size: 100% 100%;
    height: 35px;
  }
  .search-btn {
    margin-left: auto;
  }

  .el-input .el-input__inner {
    border-color: rgba(133, 145, 206, 0.5);
    border-radius: 4px;
    color: rgba(255, 255, 255, 0.8);
  }
}
.status-box {
  display: flex;
  align-items: center;
  .table-icon {
  width: 16px;
  margin-right: 3px;
}
}
</style>
