<template>
  <div class="main">
    <el-dialog :modal="false" :visible.sync="dialogShow" custom-class="mainDialog main" append-to-body :close-on-click-modal="false" :before-close="closeDialog" class="personDialog">
      <template slot="title">
        <span class="dialog-title">工单列表</span>
      </template>
      <div class="dialog-content">
        <el-table
          @row-dblclick="selectConfigRowData"
          :data="workOrderTableData"
          height="calc(100% - 40px)"
          :cell-style="$tools.setCell(3)"
          :header-cell-style="$tools.setHeaderCell(3)"
          style="width: 100%"
          v-loading="tableLoading"
          element-loading-background="rgba(0, 0, 0, 0.2)"
        >
          <el-table-column prop="createDate" show-overflow-tooltip label="开单时间"></el-table-column>
          <el-table-column prop="sourcesDeptName" show-overflow-tooltip label="所属科室">
            <template slot-scope="scope">
              {{ scope.row.sourcesDeptName === 'undefined' ? '' : scope.row.sourcesDeptName }}
            </template>
          </el-table-column>
          <el-table-column prop="localtionName" show-overflow-tooltip label="服务地点"></el-table-column>
          <el-table-column prop="itemServiceName" show-overflow-tooltip label="服务事项"></el-table-column>
          <el-table-column prop="designatePersonName" show-overflow-tooltip label="服务人员"></el-table-column>
          <el-table-column prop="designatePersonPhone" show-overflow-tooltip label="联系方式"></el-table-column>
          <el-table-column prop="flowType" show-overflow-tooltip label="工单状态"></el-table-column>
          <el-table-column prop="entryordersFeedbackFlag" show-overflow-tooltip label="是否回访">
            <template slot-scope="scope">
              {{ scope.row.entryordersFeedbackFlag === '1' ? '已回访' : '未回访' }}
            </template>
          </el-table-column>
          <el-table-column show-overflow-tooltip label="操作">
            <template slot-scope="scope">
              <div class="operationBtn">
                <span @click="selectConfigRowData(scope.row)">详情</span>
              </div>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </el-dialog>
    <template v-if="workOrderDetailCenterShow">
      <el-dialog v-dialogDrag :modal="false" :visible.sync="workOrderDetailCenterShow" custom-class="detailDialog main" :close-on-click-modal="false" :before-close="workOrderDetailCloseDialog">
        <template slot="title">
          <span class="dialog-title">综合维修（{{ detailObj.flowType }}）</span>
        </template>
        <workOrderDetailList :rowData="detailObj" :autoClose="true" @autoCloseEvent="workOrderDetailCloseDialog" />
      </el-dialog>
    </template>
  </div>
</template>
<script>
import { getAllFieldWorkOrderList } from '@/utils/peaceRightScreenApi'
import workOrderDetailList from '@views/normalMode/rightScreen/components/workOrderDetailList.vue'
export default {
  name: 'workOrderCenterList',
  components: {
    workOrderDetailList
  },
  props: {
    dialogShow: {
      type: Boolean,
      default: false
    },
    ssmCodes: {
      type: String,
      default: ''
    },
    location: {
      type: String,
      default: ''
    },
    ssmType: {
      type: String,
      default: ''
    },
    dialogData: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  data() {
    return {
      workOrderTableData: [],
      workOrderDetailCenterShow: false,
      detailObj: {},
      tableLoading: false
    }
  },
  watch: {},
  created() {},
  mounted() {
    // this.getWorkOrderTableData()
  },
  methods: {
    // 获取列表
    getWorkOrderTableData() {
      this.tableLoading = true
      const spatialId = this.ssmCodes.split(',').at(-1) || ''
      getAllFieldWorkOrderList({ spatialId: spatialId, ...this.dialogData, allType: true }).then((res) => {
        const data = res.data
        this.tableLoading = false
        if (data.code === '200') {
          this.workOrderTableData = data.data.list
        } else {
          this.$message({
            message: data.data.msg,
            type: 'warning'
          })
        }
      })
    },
    selectConfigRowData(val) {
      // val.id = '825ebf9d89814a54855d0e6964c5d66d'
      this.detailObj = val
      this.workOrderDetailCenterShow = true
      window.chrome.webview.hostObjects.sync.bridge.ShowDialog(true)
    },
    workOrderDetailCloseDialog() {
      this.workOrderDetailCenterShow = false
      this.getWorkOrderTableData()
      window.chrome.webview.hostObjects.sync.bridge.ShowDialog(false)
    },
    // 取消按钮
    closeDialog() {
      this.$emit('configCloseDialog')
    }
  }
}
</script>

<style lang="scss" type="text/css" scoped>
.personDialog {
  pointer-events: none;
  ::v-deep .el-image__error {
    background: center;
  }
}
::v-deep .mainDialog {
  width: 98%;
  height: 40vh;
  margin-top: 54vh !important;
  // background: #031553;
  // border: 1px solid #5996f9;
  background: url('@/assets/images/qhdsys/bottom-table-bg.png') no-repeat;
  background-size: 100% 100%;
  padding: 0 35px;
  pointer-events: auto;
  .el-dialog__headerbtn {
    transform: translateX(-36px);
    width: 25px;
    height: 25px;
    background-image: url('@/assets/images/close.png');
    background-repeat: no-repeat;
    background-size: 100% 100%;
    .el-dialog__close::before {
      display: none;
    }
  }
  .dialog-title {
    display: inline-block;
    width: 100%;
    text-align: center;
    transform: translateY(-6px);
    color: #cbdeed;
    &::before {
      display: none;
    }
  }
  .el-dialog__body {
    padding: 0;
    height: calc(100% - 50px);
    max-height: fit-content;
    .dialog-content {
      width: 100%;
      height: 100%;
      .el-table {
        border: none !important;
        .redBg {
          background: #341f51;
        }
      }
    }
  }
}
::v-deep .detailDialog {
  width: 60%;
  height: 80vh;
  margin-top: 7vh !important;
  background: url('@/assets/images/table-bg.png') no-repeat;
  background-size: 100% 100%;
  pointer-events: auto;
  .el-dialog__body {
    padding: 10px 50px;
    height: calc(100% - 60px);
    max-height: 80vh;
  }
}
</style>
