<template>
  <div class='component EmergencyBaseInfo'>
    <div class="EmergencyBaseInfo__description" :style="{height: isView ? '50%' : '100%'}">
      <div class="content-head">
        <div class="content-head__title">报警信息</div>
        <div class="content-head__extend">
          <div v-if="baseInfo.classic == 1" class="EmergencyBaseInfo__classic">
            <i class="el-icon-star-on"></i>
            <span>经典案例</span>
          </div>
          <div v-if="alarmAffirmTag.label" class="EmergencyBaseInfo__affirm"
               :class="`EmergencyBaseInfo__affirm--${alarmAffirmTag.class}`">
            {{ alarmAffirmTag.label }}
          </div>
        </div>
      </div>
      <div class="EmergencyBaseInfo__description__content">
        <ul class="description__content">
          <li style="margin-top: 0;">
            <div>报警类型</div>
            <div>{{ baseInfo.alarmType | defaultVal }}</div>
          </li>
          <li style="margin-top: 0;">
            <div>报警等级</div>
            <div :style="{ color: alarmLevel.color }">{{ alarmLevel.label }}</div>
          </li>
          <li>
            <div>报警对象</div>
            <div>{{ baseInfo.alarmObjectName | defaultVal }}</div>
          </li>
          <li>
            <div>报警位置</div>
            <div :title="alarmSpaceName">{{ alarmSpaceName }}</div>
          </li>
          <li>
            <div>物联设备</div>
            <div>{{ baseInfo.iotDeviceName | defaultVal }} </div>
          </li>
          <li>
            <div>报警时间</div>
            <div>{{ baseInfo.alarmStartTime | defaultVal }}</div>
          </li>
          <li class="full-row">
            <div>报警描述</div>
            <div :title="baseInfo.alarmDetails | defaultVal">
              {{ baseInfo.alarmDetails | defaultVal }}</div>
          </li>
          <li>
            <div>报警系统</div>
            <div>{{ baseInfo.projectName | defaultVal }}</div>
          </li>
          <li>
            <div>报警数值</div>
            <div :title="watchValue">{{ watchValue }}</div>
          </li>
          <li>
            <div>报警ID</div>
            <div>{{ baseInfo.alarmId }}</div>
          </li>
          <li v-if="isView">
            <div>处理状态</div>
            <div :style="{ color: alarmStatus.color }">{{ alarmStatus.label }}</div>
          </li>
          <li class="full-row">
            <div>影像信息</div>
            <div class="description__imgs">
              <span v-if="!fileList.length">-</span>
              <!-- 影像信息使用海康地址，不进入minio 所以不进行前缀替换 -->
              <FileListCard v-else :isUrlTranslation="false" :file-list="fileList" />
            </div>
          </li>
        </ul>
      </div>
    </div>
    <div v-if="isView" class="EmergencyBaseInfo__record">
      <div class="content-head">
        <div class="content-head__title">警情处理记录</div>
        <div class="content-head__extend"></div>
      </div>
      <div class="EmergencyBaseInfo__record__content">
        <div v-for="item of records" :key="item.id" class="EmergencyBaseInfo__record__item">
          <div class="EmergencyBaseInfo__record__item__left">
            <div class="EmergencyBaseInfo__record__item__status">
              <i :class="item.finish ? 'el-icon-circle-check' : 'el-icon-time'"></i>
            </div>
          </div>
          <div class="EmergencyBaseInfo__record__item__right">
            <div class="EmergencyBaseInfo__record__item__title">
              <span>{{ item.createdTime }}</span>
              <span class="EmergencyBaseInfo__record__item__tag">{{ item.operationTypeName }}</span>
            </div>
            <ul class="description__content">
              <li>
                <div>操作人</div>
                <div>{{ item.operationPersonName }}</div>
              </li>
              <li>
                <div>操作端</div>
                <div> {{ item.operationSource | platformFormat }} </div>
              </li>
              <li class="full-row">
                <div>说明</div>
                <div :title="item.remark"> {{ item.remark }} </div>
              </li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { alarmLevelConfig, alarmStatusConfig, workflowTypeConfig, platformConfig } from '../emergency-constant'
export default {
  name: 'EmergencyBaseInfo',
  components: {
    FileListCard: () => import('./FileListCard')
  },
  filters: {
    defaultVal: function (val) {
      return val || '-'
    },
    flowType: function (val) {
      const item = workflowTypeConfig.find(x => x.status == val)
      return item?.label ?? '-'
    },
    platformFormat: function (val) {
      const item = platformConfig.find(x => x.value == val)
      return item?.label ?? '-'
    }
  },
  props: {
    isView: {
      type: Boolean,
      default: true
    }
  },
  computed: {
    alarmLevel: function () {
      let result = {
        color: 'inherit',
        label: '-'
      }
      const item = alarmLevelConfig.find(x => x.value == this.baseInfo.alarmLevel)
      if (item) {
        result = item
      }
      return result
    },
    alarmStatus: function () {
      let result = {
        color: 'inherit',
        label: '-'
      }
      const item = alarmStatusConfig.find(x => x.value == this.baseInfo.alarmStatus)
      if (item) {
        result = item
      }
      return result
    },
    alarmSpaceName: function () {
      let result = '-'
      if (this.baseInfo.alarmSpaceName) {
        result = this.baseInfo.alarmSpaceName.replace(/^\S+?>/, '')
      }
      return result
    },
    // 详情信息
    detail: function () {
      return this.$parent.detail || {}
    },
    /** 基本信息 */
    baseInfo: function () {
      return this.detail.record || {}
    },
    /** 处理记录 */
    records: function () {
      return this.detail.detail || []
    },
    /** 文件列表 */
    fileList: function () {
      const list = this.baseInfo.imageFileList || []
      return list.map(item => {
        return {
          url: item.fileUrl,
          name: item.cameraIndexCode,
          isImage: true // 海康过来的图片不检验，默认为图片（图片地址加密，无法正常判断是否图片类型）
        }
      })
    },
    alarmAffirmTag: function () {
      let result = {
        class: '',
        label: ''
      }
      switch (this.baseInfo.alarmAffirm) {
        case 1:
        case 3:
          result.class = '1'
          result.label = '确警'
          break
        case 2:
          result.class = '2'
          result.label = '误报'
          break
        default:
          if (this.baseInfo.status == 0) {
            result.class = '0'
            result.label = '未处理'
          }
          break
      }
      return result
    },
    watchValue: function () {
      const alarmValue = this.baseInfo.alarmValue || '-'
      return alarmValue
      // const ruleValue = this.baseInfo.alarmRule || '-';
      // if (alarmValue === ruleValue && alarmValue === '-') {
      //   return alarmValue;
      // } else {
      //   return alarmValue + '(' + ruleValue + ')';
      // }
    }
  }
}

</script>

<style lang='scss' scoped>
.component.EmergencyBaseInfo {
  display: flex;
  flex-flow: column nowrap;
  height: 100%;
  overflow: hidden;

  .content-head {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px;
    height: 52px;

    &__extend {
      display: flex;

      .EmergencyBaseInfo__affirm {
        --bgColor: #FF2D55;
        width: 74px;
        height: 24px;
        line-height: 18px;
        padding: 3px 10px;
        text-align: center;
        position: relative;
        background-color: var(--bgColor);

        &--1 {
          margin-left: 18px;
          --bgColor: #61E29D;
          color: #06102C;
        }

        &--2,
        &--0 {
          margin-left: 24px;

          &::before {
            content: "";
            position: absolute;
            left: -8px;
            top: 0;
            display: block;
            width: 8px;
            border-top: 24px solid var(--bgColor);
            /* 数值越大角度越小 */
            border-left: 14px solid transparent;
            border-top-left-radius: 2px;
          }

          &::after {
            content: "";
            position: absolute;
            left: -8px;
            bottom: 0;
            display: block;
            width: 8px;
            border-bottom: 24px solid var(--bgColor);
            border-left: 12px solid transparent;
            border-bottom-left-radius: 2px;
          }
        }

      }

      .EmergencyBaseInfo__classic {
        color: #FFCA64;
        line-height: 24px;

        >i {
          font-size: 16px;
          margin-right: 3px;
        }
      }
    }
  }

  .description {
    &__content {
      display: flex;
      flex-flow: row wrap;

      >li {
        flex-basis: 50%;
        display: flex;
        flex-flow: row nowrap;
        margin-top: 24px;

        &.full-row {
          flex-basis: 100%;
        }

        >div:first-of-type {
          color: #B0E3FA;

          &::after {
            content: '：';
          }

        }

        >div:last-child {
          width: 0;
          flex: 1;
          overflow: hidden;
          white-space: nowrap;
          text-overflow: ellipsis;
        }
      }
    }

    &__imgs {
      display: flex;
      flex-flow: row wrap;
    }

    &__img {
      height: 100px;
      width: 100px;
      overflow: hidden;
      margin: 0 10px 10px 0;

      >img {
        height: 100%;
        width: 100%;
        object-fit: cover;
        object-position: center;
      }
    }
  }

  .EmergencyBaseInfo {
    display: flex;
    flex-flow: column nowrap;
    height: 100%;
    overflow: hidden;

    &__description,
    &__record {
      // flex-basis: 50%;
      background: rgba(133, 145, 206, 0.15);
      overflow: hidden;
      display: flex;
      flex-flow: column nowrap;
      padding-bottom: 16px;

      &__content {
        padding: 0 16px;
        // overflow: auto;
      }
    }

    &__record {
      margin-top: 16px;

      &__item {
        display: flex;
        flex-flow: row nowrap;

        &__left {
          width: 32px;
          position: relative;
        }

        &:not(:last-child) {
          .EmergencyBaseInfo__record__item__left {
            &::after {
              content: '';
              display: block;
              height: calc(100% - 32px);
              width: 1px;
              background-color: rgba(133, 145, 206, 0.5);
              margin-left: 6px;
            }
          }
        }

        &__right {
          flex: 1;
          padding-bottom: 24px;
        }

        &__title {
          background: rgba(133, 145, 206, 0.15);
          padding: 5px 12px;
          border-radius: 4px 4px 4px 4px;

        }

        &__tag {
          background: rgba(255, 202, 100, 0.2);
          border-radius: 4px 4px 4px 4px;
          color: #FFCA64;
          padding: 3px 8px;
          display: inline-block;
          margin-left: 16px;
        }

        &__status {
          height: 30px;
          line-height: 30px;
          color: #8BDDF5;
        }

        .description__content {
          padding-left: 12px;

          >li {
            margin-top: 16px;
          }
        }
      }
    }
  }
}
</style>
