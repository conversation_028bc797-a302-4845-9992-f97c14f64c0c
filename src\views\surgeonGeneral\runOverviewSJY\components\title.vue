<template>
  <div class="title-content" :style="{ backgroundImage: `url(${bg})` }">
    <div class="title-font">{{ title }}</div>
    <div class="title-right">
      <slot name="right"></slot>
      <div v-if="list.length" class="title-tab">
        <div
          v-for="(item, index) in list"
          :key="index"
          class="title-tab-item"
          :class="{ active: index == tab }"
          @click="active(index)"
        >
          {{ item.name }}
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import leftBg from '@/assets/images/qhdsys/new-title-bg-left.png'
import rightBg from '@/assets/images/qhdsys/new-title-bg-right.png'
export default {
  props: {
    title: {
      type: String,
      default: ''
    },
    list: {
      type: Array,
      default: () => []
    },
    position: {
      type: String,
      default: 'left'

    }
  },
  data() {
    return {
      tab: 0,
      leftBg,
      rightBg
    }
  },
  computed: {
    bg() {
      return this.position === 'left' ? this.leftBg : this.rightBg
    }
  },

  methods: {
    active(value) {
      this.tab = value
      this.$emit('active', value)
    }
  }
}
</script>
<style lang="scss" scoped>
.title-content {
  width: 100%;
  height: 36px;
  background-repeat: no-repeat;
  background-size: 100% 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;
  color: #fff;
  padding: 0 20px;
  font-size: 16px;
  .title-right{
    display: flex;
    .title-tab {
      padding: 0 16px;
      display: flex;
      height: 100%;
      .title-tab-item {
        position: relative; // 关键：为伪元素提供定位上下文
        height: 100%;
        display: flex;
        align-items: center;
        color: #fff;
        padding: 8px;
        cursor: pointer;
        &.active {
          color: #ffdc83;
          border-bottom: 2px solid #ffdc83;
          &::after {
            content: "";
            position: absolute;
            bottom: 0px; // 略低于 border，避免重叠
            left: 50%;
            transform: translateX(-50%);
            width: 0;
            height: 0;
            border-left: 4px solid transparent;
            border-right: 4px solid transparent;
            border-bottom: 4px solid #ffdc83; // 向上的三角
          }
        }
      }
    }
  }
}
</style>
