/*
 * @Description:
 */
// 防火分区相关接口

import http from './http'
const iemcApi = __PATH.VUE_APP_IEMC_API
// const moveBackuopsApi = __PATH.VUE_APP_MOVE_BACKUPS_API
// const imasApi = __PATH.VUE_APP_IMAS_API
// 获取指定设备的维修概况
export function fireproolList (params) {
  return http.postRequest(`${iemcApi}/FireAreaClient/queryFireAreaListBySpaceId`, params, {}, 'ihcrs')
}
export function deviceCount (params) {
  return http.postRequest(`${iemcApi}/FireAreaClient/countDeviceByFireArea`, params, {}, 'ihcrs')
}
export function deviceList (params) {
  return http.postRequest(`${iemcApi}/FireAreaClient/queryDeviceByFireArea`, params, {}, 'ihcrs')
}
export function entityType (params) {
  return http.postRequest(`${iemcApi}/FireAreaClient/queryEntityTypeInfo`, params, {}, 'ihcrs')
}
