<template>
  <div class="comprehensiveOverview">
    <div class="overview-container">
      <div class="overview-left-box">
        <simpleCard title="事件状态类型" class="simpleCard">
          <div slot="title-right" class="simpleCard-title-box">
            <p class="view-select">本周 <i class="el-icon-arrow-down"></i></p>
            <p class="view-more">详情 <i class="el-icon-arrow-right"></i></p>
          </div>
          <div
            slot="content"
            class="card-content"
            style="height: 100%; padding: 10px; box-sizing: border-box"
          >
            <div class="card-content-title-box">
              <div class="card-content-title">任务状态统计</div>
              <div class="card-content-item">
                <p v-for="(item, index) in itemData" :key="index">
                  <i :style="{ background: item.color }"></i>{{ item.name }}
                </p>
              </div>
            </div>
            <div class="card-content-box">
              <div class="card-content-head-box">
                <div class="head-box">
                  <div class="head-box-title">一级巡检</div>
                  <div class="head-box-pie">
                    <div
                      id="pie_inspection"
                      style="width: 100%; height: 100%"
                    ></div>
                  </div>
                </div>
                <div class="head-box">
                  <div class="head-box-title">二级保养</div>
                  <div class="head-box-pie">
                    <div
                      id="pie_maintain"
                      style="width: 100%; height: 100%"
                    ></div>
                  </div>
                </div>
                <div class="head-box">
                  <div class="head-box-title">三级维护</div>
                  <div class="head-box-pie">
                    <div
                      id="pie_maintenance"
                      style="width: 100%; height: 100%"
                    ></div>
                  </div>
                </div>
              </div>
              <div class="card-content-body-box">
                <div class="body-box-title-box">
                  <div class="body-box-title">保养任务量排序</div>
                  <div class="body-box-title-right">
                    <div
                      :class="{ active: maintainItemIdx == index }"
                      v-for="(item, index) in maintainItem"
                      :key="index"
                      @click="maintainItemIdx = index"
                    >
                      {{ item }}
                    </div>
                  </div>
                </div>
                <div class="echart-body-box">
                  <div
                    id="bar_maintainTaskNum"
                    style="width: 100%; height: 100%"
                  ></div>
                </div>
              </div>
            </div>
          </div>
        </simpleCard>
      </div>
      <div class="overview-right-box">
        <div class="right-header-box">
          <simpleCard title="维修统计" class="simpleCard">
            <div slot="title-right" class="simpleCard-title-box">
              <p class="view-select">本周 <i class="el-icon-arrow-down"></i></p>
              <p class="view-more">详情 <i class="el-icon-arrow-right"></i></p>
            </div>
            <div
              slot="content"
              class="card-content"
              style="
                height: 100%;
                padding: 10px;
                box-sizing: border-box;
                display: flex;
                flex-wrap: wrap;
              "
            >
              <div class="right-card-box">
                <div class="card-content-body-box">
                  <div class="body-box-title-box">
                    <div class="body-box-title">工单状态统计</div>
                  </div>
                  <div class="echart-body-box workOrder-box">
                    <div class="echart-box">
                      <div
                        id="pie_workOrder"
                        style="width: 100%; height: 100%"
                      ></div>
                    </div>
                    <div class="echart-item-box">
                      <div
                        class="echart-item"
                        v-for="(item, index) in workOrderItem"
                        :key="index"
                      >
                        <div class="echart-item-title">{{ item.name }}</div>
                        <div class="echart-item-body">
                          <span>{{ item.value }}</span>
                          <span>{{ item.unit }}</span>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              <div class="right-card-box">
                <div class="card-content-body-box">
                  <div class="body-box-title-box">
                    <div class="body-box-title">设备类型维修排行</div>
                  </div>
                  <div class="echart-body-box">
                    <div
                      id="bar_assetsType"
                      style="width: 100%; height: 100%"
                    ></div>
                  </div>
                </div>
              </div>
              <div class="right-card-box">
                <div class="card-content-body-box">
                  <div class="body-box-title-box">
                    <div class="body-box-title">维修工单量排序</div>
                    <div class="body-box-title-right">
                      <div
                        :class="{ active: repairWorkItemIdx == index }"
                        v-for="(item, index) in repairWorkItem"
                        :key="index"
                        @click="repairWorkItemIdx = index"
                      >
                        {{ item }}
                      </div>
                    </div>
                  </div>
                  <div class="echart-body-box">
                    <div
                      id="bar_repairWork"
                      style="width: 100%; height: 100%"
                    ></div>
                  </div>
                </div>
              </div>
              <div class="right-card-box">
                <div class="card-content-body-box">
                  <div class="body-box-title-box">
                    <div class="body-box-title">工单数量及维修成本趋势</div>
                  </div>
                  <div class="echart-body-box">
                    <div
                      id="line_workNumOrCost"
                      style="width: 100%; height: 100%"
                    ></div>
                  </div>
                </div>
              </div>
            </div>
          </simpleCard>
        </div>
        <div class="right-body-box">
          <simpleCard title="质控统计" class="simpleCard">
            <div slot="title-right" class="simpleCard-title-box">
              <p class="view-select">本周 <i class="el-icon-arrow-down"></i></p>
            </div>
            <div
              slot="content"
              class="card-content"
              style="height: 100%; padding: 10px; box-sizing: border-box"
            >
              <div class="event-module-box">
                <div class="event-pie-box">
                  <div class="event-pie-title">计量任务状态统计</div>
                  <div class="event-pie-echart">
                    <div
                      id="pie_jlStatus"
                      style="width: 100%; height: 100%"
                    ></div>
                  </div>
                </div>
                <div class="event-pie-box">
                  <div class="event-pie-title">性能检测任务状态</div>
                  <div class="event-pie-echart">
                    <div
                      id="pie_jcStatus"
                      style="width: 100%; height: 100%"
                    ></div>
                  </div>
                </div>
                <div class="event-pie-box">
                  <div class="event-pie-title">不良事件状态统计</div>
                  <div class="event-pie-echart">
                    <div
                      id="pie_blStatus"
                      style="width: 100%; height: 100%"
                    ></div>
                  </div>
                </div>
                <div class="event-pie-box">
                  <div class="event-pie-title">不良事件上报统计</div>
                  <div class="event-pie-echart">
                    <div
                      id="pie_blbStatus"
                      style="width: 100%; height: 100%"
                    ></div>
                  </div>
                </div>
              </div>
            </div>
          </simpleCard>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import * as echarts from "echarts";
export default {
  name: "comprehensiveOverview",
  data() {
    return {
      itemData: [
        { name: "未执行", color: "#FF2D55" },
        { name: "已执行", color: "#61E29D" },
        { name: "已停用", color: "#FFCA64" },
        { name: "已过期", color: "#8BDDF5" },
      ],
      workOrderItem: [
        { name: "维修费用", value: 34123, unit: "元" },
        { name: "工单完成率", value: 68, unit: "%" },
        { name: "平均完工时长", value: 12.9, unit: "小时" },
        { name: "平均响应时长", value: 23, unit: "小时" },
      ],
      maintainItem: ["科室", "工程师"],
      maintainItemIdx: 0,
      repairWorkItem: ["科室", "工程师"],
      repairWorkItemIdx: 0,
      pieStatusId: ["pie_inspection", "pie_maintain", "pie_maintenance"],
      barTransverseId: ["bar_assetsType", "bar_repairWork"],
      pieEventId: [
        "pie_jlStatus",
        "pie_jcStatus",
        "pie_blStatus",
        "pie_blbStatus",
      ],
    };
  },
  computed: {},
  // 监听全屏缩放 动态改变echarts图表大小
  watch: {
    "$store.state.dialogFullScreenState": {
      handler(val) {
        this.$nextTick(() => {
          setTimeout(() => {
            this.pieStatusId.forEach((item) => this.initStatusPie(item));
            this.initMaintainTaskSortBarChart();
            this.barTransverseId.forEach((item) =>
              this.initTransverseBarChart(item)
            );
            this.initWorkNumOrCostLineChart();
            this.pieEventId.forEach((item) => this.initEventPie(item));
            this.initOrderStatusPie();
          }, 200);
        });
      },
      deep: true,
    },
  },
  mounted() {
    this.pieStatusId.forEach((item) => this.initStatusPie(item));
    this.initMaintainTaskSortBarChart();
    this.barTransverseId.forEach((item) => this.initTransverseBarChart(item));
    this.initWorkNumOrCostLineChart();
    this.pieEventId.forEach((item) => this.initEventPie(item));
    this.initOrderStatusPie();
  },
  methods: {
    /** 初始化任务状态统计饼图 */
    initStatusPie(item) {
      let arrData = [
        {
          count: 150,
          name: "未执行",
          percentage: 30,
        },
        {
          count: 69,
          name: "已执行",
          percentage: 10,
        },
        {
          count: 50,
          name: "已停用",
          percentage: 15,
        },
        {
          count: 10,
          name: "已过期",
          percentage: 20,
        },
      ];
      // 基于准备好的dom，初始化echarts实例
      let getchart = echarts.init(document.getElementById(item));
      const data = [];
      var color = ["#FF2D55", "#61E29D ", "#FFAD65", "#8BDDF5"];
      const xdata = Array.from(arrData, ({ name }) => name);
      for (var i = 0; i < arrData.length; i++) {
        const randomRgbColor = this.$tools.randomRgbColor("array");
        data.push({
          name: arrData[i].name,
          value: arrData[i].count,
          percentage: arrData[i].percentage,
          itemStyle: {
            normal: {
              borderWidth: 1,
              shadowBlur: 200,
              color: color[i] ?? randomRgbColor[0],
              label: {
                normal: { show: true },
              },
            },
          },
        });
      }
      const option = {
        tooltip: {
          trigger: "item",
          axisPointer: {
            type: "shadow",
          },
          backgroundColor: '#0D2169',
          borderColor: '#727382',
          textStyle: {
            color: '#fff'
          },
        },
        backgroundColor: "",
        title: {
          text: "{name|" + "任务总数" + "}\n{val|" + 3429 + "}",
          top: "26%",
          left: "45%",
          textStyle: {
            rich: {
              name: {
                fontSize: 14,
                fontWeight: "normal",
                color: "#ffffff",
                padding: [5, 0],
              },
              val: {
                fontSize: 16,
                fontWeight: "bold",
                color: "#ffffff",
                padding: [0, 10],
              },
            },
          },
          textAlign: 'center',
          textVerticalAlign: 'center',
        },
        legend: {
          // 取消legend和echarts的联动
          selectedMode: false,
          icon: "circle",
          orient: "vartical",
          x: "left",
          left: "center",
          bottom: "3%",
          data: xdata,
          itemWidth: 8,
          itemHeight: 8,
          itemGap: 6,
          type: "scroll",
          orient: "vertical",
          textStyle: {
            color: "#B3C2DD", //  字体颜色
          },
          formatter: function (name) {
            var oa = option.series[0].data;

            for (var i = 0; i < option.series[0].data.length; i++) {
              if (name === oa[i].name) {
                return `${oa[i].percentage}%  ${oa[i].value}`;
              }
            }
          },
        },
        series: [
          {
            name: "",
            type: "pie",
            clockWise: false,
            center: ["50%", "28%"],
            radius: ["60%", "80%"],
            label: {
              normal: {
                show: false,
                position: "center",
                formatter: "{value|{c}}\n{label|{b}}",
                rich: {
                  value: {
                    padding: 5,
                    align: "center",
                    verticalAlign: "middle",
                    fontSize: 20,
                  },
                  label: {
                    align: "center",
                    verticalAlign: "middle",
                    fontSize: 14,
                  },
                },
              },
            },
            // hoverAnimation: false,
            data: data,
          },
        ],
      };
      getchart.clear();
      getchart.resize();
      getchart.setOption(option);
      // 随着屏幕大小调节图表
      window.addEventListener("resize", () => {
        getchart.resize();
      });
    },
    /** 初始化保养任务量排序柱状图 */
    initMaintainTaskSortBarChart() {
      var chartDom = document.getElementById("bar_maintainTaskNum");
      var myChart = echarts.init(chartDom);
      var option;
      option = {
        tooltip: {
          trigger: "axis",
          axisPointer: {
            type: "shadow",
          },
          backgroundColor: '#0D2169',
          borderColor: '#727382',
          textStyle: {
            color: '#fff'
          },
        },
        legend: {
          show: true,
          textStyle: {
            color: "#fff",
            fontSize: 12,
          },
          itemWidth: 8,
          itemHeight: 8,
          itemGap: 15,
        },
        grid: {
          top: "10%",
          left: "3%",
          right: "8%",
          bottom: "0%",
          containLabel: true,
        },
        xAxis: {
          type: "value",
          show: false,
          axisTick: {
            show: false,
          },
          axisLabel: {
            textStyle: {
              color: "rgba(230,247,255,0.6)", //坐标值的具体的颜色
            },
          },
          splitLine: {
            show: true,
            lineStyle: {
              color: ["rgba(233, 233, 233,0.1)"], //分割线的颜色
              type: "dashed",
            },
          },
        },
        yAxis: {
          type: "category",
          axisLine: {
            show: true,
            lineStyle: {
              color: ["rgba(233, 233, 233,0.1)"], //分割线的颜色
              type: "dashed",
            },
          },
          axisLabel: {
            textStyle: {
              color: "rgba(230,247,255,0.6)", //坐标值的具体的颜色
            },
          },
          splitLine: {
            show: true,
            lineStyle: {
              color: ["rgba(233, 233, 233,0.1)"], //分割线的颜色
              type: "dashed",
            },
          },
          data: [
            "科室名称1",
            "科室名称2",
            "科室名称3",
            "科室名称4",
            "科室名称5",
            "科室名称6",
          ],
        },
        series: [
          {
            name: "一级巡查",
            type: "bar",
            data: [10, 20, 30, 40, 50, 60],
            barWidth: 8,
            label: {
              show: true,
              position: "right",
              color: "#fff",
            },
            itemStyle: {
              color: "#8BDDF5",
            },
          },
          {
            name: "二级保养",
            type: "bar",
            data: [15, 25, 35, 45, 55, 65],
            barWidth: 8,
            label: {
              show: true,
              position: "right",
              color: "#fff",
            },
            itemStyle: {
              color: "#FFCA64",
            },
          },
          {
            name: "三级维护",
            type: "bar",
            data: [20, 40, 50, 60, 70, 80],
            barWidth: 8,
            label: {
              show: true,
              position: "right",
              color: "#fff",
            },
            itemStyle: {
              color: "#61E29D",
            },
          },
        ],
      };
      myChart.resize();
      myChart.clear();
      option && myChart.setOption(option);
      window.addEventListener("resize", () => {
        myChart.resize();
      });
    },
    /** 初始化 设备类型维修排行/维修工单量排序 柱状图 */
    initTransverseBarChart(item) {
      var chartDom = document.getElementById(item);
      var myChart = echarts.init(chartDom);
      var option;
      option = {
        legend: {
          show: false,
        },
        grid: {
          top: "10%",
          left: "3%",
          right: "8%",
          bottom: "0%",
          containLabel: true,
        },
        xAxis: {
          type: "value",
          show: item == "bar_assetsType" ? true : false,
          interval: "auto",
          axisTick: {
            show: false,
          },
          axisLabel: {
            textStyle: {
              color: "rgba(230,247,255,0.6)", //坐标值的具体的颜色
            },
          },
          splitLine: {
            show: true,
            lineStyle: {
              color: ["rgba(233, 233, 233,0.1)"], //分割线的颜色
              type: "dashed",
            },
          },
        },
        yAxis: {
          type: "category",
          axisLine: {
            show: true,
            lineStyle: {
              color: ["rgba(233, 233, 233,0.1)"], //分割线的颜色
              type: "dashed",
            },
          },
          axisLabel: {
            textStyle: {
              color: "rgba(230,247,255,0.6)", //坐标值的具体的颜色
            },
          },
          splitLine: {
            show: true,
            lineStyle: {
              color: ["rgba(233, 233, 233,0.1)"], //分割线的颜色
              type: "dashed",
            },
          },
          data: [
            "设备类型名称",
            "设备类型名称2",
            "设备类型名称3",
            "基础外科手术器械",
          ],
        },
        series: [
          {
            name: "",
            type: "bar",
            data: [10, 20, 30, 40],
            barWidth: 8,
            label: {
              show: true,
              position: "right",
              color: "#fff",
            },
            itemStyle: {
              color: "#8BDDF5",
            },
          },
        ],
      };
      myChart.resize();
      myChart.clear();
      option && myChart.setOption(option);
      window.addEventListener("resize", () => {
        myChart.resize();
      });
    },
    /** 初始化 工单数量及维修成本趋势 折线图 */
    initWorkNumOrCostLineChart() {
      var chartDom = document.getElementById("line_workNumOrCost");
      var myChart = echarts.init(chartDom);
      var option;

      option = {
        tooltip: {
          trigger: "axis",
          axisPointer: {
            type: "shadow",
          },
          backgroundColor: '#0D2169',
          borderColor: '#727382',
          textStyle: {
            color: '#fff'
          },
        },
        legend: {
          textStyle: {
            color: "#fff",
          },
        },
        grid: {
          top: "18%",
          left: "3%",
          right: "4%",
          bottom: "3%",
          containLabel: true,
        },
        xAxis: {
          type: "category",
          boundaryGap: false,
          axisLabel: {
            textStyle: {
              color: "rgba(230,247,255,0.6)", //坐标值的具体的颜色
            },
          },
          splitLine: {
            show: true,
            lineStyle: {
              color: ["rgba(233, 233, 233,0.1)"], //分割线的颜色
              type: "dashed",
            },
          },
          data: ["1月", "2月", "3月", "4月", "5月", "6月", "7月"],
        },
        yAxis: {
          type: "value",
          interval: "auto",
          axisLabel: {
            textStyle: {
              color: "rgba(230,247,255,0.6)", //坐标值的具体的颜色
            },
          },
          splitLine: {
            show: true,
            lineStyle: {
              color: ["rgba(233, 233, 233,0.1)"], //分割线的颜色
              type: "dashed",
            },
          },
        },
        series: [
          {
            name: "维修成本",
            type: "line",
            stack: "Total",
            data: [120, 132, 101, 134, 90, 230, 210],
            itemStyle: {
              color: "#61E29D",
            },
          },
          {
            name: "维修工单",
            type: "line",
            stack: "Total",
            data: [220, 182, 191, 234, 290, 330, 310],
            itemStyle: {
              color: "#8BDDF5",
            },
          },
        ],
      };

      myChart.resize();
      myChart.clear();
      option && myChart.setOption(option);
      window.addEventListener("resize", () => {
        myChart.resize();
      });
    },
    /** 初始化 质控统计 饼图 */
    initEventPie(item) {
      let arrData = [
        {
          count: 150,
          name: "未执行",
          percentage: 30,
        },
        {
          count: 69,
          name: "已执行",
          percentage: 10,
        },
        {
          count: 50,
          name: "已停用",
          percentage: 15,
        },
        {
          count: 10,
          name: "已过期",
          percentage: 20,
        },
      ];
      // 基于准备好的dom，初始化echarts实例
      let getchart = echarts.init(document.getElementById(item));
      const data = [];
      var color = ["#FF2D55", "#61E29D ", "#FFAD65", "#8BDDF5"];
      const xdata = Array.from(arrData, ({ name }) => name);
      for (var i = 0; i < arrData.length; i++) {
        const randomRgbColor = this.$tools.randomRgbColor("array");
        data.push({
          name: arrData[i].name,
          value: arrData[i].count,
          percentage: arrData[i].percentage,
          itemStyle: {
            normal: {
              borderWidth: 1,
              shadowBlur: 200,
              color: color[i] ?? randomRgbColor[0],
              label: {
                normal: { show: true },
              },
            },
          },
        });
      }
      const option = {
        tooltip: {
          trigger: "item",
          axisPointer: {
            type: "shadow",
          },
          backgroundColor: '#0D2169',
          borderColor: '#727382',
          textStyle: {
            color: '#fff'
          },
        },
        backgroundColor: "",
        title: {
          text: "{name|" + "任务总数" + "}\n{val|" + 3429 + "}",
          top: "48%",
          left: "19.5%",
          textStyle: {
            rich: {
              name: {
                fontSize: 14,
                fontWeight: "normal",
                color: "#ffffff",
                padding: [5, 0],
              },
              val: {
                fontSize: 16,
                fontWeight: "bold",
                color: "#ffffff",
                padding: [0, 10],
              },
            },
          },
          textAlign: 'center',
          textVerticalAlign: 'center',
        },
        legend: {
          // 取消legend和echarts的联动
          selectedMode: false,
          icon: "circle",
          orient: "vartical",
          right: "5%",
          bottom: "10%",
          data: xdata,
          itemWidth: 8,
          itemHeight: 8,
          itemGap: 6,
          type: "scroll",
          orient: "vertical",
          textStyle: {
            color: "#B3C2DD", //  字体颜色
          },
          formatter: function (name) {
            var oa = option.series[0].data;
            for (var i = 0; i < option.series[0].data.length; i++) {
              if (name === oa[i].name) {
                return `${oa[i].name} ${oa[i].percentage}%  ${oa[i].value}`;
              }
            }
          },
        },
        series: [
          {
            name: "",
            type: "pie",
            clockWise: false,
            center: ["22%", "50%"],
            radius: ["60%", "70%"],
            label: {
              normal: {
                show: false,
                position: "center",
                formatter: "{value|{c}}\n{label|{b}}",
                rich: {
                  value: {
                    padding: 5,
                    align: "center",
                    verticalAlign: "middle",
                    fontSize: 20,
                  },
                  label: {
                    align: "center",
                    verticalAlign: "middle",
                    fontSize: 14,
                  },
                },
              },
            },
            // hoverAnimation: false,
            data: data,
          },
        ],
      };
      getchart.clear();
      getchart.resize();
      getchart.setOption(option);
      // 随着屏幕大小调节图表
      window.addEventListener("resize", () => {
        getchart.resize();
      });
    },
    /** 初始化 工单状态 饼图 */
    initOrderStatusPie() {
      let arrData = [
        {
          count: 150,
          name: "未执行",
          percentage: 30,
        },
        {
          count: 69,
          name: "已执行",
          percentage: 10,
        },
        {
          count: 50,
          name: "已停用",
          percentage: 15,
        },
        {
          count: 10,
          name: "已过期",
          percentage: 20,
        },
      ];
      // 基于准备好的dom，初始化echarts实例
      let getchart = echarts.init(document.getElementById("pie_workOrder"));
      const data = [];
      var color = ["#FF2D55", "#61E29D ", "#FFAD65", "#8BDDF5"];
      const xdata = Array.from(arrData, ({ name }) => name);
      for (var i = 0; i < arrData.length; i++) {
        const randomRgbColor = this.$tools.randomRgbColor("array");
        data.push({
          name: arrData[i].name,
          value: arrData[i].count,
          percentage: arrData[i].percentage,
          itemStyle: {
            normal: {
              borderWidth: 1,
              shadowBlur: 200,
              color: color[i] ?? randomRgbColor[0],
              label: {
                normal: { show: true },
              },
            },
          },
        });
      }
      const option = {
        tooltip: {
          trigger: "item",
          axisPointer: {
            type: "shadow",
          },
          backgroundColor: '#0D2169',
          borderColor: '#727382',
          textStyle: {
            color: '#fff'
          },
        },
        backgroundColor: "",
        title: {
          text: "{name|" + "任务总数" + "}\n{val|" + 3429 + "}",
          top: "47%",
          left: "28.5%",
          textStyle: {
            rich: {
              name: {
                fontSize: 14,
                fontWeight: "normal",
                color: "#ffffff",
                padding: [5, 0],
              },
              val: {
                fontSize: 16,
                fontWeight: "bold",
                color: "#ffffff",
                padding: [0, 10],
              },
            },
          },
          textAlign: 'center',
          textVerticalAlign: 'center',
        },
        legend: {
          // 取消legend和echarts的联动
          selectedMode: false,
          icon: "circle",
          orient: "vartical",
          right: "10%",
          bottom: "10%",
          data: xdata,
          itemWidth: 8,
          itemHeight: 8,
          itemGap: 6,
          type: "scroll",
          orient: "vertical",
          textStyle: {
            color: "#B3C2DD", //  字体颜色
          },
          formatter: function (name) {
            var oa = option.series[0].data;
            for (var i = 0; i < option.series[0].data.length; i++) {
              if (name === oa[i].name) {
                return `${oa[i].name} ${oa[i].percentage}%  ${oa[i].value}`;
              }
            }
          },
        },
        series: [
          {
            name: "",
            type: "pie",
            clockWise: false,
            center: ["30%", "50%"],
            radius: ["75%", "90%"],
            label: {
              normal: {
                show: false,
                position: "center",
                formatter: "{value|{c}}\n{label|{b}}",
                rich: {
                  value: {
                    padding: 5,
                    align: "center",
                    verticalAlign: "middle",
                    fontSize: 20,
                  },
                  label: {
                    align: "center",
                    verticalAlign: "middle",
                    fontSize: 14,
                  },
                },
              },
            },
            // hoverAnimation: false,
            data: data,
          },
        ],
      };
      getchart.clear();
      getchart.resize();
      getchart.setOption(option);
      // 随着屏幕大小调节图表
      window.addEventListener("resize", () => {
        getchart.resize();
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.comprehensiveOverview {
  width: 100%;
  height: 100%;
  padding: 0px 40px;
  box-sizing: border-box;
  .overview-container {
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: space-between;
    .simpleCard {
      width: 100%;
      height: 100%;
      background: rgba(53, 98, 219, 0.06);
      .simpleCard-title-box {
        display: flex;
        cursor: pointer;
        .view-select {
          margin-right: 0.5rem;
        }
      }
    }
    .overview-left-box {
      width: 30%;
      height: 100%;
      margin-right: 0.625rem;
      flex-shrink: 0;
    }
    .overview-right-box {
      width: calc(70% - 0.625rem);
      height: 100%;
      background: rgba(53, 98, 219, 0.06);
      .right-header-box {
        height: calc(70% - 10px);
      }
      .right-body-box {
        height: 30%;
        margin-top: 10px;
      }
    }
  }
}
.card-content {
  .card-content-title-box {
    padding: 0px 0px 10px;
    display: flex;
    justify-content: space-between;
    .card-content-item {
      display: flex;
      align-items: center;
      p {
        display: flex;
        align-items: center;
        font-size: 12px;
        margin-left: 10px;
        i {
          width: 8px;
          height: 8px;
          margin-right: 5px;
        }
      }
    }
  }
  .card-content-box,
  .right-card-box {
    height: calc(100% - 24px);
    .card-content-head-box {
      height: 40%;
      margin-bottom: 0.5rem;
      display: flex;
      .head-box {
        width: calc((100% / 3) - 4px);
        height: 100%;
        background: rgba(133, 145, 206, 0.05);
        .head-box-title {
          width: 100%;
          padding: 10px 0px 5px;
          text-align: center;
          font-weight: bolder;
          font-size: 16px;
        }
        .head-box-pie {
          height: calc(100% - 31px);
        }
      }
      .head-box:nth-child(2) {
        margin: 0px 8px;
      }
    }
    .card-content-body-box {
      height: calc(60% - 0.5rem);
      background: rgba(133, 145, 206, 0.05);
      .body-box-title-box {
        padding: 10px;
        display: flex;
        align-items: center;
        justify-content: space-between;
        .body-box-title-right {
          display: flex;
          div {
            padding: 6px 12px;
            box-sizing: border-box;
            flex-shrink: 0;
            margin-left: 10px;
            font-size: 12px;
            color: #8bddf5;
            border: 1px solid transparent;
            cursor: pointer;
            background: linear-gradient(
                90deg,
                rgba(10, 132, 255, 0) 0%,
                rgba(10, 132, 255, 0) 100%
              ),
              rgba(255, 255, 255, 0.1);
          }
          .active {
            border: 1px solid #abf0ff;
            background: linear-gradient(
                90deg,
                rgba(10, 132, 255, 0.4) 0%,
                rgba(10, 132, 255, 0) 100%
              ),
              rgba(255, 255, 255, 0.1);
          }
        }
      }
      .echart-body-box {
        height: calc(100% - 46px);
      }
    }
  }
  .right-card-box {
    width: calc(50% - 4px);
    height: calc(50% - 4px);
    margin-right: 8px;
    margin-bottom: 8px;
    .card-content-body-box {
      height: 100%;
      .echart-body-box {
        height: calc(100% - 34px);
      }
      .workOrder-box {
        .echart-box {
          height: calc(100% - 58px);
        }
        .echart-item-box {
          padding: 10px;
          background: #8591ce0d;
          box-sizing: border-box;
          text-align: center;
          display: flex;
          align-items: center;
          justify-content: space-around;
          .echart-item-body {
            margin-top: 8px;
            span:nth-child(1) {
              color: #ffca64;
              font-size: 16px;
            }
            span:nth-child(2) {
              color: #fff;
              font-size: 14px;
              margin-left: 5px;
            }
          }
        }
      }
    }
  }
  .right-card-box:nth-child(2n) {
    margin-right: 0px;
  }
  .right-card-box:nth-child(3),
  .right-card-box:nth-child(4) {
    margin-bottom: 0px;
  }
  .right-card-box:nth-child(3) {
    .card-content-body-box {
      height: 100%;
      .echart-body-box {
        height: calc(100% - 46px);
      }
    }
  }
}
.event-module-box {
  height: 100%;
  display: flex;
  justify-content: space-between;
  .event-pie-box {
    width: calc(25% - 6px);
    height: 100%;
    background: rgba(133, 145, 206, 0.05);
    .event-pie-title {
      padding: 8px 8px 0px;
      font-size: 14px;
    }
    .event-pie-echart {
      height: calc(100% - 22px);
    }
  }
}
::v-deep .box-card .card-title {
  padding: 16px 10px 0px;
}
</style>
