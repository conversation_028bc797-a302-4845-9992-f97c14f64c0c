<template>
  <div class="energyComponent">
    <div class="energy-main">
      <div class="search-data">
        <el-dropdown trigger="click" @command="dataTypeCommand">
          <span class="el-dropdown-link"> {{ dataTypeList.find((v) => v.value == statisticalType)?.name ?? '' }} <i class="el-icon-caret-bottom"></i> </span>
          <el-dropdown-menu slot="dropdown">
            <el-dropdown-item v-for="item in dataTypeList" :key="item.value" :command="item.value" :class="{ isBjxl: statisticalType == item.value }">{{ item.name }}</el-dropdown-item>
          </el-dropdown-menu>
        </el-dropdown>
        <el-date-picker
          v-model="statisticalDate"
          class="datePickerInput"
          popper-class="date-style"
          type="daterange"
          value-format="yyyy-MM-dd"
          :disabled="statisticalType != 5"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          @change="getEnergyAllData"
          @focus="setWPFBgShow()"
          @blur="setWPFBgHide()"
        >
        </el-date-picker>
      </div>
      <div class="main-scroll">
        <ModuleCard title="能耗统计" class="module-container">
          <div slot="content" class="module-content">
            <div class="content-view">
              <div class="numberView">
                <img src="@/assets/images/qhdsys/nh_lightning.png" alt="" />
                <div>
                  <p>电力</p>
                  <p>
                    {{ kgceAnalysisData.value }}<span>{{ electricityDict.unit }}</span>
                  </p>
                </div>
              </div>
              <div class="proportion">
                <div v-for="(item, index) in tempData" :key="index" class="proportion_item">
                  <div class="proportion_data">
                    <img v-if="item.value > 0" src="@/assets/images/shang.png" alt="" />
                    <img v-else-if="item.value < 0" src="@/assets/images/xia.png" alt="" />
                    <p :class="{ up: item.value > 0, down: item.value < 0 }">{{ Math.abs(item.value) || '-' }}%</p>
                  </div>
                  <img class="proportion_img" src="@/assets/images/qhdsys/proportion.png" alt="" />
                  <span>{{ item.name }}</span>
                </div>
              </div>
            </div>
          </div>
        </ModuleCard>
        <div class="main-module" style="height: calc(35%)">
          <div class="main-module-title">
            <div class="title-left">
              <p class="left-text" :class="{ 'text-active': showType == 0 }" @click="showTypeChange(0)">用能类型</p>
              <p class="left-text" :class="{ 'text-active': showType == 1 }" style="margin-left: 16px" @click="showTypeChange(1)">能耗趋势</p>
            </div>
          </div>
          <div v-show="showType == 0" class="main-module-content main-analysis" :style="{ justifyContent: typeAnalysisList.length ? '' : 'center' }">
            <div id="analysis_chart" class="analysis-content-chart"></div>
            <div v-show="typeAnalysisList.length" class="analysis-content-list">
              <div v-for="item in typeAnalysisList" :key="item.name" class="list-item">
                <span class="list-item-color" :style="{ background: item.color }"></span>
                <p class="list-item-name">{{ filterName(item.name) }}</p>
                <p class="list-item-value">{{ filterValue(item.value) + item.unit }}</p>
              </div>
            </div>
          </div>
          <div v-show="showType == 1" class="main-module-content">
            <div id="trend_chart" class="trend_chart"></div>
          </div>
        </div>
        <div class="main-module" style="height: 32%">
          <div class="main-module-title">
            <div class="title-left">
              <p class="left-text text-active">电表能耗排行</p>
            </div>
          </div>
          <div class="main-module-content">
            <el-table
              ref="srollTable"
              v-el-table-infinite-scroll="tableLoadMore"
              v-loading="deviceTableLoading"
              class="table-center-transfer"
              :data="deviceList"
              height="calc(100% - 0px)"
              style="width: 100%"
              :cell-style="{ padding: '8px', backgroundColor: 'transparent', color: '#fff', border: 'none', padding: '3px 0px' }"
              :header-cell-style="{ background: '#8591CE26!important', color: '#8BDDF5FF', padding: '0px', fontWeight: 'bold' }"
              element-loading-background="rgba(0, 0, 0, 0.2)"
              @cell-click="tableClick"
            >
              <el-table-column prop="measurePointName" show-overflow-tooltip label="设备名称"></el-table-column>
              <el-table-column prop="energyValue" show-overflow-tooltip label="耗能(kwh)" width="110" sortable>
                <template slot-scope="scope">{{ filterValue(scope.row.energyValue) }}</template>
              </el-table-column>
            </el-table>
          </div>
        </div>
      </div>
    </div>
    <!-- 设备能耗 -->
    <template v-if="deviceEnergyShow">
      <deviceEnergy
        :show="deviceEnergyShow"
        :requestParams="requestParams"
        @closeDialog="closeDeviceEnergyDialog"
      />
    </template>
  </div>
</template>

<script>
import * as echarts from 'echarts'
import { KgceLogin, GetEnergyAndClassifyTree, GetModelEnergyDataList, GetModelFormulaPoint, GetPointHistoryList } from '@/utils/energyConsumption'
import moment from 'moment'
moment.locale('zh-cn')
export default {
  name: 'energyComponent',
  components: {
    deviceEnergy: () => import('@/views/centerScreen/businessManagement/component/deviceEnergy.vue')
  },
  props: {
    roomData: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      statisticalType: 3, // 选中日期类型
      statisticalDate: [moment().startOf('month').format('YYYY-MM-DD') + ' 00:00:00', moment().endOf('month').format('YYYY-MM-DD') + ' 23:59:59'], // 选中日期
      dataTypeList: [
        { value: 1, name: '今日' },
        { value: 2, name: '本周' },
        { value: 3, name: '本月' },
        { value: 4, name: '本年' },
        { value: 5, name: '自定义' }
      ],
      tempData: [
        {
          name: '同比',
          value: ''
        },
        {
          name: '环比',
          value: ''
        }
      ],
      kgceToken: '',
      menuList: [], // 能耗类型菜单
      energyId: '', // 当前选择能耗类型id
      kgceAnalysisData: {
        value: 0,
        momValueRatio: 0,
        yoyValueRatio: 0
      },
      electricityDict: {
        id: 'SU035',
        unit: 'kWh'
      },
      emodelId: '', // 写死获取制冷机房的数据
      showType: 0, // 0用能类型 1能耗趋势
      typeAnalysisList: [],
      deviceTableLoading: false,
      deviceList: [],
      pagination: {
        total: 0,
        pageSize: 15,
        pageNo: 1
      },
      requestParams: {}, // 设备能耗请求参数
      deviceEnergyShow: false // 设备能耗弹窗
    }
  },
  computed: {
    filterValue() {
      return (value, type) => {
        value = value && !isNaN(Number(value)) ? Number(value) : 0
        // return value ? value.toFixed(2) : 0
        if (type == 'toFixed') {
          return value.toFixed(2)
        } else {
          if (value > 1000) {
            return (value / 10000).toFixed(2) + '万'
          } else {
            return value.toFixed(2)
          }
        }
      }
    },
    filterName() {
      return (name) => {
        name = name.replace(/用水/g, '').replace(/用电/g, '')
        return name
      }
    }
  },
  watch: {
    // 监听模型层级数据变化
    roomData: {
      handler(newVal, oldVal) {
        this.getEnergyAllData()
      },
      deep: true
    }
  },
  mounted() {
    this.kgceLogin()
  },
  methods: {
    // 时间类型切换
    dataTypeCommand(val) {
      const date = {
        1: [moment().format('YYYY-MM-DD') + ' 00:00:00', moment().format('YYYY-MM-DD') + ' 23:59:59'],
        2: [moment().startOf('isoWeek').format('YYYY-MM-DD') + ' 00:00:00', moment().endOf('isoWeek').format('YYYY-MM-DD') + ' 23:59:59'],
        3: [moment().startOf('month').format('YYYY-MM-DD') + ' 00:00:00', moment().endOf('month').format('YYYY-MM-DD') + ' 23:59:59'],
        4: [moment().startOf('year').format('YYYY-MM-DD') + ' 00:00:00', moment().endOf('year').format('YYYY-MM-DD') + ' 23:59:59'],
        5: []
      }
      this.statisticalDate = date[val]
      this.statisticalType = val
      if (val != 5) {
        this.getEnergyAllData()
      }
    },
    showTypeChange(type) {
      this.showType = type
      if (type == 0) {
        this.typeAnalysis()
      } else {
        this.energyTrend()
      }
    },
    tableClick(row) {
      Object.assign(this.requestParams, {
        ...row,
        kgceToken: this.kgceToken
      })
      this.deviceEnergyShow = true
      try {
        window.chrome.webview.hostObjects.sync.bridge.ShowDialog(true)
      } catch (error) {}
    },
    closeDeviceEnergyDialog() {
      this.deviceEnergyShow = false
      try {
        window.chrome.webview.hostObjects.sync.bridge.ShowDialog(false)
      } catch (error) {}
    },
    // 获取能耗所有数据列表集合
    getEnergyAllData(val) {
      // 制冷机房对应能耗分组 深圳本地绑定，待后续产品优化关联关系后调整
      const menuContrastDict = {
        '08f2e8c491f448a8a024f9c8b41bf023': '1793562452993122306', // 2#制冷机房
        ca6bf326808146b5b37f745027f04666: '1762660246991544321' // 1#制冷机房
      }
      this.emodelId = menuContrastDict[this.roomData.menuCode]
      // 自定义时间
      if (val && val.length) {
        this.statisticalDate = [val[0] + ' 00:00:00', val[1] + ' 23:59:59']
      }
      if (!this.statisticalDate.length) {
        this.statisticalType = 1
        this.statisticalDate = [moment().format('YYYY-MM-DD') + ' 00:00:00', moment().format('YYYY-MM-DD') + ' 23:59:59']
      }
      const dataType = {
        1: 'day',
        2: 'day',
        3: 'month',
        4: 'year',
        5: 'day'
      }
      this.kgceAnalysis(dataType)
      if (this.showType == 0) {
        this.typeAnalysis()
      } else {
        this.energyTrend()
      }
      this.pagination.pageNo = 1
      this.getDeviceList()
    },
    // 能耗分析
    kgceAnalysis(dataType) {
      const energyIdCateList = this.menuList[0].children.map((item) => item.id)
      const params = {
        emodelId: this.emodelId,
        energyId: this.energyId,
        timeCategory: dataType[this.statisticalType],
        startDate: this.statisticalDate[0],
        endDate: this.statisticalDate[1],
        energyIdCate: energyIdCateList.toString()
      }
      this.getModelEnergyDataList(params)
        .then((res) => {
          const resData = res.data.data
          if (resData.length) {
            this.kgceAnalysisData = {
              value: this.getListFieldSum(resData, 'value'), // 当前值
              momValueRatio: this.getListFieldSum(resData, 'momValueRatio', resData.length > 1, 'toFixed'), // 环比比率
              yoyValueRatio: this.getListFieldSum(resData, 'yoyValueRatio', resData.length > 1, 'toFixed') // 同比比率
            }
          } else {
            this.kgceAnalysisData = {
              value: 0,
              momValueRatio: 0,
              yoyValueRatio: 0
            }
          }
          this.tempData[0].value = this.kgceAnalysisData.yoyValueRatio
          this.tempData[1].value = this.kgceAnalysisData.momValueRatio
        })
        .catch()
    },
    getListFieldSum(list, field, isRatio = false, type = '') {
      if (isRatio) return 0
      const sum = list.reduce((prev, cur) => {
        return prev + Number(cur[field]) ?? 0
      }, 0)
      return this.filterValue(sum, type)
    },
    // 类型分析
    typeAnalysis() {
      const dataType = {
        1: 'day',
        2: 'day',
        3: 'month',
        4: 'year',
        5: 'day'
      }
      const energyIdCateList = this.menuList[0].children.map((item) => item.id)
      const param = {
        aggregation: 'sum',
        valueName: 'value',
        groupBy: 'energyId',
        timeCategory: dataType[this.statisticalType],
        startDate: this.statisticalDate[0],
        endDate: this.statisticalDate[1],
        emodelId: this.emodelId,
        energyId: this.energyId,
        energyIdCate: energyIdCateList.toString()
      }
      const color = ['#E88D6B', '#FFCA64', '#5E89EE', '#0A84FF', '#61E29D']
      let newData = []
      this.getModelEnergyDataList(param)
        .then((res) => {
          newData = res.data.data.map((v, i) => {
            return {
              value: v.value,
              name: v.energyName,
              color: color[i],
              unit: v.energyUnitEn
            }
          })
          this.typeAnalysisList = newData
          const total = newData.reduce((prev, cur) => {
            return prev + Number(cur.value) ?? 0
          }, 0)
          setTimeout(() => {
            this.$nextTick(() => {
              this.electricityPieEchart(newData, total)
            })
          }, 100)
        })
        .catch()
    },
    // 能耗趋势
    energyTrend() {
      const dataType = {
        1: 'hour',
        2: 'day',
        3: 'day',
        4: 'month',
        5: 'day'
      }
      const energyIdCateList = this.menuList[0].children.map((item) => item.id)
      let params = {
        emodelId: this.emodelId, // 模型id
        timeCategory: dataType[this.statisticalType],
        // energyId: this.energyId,
        energyId: energyIdCateList.toString() + ',' + this.energyId
      }
      if (this.statisticalType === 1) {
        params = {
          ...params,
          startDate: moment().format('YYYY-MM-DD') + ' 00:00:00',
          endDate: moment().format('YYYY-MM-DD') + ' 23:59:59'
        }
      } else if (this.statisticalType === 2) {
        params = {
          ...params,
          startDate: moment().startOf('isoWeek').format('YYYY-MM-DD') + ' 00:00:00',
          endDate: moment().endOf('isoWeek').format('YYYY-MM-DD') + ' 23:59:59'
        }
      } else if (this.statisticalType === 3) {
        params = {
          ...params,
          startDate: moment().startOf('month').format('YYYY-MM-DD') + ' 00:00:00',
          endDate: moment().endOf('month').format('YYYY-MM-DD') + ' 23:59:59'
        }
      } else if (this.statisticalType === 4) {
        params = {
          ...params,
          startDate: moment().startOf('year').format('YYYY-MM-DD') + ' 00:00:00',
          endDate: moment().endOf('year').format('YYYY-MM-DD') + ' 23:59:59'
        }
      } else if (this.statisticalType === 5) {
        params = {
          ...params,
          startDate: this.statisticalDate[0],
          endDate: this.statisticalDate[1]
        }
      }
      const newObj = {}
      this.getModelEnergyDataList(params)
        .then((res) => {
          let key = ''
          res.data.data.forEach((item) => {
            key = item.energyName
            if (newObj[key]) {
              newObj[key].push(item)
            } else {
              newObj[key] = [item]
            }
          })
          setTimeout(() => {
            this.$nextTick(() => {
              this.electricityLineEchart(newObj)
            })
          }, 100)
        })
        .catch()
    },
    // 获取模型能源数据列表
    getModelEnergyDataList(params) {
      return new Promise((resolve, reject) => {
        GetModelEnergyDataList(params, this.kgceToken)
          .then((res) => {
            if (res.data.code === 200) {
              if (res.data.data.length) {
                res.data.data.forEach((item) => {
                  this.electricityDict.unit = item.energyUnitEn
                })
              }
              resolve(res)
            } else {
              reject(res)
            }
          })
          .catch((err) => {
            reject(err)
          })
      })
    },
    // 获取能源及分类的树结构
    getEnergyAndClassifyTree() {
      GetEnergyAndClassifyTree({}, this.kgceToken).then((res) => {
        if (res.data.code === 200) {
          this.menuList = res.data.data.filter((item) => item.id == this.electricityDict.id)
          this.energyId = this.menuList[0].id
          this.getEnergyAllData()
        }
      })
    },
    // 饼图
    electricityPieEchart(data, total) {
      const myChart = echarts.init(document.getElementById('analysis_chart'))
      let option
      const nameObj = {
        SU035: '总用电量',
        CM020: '总用水量',
        '4c78a70e0b7deb51b0a227fb2cd9196f': '总用热量',
        '66655f36bc0dc86adbaf2cab80d9dce2': '总蒸汽用量'
      }
      if (data.length) {
        option = {
          color: data.map((v) => v.color),
          title: {
            text: '{a|' + this.filterValue(total) + '}{c|' + this.electricityDict.unit + '}',
            subtext: nameObj[this.electricityDict.id],
            x: 'center',
            y: '40%',
            textStyle: {
              rich: {
                a: {
                  fontSize: 13,
                  color: '#FFFFFF'
                },
                c: {
                  fontSize: 12,
                  color: '#FFFFFF'
                }
              }
            },
            subtextStyle: {
              color: 'rgba(255,255,255,0.6)',
              fontSize: 12
            }
          },
          tooltip: {
            show: false
          },
          series: [
            {
              type: 'pie',
              name: 'TypeB', // 内层细圆环2
              radius: ['46%', '47%'],
              hoverAnimation: false,
              clockWise: false,
              itemStyle: {
                color: 'rgba(255,255,255,0.2)'
              },
              label: {
                show: false
              },
              data: [100]
            },
            {
              type: 'pie',
              name: 'TypeA', // 最外层细圆环
              hoverAnimation: false,
              clockWise: false,
              radius: ['63%', '64%'],
              itemStyle: {
                color: 'rgba(255,255,255,0.2)'
              },
              label: {
                show: false
              },
              data: [100]
            },
            {
              name: 'content',
              type: 'pie',
              clockWise: false,
              radius: ['50%', '60%'],
              hoverAnimation: true,
              data: data,
              label: {
                show: false
              },
              itemStyle: {
                normal: {
                  borderWidth: 2, // 间距的宽度
                  borderColor: 'rgba(40,48,65,1)' // 背景色
                }
              }
            },
            {
              // 内圆
              type: 'pie',
              radius: '42%',
              center: ['50%', '50%'],
              hoverAnimation: false,
              itemStyle: {
                color: 'rgba(133,145,206,0.15)'
              },
              label: {
                show: false
              },
              tooltip: {
                show: false
              },
              data: [100]
            }
          ]
        }
      } else {
        option = {
          title: {
            text: '暂无数据',
            x: 'center',
            y: 'center',
            textStyle: {
              fontSize: 14,
              fontWeight: 'normal',
              color: '#999'
            }
          }
        }
      }
      myChart.off('mouseover')
      myChart.off('mouseout')
      myChart.clear()
      myChart.setOption(option)
      // 随着屏幕大小调节图表
      window.addEventListener('resize', () => {
        myChart.resize()
      })
      myChart.on('mouseover', (e) => {
        if (e.seriesName == 'content') {
          option.title.text = '{a|' + this.filterValue(data[e.dataIndex].value) + '}{c|' + data[e.dataIndex].unit + '}'
          option.title.subtext = this.filterName(data[e.dataIndex].name)
          myChart.setOption(option)
        }
      })
      myChart.on('mouseout', (e) => {
        if (e.seriesName == 'content') {
          option.title.text = '{a|' + this.filterValue(total) + '}{c|' + this.electricityDict.unit + '}'
          option.title.subtext = nameObj[this.electricityDict.id]
          myChart.setOption(option)
        }
      })
    },
    // 折线图
    electricityLineEchart(data) {
      const getchart = echarts.init(document.getElementById('trend_chart'))
      let option
      if (Object.keys(data).length) {
        option = {
          dataZoom: [
            {
              type: 'inside',
              start: 0,
              end: 100
            }
          ],
          tooltip: {
            trigger: 'axis',
            axisPointer: {
              lineStyle: {
                color: '#FFE3A6'
              }
            },
            backgroundColor: '#0C2269',
            borderColor: '#5B617C',
            textStyle: {
              color: '#fff'
            }
          },
          grid: {
            top: '20%',
            left: '15%',
            right: '4$%',
            bottom: '12%'
          },
          legend: {
            x: 'center',
            top: '4',
            data: Object.keys(data),
            itemWidth: 8,
            itemHeight: 8,
            // icon: "stack",
            pageTextStyle: {
              color: '#fff'
            },
            textStyle: {
              //  fontSize: 18,//字体大小
              color: '#B3C2DD' //  字体颜色
            }
          },
          xAxis: {
            type: 'time',
            boundaryGap: false,
            axisTick: {
              show: false
            },
            axisLine: {
              lineStyle: {
                color: '#303F69'
              }
            },
            axisLabel: {
              textStyle: {
                fontSize: 10,
                color: '#8C9EB5'
              }
            }
          },
          yAxis: {
            type: 'value',
            name: '单位：' + this.electricityDict.unit,
            nameTextStyle: {
              color: '#A4AFC1',
              fontSize: 12,
              padding: [0, 0, 0, 0]
            },
            axisTick: {
              show: false
            },
            axisLabel: {
              margin: 10,
              textStyle: {
                fontSize: 10,
                color: '#8C9EB5'
              }
            },
            splitLine: {
              show: true,
              lineStyle: {
                color: ['rgba(230, 247, 255, 0.20)'],
                width: 1,
                type: 'dashed'
              }
            }
          },
          series: Object.keys(data).map((key) => {
            return {
              name: key,
              type: 'line',
              // smooth: true,
              showSymbol: false,
              symbol: 'circle',
              symbolSize: 6,
              // data: valueList,
              data: data[key].map((item) => [item.dataTime, item.value]),
              itemStyle: {
                normal: {
                  color: '#FFE3A6'
                }
              },
              lineStyle: {
                normal: {
                  width: 2
                }
              }
            }
          })
        }
      } else {
        option = {
          title: {
            text: '暂无数据',
            x: 'center',
            y: 'center',
            textStyle: {
              fontSize: 14,
              fontWeight: 'normal',
              color: '#999'
            }
          }
        }
      }
      getchart.clear()
      getchart.setOption(option)
      // 随着屏幕大小调节图表
      window.addEventListener('resize', () => {
        getchart.resize()
      })
    },
    // 获取设备列表
    getDeviceList() {
      this.deviceTableLoading = true
      GetModelFormulaPoint({ modelId: this.emodelId }, this.kgceToken).then((res) => {
        if (res.data.code == 200) {
          const list = res.data.data
          const measurePointIds = Array.from(list, ({ measurePointId }) => measurePointId)
          const params = {
            dataType: 'day',
            startDate: this.statisticalDate[0],
            endDate: this.statisticalDate[1],
            measurePointId: measurePointIds.toString(), // 测点编号
            domainKeyword: '70020000', // 正向有功总电能
            sum: false // 是否计算合计值
          }
          GetPointHistoryList(params, this.kgceToken).then((res) => {
            this.deviceTableLoading = false
            if (res.data.code == 200 && res.data.data.length) {
              res.data.data.forEach((item) => {
                const index = list.findIndex((e) => e.measurePointId == item.measurePointId)
                list[index].energyValue = (list[index].energyValue || 0) + Number(item.value)
              })
              this.deviceList = list
              this.pagination.total = this.deviceList.length
            } else {
              this.pagination.total = 0
              this.deviceList = []
            }
          })
        } else {
          this.deviceTableLoading = false
          this.pagination.total = 0
          this.deviceList = []
        }
      }).finally(() => {
        this.deviceTableLoading = false
      })
    },
    tableLoadMore() {
      if (this.pagination.total > this.pagination.pageNo * this.pagination.pageSize) {
        this.pagination.pageNo++
        this.getDeviceList()
      }
    },
    setWPFBgShow() {
      try {
        window.chrome.webview.hostObjects.sync.bridge.DateMask(true)
      } catch (error) {}
    },
    setWPFBgHide() {
      try {
        window.chrome.webview.hostObjects.sync.bridge.DateMask(false)
      } catch (error) {}
    },
    // 能耗登录
    kgceLogin() {
      KgceLogin(KECG_ACCOUNT).then((res) => {
        if (res.data.code === 200) {
          this.kgceToken = res.data.result.token
          this.getEnergyAndClassifyTree()
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.module-content {
  padding: 10px 0;
  box-sizing: border-box;
}

.energyComponent {
  width: 100%;
  height: 100%;
  .energy-main {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    .search-data {
      display: flex;
      background: rgba(133, 145, 206, 0.15);
      padding: 0px 10px;
      ::v-deep .el-dropdown {
        padding: 7px 6px;
        .el-dropdown-link {
          font-size: 14px;
          font-weight: 500;
          color: #8bddf5;
          line-height: 16px;
          position: relative;
          cursor: pointer;
        }
        .el-dropdown-link::after {
          content: '';
          position: absolute;
          width: 1px;
          height: 12px;
          background: rgba(133, 145, 206, 0.5);
          top: 50%;
          right: -6px;
          transform: translateY(-50%);
        }
      }
      ::v-deep .datePickerInput {
        flex: 1;
        padding: 8px 10px;
        height: 16px;
        box-sizing: content-box;
        background: none;
        border: none;
        .el-input__icon,
        .el-range-separator {
          line-height: 16px;
          color: #b0e3fa;
        }
        .el-range-input {
          background: none;
          color: #a4afc1;
        }
      }
    }
    .main-scroll {
      flex: 1;
      overflow: auto;
      .proportion {
        display: flex;
        margin-top: 17px;
        .proportion_item {
          flex: 1;
          display: flex;
          flex-direction: column;
          justify-content: center;
          align-items: center;
          .proportion_data {
            display: flex;
            p {
              font-size: 14px;
              font-family: HarmonyOS Sans SC-Bold, HarmonyOS Sans SC;
              font-weight: bold;
              color: #ffffff;
              margin-left: 5px;
            }
            .up {
              color: #ff2d55;
            }
            .down {
              color: #61e29d;
            }
          }
          .proportion_img {
            margin-top: 5px;
          }
          span {
            font-size: 14px;
            font-family: HarmonyOS Sans SC-Regular, HarmonyOS Sans SC;
            font-weight: 400;
            color: #a4acb9;
          }
        }
      }
      .content-view {
        padding: 0px 10px 15px 10px;
        .numberView {
          width: 100%;
          height: 100px;
          background: url('../../../assets/images/qhdsys/nh_bg.png') no-repeat center / 100%;
          display: flex;
          align-items: center;
          justify-content: center;
          div {
            margin-left: 32px;
            p:first-child {
              font-size: 16px;
              font-weight: 500;
              color: #b0e3fa;
            }
            p:last-child {
              margin-top: 8px;
              font-size: 24px;
              font-family: DIN-Bold, DIN;
              font-weight: 600;
              color: #ffca64;
              span {
                font-size: 14px;
                font-family: PingFang-SC-Medium, PingFang-SC;
                font-weight: 400;
                margin-left: 9px;
                color: rgba(164, 172, 185, 0.3);
              }
            }
          }
        }
        .trendView {
          display: flex;
          align-items: flex-end;
          justify-content: space-between;
          .trendView-item {
          }
          .trendView-title {
            font-size: 12px;
            font-weight: 500;
            padding: 30px 0px 10px 0px;
          }
          .trendView-num {
            font-size: 14px;
          }
        }
      }
      .main-module {
        .main-module-title {
          padding: 7px 0px 7px 38px;
          background: url('~@/assets/images/qhdsys/bg-bt.png') no-repeat center / 100% 100%;
          display: flex;
          justify-content: space-between;
          .title-left {
            display: flex;
            .left-text {
              cursor: pointer;
              font-size: 16px;
              font-family: HarmonyOS Sans SC-Bold;
              color: #a6afbf;
              line-height: 30px;
            }
            .text-active {
              font-weight: bold;
              color: #ffffff;
              text-shadow: 0px 0px 9px #158eff;
            }
          }
        }
        .main-module-content {
          height: calc(100% - 44px);
        }
        ::v-deep .table-center-transfer {
          border: 0 !important;
        }
      }
      .main-analysis {
        height: 100%;
        display: flex;
        .analysis-content-chart {
          height: 100%;
          width: 50%;
        }
        .analysis-content-list {
          height: 100%;
          width: 50%;
          padding: 16px 16px 16px 0;
          display: flex;
          flex-direction: column;
          box-sizing: border-box;
          justify-content: space-evenly;
          .list-item {
            position: relative;
            display: flex;
            align-items: center;
            padding: 4px 0px 4px 27px;
            background: rgba(42, 54, 68, 0.4);
            .list-item-color {
              width: 7px;
              height: 7px;
              border-radius: 50%;
              position: absolute;
              top: 50%;
              transform: translateY(-50%);
              left: 10px;
            }
            .list-item-name {
              font-size: 12px;
              font-weight: 400;
              color: #a4acb9;
              line-height: 14px;
              min-width: 40px;
            }
            .list-item-value {
              font-size: 16px;
              font-weight: 400;
              color: #ffffff;
              line-height: 19px;
            }
          }
          .list-item::before {
            content: '';
            width: 2px;
            height: 2px;
            position: absolute;
            background: #9bb8c7;
            left: 0;
            top: 0;
          }
        }
      }
      .trend_chart {
        width: 100%;
        height: 100%;
      }
    }
    .main-scroll::-webkit-scrollbar {
      display: none; /* Chrome Safari */
    }
  }
}
</style>
