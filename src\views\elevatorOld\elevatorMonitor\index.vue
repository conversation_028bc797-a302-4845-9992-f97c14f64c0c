<!--
 * @Author: hedd
 * @Date: 2023-07-04 14:35:15
 * @LastEditTime: 2024-03-20 15:50:34
 * @FilePath: \ihcrs_client_iframe\src\views\elevator\elevatorMonitor\index.vue
 * @Description:
-->
<template>
  <div class="content">
    <div class="left-content">
      <div class="left_top">
        <div class="top_count">
          <div class="vertical_count">
            <img src="@/assets/images/elevator/ic-vertical.png" alt="" />
            <div class="count_right">
              <span class="count_title">直梯数量</span>
              <span class="count_num"
              ><span>{{ elevatorList[0].connectNum }}</span> / {{ elevatorList[0].totalNum }}</span
              >
            </div>
          </div>
          <div class="escalator_count">
            <img src="@/assets/images/elevator/ic-escalator.png" alt="" />
            <div class="count_right">
              <span class="count_title">扶梯数量</span>
              <span class="count_num"
              ><span>{{ elevatorList[1].connectNum }}</span> / {{ elevatorList[1].totalNum }}</span
              >
            </div>
          </div>
          <div class="warn_count">
            <img src="@/assets/images/elevator/warn-alert.png" alt="" />
            <div class="count_right">
              <div class="count_title">报警数量</div>
              <div class="count_num">
                {{ elevatorOthersData.alarmCount }}
              </div>
            </div>
          </div>
        </div>
        <div class="top_operating operating_common">
          <div class="operating_box">
            <div class="box_icon duration-icon"></div>
            <div class="box_content">
              <span>运行时长</span>
              <span
              ><span class="big_num">{{ elevatorOthersData.runTime ?? 0 }}</span> 时</span
              >
            </div>
          </div>
          <div class="operating_box">
            <div class="box_icon distance-icon"></div>
            <div class="box_content">
              <span>运行距离</span>
              <span
              ><span class="big_num">{{ elevatorOthersData.runDistance ?? 0 }}</span> 米</span
              >
            </div>
          </div>
          <div class="operating_box">
            <div class="box_icon numbers-icon"></div>
            <div class="box_content">
              <span>运行次数</span>
              <span
              ><span class="big_num">{{ elevatorOthersData.runNum ?? 0 }}</span> 次</span
              >
            </div>
          </div>
          <div class="operating_box">
            <div class="box_icon numbers-icon"></div>
            <div class="box_content">
              <span>开门次数</span>
              <span
              ><span class="big_num">{{ elevatorOthersData.openCount ?? 0 }}</span> 次</span
              >
            </div>
          </div>
        </div>
      </div>
      <div class="left_center">
        <div>
          <div class="card_box_title card_box_short_bg">电梯品牌分布</div>
          <div v-if="!elevatorBrandShow" class="echart-null">
            <img src="@/assets/images/null.png" alt="" />
            <div>暂无数据~</div>
          </div>
          <div v-else style="width: 100%; height: calc(100% - 31px)">
            <div id="elevatorBrandPieEcharts"></div>
          </div>
        </div>
        <div>
          <div class="card_box_title card_box_short_bg">使用年限分布</div>
          <div v-if="!serviceLifeShow" class="echart-null">
            <img src="@/assets/images/null.png" alt="" />
            <div>暂无数据~</div>
          </div>
          <div v-else style="width: 100%; height: calc(100% - 31px)">
            <div id="serviceLifeBarEcharts"></div>
          </div>
        </div>
      </div>
      <div class="left_bottom">
        <div class="card_box_title card_box_long_bg">报警类型分析</div>
        <div v-if="!alarmTypeAnalysisShow" class="echart-null">
          <img src="@/assets/images/null.png" alt="" />
          <div>暂无数据~</div>
        </div>
        <div v-else style="width: 100%; height: calc(100% - 31px); position: relative">
          <!-- <div class="pie-decoration"></div> -->
          <div id="alarmTypeAnalysisPieEcharts" ref="alarmTypeAnalysisPieEcharts"></div>
        </div>
      </div>
    </div>
    <div class="right-content">
      <div class="all_bar_box">
        <div v-for="item in keyList" :key="item.dom">
          <div class="card_box_title card_box_short_bg">
            {{ item.title }}
          </div>
          <div v-if="!item.show" class="echart-null">
            <img src="@/assets/images/null.png" alt="" />
            <div>暂无数据~</div>
          </div>
          <div v-else style="width: 100%; height: calc(100% - 31px)">
            <div :id="item.dom" :ref="item.dom"></div>
          </div>
        </div>
      </div>
      <div class="elevator_health">
        <div class="card_box_title card_box_long_bg">电梯视频监控</div>
        <div class="health_level_box">
          <!-- <video-flv ref="videoflv" class="video_preview" /> -->
          <rtspCavas ref="rtspCavas" :rtspUrl="videoUrl" :videoName="videoName" :hasCavas="Boolean(videoList.length)" class="video_preview"></rtspCavas>
          <div class="contorl-btn">
            <el-button class="elevatorClass" type="primary" @click="changePollingDialog(true)">轮询设置</el-button>
            <el-button class="elevatorClass" type="primary" @click="changePollingTimer">{{pollingTimer ? '停止' : '开始'}}</el-button>
          </div>
          <!-- <div
            v-for="(item, index) in healthLevelData"
            :key="index"
            class="health_level"
            :style="{
              color: item.color
            }"
          >
            <div class="health_level_title" :class="item.levelBg"></div>
            <div class="health_level_num">{{ item.levelNum }}</div>
          </div> -->
        </div>
      </div>
    </div>
    <el-dialog
      v-if="PollingDialogVisible"
      v-dialogDrag
      custom-class="polling-dialog"
      :modal="false"
      :close-on-click-modal="false"
      :visible.sync="PollingDialogVisible"
      :before-close="changePollingDialog"
    >
      <span slot="title">
        轮询设置
      </span>
      <div class="polling-content">
        <label>轮询时间：</label>
        <el-input
          v-model="pollingTime"
          oninput="value=value.replace(/^0|[^0-9]/g,'')"
          placeholder="请输入轮询时间"
          style="width: 200px"
        ><template slot="append">秒</template></el-input>
      </div>
      <span slot="footer">
        <el-button class="elevatorClass" type="primary" plain @click="changePollingDialog">取消</el-button>
        <el-button class="elevatorClass" type="primary" @click="submitPollingForm">保存</el-button>
      </span>
    </el-dialog>
  </div>
</template>
<script>
// import $ from 'jquery'
import * as echarts from 'echarts'
import { getNumberOfElevators, getIaasStatistics, getElevatorMonitoringList, getElevatorFloorList, getReasonStatisticPie, getCameraListByProjectCode } from '@/utils/elevatorApi'
export default {
  name: 'elevatorMonitor',
  components: {
    rtspCavas: () => import('../components/rtspCavas.vue')
  },
  data() {
    return {
      PollingDialogVisible: false, // 轮播设置弹框
      pollingTime: localStorage.getItem('polingTime') || 20, // 摄像机轮询默认20s
      pollingTimer: null, // 轮播摄像机定时器
      pollingIndex: 0, // 当前轮播摄像机的序号
      videoList: [{}], // 摄像机列表
      elevatorBrandShow: false,
      serviceLifeShow: false,
      alarmTypeAnalysisShow: false,
      keyList: [
        {
          dom: 'timeRankingBarEcharts',
          show: true,
          title: '运行时间排行',
          type: '电梯运行时间',
          color: '#7CD0FF'
          // color: ['#7CD0FF', 'rgba(10,63,166,0)']
        },
        {
          dom: 'floorRankingBarEcharts',
          show: true,
          title: '运行楼层排行',
          type: '运行楼层',
          color: '#F4DB67'
          // color: ['#F4DB67', 'rgba(244,219,103,0)']
        },
        {
          dom: 'heightRankingBarEcharts',
          show: true,
          title: '运行距离排行',
          type: '电梯运行距离',
          color: '#F4DB67'
          // color: ['#F4DB67', 'rgba(244,219,103,0)']
        },
        {
          dom: 'numsRankingBarEcharts',
          show: true,
          title: '开门次数排行',
          type: '电梯开门次数',
          color: '#7CD0FF'
          // color: ['#7CD0FF', 'rgba(10,63,166,0)']
        }
      ],
      // healthLevelData: [
      //   {
      //     levelNum: 0,
      //     levelType: 'A',
      //     color: '#D20101',
      //     levelBg: 'level-a'
      //   },
      //   {
      //     levelNum: 0,
      //     levelType: 'B',
      //     color: '#FF8A26',
      //     levelBg: 'level-b'
      //   },
      //   {
      //     levelNum: 0,
      //     levelType: 'C',
      //     color: '#5188FC',
      //     levelBg: 'level-c'
      //   },
      //   {
      //     levelNum: 0,
      //     levelType: 'D',
      //     color: '#0B854D',
      //     levelBg: 'level-d'
      //   }
      // ],
      elevatorList: [
        {
          type: '1',
          connectNum: 0,
          totalNum: 0
        },
        {
          type: '2',
          connectNum: 0,
          totalNum: 0
        }
      ],
      elevatorOthersData: {
        openCount: '0',
        runDistance: '0',
        runNum: '0',
        runTime: '0',
        alarmCount: '0'
      },
      projectCode: '713e24b03094410499db0b08a2eccbcc',
      timer: null,
      videoUrl: 'rtsp://admin:yjl@2021@10.10.20.179:554/ch1/sub/av/stream', // 摄像机视频地址
      videoName: '' // 摄像机视频名称
    }
  },
  mounted() {
    this.mountendInit()
    this.getCameraListByProjectCode()
    this.timer = setInterval(() => {
      setTimeout(() => {
        this.mountendInit()
      }, 0)
    }, 1000 * timerNum)
    this.$once('hook:beforeDestroy', () => {
      clearInterval(this.timer)
      this.timer = null
      clearInterval(this.pollingTimer)
      this.pollingTimer = null
    })
  },
  methods: {
    // 初始化渲染
    mountendInit() {
      this.getIaasStatistics()
      this.getAlarmTypeAnalysisData()
      this.getTimeRankingData()
      this.getElevatorStatistics()
      this.getElevatorFloorList()
    },
    // 获取电梯类型数据 左上角统计 及电梯健康分布数据
    getElevatorStatistics() {
      const params = {
        projectCode: this.projectCode
      }
      getNumberOfElevators(params).then((res) => {
        const data = res.data
        if (data.code === '200') {
          this.elevatorStatisticsData = data.data
          const { list, recordList, ...othersData } = data.data
          // 直梯扶梯数量渲染
          this.elevatorList.map((e) => {
            const filterData = list.find((item) => item.elevatorType === e.type)
            if (filterData) {
              e.connectNum = filterData.connectNum
              e.totalNum = filterData.totalNum
            }
          })
          // 其他统计项渲染
          this.elevatorOthersData = othersData
          // 电梯健康分布渲染
          // this.healthLevelData.map((e) => {
          //   const filterData = recordList?.find((item) => item.record === e.levelType) ?? {}
          //   e.levelNum = filterData?.num ?? 0
          // })
        }
      })
    },
    getIaasStatistics() {
      getIaasStatistics().then((res) => {
        const data = res.data
        if (data.code === '200') {
          // 获取电梯品牌数据
          if (Object.prototype.hasOwnProperty.call(data.data, 'brand')) {
            this.elevatorBrandShow = true
            this.$nextTick(() => {
              this.setElevatorBrandEcharts(data.data.brand)
            })
          } else {
            this.elevatorBrandShow = false
          }
          // 获取使用年限数据
          if (Object.prototype.hasOwnProperty.call(data.data, 'life')) {
            this.serviceLifeShow = true
            this.$nextTick(() => {
              this.setServiceLifeEcharts(data.data.life)
            })
          } else {
            this.serviceLifeShow = false
          }
        }
      })
    },
    // 获取报警类型数据
    getAlarmTypeAnalysisData() {
      getReasonStatisticPie({
        projectCode: this.projectCode
      }).then((res) => {
        const data = res.data
        if (data.code === '200') {
          if (data.data.length > 0) {
            this.alarmTypeAnalysisShow = true
            this.$nextTick(() => {
              this.setAlarmTypeAnalysisEcharts(data.data)
            })
          } else {
            this.alarmTypeAnalysisShow = false
          }
        }
      })
    },
    // 获取运行时间 楼层 距离 次数 排行
    getTimeRankingData() {
      const data = {
        // entityMenuCode: "",
        projectCode: this.projectCode,
        parameterIds: '2639,2634,2641',
        timeType: null
      }
      getElevatorMonitoringList(data).then((res) => {
        const data = res.data
        if (data.code === '200') {
          this.keyList.forEach((item) => {
            // 楼层接口 单独执行
            if (item.dom === 'floorRankingBarEcharts') {
              return
            }
            this.$nextTick(() => {
              const chartdata = data.data?.find((e) => e.type === item.type)?.map
              item.show = (chartdata && Object.keys(chartdata).length > 0) ?? false
              this.setRankingBarEcharts(item.dom, chartdata?.yAxisData ?? [], chartdata?.seriesData ?? [], item.color)
            })
          })
        }
      })
      // const nameList = chartdata.map((item) => item.name);
    },
    getElevatorFloorList() {
      const data = {
        projectCode: this.projectCode,
        parameterIds: '2700'
      }
      getElevatorFloorList(data).then((res) => {
        const data = res.data
        if (data.code === '200') {
          this.keyList.forEach((item) => {
            if (item.dom === 'floorRankingBarEcharts') {
              this.$nextTick(() => {
                const chartdata = data.data.columnAsCount
                item.show = (chartdata && Object.keys(chartdata).length > 0) ?? false
                this.setRankingBarEcharts(item.dom, chartdata?.yAxisData ?? [], chartdata?.seriesData ?? [], item.color)
              })
            }
          })
        }
      })
    },
    // 电梯品牌数据echarts
    setElevatorBrandEcharts(data) {
      const getchart = echarts.init(document.getElementById('elevatorBrandPieEcharts'))
      const sum = data.totalNum
      const gap = (1 * sum) / 100
      const pieData = []
      const gapData = {
        name: '',
        value: gap,
        itemStyle: {
          color: 'transparent'
        }
      }
      for (let i = 0; i < data.brandPic.length; i++) {
        pieData.push({
          name: data.brandPic[i].assetBrand,
          value: data.brandPic[i].brandNum,
          itemStyle: {
            normal: {}
          }
        })
        data.brandPic > 1 ? pieData.push(gapData) : ''
      }
      let option = {
        tooltip: {
          trigger: 'item',
          formatter: function (params) {
            if (params.name) {
              return params.marker + params.name + ' : ' + params.value + '    (' + ((params.value / sum) * 100).toFixed(2) + '%)'
            }
          }
        },
        title: [
          {
            text: sum,
            left: '48%',
            top: '36%',
            textAlign: 'center',
            textStyle: {
              fontSize: '24',
              fontWeight: '600',
              color: '#FFF',
              textAlign: 'center'
            }
          },
          {
            text: '总数',
            x: '47%',
            y: '52%',
            textAlign: 'center',
            textStyle: {
              fontSize: '14',
              fontWeight: '400',
              color: '#A3A9AD',
              textAlign: 'center'
            }
          }
        ],
        series: [
          {
            type: 'pie',
            roundCap: true,
            radius: ['45%', '52%'],
            center: ['50%', '50%'],
            color: ['RGBA(100, 210, 255, 1)', 'RGBA(10, 132, 255, 1)', 'RGBA(244, 220, 110, 1)', 'RGBA(212, 222, 236, 1)', 'RGBA(94, 92, 230, 1)', 'RGBA(255, 255, 255, 1)'],
            labelLine: {
              show: true,
              length: 10,
              length2: 5
            },
            label: {
              show: true,
              color: '#fff',
              overflow: 'break',
              formatter: function (params) {
                return params.data.name + ' ' + params.data.value + '个'
              }
            },
            data: pieData
          },
          {
            name: '',
            type: 'pie',
            startAngle: 80,
            radius: ['40%'],
            hoverAnimation: false,
            center: ['50%', '50%'],
            tooltip: {
              show: false
            },
            itemStyle: {
              color: 'rgba(10, 25, 40, 1)',
              borderWidth: 1,
              borderColor: '#1E2C39'
            },
            data: [sum]
          },
          {
            name: '',
            type: 'pie',
            startAngle: 80,
            radius: ['36%'],
            hoverAnimation: false,
            center: ['50%', '50%'],
            tooltip: {
              show: false
            },
            itemStyle: {
              color: 'rgba(21, 36, 50, 1)'
            },
            data: [sum]
          },
          {
            name: '',
            type: 'pie',
            startAngle: 80,
            radius: ['58%', '59%'],
            center: ['50%', '50%'],
            hoverAnimation: false,
            tooltip: {
              show: false
            },
            itemStyle: {
              normal: {
                color: 'rgba(10, 25, 40, .1)',
                borderColor: '#1E2C39',
                borderWidth: 1
              }
            },
            data: [sum]
          }
        ]
      }
      getchart.clear()
      getchart.setOption(option)
      // 随着屏幕大小调节图表
      window.addEventListener('resize', () => {
        getchart.resize()
      })
    },
    // 使用年限数据echarts
    setServiceLifeEcharts(data) {
      const getchart = echarts.init(document.getElementById('serviceLifeBarEcharts'))
      const nameList = data.xserial
      const valueList = data.yserial
      // 数组求和
      // const total = eval(valueList.join('+'))
      const option = {
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            // 坐标轴指示器，坐标轴触发有效
            type: 'shadow' // 默认为直线，可选为：'line' | 'shadow'
          }
        },
        grid: {
          left: '5%',
          right: '15%',
          top: '10%',
          bottom: '5%',
          containLabel: true
        },
        xAxis: [
          {
            name: '年',
            type: 'category',
            axisLine: {
              lineStyle: {
                color: '#B6C7EA',
                type: 'dashed'
              }
            },
            axisLabel: {
              textStyle: {
                color: '#FFFFFF'
              }
            },
            data: nameList,
            axisTick: { show: false }
          }
        ],
        yAxis: [
          {
            type: 'value',
            minInterval: 1, // 最小刻度是1
            axisLabel: {
              textStyle: {
                color: '#FFFFFF'
              }
            },
            splitLine: {
              lineStyle: {
                color: '#B6C7EA',
                type: 'dashed'
              }
            }
          }
        ],
        series: [
          {
            type: 'bar',
            barWidth: 8,
            data: valueList,
            itemStyle: {
              normal: {
                color: '#FFE3A6'
                // color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                //   {
                //     offset: 0,
                //     color: 'rgba(10,132,255,0.4)'
                //   },
                //   {
                //     offset: 1,
                //     color: 'rgba(10,132,255,0)'
                //   }
                // ])
                // label: {
                //   show: true,
                //   position: 'top',
                //   formatter: (params) => {
                //     var text
                //     text = ((params.data * 100) / total).toFixed(2) + '%'
                //     return text
                //   }
                // }
              }
            }
          }
          // {
          //   type: 'scatter',
          //   symbolSize: 10,
          //   itemStyle: {
          //     color: {
          //       type: 'radial',
          //       r: 2,
          //       colorStops: [
          //         {
          //           offset: 0,
          //           color: '#0A84FF'
          //         },
          //         {
          //           offset: 1,
          //           color: '#0A84FF'
          //         }
          //       ],
          //       global: false
          //     }
          //   },
          //   tooltip: {
          //     show: false
          //   },
          //   silent: true,
          //   data: valueList
          // },
          // {
          //   name: '',
          //   type: 'line',
          //   symbolSize: 25,
          //   symbol: 'circle',
          //   label: {
          //     // normal: {
          //     //   show: true,
          //     //   position: 'top',
          //     //   formatter: '{c}%',
          //     //   color: '#fff'
          //     // }
          //   },
          //   tooltip: {
          //     show: false
          //   },
          //   itemStyle: {
          //     normal: {
          //       color: 'rgba(0, 0, 0, 0)',
          //       borderColor: '#0A84FF',
          //       borderWidth: 2
          //     }
          //   },
          //   data: valueList
          // }
        ]
      }
      getchart.clear()
      getchart.setOption(option)
      // 随着屏幕大小调节图表
      window.addEventListener('resize', () => {
        getchart.resize()
      })
    },
    // 报警类型数据echarts
    setAlarmTypeAnalysisEcharts(data) {
      const getchart = echarts.init(this.$refs.alarmTypeAnalysisPieEcharts)
      const nameList = Array.from(data, (item) => item.name)
      const sum = data.reduce((per, cur) => per + cur.value, 0)
      const gap = (1 * sum) / 100
      const pieData = []
      const gapData = {
        name: '',
        value: gap,
        itemStyle: {
          color: 'transparent'
        }
      }
      for (let i = 0; i < data.length; i++) {
        pieData.push({
          ...data[i],
          itemStyle: {
            normal: {
              // borderRadius: 5
            }
          }
        })
        pieData.push(gapData)
      }
      var objData = this.array2obj(pieData, 'name')
      const option = {
        tooltip: {
          trigger: 'item',
          formatter: function (params) {
            if (params.name) {
              return params.marker + params.name + ' : ' + params.value + '    (' + ((params.value / sum) * 100).toFixed(2) + '%)'
            }
          }
        },
        legend: {
          type: 'scroll',
          orient: 'vertical',
          top: '10',
          x: '55%',
          pageIconColor: '#5188fc', // 激活的分页按钮颜色
          pageIconInactiveColor: '#c2c6ce', // 没激活的分页按钮颜色
          data: nameList,
          itemWidth: 8,
          itemHeight: 8,
          itemGap: 16,
          formatter: function (name) {
            if (name) {
              return '{c|' + objData[name].percent + '}{a|' + name + '}'
            }
          },
          textStyle: {
            rich: {
              a: {
                align: 'center',
                fontSize: 13,
                color: 'rgba(255,255,255,1)'
              },
              c: {
                align: 'center',
                width: 70,
                fontSize: 15,
                color: '#00C2FF'
              }
            }
          }
        },
        series: [
          {
            type: 'pie',
            roundCap: true,
            radius: ['56%', '72%'],
            center: ['30%', '50%'],
            hoverAnimation: true,
            label: {
              normal: {
                show: false,
                position: 'center',
                // formatter: '{value|{d}' + '%' + '}\n{label|{b}}',
                formatter: function (data) {
                  if (data.name) {
                    return '{value|' + objData[data.name].percent + '}\n{label|' + data.name + '}'
                  }
                },
                rich: {
                  value: {
                    padding: 5,
                    align: 'center',
                    verticalAlign: 'middle',
                    fontSize: 24,
                    fontWeight: '600'
                  },
                  label: {
                    align: 'center',
                    verticalAlign: 'middle',
                    color: '#A3A9AD',
                    fontWeight: '400',
                    fontSize: 14
                  }
                }
              },
              emphasis: {
                show: true,
                textStyle: {
                  fontSize: '12',
                  color: '#fff'
                }
              }
            },
            data: pieData
          },
          {
            name: '',
            type: 'pie',
            startAngle: 80,
            radius: ['54%'],
            hoverAnimation: false,
            center: ['30%', '50%'],
            tooltip: {
              show: false
            },
            itemStyle: {
              color: 'rgba(10, 25, 40, .3)',
              borderWidth: 1,
              borderColor: '#1E2C39'
            },
            data: [sum]
          },
          {
            name: '',
            type: 'pie',
            startAngle: 80,
            radius: ['48%'],
            hoverAnimation: false,
            center: ['30%', '50%'],
            tooltip: {
              show: false
            },
            itemStyle: {
              color: 'rgba(21, 36, 50, .1)'
            },
            data: [sum]
          },
          {
            name: '',
            type: 'pie',
            startAngle: 80,
            radius: ['77%', '78%'],
            hoverAnimation: false,
            center: ['30%', '50%'],
            tooltip: {
              show: false
            },
            itemStyle: {
              normal: {
                color: 'rgba(10, 25, 40, .1)',
                borderColor: '#1E2C39',
                borderWidth: 1
              }
            },
            data: [sum]
          }
        ]
      }
      getchart.clear()
      getchart.setOption(option)
      // 随着屏幕大小调节图表
      window.addEventListener('resize', () => {
        getchart.resize()
      })
      getchart.dispatchAction({
        type: 'highlight',
        seriesIndex: 0,
        dataIndex: 0
      })
      getchart.on('mouseover', (e) => {
        // 取消默认高亮
        getchart.dispatchAction({
          type: 'downplay',
          seriesIndex: 0,
          dataIndex: 0
        })
      })
      // 鼠标移出后默认高亮
      getchart.on('mouseout', (e) => {
        getchart.dispatchAction({
          type: 'highlight',
          seriesIndex: 0,
          dataIndex: 0
        })
      })
    },
    // 电梯4个状态数据echarts
    setRankingBarEcharts(dom, nameList, valueList, color) {
      const getchart = echarts.init(this.$refs[dom][0])
      const option = {
        tooltip: {
          trigger: 'axis'
        },
        grid: {
          left: '6%',
          right: '12%',
          top: '6%',
          bottom: '5%',
          containLabel: true
        },
        xAxis: [
          {
            type: 'value',
            position: 'top',
            splitNumber: 3,
            splitLine: {
              lineStyle: {
                color: '#B6C7EA',
                type: 'dashed'
              }
            },
            axisLabel: {
              textStyle: {
                color: '#FFFFFF'
              },
              fontSize: 11,
              interval: 30000,
              hideOverlap: true // 隐藏互相遮挡的文本标签
            }
          }
        ],
        yAxis: [
          {
            type: 'category',
            data: nameList,
            axisTick: { show: false },
            axisLine: {
              lineStyle: {
                color: '#B6C7EA',
                type: 'dashed'
              }
            },
            axisLabel: {
              textStyle: {
                color: '#FFFFFF'
              },
              margin: 8,
              interval: 0,
              hideOverlap: true, // 隐藏互相遮挡的文本标签
              formatter: function (val) {
                var strs = val.split('') // 字符串数组
                var str = ''
                // strs循环 forEach 每五个字符增加换行符
                strs.forEach((item, index) => {
                  if (index % 6 === 0 && index !== 0) {
                    str += '\n'
                  }
                  str += item
                })
                return str
              }
            }
          }
        ],
        series: [
          {
            type: 'bar',
            barWidth: 8,
            data: valueList,
            itemStyle: {
              normal: {
                color: color
                // color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                //   {
                //     offset: 0,
                //     color: color[0]
                //   },
                //   {
                //     offset: 1,
                //     color: color[1]
                //   }
                // ])
              }
            }
          }
        ],
        dataZoom: [
          {
            type: 'slider',
            show: true,
            orient: 'vertical',
            // 设置组件控制的y轴
            yAxisIndex: 0,
            right: 4,
            start: 100,
            // end: 0,
            width: 5,
            // borderRadius: 0,
            borderColor: 'transparent',
            fillerColor: '#6580b8', // 滑动块的颜色
            backgroundColor: 'transparent', // 两边未选中的滑动条区域的颜色
            // 是否显示detail，即拖拽时候显示详细数值信息
            showDataShadow: false,
            showDetail: false,
            zoomLock: true,
            // 控制手柄的尺寸
            // handleSize: 12,
            // 是否在 dataZoom-silder 组件中显示数据阴影。数据阴影可以简单地反应数据走势。
            filterMode: 'filter',
            handleIcon: 'M-9.35,34.56V42m0-40V9.5m-2,0h4a2,2,0,0,1,2,2v21a2,2,0,0,1-2,2h-4a2,2,0,0,1-2-2v-21A2,2,0,0,1-11.35,9.5Z',
            handleStyle: {
              color: '#6580b8',
              borderColor: '#6580b8'
            },
            maxValueSpan: 6,
            minValueSpan: 6,
            brushSelect: false
          },
          {
            type: 'inside',
            // show: false,
            yAxisIndex: [0],
            zoomOnMouseWheel: false, // 关闭滚轮缩放
            moveOnMouseWheel: true, // 开启滚轮平移
            moveOnMouseMove: true // 鼠标移动能触发数据窗口平移
          }
        ]
      }
      getchart.clear()
      getchart.setOption(option)
      // 随着屏幕大小调节图表
      window.addEventListener('resize', () => {
        getchart.resize()
      })
    },
    // 摄像机轮播列表
    getCameraListByProjectCode() {
      return
      const params = {
        projectCode: this.projectCode
      }
      getCameraListByProjectCode(params, this.requestHttp).then((resp) => {
        const res = resp.data
        if (res.code === '200') {
          this.videoList = res.data
          // 重置轮播
          this.pollingIndex = 0
          clearInterval(this.pollingTimer)
          this.pollingTimer = null
          if (this.videoList.length) {
            setTimeout(() => {
              this.playVideo()
              this.changePollingTimer()
            }, 1000)
          }
        }
      })
    },
    // 保存轮询设置表单时长
    submitPollingForm() {
      localStorage.setItem('polingTime', this.pollingTime)
      this.changePollingDialog()
      clearInterval(this.pollingTimer)
      this.pollingTimer = null
      if (this.videoList.length) {
        this.changePollingTimer()
      }
    },
    // 切换摄像机轮询
    changePollingTimer() {
      if (this.pollingTimer == null) {
        this.pollingTimer = setInterval(() => {
          this.pollingIndex++
          if (this.pollingIndex >= this.videoList.length) {
            this.pollingIndex = 0
          }
          this.playVideo()
        }, this.pollingTime * 1000)
      } else {
        clearInterval(this.pollingTimer)
        this.pollingTimer = null
      }
    },
    // 打开摄像机轮询设置
    changePollingDialog() {
      this.PollingDialogVisible = !this.PollingDialogVisible
      console.log('ShowDialog')
      try {
        window.chrome.webview.hostObjects.sync.bridge.ShowDialog(this.PollingDialogVisible)
      } catch (error) {}
    },
    playVideo() {
      const videoData = this.videoList[this.pollingIndex]
      console.log(this.pollingIndex, videoData)
      this.videoUrl = videoData.icmRtsp
      this.videoName = videoData.icmName
      // this.$nextTick(() => {
      //   this.$refs.rtspCavas.initPlayer(videoUrl)
      // })
    },
    array2obj(array, key) {
      var resObj = {}
      for (var i = 0; i < array.length; i++) {
        resObj[array[i][key]] = array[i]
      }
      return resObj
    }
  }
}
</script>
<style lang="scss" scoped>
.content {
  width: 100%;
  height: 100%;
  position: relative;
  padding-top: 4px;
  box-sizing: border-box;
  .center-center {
    width: 100%;
    height: calc(100% - 4px);
    display: flex;
    align-items: center;
    justify-content: center;
    color: #4d5880;
    font-size: 16px;
  }
  .left-content {
    position: absolute;
    top: 0;
    left: 0;
    width: 30%;
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    transition: width 0.3s;
    overflow: hidden;
    background: url('~@/assets/images/qhdsys/bg-left-mask.png') no-repeat;
    background-size: 100% 100%;
    padding: 20px 24px 60px 24px;
    box-sizing: border-box;
    .left_top {
      height: 32%;
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      .top_count {
        display: flex;
        justify-content: space-between;
        height: calc(36% - 15px);
        .vertical_count,
        .escalator_count {
          width: calc(36% - 15px);
          height: 100%;
          background: #5188fc;
          display: flex;
          // flex-direction: column;
          justify-content: flex-start;
          align-items: center;
          padding: 0 20px;
          // background: url('~@/assets/images/elevator/elevator-type-bg.png') no-repeat;
          // background-size: 100% 100%;
          background: linear-gradient(180deg, rgba(5, 22, 52, 0) 0%, rgba(5, 22, 50, 0.35) 52%, #04285c 100%);
          border-radius: 0px 0px 0px 0px;
          opacity: 1;
          border-top: 1px solid #0086fb;
          border-bottom: 1px solid #0086fb;
          box-sizing: border-box;
          .count_right {
            display: flex;
            flex-direction: column;
            justify-content: space-around;
            height: 75%;
            padding-left: 15px;
            box-sizing: border-box;
            .count_title {
              font-size: 14px;
              font-family: PingFang-SC-Medium, PingFang-SC;
              font-weight: 500;
              color: #fff;
            }
            .count_num {
              font-size: 20px;
              font-family: Arial-Bold, Arial;
              font-weight: 500;
              color: #fff;
              span {
                color: #ffd661;
              }
            }
          }
        }
        .warn_count {
          width: calc(32% - 15px);
          height: 100%;
          display: flex;
          img {
            width: 56px;
            height: 56px;
            margin: auto 12px;
          }
          .count_right {
            display: flex;
            flex-direction: column;
            justify-content: space-evenly;
            color: #fff;
            .count_title {
              font-size: 14px;
              font-family: PingFang SC-Medium, PingFang SC;
            }
            .count_num {
              font-size: 20px;
              font-family: Arial-Bold, Arial;
              font-weight: bold;
            }
          }
        }
      }
      .top_operating {
        height: calc(64% - 20px);
      }
    }
    .left_center {
      height: 33%;
      display: flex;
      padding-top: 15px;
      box-sizing: border-box;
      justify-content: space-between;
      > div {
        width: calc(50% - 8px);
      }
    }
    .left_bottom {
      height: 35%;
      width: 100%;
    }
  }
  .right-content {
    position: absolute;
    top: 0;
    right: 0;
    width: 30%;
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    transition: width 0.3s;
    overflow: hidden;
    // background: url('~@/assets/images/elevator/mask-right-bg.png') no-repeat;
    background: url('~@/assets/images/qhdsys/bg-mask.png') no-repeat;
    background-size: 100% 100%;
    padding: 20px 24px 60px 24px;
    box-sizing: border-box;
    .all_bar_box {
      height: 65%;
      display: flex;
      flex-wrap: wrap;
      justify-content: space-between;
      > div {
        width: calc(50% - 8px);
        height: 50%;
      }
    }
    .elevator_health {
      height: calc(35%);
      padding-top: 15px;
      box-sizing: border-box;
      .health_level_box {
        height: calc(100% - 31px);
        display: flex;
        // flex-wrap: wrap;
        justify-content: space-between;
        align-content: space-between;
        padding: 15px 0 5px 0;
        .health_level {
          width: calc(50% - 10px);
          height: calc(50% - 14px);
          display: flex;
          border-radius: 4px;
          align-items: center;
          padding: 0 20px;
          background: url('~@/assets/images/elevator/healthy-level-bg.png') no-repeat;
          background-size: 100% 100%;
          .health_level_title {
            height: 60%;
            aspect-ratio: 0.9/1;
            background: #fff;
          }
          .health_level_num {
            font-size: 18px;
            padding-left: 15px;
            font-family: Arial-Bold, Arial;
            font-weight: 600;
            text-align: center;
          }
          .level-a {
            background: url('~@/assets/images/elevator/level-a.png') no-repeat;
            background-size: 100% 100%;
          }
          .level-b {
            background: url('~@/assets/images/elevator/level-b.png') no-repeat;
            background-size: 100% 100%;
          }
          .level-c {
            background: url('~@/assets/images/elevator/level-c.png') no-repeat;
            background-size: 100% 100%;
          }
          .level-d {
            background: url('~@/assets/images/elevator/level-d.png') no-repeat;
            background-size: 100% 100%;
          }
        }
        .video_preview {
          width: 0;
          flex: 1;
        }
        ::v-deep .contorl-btn {
          padding: 10px 0 10px 10px;
          width: fit-content;
          display: flex;
          flex-direction: column;
          justify-content: space-evenly;
          align-items: center;
          .el-button {
            padding: 8px 0!important;
            width: 85px;
          }
          .el-button+.el-button, .el-checkbox.is-bordered+.el-checkbox.is-bordered {
            margin-left: 0;
          }
        }
        .health_level {
          width: calc(50% - 10px);
          height: calc(50% - 14px);
          display: flex;
          border-radius: 4px;
          align-items: center;
          padding: 0 20px;
          box-sizing: border-box;
          background: url('~@/assets/images/elevator/healthy-level-bg.png') no-repeat;
          background-size: 100% 100%;
          .health_level_title {
            height: 60%;
            aspect-ratio: 0.9/1;
            background: #fff;
          }
          .health_level_num {
            font-size: 18px;
            padding-left: 15px;
            box-sizing: border-box;
            font-family: Arial-Bold, Arial;
            font-weight: 600;
            text-align: center;
          }
          .level-a {
            background: url('~@/assets/images/elevator/level-a.png') no-repeat;
            background-size: 100% 100%;
          }
          .level-b {
            background: url('~@/assets/images/elevator/level-b.png') no-repeat;
            background-size: 100% 100%;
          }
          .level-c {
            background: url('~@/assets/images/elevator/level-c.png') no-repeat;
            background-size: 100% 100%;
          }
          .level-d {
            background: url('~@/assets/images/elevator/level-d.png') no-repeat;
            background-size: 100% 100%;
          }
        }
      }
    }
  }
  .echarts_title {
    height: 20px;
    font-size: 15px;
    font-family: NotoSansHans-Medium, NotoSansHans;
    font-weight: 600;
    color: #393a3d;
    line-height: 20px;
    text-align: center;
  }
  .echart-null {
    margin: 0 auto;
    height: calc(100% - 31px);
    width: 50%;
    text-align: center;
    color: #8a8c8f;
    img {
      max-width: 100%;
      max-height: calc(100% - 20px);
    }
    div {
      font-size: 14px;
    }
  }
  #elevatorBrandPieEcharts,
  #serviceLifeBarEcharts,
  #alarmTypeAnalysisPieEcharts,
  #timeRankingBarEcharts,
  #floorRankingBarEcharts,
  #heightRankingBarEcharts,
  #numsRankingBarEcharts {
    width: 100%;
    height: 100%;
    z-index: 2;
  }
  .pie-decoration {
    position: absolute;
    height: 50%;
    aspect-ratio: 1;
    background: url('~@/assets/images/elevator/pie-decoration.png') no-repeat;
    background-size: 100% 100%;
  }
  .operating_common {
    // height: calc(25% - 20px);
    display: flex;
    justify-content: space-between;
    flex-wrap: wrap;
    // align-items: ;
    .operating_box {
      width: calc(50% - 8px);
      height: calc(50% - 16px);
      display: flex;
      .box_icon {
        margin: auto 0;
        height: 80%;
        aspect-ratio: 1;
      }
      .duration-icon {
        background: url('~@/assets/images/elevator/duration-icon.png') no-repeat;
        background-size: 100% 100%;
      }
      .distance-icon {
        background: url('~@/assets/images/elevator/distance-icon.png') no-repeat;
        background-size: 100% 100%;
      }
      .numbers-icon {
        background: url('~@/assets/images/elevator/numbers-icon.png') no-repeat;
        background-size: 100% 100%;
      }
      .box_content {
        display: flex;
        flex-direction: column;
        justify-content: space-evenly;
        padding-left: 10px;
        box-sizing: border-box;
        > span {
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
        > span:first-child {
          font-size: 15px;
          font-family: Alibaba PuHuiTi-Regular, Alibaba PuHuiTi;
          color: #a2b7d9;
        }
        > span:last-child {
          font-size: 12px;
          font-family: PingFang SC-Medium, PingFang SC;
          font-weight: 500;
          color: #ccced3;
        }
        .big_num {
          font-size: 20px;
          font-family: Arial-Bold, Arial;
          font-weight: bold;
          color: #c9dfff;
        }
      }
    }
  }
  .card_box_title {
    height: 31px;
    width: 100%;
    line-height: 31px;
    padding-left: 45px;
    box-sizing: border-box;
    font-size: 15px;
    font-family: Alibaba PuHuiTi-Medium, Alibaba PuHuiTi;
    font-weight: 500;
    color: #b7cfff;
  }
  .card_box_bg {
    background: url('~@/assets/images/elevator/card-title-bg.png') no-repeat;
    background-size: 100% 100%;
  }
  .card_box_long_bg {
    background: url('~@/assets/images/elevator/card-title-long-bg.png') no-repeat;
    background-size: 100% 100%;
  }
  .card_box_short_bg {
    background: url('~@/assets/images/elevator/card-title-short-bg.png') no-repeat;
    background-size: 100% 100%;
  }
}
::v-deep .polling-dialog {
  width: 350px;
  background: url('~@/assets/images/elevator/polling-bg.png') no-repeat;
  background-size: 100% 100%;
  .el-dialog__header {
    padding: 10px 10px 10px 26px;
    span {
      font-size: 20px;
      font-family: HarmonyOS Sans SC-Medium, HarmonyOS Sans SC;
      font-weight: 500;
      color: #DCE9FF;
    }
    .el-dialog__headerbtn .el-dialog__close {
      color: #7CD0FF;
      font-size: 20px;
    }
  }
  .polling-content {
    label {
      font-size: 14px;
      font-family: PingFang SC-Regular, PingFang SC;
      font-weight: 400;
      color: #FFFFFF;
    }
    .el-input {
      border-radius: 4px 4px 4px 4px;
      .el-input__inner {
        background: rgba(3,23,81,0.5);
        border: 1px solid #193382;
        color: #fff;
      }
      .el-input-group__append, .el-input-group__prepend {
        padding: 0 10px;
        background: rgba(3,23,81,0.5);
        border-color: #193382;
        color: #fff;
      }
    }
  }
}
</style>
