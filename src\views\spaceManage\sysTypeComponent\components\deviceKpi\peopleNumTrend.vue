<template>
  <div class="peopleNumTrend">
    <div id="peopleNumTrend_chart"></div>
  </div>
</template>

<script>
import * as echarts from 'echarts'
import { mockPeopleNumTrendData } from './mockData.js'
export default {
  name: 'peopleNumTrend',
  data() {
    return {
      peopleNumTrendData: {
        legendList: ['检查人次', '检查阳性人次', '阳性率'],
        nameList: ['2019', '2020', '2021', '2022', '2023'],
        checkNum: [0, 0, 0, 0, 0],
        checkPositiveNum: [0, 0, 0, 0, 0],
        positiveRate: [0, 0, 0, 0, 0]
      }
    }
  },
  computed: {},
  created() {},
  mounted() {
    if (__PATH.VUE_APP_HOSPITAL_NODE === 'outernet') {
      this.peopleNumTrendData = mockPeopleNumTrendData
    }
    this.$nextTick(() => {
      this.setChart()
    })
  },
  methods: {
    setChart() {
      const getchart = echarts.init(document.getElementById('peopleNumTrend_chart'))
      const option = {
        title: {
          text: '检查人数趋势图',
          textStyle: {
            fontSize: 16,
            fontWeight: 'normal',
            color: '#fff'
          },
          x: 20,
          top: 20
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow',
            label: {
              show: true
            }
          }
        },
        grid: {
          left: '7%',
          top: '20%',
          right: '7%',
          bottom: '10%'
        },
        legend: {
          data: this.peopleNumTrendData.legendList,
          top: '8%',
          textStyle: {
            color: '#FFFFFF',
            fontSize: 12
          }
        },
        xAxis: {
          data: this.peopleNumTrendData.nameList,
          axisLine: {
            show: true, // 隐藏X轴轴线
            lineStyle: {
              color: 'rgba(133, 145, 206, 0.50)',
              width: 1
            }
          },
          axisTick: {
            show: false // 隐藏X轴刻度
          },
          axisLabel: {
            show: true,
            textStyle: {
              color: '#E6F7FF', // X轴文字颜色
              fontSize: 12
            }
            //   interval: 0,
          }
        },
        yAxis: [
          {
            type: 'value',
            splitLine: {
              show: true,
              lineStyle: {
                type: 'dashed',
                width: 1,
                color: 'rgba(230, 247, 255, 0.20)'
              }
            },
            axisTick: {
              show: false
            },
            axisLine: {
              show: false
            },
            axisLabel: {
              show: true,
              textStyle: {
                color: '#E6F7FF',
                fontSize: 12
              }
            }
          },
          {
            type: 'value',
            position: 'right',
            splitLine: {
              show: false
            },
            axisTick: {
              show: false
            },
            axisLine: {
              show: false
            },
            axisLabel: {
              show: true,
              formatter: '{value} %', // 右侧Y轴文字显示
              textStyle: {
                color: '#E6F7FF',
                fontSize: 12
              }
            }
          }
        ],
        series: [
          {
            name: '检查人次',
            type: 'bar',
            barWidth: 18,
            itemStyle: {
              normal: {
                color: '#3CC1FF'
              }
            },
            data: this.peopleNumTrendData.checkNum
          },
          {
            name: '检查阳性人次',
            type: 'bar',
            barWidth: 18,
            itemStyle: {
              normal: {
                color: '#FF2D55'
              }
            },
            data: this.peopleNumTrendData.checkPositiveNum
          },
          {
            name: '阳性率',
            type: 'line',
            yAxisIndex: 1, // 使用的 y 轴的 index，在单个图表实例中存在多个 y轴的时候有用
            showAllSymbol: true, // 显示所有图形。
            symbol: 'circle', // 标记的图形为实心圆
            symbolSize: 5, // 标记的大小
            itemStyle: {
              // 折线拐点标志的样式
              color: '#FFCA64',
              borderColor: '#fff',
              width: 1
            },
            lineStyle: {
              color: '#FFCA64',
              width: 1
            },
            data: this.peopleNumTrendData.positiveRate
          }
        ]
      }
      getchart.clear()
      getchart.setOption(option)
      // 随着屏幕大小调节图表
      window.addEventListener('resize', () => {
        getchart.resize()
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.peopleNumTrend {
  width: 100%;
  height: 100%;
  background: rgba(53, 98, 219, 0.06);
  #peopleNumTrend_chart {
    width: 100%;
    height: 100%;
  }
}
</style>
