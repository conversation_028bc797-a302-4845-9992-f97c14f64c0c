<template>
  <div class="main">
    <el-dialog v-dialogDrag :visible.sync="addDialogShow" :close-on-click-modal="false" :before-close="closeDialog" class="personDialog">
      <template slot="title">
        <span class="dialog-title">{{ dialogTitle }}</span>
      </template>
      <div class="dialog-content" style="width: 100%">
        <el-form ref="formInline" :model="formInline" :rules="rules" :inline="true" label-width="auto" size="medium">
          <el-collapse v-model="activeNames">
            <el-collapse-item title="警情基本信息" name="1">
              <div class="formRow">
                <el-form-item label="报警来源：" prop="alarmSourceObj">
                  <el-select v-if="type !== 'detail'" value-key="id" v-model="formInline.alarmSourceObj" placeholder="请选择报警来源">
                    <el-option v-for="item in alarmSourceOption" :key="item.dictValue" :label="item.labelName" :value="{ id: item.dictValue, name: item.labelName }"> </el-option>
                  </el-select>
                  <span class="form-detail-span" v-else>{{ formInline.alarmSourceName }}</span>
                </el-form-item>
              </div>
              <div class="formRow">
                <el-form-item label="报警类型：" prop="alarmTypeObj">
                  <el-select v-if="type !== 'detail'" value-key="id" v-model="formInline.alarmTypeObj" placeholder="请选择报警类型">
                    <el-option v-for="item in alarmTypeOption" :key="item.dictValue" :label="item.labelName" :value="{ id: item.dictValue, name: item.labelName }"> </el-option>
                  </el-select>
                  <span class="form-detail-span" v-else>{{ formInline.alarmTypeName }}</span>
                </el-form-item>
              </div>
              <div class="formRow">
                <el-form-item label="报警参数：" prop="alarmCodeObj">
                  <el-select v-if="type !== 'detail'" value-key="id" v-model="formInline.alarmCodeObj" placeholder="请选择报警参数">
                    <el-option v-for="item in alarmCodeOption" :key="item.dictValue" :label="item.labelName" :value="{ id: item.dictValue, name: item.labelName }"> </el-option>
                  </el-select>
                  <span class="form-detail-span" v-else>{{ formInline.alarmCodeName }}</span>
                </el-form-item>
              </div>
              <div class="formRow">
                <el-form-item label="警情级别：" prop="policeLevelObj">
                  <el-select v-if="type !== 'detail'" value-key="id" v-model="formInline.policeLevelObj" placeholder="请选择警情级别">
                    <el-option v-for="item in policeLevelOption" :key="item.dictValue" :label="item.labelName" :value="{ id: item.dictValue, name: item.labelName }"> </el-option>
                  </el-select>
                  <span class="form-detail-span" v-else>{{ formInline.policeLevelName }}</span>
                </el-form-item>
              </div>
              <div class="formRow">
                <el-form-item label="是否接收报警：" prop="takeOver">
                  <el-radio v-if="type !== 'detail'" v-model="formInline.takeOver" :label="0">是</el-radio>
                  <el-radio v-if="type !== 'detail'" v-model="formInline.takeOver" :label="1">否</el-radio>
                  <span class="form-detail-span" v-else>{{ formInline.takeOver === 0 ? '是' : formInline.takeOver === 1 ? '否' : '' }}</span>
                </el-form-item>
              </div>
            </el-collapse-item>
            <el-collapse-item title="报警联动配置" name="2">
              <div class="formRow">
                <el-form-item label="启用状态：" prop="enabledState">
                  <el-radio @change="enabledStateChange(0)" v-if="type !== 'detail'" v-model="formInline.enabledState" :label="0">启用</el-radio>
                  <el-radio @change="enabledStateChange(1)" v-if="type !== 'detail'" v-model="formInline.enabledState" :label="1">禁用</el-radio>
                  <span class="form-detail-span" v-else>{{ formInline.enabledState === 0 ? '启用' : formInline.enabledState === 1 ? '禁用' : '' }}</span>
                </el-form-item>
              </div>
              <!-- <div class="formRow">
                <el-form-item label="报警弹单：" prop="alarmBulletin">
                  <el-radio v-if="type !== 'detail'" v-model="formInline.alarmBulletin" :label="0">启用</el-radio>
                  <el-radio v-if="type !== 'detail'" v-model="formInline.alarmBulletin" :label="1">禁用</el-radio>
                  <span class="form-detail-span" v-else>{{ formInline.alarmBulletin === 0 ? '启用' : formInline.alarmBulletin === 1 ? '禁用' : '' }}</span>
                </el-form-item>
              </div> -->
              <div class="formRow">
                <el-form-item label="警情上墙：" prop="alarmWall">
                  <el-radio v-if="type !== 'detail'" v-model="formInline.alarmWall" :label="0">启用</el-radio>
                  <el-radio v-if="type !== 'detail'" v-model="formInline.alarmWall" :label="1">禁用</el-radio>
                  <span class="form-detail-span" v-else>{{ formInline.alarmWall === 0 ? '启用' : formInline.alarmWall === 1 ? '禁用' : '' }}</span>
                </el-form-item>
              </div>
              <div class="formRow">
                <el-form-item label="声光报警：" prop="audibleAlarm">
                  <el-radio v-if="type !== 'detail'" v-model="formInline.audibleAlarm" :label="0">启用</el-radio>
                  <el-radio v-if="type !== 'detail'" v-model="formInline.audibleAlarm" :label="1">禁用</el-radio>
                  <span class="form-detail-span" v-else>{{ formInline.audibleAlarm === 0 ? '启用' : formInline.audibleAlarm === 1 ? '禁用' : '' }}</span>
                </el-form-item>
              </div>
              <div class="formRow">
                <el-form-item label="LED显示：" prop="ledshow">
                  <el-radio v-if="type !== 'detail'" v-model="formInline.ledshow" :label="0">启用</el-radio>
                  <el-radio v-if="type !== 'detail'" v-model="formInline.ledshow" :label="1">禁用</el-radio>
                  <span class="form-detail-span" v-else>{{ formInline.ledshow === 0 ? '启用' : formInline.ledshow === 1 ? '禁用' : '' }}</span>
                </el-form-item>
              </div>
            </el-collapse-item>
            <el-collapse-item title="预案配置" name="3">
              <div class="formRow">
                <el-form-item label="联动预案：" prop="planObj">
                  <el-select v-if="type !== 'detail'" value-key="id" v-model="formInline.planObj" placeholder="请选择联动预案">
                    <el-option v-for="item in planOptions" :key="item.id" :label="item.prePlanName" :value="{ id: item.id, name: item.prePlanName }"> </el-option>
                  </el-select>
                  <span class="form-detail-span" v-else>{{ formInline.planName }}</span>
                </el-form-item>
              </div>
              <div class="formRow">
                <el-form-item label="自启动预案：" prop="automaticPlan">
                  <el-radio v-if="type !== 'detail'" v-model="formInline.automaticPlan" :label="0">是</el-radio>
                  <el-radio v-if="type !== 'detail'" v-model="formInline.automaticPlan" :label="1">否</el-radio>
                  <span class="form-detail-span" v-else>{{ formInline.automaticPlan === 0 ? '是' : formInline.automaticPlan === 1 ? '否' : '' }}</span>
                </el-form-item>
              </div>
            </el-collapse-item>
            <el-collapse-item title="工单配置" name="4">
              <div class="formRow">
                <el-form-item label="关联工单：" prop="workOrderObj">
                  <el-select v-if="type !== 'detail'" value-key="id" v-model="formInline.workOrderObj" placeholder="请选择关联工单">
                    <el-option v-for="item in workOrderOption" :key="item.id" :label="item.workTypeName" :value="{ id: item.id, name: item.workTypeName }"> </el-option>
                  </el-select>
                  <span class="form-detail-span" v-else>{{ formInline.workOrderName }}</span>
                </el-form-item>
              </div>
              <div class="formRow" v-if="(formInline.workOrderObj.id && formInline.workOrderObj.id !== '') || formInline.departmentName">
                <el-form-item label="服务部门：" prop="orderDeptObj">
                  <el-select v-if="type !== 'detail'" value-key="id" v-model="formInline.orderDeptObj" placeholder="请选择服务部门">
                    <el-option v-for="item in sourcesDeptOptions" :key="item.value" :label="item.label" :value="{ id: item.value, name: item.label }"> </el-option>
                  </el-select>
                  <span class="form-detail-span" v-else>{{ formInline.departmentName }}</span>
                </el-form-item>
              </div>
              <div class="formRow">
                <el-form-item label="自创建工单：" prop="automaticWorkOrder">
                  <el-radio v-if="type !== 'detail'" v-model="formInline.automaticWorkOrder" :label="0">是</el-radio>
                  <el-radio v-if="type !== 'detail'" v-model="formInline.automaticWorkOrder" :label="1">否</el-radio>
                  <span class="form-detail-span" v-else>{{ formInline.automaticWorkOrder === 0 ? '是' : formInline.automaticWorkOrder === 1 ? '否' : '' }}</span>
                </el-form-item>
              </div>
            </el-collapse-item>
          </el-collapse>
        </el-form>
      </div>
      <span slot="footer" class="dialog-footer" v-if="type !== 'detail'">
        <el-button class="sino-button-sure" @click="closeDialog">取 消</el-button>
        <el-button class="sino-button-sure" type="primary" @click="userSaveFn">保 存</el-button>
      </span>
    </el-dialog>
  </div>
</template>
<script>
import { submitWarn, findIntelligencePrePlanEnactment, getWorkOrderConfigList, getDictDataList } from '@/utils/api'
import { getAllOffice } from '@/utils/peaceRightScreenApi'
export default {
  name: 'addWarn',
  props: {
    addDialogShow: {
      type: Boolean,
      default: false
    },
    dialogTitle: {
      type: String,
      default: ''
    },
    rowData: {
      type: Object,
      default: function () {
        return {}
      }
    },
    type: {
      type: String,
      default: ''
    }
  },
  data() {
    const validateObject = (rule, value, callback) => {
      // 这里每一种情况都要callback，不然表单验证会失效
      if (JSON.stringify(value) === '{}') {
        callback(new Error('请选择'))
      } else {
        callback()
      }
    }
    return {
      formInline: {
        alarmSourceObj: {},
        alarmTypeObj: {},
        alarmCodeObj: {},
        policeLevelObj: {},
        takeOver: 0,
        enabledState: 0,
        // alarmBulletin: 0,
        alarmWall: 0,
        audibleAlarm: 0,
        ledshow: 0,
        planObj: {},
        automaticPlan: 0,
        workOrderObj: {},
        orderDeptObj: {},
        automaticWorkOrder: 0
      },
      roleOptions: [],
      planOptions: [],
      alarmSourceOption: [],
      alarmCodeOption: [],
      alarmTypeOption: [],
      policeLevelOption: [],
      workOrderOption: [],
      sourcesDeptOptions: [],
      rules: {
        alarmSourceObj: [
          { required: true, message: '请选择报警来源', trigger: 'blur' },
          { validator: validateObject, trigger: 'blur' }
        ],
        alarmTypeObj: [
          { required: true, message: '请选择报警类型', trigger: 'blur' },
          { validator: validateObject, trigger: 'blur' }
        ],
        alarmCodeObj: [
          { required: true, message: '请选择报警参数', trigger: 'blur' },
          { validator: validateObject, trigger: 'blur' }
        ],
        policeLevelObj: [
          { required: true, message: '请选择警情级别', trigger: 'blur' },
          { validator: validateObject, trigger: 'blur' }
        ],
        orderDeptObj: [
          { required: true, message: '请选择服务部门', trigger: 'blur' },
          { validator: validateObject, trigger: 'blur' }
        ],
        takeOver: [{ required: true, message: '请选择是否接收报警', trigger: 'blur' }],
        enabledState: [{ required: true, message: '请选择是否启用状态', trigger: 'blur' }]
      },
      activeNames: ['1', '2', '3', '4']
    }
  },
  watch: {},
  mounted() {
    const dictList = JSON.parse(localStorage.getItem('dictList'))
    if (dictList === null || dictList === undefined) {
      this.setDictStorage()
    } else {
      this.setOptionList(dictList)
    }
    const newData = JSON.parse(JSON.stringify(this.rowData))
    this.$forceUpdate()
    if (this.type !== 'detail') {
      this.getPlanList()
      this.getWorkOrderList()
      this.getSourcesDeptOptions()
      if (this.type === 'edit') {
        newData.alarmCodeObj = {
          id: newData.alarmCodeId,
          name: newData.alarmCodeName
        }
        newData.alarmSourceObj = {
          id: newData.alarmSourceId,
          name: newData.alarmSourceName
        }
        newData.alarmTypeObj = {
          id: newData.alarmTypeId,
          name: newData.alarmTypeName
        }
        newData.planObj = {
          id: newData.planId,
          name: newData.planName
        }
        newData.policeLevelObj = {
          id: newData.policeLevelId,
          name: newData.policeLevelName
        }
        newData.workOrderObj = {
          id: newData.workOrderId ? String(newData.workOrderId) : newData.workOrderId,
          name: newData.workOrderName
        }
        newData.orderDeptObj = {
          id: newData.departmentId ? String(newData.departmentId) : newData.departmentId,
          name: newData.departmentName
        }
      }
    }
    Object.assign(this.formInline, newData)
  },
  methods: {
    enabledStateChange(state) {
      if (state === 1) {
        // this.formInline.alarmBulletin = 1
        this.formInline.alarmWall = 1
        this.formInline.audibleAlarm = 1
        this.formInline.ledshow = 1
      }
    },
    // 字典信息重新赋值
    setDictStorage() {
      getDictDataList({}).then((res) => {
        const data = res.data
        if (data.code === '200') {
          localStorage.setItem('dictList', JSON.stringify(data.data))
          this.setOptionList(data.data)
        }
      })
    },
    setOptionList(dictList) {
      this.alarmSourceOption = this.getSelectDictOption(dictList, '报警来源')
      this.alarmCodeOption = this.getSelectDictOption(dictList, '报警参数')
      this.alarmTypeOption = this.getSelectDictOption(dictList, '报警类型')
      this.policeLevelOption = this.getSelectDictOption(dictList, '警情级别')
    },
    getSelectDictOption(list, type) {
      const option = list.length ? list.filter((e) => e.dictName === type) : []
      const optionList = option.length ? option[0].dictConfigList : []
      return optionList
    },
    // 获取科室数据
    getSourcesDeptOptions() {
      getAllOffice().then((res) => {
        if (res.data.success) {
          this.sourcesDeptOptions = res.data.body.result
        }
      })
    },
    // 取消按钮
    closeDialog() {
      this.$emit('closeDialog')
    },
    // 获取预案
    getPlanList() {
      const params = {
        currentPage: 1,
        pageSize: 999
      }
      findIntelligencePrePlanEnactment(params).then((res) => {
        const data = res.data
        if (data.code === '200') {
          if (data.data.records.length) {
            this.planOptions = data.data.records
          }
        } else {
          this.$message({
            message: data.msg,
            type: 'warning'
          })
        }
      })
    },
    // 获取一站式工单
    getWorkOrderList() {
      const params = {
        currentPage: 1,
        pageSize: 999
      }
      getWorkOrderConfigList(params).then((res) => {
        const data = res.data
        if (data.code === '200') {
          if (data.data.list.length) {
            this.workOrderOption = data.data.list
          }
        } else {
          this.$message({
            message: data.message,
            type: 'warning'
          })
        }
      })
    },
    // 确认按钮
    userSaveFn() {
      const formData = JSON.parse(JSON.stringify(this.formInline))
      this.$refs.formInline.validate((valid) => {
        if (valid) {
          Object.assign(formData, {
            alarmCodeId: this.formInline.alarmCodeObj.id,
            alarmCodeName: this.formInline.alarmCodeObj.name,
            alarmSourceId: this.formInline.alarmSourceObj.id,
            alarmSourceName: this.formInline.alarmSourceObj.name,
            alarmTypeId: this.formInline.alarmTypeObj.id,
            alarmTypeName: this.formInline.alarmTypeObj.name,
            planId: this.formInline.planObj.id,
            planName: this.formInline.planObj.name,
            policeLevelId: this.formInline.policeLevelObj.id,
            policeLevelName: this.formInline.policeLevelObj.name,
            workOrderId: this.formInline.workOrderObj.id,
            workOrderName: this.formInline.workOrderObj.name,
            departmentId: this.formInline.orderDeptObj.id,
            departmentName: this.formInline.orderDeptObj.name
          })
          delete formData.alarmCodeObj
          delete formData.alarmSourceObj
          delete formData.alarmTypeObj
          delete formData.planObj
          delete formData.policeLevelObj
          delete formData.workOrderObj
          delete formData.orderDeptObj
          submitWarn(formData).then((res) => {
            const data = res.data
            if (data.code === '200') {
              this.$message({
                message: this.dialogTitle + '成功！',
                type: 'success'
              })
              this.$emit('warnSure', this.type)
            } else {
              // this.$message({
              //   message: data.msg,
              //   type: 'warning'
              // })
            }
          })
        } else {
          console.log('error submit!!')
          // return false
        }
      })
    }
  }
}
</script>

<style lang="scss" type="text/css" scoped>
// .main {
//   width: 50%;
//   height: 40px;
//   display: inline-block;
//   line-height: 40px;
// }
::v-deep .el-collapse {
  border: none;
}
::v-deep .el-collapse-item__header::before {
  content: '';
  display: inline-block;
  width: 2px;
  border-radius: 1px;
  height: 13px;
  background: #ffe3a6;
  margin-right: 10px;
}
::v-deep .el-dialog__body {
  padding: 10px 13% 10px 5%;
}
::v-deep .el-collapse-item__header {
  // background: center;
  background-color: #232e57;
  border-radius: 5px;
  height: 30px;
  line-height: 30px;
  border: none;
  color: #ffe3a6;
  // margin: 10px 0 20px 0;
  margin-bottom: 15px;
  padding-left: 10px;
}
::v-deep .el-collapse-item__content {
  color: #a3a9c0;
}
::v-deep .el-collapse-item__wrap {
  background: center;
  border: none;
}
.formRow {
  width: 100%;
  display: flex;
  ::v-deep .el-form-item {
    flex: 1;
    display: flex;
  }
  ::v-deep .el-form-item__content {
    flex: 1;
  }
  .el-select ::v-deep .el-tag {
    border-color: #ffe3a6;
    color: #ffe3a6;
    background: center;
    .el-icon-close {
      display: none;
    }
  }
}
</style>
