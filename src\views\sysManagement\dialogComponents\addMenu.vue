<template>
  <div class="main">
    <el-dialog v-dialogDrag :visible.sync="addDialogShow" :close-on-click-modal="false" :before-close="closeDialog" class="personDialog">
      <template slot="title">
        <span class="dialog-title">{{ dialogTitle }}</span>
      </template>
      <div class="dialog-content" style="width: 100%">
        <el-form ref="formInline" :model="formInline" :rules="rules" :inline="true" label-width="auto" size="medium">
          <div class="formRow" style="display: flex">
            <el-form-item label="菜单名称：" prop="menuName">
              <el-input v-if="type !== 'detail'" v-model.trim="formInline.menuName" placeholder="请输入菜单名称"></el-input>
              <span class="form-detail-span" v-else>{{ formInline.menuName }}</span>
            </el-form-item>
          </div>
          <!-- <div class="formRow" style="display: flex">
            <el-form-item label="菜单地址：" prop="menuUrl">
              <el-input v-if="type !== 'detail'" v-model.trim="formInline.menuUrl" placeholder="请输入菜单地址"></el-input>
              <span class="form-detail-span" v-else>{{ formInline.menuUrl }}</span>
            </el-form-item>
          </div> -->
          <div class="formRow" style="display: flex">
            <el-form-item label="所属模块：" prop="belongModule">
              <el-select v-if="type !== 'detail'" v-model="formInline.belongModule" placeholder="请选择所属模块">
                <el-option v-for="item in modularOptions" :key="item.value" :label="item.label" :value="item.value"> </el-option>
              </el-select>
              <span class="form-detail-span" v-else>{{ formInline.belongModule === 1 ? '左屏' : formInline.belongModule === 2 ? '中屏' : formInline.belongModule === 3 ? '右屏' : '' }}</span>
            </el-form-item>
          </div>
          <div class="formRow" style="display: flex">
            <el-form-item label="上级菜单：" prop="menuFid">
              <select-tree v-if="type !== 'detail'" v-model="formInline.menuFid" @getName="getParentMenuName" :data="treeData" :props="{ label: 'menuName', children: 'children' }"></select-tree>
              <span class="form-detail-span" v-else>{{ formInline.menuParentName }}</span>
            </el-form-item>
          </div>
          <div class="formRow" style="display: flex">
            <el-form-item label="排序：" prop="menuSort">
              <el-input v-if="type !== 'detail'" v-model.trim="formInline.menuSort" placeholder="请输入序号" onkeyup="value=Number(value.replace(/[^\d]/g,''));"></el-input>
              <span class="form-detail-span" v-else>{{ formInline.menuSort }}</span>
            </el-form-item>
          </div>
        </el-form>
      </div>
      <span slot="footer" class="dialog-footer" v-if="type !== 'detail'">
        <el-button class="sino-button-sure" @click="closeDialog">取 消</el-button>
        <el-button class="sino-button-sure" type="primary" @click="userSaveFn">保 存</el-button>
      </span>
    </el-dialog>
  </div>
</template>
<script>
import selectTree from '@/components/common/selectTree'
import { getMenuList, submitMenu } from '@/utils/api'
export default {
  name: 'addMenu',
  components: {
    selectTree
  },
  props: {
    addDialogShow: {
      type: Boolean,
      default: false
    },
    dialogTitle: {
      type: String,
      default: ''
    },
    rowData: {
      type: Object,
      default: function () {
        return {}
      }
    },
    type: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      formInline: {
        menuName: '',
        belongModule: '',
        menuFid: '',
        menuSort: 0,
        // menuUrl: '',
        menuParentName: ''
      },
      modularOptions: [
        {
          label: '左屏',
          value: 1
        },
        {
          label: '中屏',
          value: 2
        },
        {
          label: '右屏',
          value: 3
        }
      ],
      treeData: [],
      rules: {
        menuName: [
          { required: true, message: '请输入菜单名称', trigger: 'blur' },
          { min: 2, max: 50, message: '长度在 2 到 50 个字符', trigger: 'blur' }
        ],
        belongModule: [{ required: true, message: '请选择菜单所属模块', trigger: 'blur' }]
      }
    }
  },
  watch: {},
  mounted() {
    const newData = JSON.parse(JSON.stringify(this.rowData))
    if (this.type === 'edit') {
      newData.menuFid = newData.menuFid ? newData.menuFid : ''
    }
    Object.assign(this.formInline, newData)
    if (this.type !== 'detail') {
      this.getMenuList()
    }
  },
  methods: {
    getMenuList() {
      getMenuList().then((res) => {
        const data = res.data
        if (data.code === '200' && data.data.length) {
          this.treeData = this.$tools.listToTree(data.data, 'id', 'menuFid')
        } else {
          this.$message({
            message: data.msg,
            type: 'warning'
          })
        }
      })
    },
    getParentMenuName(name) {
      this.formInline.menuParentName = name
    },
    // 取消按钮
    closeDialog() {
      this.$emit('closeDialog')
    },
    // 确认按钮
    userSaveFn() {
      this.$refs.formInline.validate((valid) => {
        if (valid) {
          if (this.formInline.menuFid === '' || this.formInline.menuFid === null || this.formInline.menuFid === undefined) {
            this.formInline.menuFid = 0
            this.formInline.menuParentName = this.formInline.menuName
          }
          submitMenu(this.formInline).then((res) => {
            const data = res.data
            if (data.code === '200') {
              this.$message({
                message: this.dialogTitle + '菜单成功！',
                type: 'success'
              })
              this.$emit('sure', this.type)
            } else {
              this.$message({
                message: data.msg,
                type: 'warning'
              })
            }
          })
        } else {
          console.log('error submit!!')
          return false
        }
      })
    }
  }
}
</script>

<style lang="scss" type="text/css" scoped>
// .main {
//   width: 50%;
//   height: 40px;
//   display: inline-block;
//   line-height: 40px;
// }
.formRow {
  width: 100%;
  display: flex;
  ::v-deep .el-form-item {
    flex: 1;
    display: flex;
  }
  ::v-deep .el-form-item__content {
    flex: 1;
  }
  .form-detail-span {
    font-size: 15px;
    padding-left: 20px;
  }
}
</style>
