<template>
  <div class="main">
    <el-dialog v-dialogDrag :visible.sync="dialogShow" :modal="false" :close-on-click-modal="false" :before-close="closeDialog" class="personDialog">
      <template slot="title">
        <span class="dialog-title">中央空调<span>(运行中/设备总数)</span></span>
      </template>
      <div class="dialog-content">
        <div v-for="(item, index) in overviewData" :key="index" class="air_conditioner">
          <p>
            <span class="run_class">{{ item.runing }}</span> / {{ item.total }}
          </p>
          <p>{{ item.name }}</p>
        </div>
      </div>
    </el-dialog>
  </div>
</template>
<script>
export default {
  name: 'airOverview',
  props: {
    dialogShow: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      overviewData: [
        {
          name: '空调机组',
          runing: 3,
          total: 10
        },
        {
          name: '风机盘管',
          runing: 12,
          total: 140
        },
        {
          name: '冷冻水泵',
          runing: 3,
          total: 10
        },
        {
          name: '冷却水泵',
          runing: 3,
          total: 10
        },
        {
          name: '离心式冷水机组',
          runing: 3,
          total: 10
        }
      ]
    }
  },
  watch: {},
  mounted() {},
  methods: {
    // 取消按钮
    closeDialog() {
      this.$emit('configCloseDialog')
    }
  }
}
</script>

<style lang="scss" type="text/css" scoped>
::v-deep .el-dialog {
  min-width: 30%;
  width: fit-content;
  margin: 3rem 0 0 10rem;
  .el-dialog__body {
    padding: 1rem;
  }
}
.dialog-content {
  display: flex;
  flex-wrap: nowrap;
  padding: 0 15px 15px 15px;
  .air_conditioner {
    min-width: 120px;
    height: 70px;
    text-align: center;
    display: flex;
    flex-direction: column;
    justify-content: space-around;
    font-size: 13px;
    padding: 6px 0;
    .run_class {
      font-size: 29px;
      font-family: Roboto-Medium, Roboto;
      color: #ffe3a6;
    }
    p {
      font-size: 14px;
      font-family: HarmonyOS_Sans_SC_Light;
      color: #ffffff;
    }
  }
}
</style>
