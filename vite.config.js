/* eslint-disable */
import { defineConfig, loadEnv } from 'vite'
import legacyPlugin from '@vitejs/plugin-legacy';
// import * as path from 'path'
// import path from 'path-browserify'
import path from 'node:path'
import pkg from './package.json'
import dayjs from 'dayjs'
import fs from 'fs'
import { execSync } from 'child_process'
import createVitePlugins from './vitePlugins/index.js'
// @see https://cn.vitejs.dev/config/

// 获取git信息
function getGitInfo () {
  try {
    const gitBranch = execSync('git rev-parse --abbrev-ref HEAD').toString().trim()
    const gitCommit = execSync('git rev-parse HEAD').toString().trim()
    const gitLastCommitTime = execSync('git log -1 --format=%cd --date=format:"%Y-%m-%d %H:%M:%S"').toString().trim()
    const gitUser = execSync('git config user.name').toString().trim()

    return {
      GIT_BRANCH: gitBranch,
      GIT_COMMIT: gitCommit,
      GIT_LAST_COMMIT_TIME: gitLastCommitTime,
      GIT_USER: gitUser,
      BUILD_TIME: dayjs().format('YYYY-MM-DD HH:mm:ss')
    }
  } catch (e) {
    console.error('获取Git信息失败:', e)
    return {
      GIT_BRANCH: 'unknown',
      GIT_COMMIT: 'unknown',
      GIT_LAST_COMMIT_TIME: 'unknown',
      GIT_USER: 'unknown',
      BUILD_TIME: dayjs().format('YYYY-MM-DD HH:mm:ss')
    }
  }
}

export default async ({
  command,
  mode
}) => {
  // 加载环境变量，因为 vite 中不会加载以 VUE 开头的，我们得自己指定下
  const envPrefix = ['VUE']
  const env = loadEnv(mode, process.cwd(), envPrefix)
  let rollupOptions = {};

  // 获取Git信息
  const gitInfo = getGitInfo()
  const createVersionFile = () => {
    // 如果是构建模式，创建version.txt
    if (command !== 'build') return
    const versionContent = `
打包分支: ${gitInfo.GIT_BRANCH}
提交ID: ${gitInfo.GIT_COMMIT}
最后提交时间: ${gitInfo.GIT_LAST_COMMIT_TIME}
打包时间: ${gitInfo.BUILD_TIME}
打包用户: ${gitInfo.GIT_USER}
运行环境: ${mode}
`;

    // 确保目录存在
    const outDir = mode === 'production' ? 'dist' : `dist-${mode}`
    if (!fs.existsSync(outDir)) {
      fs.mkdirSync(outDir, { recursive: true })
    }

    // 写入版本信息文件
    fs.writeFileSync(path.join(outDir, 'version.txt'), versionContent)
    console.log('版本信息文件已生成:', path.join(outDir, 'version.txt'))

  }

  let optimizeDeps = {
    include: ['vue', 'vuex', 'sass', 'vue-router']
  };


  let alias = {
    'public': path.resolve(__dirname, './public'),
    'src': path.resolve(__dirname, './src'),
    '@': path.resolve(__dirname, 'src'),
    '~@': path.resolve(__dirname, 'src'),
    '@assets': path.resolve(__dirname, './src/assets'),
    '@components': path.resolve(__dirname, './src/components'),
    '@views': path.resolve(__dirname, './src/views'),
    '@store': path.resolve(__dirname, './src/store'),
    vue: 'vue/dist/vue.esm.js' //解决el-table 生产环境不显示
  }

  let proxy = {}

  let define = {
    'process.env.VITE': true,
    // 定义版本信息常量
    'GIT_BRANCH': JSON.stringify(gitInfo.GIT_BRANCH),
    'GIT_COMMIT': JSON.stringify(gitInfo.GIT_COMMIT),
    'GIT_LAST_COMMIT_TIME': JSON.stringify(gitInfo.GIT_LAST_COMMIT_TIME),
    'GIT_USER': JSON.stringify(gitInfo.GIT_USER),
    'BUILD_TIME': JSON.stringify(gitInfo.BUILD_TIME),
    'NODE_ENV': JSON.stringify(mode),
    __SYSTEM_INFO__: JSON.stringify({
      pkg: {
        version: pkg.version,
        dependencies: pkg.dependencies,
        devDependencies: pkg.devDependencies
      },
      lastBuildTime: dayjs().format('YYYY-MM-DD HH:mm:ss')
    })
  }
  for (const [key, value] of Object.entries(env)) {
    define[`process.env.${key}`] = `"${value}"`
  }

  // todo 替换为原有变量
  // let define = {
  //   'process.env.NODE_ENV': command === 'serve' ? '"development"' : '"production"',
  // }

  let esbuild = {

  }

  return defineConfig({
    base: './', // index.html文件所在位置
    // root: './', // js导入的资源路径，src
    resolve: {
      alias,
      // 忽略后缀名
      extensions: ['.js', '.jsx', '.ts', '.tsx', '.json', '.vue', '.json', '.css', '.scss']
    },
    define: define,
    server: {
      open: false,
      host: "0.0.0.0",
      port: 1226,
      // fs: { // 不对根目录访问做限制
      //   strict: false,
      // },
      // 代理
      proxy,
    },
    build: {
      rollupOptions,
      outDir: mode === 'production' ? 'dist' : `dist-${mode}`,
      sourcemap: false
    },
    esbuild,
    optimizeDeps,
    plugins: [
      ...createVitePlugins(env, command === 'build'),
      // 自定义插件，生成版本信息
      {
        name: 'vite-plugin-version-info',
        closeBundle () {
          createVersionFile()
        }
      }
    ],
    // plugins: [
    //   vue(), //配置vue
    //   viteCommonjs(),
    //   vitePluginRequire(), //兼容require
    //   createVuePlugin(), legacyPlugin({
    //     targets: ['Android > 39', 'Chrome >= 60', 'Safari >= 10.1', 'iOS >= 10.3', 'Firefox >= 54', 'Edge >= 15'],
    //   }),
    // ],
    css: {
      preprocessorOptions: {
        scss: {
          additionalData: `@import "@/assets/sino-ui/common/var.scss";`
        }
      }
    }
  });
};
