<template>
  <!-- 字典管理 -->
  <div class="content special_box">
    <div class="top_content" style="margin-bottom: 20px">
      <el-input
        style="width: 217px; margin-right: 20px"
        placeholder="请输入字典名称"
        v-model="searchDataObj.dictName"
        maxlength="25"
        @keyup.enter.native="_searchByCondition"
        onkeyup="if(value.length>25)value=value.slice(0,25)"
      ></el-input>
      <el-button class="sino-button-sure" style="margin-left: 30px" @click="_searchByCondition">查询</el-button>
      <el-button class="sino-button-sure" @click="_resetCondition">重置</el-button>
      <el-button class="sino-button-sure addBtn" @click="selectRowData({}, 'add')"
        ><div class="img-add-icon"></div>
        新增</el-button
      >
      <el-button class="sino-button-sure addConfigBtn" v-show="configShow" @click="selectConfigRowData({ dictId: selectDictId }, 'add')"
        ><div class="img-add-icon"></div>
        添加键值</el-button
      >
    </div>
    <div class="table_list">
      <div class="dictTable">
        <el-table
          :data="tableData"
          height="calc(100% - 40px)"
          :cell-style="$tools.setCell(3)"
          :header-cell-style="$tools.setHeaderCell(3)"
          style="width: 100%"
          highlight-current-row
          v-loading="tableLoading"
          element-loading-background="rgba(0, 0, 0, 0.2)"
        >
          <el-table-column fixed type="index" width="80" show-overflow-tooltip label="序号"></el-table-column>
          <el-table-column fixed prop="dictName" show-overflow-tooltip label="字典名称"></el-table-column>
          <el-table-column fixed prop="dictCode" show-overflow-tooltip label="字典编码"></el-table-column>
          <el-table-column fixed prop="description" show-overflow-tooltip label="描述"></el-table-column>
          <el-table-column fixed show-overflow-tooltip label="操作">
            <template slot-scope="scope">
              <div class="operationBtn">
                <span @click="selectRowData(scope.row, 'edit')">编辑</span>
                <span @click="selectRowData(scope.row, 'del')" style="margin: 0 10px">删除</span>
                <span @click="selectRowData(scope.row, 'dispose')">配置</span>
              </div>
            </template>
          </el-table-column>
        </el-table>
        <el-pagination
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="currentPage"
          :page-sizes="[15, 20, 30, 40]"
          :page-size="pageSize"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
          class="pagination"
        ></el-pagination>
      </div>
      <div v-show="false" class="dictDetailTable">
        <span class="configListSpan"
          >键值列表&nbsp;&nbsp;&nbsp;所属字典：<span>{{ selectDictName }}</span></span
        >
        <el-table
          :data="tableConfigData"
          height="calc(100% - 40px)"
          :cell-style="$tools.setCell(3)"
          :header-cell-style="$tools.setHeaderCell(3)"
          style="width: 100%"
          v-loading="tableLoading"
          element-loading-background="rgba(0, 0, 0, 0.2)"
        >
          <el-table-column fixed prop="labelName" show-overflow-tooltip label="标签"></el-table-column>
          <el-table-column fixed prop="dictValue" show-overflow-tooltip label="键值"></el-table-column>
          <el-table-column fixed prop="sort" show-overflow-tooltip label="排序"></el-table-column>
          <el-table-column fixed prop="description" show-overflow-tooltip label="描述"></el-table-column>
          <el-table-column fixed show-overflow-tooltip label="操作">
            <template slot-scope="scope">
              <div class="operationBtn">
                <span @click="selectConfigRowData(scope.row, 'edit')">编辑</span>
                <span @click="selectConfigRowData(scope.row, 'del')" style="margin: 0 10px">删除</span>
              </div>
            </template>
          </el-table-column>
        </el-table>
        <!-- <el-pagination
          @size-change="handleSizeChangeConfig"
          @current-change="handleCurrentChangeConfig"
          :current-page="currentPageConfig"
          :page-sizes="[15, 20, 30, 40]"
          :page-size="pageSizeConfig"
          layout="total, sizes, prev, pager, next, jumper"
          :total="totalConfig"
          class="pagination"
        ></el-pagination> -->
      </div>
    </div>
    <!-- dictDetailTable -->
    <!-- 新增字典弹框 -->
    <template v-if="addDialogShow">
      <addDict ref="addDict" :dialogTitle="dialogTitle" :rowData="rowData" :type="type" :addDialogShow="addDialogShow" @sure="sure" @closeDialog="closeDialog"></addDict>
    </template>
    <!-- 新增字典配置弹框 -->
    <template v-if="addConfigDialogShow">
      <addDictConfig
        ref="addDict"
        :dialogTitle="configDialogTitle"
        :rowData="configRowData"
        :type="configType"
        :addDialogShow="addConfigDialogShow"
        @configSure="configSure"
        @configCloseDialog="configCloseDialog"
      ></addDictConfig>
    </template>
  </div>
</template>
<script type="text/ecmascript-6">
import $ from 'jquery'
import addDict from './dialogComponents/addDict'
import addDictConfig from './dialogComponents/addDictConfig'
import { getDictPage, getDictConfigPage, delDict, delDictConfig, getDictDataList } from '@/utils/api'
export default {
  name: 'dicManagement',
  components: {
    addDict,
    addDictConfig
  },
  data() {
    return {
      tableData: [],
      currentPage: 1,
      pageSize: 15,
      total: 0,
      tableConfigData: [],
      // currentPageConfig: 1,
      // pageSizeConfig: 15,
      // totalConfig: 0,
      selectDictId: '',
      selectDictName: '',
      tableLoading: false, // 字典列表loading
      searchDataObj: {
        dictName: ''
      },
      addDialogShow: false, // 新增字典弹窗
      dialogTitle: '',
      rowData: {},
      type: '',
      addConfigDialogShow: false, // 新增字典弹窗
      configDialogTitle: '',
      configRowData: {},
      configType: '',
      dialogType: {
        add: '新建',
        edit: '修改',
        detail: '查看'
      },
      configShow: false
    }
  },
  mounted() {
    this._resetCondition()
  },
  methods: {
    // 选中列表行数据
    selectRowData(row, type) {
      if (type === 'del') {
        this.dictDelFn(row.id)
      } else if (type === 'dispose') {
        this.selectDictId = row.id
        this.selectDictName = row.dictName
        this.changeTableShow(true)
        this.getDictConfigPage(this.selectDictId)
      } else {
        this.addDialogShow = true
        this.rowData = row
        this.type = type
        this.dialogTitle = this.dialogType[type] + '字典'
      }
    },
    // 选中配置行数据
    selectConfigRowData(row, type) {
      if (type === 'del') {
        this.dictCongifDelFn(row.id)
      } else {
        this.addConfigDialogShow = true
        this.configRowData = row
        this.configType = type
        this.configDialogTitle = this.dialogType[type] + '标签'
      }
    },
    changeTableShow(flag) {
      this.configShow = flag
      if (flag) {
        $('.dictTable').addClass('width49')
        $('.addBtn').addClass('btnTransform')
        setTimeout(function () {
          $('.dictDetailTable').fadeIn(500)
        }, 500)
      } else {
        $('.dictDetailTable').fadeOut(500)
        setTimeout(function () {
          $('.dictTable').removeClass('width49')
          $('.addBtn').removeClass('btnTransform')
        }, 500)
      }
    },
    getDictConfigPage(id) {
      getDictConfigPage(id).then((res) => {
        const data = res.data
        this.tableLoading = false
        if (data.code === '200') {
          this.tableConfigData = data.data
        } else {
          this.$message({
            message: data.msg,
            type: 'warning'
          })
        }
      })
    },
    // 查询
    _searchByCondition() {
      this.currentPage = 1
      this.dictTableList()
    },
    // 重置
    _resetCondition() {
      this.searchDataObj = {
        dictName: ''
      }
      this.currentPage = 1
      this.pageSize = 15
      this.total = 0
      this.dictTableList()
    },
    // 获取字典列表
    dictTableList() {
      const params = JSON.parse(JSON.stringify(this.searchDataObj))
      Object.assign(params, {
        page: this.currentPage,
        pageSize: this.pageSize
      })
      this.tableLoading = true
      getDictPage(params).then((res) => {
        // console.log(res)
        const data = res.data
        this.tableLoading = false
        if (data.code === '200') {
          this.tableData = data.data.records
          this.total = data.data.total
        } else {
          this.$message({
            message: data.msg,
            type: 'warning'
          })
        }
      })
      this.changeTableShow(false)
      this.tableConfigData = []
      this.selectDictId = ''
    },
    // 删除字典
    dictDelFn(id) {
      this.$confirm('删除后将无法恢复，确认要删除该记录吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        cancelButtonClass: 'sino-button-sure',
        confirmButtonClass: 'sino-button-sure',
        customClass: 'confirm-box-class',
        type: 'warning'
      })
        .then(() => {
          this.tableLoading = true
          delDict(id).then((res) => {
            const data = res.data
            this.tableLoading = false
            if (data.code === '200') {
              this.$message({
                type: 'success',
                message: '删除成功！'
              })
              this.currentPage = this.currentPage === 1 ? 1 : this.currentPage
              this.dictTableList()
              this.setDictStorage()
            } else {
              this.$message({
                type: 'warning',
                message: data.msg
              })
            }
          })
        })
        .catch(() => {})
    },
    // 删除字典配置
    dictCongifDelFn(id) {
      this.$confirm('删除后将无法恢复，确认要删除该记录吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        cancelButtonClass: 'sino-button-sure',
        confirmButtonClass: 'sino-button-sure',
        customClass: 'confirm-box-class',
        type: 'warning'
      })
        .then(() => {
          this.tableLoading = true
          delDictConfig(id).then((res) => {
            const data = res.data
            this.tableLoading = false
            if (data.code === '200') {
              this.$message({
                type: 'success',
                message: '删除成功！'
              })
              this.currentPage = this.currentPage === 1 ? 1 : this.currentPage
              this.getDictConfigPage(this.selectDictId)
              this.setDictStorage()
            } else {
              this.$message({
                type: 'warning',
                message: data.msg
              })
            }
          })
        })
        .catch(() => {})
    },
    handleSizeChange(val) {
      this.pageSize = val
      this.dictTableList()
    },
    handleCurrentChange(val) {
      this.currentPage = val
      this.dictTableList()
    },
    // handleSizeChangeConfig(val) {
    //   this.pageSizeConfig = val
    //   this.getDictConfigPage(this.selectDictId)
    // },
    // handleCurrentChangeConfig(val) {
    //   this.currentPageConfig = val
    //   this.getDictConfigPage(this.selectDictId)
    // },
    closeDialog() {
      this.addDialogShow = false
    },
    sure(type) {
      this.addDialogShow = false
      if (type === 'detail') {
        return false
      } else if (type === 'add') {
        this._resetCondition()
      } else {
        this.dictTableList()
      }
    },
    configCloseDialog() {
      this.addConfigDialogShow = false
    },
    configSure() {
      this.addConfigDialogShow = false
      this.getDictConfigPage(this.selectDictId)
      this.setDictStorage()
    },
    // 字典信息重新赋值
    setDictStorage() {
      getDictDataList({}).then((res) => {
        const data = res.data
        if (data.code === '200') {
          localStorage.setItem('dictList', JSON.stringify(data.data))
        }
      })
    }
  }
}
</script>
<style lang="scss" scoped>
.pagination {
  // bottom: 22px;
  // text-align: right;
}
.content {
  position: relative;
  width: 80%;
  height: 97%;
  background-color: #031553;
  box-sizing: border-box;
  padding: 20px 10px 10px 10px;
  margin: 10px 10px 10px 10%;
  .top_content {
    position: relative;
    .addBtn {
      position: absolute;
      right: 0;
      transition: right 0.8s;
    }
    .addConfigBtn {
      position: absolute;
      right: 0;
    }
  }

  .table_list {
    height: calc(100% - 60px);
    display: flex;
    justify-content: space-between;
    // overflow-y: scroll;
    .dictTable {
      width: 100%;
      transition: width 0.8s;
    }
    .dictDetailTable {
      // flex: 1;
      width: 49%;
      position: relative;
      .configListSpan {
        left: 1%;
        position: absolute;
        top: -25px;
        color: #fff;
        font-size: 15px;
        span {
          color: #ffe3a6;
        }
      }
      .configListSpan::before {
        content: '';
        display: inline-block;
        width: 2px;
        border-radius: 1px;
        height: 13px;
        background: #ffe3a6;
        margin-right: 10px;
      }
    }
  }
  .btnTransform {
    right: 51% !important;
    transition: right 0.8s;
  }
  .width49 {
    width: 49% !important;
    transition: width 0.8s;
  }
  .btnTransformOut {
    right: 0 !important;
    transition: right 0.8s;
  }
  .width100 {
    width: 100%;
    transition: width 0.8s;
  }
}
</style>
