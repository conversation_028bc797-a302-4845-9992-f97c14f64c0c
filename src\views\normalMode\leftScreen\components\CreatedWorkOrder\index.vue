<template>
  <el-dialog
    v-dialogDrag
    :modal="false"
    :title="(dealType === 'add' ? '新建' : '') + workTypeName + '工单' + (dealType === 'deal' ? '（ 处理 ）' : '') "
    custom-class="CreatedWorkOrder"
    width="60%"
    append-to-body
    :show-close="false"
    :visible.sync="workOrderDealShow"
    :close-on-click-modal="false"
    :before-close="closeDialog"
  >
    <div class="dialog-right">
      <div class="dialog-tc" @click="closeDialog"></div>
    </div>
    <div class="right-tips"><span class="v-img"></span>{{ workOrderDetail.taskWorkHint }}</div>
    <olgMaintenance
      v-if="workOrderDetail?.olgTaskManagement?.workTypeCode === '1' || workOrderDetail?.olgTaskManagement?.workTypeCode === '16'"
      ref="olgMaintenance"
      :projectCode="projectCode"
      :dealType="dealType"
      :alarmId="alarmId"
      :spaceId="spaceId"
      routerFrom="local"
      :workOrderDetail="workOrderDetail"
      @save="getSaveCallback"
    />
    <span slot="footer" class="dialog-footer">
      <el-button class="sino-button-sure" @click="closeDialog">取 消</el-button>
      <el-button class="sino-button-sure" @click="savePeople">确 定</el-button>
    </span>
  </el-dialog>
</template>
<script>
import {
  getWorkOrderOper,
  getWorkOrderToAdd,
  limWorkInfo
} from '@/utils/peaceLeftScreenApi'
import olgMaintenance from './components/olgMaintenance.vue'
export default {
  name: 'CreatedWorkOrder',
  components: {
    olgMaintenance
  },
  props: {
    workOrderDealShow: {
      type: Boolean,
      default: false
    },
    dealType: {
      type: String,
      default: 'add'
    },
    alarmId: {
      type: String,
      default: ''
    },
    spaceId: {
      type: String,
      default: ''
    },
    projectCode: {
      type: String,
      default: ''
    },
    workTypeCode: {
      type: String,
      default: ''
    },
    workTypeName: {
      type: String,
      default: ''
    },
    workTypeId: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      workOrderDetail: {}
    }
  },
  watch: {},
  created() {
    if (this.dealType === 'deal') {
      this.getWorkOrderOper()
    } else {
      this.getWorkOrderToAdd()
    }
  },
  mounted() {},
  methods: {
    getWorkOrderOper() {
      const params = {
        id: this.workTypeId,
        operType: 'placeOrder'
        // ...__PATH.USER_CODE
      }
      getWorkOrderOper(params).then((res) => {
        this.workOrderDetail = res.data
      })
    },
    getWorkOrderToAdd() {
      const params = {
        type: 1,
        workTypeCode: this.workTypeCode,
        workTypeName: this.getWorkTypeLetter(this.workTypeCode)
      }
      getWorkOrderToAdd(params).then((res) => {
        this.workOrderDetail = res.data
      })
    },
    getWorkTypeLetter(code) {
      switch (code) {
        case '1':
          return 'WX'
        case '16':
          return 'QJ'
      }
    },
    // 取消按钮
    closeDialog() {
      this.$emit('update:workOrderDealShow', !this.workOrderDealShow)
    },
    // 确认按钮
    savePeople() {
      this.$refs.olgMaintenance.saveForm()
    },
    getSaveCallback(item) {
      // 关联工单返回数据 保存关联报警
      if (item) {
        item.operationSource = 1; // 代表客户端操作
        limWorkInfo(item).then((res) => {
          console.log(res)
        })
      }
      this.$emit('workOrderSure', item)
    }
  }
}
</script>

<style lang="scss" scoped>
::v-deep .el-dialog{
  background: url('@/assets/images/qhdsys/big1-mask-bg.png') no-repeat center center / 100% 100%;
  .el-dialog__header {
    padding: 10px 20px 10px;
    text-align: center;
    .el-dialog__title {
      height: 20px;
      font-size: 18px;
      font-family: Source Han Sans CN-Regular, Source Han Sans CN;
      font-weight: 400;
      color: #cbdeed;
      line-height: 18px;
    }
  }
  .el-dialog__body {
    height: calc(78vh - 120px) !important;
    overflow-y: auto;
    display: flex;
  }
  .el-dialog__footer {
    padding-top: 0;
    padding-bottom: 40px;
    padding-right: 40px;
  }
}
.CreatedWorkOrder {
  .dialog-right {
    position: absolute;
    top: 17px;
    right: 60px;
    display: flex;
    .dialog-tc {
      width: 36px;
      height: 36px;
      background: url('@/assets/images/qhdsys/bg-icon-gb.png') no-repeat;
    }
    .dialog-tc:hover {
      cursor: pointer;
    }
  }
  .right-tips {
    position: absolute;
    left: 50%;
    transform: translateX(-50%);
    top: 79px;
    width: 50%;
    background: linear-gradient(90deg, rgba(255, 36, 36, 0) 0%, rgba(255, 43, 43, 0.4) 50%, rgba(255, 49, 49, 0) 100%);
    height: 30px;
    line-height: 30px;
    text-align: center;
    color: #fff;
    display: flex;
    justify-content: center;
    .v-img {
      margin: auto 0;
      margin-right: 10px;
      display: inline-block;
      height: 20px;
      width: 20px;
      background: center url('~@/assets/images/peace/volume.png') no-repeat;
      background-size: 100% 100%;
    }
  }
  .el-table-column--selection .cell {
    padding-left: 0.875rem;
    padding-right: 0.875rem;
    text-overflow: initial;
  }
}
</style>
