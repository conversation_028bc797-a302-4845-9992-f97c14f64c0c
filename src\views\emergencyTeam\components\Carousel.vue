<!-- 功能描述：公共相关案例展示模块 -->
<template>
  <div v-if="list.length">
    <div class="team-name">
      <span>{{list.length?list[0].teamName:''}}</span>
    </div>
    <div class="case-discuss" @mouseover="mouseoverCase" @mouseout="autoPlayCase" v-show="isCaseLength">
      <div class="case-discuss-content">
        <div :class="['case-discuss-cont' + id, 'case-discuss-cont']" :style="'width: ' + list.length * 320 + 'px'">
          <div class="case-discuss-con" v-for="(item, index) in list" :key="index" @click="clickCase(item)">
            <div class="case-img">
              <img v-if="item.picture" :src="item.picture" />
              <img v-else src="../../../assets/images/war/no-people.png" />
            </div>
            <div class="case-tit">
              <p class="case-con">{{ item.name }}</p>
              <p class="case-con">{{ item.postName }}</p>
              <p class="case-con">{{ item.mobile }}</p>
            </div>
          </div>
        </div>
      </div>
      <!-- <div class="case-control-l case-control-btn" v-if="controlBtnStatus" @click="lastCase">◀</div> -->
      <i v-if="list.length > 5" class="el-icon-arrow-left case-control-l case-control-btn" @click="lastCase"></i>
      <i v-if="list.length > 5" class="el-icon-arrow-right case-control-r case-control-btn" @click="nextCase"></i>
      <!-- <div class="case-control-r case-control-btn" v-if="controlBtnStatus" @click="nextCase">▶</div> -->
    </div>
  </div>
</template>
<script type="text/babel">
import $ from 'jquery'
export default {
  data() {
    return {
      // cases: otherCase,
      timerCase: null,
      selectedCase: 0,
      controlBtnStatus: true,
      currentCase: [
        {
          // img: 'https://gimg2.baidu.com/image_search/src=http%3A%2F%2Finews.gtimg.com%2Fnewsapp_bt%2F0%2F14025803737%2F641.jpg&refer=http%3A%2F%2Finews.gtimg.com&app=2002&size=f9999,10000&q=a80&n=0&g=0n&fmt=auto?sec=1650607122&t=76e24a27458d850f3cc566d6ccb2717d',
          tit: '12345678910',
          content: '电工',
          name: '张三'
        },
        {
          img: '',
          tit: '12345678910',
          content: '电工',
          name: '张三'
        },
        {
          // img: 'https://gimg2.baidu.com/image_search/src=http%3A%2F%2Fbkimg.cdn.bcebos.com%2Fpic%2F9f510fb30f2442a7d933e1267709ba4bd11373f0a454&refer=http%3A%2F%2Fbkimg.cdn.bcebos.com&app=2002&size=f9999,10000&q=a80&n=0&g=0n&fmt=auto?sec=1650607236&t=2f4a9f6ad227108aa883a76fd9446aa4',
          tit: '12345678910',
          content: '电工',
          name: '张三'
        },
        {
          // img: 'https://gimg2.baidu.com/image_search/src=http%3A%2F%2Fnimg.ws.126.net%2F%3Furl%3Dhttp%253A%252F%252Fdingyue.ws.126.net%252F2021%252F0624%252Fef569cf5p00qv73sb0046c000gg00fwm.png%26thumbnail%3D650x2147483647%26quality%3D80%26type%3Djpg&refer=http%3A%2F%2Fnimg.ws.126.net&app=2002&size=f9999,10000&q=a80&n=0&g=0n&fmt=auto?sec=1650607281&t=b0506a92072a63f2a62292a66b870a7c',
          tit: '12345678910',
          content: '电工',
          name: '张三'
        }
      ],
      isCaseLength: true
    }
  },
  props: {
    caseId: {
      type: Number,
      default: null
    },
    id: {
      type: Number,
      default: null
    },
    list: {
      type: Array,
      default() {
        return []
      }
    }
  },
  mounted() {
    // console.log(this.list)
    const self = this
    // if (self.caseId == 10) {
    //   self.cases[19].caseId = 15;
    //   self.cases[20].caseId = 16;
    //   self.cases[21].caseId = 17;
    //   self.cases[22].caseId = 18;
    // }
    // if (self.caseId === 30) {
    //   self.cases[25].caseId = 35
    // }
    // if (self.caseId === 50) {
    //   self.cases[26].caseId = 53
    //   self.cases[22].caseId = 54
    // }

    // self.cases.forEach((item) => {
    //   if (parseInt(item.caseId / 10) === parseInt(self.caseId / 10)) {
    //     self.currentCase.push(item)
    //   }
    // })

    if (self.list.length > 5) {
      self.autoPlayCase()
    } else {
      self.controlBtnStatus = false
      // $('.case-discuss-content').css('width', 320 * self.list.length + 'px')
    }

    if (self.list.length > 0) {
      self.isCaseLength = true
    }
  },
  destroyed() {
    // 生命周期：实例被销毁之后出发
    clearInterval(this.timerCase)
  },
  methods: {
    autoPlayCase() {
      const self = this
      // 自动轮播
      this.timerCase = setInterval(function () {
        self.selectedCase++
        if (self.selectedCase > self.list.length - 5) {
          self.selectedCase = 0
        }
        $('.case-discuss-cont' + self.id + ':visible').animate({ left: -self.selectedCase * 245 }, 1500)
      }, 3500)
    },
    caseScroll() {
      // 上下页操作
      if (this.selectedCase < 0) {
        this.selectedCase = 0
      }
      if (this.selectedCase > this.list.length - 5) {
        this.selectedCase = this.list.length - 5
      }
      $('.case-discuss-cont' + this.id + ':visible').animate({ left: -this.selectedCase * 245 }, 1500)
    },
    mouseoverCase() {
      // 鼠标进入暂停轮播
      clearInterval(this.timerCase)
    },
    nextCase() {
      // 下一页
      this.selectedCase++
      this.caseScroll()
    },
    lastCase() {
      // 下一页
      this.selectedCase--
      this.caseScroll()
    },
    clickCase(item) {
      // if (window.location.href.indexOf('case') > -1) {
      //   this.$emit('change-case', item.caseId)
      // } else {
      //   this.$router.push({ name: 'case-caseId', params: { caseId: item.caseId } })
      // }
      this.$parent.personClick(item)
    },
    handleVisiable() {
      clearInterval(this.timerCase)
    }
  }
}
</script>
<style lang="scss" scoped>
.case-discuss {
  position: relative;
  padding: 8px 16px 8px 16px;
  color: #000;
  overflow: hidden !important;
}

.case-discuss-tit {
  font-size: 16px;
  text-align: center;
}

.case-discuss-content {
  overflow: hidden;
  position: relative;
  width: 1150px;
  height: 160px;
  // padding-left: 16px;
  // box-sizing: border-box;
  // margin: 0 auto;
}

.case-discuss-cont {
  position: absolute;
  top: 0;
  left: 0;
  display: flex;
}

.case-discuss-con {
  display: flex;
  box-sizing: border-box;
  width: 230px;
  padding: 0 20px;
  cursor: pointer;
  border: 1px solid #3056a2;
  margin: 0 8px;
  padding: 14px;
}

.case-discuss-con:hover {
  border: 1px solid rgba(255, 227, 166, 0.6);
}
.case-img {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 320px;
  height: 180px;
  // margin: 30px 0;

  img {
    width: 100%;
    height: 100%;
  }
}

.case-tit {
  text-align: center;
  font-size: 14px;
  margin-left: 12px;
  color: rgba(255, 255, 255, 0.9);
}

.case-tit > p:nth-child(1) {
  margin-top: 3px;
  margin-bottom: 12px;
  letter-spacing: 1px;
  color: #fff;
}
.case-tit > p:nth-child(2) {
  font-size: 12px;
}
.case-tit > p:nth-child(3) {
  position: absolute;
  bottom: 20px;
  letter-spacing: 1px;
}
.case-con {
  text-align: left;
}

.case-control-btn {
  position: absolute;
  top: 50%;
  font-size: 24px;
  color: #5996f9;
  margin-top: (calc(-1 * (150px - 100px) / 2));
  cursor: pointer;

  &.case-control-l {
    left: 0px;
  }

  &.case-control-r {
    right: 0px;
  }
}
.case-control-btn:hover {
  color: #ffe3a6;
}
.case-img {
  width: 80px;
  height: 100px;
}
.left-box .team-name {
  text-align: left;
  padding: 5px 20px;
}
.team-name > span:before {
  display: inline-block;
  margin-right: 5px;
  transform: translateY(1px);
  content: '';
  width: 15px;
  height: 15px;
  background-size: 100% 100%;
  background-repeat: no-repeat;
  background-image: url(../../../assets/images/war/arrow.png);
}
</style>
