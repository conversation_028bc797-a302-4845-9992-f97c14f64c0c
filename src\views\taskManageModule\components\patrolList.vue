<template>
  <div class="list-index-view">
    <div class="view-header-box">
      <div class="header-left">
        <el-form ref="formRef" :model="searchForm" class="search-form" inline>
          <el-form-item prop="workName">
            <el-input v-model="searchForm.workName" placeholder="作业名称" size="small"></el-input>
          </el-form-item>
          <el-form-item prop="maintainDeptName">
            <el-input v-model="searchForm.maintainDeptName" placeholder="巡检部门" size="small"></el-input>
          </el-form-item>
          <!-- <el-form-item>
            <el-select v-model="searchForm.dept" placeholder="巡检部门" size="small">
              <el-option v-for="item in deptOptions" :key="item.value" :label="item.label" :value="item.value">
              </el-option>
            </el-select>
          </el-form-item> -->
          <el-form-item prop="maintainUserName">
            <el-input v-model="searchForm.maintainUserName" placeholder="巡检人员" size="small"></el-input>
          </el-form-item>
          <!-- <el-form-item>
            <el-select v-model="searchForm.state" placeholder="验收状态" size="small">
              <el-option v-for="item in stateOptions" :key="item.value" :label="item.label" :value="item.value">
              </el-option>
            </el-select>
          </el-form-item> -->
          <el-form-item prop="maintainDate">
            <el-date-picker v-model="searchForm.maintainDate" format="yyyy-MM-dd" value-format="yyyy-MM-dd"
                            popper-class="date-style" type="daterange" range-separator="至" start-placeholder="巡查日期"
                            end-placeholder="巡查日期">
            </el-date-picker>
          </el-form-item>
        </el-form>
      </div>
      <div class="header-right">
        <el-button class="sino-button-sure" @click="resetForm">重置</el-button>
        <el-button class="sino-button-sure" @click="handleSearchForm">查询</el-button>
      </div>
    </div>
    <div class="view-content">
      <div class="table-view">
        <el-table ref="table" v-loading="tableLoading" :data="tableData" :resizable="false" height="calc(100%)"
                  :cell-style="$tools.setCell(3)" :header-cell-style="$tools.setHeaderCell(3)" stripe
                  @selection-change="handleSelectionChange">
          <el-table-column type="selection" width="55" align="center" class-name="patrolList__cell--selection">
          </el-table-column>
          <el-table-column label="序号" type="index" width="85" />
          <el-table-column prop="workName" label="作业名称" show-overflow-tooltip></el-table-column>
          <el-table-column prop="maintainDate" label="巡查日期" show-overflow-tooltip></el-table-column>
          <el-table-column prop="maintainDeptName" label="巡检部门" show-overflow-tooltip></el-table-column>
          <el-table-column prop="maintainUserName" label="巡检人员" show-overflow-tooltip></el-table-column>
          <!-- <el-table-column prop="acceptance" label="验收状态" show-overflow-tooltip>
            <template slot-scope="scope">
              <div class="status-box">
                <img class="table-icon" :src='icon_6' />
                <span style="color:#D4DEEC">{{scope.row.acceptance}}</span>
              </div>
            </template>
          </el-table-column> -->
          <el-table-column label="操作" width="100px">
            <template slot-scope="scope">
              <span class="operation-span" @click="toDetail(scope.row)">查看</span>
            </template>
          </el-table-column>
        </el-table>
        <el-pagination
          :current-page="currentPage"
          :page-sizes="[15, 20, 30, 40]"
          :page-size="pageSize"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total" class="pagination"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        ></el-pagination>
      </div>
    </div>
  </div>
</template>
<script>
import icon_5 from '@/assets/images/icon-5.png'
import icon_6 from '@/assets/images/icon-6.png'
import { getConstructionInspectionPageList } from '@/utils/spaceManage'
export default {
  name: 'leaseList',
  components: {
  },
  data() {
    return {
      icon_5,
      icon_6,
      tableLoading: false,
      searchForm: {
        workName: '', // 作业名称
        maintainDeptName: '', // 巡检部门
        maintainUserName: '', // 巡检用户名称
        maintainDate: [] // 巡查日期
      },
      deptOptions: [],
      stateOptions: [],
      navIdx: 0,
      currentPage: 1,
      pageSize: 15,
      total: 0,
      tableData: [],
      multipleSelection: []
    }
  },
  mounted() {
    this.getDataList()
  },
  methods: {
    getDataList() {
      this.tableLoading = true
      const params = {
        currentPage: this.currentPage,
        size: this.pageSize,
        ...this.searchForm
      }
      params.startTime = params.date ? params.maintainDate[0] : ''
      params.endTime = params.date ? params.maintainDate[1] : ''
      delete params.maintainDate
      getConstructionInspectionPageList(params).then((res) => {
        this.tableLoading = false
        if (res.data.code == '200') {
          this.total = res.data.data.count
          this.tableData = res.data.data.list
        }
      })
    },
    /** 重置 */
    resetForm() {
      this.$refs.formRef.resetFields()
      this.getDataList()
    },
    /** 查询 */
    handleSearchForm() {
      this.getDataList()
    },
    /** 条数变化事件 */
    handleSizeChange(val) {
      this.pageSize = val
      this.getDataList()
    },
    /** 页数变化事件 */
    handleCurrentChange(val) {
      this.currentPage = val
      this.getDataList()
    },
    /** 多选table */
    handleSelectionChange(val) {
      this.multipleSelection = val
    },
    toDetail(item) {
      console.log(66666, item)
      this.$emit('openDetailComponent', {
        key: 'patrolDetail',
        item
      })
    }
  }
}
</script>
<style lang="scss" scoped>
@import "../../../assets/sino-ui/common/var.scss";
.list-index-view {
  position: relative;
  height: 100%;
  width: 100%;
  padding: 0px 40px;
}
.view-header-box {
  display: flex;
  justify-content: space-between;
}
.view-content {
  height: calc(100% - 4rem);
  .table-view {
    height: calc(100% - 80px) !important;
  }
}
.operation-span {
  cursor: pointer;
  color: #8bddf5;
}
.operation-span:nth-child(2) {
  margin: 0 10px;
  color: rgba(133, 145, 206, 0.5);
}

::v-deep
  .el-table--striped
  .el-table__body
  tr.el-table__row--striped
  td.el-table__cell {
  background: none;
}
.table-icon {
  width: 16px;
  margin-right: 3px;
}
.status-box {
  display: flex;
  align-items: center;
}

::v-deep.el-form-item .el-form-item__content {
  line-height: 34px !important;
  height: 34px;
}
::v-deep .el-form-item .el-date-editor .el-range-input {
  height: 30px !important;
}
::v-deep .el-form-item .el-date-editor .el-input__icon {
  line-height: 26px;
}
.search-form {
  height: 34px;
  line-height: 34px;
  margin-bottom: 20px;
  width: 100%;
  ::v-deep .el-input__inner {
    background: center;
    border: 1px solid $input-border-color;
    border-radius: 0;
    color: $color;
    height: inherit;
    font-family: PingFangSC-Medium, PingFang SC;
  }

  ::v-deep .el-range-input {
    background: center;
    color: $color;
    font-size: 12px !important;
  }

  ::v-deep .el-range-separator,
  ::v-deep .el-range__icon {
    color: #3769be;
    line-height: 26px !important;
  }
}
</style>
