<template>
  <div class='component EmergencySummary'>
    <el-form class="EmergencySummary__form" ref="formRef" :model="formModel" :rules="rules" label-width="100px"
      label-suffix=":">
      <div class="EmergencySummary__wrapper EmergencySummary__wrapper--upload">
        <el-form-item label="总结文件" prop="files">
          <FileUploadCard :file-data.sync="formModel.files" :alarmId="alarmId" />
        </el-form-item>
      </div>

      <div class="EmergencySummary__wrapper">
        <el-form-item label="总结说明" prop="summaryAnalysis">
          <el-input type="textarea" v-model="formModel.summaryAnalysis" :maxlength="500" :rows="4" show-word-limit
            resize="none" placeholder="请输入研判说明"></el-input>
        </el-form-item>
        <el-form-item label="院内参与人员" prop="hospitalPerson">
          <EmergencyUserSelect multiple v-model="formModel.hospitalPerson" removable placeholder="请选择院内参与人员" />
        </el-form-item>
        <el-form-item label="其他参与人员" prop="otherPerson">
          <el-input v-model="formModel.otherPerson" placeholder="请输入其他参与人员"></el-input>
        </el-form-item>
      </div>

      <div class="EmergencySummary__wrapper">
        <el-form-item label="研判处理人" prop="createName">
          <EmergencyUserSelect v-model="formModel.createName" placeholder="请选择研判处理人" />
        </el-form-item>
      </div>
    </el-form>
  </div>
</template>


<script>
import TextTagArea from "./TextTagArea.vue";
import FileUploadCard from "./FileUploadCard.vue"
import EmergencyUserSelect from "./EmergencyUserSelect.vue";

export default {
  name: 'EmergencySummary',
  components: {
    TextTagArea,
    FileUploadCard,
    EmergencyUserSelect
  },
  props: {
    alarmId: String
  },
  data() {
    return {
      formModel: {
        files: [],
        summaryAnalysis: '',
        hospitalPerson: [],
        otherPerson: '',
        createName: [],
      },
      rules: {

      },
    }
  },
  computed: {
    imageUrls: function () {
      return this.formModel.files
        .filter(x => {
          const extension = x.name.substring(x.name.lastIndexOf("."));
          return '.png,.jpg'.includes(extension);
        })
        .map(x => x.url)
    }
  },
  mounted() {
    const userInfo = this.$store.state.loginInfo.user;
    this.formModel.createName = [
      {
        id: userInfo.id,
        staffNumber: userInfo.staffNumber,
        staffName: userInfo.staffName
      }
    ]
  },
  methods: {
    validate() {
      return this.$refs.formRef.validate().then(() => {
        return {
          createName: this.formModel.createName.map(this.userFormatter).join(),
          fileUrl: JSON.stringify(this.formModel.files),
          hospitalPerson: this.formModel.hospitalPerson.map(this.userFormatter).join(),
          otherPerson: this.formModel.otherPerson,
          summaryAnalysis: this.formModel.summaryAnalysis,
        }
      })
    },
    userFormatter(user) {
      let { staffNumber, staffName } = user
      if (staffNumber) {
        staffName += `(${staffNumber})`
      }
      return staffName
    }
  }
}


</script>

<style lang='scss' scoped>
.EmergencySummary {
  height: 100%;

  &__form {
    display: flex;
    flex-flow: column nowrap;
    height: 100%;
    overflow: hidden;
  }

  &__wrapper {
    padding: 16px 16px 1px;
    background: rgba(133, 145, 206, 0.15);

    +.EmergencySummary__wrapper {
      margin-top: 16px;
    }

    &--upload {
      flex: 1;
      overflow: hidden;

      .el-form-item {
        height: calc(100% - 16px);

        ::v-deep .el-form-item__content {
          height: 100%;
        }
      }
    }
  }
}
</style>
