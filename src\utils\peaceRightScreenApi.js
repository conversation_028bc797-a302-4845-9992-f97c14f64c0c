/*
 * @Author: <PERSON>
 * @Date: 2021-12-14 15:39:39
 * @Last Modified by: mikey.z<PERSON>peng
 * @Last Modified time: 2022-03-15 10:42:07
 */
import qs from 'qs'
import http from './http'

const spaceApi = __PATH.VUE_APP_SPACE_API
const planApi = __PATH.VUE_APP_RESERVE_PLAN_API
const iomsApi = __PATH.VUE_APP_IOMS_API
const idpsApi = __PATH.VUE_APP_IDPS_API
const iemsApi = __PATH.VUE_APP_IEMS_API
const iemcApi = __PATH.VUE_APP_IEMC_API

// const adminInfo = {
//   unitCode: 'BJSYYGLJ',
//   hospitalCode: 'BJSJTY'
// }
export function getComprehensiveWorkOrderInfo(params) {
  return http.postParamsQS(`${iomsApi}/iHCRSStatisticsController/getComprehensiveWorkOrderInfo`, params)
}
export function getDynamicWorkOrderList() {
  return http.postParamsQS(`${iomsApi}/iHCRSStatisticsController/getDynamicWorkOrderList`, {})
}
export function getTodayTaskAnalysis(params) {
  return http.postParamsQS(`${idpsApi}/planTaskNew/tasklistData`, params)
}
export function getAllFieldWorkOrderList(params) {
  return http.postParamsQS(`${iomsApi}/iHCRSStatisticsController/getWorkOrderList`, params)
}
export function getWorkOrderDetail(params) {
  return http.postParamsQS(`${iomsApi}/iHCRSStatisticsController/getWorkOrderDetail`, params)
}
export function urgeWorkOder(params) {
  return http.postParamsQS(`${iomsApi}/appOlgTaskManagement.do?urgeOderByWeChat`, params)
}
export function getAllOffice() {
  return http.postParamsQS(`${iomsApi}/iHCRSStatisticsController/getAllOffice`, {})
}
export function getServiceCompany() {
  return http.postParamsQS(`${iomsApi}/iHCRSStatisticsController/getOutsourcingCompanyInfo`, {})
}
export function getDataByTypeTeam(params) {
  return http.postParamsQS(`${iomsApi}/iHCRSStatisticsController/getOCTeamInfo`, params)
}
export function getServicePersonName(params) {
  return http.postParamsQS(`${iomsApi}/iHCRSStatisticsController/getOCTeamMemberInfo`, params)
}
export function getObtainAssignee() {
  return http.postParamsQS(`${iomsApi}/customReportsController/obtainAssignee`, {})
}
export function getCallerRetrieve() {
  return http.postParamsQS(`${iomsApi}/iHCRSOperOrderController/callerRetrieve`, {})
}
export function getItemTreeData(params) {
  return http.postParamsQS(`${iomsApi}/iHCRSStatisticsController/bootstrapTreeData`, params)
}
export function getWorkOrderDataList(params) {
  return http.postParamsQS(
    `${iomsApi}/iHCRSStatisticsController/data?${qs.stringify({
      ...params
    })}`
  )
}
export function getTeamAllDutyPersonnel(params) {
  return http.requestPost(`${planApi}/personnelManagementExternal/queryTeamAllDutyPersonnelInfo`, params, {}, 'plan')
}
export function getTeamsByWorkTypeCode(params) {
  return http.postParamsQS(`${iomsApi}/iHCRSOperOrderController/getTeamsByWorkTypeCode`, params)
}
export function getIomsDictList(params) {
  return http.postParamsQS(`${iomsApi}/iHCRSOperOrderController/getDictList`, params)
}
export function getfactMaterialTreeData(params) {
  return http.postParamsQS(`${iomsApi}/iHCRSOperOrderController/treeData`, params)
}
export function getConsumablesList(params) {
  return http.postParamsQS(`${iomsApi}/iHCRSOperOrderController/getConsumablesList`, params)
}
export function getMalfunctionReasonMethod(params) {
  return http.postParamsQS(`${iomsApi}/iHCRSOperOrderController/getTreeData`, params)
}
export function getCallCenterData(params) {
  return http.postParamsQS(`${iomsApi}/iHCRSOperOrderController/callCenterData`, params)
}
export function saveMalfunctionReasonMethod(params) {
  return http.postParamsQS(`${iomsApi}/appDisOlgTask.do?saveMalfunctionReasonMethod`, params)
}
export function getDesignPersonByDept(params) {
  return http.postParamsQS(`${iomsApi}/iHCRSOperOrderController/designPerson`, params)
}
// 获取工单oper
export function getWorkOrderOper(params) {
  return http.post(
    `${iomsApi}/iHCRSOperOrderController/oper?${qs.stringify({
      ...params
    })}`
  )
}
// 获取工单add
export function getWorkOrderToAdd(params) {
  return http.post(
    `${iomsApi}/iHCRSOperOrderController/toAdd?${qs.stringify({
      ...params
    })}`
  )
}
// 获取应急联系人
export function getEmergencyBook(params) {
  return http.postParamsQS(`${iomsApi}/iHCRSOperOrderController/getEmergencyBook`, params)
}
// 获取服务地点
export function getAssociationList(params) {
  return http.postParamsQS(`${iomsApi}/iHCRSOperOrderController/associationList`, params)
}
// 获取服务地点树
export function getGridTreeData(params) {
  return http.postParamsQS(`${iomsApi}/iHCRSStatisticsController/getNewAllSpaceInfo`, params)
}
// 获取工单类型列表
export function getWorkOrderConfigList(params) {
  return http.postParamsQS(`${iomsApi}/workOrderConfigController/getWorkOrderConfigList`, params)
}
// 一站式工单 详情提交
export function workOrderOperOrder(params) {
  return http.post(`${iomsApi}/iHCRSOperOrderController/operOrder`, params)
}
// 一站式工单 未处理工单提交
export function placeAndCancelOrder(params) {
  return http.postParamsQS(`${iomsApi}/iHCRSOperOrderController/placeAndCancelOrder`, params)
}
// 一站式工单 转派提交
export function toTeamsChangeTask(params) {
  return http.postParamsQS(`${iomsApi}/iHCRSOperOrderController/toTeamsChangeTask`, params)
}
// 一站式工单 督办提交
export function addFeedback(params) {
  return http.postParamsQS(`${iomsApi}/iHCRSOperOrderController/addFeedback`, params)
}
// 一站式工单 评价提交
export function updEvaluationOk(params) {
  return http.postParamsQS(`${iomsApi}/iHCRSOperOrderController/updEvaluationOk`, params)
}
// 一站式工单 修改提交
export function updateTask(params) {
  return http.postParamsQS(`${iomsApi}/iHCRSOperOrderController/updateTask`, params)
}
// 一站式工单 创建工单提交
export function placeAndCancelSave(params) {
  return http.postParamsQS(`${iomsApi}/iHCRSOperOrderController/save`, params)
}
// 一站式工单 完工上传文件
export function uploadFiles(params) {
  return http.post(`${iomsApi}/minio/upload`, params)
}
// 资产统计
export function getMaintenance(params) {
  return http.getQueryQS(`${iemsApi}/assetsRenewalRecord/maintenance`, params, {}, 'imesCode')
}
// 值班初始化查询班次
export function getShiftRecord(params) {
  return http.requestPost(`${iemcApi}/duty/getShiftRecord`, {
    ...params
  })
}
// 值班打卡
export function signIn(params) {
  return http.requestPost(`${iemcApi}/duty/signIn`, {
    ...params
  })
}
// 获取签到人员
export function getStaffList(params) {
  return http.requestPost(`${iemcApi}/staffManage/getStaffList`, {
    ...params
  })
}
// 保存交接班
export function saveDutyRota(params) {
  return http.requestPost(`${iemcApi}/duty/saveDutyRota`, {
    ...params
  })
}
// 用户信息列表
export function selectByList(params) {
  return http.requestPost(`${spaceApi}/postManager/post-manager/selectByList`, {
    ...params
  })
}
// 用户信息列表
export function getSelectedDept(params) {
  return http.getRequest(`${spaceApi}/departmentManager/department-manager/getSelectedDept`, {
    ...params
  })
}
// 用户信息列表
export function getSelected(params) {
  return http.getRequest(`${spaceApi}/unitManager/unit-manager/getSelected`, {
    ...params
  })
}
// 用户信息列表
export function staffList(params) {
  return http.postParamsRequest(`${spaceApi}/hospitalStaff/hospital-staff/list`, {
    ...params
  })
}
