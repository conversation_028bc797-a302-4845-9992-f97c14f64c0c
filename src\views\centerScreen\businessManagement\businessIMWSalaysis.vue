<template>
  <div class="content">
    <div class="title">
      <div @click="backToWPFhome()"><i class="el-icon-arrow-left"></i>返回</div>
    </div>
    <div class="content-left">
      <div class="left-top">
        <div class="bg-title">
          <span>科室医废类型分析</span><span class="center-legend"></span
          ><span class="right-search"
            ><span @click="activeTop5Event('#top5Week', 'week')" id="top5Week">本周</span><i>|</i><span @click="activeTop5Event('#top5Month', 'month')" id="top5Month">本月</span><i>|</i
            ><span id="top5Year" @click="activeTop5Event('#top5Year', 'year')">本年</span></span
          >
        </div>
        <div class="bg-content">
          <div v-if="deptAllShow" class="center-center">暂无数据</div>
          <div v-else style="width: 100%; height: 100%">
            <div id="deptProduceEcharts"></div>
          </div>
        </div>
      </div>
      <div class="left-bottom">
        <div class="bg-title">
          <span>科室医废重量分析</span><span class="center-legend"></span
          ><span class="right-search"
            ><span @click="activeAnalysisEvent('#deptWeek', 'week')" id="deptWeek">本周</span><i>|</i><span @click="activeAnalysisEvent('#deptMonth', 'month')" id="deptMonth">本月</span><i>|</i
            ><span id="deptYear" @click="activeAnalysisEvent('#deptYear', 'year')">本年</span></span
          >
        </div>
        <div class="bg-content">
          <el-table
            class="table-center-transfer"
            :data="tableData"
            height="100%"
            :cell-style="$tools.setCell(3)"
            :header-cell-style="$tools.setHeaderCell(3)"
            @row-dblclick="officeAnalysisChange"
            style="width: 100%"
            element-loading-background="rgba(0, 0, 0, 0.2)"
            v-loading="tableLoading"
          >
            <el-table-column fixed prop="officeName" show-overflow-tooltip label="所属科室"></el-table-column>
            <el-table-column fixed prop="totalweigh" show-overflow-tooltip label="收集重量(kg)"></el-table-column>
            <el-table-column fixed prop="totalTags" show-overflow-tooltip label="收集数量(袋)"></el-table-column>
          </el-table>
        </div>
      </div>
    </div>
    <div class="content-right">
      <!-- <el-button class="sino-button-sure" @click="collectRecordChange">showDialog</el-button> -->
    </div>
    <template v-if="collectRecordShow">
      <collectRecord ref="collectRecord" :location="location" :ssmType="ssmType" :dialogShow="collectRecordShow" @configCloseDialog="configCloseDialog"></collectRecord>
    </template>
    <template v-if="officeAnalysisShow">
      <officeAnalysis ref="officeAnalysis" :officeId="selectofficeId" :dialogShow="officeAnalysisShow" @configCloseDialog="configCloseDialog"></officeAnalysis>
    </template>
  </div>
</template>
<script>
import $ from 'jquery'
import * as echarts from 'echarts'
import collectRecord from './component/collectRecord.vue'
import officeAnalysis from './component/officeAnalysis.vue'
import { getDepartmentMedicalWasteInfo, getDepartWeightMedicalWasteInfo } from '@/utils/centerScreenApi'
export default {
  name: 'businessIMWSalaysis',
  components: {
    collectRecord,
    officeAnalysis
  },
  data() {
    return {
      tableData: [],
      deptAllShow: true,
      tableLoading: false,
      collectRecordShow: false,
      location: '',
      ssmType: '',
      selectofficeId: '',
      officeAnalysisShow: false
    }
  },
  mounted() {
    // this.location = '0100103'
    // this.ssmType = '5'
    // 初始化 获取定位信息
    if (Object.hasOwn(this.$route.query, 'localtion') || Object.hasOwn(this.$route.query, 'ssmType')) {
      this.location = this.$route.query.localtion || ''
      this.ssmType = this.$route.query.ssmType || ''
    }
    this.activeTop5Event('#top5Week', 'week')
    this.activeAnalysisEvent('#deptWeek', 'week')
    try {
      window.chrome.webview.addEventListener('message', (event) => {
        const data = JSON.parse(event.data)
        this.location = data.localtion || ''
        this.ssmType = data.ssmType || ''
        if (data.type === 'area') {
          this.activeTop5Event('#top5Week', 'week')
          this.activeAnalysisEvent('#deptWeek', 'week')
        } else if (data.type === 'tag') {
          this.collectRecordShow = true
          this.$nextTick(() => {
            this.$refs.collectRecord.getDepartMedicalWasteTableList()
          })
          window.chrome.webview.hostObjects.sync.bridge.ShowTable(true)
        }
      })
    } catch (errpr) {}
  },
  methods: {
    // 科室医废类型分析
    getDepartmentMedicalWasteInfo(type) {
      getDepartmentMedicalWasteInfo({ dateType: type, currentPage: 1, pageSize: 50, spatialId: this.location, ssmType: this.ssmType }).then((res) => {
        const data = res.data
        if (data.code === '200') {
          const arr = data.data.list
          if (arr.length) {
            this.deptAllShow = false
            this.$nextTick(() => {
              this.getDeptProduceEcharts(arr)
            })
          } else {
            this.deptAllShow = true
          }
        }
      })
    },
    getDeptProduceEcharts(arr) {
      const getchart = echarts.init(document.getElementById('deptProduceEcharts'))
      const name = Array.from(arr, ({ officeName }) => officeName)
      const Infectedweigh = Array.from(arr, ({ Infectedweigh }) => Infectedweigh)
      const chemistryweigh = Array.from(arr, ({ chemistryweigh }) => chemistryweigh)
      const damageweigh = Array.from(arr, ({ damageweigh }) => damageweigh)
      const epidemicweigh = Array.from(arr, ({ epidemicweigh }) => epidemicweigh)
      const medicineweigh = Array.from(arr, ({ medicineweigh }) => medicineweigh)
      const pathoweigh = Array.from(arr, ({ pathoweigh }) => pathoweigh)
      const otherweigh = Array.from(arr, ({ otherweigh }) => otherweigh)
      const option = {
        backgroundColor: '',
        tooltip: {
          trigger: 'axis',
          backgroundColor: '#0D2169',
          borderColor: '#727382',
          textStyle: {
            color: '#fff'
          },
          axisPointer: {
            // 坐标轴指示器，坐标轴触发有效
            type: 'shadow' // 默认为直线，可选为：'line' | 'shadow'
          }
        },
        grid: {
          x: '13%',
          width: '75%',
          // y: '22%'
          bottom: '20%'
        },
        legend: {
          orient: 'horizontal',
          left: 'center',
          top: '0',
          // data: xdata,
          itemWidth: 8,
          itemHeight: 8,
          itemGap: 16,
          textStyle: {
            color: '#B3C2DD' //  字体颜色
          }
        },
        xAxis: {
          type: 'category',
          axisLine: {
            show: true,
            lineStyle: {
              color: '#878EA9',
              width: 1,
              type: 'solid'
            }
          },
          // boundaryGap: true,
          axisTick: {
            show: false
          },
          axisLabel: {
            interval: 0,
            margin: 8
          },
          data: name
        },
        yAxis: {
          type: 'value',
          axisLabel: {
            show: true,
            textStyle: {
              color: '#878EA9'
            }
          },
          axisLine: {
            lineStyle: {
              type: 'solid',
              color: '#3862B7',
              width: '1'
            }
          },
          splitLine: {
            lineStyle: {
              color: '#314A89',
              width: 1,
              type: 'solid'
            }
          },
          axisTick: {
            show: false
          }
        },
        series: [
          {
            name: '损伤类',
            data: damageweigh,
            type: 'bar',
            stack: 'one',
            barWidth: '6',
            itemStyle: {
              color: new echarts.graphic.LinearGradient(0, 1, 0, 0, [
                {
                  offset: 0,
                  color: 'rgba(175, 255, 217, 0.2)'
                },
                {
                  offset: 1,
                  color: '#2AF598 '
                }
              ])
            }
          },
          {
            name: '病理类',
            data: pathoweigh,
            type: 'bar',
            stack: 'one',
            barWidth: '6',
            itemStyle: {
              color: new echarts.graphic.LinearGradient(0, 1, 0, 0, [
                {
                  offset: 0,
                  color: 'rgba(255, 229, 143, 0.2)'
                },
                {
                  offset: 1,
                  color: '#FFE58F '
                }
              ])
            }
          },
          {
            name: '化学类',
            data: chemistryweigh,
            type: 'bar',
            stack: 'one',
            barWidth: '6',
            itemStyle: {
              color: new echarts.graphic.LinearGradient(0, 1, 0, 0, [
                {
                  offset: 0,
                  color: 'rgba(124, 174, 255, 0.2)'
                },
                {
                  offset: 1,
                  color: '#7CAEFF'
                }
              ])
            }
          },
          {
            name: '感染类',
            data: Infectedweigh,
            type: 'bar',
            stack: 'one',
            barWidth: '6',
            itemStyle: {
              color: new echarts.graphic.LinearGradient(0, 1, 0, 0, [
                {
                  offset: 0,
                  color: 'rgba(255, 198, 144, 0.2)'
                },
                {
                  offset: 1,
                  color: '#FFC690'
                }
              ])
            }
          },
          {
            name: '药物类',
            data: medicineweigh,
            type: 'bar',
            stack: 'one',
            barWidth: '6',
            itemStyle: {
              color: new echarts.graphic.LinearGradient(0, 1, 0, 0, [
                {
                  offset: 0,
                  color: 'rgba(255, 96, 96, 0.2)'
                },
                {
                  offset: 1,
                  color: '#FF6060'
                }
              ])
            }
          },
          {
            name: '涉疫类',
            data: epidemicweigh,
            type: 'bar',
            stack: 'one',
            barWidth: '6',
            itemStyle: {
              color: new echarts.graphic.LinearGradient(0, 1, 0, 0, [
                {
                  offset: 0,
                  color: 'rgba(0, 248, 114, 0.2)'
                },
                {
                  offset: 1,
                  color: 'rgba(0, 248, 114, 1)'
                }
              ])
            }
          },
          {
            name: '其他类',
            data: otherweigh,
            type: 'bar',
            stack: 'one',
            barWidth: '6',
            itemStyle: {
              color: new echarts.graphic.LinearGradient(0, 1, 0, 0, [
                {
                  offset: 0,
                  color: 'rgba(147, 130, 255, 0.2)'
                },
                {
                  offset: 1,
                  color: 'rgba(147, 130, 255, 1)'
                }
              ])
            }
          }
        ],
        dataZoom: [
          {
            xAxisIndex: 0,
            show: true,
            type: 'slider',
            startValue: 100,
            endValue: 100,
            height: 8,
            bottom: '2%',
            borderColor: 'rgba(43,48,67,.1)',
            fillerColor: '#6580b8',
            zoomLock: false,
            showDataShadow: false,
            backgroundColor: 'rgba(43,48,67,.1)',
            showDetail: false,
            realtime: true,
            filterMode: 'filter',
            handleIcon: 'M-9.35,34.56V42m0-40V9.5m-2,0h4a2,2,0,0,1,2,2v21a2,2,0,0,1-2,2h-4a2,2,0,0,1-2-2v-21A2,2,0,0,1-11.35,9.5Z',
            handleStyle: {
              color: '#6580b8',
              borderColor: '#6580b8'
            },
            moveOnMouseMove: true,
            maxValueSpan: arr.length,
            minValueSpan: 1,
            brushSelect: false
          },
          {
            type: 'inside',
            show: false,
            height: 8,
            start: 100,
            end: 100
          }
        ]
        // dataZoom: [
        // {
        //   type: 'slider',
        //   show: true,
        //   xAxisIndex: [0],
        //   handleSize: 10, // 滑动条的 左右2个滑动条的大小
        //   height: 0, // 组件高度
        //   left: 30, // 左边的距离
        //   right: 40, // 右边的距离
        //   bottom: 30, // 右边的距离
        //   handleColor: '#3056A2', // h滑动图标的颜色
        //   handleStyle: {
        //     borderColor: '#3056A2',
        //     borderWidth: '0',
        //     shadowBlur: 0,
        //     background: '',
        //     shadowColor: ''
        //   },
        //   fillerColor: '#3056A2',
        //   backgroundColor: '', // 两边未选中的滑动条区域的颜色
        //   showDataShadow: false, // 是否显示数据阴影 默认auto
        //   showDetail: false, // 即拖拽时候是否显示详细数值信息 默认true
        //   // handleIcon:
        //   // 'M-292,322.2c-3.2,0-6.4-0.6-9.3-1.9c-2.9-1.2-5.4-2.9-7.6-5.1s-3.9-4.8-5.1-7.6c-1.3-3-1.9-6.1-1.9-9.3c0-3.2,0.6-6.4,1.9-9.3c1.2-2.9,2.9-5.4,5.1-7.6s4.8-3.9,7.6-5.1c3-1.3,6.1-1.9,9.3-1.9c3.2,0,6.4,0.6,9.3,1.9c2.9,1.2,5.4,2.9,7.6,5.1s3.9,4.8,5.1,7.6c1.3,3,1.9,6.1,1.9,9.3c0,3.2-0.6,6.4-1.9,9.3c-1.2,2.9-2.9,5.4-5.1,7.6s-4.8,3.9-7.6,5.1C-285.6,321.5-288.8,322.2-292,322.2z',
        //   filterMode: 'filter',
        //   start: 0,
        //   end: 20,
        //   zoomLock: true
        // },
        // 下面这个属性是里面拖到
        // {
        //   type: 'slider',
        //   show: true,
        //   xAxisIndex: [0],
        //   start: 0,
        //   end: 20,
        //   zoomLock: true,
        //   height: 0,
        //   showDetail: false // 即拖拽时候是否显示详细数值信息 默认true
        // }
        // ]
      }
      getchart.clear()
      getchart.setOption(option)
      // 随着屏幕大小调节图表
      window.addEventListener('resize', () => {
        getchart.resize()
      })
    },
    // 科室医废重量分析
    getTypeAnalysisTable(val) {
      this.tableLoading = true
      getDepartWeightMedicalWasteInfo({ dateType: val, currentPage: 1, pageSize: 50, spatialId: this.location, ssmType: this.ssmType }).then((res) => {
        const data = res.data
        this.tableLoading = false
        if (data.code === '200') {
          const arr = data.data.list
          if (arr.length) {
            this.tableData = data.data.list
          } else {
            this.tableData = []
          }
        }
      })
    },
    officeAnalysisChange(row) {
      this.officeAnalysisShow = true
      this.selectofficeId = row.officeId
      this.$nextTick(() => {
        this.$refs.officeAnalysis.getDepartMedicalWasteTableList()
      })
      window.chrome.webview.hostObjects.sync.bridge.ShowTable(true)
    },
    activeTop5Event(type, val) {
      $('#top5Week').removeClass('active')
      $('#top5Month').removeClass('active')
      $('#top5Year').removeClass('active')
      $(type).addClass('active')
      this.getDepartmentMedicalWasteInfo(val)
    },
    activeAnalysisEvent(type, val) {
      $('#deptWeek').removeClass('active')
      $('#deptMonth').removeClass('active')
      $('#deptYear').removeClass('active')
      $(type).addClass('active')
      this.getTypeAnalysisTable(val)
    },
    collectRecordChange() {
      this.collectRecordShow = !this.collectRecordShow
      this.$nextTick(() => {
        this.$refs.collectRecord.getDepartMedicalWasteTableList()
      })
    },
    configCloseDialog(dialogName) {
      this[dialogName] = false
      try {
        window.chrome.webview.hostObjects.sync.bridge.ShowTable(false)
      } catch (error) {}
    },
    backToWPFhome() {
      window.chrome.webview.hostObjects.sync.bridge.Back('businessIMWS')
    }
  }
}
</script>
<style lang="scss" scoped>
.content {
  width: 100%;
  height: 100%;
  position: relative;
  display: flex;
  .active {
    background: url('~@/assets/images/center/light-yellow.png') no-repeat;
    background-size: 100% 100%;
    color: #ffe3a6 !important;
    outline: none;
  }
  .title {
    height: 30px;
    position: relative;
    div {
      cursor: pointer;
      position: absolute;
      top: 5px;
      left: 0;
      width: 80px;
      height: 30px;
      line-height: 30px;
      text-align: center;
      color: #618ad3;
      font-size: 0.875rem;
      white-space: nowrap;
      background-image: url(../../../assets/images/peace/btn-back.png);
      background-size: 100% 100%;
      background-repeat: no-repeat;
    }
  }
  .content-left {
    width: 20%;
    height: calc(100% - 35px);
    padding-top: 35px;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
  }
  .left-top {
    width: 100%;
    height: calc(50% - 5px);
    background: red;
    background: url('~@/assets/images/center/ioms-analysis-bg.png') no-repeat;
    background-size: 100% 100%;
  }
  .left-bottom {
    width: 100%;
    height: 50%;
    background: red;
    background: url('~@/assets/images/center/ioms-analysis-bg.png') no-repeat;
    background-size: 100% 100%;
    ::v-deep .el-table {
      border: none !important;
      .el-table__header .el-table__cell {
        padding: 5px 0 !important;
        color: #fff;
      }
      .el-table__body {
        tr {
          background: center;
        }
        td.el-table__cell,
        th.el-table__cell.is-leaf {
          border-right: 2px solid #0a164e;
          border-bottom: 2px solid #0a164e;
          background: rgba(56, 103, 180, 0.2);
          color: #fff;
        }
      }
    }
  }
  .bg-title {
    margin-top: 5px;
    // height: 23%;
    height: 2.8rem;
    line-height: 2.8rem;
    color: #d4e3f9;
    padding: 0 1.5rem 0 3rem;
    font-family: TRENDS;
    /*设置为伸缩容器*/
    display: -webkit-box;
    display: -moz-box;
    display: -ms-flexbox;
    display: -webkit-flex;
    display: flex;
    /*垂直居中*/
    -webkit-box-align: center; /*旧版本*/
    -moz-box-align: center; /*旧版本*/
    -ms-flex-align: center; /*混合版本*/
    -webkit-align-items: center; /*新版本*/
    align-items: center; /*新版本*/
    justify-content: space-between;
    .right-search {
      span {
        display: inline-block;
        width: 30px;
        height: 30px;
        font-size: 14px;
        text-align: center;
        line-height: 26px;
        margin: 0 4px;
        color: #7eaef9;
        cursor: pointer;
      }
      span:hover {
        background: url('~@/assets/images/center/light-yellow.png') no-repeat;
        background-size: 100% 100%;
        color: #ffe3a6;
      }
      i {
        color: #7eaef9;
      }
    }
  }
  .bg-content {
    position: relative;
    box-sizing: border-box;
    padding: 10px 10px 15px 10px;
    width: 100%;
    height: calc(100% - 2.5rem);
    #deptProduceEcharts {
      width: 100%;
      height: 100%;
    }
  }
}
</style>
