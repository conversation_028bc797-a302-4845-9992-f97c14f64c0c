<template>
  <div class="list-index-view">
    <div class="view-header-box">
      <div class="header-left-box">
        <div :class="[`state${currentStatus}`, 'cfx']">
          <div class="text">
            <em class="icon"></em>
            <span>
              未完成
            </span>
          </div>
          <div class="triangle"></div>
        </div>
        <slot name="left"></slot>
      </div>
      <div class="header-right-box">
        <span>操作人：赵新兵</span><span>|</span>
        <span>操作时间：2024-10-08 02:59:47</span>
        <span>操作详情</span>
      </div>
    </div>
    <div class="view-content">
      <el-form ref="form" :model="detailInfo" label-width="120px">
        <div class="baseInfo">
          <div class="toptip">
            <span class="green_line"></span>
            基本信息
          </div>
          <el-row :gutter="24">
            <el-col :span="item.lgWidth" v-for="(item, index) in assetsInfo" :key="index">
              <el-form-item :label="`${item.label}：`">
                <span>{{ basicInfoForm[item.prop] | basicFilters }}</span>
              </el-form-item>
            </el-col>
          </el-row>
        </div>
        <div class="assetsInfo">
          <div class="toptip">
            <span class="green_line"></span>
            租赁资产
          </div>
          <el-table :data="tableData" :cell-style="$tools.setCell(3)" :header-cell-style="$tools.setHeaderCell(3)"
            style="width: 100%" element-loading-background="rgba(0, 0, 0, 0.2)" v-loading="tableLoading">
            <el-table-column prop="assetsName" show-overflow-tooltip label="资产名称"></el-table-column>
            <el-table-column prop="assetsCode" show-overflow-tooltip label="资产编码"></el-table-column>
            <el-table-column prop="commonName" show-overflow-tooltip label="通用名"></el-table-column>
            <el-table-column prop="model" show-overflow-tooltip label="规格型号"></el-table-column>
            <el-table-column prop="statusName" show-overflow-tooltip label="资产状态">
              <template slot-scope="scope">
                <div class="status-box">
                  <img class="table-icon" :src='icon_5' />
                  <span style="color:#61E29D">{{scope.row.statusName}}</span>
                </div>
              </template>
            </el-table-column>
            <el-table-column prop="money" show-overflow-tooltip label="资产金额"></el-table-column>
            <el-table-column prop="settlementWay" show-overflow-tooltip label="结算方式"></el-table-column>
            <el-table-column prop="rate" show-overflow-tooltip label="费率"></el-table-column>
          </el-table>
        </div>
        <div class="fileInfo">
          <div class="toptip">
            <span class="green_line"></span>
            其他
          </div>
          <el-row :gutter="24">
            <el-col :span="8">
              <el-form-item label="现场照片">
                <img src="@/assets/images/war/no-people.png" alt="" class="image" />
              </el-form-item>
            </el-col>
          </el-row>
        </div>
      </el-form>
    </div>
  </div>
</template>
  <script>
import icon_5 from '@/assets/images/icon-5.png'
export default {
  name: 'leaseDetail',
  components: {
  },
  data() {
    return {
      icon_5,
      tableLoading: false,
      searchForm: {
        input: "",
        dept: "",
      },
      detailInfo: {},
      deptOptions: [],
      stateOptions: [],
      currentPage: 1,
      pageSize: 15,
      total: 5,
      tableData: [],
      assetsInfo: [
        {
          label: '租赁编号',
          prop: 'code',
          lgWidth: 8,
        },
        {
          label: '租赁科室',
          prop: 'dept',
          lgWidth: 8,
        },
        {
          label: '租赁人',
          prop: 'name',
          lgWidth: 8,
        },
        {
          label: '租赁人电话',
          prop: 'phone',
          lgWidth: 8,
        },
        {
          label: '租赁时间',
          prop: 'time',
          lgWidth: 8,
        },
        {
          label: '预计归还时间',
          prop: 'returnTime',
          lgWidth: 8,
        },
        {
          label: '配送方式',
          prop: 'way',
          lgWidth: 8,
        },
        {
          label: '预计费用',
          prop: 'money',
          lgWidth: 8,
        },
      ],
      basicInfoForm: {
        code: 'JYD202400001',
        dept: '测试部',
        name: '管理员',
        phone: '15910596654',
        time: '2024-10-14 01:22:03',
        returnTime: '2024-10-24 01:22:03',
        way: '自提',
        money: '6.000元'
      },
      currentStatus: 13,
      purchaseStatusType:13,
    };
  },
  filters: {
    basicFilters(val) {
      console.log(val, 'sssssssssss')
      return val ? val : "---";
    },
  },
  mounted() {
    this.getDataList()
  },
  methods: {
    getDataList() {
      this.tableData = [
        {
          assetsName: '呼吸机',
          assetsCode: '30SD45EDT123',
          commonName: '--',
          model: 'Primus',
          statusName: '闲置中',
          sort: '呼吸机',
          money: '--',
          settlementWay: '按小时结算',
          rate: '1.00元/小时',
          money: '--',
          settlementWay: '按小时结算',
          rate: '1.00元/小时'
        },
        {
          assetsName: '眼科手术器械',
          assetsCode: '30SD45EDT123',
          commonName: '--',
          model: 'Primus',
          statusName: '闲置中',
          sort: '呼吸机',
          money: '--',
          settlementWay: '按小时结算',
          rate: '1.00元/小时'
        },
      ]
    },
    handleClose(done) {
      done();
    },
  },
};
  </script>
  <style lang="scss" scoped>
.list-index-view {
  position: relative;
  height: 100%;
  padding: 0px 40px;
}
.view-header-box {
  background-color: rgba(53, 98, 219, 0.06);
  display: flex;
  align-items: center;
  height: 60px;
  line-height: 60px;
  justify-content: space-between;
  .header-left-box {
    .state0,
    .state12 {
      background-color: #8c8c8c;
    }
    .state4,
    .state1 {
      background-color: #fa8c2b;
    }
    .state5,
    .state2 {
      background-color: #52c41a;
    }
    .state6,
    .state3,
    .state13 {
      background-color: #ff4d4f;
    }
    .state7 {
      background-color: #1474a4;
    }
    .cfx {
      display: flex;
      height: 36px;
      line-height: 36px;
      position: relative;
      .text {
        display: inline-flex;
        align-items: center;
        height: 36px;
        line-height: 36px;
        font-size: 14px;
        color: #fff;
        margin-right: 26px;
        .icon {
          color: #fff;
          margin: 0 10px;
        }
        span {
          font-weight: bold;
        }
      }
      .triangle {
        position: absolute;
        top: 2px;
        right: 0;
        width: 0;
        height: 0;
        border-width: 16px;
        border-style: solid;
        border-color: transparent #061430 transparent transparent;
      }
    }
  }
  .header-right-box {
    span:nth-child(2) {
      margin: 0 18px;
    }
    span:nth-child(4) {
      color: #8bddf5;
      margin-left: 18px;
      cursor: pointer;
    }
  }
}
.view-content {
  height: calc(100% - 4rem);
  .image {
    width: 100px;
    height: 200px;
    margin-top: 8px;
  }
  .toptip {
    box-sizing: border-box;
    height: 50px;
    width: 100%;
    line-height: 50px;
    display: flex;
    font-size: 16px;
    align-items: center;
  }
  .green_line {
    display: inline-block;
    width: 6px;
    height: 16px;
    border-radius: 2px;
    background: #1574a4;
    margin-right: 10px;
    vertical-align: middle;
  }
  .table-icon {
    width: 16px;
    margin-right: 3px;
  }
  .status-box {
    display: flex;
    align-items: center;
  }
  .table-view {
    height: 100%;
  }
}
::v-deep .el-form-item__content {
  font-size: 0.875rem;
}
::v-deep .el-form-item {
  margin-bottom: 8px !important;
}
::v-deep .el-form-item__label {
  color: #ffffff !important;
}
</style>

