<template>
  <div class="energy">
    <!-- <div style="color: red;word-wrap:break-word;">{{ wpfData }}</div> -->
    <div class="energy-main">
      <div class="main-search">
        <div class="search-tabs">
          <div
            v-for="(v, i) in menuList"
            :key="i"
            class="tabs-item"
            :style="{ width: 'calc(100% / ' + menuList.length + ')' }"
            :class="{ 'tabs-active': menuActive === i }"
            @click="activeEnergyEvent(i)"
          >
            {{ v.name }}
          </div>
        </div>
        <div v-if="tabChildren.length" class="search-tags">
          <div v-for="(v, i) in tabChildren" :key="i" class="tags-item" :class="{ 'tags-active': menuTitleActive === i }" @click="selectMenuTitle(i)">
            {{ filterName(v.name) }}
          </div>
        </div>
        <div class="search-data">
          <el-dropdown trigger="click" @command="dataTypeCommand">
            <span class="el-dropdown-link"> {{ dataTypeList.find((v) => v.value == statisticalType)?.name ?? '' }} <i class="el-icon-caret-bottom"></i> </span>
            <el-dropdown-menu slot="dropdown">
              <el-dropdown-item v-for="item in dataTypeList" :key="item.value" :command="item.value" :class="{ isBjxl: statisticalType == item.value }">{{ item.name }}</el-dropdown-item>
            </el-dropdown-menu>
          </el-dropdown>
          <el-date-picker
            v-model="statisticalDate"
            class="datePickerInput"
            popper-class="date-style"
            type="daterange"
            value-format="yyyy-MM-dd"
            :disabled="statisticalType != 5"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            @change="setWPFdata"
            @focus="setWPFBgShow()"
            @blur="setWPFBgHide()"
          >
          </el-date-picker>
        </div>
      </div>
      <div class="main-scroll">
        <div class="main-module main-stat">
          <div class="main-module-title">
            <div class="title-left">
              <p class="left-text text-active">能耗统计</p>
            </div>
          </div>
          <div class="main-module-content">
            <div class="module-content">
              <div id="stat_chart" class="content-left"></div>
              <div class="content-right">
                <div class="right-item">
                  <div class="item-head">
                    <p class="item-title">能耗总量</p>
                    <p class="item-value">
                      <span>{{ monitorData.value }}</span>
                      <span>{{ unitItems[energyId] }}</span>
                    </p>
                  </div>
                  <p class="item-line"></p>
                  <div style="display: flex; height: 14px">
                    <p class="item-ratio">
                      <span class="item-ratio-name">同比</span>
                      <i
                        :class="monitorData.yoyValueRatio > 0 ? 'el-icon-top-right' : 'el-icon-bottom-left'"
                        :style="{ color: monitorData.yoyValueRatio > 0 ? '#FF2D55' : '#61E29D', background: monitorData.yoyValueRatio > 0 ? 'rgba(255, 45, 85, .2)' : 'rgba(97, 226, 157, .2)' }"
                      ></i>
                      <span :style="{ color: monitorData.yoyValueRatio > 0 ? '#FF2D55' : '#61E29D' }">{{ monitorData.yoyValueRatio || '-' }}%</span>
                    </p>
                    <p class="item-ratio" style="margin-left: 6px">
                      <span class="item-ratio-name">环比</span>
                      <i
                        :class="monitorData.momValueRatio > 0 ? 'el-icon-top-right' : 'el-icon-bottom-left'"
                        :style="{ color: monitorData.momValueRatio > 0 ? '#FF2D55' : '#61E29D', background: monitorData.momValueRatio > 0 ? 'rgba(255, 45, 85, .2)' : 'rgba(97, 226, 157, .2)' }"
                      ></i>
                      <span :style="{ color: monitorData.momValueRatio > 0 ? '#FF2D55' : '#61E29D' }">{{ monitorData.momValueRatio || '-' }}%</span>
                    </p>
                  </div>
                  <!-- <div v-else style="height: 14px"></div> -->
                  <div class="item-head" style="margin-top: 40px">
                    <p class="item-title">费用总数</p>
                    <p class="item-value">
                      <span>{{ monitorData.valueCost || '-' }}</span>
                      <span>元</span>
                    </p>
                  </div>
                </div>
              </div>
            </div>
            <div class="module-content" style="padding: 0px 35px; justify-content: space-between">
              <p class="content-bottom">
                <span class="bottom-name">去年同期</span>
                <span class="bottom-value">{{ monitorData.yoyValue || '-' }}</span>
                <span class="bottom-unit">{{ unitItems[energyId] }}</span>
              </p>
              <p class="content-bottom">
                <span class="bottom-name">能耗定额</span>
                <span class="bottom-value">{{ '-' }}</span>
                <span class="bottom-unit">{{ unitItems[energyId] }}</span>
              </p>
            </div>
          </div>
        </div>
        <div class="main-module" style="min-height: 10%; max-height: 37.5%; display: flex; flex-direction: column; margin-bottom: 32px">
          <div class="main-module-title">
            <div class="title-left">
              <p class="left-text text-active">能耗排行</p>
            </div>
          </div>
          <div v-show="areaRankingList.length" class="main-module-content main-ranking">
            <div
              v-for="item in areaRankingList"
              :key="item.emodelName"
              class="list-item"
              :class="{ 'item-active': selectEmodelId == item.emodelId }"
              @click="
                () => {
                  (selectEmodelId = item.emodelId, pagination.pageNo = 1), getDeviceList()
                }
              "
            >
              <img class="item-img" :src="imgs[energyId] || dianImg" :alt="showContentType" />
              <div class="item-content">
                <div class="content-text">
                  <p>{{ item.emodelName }}</p>
                  <p style="flex-shrink: 0">{{ filterValue(item.value) + item.energyUnitEn + '&nbsp;&nbsp;' + filterValue(item.ratio, 'toFixed') + '%' }}</p>
                </div>
                <p class="content-line">
                  <span :style="{ width: item.ratio + '%' }"></span>
                </p>
              </div>
            </div>
          </div>
          <div v-show="!areaRankingList.length" class="main-module-content main-ranking">
            <p class="nodeData">暂无数据</p>
          </div>
        </div>
        <div class="main-module" :style="{ height: tabChildren.length ? '35%' : 'calc(35% + 52px)' }">
          <div class="main-module-title">
            <div class="title-left">
              <p class="left-text" :class="{ 'text-active': showType == 0 }" @click="showTypeChange(0)">用能类型</p>
              <p class="left-text" :class="{ 'text-active': showType == 1 }" style="margin-left: 16px" @click="showTypeChange(1)">能耗趋势</p>
            </div>
          </div>
          <div v-show="showType == 0" class="main-module-content main-analysis" :style="{ justifyContent: typeAnalysisList.length ? '' : 'center' }">
            <div id="analysis_chart" class="analysis-content-chart"></div>
            <div v-show="typeAnalysisList.length" class="analysis-content-list">
              <div v-for="item in typeAnalysisList" :key="item.name" class="list-item">
                <span class="list-item-color" :style="{ background: item.color }"></span>
                <p class="list-item-name">{{ filterName(item.name) }}</p>
                <p class="list-item-value">{{ filterValue(item.value) + item.unit }}</p>
              </div>
            </div>
          </div>
          <div v-show="showType == 1" class="main-module-content">
            <div id="trend_chart" class="trend_chart"></div>
          </div>
        </div>
        <div v-if="energyId === 'SU035'" class="main-module" style="height: 32%">
          <div class="main-module-title">
            <div class="title-left">
              <p class="left-text text-active">电表能耗排行</p>
            </div>
          </div>
          <div class="main-module-content">
            <el-table
              ref="srollTable"
              v-el-table-infinite-scroll="tableLoadMore"
              v-loading="deviceTableLoading"
              class="table-center-transfer"
              :data="deviceList"
              height="calc(100% - 0px)"
              style="width: 100%"
              :cell-style="{ padding: '8px', backgroundColor: 'transparent', color: '#fff', border: 'none', padding: '3px 0px' }"
              :header-cell-style="{ background: '#8591CE26!important', color: '#8BDDF5FF', padding: '0px', fontWeight: 'bold' }"
              element-loading-background="rgba(0, 0, 0, 0.2)"
              @sort-change="sortTableEvent"
              @row-click="tableRowClick"
            >
              <el-table-column prop="surveyName" show-overflow-tooltip label="设备名称"></el-table-column>
              <!-- <el-table-column prop="type" show-overflow-tooltip label="用电类型"></el-table-column> -->
              <el-table-column prop="regionName" show-overflow-tooltip label="空间位置"></el-table-column>
              <el-table-column prop="energyValue" show-overflow-tooltip label="耗能(kwh)" width="110" sortable="custom">
                <template slot-scope="scope">{{ filterValue(scope.row.energyValue) }}</template>
              </el-table-column>
            </el-table>
          </div>
        </div>
      </div>
    </div>
    <!-- 设备能耗 -->
    <template v-if="deviceEnergyShow">
      <deviceEnergy
        :show="deviceEnergyShow"
        :requestParams="{ title: '设备1', surveyCode: deviceSurveyCode, gyToken: kgceToken }"
        @closeDialog="
          () => {
            deviceEnergyShow = false
          }
        "
      />
    </template>
  </div>
</template>
<script>
import { KgceLogin, GetEnergyAndClassifyTree, GetModelEnergyDataList, GetTreeAndPathAndDetail, GetEnergyMeteringList, GetModelFormulaPoint } from '@/utils/energyConsumption'

import dianImg from '@/assets/images/qhdsys/dianImg.png'
import shuiImg from '@/assets/images/qhdsys/shuiImg.png'
import reImg from '@/assets/images/qhdsys/reImg.png'
import qiImg from '@/assets/images/qhdsys/qiImg.png'
import dianIcon from '@/assets/images/qhdsys/dianIcon.png'
import shuiIcon from '@/assets/images/qhdsys/shuiIcon.png'
import reIcon from '@/assets/images/qhdsys/reIcon.png'
import qiIcon from '@/assets/images/qhdsys/qiIcon.png'
import deviceEnergy from './component/deviceEnergy.vue'
import moment from 'moment'
import * as echarts from 'echarts'
moment.locale('zh-cn')
// import 'echarts-gl'
export default {
  name: 'energyConsumption',
  components: { deviceEnergy },
  data() {
    return {
      dianImg,
      deviceEnergyShow: false,
      wpfData: '',
      energyId: '', // 选中一级能耗id
      menuActive: 0, // 一级用能类型
      menuTitleActive: 0, // 二级用能类型
      showContentType: '', // 用能类型名称
      menuList: [], // 一级用能类型列表
      tabChildren: [], // 二级用能类型列表
      emodelId: '1724753192417259521', // 模型id
      emodelIdAll: '', // 模型id
      activeEnergyUnit: '', // 当前选中菜单对应的耗能单位
      unitItems: {
        SU035: 'kWh', // 电
        CM020: 't', // 水
        '4c78a70e0b7deb51b0a227fb2cd9196f': 'GJ', // 热
        '66655f36bc0dc86adbaf2cab80d9dce2': 'm³' // 气
      }, // 能量单位
      kgceToken: '', // 能耗用户token
      // spaceLevel: '4', // 空间等级：1医院，2区域，3建筑，4楼层，5房间
      spaceLevel: '1', // 空间等级：1医院，2区域，3建筑，4楼层，5房间
      statisticalType: 3, // 选中日期类型
      statisticalDate: [moment().startOf('month').format('YYYY-MM-DD') + ' 00:00:00', moment().endOf('month').format('YYYY-MM-DD') + ' 23:59:59'], // 选中日期
      dataTypeList: [
        { value: 1, name: '今日' },
        { value: 2, name: '本周' },
        { value: 3, name: '本月' },
        { value: 4, name: '本年' },
        { value: 5, name: '自定义' }
      ],
      imgs: {
        SU035: dianImg,
        CM020: shuiImg,
        '4c78a70e0b7deb51b0a227fb2cd9196f': reImg,
        '66655f36bc0dc86adbaf2cab80d9dce2': qiImg
      },
      monitorData: {}, // 实时监测数据
      typeAnalysisList: [], // 类型分析数据
      areaRankingList: [], // 排行列表
      spaceName: '',
      deviceTableLoading: false,
      deviceList: [],
      pagination: {
        total: 0,
        pageSize: 15,
        pageNo: 1
      },
      tableOrder: 'asc', // 表格排序
      showType: 0, // 0用能类型 1能耗趋势
      selectEmodelId: '', // 选中排行空间id
      deviceSurveyCode: '8c1673f246ee4cee9cbc91bee5016849' // 设备监测项code
    }
  },
  computed: {
    filterValue() {
      return (value, type) => {
        value = value && !isNaN(Number(value)) ? Number(value) : 0
        // return value ? value.toFixed(2) : 0
        if (type == 'toFixed') {
          return value.toFixed(2)
        } else {
          if (value > 1000) {
            return (value / 10000).toFixed(2) + '万'
          } else {
            return value.toFixed(2)
          }
        }
      }
    },
    filterName() {
      return (name) => {
        name = name.replace(/用水/g, '').replace(/用电/g, '')
        return name
      }
    }
  },
  watch: {
    deviceEnergyShow(val) {
      try {
        window.chrome.webview.hostObjects.sync.bridge.ShowDialog(val)
      } catch (error) {}
    }
    // selectEmodelId: {
    //   handler() {
    //     this.getDeviceList()
    //   }
    // }
  },
  mounted() {
    if (this.$route.query.areaData) {
      // this.wpfData = this.$route.query.areaData
      const data = JSON.parse(this.$route.query.areaData)
      this.emodelId = data.parentId
      this.emodelIdAll = data.childList.join(',')
      this.spaceLevel = data.ssmType
      this.spaceName = data.ssmName
    }
    try {
      window.chrome.webview.addEventListener('message', (event) => {
        // this.wpfData = JSON.parse(event.data).areaData
        this.enterBuild = true
        const data = JSON.parse(JSON.parse(event.data).areaData)
        this.spaceLevel = data.ssmType
        this.spaceName = data.ssmName
        this.emodelId = data.parentId
        this.emodelIdAll = data.childList.join(',')
        this.setWPFdata()
      })
    } catch (errpr) {}
    this.$nextTick(() => {
      this.kgceLogin()
    })
  },
  methods: {
    tableRowClick(row) {
      // 如果关联了设备即跳转设备详情页
      if (row.assetId) {
        const params = {
          assetsId: row.assetId,
          projectCode: 'IEMC-EnergyMetering',
          spaceCode: row?.regionCode
        }
        try {
          window.chrome.webview.hostObjects.sync.bridge.EquipmentRealView(JSON.stringify(params))
        } catch (error) {}
      }
    },
    // 能耗趋势
    energyTrend() {
      const dataType = {
        1: 'hour',
        2: 'day',
        3: 'day',
        4: 'month',
        5: 'day'
      }
      let params = {
        emodelId: this.emodelId, // 模型id
        timeCategory: dataType[this.statisticalType],
        energyId: this.menuList[this.menuActive].children.length
          ? this.menuTitleActive === 0
            ? this.menuList[this.menuActive].children[this.menuTitleActive].allId + ',' + this.energyId
            : this.menuList[this.menuActive].children[this.menuTitleActive].id
          : this.energyId
        // energyId: this.menuList[this.menuActive].children.length
        // ? this.menuList[this.menuActive].children[this.menuTitleActive].id
        // : this.energyId
      }

      if (this.statisticalType === 1) {
        params = {
          ...params,
          startDate: moment().format('YYYY-MM-DD') + ' 00:00:00',
          endDate: moment().format('YYYY-MM-DD') + ' 23:59:59'
        }
      } else if (this.statisticalType === 2) {
        params = {
          ...params,
          startDate: moment().startOf('isoWeek').format('YYYY-MM-DD') + ' 00:00:00',
          endDate: moment().endOf('isoWeek').format('YYYY-MM-DD') + ' 23:59:59'
        }
      } else if (this.statisticalType === 3) {
        params = {
          ...params,
          startDate: moment().startOf('month').format('YYYY-MM-DD') + ' 00:00:00',
          endDate: moment().endOf('month').format('YYYY-MM-DD') + ' 23:59:59'
        }
      } else if (this.statisticalType === 4) {
        params = {
          ...params,
          startDate: moment().startOf('year').format('YYYY-MM-DD') + ' 00:00:00',
          endDate: moment().endOf('year').format('YYYY-MM-DD') + ' 23:59:59'
        }
      } else if (this.statisticalType === 5) {
        params = {
          ...params,
          startDate: this.statisticalDate[0],
          endDate: this.statisticalDate[1]
        }
      }
      const newObj = this.menuTitleActive === 0 ? { 总能耗: [] } : {}
      this.getModelEnergyDataList(params)
        .then((res) => {
          let key = ''
          res.data.data.forEach((item) => {
            key = item.energyName == this.showContentType ? '总能耗' : item.energyName
            if (newObj[key]) {
              newObj[key].push(item)
            } else {
              newObj[key] = [item]
            }
          })
          setTimeout(() => {
            this.$nextTick(() => {
              this.electricityLineEchart(newObj)
            })
          }, 100)
        })
        .catch()
    },
    // 折线图
    electricityLineEchart(data) {
      const getchart = echarts.init(document.getElementById('trend_chart'))
      let option
      if (Object.keys(data).length) {
        option = {
          dataZoom: [
            {
              type: 'inside',
              start: 0,
              end: 100
            }
          ],
          tooltip: {
            trigger: 'axis',
            axisPointer: {
              lineStyle: {
                color: '#FFE3A6'
              }
            },
            backgroundColor: '#0C2269',
            borderColor: '#5B617C',
            textStyle: {
              color: '#fff'
            }
          },
          grid: {
            top: '20%',
            left: '15%',
            right: '4$%',
            bottom: '12%'
          },
          legend: {
            x: 'center',
            top: '4',
            data: Object.keys(data),
            itemWidth: 8,
            itemHeight: 8,
            // icon: "stack",
            pageTextStyle: {
              color: '#fff'
            },
            textStyle: {
              //  fontSize: 18,//字体大小
              color: '#B3C2DD' //  字体颜色
            }
          },
          xAxis: {
            // type: 'category',
            // data: nameList,
            type: 'time',
            boundaryGap: false,
            axisTick: {
              show: false
            },
            axisLine: {
              lineStyle: {
                color: '#303F69'
              }
            },
            axisLabel: {
              textStyle: {
                fontSize: 10,
                color: '#8C9EB5'
              }
            }
          },
          yAxis: {
            type: 'value',
            name: '单位：' + this.unitItems[this.energyId],
            nameTextStyle: {
              color: '#A4AFC1',
              fontSize: 12,
              padding: [0, 0, 0, 0]
            },
            axisTick: {
              show: false
            },
            // axisLine: {
            //   lineStyle: {
            //     color: '#609ee9'
            //   }
            // },
            axisLabel: {
              margin: 10,
              textStyle: {
                fontSize: 10,
                color: '#8C9EB5'
              }
            },
            splitLine: {
              show: true,
              lineStyle: {
                color: ['rgba(230, 247, 255, 0.20)'],
                width: 1,
                type: 'dashed'
              }
            }
          },
          series: Object.keys(data).map((key) => {
            return {
              name: key,
              type: 'line',
              // smooth: true,
              showSymbol: false,
              symbol: 'circle',
              symbolSize: 6,
              // data: valueList,
              data: data[key].map((item) => [item.dataTime, item.value]),
              itemStyle: {
                normal: {
                  color: '#FFE3A6'
                }
              },
              lineStyle: {
                normal: {
                  width: 2
                }
              }
            }
          })
        }
      } else {
        option = {
          title: {
            text: '暂无数据',
            x: 'center',
            y: 'center',
            textStyle: {
              fontSize: 14,
              fontWeight: 'normal',
              color: '#999'
            }
          }
        }
      }
      getchart.clear()
      getchart.setOption(option)
      // 随着屏幕大小调节图表
      window.addEventListener('resize', () => {
        getchart.resize()
      })
    },
    showTypeChange(type) {
      this.showType = type
      if (type == 0) {
        this.typeAnalysis()
      } else {
        this.energyTrend()
      }
    },
    // 获取设备列表
    getDeviceList() {
      // 电力数据才有设备列表
      if (this.energyId !== 'SU035') {
        this.deviceList = []
        this.pagination.total = 0
        return
      }
      const dataType = {
        1: 'day',
        2: 'day',
        3: 'month',
        4: 'year',
        5: 'day'
      }
      const params = {
        page: this.pagination.pageNo,
        pageSize: this.pagination.pageSize,
        timeCategory: dataType[this.statisticalType],
        startDate: this.statisticalDate[0],
        endDate: this.statisticalDate[1],
        gyToken: this.kgceToken,
        // spaceId: this.selectEmodelId,
        spaceId: this.spaceLevel == 4 ? this.emodelId : this.selectEmodelId,
        dataType: 'day',
        order: this.tableOrder || 'asc'
        // modelId: this.spaceLevel == 4 ? this.selectEmodelId : ''
      }
      this.deviceTableLoading = true
      GetEnergyMeteringList(params)
        .then((res) => {
          this.deviceTableLoading = false
          if (res.data.code == 200) {
            const resData = res.data.data
            // if (resData?.list.length && this.selectEmodelId) {
            if (resData?.list.length && this.spaceLevel == 4 && this.selectEmodelId) {
              GetModelFormulaPoint({ modelId: this.selectEmodelId }, this.kgceToken).then((res) => {
                if (res.data.code == 200) {
                  const list = res.data.data
                  this.deviceList = resData.list.filter((e) => e.paraList.some((item) => list.some((point) => point.measurePointNo == item.harvesterId)))
                  this.pagination.total = this.deviceList.length
                }
              })
            } else {
              if (this.pagination.pageNo === 1) {
                this.deviceList = []
                this.$nextTick(() => {
                  this.$refs.srollTable.bodyWrapper.scrollTop = 0
                })
              }
              this.deviceList = this.deviceList.concat(resData.list)
              this.pagination.total = resData.count
            }
          } else {
            this.deviceList = []
            this.pagination.total = 0
          }
        })
        .catch(() => {
          this.deviceTableLoading = false
        })
    },
    tableLoadMore() {
      if (this.pagination.total > this.pagination.pageNo * this.pagination.pageSize) {
        this.pagination.pageNo++
        this.getDeviceList()
      }
    },
    sortTableEvent({ prop, order }) {
      if (order === 'descending') {
        this.tableOrder = 'desc'
      } else {
        this.tableOrder = 'asc'
      }
      this.pagination.pageNo = 1
      this.getDeviceList()
    },
    setWPFBgShow() {
      window.chrome.webview.hostObjects.sync.bridge.DateMask(true)
    },
    setWPFBgHide() {
      window.chrome.webview.hostObjects.sync.bridge.DateMask(false)
    },
    // 时间类型切换
    dataTypeCommand(val) {
      const date = {
        1: [moment().format('YYYY-MM-DD') + ' 00:00:00', moment().format('YYYY-MM-DD') + ' 23:59:59'],
        2: [moment().startOf('isoWeek').format('YYYY-MM-DD') + ' 00:00:00', moment().endOf('isoWeek').format('YYYY-MM-DD') + ' 23:59:59'],
        3: [moment().startOf('month').format('YYYY-MM-DD') + ' 00:00:00', moment().endOf('month').format('YYYY-MM-DD') + ' 23:59:59'],
        4: [moment().startOf('year').format('YYYY-MM-DD') + ' 00:00:00', moment().endOf('year').format('YYYY-MM-DD') + ' 23:59:59'],
        5: []
      }
      this.statisticalDate = date[val]
      this.statisticalType = val
      if (val != 5) {
        this.setWPFdata()
      }
    },
    // 区域排行
    regionalRanking(emodelId = '') {
      const dataType = {
        1: 'day',
        2: 'day',
        3: 'month',
        4: 'year',
        5: 'day'
      }
      const param = {
        aggregation: 'sum',
        valueName: 'value',
        groupBy: 'emodelId',
        timeCategory: dataType[this.statisticalType],
        startDate: this.statisticalDate[0],
        endDate: this.statisticalDate[1],
        emodelId: this.spaceLevel == 4 ? emodelId : this.emodelIdAll, // 模型id
        energyId: this.menuList[this.menuActive].children.length ? this.menuList[this.menuActive].children[this.menuTitleActive].id : this.energyId
      }
      this.getModelEnergyDataList(param)
        .then((res) => {
          let arr = []
          arr = res.data.data
          var newArr = []
          if (arr.length) {
            arr.sort((a, b) => {
              return b.value - a.value
            })
            const total = arr.reduce((sum, num) => {
              return sum + num.value
            }, 0)
            arr.forEach((item) => {
              item.ratio = (item.value / total) * 100
            })
            this.areaRankingList = arr
            this.selectEmodelId = arr[0].emodelId
            arr.forEach((item, index) => {
              newArr.push({
                sum: this.monitorData.fixedValue,
                energyName: this.showContentType,
                areaid: item.emodelId,
                areaData: item.value,
                unit: this.unitItems[this.energyId]
                // areaData: index === 0 ? 100 : ((item.value / arr[0].value) * 100).toFixed(2)
              })
            })
          } else {
            this.areaRankingList = []
            this.selectEmodelId = ''
          }
          this.pagination.pageNo = 1
          this.getDeviceList()
          try {
            window.chrome.webview.hostObjects.sync.bridge.AreaData(JSON.stringify(newArr))
          } catch (errpr) {}
        })
        .catch()
    },
    // 饼图
    electricityPieEchart(data, total) {
      const myChart = echarts.init(document.getElementById('analysis_chart'))
      const nameObj = {
        SU035: '总用电量',
        CM020: '总用水量',
        '4c78a70e0b7deb51b0a227fb2cd9196f': '总用热量',
        '66655f36bc0dc86adbaf2cab80d9dce2': '总蒸汽用量'
      }
      let option
      if (data.length) {
        option = {
          // backgroundColor: '',
          color: data.map((v) => v.color),
          title: {
            text: '{a|' + this.filterValue(total) + '}{c|' + this.unitItems[this.energyId] + '}',
            subtext: nameObj[this.energyId],
            x: 'center',
            y: '40%',
            textStyle: {
              rich: {
                a: {
                  fontSize: 13,
                  color: '#FFFFFF'
                },
                c: {
                  fontSize: 12,
                  color: '#FFFFFF'
                }
              }
            },
            subtextStyle: {
              color: 'rgba(255,255,255,0.6)',
              fontSize: 12
            }
          },
          tooltip: {
            show: false
          },
          series: [
            {
              type: 'pie',
              name: 'TypeB', // 内层细圆环2
              radius: ['46%', '47%'],
              hoverAnimation: false,
              clockWise: false,
              itemStyle: {
                color: 'rgba(255,255,255,0.2)'
              },
              label: {
                show: false
              },
              data: [100]
            },
            {
              type: 'pie',
              name: 'TypeA', // 最外层细圆环
              hoverAnimation: false,
              clockWise: false,
              radius: ['63%', '64%'],
              itemStyle: {
                color: 'rgba(255,255,255,0.2)'
              },
              label: {
                show: false
              },
              data: [100]
            },
            {
              name: 'content',
              type: 'pie',
              clockWise: false,
              radius: ['50%', '60%'],
              hoverAnimation: true,
              data: data,
              label: {
                show: false
              },
              itemStyle: {
                normal: {
                  borderWidth: 2, // 间距的宽度
                  borderColor: 'rgba(40,48,65,1)' // 背景色
                }
              }
            },
            {
              // 内圆
              type: 'pie',
              radius: '42%',
              center: ['50%', '50%'],
              hoverAnimation: false,
              itemStyle: {
                color: 'rgba(133,145,206,0.15)'
              },
              label: {
                show: false
              },
              tooltip: {
                show: false
              },
              data: [100]
            }
          ]
        }
      } else {
        option = {
          title: {
            text: '暂无数据',
            x: 'center',
            y: 'center',
            textStyle: {
              fontSize: 14,
              fontWeight: 'normal',
              color: '#999'
            }
          }
        }
      }
      myChart.off('mouseover')
      myChart.off('mouseout')
      myChart.clear()
      myChart.setOption(option)
      // 随着屏幕大小调节图表
      window.addEventListener('resize', () => {
        myChart.resize()
      })
      myChart.on('mouseover', (e) => {
        if (e.seriesName == 'content') {
          option.title.text = '{a|' + this.filterValue(data[e.dataIndex].value) + '}{c|' + data[e.dataIndex].unit + '}'
          option.title.subtext = this.filterName(data[e.dataIndex].name)
          myChart.setOption(option)
        }
      })
      myChart.on('mouseout', (e) => {
        if (e.seriesName == 'content') {
          option.title.text = '{a|' + this.filterValue(total) + '}{c|' + this.unitItems[this.energyId] + '}'
          option.title.subtext = nameObj[this.energyId]
          myChart.setOption(option)
        }
      })
    },
    // 类型分析
    typeAnalysis() {
      const dataType = {
        1: 'day',
        2: 'day',
        3: 'month',
        4: 'year',
        5: 'day'
      }
      const param = {
        aggregation: 'sum',
        valueName: 'value',
        groupBy: 'energyId',
        timeCategory: dataType[this.statisticalType],
        startDate: this.statisticalDate[0],
        endDate: this.statisticalDate[1],
        emodelId: this.emodelId, // 模型id
        energyId: this.menuList[this.menuActive].children.length
          ? this.menuTitleActive === 0
            ? this.menuList[this.menuActive].children[this.menuTitleActive].allId
            : this.menuList[this.menuActive].children[this.menuTitleActive].id
          : this.energyId
      }
      const color = ['#E88D6B', '#FFCA64', '#5E89EE', '#0A84FF', '#61E29D']
      let newData = []
      this.getModelEnergyDataList(param)
        .then((res) => {
          newData = res.data.data.map((v, i) => {
            return {
              value: v.value,
              name: v.energyName,
              color: color[i],
              unit: v.energyUnitEn
            }
          })
          this.typeAnalysisList = newData
          const total = newData.reduce((prev, cur) => {
            return prev + Number(cur.value) ?? 0
          }, 0)
          setTimeout(() => {
            this.$nextTick(() => {
              this.electricityPieEchart(newData, total)
            })
          }, 100)
        })
        .catch()
    },
    // 实时监测
    realTimeMonitor() {
      const dataType = {
        1: 'day',
        2: 'day',
        3: 'month',
        4: 'year',
        5: 'day'
      }
      const params = {
        emodelId: this.emodelId, // 模型id
        energyId: this.menuList[this.menuActive].children.length ? this.menuList[this.menuActive].children[this.menuTitleActive].id : this.energyId,
        timeCategory: dataType[this.statisticalType],
        startDate: this.statisticalDate[0],
        endDate: this.statisticalDate[1]
        // timeCategory: '' // 数据类型 （hour小时数据 day日数据 week周数据  month月数据 year年数据）
      }
      this.getModelEnergyDataList(params)
        .then((res) => {
          const resData = res.data.data
          if (resData.length) {
            this.monitorData = {
              value: this.getListFieldSum(resData, 'value'), // 当前值
              fixedValue: this.getListFieldSum(resData, 'value', false, 'toFixed'), // 当前值
              momValueRatio: this.getListFieldSum(resData, 'momValueRatio', resData.length > 1, 'toFixed'), // 环比比率
              yoyValueRatio: this.getListFieldSum(resData, 'yoyValueRatio', resData.length > 1, 'toFixed'), // 同比比率
              yoyValue: this.getListFieldSum(resData, 'yoyValue', resData.length > 1), // 同比值
              momValue: this.getListFieldSum(resData, 'momValue', resData.length > 1), // 环比值
              emodelName: resData[0].emodelName ?? '', // 模型名称
              valueCost: this.getListFieldSum(resData, 'valueCost') // 费用总数
            }
          } else {
            this.monitorData = {
              value: 0,
              fixedValue: 0,
              momValueRatio: 0,
              yoyValueRatio: 0,
              yoyValue: 0,
              momValue: 0,
              emodelName: '',
              valueCost: 0
            }
          }
          if (this.spaceLevel == 4) {
            this.getTreeAndPathAndDetail()
          } else {
            this.regionalRanking()
          }
          setTimeout(() => {
            this.$nextTick(() => {
              this.steStatEchart(80)
            })
          }, 100)
        })
        .catch()
    },
    getListFieldSum(list, field, isRatio = false, type = '') {
      if (isRatio) return 0
      const sum = list.reduce((prev, cur) => {
        return prev + Number(cur[field]) ?? 0
      }, 0)
      return this.filterValue(sum, type)
    },
    // 进度图
    steStatEchart(value) {
      const getchart = echarts.init(document.getElementById('stat_chart'))
      const imgSrc = {
        SU035: dianIcon,
        CM020: shuiIcon,
        '4c78a70e0b7deb51b0a227fb2cd9196f': reIcon,
        '66655f36bc0dc86adbaf2cab80d9dce2': qiIcon
      }
      const option = {
        graphic: {
          elements: [
            {
              type: 'image',
              z: 3,
              style: {
                image: imgSrc[this.energyId] || dianIcon,
                width: 28,
                height: 28
              },
              left: 'center',
              top: '33%',
              position: [100, 100]
            }
          ]
        },
        title: [
          {
            text: this.showContentType,
            x: 'center',
            top: '55%',
            textStyle: {
              color: '#FFFFFF',
              fontSize: 14,
              fontWeight: '100'
            }
          }
        ],
        polar: {
          radius: ['100%', '50%'],
          center: ['50%', '50%']
        },
        angleAxis: {
          max: 100,
          show: false
        },
        radiusAxis: {
          type: 'category',
          show: true,
          axisLabel: {
            show: false
          },
          axisLine: {
            show: false
          },
          axisTick: {
            show: false
          }
        },
        series: [
          {
            name: '',
            type: 'bar',
            //   roundCap: true,
            barWidth: 10,
            showBackground: true,
            backgroundStyle: {
              color: 'rgba(255, 255, 255, .3)'
            },
            data: [value],
            coordinateSystem: 'polar',
            itemStyle: {
              normal: {
                color: '#FFCA64'
              }
            }
          },
          {
            name: '',
            type: 'pie',
            startAngle: 90,
            radius: ['88%'],
            hoverAnimation: false,
            center: ['50%', '50%'],
            itemStyle: {
              color: 'rgba(66, 66, 66, 0)',
              borderWidth: 1,
              borderColor: 'rgba(255, 255, 255, 0.2)'
            },
            data: [100]
          },
          {
            name: '',
            type: 'pie',
            startAngle: 90,
            radius: ['62%'],
            hoverAnimation: false,
            center: ['50%', '50%'],
            itemStyle: {
              color: 'rgba(255,255,255,0.05)',
              borderWidth: 1,
              borderColor: 'rgba(255, 255, 255, 0.2)'
            },
            data: [100]
          }
        ]
      }
      getchart.clear()
      getchart.setOption(option)
      // 随着屏幕大小调节图表
      window.addEventListener('resize', () => {
        getchart.resize()
      })
    },
    // 获取模型能源数据列表
    getModelEnergyDataList(params) {
      return new Promise((resolve, reject) => {
        GetModelEnergyDataList(params, this.kgceToken)
          .then((res) => {
            if (res.data.code === 200) {
              if (res.data.data.length) {
                res.data.data.forEach((item) => {
                  this.unitItems[this.energyId] = item.energyUnitEn
                })
              }
              resolve(res)
            } else {
              reject(res)
            }
          })
          .catch((err) => {
            reject(err)
          })
      })
    },
    // 根据建筑id获取科室列表
    getTreeAndPathAndDetail() {
      GetTreeAndPathAndDetail({ rootNodeId: this.emodelId, parentMean: false }, this.kgceToken).then((res) => {
        if (res.data.code === 200) {
          if (res.data.data[0].children) {
            this.regionalRanking(res.data.data[0].children.map((v) => v.modelId).join(','))
          } else {
            this.regionalRanking(this.emodelId)
          }
        }
      })
    },
    // wpf数据传输
    setWPFdata(val) {
      if (val && val.length) {
        this.statisticalDate = [val[0] + ' 00:00:00', val[1] + ' 23:59:59']
      }
      if (!this.statisticalDate.length) {
        this.statisticalType = 1
        this.statisticalDate = [moment().format('YYYY-MM-DD') + ' 00:00:00', moment().format('YYYY-MM-DD') + ' 23:59:59']
      }
      this.realTimeMonitor()
      this.showTypeChange(this.showType)
    },
    // 选中 二级菜单
    selectMenuTitle(index) {
      this.menuTitleActive = index
      this.setWPFdata()
    },
    // 选中 一级菜单
    activeEnergyEvent(index) {
      this.activeEnergyUnit = this.menuList[index].info.unitNameEn
      this.tabChildren = this.menuList[index].children
      this.showContentType = this.menuList[index].name
      try {
        window.chrome.webview.hostObjects.sync.bridge.AreaDataSwitch(JSON.stringify({ type: this.showContentType }))
      } catch (errpr) {}
      this.menuActive = index
      this.energyId = this.menuList[index].id
      this.selectMenuTitle(0)
    },
    // 获取能源及分类的树结构
    getEnergyAndClassifyTree() {
      GetEnergyAndClassifyTree({}, this.kgceToken).then((res) => {
        if (res.data.code === 200) {
          res.data.data.forEach((item) => {
            if (item.children.length == 1) {
              item.children[0].allId = item.id
            } else if (item.children.length >= 2) {
              item.children.unshift({ id: item.id, allId: item.children.map((v) => v.id).join(','), name: '总览' })
              item.children = item.children.filter((v) => v.name !== '设备用电')
            }
          })
          this.menuList = res.data.data.filter((item) => item.name !== '综合能耗' && item.name !== '蒸汽')
          let index = 0
          if (this.$route.query.energyType) {
            let obj = JSON.parse(this.$route.query.energyType)
            index = this.menuList.findIndex(el => {
              return el.name === obj.type
            })
          }
          this.activeEnergyEvent(index)
        }
      })
    },
    // 能耗登录
    kgceLogin() {
      KgceLogin(KECG_ACCOUNT).then((res) => {
        if (res.data.code === 200) {
          this.kgceToken = res.data.result.token
          this.getEnergyAndClassifyTree()
        }
      })
    }
  }
}
</script>
<style lang="scss" scoped>
.energy {
  width: 100%;
  height: 100%;
  background: center;
  .energy-main {
    width: 24%;
    height: 100%;
    margin: 0 0 3.125rem auto;
    background: url('~@/assets/images/qhdsys/bg-mask.png') no-repeat;
    background-size: 100% 100%;
    box-sizing: border-box;
    padding: 0 24px 0 36px;
    position: relative;
    transition: width 0.3s linear;
    display: flex;
    flex-direction: column;
    .main-scroll {
      flex: 1;
      overflow: auto;
    }
    .main-scroll::-webkit-scrollbar {
      display: none; /* Chrome Safari */
    }
    .main-search {
      padding: 12px 0px;
      .search-tabs {
        display: flex;
        .tabs-item {
          cursor: pointer;
          padding: 10px 0;
          text-align: center;
          font-size: 16px;
          font-weight: 400;
          color: #a4afc1;
          line-height: 19px;
          overflow: hidden;
          background: linear-gradient(360deg, #334572 0%, rgba(38, 49, 79, 0.14) 57%, rgba(36, 46, 73, 0) 100%);
          border-left: 1px solid;
          border-bottom: 1px solid;
          border-image: linear-gradient(180deg, rgba(113, 125, 168, 0), rgba(113, 128, 168, 1)) 1 1;
          font-family: HarmonyOS_Sans_SC_Medium;
        }
        .tabs-item:last-child {
          border-right: 1px solid;
        }
        .tabs-active {
          position: relative;
          color: #b0e3fa;
          background: linear-gradient(360deg, #3a668e 0%, rgba(36, 46, 73, 0) 100%);
        }
        .tabs-active::after {
          content: '';
          position: absolute;
          left: 50%;
          transform: translateX(-50%);
          width: 36px;
          height: 3px;
          bottom: 10px;
          background: #6ee1f9;
          filter: blur(10.87px);
        }
      }
      .search-tags {
        padding: 4px 0px 12px 0px;
        flex-wrap: wrap;
        display: flex;
        background: rgba(133, 145, 206, 0.15);
        .tags-item {
          cursor: pointer;
          margin-left: 8px;
          margin-top: 8px;
          padding: 6px 12px;
          background: rgba(255, 255, 255, 0.1) linear-gradient(90deg, rgba(10, 132, 255, 0) 0%, rgba(10, 132, 255, 0) 100%);
          font-size: 14px;
          font-weight: 400;
          color: #b0e3fa;
          line-height: 14px;
          border: 1px solid transparent;
          font-family: HarmonyOS Sans SC-Regular;
        }
        .tags-active {
          background: rgba(255, 255, 255, 0.1) linear-gradient(90deg, rgba(10, 132, 255, 0.4) 0%, rgba(10, 132, 255, 0) 100%);
          border: 1px solid;
          border-image: radial-gradient(circle, rgba(171, 240, 255, 1), rgba(226, 254, 255, 1), rgba(132, 196, 203, 1), rgba(48, 151, 166, 0)) 1 1;
        }
      }
      .search-data {
        display: flex;
        background: rgba(133, 145, 206, 0.15);
        padding: 0px 10px;
        ::v-deep .el-dropdown {
          padding: 7px 6px;
          .el-dropdown-link {
            font-size: 14px;
            font-weight: 500;
            color: #8bddf5;
            line-height: 16px;
            position: relative;
            cursor: pointer;
          }
          .el-dropdown-link::after {
            content: '';
            position: absolute;
            width: 1px;
            height: 12px;
            background: rgba(133, 145, 206, 0.5);
            top: 50%;
            right: -6px;
            transform: translateY(-50%);
          }
        }
        ::v-deep .datePickerInput {
          flex: 1;
          padding: 8px 10px;
          height: 16px;
          box-sizing: content-box;
          background: none;
          border: none;
          .el-input__icon,
          .el-range-separator {
            line-height: 16px;
            color: #b0e3fa;
          }
          .el-range-input {
            background: none;
            color: #a4afc1;
          }
        }
      }
    }
    .main-stat {
      padding: 12px 0px 32px 0px;
      .main-module-content {
        display: flex;
        flex-direction: column;
        padding-top: 8px;
      }
      .module-content {
        margin-top: 8px;
        display: flex;
        flex: 1;
        background: rgba(255, 255, 255, 0.05);
        .content-bottom {
          padding: 10px 0px;
          line-height: 16px;
        }
        .bottom-name {
          font-size: 14px;
          font-weight: 400;
          color: #b0e3fa;
        }
        .bottom-value {
          font-size: 14px;
          font-weight: bold;
          color: #ffffff;
          margin-left: 8px;
          margin-right: 3px;
        }
        .bottom-unit {
          font-size: 12px;
          font-weight: 400;
          color: #ffffff;
        }
      }
      .content-left {
        width: 40%;
      }
      .content-right {
        width: 60%;
        .right-item {
          padding: 20px 26px 30px 16px;
          // background: linear-gradient(90deg, rgba(255, 255, 255, .1) 0%, rgba(255, 255, 255, 0) 100%);
        }
        .item-line {
          height: 1px;
          width: 100%;
          margin: 8px 0px;
          background: linear-gradient(90deg, rgba(34, 143, 225, 1) 0%, rgba(34, 143, 225, 0) 100%);
        }
        .item-head {
          display: flex;
          align-items: end;
        }
        .item-title {
          font-size: 14px;
          font-weight: 400;
          color: #b0e3fa;
        }
        .item-value {
          margin-left: 8px;
          span:first-child {
            font-size: 18px;
            font-weight: bold;
            color: #ffca64;
          }
          span:last-child {
            margin-left: 3px;
            font-size: 12px;
            font-weight: 400;
            color: #ffffff;
            line-height: 14px;
          }
        }
        .item-ratio {
          font-size: 12px;
          i {
            font-size: 14px;
            margin-left: 6px;
            margin-right: 4px;
          }
          .item-ratio-name {
            color: #ffffff;
          }
        }
      }
    }
    .main-module {
      .main-module-title {
        padding: 7px 0px 7px 38px;
        background: url('~@/assets/images/qhdsys/bg-bt.png') no-repeat center / 100% 100%;
        display: flex;
        justify-content: space-between;
        .title-left {
          display: flex;
          .left-text {
            cursor: pointer;
            font-size: 16px;
            font-family: HarmonyOS Sans SC-Bold;
            color: #a6afbf;
            line-height: 30px;
          }
          .text-active {
            font-weight: bold;
            color: #ffffff;
            text-shadow: 0px 0px 9px #158eff;
          }
        }
      }
      .main-module-content {
        height: calc(100% - 44px);
      }
      ::v-deep .table-center-transfer {
        border: 0 !important;
      }
    }
    .main-analysis {
      display: flex;
      .analysis-content-chart {
        height: 100%;
        width: 50%;
      }
      .analysis-content-list {
        height: 100%;
        width: 50%;
        padding: 16px 16px 16px 0;
        display: flex;
        flex-direction: column;
        box-sizing: border-box;
        justify-content: space-evenly;
        .list-item {
          position: relative;
          display: flex;
          align-items: center;
          padding: 4px 0px 4px 27px;
          background: rgba(42, 54, 68, 0.4);
          .list-item-color {
            width: 7px;
            height: 7px;
            border-radius: 50%;
            position: absolute;
            top: 50%;
            transform: translateY(-50%);
            left: 10px;
          }
          .list-item-name {
            font-size: 12px;
            font-weight: 400;
            color: #a4acb9;
            line-height: 14px;
            min-width: 40px;
          }
          .list-item-value {
            font-size: 16px;
            font-weight: 400;
            color: #ffffff;
            line-height: 19px;
          }
        }
        .list-item::before {
          content: '';
          width: 2px;
          height: 2px;
          position: absolute;
          background: #9bb8c7;
          left: 0;
          top: 0;
        }
      }
    }
    .main-ranking {
      padding: 10px 0px 0px 0px;
      box-sizing: border-box;
      position: relative;
      width: 100%;
      max-height: 100%;
      box-sizing: border-box;
      overflow: auto;
      flex: 1;
      .list-item {
        cursor: pointer;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-bottom: 8px;
        padding: 4px 16px 4px 4px;
        background: linear-gradient(90deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0) 100%);
        border-radius: 99px;
        .item-img {
          width: 34px;
          height: 34px;
        }
        .item-content {
          padding-left: 16px;
          flex: 1;
        }
        .content-text {
          display: flex;
          justify-content: space-between;
          font-size: 14px;
          color: #fff;
        }
        .content-line {
          background: rgba(255, 255, 255, 0.1);
          padding: 2px;
          width: 100%;
          margin-top: 6px;
          span {
            display: block;
            height: 4px;
            background: #0a84ff;
          }
        }
      }
      .item-active {
        background: rgba(255, 202, 100, 0.12);
        .content-text {
          color: #ffca64;
        }
        .content-line {
          span {
            background: #ffca64;
          }
        }
      }
      .list-item:last-child {
        margin-bottom: 0px;
      }
      .nodeData {
        font-size: 14px;
        color: #999;
        position: absolute;
        left: 50%;
        top: 50%;
        transform: translate(-50%, -50%);
      }
    }
    .main-ranking::-webkit-scrollbar {
      display: none; /* Chrome Safari */
    }
    .trend_chart {
      width: 100%;
      height: 100%;
    }
  }
}
</style>
