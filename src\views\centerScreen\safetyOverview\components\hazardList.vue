<template>
  <div class="main">
    <el-dialog :modal="false" :visible.sync="dialogShow" custom-class="mainDialog main" append-to-body :close-on-click-modal="false" :before-close="closeDialog" class="personDialog">
      <template slot="title">
        <span class="dialog-title">{{ type === 'danger' ? '隐患' : '风险' }}清单</span>
      </template>
      <div class="dialog-content">
        <el-table
          @row-dblclick="selectConfigRowData"
          :data="workOrderTableData"
          height="calc(100% - 40px)"
          :cell-style="$tools.setCell(3)"
          :header-cell-style="$tools.setHeaderCell(3)"
          style="width: 100%"
          v-loading="tableLoading"
          element-loading-background="rgba(0, 0, 0, 0.2)"
        >
          <el-table-column label="序号" type="index" width="100" />
          <el-table-column v-for="item in column[type === 'danger' ? 0 : 1]" :key="item.id" :prop="item.prop" show-overflow-tooltip :label="item.label" />
          <el-table-column show-overflow-tooltip label="操作">
            <template slot-scope="scope">
              <div class="operationBtn">
                <span @click="selectConfigRowData(scope.row)">详情</span>
              </div>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </el-dialog>
    <template v-if="hiddenDangerDetailsListShow">
      <el-dialog v-dialogDrag :modal="false" :visible.sync="hiddenDangerDetailsListShow" custom-class="detailDialog main" :close-on-click-modal="false" :before-close="hiddenDangerDetailCloseDialog">
        <template slot="title">
          <span class="dialog-title">隐患详情</span>
        </template>
        <hiddenDangerDetailsList :rowData="detailObj" />
      </el-dialog>
    </template>
    <template v-if="hiddenRiskDetailsListShow">
      <el-dialog
        v-dialogDrag
        :modal="false"
        :visible.sync="hiddenRiskDetailsListShow"
        :fullscreen="dialogFullScreen"
        :class="[dialogFullScreen ? 'fullscreen' : 'no_fullscreen']"
        :custom-class="[dialogFullScreen ? 'fullscreen' : 'detailDialog', 'main']"
        :close-on-click-modal="false"
        :before-close="hiddenRiskDetailCloseDialog"
      >
        <template slot="title">
          <span class="dialog-title">风险点详情</span>
          <div class="custom_dialog_menu" @click="dialogFullScreen = !dialogFullScreen">
            <i class="el-icon-full-screen"></i>
          </div>
        </template>
        <riskPointDetailsList :rowData="detailObj" />
      </el-dialog>
    </template>
  </div>
</template>
<script>
import { GetHiddenDangersList, GetRiskPageList } from '@/utils/centerScreenApi'
import hiddenDangerDetailsList from './hiddenDangerDetailsList.vue'
import riskPointDetailsList from './riskPointDetailsList.vue'
export default {
  name: 'workOrderCenterList',
  components: {
    hiddenDangerDetailsList,
    riskPointDetailsList
  },
  props: {
    type: {
      type: String,
      default: ''
    },
    dialogShow: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      dialogFullScreen: false,
      hiddenDangerDetailsListShow: false,
      hiddenRiskDetailsListShow: false,
      detailObj: {},
      workOrderTableData: [],
      tableLoading: false,
      column: [
        [
          { label: '隐患单号', prop: 'questionCode' },
          { label: '隐患状态', prop: 'flowType' },
          { label: '登记部门', prop: 'createByDeptName' },
          { label: '登记时间', prop: 'createTime' },
          { label: '登记人', prop: 'createPersonName' },
          { label: '隐患等级', prop: 'riskName' },
          { label: '要求整改完成时间', prop: 'rectificationPlanTime' },
          { label: '责任部门', prop: 'dutyDeptName' }
        ],
        [
          { label: '风险点编号', prop: 'riskCode' },
          { label: '风险点名称', prop: 'riskName' },
          { label: '风险点位置', prop: 'riskPlace' },
          { label: '风险类型', prop: 'riskTypeName' },
          { label: '风险等级', prop: 'riskLevelName' },
          { label: '责任部门', prop: 'taskTeamName' },
          { label: '责任人', prop: 'responsiblePersonName' },
          { label: '应急联系电话', prop: 'urgentContactPhone' },
          { label: '登记时间', prop: 'createTime' }
        ]
      ]
    }
  },
  watch: {},
  created() {},
  mounted() {
    // this.getWorkOrderTableData()
  },
  methods: {
    hiddenDangerDetailCloseDialog() {
      this.hiddenDangerDetailsListShow = false
      try {
        window.chrome.webview.hostObjects.sync.bridge.ShowDialog(false)
      } catch (error) {}
    },
    hiddenRiskDetailCloseDialog() {
      this.hiddenRiskDetailsListShow = false
      try {
        window.chrome.webview.hostObjects.sync.bridge.GetSwichFloorIsShow(false)
        window.chrome.webview.hostObjects.sync.bridge.ShowDialog(false)
      } catch (error) {}
    },
    selectConfigRowData(row) {
      this.detailObj = { id: row.id }
      if (this.type === 'danger') {
        this.hiddenDangerDetailsListShow = true
        // this.$router.push({
        //   path: '/hiddenDangerDetails',
        //   query: { id: row.id }
        // })
      } else {
        this.hiddenRiskDetailsListShow = true
        // this.$router.push({
        //   path: '/riskPointDetails',
        //   query: { id: row.id }
        // })
      }
      try {
        window.chrome.webview.hostObjects.sync.bridge.GetSwichFloorIsShow(true)
        window.chrome.webview.hostObjects.sync.bridge.ShowDialog(true)
      } catch (error) {}
    },
    // 获取列表
    getWorkOrderTableData(params) {
      this.tableLoading = true
      if (this.type === 'danger') {
        GetHiddenDangersList({ ...params, pageNo: 1, pageSize: 999 }).then((res) => {
          const data = res.data
          this.tableLoading = false
          if (data.code === '200') {
            this.workOrderTableData = data.data.list
          } else {
            this.$message({
              message: data.data.msg,
              type: 'warning'
            })
          }
        })
      } else {
        GetRiskPageList({ ...params, pageNo: 1, pageSize: 999 }).then((res) => {
          const data = res.data
          this.tableLoading = false
          if (data.code === '200') {
            this.workOrderTableData = data.data.list
          } else {
            this.$message({
              message: data.data.msg,
              type: 'warning'
            })
          }
        })
      }
    },
    // 取消按钮
    closeDialog() {
      this.$emit('configCloseDialog')
    }
  }
}
</script>

<style lang="scss" type="text/css" scoped>
.personDialog {
  pointer-events: none;
  ::v-deep .el-image__error {
    background: center;
  }
}
.custom_dialog_menu {
  position: absolute;
  top: 1.25rem;
  right: 3rem;
  cursor: pointer;
  height: 20px;
  line-height: 20px;
  width: 20px;
  i {
    text-align: center;
    color: #4c82df;
    font-size: 1rem;
  }
}
::v-deep .mainDialog {
  width: 98%;
  height: 40vh;
  margin-top: 54vh !important;
  background: url('@/assets/images/qhdsys/bottom-table-bg.png') no-repeat;
  background-size: 100% 100%;
  padding: 0 35px;
  pointer-events: auto;
  .dialog-title {
    display: inline-block;
    width: 100%;
    text-align: center;
    transform: translateY(-6px);
    color: #cbdeed;
    &::before {
      display: none;
    }
  }
  .el-dialog__body {
    padding: 0;
    height: calc(100% - 50px);
    .dialog-content {
      width: 100%;
      height: 100%;
      .el-table {
        border: none !important;
        .redBg {
          background: #341f51;
        }
      }
    }
  }
}
::v-deep .detailDialog {
  width: 60%;
  height: 80vh;
  margin-top: 7vh !important;
  background: url('@/assets/images/table-bg.png') no-repeat;
  background-size: 100% 100%;
  pointer-events: auto;
  .el-dialog__body {
    padding: 10px 50px;
    height: calc(100% - 60px);
    max-height: 80vh;
  }
}
::v-deep .fullscreen {
  width: 100%;
  // background: #031553;
  .el-dialog__body {
    max-height: 100vh;
  }
  // height: 0vh;
}
</style>
