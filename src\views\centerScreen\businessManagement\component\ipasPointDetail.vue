<template>
  <div class="content-ipas">
    <div class="title" @click="goBack"><i class="el-icon-arrow-left"></i>返回</div>
    <div class="form-content">
      <div class="form-title">巡检记录详情</div>
      <div class="form-detail">
        <div class="list_content">
          <div class="linear-g">
            <span class="linear-g-span1">任务点</span>
            <span>{{ taskPoint.taskPointName }}</span>
          </div>
        </div>
        <div class="list_content">
          <div class="linear-g">
            <span class="linear-g-span1">巡检内容</span>
          </div>
          <el-table :data="tableData" :cell-style="$tools.setCell(3)" :header-cell-style="$tools.setHeaderCell(3)" style="width: 100%; margin-bottom: 10px">
            <el-table-column type="index" label="序号" width="100" align="center"></el-table-column>
            <el-table-column
              v-for="(column, index) in tableColumn"
              :key="index"
              :prop="column.prop"
              :label="column.label"
              align="center"
              show-overflow-tooltip
            >
              <template slot-scope="scope">
                <div v-if="column.formatter">
                  {{ column.formatter(scope) }}
                </div>
                <div v-else>
                  {{ scope.row[column.prop] }}
                </div>
              </template>
            </el-table-column>
          </el-table>
        </div>
        <!-- 巡检执行 -->
        <div class="list_content">
          <div class="linear-g">
            <span class="linear-g-span1">巡检执行</span>
          </div>
          <div class="plan-content">
            <ul class="item-row">
              <li class="width30">
                <span class="li-first-span">巡检情况</span><span class="li-last-span">{{ excute.carryOutFlag == '0' ? '未巡' : excute.carryOutFlag == '1' ? '已巡' : '' }}</span>
              </li>
              <li class="width30">
                <span class="li-first-span">应巡部门/人员</span><span class="li-last-span">{{ excute.distributionTeamName || excute.planPersonName }}</span>
              </li>
              <li class="width30">
                <span class="li-first-span">执行人员</span><span class="li-last-span">{{ excute.implementPersonName }}</span>
              </li>
            </ul>
            <ul class="item-row">
              <li class="width30">
                <span class="li-first-span">实际巡检时间</span><span class="li-last-span">{{ excute.excuteTime }}</span>
              </li>
              <li class="width30">
                <span class="li-first-span">定位状态</span><span class="li-last-span">{{ excute.spyScan }}</span>
              </li>
            </ul>
          </div>
        </div>
        <!-- 巡检结果 -->
        <div class="list_content">
          <div class="linear-g">
            <span class="linear-g-span1">巡检结果</span>
          </div>
          <div class="plan-content">
            <ul class="item-row">
              <li class="width90">
                <span class="li-first-span">结果</span
                ><span class="li-last-span">{{ result.state == '2' ? '合格' : result.state == '3' ? '不合格' : result.state == '4' ? '异常报修' : '未巡检' }}</span>
              </li>
            </ul>
            <ul class="item-row">
              <li class="width90">
                <span class="li-first-span">描述</span><span class="li-last-span">{{ result.desc }}</span>
              </li>
            </ul>
            <ul class="item-row">
              <li class="width90">
                <span class="li-first-span">语音</span>
                <div v-if="result.callerTapeUrl" id="audio-box">
                  <audio ref="player" style="height: 40px" :src="result.callerTapeUrl" preload="true" controls="controls"></audio>
                  <!-- <audio controls>
                      <source :src="workOrderDetail.olgTaskManagement.callerTapeUrl" />
                    </audio> -->
                  <!-- src="https://sinomis.oss-cn-beijing.aliyuncs.com/ioms/ZKYXYY/question/2021/06/24/mp3/20210624104642208466.mp3?Expires=1649988786&OSSAccessKeyId=LTAIeUfTgrfT5j7Y&Signature=QI12blGWwn1KzARBWOu7TdeD0rI%3D" -->
                </div>
                <span v-else>暂无</span>
              </li>
            </ul>
            <ul class="item-row">
              <li class="width90">
                <span class="li-first-span">图片</span>
                <p v-if="result.attachmentUrlList">
                  <span v-for="(img, index) in result.attachmentUrlList" :key="index">
                    <el-image style="width: 100px; height: 100px; margin-right: 15px" :src="img" :preview-src-list="[img]"> </el-image>
                  </span>
                </p>
                <span v-else>暂无</span>
              </li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { getIPASpointDetail } from '@/utils/centerScreenApi'
export default {
  name: 'ipasPointDetail',
  props: {
    detailId: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      tableData: [],
      tableColumn: [],
      dailyColumn: [
        {
          label: '巡检内容',
          prop: 'content'
        },
        {
          label: '标准要求',
          prop: 'standardRequirements'
        },
        {
          label: '巡检依据',
          prop: 'inspectionBasis'
        },
        {
          label: '巡检结果',
          prop: 'normal',
          formatter: (scope) => {
            const row = scope.row
            return row.normal === '0' ? '正常' : row.normal === '1' ? '异常' : ''
          }
        }
      ],
      majorColumn: [
        {
          label: '巡检项目',
          prop: 'detailName'
        },
        {
          label: '巡检要点',
          prop: 'content'
        },
        {
          label: '巡检内容',
          prop: '巡检内容',
          formatter: (scope) => {
            const row = scope.row
            if (row.isNum === '0') {
              return `正常值：${row.rangeStart}-${row.rangeEnd} ${row.einheitName}` + row.contentStandard ? `（当前${row.contentStandard} ${row.einheitName}）` : ''
            } else if (row.isNum === '1') {
              return '无'
            } else if (row.isNum === '2' || row.isNum === '3') {
              return row.contentStandard || '无'
            }
          }
        }
      ],
      taskPoint: '',
      excute: '',
      result: '',
      projectType: '' // 判断日常保养还是专业保养
    }
  },
  mounted() {
    this.getDataDetail()
  },
  methods: {
    getDataDetail() {
      const params = {
        id: this.detailId
      }
      getIPASpointDetail(params).then((res) => {
        const data = res.data
        if (data.code === '200') {
          this.projectType = data.data.project.equipmentTypeId
          this.tableColumn = this.projectType === '0' ? this.dailyColumn : this.majorColumn
          Object.assign(this, {
            taskPoint: data.data.taskPoint,
            tableData: data.data.project.projectdetailsReleaseList,
            excute: data.data.excute,
            result: data.data.result
          })
          if (this.tableData && this.tableData.length) {
            this.tableData.forEach((val, index) => {
              if (val.isNum === '3') {
                val.radioTextArr = JSON.parse(val.termJson)
                // console.log(val.radioTextArr)
              }
            })
          }
        }
      })
    },
    goBack() {
      this.$emit('retrospectCloseDialog')
    }
  }
}
</script>

<style lang="scss" scoped>
.content-ipas {
  position: absolute;
  top: 0;
  left: 0;
  // background-color: #031553;
  background: url('~@/assets/images/qhdsys/bj.png') no-repeat;
  box-sizing: border-box;
  width: calc(100vw - 24px - 24px) !important;
  height: calc(100vh - 6px - 16px - 23px) !important;
  padding: 1.625rem 2.125rem;
  z-index: 9999;
}
.title {
  cursor: pointer;
  position: absolute;
  top: 2rem;
  left: 1.5rem;
  width: 80px;
  height: 30px;
  line-height: 30px;
  text-align: center;
  color: #618ad3;
  font-size: 0.875rem;
  white-space: nowrap;
  background-image: url(~@/assets/images/peace/btn-back.png);
  background-size: 100% 100%;
  background-repeat: no-repeat;
}
.form-content {
  width: 60%;
  height: 100%;
  margin-left: 20%;
  .form-title {
    color: #dceaff;
    text-align: center;
    width: 200px;
    height: 30px;
    line-height: 30px;
    margin: 0 auto;
    background-image: url(~@/assets/images/war/title-bg.png);
    background-size: 100% 100%;
    background-repeat: no-repeat;
    font-family: 'TRENDS';
  }
  .form-detail {
    height: calc(100% - 30px);
    width: 100%;
    padding: 10px 25px;
    overflow-y: scroll;
    box-sizing: border-box;
    .list_content {
      font-size: 14px;
      .linear-g {
        margin-left: 2px;
        margin-bottom: 10px;
        width: 100%;
        height: 100%;
        background: #263057;
        font-size: 13px;
        line-height: 30px;
        font-family: PingFangSC-Medium;
        color: #ffe3a6;
        border-radius: 6px;
        .linear-g-span1 {
          margin: 0 15px;
          font-weight: 600;
        }
      }
      .plan-content {
        width: calc(100% - 33px);
        // border-left: 1px solid #303758;
        margin-left: 11px;
        // padding: 20px 0px 20px 20px;
        color: #b5bacb;
        font-size: 13px;
        .item-row {
          width: 100%;
          display: flex;
          padding: 20px 0px 20px 30px;
          box-sizing: border-box;
          .width30 {
            width: 30%;
          }
          .width45 {
            width: 45%;
          }
          .width90 {
            width: 90%;
            display: flex;
          }
          ::v-deep .el-image__error,
          ::v-deep .el-image__placeholder {
            background: center;
          }
          .li-first-span {
            display: inline-block;
            width: 120px;
            // margin-right: 20px;
            font-size: 14px;
            font-family: PingFang-SC-Medium, PingFang-SC;
            font-weight: 500;
            color: #7eaef9;
          }
          .li-last-span {
            font-size: 14px;
            font-family: PingFang-SC-Medium, PingFang-SC;
            font-weight: 500;
            color: #ffffff;
            // align-items: center;
          }
          .recording-ALabel {
            color: #ffe3a6;
            font-size: 14px;
            text-decoration: none;
            i {
              margin: 0 3px 0 10px;
            }
          }
          #audio-box {
            display: flex;
          }
          #audio-box > audio {
            width: 260px;
            height: 30px;
          }
          #audio-box > a {
            width: 40px;
            text-align: center;
            background-color: #2cc7c5;
            height: 35px;
            line-height: 35px;
            color: #fff;
            border-radius: 5px;
            margin-left: 10px;
          }
        }
      }
    }
  }
}
</style>
