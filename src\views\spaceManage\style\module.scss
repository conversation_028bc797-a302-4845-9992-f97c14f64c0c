.module-container {
    .module-header {
        padding: 10px 0px;
        display: flex;
        justify-content: space-between;
        align-items: center;

        .title-left {
            display: flex;
            align-items: center;
            height: 24px;

            .title-left-icon {
                // width: 14px;
                // height: 14px;
                // background: url('../../../assets/images/peace/arrow-bottom.png') no-repeat 100%;
                width: 2px;
                height: 16px;
                background: #FFE3A6;
            }

            .title-left-text {
                padding-left: 10px;
                font-family: HarmonyOS Sans SC-Bold;
                font-size: 16px;
                font-weight: bold;
                color: #fff;
            }
        }

        .title-right {
            display: flex;
            align-items: center;

            .title-right-tag {
                font-size: 14px;
                padding: 5px 10px;
                border-right: 1px solid #7EAEF9;
                cursor: pointer;
                color: #7EAEF9;
            }

            .tag-active {
                color: #FFE3A6;
                background: url('../../../assets/images/center/light-yellow.png') no-repeat center / 100%;
            }

            .title-right-tag:last-child {
                border-right: none;
            }

            .viewMore {
                margin-right: 5px;
                font-size: 14px;
                font-family: PingFang-SC-Medium, PingFang-SC;
                color: #7eaef9;
                cursor: pointer;
            }

            .el-dropdown-link {
                cursor: pointer;
                color: #fff;

                .el-icon-caret-bottom {
                    color: #7EAEF9;
                    margin-left: 7px;
                }
            }
        }
    }

    .module-content {
        .image-box {
            width: 100%;
            height: 100%;
            object-fit: contain;
        }
    }

    ::v-deep .el-table {
        border: none !important;

        .el-table__header .el-table__cell {
            padding: 5px 0 !important;
            color: #fff;
        }

        .el-table__body {
            tr {
                background: center;
            }

            td.el-table__cell,
            th.el-table__cell.is-leaf {
                border-right: 2px solid #0a164e;
                border-bottom: 2px solid #0a164e;
                background: rgba(56, 103, 180, 0.2);
                color: #fff;
            }
        }
    }

    ::v-deep .bottom-el-table {
        border: 0 !important;

        .el-table__header-wrapper {
            .el-table__header {
                .has-gutter {
                    tr {
                        background: transparent;
                    }

                    tr th {
                        padding: 0;
                        height: 32px;
                        background: rgba(133, 145, 206, 0.15) !important;
                        border-bottom: 1px solid rgba(255, 255, 255, 0.5);

                        .cell {
                            font-size: 14px;
                            font-family: HarmonyOS Sans SC-Bold, HarmonyOS Sans SC;
                            font-weight: bold;
                            color: #8bddf5;
                        }
                    }
                }
            }
        }

        .el-table__body-wrapper {
            background: rgba(133,145,206,0.15);
            // height: 222px !important;
            overflow: hidden;
            overflow-y: auto;

            td.el-table__cell {
                border: 0 !important;
            }

            .el-table__body {
                background: transparent;

                tbody {
                    background: transparent;

                    .el-table__row {
                        // background-color: rgba(255, 255, 255, 0.2);
                        background: transparent;
                        border: 0;

                        td {
                            border: 0;
                            padding: 0;
                            height: 30px;

                            div {
                                line-height: 30px;
                                font-size: 14px;
                                font-family: HarmonyOS Sans SC-Regular, HarmonyOS Sans SC;
                                font-weight: 400;
                                color: #ffffff;
                            }
                        }
                    }

                    .el-table__row:nth-child(2n - 1) {
                        background: rgba(168, 172, 171, 0.08);
                    }

                    .el-table__row:hover {
                        border: 0;
                        opacity: 1;
                        cursor: pointer;

                        td div {
                            color: rgba(255, 202, 100, 1);
                        }
                    }
                }
            }
        }
    }

    .statistics-top {
        height: 50px;
        width: 100%;
        display: flex;
        justify-content: space-around;

        >div {
            min-width: 60px;
            width: max-content;
            // width: 20%;
            height: 100%;
            // padding: 10px 0;
            box-sizing: border-box;
            display: flex;
            justify-content: space-evenly;
            flex-direction: column;

            p {
                text-align: center;
                font-size: 0.75rem;
                font-family: DIN-Bold, DIN;
                color: #ffffff;
            }

            .green-font {
                font-size: 0.875rem;
                color: #ffe3a6;
                font-weight: bold;
            }
        }
    }

    ::v-deep .el-tabs {
        height: 100%;

        .el-tabs__nav {
            .el-tabs__item {
                font-size: 14px;
                color: #FFFFFF;
            }

            .is-active {
                color: #FFE3A6;
            }
        }

        .el-tabs__active-bar {
            background-color: #FFE3A6;
        }

        .el-tabs__nav-wrap::after {
            height: 1px;
            background-color: #7EAEF9;
        }

        .el-tabs__content {
            height: calc(100% - 55px);

            .el-tab-pane {
                height: 100%;
            }
        }
    }

    .content-select {
        display: flex;
        justify-content: center;
        padding: 7px 0px 16px 0px;

        .select-tag {
            font-size: 14px;
            padding: 5px 10px;
            border-right: 1px solid #7EAEF9;
            cursor: pointer;
            color: #7EAEF9;
        }

        .tag-active {
            color: #FFE3A6;
            background: url('../../../assets/images/center/light-yellow.png') no-repeat center / 100%;
        }

        .select-tag:last-child {
            border-right: none;
        }
    }
}