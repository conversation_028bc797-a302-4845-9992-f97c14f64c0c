<template>
  <div class="main">
    <el-dialog
      v-dialogDrag
      :modal="false"
      :visible.sync="dialogShow"
      custom-class="openDoorRecordDialog main"
      append-to-body
      :close-on-click-modal="false"
      :before-close="closeDialog"
    >
      <template slot="title">
        <span class="dialog-title">停靠记录</span>
      </template>
      <div class="dialog-content">
        <div class="search-box">
          <div class="search-form">
            <el-date-picker
              v-model="dateRange"
              type="daterange"
              unlink-panels
              popper-class="date-style"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              value-format="yyyy-MM-dd"
              :clearable="false"
            >
            </el-date-picker>
            <el-select
              v-model="searchParam.buildingId"
              placeholder="请选择楼宇"
              collapse-tags
              filterable
              clearable
              popper-class="new-select"
              @change="buildingIdChange"
            >
              <el-option
                v-for="item in buildingList"
                :key="item.id"
                :label="item.ssmName"
                :value="item.id"
              >
              </el-option>
            </el-select>
            <el-select
              v-model="searchParam.elevatorId"
              placeholder="请选择电梯"
              filterable
              clearable
              popper-class="new-select"
            >
              <el-option
                v-for="item in elevatorEquList"
                :key="item.id"
                :label="item.assetsName"
                :value="item.id"
              >
              </el-option>
            </el-select>
            <el-select
              v-model="searchParam.countType"
              filterable
              clearable
              popper-class="new-select"
              @change="dateTypeCommand"
            >
              <el-option
                v-for="item in dateTypeList"
                :key="item.value"
                :label="item.name"
                :value="item.value"
              >
              </el-option>
            </el-select>
          </div>
          <div class="search-btn">
            <el-button @click="resetForm">重置</el-button>
            <el-button @click="searchForm">查询</el-button>
          </div>
        </div>
        <div class="main-content">
          <el-table
            v-loading="tableLoading"
            :data="tableData"
            height="calc(100%)"
            :cell-style="$tools.setCell(3)"
            :header-cell-style="{
              ...$tools.setHeaderCell(3),
              fontSize: '12px',
            }"
            style="width: 100%"
            element-loading-background="rgba(0, 0, 0, 0.2)"
          >
            <el-table-column
              v-for="item in tableHeader"
              :key="item.id"
              :prop="item.id"
              :label="item.name"
              show-overflow-tooltip
              :width="computTableColumnWidth(item.id)"
            ></el-table-column>
          </el-table>
        </div>
        <el-pagination
          v-if="searchParam.countType == 'default'"
          :current-page="currentPage"
          :page-sizes="[15, 20, 30, 40]"
          :page-size="pageSize"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
          class="pagination"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        ></el-pagination>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getBuildByAreaID } from '@/utils/elevatorApi'
import { elevatorList } from '@/utils/comprehensiveStatistics'
import moment from 'moment'
import { cloneDeep } from 'lodash'
import {
  elevatorStopFloorList,
  elevatorStopFloorRecord
} from '@/utils/elevatorApi'
moment.locale('zh-cn')
export default {
  name: 'stopRecord',
  components: {},
  props: {
    dialogShow: {
      type: Boolean,
      default: false
    },
    roomData: {
      type: Object,
      default: () => {}
    },
    selectData: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      dateTypeList: [
        { name: '开门时间统计', value: 'default' },
        { name: '24小时统计', value: 'hour' },
        { name: '日统计', value: 'day' },
        { name: '周统计', value: 'week' },
        { name: '月统计', value: 'month' }
      ],
      searchParam: {
        countType: 'default',
        buildingId: '',
        elevatorId: [],
        floorName: ''
      },
      dateRange: [moment().format('YYYY-MM-DD'), moment().format('YYYY-MM-DD')],
      tableLoading: false,
      tableHeaderOrigin: [
        { name: '电梯名称', id: 'liftName' },
        { name: '停靠时间', id: 'dateTime' },
        { name: '停靠楼层', id: 'floorName' }
      ],
      tableHeader: [],
      tableData: [],
      currentPage: 1,
      pageSize: 15,
      total: 0,
      buildingList: [], // 楼宇列表
      elevatorEquList: [], // 电梯列表
      floorList: [] // 楼层列表
    }
  },
  computed: {},
  created() {
    this.initEvent()
  },
  methods: {
    computTableColumnWidth (id) {
      if (this.searchParam.countType == 'day' && id !== 'name' && id !== 'count') {
        return '120px'
      } else {
        return ''
      }
    },
    initEvent() {
      this.searchParam.buildingId = this.selectData?.buildingId ?? ''
      this.dateRange = [
        this.selectData.dateRange[0],
        this.selectData.dateRange[1]
      ]
      this.searchParam.countType = this.selectData.countType || 'default'
      this.tableHeader = cloneDeep(this.tableHeaderOrigin)
      this.getBuildByAreaID()
      this.getElevatorList()
    },
    // 时间类型切换
    dateTypeCommand(val) {
      const date = {
        default: [moment().format('YYYY-MM-DD'), moment().format('YYYY-MM-DD')],
        hour: [moment().format('YYYY-MM-DD'), moment().format('YYYY-MM-DD')],
        day: [
          moment().startOf('day').format('YYYY-MM-DD'),
          moment().endOf('day').format('YYYY-MM-DD')
        ],
        week: [
          moment().startOf('week').format('YYYY-MM-DD'),
          moment().endOf('week').format('YYYY-MM-DD')
        ],
        month: [
          moment().startOf('month').format('YYYY-MM-DD'),
          moment().endOf('month').format('YYYY-MM-DD')
        ]
      }
      this.dateRange = date[val]
      if (val == 'default') {
        this.tableHeader = cloneDeep(this.tableHeaderOrigin)
      }
      this.searchForm()
    },
    buildingIdChange() {
      this.searchParam.elevatorId = ''
      this.getElevatorList()
    },
    getDataList() {
      let params = {
        dateType: this.selectData.dateType,
        sTime: this.dateRange[0],
        eTime: this.dateRange[1],
        countType: this.searchParam.countType,
        monitorDeviceId: this.searchParam.elevatorId,
        liftType: '0',
        pageNo: this.currentPage,
        pageSize: this.pageSize
      }
      if (this.searchParam.countType === 'default') {
        elevatorStopFloorList(params).then((res) => {
          if (res.data.code == 200) {
            this.tableData = res.data.result.records
            this.total = res.data.result.total
          }
        })
      } else {
        elevatorStopFloorRecord(params).then((res) => {
          if (res.data.code == 200) {
            this.tableHeader = res.data.result.headerList
            let arr = [
              { name: '设备名称', id: 'name' },
              { name: '总次数', id: 'count' }
            ]
            this.tableHeader = [...arr, ...this.tableHeader]
            this.tableData = res.data.result.dataList.map((el) => {
              let obj = {
                name: el.name,
                deviceId: el.deviceId,
                sn: el.sn,
                count: el.count
              }
              this.tableHeader.forEach((key) => {
                el.list.forEach((item) => {
                  if (key.id == item.timeStr) {
                    obj[key.id] = item.count
                  }
                })
              })
              return obj
            })
          }
        })
      }
    },

    // 获取电梯列表
    getElevatorList() {
      elevatorList({ sysOfCode: 'DTXT' }).then((res) => {
        if (res.data.code == 200) {
          this.elevatorEquList = res.data.data
          this.searchForm()
        }
      })
    },

    // 获取楼栋
    getBuildByAreaID() {
      getBuildByAreaID({ areaId: this.roomData?.ssmCodes?.split(',')[1] }).then(
        (res) => {
          if (res.data.code == 200) {
            this.buildingList = res.data.data
          }
        }
      )
    },
    // 查询
    searchForm() {
      this.currentPage = 1
      this.getDataList()
    },
    // 重置查询
    resetForm() {
      // this.handleClear()
      // Object.assign(this.searchParam, {
      //   buildingId: '',
      //   elevatorId: [],
      //   floorName: ''
      // })
      this.currentPage = 1
      // this.getDataList()
      this.initEvent()
    },
    handleSizeChange(val) {
      this.currentPage = 1
      this.pageSize = val
      this.getDataList()
    },
    handleCurrentChange(val) {
      this.currentPage = val
      this.getDataList()
    },
    // 取消按钮
    closeDialog() {
      this.$emit('openDoorRecordClose')
    }
  }
}
</script>

<style lang="scss" scoped>
::v-deep .openDoorRecordDialog {
  width: 70%;
  height: 84vh;
  margin-top: 6vh !important;
  // border: 1px solid #5996f9;
  background-color: transparent !important;
  pointer-events: auto;
  background-image: url("@/assets/images/table-bg.png");
  background-repeat: no-repeat;
  background-size: 100% 100%;
  box-shadow: none;
  .el-dialog__headerbtn {
    transform: translateX(-36px);
    width: 25px;
    height: 25px;
    background-image: url("@/assets/images/close.png");
    background-repeat: no-repeat;
    background-size: 100% 100%;
    .el-dialog__close::before {
      display: none;
    }
  }
  .dialog-title {
    display: inline-block;
    width: 100%;
    text-align: center;
    transform: translateY(-6px);
    color: #cbdeed;
  }
  .dialog-title::before {
    display: none;
  }
  .el-dialog__header {
    background-color: transparent;
  }
  .el-dialog__body {
    height: calc(100% - 60px);
    max-height: fit-content;
    overflow-y: hidden;
    background-color: transparent;
    padding: 0px 80px;
  }
  .dialog-content {
    width: 100%;
    height: 96%;
    background-color: transparent;
    display: flex;
    flex-direction: column;
    .search-box {
      padding: 8px 0px 24px 0px;
      display: flex;
      align-items: center;
      justify-content: space-between;
      .search-form {
        display: flex;
        align-items: center;
      }
      .el-button {
        background-image: url("@/assets/images/btn.png");
        background-repeat: no-repeat;
        background-size: 100% 100%;
        height: 35px;
      }
      .el-select {
        width: 185px;
        margin-right: 16px;
        .el-input {
          .el-input__inner {
            height: 35px !important;
            border: 1px solid rgba(133, 145, 206, 0.5);
            border-radius: 4px;
          }
          .el-input__icon {
            line-height: 35px;
          }
        }
      }
      .el-date-editor {
        margin-right: 16px;
        width: 380px;
        height: 35px;
        background-color: #14233e;
        border-color: rgba(133, 145, 206, 0.5);
        .el-input__icon {
          transform: translateY(-2px);
        }
        .el-range-input {
          background-color: #14233e;
          color: rgba(255, 255, 255, 0.8);
        }
        .el-range-separator {
          color: rgba(255, 255, 255, 0.8);
          transform: translateY(-2px);
        }
      }
    }
    .main-content {
      flex: 1;
      overflow: hidden;
      .set-icon {
        cursor: pointer;
        // box-shadow: 0px 3px 4px 0px rgba(2,0,21,0.6);
        color: #8bddf5;
        width: 100%;
        height: 100%;
      }
      .el-table {
        .el-table__fixed-right-patch {
          background: transparent;
          border-color: transparent;
        }
        .el-table__fixed-right::before,
        .el-table__fixed::before {
          background: transparent;
        }
      }
    }
  }
}
:deep(.el-select__tags) {
  span {
    .el-tag {
      &:first-child {
        .el-select__tags-text {
          display: inline-block;
          width: 50px;
          text-overflow: ellipsis;
          overflow: hidden;
          white-space: nowrap;
        }
      }
    }
  }
}
</style>
