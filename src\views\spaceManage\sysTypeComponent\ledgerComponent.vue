<template>
  <div class="ledgerComponent">
    <div class="module-container" style="height: 60%">
      <div class="module-header">
        <div class="title-left">
          <p class="title-left-text">设备台账</p>
        </div>
      </div>
      <div class="device-static" style="height: calc(45% - 44px)">
        <div class="static-box">
          <span class="total-text">总数量</span>
          <div class="total-box">
            <span class="total-num">{{ deviceTotal }}</span
            ><span class="total-unit">台</span>
          </div>
        </div>
      </div>
      <div class="module-content" style="height: 55%">
        <el-table
          height="calc(100%)"
          ref="deviceTypeTable"
          class="bottom-el-table"
          :data="deviceTypeList"
          :cell-style="$tools.setCell(3)"
          :header-cell-style="$tools.setHeaderCell(3)"
          style="width: 100%"
          element-loading-background="rgba(0, 0, 0, 0.2)"
          v-loading="deviceTypeTableLoading"
          highlight-current-row
          @current-change="selectDeviceType"
        >
          <el-table-column type="index" label="序号" width="100" align="center"></el-table-column>
          <el-table-column prop="menuName" show-overflow-tooltip label="设备类型"></el-table-column>
          <el-table-column prop="count" show-overflow-tooltip label="总数"></el-table-column>
        </el-table>
      </div>
    </div>
    <div class="module-container" style="height: 40%">
      <div class="module-header">
        <div class="title-left">
          <p class="title-left-text">管线台账</p>
        </div>
        <div class="middle-right">
          <div :class="{ 'is-activeTab': true }" @click="pipelineTrend">流向</div>
        </div>
      </div>
      <div class="module-content pipeline">
        <el-checkbox v-model="checkAll" :indeterminate="isIndeterminate" @change="changeCheckAll">全选</el-checkbox>
        <el-checkbox-group v-model="checkList" class="middle-xz" @change="changeCheckBox">
          <div v-for="(item, index) in pipelineList" class="fiex-all" :key="index">
            <el-checkbox :label="item.PipeId" :value="item.PipeId" :key="item.PipeId" class="checkbox"></el-checkbox>
            <div class="fiex-div-xz" :style="{ background: item.BGclolor }"></div>
            <div class="fiex-div-font">{{ item.PipeName }}</div>
          </div>
        </el-checkbox-group>
      </div>
    </div>
  </div>
</template>

<script>
import { getDeviceCountByMenuCode } from '@/utils/spaceManage'
import { refrigeratorData, refrigeratorParams } from '@/assets/common/dict.js'
export default {
  name: 'ledgerComponent',
  props: {
    roomData: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      deviceTypeTableLoading: false,
      deviceTypeList: [], // 设备类型列表
      checkAll: false, // 全选
      checkList: [], // 选中数组
      isIndeterminate: true,
      pipelineList: [],
      refrigeratorParams: refrigeratorParams,
      deviceTotal: 0
    }
  },
  mounted() {
    if (this.roomData.projectCode === 'b5587fbbe453422ebc937cc0b7c69a3c') {
      this.pipelineList = refrigeratorData
      this.checkList = ['Pipe_KT_001', 'Pipe_KT_002', 'Pipe_KT_003', 'Pipe_KT_004', 'Pipe_KT_005']
      this.setCheckDataToWpf()
    }
    this.getDeviceAccountStatistics()
    // this.getListBySpaceCategory()
  },
  methods: {
    selectDeviceType(currentRow) {
      this.$refs.deviceTypeTable.setCurrentRow(currentRow)
      try {
        window.chrome.webview.hostObjects.sync.bridge.deviceHighlight(currentRow.modelCode)
      } catch (error) {}
    },
    // 管线流向
    pipelineTrend() {
      try {
        // window.chrome.webview.hostObjects.sync.bridge.GetPipelineSelectData(JSON.stringify(params))
      } catch (error) {}
    },
    // 获取运行设备台账统计
    getDeviceAccountStatistics() {
      const params = {
        projectCode: this.roomData.projectCode
      }
      this.deviceTypeTableLoading = true
      getDeviceCountByMenuCode(params).then((res) => {
        if (res.data.code === '200') {
          this.deviceTypeTableLoading = false
          this.deviceTypeList = res.data.data
          // 获取总数
          this.deviceTotal = this.deviceTypeList.reduce((total, item) => {
            return total + item.count
          }, 0)
          // if (this.deviceTypeList.length) {
          //   this.$refs.deviceTypeTable.setCurrentRow(this.deviceTypeList[0])
          // }
        }
      })
    },
    // 获取管线数据
    // getListBySpaceCategory() {
    //   const params = {
    //     categoryLevel: 1
    //   }
    //   listBySpaceCategory(params).then((res) => {
    //     if (res.data.code === 200) {
    //       const data = res.data.data
    //       this.pipelineList = data.records?.map((item) => {
    //         return {
    //           Visibility: 1,
    //           id: item.category,
    //           name: item.categoryName,
    //           background: item.colour ? `rgba(${item.colour})` : 'rgba(139, 221, 245, 1)'
    //         }
    //       })
    //       if (this.pipelineList) {
    //         const allIDs = Array.from(this.pipelineList, ({ id }) => id)
    //         this.checkList = allIDs
    //         this.setCheckDataToWpf()
    //       }
    //     }
    //   })
    // },
    changeCheckAll(val) {
      this.checkList = val ? Array.from(this.pipelineList, ({ PipeId }) => PipeId) : []
      this.isIndeterminate = false
      this.setCheckDataToWpf()
    },
    changeCheckBox(value) {
      const checkedCount = value.length
      this.checkAll = checkedCount === this.pipelineList.length
      this.isIndeterminate = checkedCount > 0 && checkedCount < this.pipelineList.length
      this.setCheckDataToWpf()
    },
    setCheckDataToWpf() {
      const params = JSON.parse(JSON.stringify(this.refrigeratorParams))
      this.checkList.forEach((item) => {
        params.forEach((e) => {
          if (item === e.CategoryCode) {
            e.Visibility = 1
          }
        })
      })
      try {
        window.chrome.webview.hostObjects.sync.bridge.GetPipelineSelectData(JSON.stringify(params))
      } catch (error) {}
    }
  }
}
</script>

<style lang="scss" scoped>
@import '../style/module.scss';
.ledgerComponent {
  width: 100%;
  height: 100%;
  .pipeline {
    box-sizing: border-box;
    padding: 16px 0 10px 10px;
    height: calc(100% - 44px);
    ::v-deep .middle-xz {
      display: flex;
      flex-direction: column;
      max-height: calc(100% - 16px);
      overflow-y: auto;
      .fiex-all {
        display: flex;
        align-items: center;
        margin-top: 8px;

        .fiex-div-xz {
          width: 197px;
          height: 8px;
          background: #8bddf5;
          margin: 0 8px;
          flex-shrink: 0;
        }

        .fiex-div-font {
          height: 20px;
          font-size: 14px;
          font-family: PingFang SC-Medium, PingFang SC;
          font-weight: 500;
          color: #ffffff;
          line-height: 20px;
          text-overflow: ellipsis;
          white-space: nowrap;
          overflow: hidden;
        }

        .checkbox {
          width: 14px;
          height: 14px;

          .el-checkbox__input {
            .el-checkbox__inner {
              box-shadow: 0px 0px 3px 0px #78fff8;
              opacity: 1;
              border: 1px solid #52fffc;
            }
          }
        }
        .el-checkbox__label {
          display: none;
        }
      }
    }
  }
  .device-static {
    display: flex;
    .static-box {
      margin: auto;
      width: 146px;
      height: 168px;
      background: url('@/assets/images/qhdsys/device-turntable.png') no-repeat;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      .total-text {
        padding-bottom: 10px;
        font-size: 16px;
        font-family: HarmonyOS Sans SC-Medium, HarmonyOS Sans SC;
        color: #b0e3fa;
      }
      .total-box {
        color: #ffffff;
        .total-num {
          font-size: 38px;
          font-family: HarmonyOS Sans SC-Bold, HarmonyOS Sans SC;
          font-weight: bold;
        }
        .total-unit {
          font-size: 14px;
          font-family: HarmonyOS Sans SC-Regular, HarmonyOS Sans SC;
        }
      }
    }
  }
}
// 滚动条的宽度
::v-deep .el-table__body-wrapper::-webkit-scrollbar {
  width: 6px; // 横向滚动条
  height: 6px; // 纵向滚动条 必写
}

// 注意:hover
::v-deep .el-table__body-wrapper:hover::-webkit-scrollbar {
  width: 6px; // 横向滚动条
  height: 6px; // 纵向滚动条 必写
}
::v-deep .el-table__body-wrapper::-webkit-scrollbar-corner,
::v-deep .el-table__body-wrapper::-webkit-scrollbar-track {
  background-color: transparent;
}
.module-header {
  width: 100%;
  // padding-left: 50px;
  background: url('~@/assets/images/qhdsys/bg-bt.png') no-repeat;
  .middle-right {
    display: flex;
    margin-right: 7px;
    & > div {
      width: 52px;
      height: 26px;
      line-height: 26px;
      text-align: center;
      background: rgba(255, 255, 255, 0.1) linear-gradient(90deg, rgba(10, 132, 255, 0) 0%, rgba(10, 132, 255, 0) 100%);
      border-radius: 0px 0px 0px 0px;
      opacity: 1;
      font-size: 14px;
      font-family: HarmonyOS Sans SC-Regular, HarmonyOS Sans SC;
      font-weight: 400;
      color: #8bddf5;
      cursor: pointer;
    }
  }
  .is-activeTab {
    background: url('@/assets/images/qhdsys/bg-kj.png') no-repeat !important;
  }
}
.title-left {
  padding-left: 20px;
}
::v-deep .bottom-el-table {
  border: 0 !important;

  .el-table__header-wrapper {
    .el-table__header {
      .has-gutter {
        tr {
          background: transparent;
        }

        tr th {
          padding: 0;
          height: 32px;
          background: rgba(133, 145, 206, 0.15) !important;
          border-bottom: 1px solid rgba(255, 255, 255, 0.5);

          .cell {
            font-size: 14px;
            font-family: HarmonyOS Sans SC-Bold, HarmonyOS Sans SC;
            font-weight: bold;
            color: #8bddf5;
          }
        }
      }
    }
  }
}
::v-deep .el-table__body-wrapper {
  background: transparent;
  height: calc(100% - 50px) !important;
  overflow: hidden;
  overflow-y: auto;
  td.el-table__cell {
    border: 0 !important;
  }
  .el-table__body {
    background: transparent;

    tbody {
      background: transparent;

      .el-table__row {
        // background-color: rgba(255, 255, 255, 0.2);
        background: transparent;
        border: 0;

        td {
          border: 0;
          padding: 0;
          height: 30px;

          div {
            font-size: 14px;
            font-family: HarmonyOS Sans SC-Regular, HarmonyOS Sans SC;
            font-weight: 400;
            color: #ffffff;
          }
        }
      }

      .el-table__row:nth-child(2n - 1) {
        background: rgba(168, 172, 171, 0.08);
      }

      .el-table__row:hover {
        border: 0;
        opacity: 1;
        cursor: pointer;

        td div {
          color: rgba(255, 202, 100, 1);
        }
      }
    }
  }
}
</style>
