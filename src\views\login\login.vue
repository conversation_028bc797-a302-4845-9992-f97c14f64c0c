<template>
  <div class="sion-login-container" :style="{ 'background-image': 'url(' + loginBg + ')' }">
    <div class="sion-login-header">
      <sino-logo type="colour"></sino-logo>
    </div>
    <div class="sion-login-body">
      <login></login>
    </div>
    <div class="sion-login-footer">
      <span>中科医信公司版权所有</span>
      <a href="http://beian.miit.gov.cn/" target="_blank">CopyRight 2020 logimis.com All Rights Reserved 京ICP备17052614号-3</a>
    </div>
  </div>
</template>

<script>
import { postFormAPI } from '@/utils/api'
import loginImage from '@assets/images/login/login.png'
import sinoLogo from '@components/common/sinoLogo'
import login from '@components/login/login'
export default {
  data() {
    return {
      loginBg: loginImage
    }
  },
  created() {
    // this.getList()
  },
  methods: {
    async getList() {
      const params = {
        username: 'admin',
        password: '123456'
      }
      const res = await postFormAPI(params)
    }
  },
  components: {
    sinoLogo,
    login
  }
}
</script>

<style lang="scss" scoped>

</style>
