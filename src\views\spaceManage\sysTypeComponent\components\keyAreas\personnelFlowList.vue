<template>
  <div class="list-index-view">
    <div class="view-header-box">
      <div class="header-left">
        <el-form :model="searchForm" class="search-form" inline>
          <el-form-item>
            <el-input v-model="searchForm.assetName" size="small" placeholder="设备名称" clearable></el-input>
          </el-form-item>
          <el-form-item>
            <el-date-picker
              v-model="searchForm.dateRange"
              type="daterange"
              unlink-panels
              popper-class="date-style"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              value-format="yyyy-MM-dd"
            >
            </el-date-picker>
          </el-form-item>
        </el-form>
      </div>
      <div class="header-right">
        <el-button class="sino-button-sure" @click="resetForm">重置</el-button>
        <el-button class="sino-button-sure" @click="handleSearchForm">查询</el-button>
      </div>
    </div>
    <div class="view-content">
      <el-table
        ref="table"
        v-loading="tableLoading"
        :data="tableData"
        :resizable="false"
        height="calc(100% - 46px)"
        :cell-style="$tools.setCell(3)"
        :header-cell-style="$tools.setHeaderCell(3)"
        stripe
        element-loading-background="rgba(0, 0, 0, 0.2)"
      >
        <el-table-column prop="areaName" label="重点区域" show-overflow-tooltip></el-table-column>
        <el-table-column prop="assetName" label="设备名称" show-overflow-tooltip></el-table-column>
        <el-table-column prop="throughCount" label="人员流量" show-overflow-tooltip></el-table-column>
      </el-table>
      <el-pagination
        :current-page="currentPage"
        :page-sizes="[15, 20, 30, 40]"
        :page-size="pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
        class="pagination"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      ></el-pagination>
    </div>
  </div>
</template>

<script>
import { GetRridAreaDeviceList } from '@/utils/spaceManage'
export default {
  name: 'personnelFlowList',
  props: {
    roomData: {
      type: Object,
      default: () => {
        return {}
      }
    },
    seleteItem: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  data() {
    return {
      searchForm: {
        assetName: '',
        dateRange: []
      },
      currentPage: 1,
      pageSize: 15,
      total: 0,
      tableData: [],
      tableLoading: false
    }
  },
  computed: {

  },
  created() {
    this.getRridAreaDeviceList()
  },
  methods: {
    // 获取人流监测设备
    getRridAreaDeviceList() {
      this.searchForm.dateRange = this.searchForm.dateRange || []
      const params = {
        areaId: this.seleteItem.id,
        assetName: this.searchForm.assetName,
        beginDate: this.searchForm.dateRange[0],
        endDate: this.searchForm.dateRange[1],
        pageNo: this.currentPage,
        pageSize: this.pageSize
      }
      this.tableLoading = true
      GetRridAreaDeviceList(params).then(res => {
        this.tableLoading = false
        if (res.data.code == 200) {
          this.tableData = res.data.data.list
          this.total = Number(res.data.data.totalCount)
        }
      }).finally(() => {
        this.tableLoading = false
      })
    },
    /** 查询 */
    handleSearchForm() {
      this.getRridAreaDeviceList()
    },
    /** 重置 */
    resetForm() {
      Object.assign(this.$data.searchForm, this.$options.data().searchForm)
      this.handleSearchForm()
    },
    viewDetail() {
      this.$emit('openDetailComponent', 'constructionFlow')
    },
    /** 条数变化事件 */
    handleSizeChange(val) {
      this.pageSize = val
      this.handleSearchForm()
    },
    /** 页数变化事件 */
    handleCurrentChange(val) {
      this.currentPage = val
      this.handleSearchForm()
    }
  }
}

</script>

<style lang="scss" scoped>
.list-index-view {
  position: relative;
  height: 100%;
  padding: 0px 40px;
}
.view-header-box {
  display: flex;
  justify-content: space-between;
  ::v-deep(.el-date-editor) {
    margin-top: 4px;
    width: 300px;
    height: 32px;
    background-color: transparent;
    border-color: #3056A2;
    border-radius: 0;
    .el-input__icon {
      transform: translateY(-4px);
    }
    .el-range-input {
      height: 30px;
      background-color: transparent;
      color: rgba(255, 255, 255, 0.8);
    }
    .el-range-separator {
      color: rgba(255, 255, 255, 0.8);
      transform: translateY(-4px);
    }
  }
  .header-right {
    line-height: 40px;
  }
}
.view-nav-box {
  margin-bottom: 1.5rem;
  display: flex;
  align-items: center;
  box-sizing: border-box;
  .nav-box {
    height: 2.5rem;
    line-height: 2.5rem;
    width: 12.38rem;
    font-size: 1rem;
    text-align: center;
    color: #a4afc1;
    cursor: pointer;
    box-sizing: border-box;
    border: 1px dashed #26314f;
    border-right: none;
    background: url(@/assets/images/nav-bg.png) no-repeat;
    background-size: cover;
  }
  .nav-box:last-child {
    border-right: 1px dashed #26314f;
  }
  .active {
    color: #b0e3fa;
    background: url(@/assets/images/nav-bg-xz.png) no-repeat;
    background-size: cover;
  }
}
.view-content {
  height: calc(100% - 4rem);
  .table-view {
    height: 100%;
  }
  .axis-big-box {
    width: 100%;
    height: 5.62rem;
    box-sizing: border-box;
    padding: 0px 20px;
    overflow-x: auto;
    display: flex;
  }
  .axis-box {
    height: 5rem;
    position: relative;
    display: flex;
    align-items: center;
    .axis {
      width: 100%;
      height: 1px;
      background: #ffca64;
      position: absolute;
      left: 0px;
      top: 45px;
    }
    .axis-text-box {
      margin-right: 1.25rem;
      font-size: 14px;
      div {
        width: 5.44rem;
        height: 1.25rem;
        line-height: 1.25rem;
        text-align: center;
      }
      &-top {
        font-size: 14px;
        color: #b0e3fa;
        margin-top: 0.44rem;
      }
      &-bot {
        margin-top: 1.44rem;
        border-radius: 2px;
        font-size: 12px;
        color: #ffca64;
        background: rgba(255, 202, 100, 0.2);
        position: relative;
      }
      &-bot::after {
        content: "";
        width: 1px;
        height: 0.75rem;
        background: #ffca64;
        position: absolute;
        left: 50%;
        top: -0.75rem;
      }
      &-bot::before {
        content: "";
        width: 5px;
        height: 5px;
        border-radius: 5px;
        background: #ffca64;
        position: absolute;
        left: 2.6rem;
        top: -0.75rem;
      }
    }
  }
}
.operation-span {
  cursor: pointer;
  color: #8BDDF5;
}
::v-deep .el-table--striped .el-table__body tr.el-table__row--striped td.el-table__cell{
  background: transparent;
}
</style>
