<template>
  <div style="width: 100%; height: 3rem">
    <div :class="['circle', 'circle' + boxId]">
      <div class="circle-left"></div>
      <div class="circle-right"></div>
      <div class="circle-bottom-left"></div>
      <div class="circle-bottom-right"></div>
    </div>
    <div class="info">{{ (percent * 100).toFixed(0) }}%</div>
  </div>
</template>
<script>
export default {
  name: 'circleBox',
  data() {
    return {}
  },
  props: {
    percent: {
      default: 0,
      type: [Number, String]
    },
    comColor: {
      default: '#FFC884',
      type: String
    },
    boxId: {
      default: '1',
      type: String
    }
  },
  watch: {
    percent() {
      this.setDomChange()
    }
  },
  mounted() {
    // console.log(this.percent)
    this.setDomChange()
  },
  methods: {
    setDomChange() {
      var right = document.getElementsByClassName(`circle${this.boxId}`)[0].getElementsByClassName('circle-right')[0]
      var left = document.getElementsByClassName(`circle${this.boxId}`)[0].getElementsByClassName('circle-left')[0]
      var rightBottom = document.getElementsByClassName(`circle${this.boxId}`)[0].getElementsByClassName('circle-bottom-right')[0]
      var leftBottom = document.getElementsByClassName(`circle${this.boxId}`)[0].getElementsByClassName('circle-bottom-left')[0]
      rightBottom.style.background = this.comColor
      leftBottom.style.background = this.comColor
      if (this.percent <= 0.5) {
        // 红色区域不超过一半
        right.style.transform = `rotate(${this.percent * 360}deg)`
      } else {
        // 红色区域超过一半的情况，重点部分
        right.style.transform = 'rotate(180deg)'
        right.style.opacity = 0
        left.style.transform = `rotate(${this.percent * 360 - 180}deg)`
      }
    }
  }
}
</script>
<style lang="scss" scoped>
// css部分
.circle {
  width: 3rem;
  height: 3rem;
  margin: auto;
  overflow: hidden;
  border-radius: 50%;
  position: relative;
  -webkit-mask: radial-gradient(transparent 1.2rem, rgb(0, 0, 0) 1.21rem);
}

.circle-left {
  width: 50%;
  height: 100%;
  background: #39456a;
  transform-origin: 100% 50%;
  position: absolute;
  left: 0;
  z-index: 0;
}

.circle-right {
  width: 50%;
  height: 100%;
  background: #39456a;
  transform-origin: 0% 50%;
  position: absolute;
  right: 0;
  z-index: 2;
}

.circle-bottom-left {
  width: 50%;
  height: 100%;
  // background: #FFC884;
  position: absolute;
  left: 0;
  z-index: -1;
}

.circle-bottom-right {
  width: 50%;
  height: 100%;
  // background: #FFC884;
  position: absolute;
  right: 0;
  z-index: 1;
}

.info {
  width: 3rem;
  height: 3rem;
  text-align: center;
  // margin-top: -3rem;
  font-size: 0.7rem;
  color: #fff;
  margin: -2rem auto 0;
  align-items: center;
}
</style>
