<template>
  <div class="information-details">
    <div class="sino-lists-wrapper">
      <div class="title-bar">
        <div class="module-title">
          <span>基础信息</span>
        </div>
        <div class="title-btn" @click="allTableChange">
          <span>查看附属信息</span>
        </div>
      </div>
      <div class="content-warp">
        <div class="warp-box" v-for="(item,index) in basiceInfo" :key="index">
          <div class="warp-box-title">{{ `${item.label}:` }}</div>
          <div class="warp-box-text"><span></span><span>{{ basicInfoForm[item.prop] | basicFilters }}</span></div>
        </div>
      </div>
    </div>
    <div class="sino-lists-wrapper">
      <div class="title-bar">
        <div class="module-title">
          <span>使用信息</span>
        </div>
      </div>
      <div class="content-warp">
        <div class="warp-box" v-for="(item, index) in useInfo" :key="index">
          <div class="warp-box-title">{{ `${item.label}:` }}</div>
          <div class="warp-box-text"><span>{{ basicInfoForm[item.prop] | basicFilters }}</span></div>
        </div>
      </div>
    </div>
    <div class="sino-lists-wrapper">
      <div class="title-bar">
        <div class="module-title">
          <span>类别信息</span>
        </div>
      </div>
      <div class="content-warp">
        <div class="warp-box" v-for="(item, index) in categoryInformationFormItemList" :key="index">
          <div class="warp-box-title" style="width: 7.5rem;">{{ `${item.label}:` }}</div>
          <div class="warp-box-text"><span>{{ basicInfoForm[item.prop] | basicFilters }}</span></div>
        </div>
      </div>
    </div>
    <dialogFrame
      :visible="visible"
      :breadcrumb="breadcrumb"
      :title="title"
      @back="back"
      @update:visible="closeDialogFrame"
    >
      <component
        :is="activeComponent"
        @openDetailComponent="openDetailComponent"
      ></component>
    </dialogFrame>
  </div>
</template>
  <script>
import {
  basiceInfo,
  useInfo,
  categoryInformationFormItemList
} from "./json/details";
export default {
  name: "equipmentInfo",
  props: {
    deviceId: {
      type: String,
      default: "",
    },
  },
  components:{
    dialogFrame: () => import("@/components/common/DialogFrame"),
    monitorListIndex: () => import("@/views/monitorModular/listIndex.vue"),
    monitorDetails: () =>
      import("@/views/monitorModular/monitorDetails/details.vue"),
  },
  filters: {
    basicFilters(val) {
      return val ? val : "---";
    },
  },
  data() {
    return {
      basiceInfo,
      useInfo,
      categoryInformationFormItemList,
      visible: false,
      breadcrumb: [{ label: "设备详情", name: "monitorDetails" }],
      activeComponent: "monitorDetails",
      title: "设备附属信息",
      basicInfoForm: {}, // 复制和编辑时详情信息
      departmentTurn: {},
      pageLoading: false,
      imageArr: [],
      departmentShare: [],
    };
  },
  created() {},
  methods:{
    allTableChange() {
      this.visible = true;
    },
    /** 打开组件 */
    openDetailComponent(params) {
      this.activeComponent = params;
      let obj = {
        monitorDetails: { label: "设备详情", name: "monitorDetails" },
        monitorListIndex: { label: "监测设备列表", name: "monitorListIndex" },
      };
      this.breadcrumb.push(obj[params]);
      this.title = "";
    },
    /** 组件返回上一级 */
    back(name) {
      this.activeComponent = name;
      let arr = [];
      for (let i = 0; i < this.breadcrumb.length - 1; i++) {
        arr.push(this.breadcrumb[i]);
        if (this.breadcrumb[i].name == name) {
          break;
        }
      }
      this.breadcrumb = arr;
      if (this.breadcrumb.length) {
        this.title = this.breadcrumb.find((item) => item.name === name).label;
      }
    },
    /** 关闭弹窗 */
    closeDialogFrame(){
      this.visible = false;
      this.activeComponent = "monitorDetails"
      this.title = "设备附属信息"
      this.breadcrumb = [{ label: "设备附属信息", name: "monitorDetails" }]
    },
  }
};
</script>
  
  <style lang="scss" scoped>
.information-details {
  margin: 0 auto;
  width: 100%;
  height: 100%;
  overflow-y: auto;
  .sino-lists-wrapper {
    color: #fff;
    font-size: 15px;
    margin-bottom: 1rem;
    .title-bar {
      width: 100%;
      padding: 0px 10px;
      height: 2.625rem;
      background: rgba(133, 145, 206, 0.15);
      display: flex;
      align-items: center;
      color: #dadee1;
      justify-content: space-between;
      .title-btn {
        padding: 10px;
        font-size: 14px;
        cursor: pointer;
        background: url("@/assets/images/title_bg.png") no-repeat center center;
      }
    }
    .content-warp {
      padding-left: 0.5rem;
      background: rgba(133, 145, 206, 0.1);
      .warp-box {
        width: 100%;
        height: 2.5rem;
        display: flex;
        align-items: center;
        .warp-box-title {
          width: 5.5rem;
          font-size: 14px;
          flex-shrink: 0;
          color: #b0e3fa;
          margin-right: 0.5rem;
        }
        .warp-box-text{
          flex: 1;
          flex-shrink: 0;
        }
      }
    }
  }
}
</style>
  