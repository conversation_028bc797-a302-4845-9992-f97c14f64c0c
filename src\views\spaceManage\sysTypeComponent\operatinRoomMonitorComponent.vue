<template>
  <div class="operatin-room-monitor-container">
    <ModuleCard title="监测统计" style="height: calc(15%)" class="container-box">
      <!-- <div slot="title-right" class="title-right-img">
        <img src="@/assets/images/order_more.png" class="order-more" @click="allTableChange" />
      </div> -->
      <div slot="content" class="top-content" style="height: 100%">
        <div class="monitor-object-box">
          <div v-for="(item, index) in monitorObjectStatistics" :key="index" class="monitor-object-item">
            <span class="label">{{ item.label }}</span>
            <el-statistic group-separator="," :precision="0" :value="item.value" :title="''"></el-statistic>
          </div>
        </div>
      </div>
    </ModuleCard>
    <ModuleCard title="手术室列表" style="height: calc(85%)" class="container-box">
      <div slot="title-right">
        <div class="chart-icon" style="cursor: pointer;" @click="handleAllCollapsed">
          <span>{{ collapseText }}</span>
          <span style="margin-left: 5px" :class="collapseIcon"></span>
        </div>
      </div>
      <div slot="content" v-loading="deviceTableLoading" v-infinite-scroll="tableLoadMore" style="height: calc(100% - 10px)">
        <div class="realTime-module">
          <div v-for="(item, index) in monitorObjectData" :key="index" class="realTime-module-box">
            <div class="rtm-header-box" :style="{borderColor: item.alarmStatus == 1 ? '#FF2D55' : 'rgba(133,145,206,0.5)'}" @click="tabledblClick(item)">
              <div class="rtm-header-title">
                <p>
                  <span>{{ item.assetsName }}</span>
                  <span :style="{
                    color: item.alarmStatus == 1 ? '#FF2D55' : (item.onlineStatus == 1 ? '#61E29D' : '#D4DEEC'),
                    background: item.alarmStatus == 1 ? 'rgba(255, 45, 85, .2)' : (item.onlineStatus == 1 ? 'rgba(97, 226, 157, .2)' : 'rgba(212, 222, 236, .2)')
                  }">{{ item.alarmStatus == 1 ? '报警' : (item.onlineStatus == 1 ? '在线' : '离线') }}</span>
                </p>
                <div class="rtm-header-btn-text" style="color: #8BDDF5;" @click.stop="handleCollapseClick(index)">
                  <span>{{ item.collapsed ? '展开' : '收起' }}</span>
                  <span style="margin-left: 0px" :class="item.collapsed ? 'el-icon-arrow-down' : 'el-icon-arrow-up'"></span>
                </div>
              </div>
              <div class="rtm-header-btn-text">
                <span style="color: #C4F3FE;">物联设备：</span>
                <span style="margin-left: 5px">{{ item.iotCount }}</span>
              </div>
            </div>
            <div v-if="!item.collapsed" class="rtm-table-box">
              <el-table
                class="table-center-transfer"
                :data="item.iotPropertyList"
                :cell-style="{ padding: ' 8px', backgroundColor: 'transparent', border: 'none', padding: '3px', color: '#fff'}"
                :header-cell-style="{ background: '#8591CE26!important', color: '#8BDDF5FF', padding: '3px', fontWeight: 'bold',}"
                style="width: 100%"
              >
                <el-table-column prop="metadataName" show-overflow-tooltip label="监测参数"></el-table-column>
                <el-table-column prop="alarmStatus" show-overflow-tooltip label="物联状态" width="100">
                  <template slot-scope="scope">
                    {{ scope.row.alarmStatus == 1 ? '报警' : (scope.row.onlineStatus == 1 ? '在线' : '离线') }}
                  </template>
                </el-table-column>
                <el-table-column prop="valueText" show-overflow-tooltip label="当前值" width="100"></el-table-column>
                <el-table-column prop="sectionName" show-overflow-tooltip label="状态" width="100">
                  <span slot-scope="scope" :style="{color: scope.row.sectionColor}">
                    {{ scope.row.sectionName }}
                  </span>
                </el-table-column>
              </el-table>
            </div>
          </div>
        </div>
        <div v-if="!monitorObjectData.length" class="center-center">暂无数据</div>
      </div>
    </ModuleCard>
  </div>
</template>
<script>
import { GetMonitoringItemsStatistics, GetMonitoringItemsList, GetMonitoringItemsParams } from '@/utils/spaceManage'
export default {
  name: 'operatinRoomMonitorComponent',
  props: {
    roomData: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      allCollapsed: true,
      deviceTableLoading: false,
      monitorObjectStatistics: [
        {
          label: '手术室总数',
          value: 0
        },
        {
          label: '正常',
          value: 0
        },
        {
          label: '离线',
          value: 0
        },
        {
          label: '报警',
          value: 0
        }
      ],
      monitorObjectData: [],
      pagination: {
        total: 0,
        pageSize: 15,
        pageNo: 1
      }
    }
  },
  computed: {
    collapseText() {
      return this.allCollapsed ? '全部展开' : '全部收起'
    },
    collapseIcon() {
      return this.allCollapsed ? 'el-icon-arrow-down' : 'el-icon-arrow-up'
    }
  },
  watch: {
    roomData: {
      handler: function(val) {
        this.pagination.pageNo = 1
        this.getMonitoringItemsStatistics()
        this.getMonitoringItemsList()
      },
      deep: true
      // immediate: true
    }
  },
  mounted() {
    this.getMonitoringItemsStatistics()
    this.getMonitoringItemsList()
  },
  methods: {
    tabledblClick(row) {
      // return
      this.$emit('roomEvent', {
        type: 'move',
        assetId: row.id,
        assetName: row.assetsName,
        modelCode: row.modelCode,
        spaceLocation: row.spaceLocation
      })
      try {
        window.chrome.webview.hostObjects.sync.bridge.EquipmentRealView(JSON.stringify({
          assetsId: row.id,
          DeviceName: row.assetsName,
          DeviceCode: row.modelCode,
          spaceLocation: row.spaceLocation
        }))
      } catch (error) {}
    },
    // 监测列表
    getMonitoringItemsList() {
      let params = {
        page: this.pagination.pageNo,
        pageSize: this.pagination.pageSize,
        spaceId: this.roomData.ssmCodes?.split(',')?.at(-1),
        sysOfCode: this.roomData.projectCode,
        sysOf1Code: this.roomData.categoryCode
      }
      this.deviceTableLoading = true
      GetMonitoringItemsList(params).then((res) => {
        this.deviceTableLoading = false
        if (res.data.code == 200) {
          if (this.pagination.pageNo == 1) this.monitorObjectData = []
          res.data.data.records.forEach(async (item) => {
            item.collapsed = true
            item.iotPropertyList = await this.getMonitoringItemsParams(item.id).catch()
          })
          this.monitorObjectData = this.monitorObjectData.concat(res.data.data.records)
          this.pagination.total = res.data.data.total
        }
      }).catch(() => {
        this.deviceTableLoading = false
      })
    },
    // 获取检测参数
    getMonitoringItemsParams(assetsId) {
      return new Promise((resolve, reject) => {
        GetMonitoringItemsParams({assetsId}).then((res) => {
          if (res.data.code == 200) {
            resolve(res.data.data)
          } else {
            reject(res)
          }
        }).catch((err) => {
          reject(err)
        })
      })
    },
    // 监测统计
    getMonitoringItemsStatistics() {
      let params = {
        spaceId: this.roomData.ssmCodes?.split(',')?.at(-1),
        sysOfCode: this.roomData.projectCode,
        sysOf1Code: this.roomData.categoryCode
      }
      GetMonitoringItemsStatistics(params).then((res) => {
        if (res.data.code == 200) {
          this.monitorObjectStatistics[0].value = res.data.data.totalCount
          this.monitorObjectStatistics[1].value = res.data.data.onlineCount
          this.monitorObjectStatistics[2].value = res.data.data.offlineCount
          this.monitorObjectStatistics[3].value = res.data.data.alarmCount
        }
      })
    },
    allTableChange() {},
    /** 全部展开/收起 */
    handleAllCollapsed() {
      this.allCollapsed = !this.allCollapsed
      this.monitorObjectData.forEach((item, i) => {
        item.collapsed = this.allCollapsed
      })
    },
    /** 展开/收起 */
    handleCollapseClick(index) {
      this.monitorObjectData[index].collapsed =
        !this.monitorObjectData[index].collapsed
      this.allCollapsed = this.monitorObjectData.every(
        (item) => item.collapsed
      )
    },
    tableLoadMore() {
      if (this.pagination.total > this.pagination.pageNo * this.pagination.pageSize) {
        this.pagination.pageNo++
        this.getMonitoringItemsList()
      }
    }
  }
}
</script>
<style lang="scss" scoped>
%flex-styles {
  display: flex;
  align-items: center;
}
.operatin-room-monitor-container {
  height: 100%;
}
.title-right-img {
  display: flex;
  align-items: center;
}
.order-more {
  width: 24px;
  height: 24px;
  margin-right: 0 !important;
  margin-left: 8px;
  cursor: pointer;
}
.monitor-object-box {
  height: 100%;
  display: flex;
  justify-content: space-around;
  .monitor-object-item {
    padding: 5px;
    display: flex;
    align-items: center;
    flex-direction: column;
    .label {
      margin-bottom: 0.5rem;
    }
  }
}
.chart-icon {
  color: #8BDDF5;
  font-size: 14px;
}
.realTime-module {
  .realTime-module-box {
    background: #8591ce26;
    margin-bottom: 10px;
    .rtm-header-box {
      padding: 16px;
      cursor: pointer;
      border: 1px solid;
      .rtm-header-title {
        @extend %flex-styles;
        justify-content: space-between;
        margin-bottom: 16px;
        font-size: 15px;
        span:last-child{
          margin-left: 10px;
          font-size: 14px;
          padding: 3px 8px;
          border-radius: 100px;
          line-height: 16px;
          display: inline-block;
        }
      }
      .rtm-header-btn-text {
        font-size: 14px;
        cursor: pointer;
      }
    }
    .rtm-table-box {
      border: 1px solid rgba(133,145,206,0.5);
      border-top: none;
    }
  }
}
::v-deep .el-statistic .con .number {
  color: #fff;
}
</style>
