<template>
  <div v-if="ipasPointDetailShow">
    <ipasPointDetail ref="retrospect" :dialogShow="ipasPointDetailShow" :detailId="detailId" @retrospectCloseDialog="retrospectCloseDialog"> </ipasPointDetail>
  </div>
  <div v-else class="content">
    <div class="title">
      <div @click="backToWPFhome()"><i class="el-icon-arrow-left"></i>返回</div>
    </div>
    <div class="content-left">
      <div class="left-bottom">
        <div class="bg-title">
          <span>巡检点分析</span><span class="center-legend"></span
          ><span class="right-search"
            ><span @click="activeAnalysisEvent('#pointDay', 'day')" id="pointDay">今日</span><i>|</i><span @click="activeAnalysisEvent('#pointWeek', 'week')" id="pointWeek">本周</span><i>|</i
            ><span id="pointMonth" @click="activeAnalysisEvent('#pointMonth', 'month')">本月</span></span
          >
        </div>
        <div class="bg-content">
          <el-table
            class="table-center-transfer"
            :data="tableData"
            height="100%"
            :cell-style="$tools.setCell(3)"
            :header-cell-style="$tools.setHeaderCell(3)"
            style="width: 100%"
            element-loading-background="rgba(0, 0, 0, 0.2)"
            v-loading="tableLoading"
            @row-click="toPointList"
          >
            <el-table-column fixed prop="taskPointName" show-overflow-tooltip label="巡检点"></el-table-column>
            <el-table-column fixed prop="sum" show-overflow-tooltip label="应巡次数"></el-table-column>
            <el-table-column fixed prop="accomplishCount" show-overflow-tooltip label="实巡次数"></el-table-column>
            <el-table-column fixed prop="unfinishedCount" show-overflow-tooltip label="未巡次数"></el-table-column>
            <el-table-column fixed prop="percentage" show-overflow-tooltip label="完成率">
              <template slot-scope="scope">
                <span>{{ scope.row.percentage }}%</span>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>
    </div>
    <!-- <div class="content-right">
      <el-button class="sino-button-sure" @click="ipasPointRecordChange">showDialog</el-button>
    </div> -->
    <template v-if="ipasPointRecordShow">
      <ipasPointRecord ref="ipasPointRecord" :location="location" :ssmType="ssmType" :dialogShow="ipasPointRecordShow" @configCloseDialog="configCloseDialog"></ipasPointRecord>
    </template>
  </div>
</template>
<script>
import $ from 'jquery'
import ipasPointRecord from './component/ipasPointRecord.vue'
import ipasPointDetail from './component/ipasPointDetail.vue'
import { getTaskPointSpaceList } from '@/utils/centerScreenApi'
export default {
  name: 'businessIPASalaysis',
  components: {
    ipasPointRecord,
    ipasPointDetail
  },
  data() {
    return {
      tableData: [],
      deptAllShow: true,
      tableLoading: false,
      ipasPointRecordShow: false,
      ipasPointDetailShow: false,
      detailId: '',
      location: '',
      ssmType: '',
      dateType: 'day',
      toPage: ''
    }
  },
  mounted() {
    // this.location = '1515969597336014850'
    // this.ssmType = '3'
    // 初始化 获取定位信息
    if (Object.hasOwn(this.$route.query, 'localtion') || Object.hasOwn(this.$route.query, 'ssmType')) {
      this.location = this.$route.query.localtion || ''
      this.ssmType = this.$route.query.ssmType || ''
    }
    this.activeAnalysisEvent('#pointDay', 'day')
    try {
      window.chrome.webview.addEventListener('message', (event) => {
        const data = JSON.parse(event.data)
        this.location = data.localtion || ''
        this.ssmType = data.ssmType || ''
        if (data.type === 'area') {
          this.activeAnalysisEvent('#pointDay', 'day')
        } else if (data.type === 'tag') {
          this.ipasPointRecordShow = true
          this.$nextTick(() => {
            this.$refs.ipasPointRecord.getPointDetailTableList()
          })
          window.chrome.webview.hostObjects.sync.bridge.ShowTable(true)
        }
      })
    } catch (errpr) {}
  },
  methods: {
    // 巡检点分析
    getTypeAnalysisTable(val) {
      this.tableLoading = true
      getTaskPointSpaceList({ dateType: val, spaceCode: this.location, spaceLevel: this.ssmType, spaceIdentifying: 0 }).then((res) => {
        const data = res.data
        this.tableLoading = false
        if (data.code === '200') {
          const arr = data.data.list
          if (arr.length) {
            this.tableData = data.data.list
          } else {
            this.tableData = []
          }
        }
      })
    },
    activeAnalysisEvent(type, val) {
      this.dateType = val
      $('#pointDay').removeClass('active')
      $('#pointWeek').removeClass('active')
      $('#pointMonth').removeClass('active')
      $(type).addClass('active')
      this.getTypeAnalysisTable(val)
    },
    ipasPointRecordChange() {
      this.ipasPointRecordShow = !this.ipasPointRecordShow
      this.$nextTick(() => {
        this.$refs.ipasPointRecord.getPointDetailTableList()
      })
      try {
        window.chrome.webview.hostObjects.sync.bridge.ShowTable(this.ipasPointRecordShow)
      } catch (error) {}
    },
    configCloseDialog() {
      this.ipasPointRecordShow = false
      try {
        window.chrome.webview.hostObjects.sync.bridge.ShowTable(false)
      } catch (error) {}
    },
    retrospectCloseDialog() {
      if (this.toPage === 'inspection') {
        this.toPage = ''
        Object.assign(this, {
          detailId: '',
          ipasPointDetailShow: false,
          ipasPointRecordShow: false
        })
      } else {
        Object.assign(this, {
          detailId: '',
          ipasPointDetailShow: false,
          ipasPointRecordShow: true
        })
      }
      this.$nextTick(() => {
        // document.getElementsByClassName('transparentBgColor')[0].style.backgroundColor = 'center'
        this.activeAnalysisEvent('#pointDay', 'day')
        this.$refs.ipasPointRecord.getPointDetailTableList()
      })
      try {
        window.chrome.webview.hostObjects.sync.bridge.ShowTable(this.ipasPointRecordShow)
        window.chrome.webview.hostObjects.sync.bridge.ShowDialog(false)
      } catch (error) {}
    },
    backToWPFhome() {
      window.chrome.webview.hostObjects.sync.bridge.Back('businessIPAS')
    },
    toPointList(row, column, cell, event) {
      this.toPage = 'inspection'
      this.ipasPointRecordShow = true
      this.$nextTick(() => {
        row.dateType = this.dateType
        this.$refs.ipasPointRecord.getPointDetailTableList('inspection', row)
      })
      try {
        window.chrome.webview.hostObjects.sync.bridge.ShowTable(true)
      } catch (error) {}
    }
  }
}
</script>
<style lang="scss" scoped>
.content {
  width: 100%;
  height: 100%;
  position: relative;
  display: flex;
  .active {
    background: url('~@/assets/images/center/light-yellow.png') no-repeat;
    background-size: 100% 100%;
    color: #ffe3a6 !important;
    outline: none;
  }
  .title {
    height: 30px;
    position: relative;
    div {
      cursor: pointer;
      position: absolute;
      top: 5px;
      left: 0;
      width: 80px;
      height: 30px;
      line-height: 30px;
      text-align: center;
      color: #618ad3;
      font-size: 0.875rem;
      white-space: nowrap;
      background-image: url(../../../assets/images/peace/btn-back.png);
      background-size: 100% 100%;
      background-repeat: no-repeat;
    }
  }
  .content-left {
    width: 25%;
    height: calc(100% - 35px);
    padding-top: 35px;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
  }
  .left-bottom {
    width: 100%;
    height: 50%;
    background: red;
    background: url('~@/assets/images/center/ioms-analysis-bg.png') no-repeat;
    background-size: 100% 100%;
    ::v-deep .el-table {
      border: none !important;
      .el-table__header .el-table__cell {
        padding: 5px 0 !important;
        color: #fff;
      }
      .el-table__body {
        tr {
          background: center;
        }
        td.el-table__cell,
        th.el-table__cell.is-leaf {
          border-right: 2px solid #0a164e;
          border-bottom: 2px solid #0a164e;
          background: rgba(56, 103, 180, 0.2);
          color: #fff;
        }
      }
    }
  }
  .bg-title {
    margin-top: 5px;
    // height: 23%;
    height: 2.8rem;
    line-height: 2.8rem;
    color: #d4e3f9;
    padding: 0 1.5rem 0 3rem;
    font-family: TRENDS;
    /*设置为伸缩容器*/
    display: -webkit-box;
    display: -moz-box;
    display: -ms-flexbox;
    display: -webkit-flex;
    display: flex;
    /*垂直居中*/
    -webkit-box-align: center; /*旧版本*/
    -moz-box-align: center; /*旧版本*/
    -ms-flex-align: center; /*混合版本*/
    -webkit-align-items: center; /*新版本*/
    align-items: center; /*新版本*/
    justify-content: space-between;
    .right-search {
      span {
        display: inline-block;
        width: 35px;
        height: 30px;
        font-size: 14px;
        text-align: center;
        line-height: 26px;
        margin: 0 4px;
        color: #7eaef9;
        cursor: pointer;
      }
      span:hover {
        background: url('~@/assets/images/center/light-yellow.png') no-repeat;
        background-size: 100% 100%;
        color: #ffe3a6;
      }
      i {
        color: #7eaef9;
      }
    }
  }
  .bg-content {
    position: relative;
    box-sizing: border-box;
    padding: 10px 10px 15px 10px;
    width: 100%;
    height: calc(100% - 2.5rem);
    #deptProduceEcharts {
      width: 100%;
      height: 100%;
    }
  }
}
</style>
