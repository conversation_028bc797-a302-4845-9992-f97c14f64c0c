<template>
  <div>
    <el-row>
      <el-col :span="8">
        <div class="item">
          <span class="item-title">资产编码</span>
          <span class="item-content">{{ detailsInfo.资产编码 || '--' }}</span>
        </div>
        <div class="item">
          <span class="item-title">资产名称</span>
          <span class="item-content">{{ detailsInfo.资产名称 || '--' }}</span>
        </div>
        <div class="item">
          <span class="item-title">68编码</span>
          <span class="item-content">{{ detailsInfo['68编码'] || '--' }}</span>
        </div>
        <div class="item">
          <span class="item-title">财务编码</span>
          <span class="item-content">{{ detailsInfo.财务编码 || '--' }}</span>
        </div>
        <div class="item">
          <span class="item-title">规格型号</span>
          <span class="item-content">{{ detailsInfo.规格型号 || '--' }}</span>
        </div>
        <div class="item">
          <span class="item-title">出厂编码</span>
          <span class="item-content">{{ detailsInfo.出厂编码 || '--' }}</span>
        </div>
        <div class="item">
          <span class="item-title">所在区域</span>
          <span class="item-content">{{ detailsInfo.所在区域 || '--' }}</span>
        </div>
        <div class="item">
          <span class="item-title">存放位置</span>
          <span class="item-content">{{ detailsInfo.存放位置 || '--' }}</span>
        </div>
        <div class="item">
          <span class="item-title">使用科室</span>
          <span class="item-content">{{ detailsInfo.使用科室 || '--' }}</span>
        </div>
      </el-col>
      <el-col :span="8">
        <div class="item">
          <span class="item-title">管理员</span>
          <span class="item-content">{{ detailsInfo.管理员|| '--' }}</span>
        </div>
        <div class="item">
          <span class="item-title">计量单位</span>
          <span class="item-content">{{ detailsInfo.计量单位 || '--' }}</span>
        </div>
        <div class="item">
          <span class="item-title">入库日期</span>
          <span class="item-content">{{ detailsInfo.入库日期 || '--' }}</span>
        </div>
        <div class="item">
          <span class="item-title">安装日期</span>
          <span class="item-content">{{ detailsInfo.安装日期 || '--' }}</span>
        </div>
        <div class="item">
          <span class="item-title">注册证号</span>
          <span class="item-content">{{ detailsInfo.注册证号 || '--' }}</span>
        </div>
        <!-- <div class="item">
          <span class="item-title">维保到期</span>
          <span class="item-content">{{ detailsInfo.warrantyExpiresDateLine || '--' }}</span>
        </div> -->
        <div class="item">
          <span class="item-title">出库日期</span>
          <span class="item-content">{{ detailsInfo.出库日期 || '--' }}</span>
        </div>
        <div class="item">
          <span class="item-title">验收日期</span>
          <span class="item-content">{{ detailsInfo.验收日期 || '--' }}</span>
        </div>
        <div class="item">
          <span class="item-title">启用日期</span>
          <span class="item-content">{{ detailsInfo.启用日期 || '--' }}</span>
        </div>
      </el-col>
      <el-col :span="8">
        <div class="item">
          <span class="item-title">资产用途</span>
          <span class="item-content">{{ detailsInfo.资产用途 || '--' }}</span>
        </div>
        <div class="item">
          <span class="item-title">成本核算</span>
          <span class="item-content">{{ detailsInfo.成本核算 || '--' }}</span>
        </div>
        <div class="item">
          <span class="item-title">资产来源</span>
          <span class="item-content">{{ detailsInfo.资产来源 || '--' }}</span>
        </div>
        <div class="item">
          <span class="item-title">是否进口</span>
          <span class="item-content">{{ detailsInfo.是否进口 || '--' }}</span>
        </div>
        <div class="item">
          <span class="item-title">资产状态</span>
          <span class="item-content">{{ detailsInfo.资产状态 || '--' }}</span>
        </div>
        <div class="item">
          <span class="item-title">采购合同</span>
          <span class="item-content">{{ detailsInfo.采购合同 || '--' }}</span>
        </div>
        <div class="item">
          <span class="item-title">保修合同</span>
          <span class="item-content">{{ detailsInfo.保修合同 || '--' }}</span>
        </div>
        <div class="item">
          <span class="item-title">保修日期</span>
          <span class="item-content">{{ detailsInfo.保修日期 || '--' }}</span>
        </div>
      </el-col>
    </el-row>
  </div>
</template>
<script>
export default {
  name: 'BaseInfo',
  props: ['detailsInfo'],
  data() {
    return {}
  },
  methods: {},
  mounted() {
    console.log(this.detailsInfo)
  }
}
</script>

<style scoped lang="scss">
  .item {
    margin-bottom: 12px;
    font-size: 14px;
    display: flex;
    height: 20px;
    line-height: 20px;
    .item-title {
      color: #7EAEF9;
      width: 100px;
    }
    .item-content {
      color: #fff;
    }
  }
</style>
