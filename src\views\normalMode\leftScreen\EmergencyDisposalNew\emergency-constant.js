

export const alarmAffirmConfig = [
  { value: '1', label: '确警' },
  { value: '2', label: '误报' },
  { value: '3', label: '演习' },
  { value: '4', label: '调试' }
];

/** 确警类型 */
export const alarmConfirmOptions = alarmAffirmConfig.filter(x => x.value != '4');

export const alarmLevelConfig = [
  { value: '0', label: '通知', color: '#3562DB' },
  { value: '1', label: '一般', color: '#3562DB' },
  { value: '2', label: '紧急', color: '#FF9435' },
  { value: '3', label: '重要', color: '#FA403C' }
]

export const alarmStatusConfig = [
  { label: '未处理', value: '0', color: '#FF2D55', icon: 'el-icon-warning' },
  { label: '处理中', value: '1', color: '#FD9434', icon: 'el-icon-success' },
  { label: '已处理', value: '2', color: '#61E29D', icon: 'el-icon-success' },
]

export const alarmWorkOrderConfig = [
  { label: '未派工', value: '2', color: '#FF2D55', icon: 'el-icon-warning' },
  { label: '处理中', value: '3', color: '#FD9434', icon: 'el-icon-success' },
  { label: '已完成', value: '4', color: '#61E29D', icon: 'el-icon-success' },
]


export const toDetailTab = {
  base: 'EmergencyBaseInfo',
  execute: 'EmergencyExecute',
  order: 'EmergencyWorkOrder',
  summary: 'EmergencySummary'
}

/** 批量操作按钮 */
export const batchControlList = [
  { state: 1, label: '确警' },
  { state: 3, label: '演习' },
  { state: 2, label: '误报' },
  { state: 6, label: '屏蔽' }
]

/** 批量操作类型 */
export const batchControlType = {
  /** 确警 */
  confirm: 1,
  /** 演习 */
  maneuver: 3,
  /** 误报 */
  mistake: 2,
  /** 屏蔽 */
  shield: 6
}

/** 流程处理状态配置 */
export const workflowTypeConfig = [
  { status: 0, label: "创建报警记录" },
  { status: 1, label: "真实报警" },
  { status: 2, label: "误报" },
  { status: 3, label: "演练" },
  { status: 4, label: "调试" },
  { status: 5, label: "未确认" },
  { status: 6, label: "备注" },
  { status: 7, label: "关闭" },
  { status: 8, label: "设置为经典案例" },
  { status: 9, label: "取消为经典案例" },
  { status: 10, label: "派单" },
  { status: 11, label: "新建工单" },
  { status: 12, label: "已屏蔽" },
  { status: 13, label: "取消屏蔽" }
]

/** 屏蔽类型 */
export const shieldConfig = [
  { status: 2, label: "误报" },
  { status: 4, label: "调试" },
  { status: 99, label: "其他" },
]

/** 操作平台 */
export const platformConfig = [
  { value: 0, label: "PC端" },
  { value: 1, label: "客户端" },
  { value: 2, label: "移动端" },
]

/** 操作平台 */
export const operatePlatform = {
  /** pc 端 */
  pc: 0,
  /** 客户端 */
  client: 1,
  /** 移动端 */
  mobile: 2
}
