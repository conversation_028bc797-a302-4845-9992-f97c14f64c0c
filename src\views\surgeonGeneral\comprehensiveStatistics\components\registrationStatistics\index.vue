<template>
  <div class="echarts-topLeft">
    <BgTitle showMore @moreClick="showDetail">
      <template #title>挂号统计</template>
    </BgTitle>
    <div class="bg-content">
      <TopContent @itemClick="showDetail"/>
      <BottomContent @itemClick="showDetail"/>
    </div>
    <RegistDialog v-if="visible" :visible="visible" :params="detailParams" @close="visible = false"/>
    <ConsultationDialog v-if="consultationVisible" :visible="consultationVisible" :params="detailParams" @close="consultationVisible = false"/>
  </div>
</template>
<script>
import BgTitle from '../../components/common/bgTitle'
import TopContent from './topContent'
import BottomContent from './bottomContent'
import RegistDialog from './detail.vue'
import ConsultationDialog from '../consultationStatistics/detail.vue'
export default {
  components: {
    BgTitle,
    TopContent,
    BottomContent,
    RegistDialog,
    ConsultationDialog
  },
  data() {
    return {
      visible: false,
      consultationVisible: false,
      detailParams: {}
    }
  },
  mounted() {},
  methods: {
    showDetail (data) {
      this.detailParams = data || {}
      if (this.detailParams && this.detailParams.type === '1') {
        this.consultationVisible = true
      } else {
        this.visible = true
      }
    }
  }
}
</script>
<style lang="scss" scoped>
.echarts-topLeft {
  height: 100%;
  width: calc(33% - 8px);
  background: url("~@/assets/images/bg-content1.png") no-repeat;
  background-size: 100% 100%;
  box-sizing: border-box;
  .bg-content {
    position: relative;
    box-sizing: border-box;
    padding: 10px;
    width: 100%;
    height: calc(100% - 44px);
    display: flex;
    flex-wrap: wrap;
  }
}
.statistics_item {
  width: calc(100% - 4px);
  height: calc(50% - 4px);
  // border: 1px solid #fff;
  margin-right: 8px;
  &:nth-child(even) {
    margin-right: 0;
  }
}

.item_title {
  font-weight: 500;
  font-size: 16px;
  color: #b0e3fa;
  margin-bottom: 10px;
}
</style>
