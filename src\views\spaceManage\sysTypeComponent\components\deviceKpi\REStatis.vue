<template>
  <div class="RETrend">
    <div class="trend-item">
      <div class="item-left">
        <p>
          <span style="background: #8BDDF5;"></span>
          <span>年收入</span>
        </p>
        <p>{{RETrend.annualIncome}}</p>
      </div>
      <div class="item-right">
        <p>同比去年</p>
        <p>
          <i class="el-icon-top-right" style="color: #FF2D55; background: rgba(255, 45, 85, .2)"></i>
          <span style="color: #FF2D55">{{RETrend.annualIncomeLastYear}}</span>
        </p>
      </div>
    </div>
    <div class="trend-item">
      <div class="item-left">
        <p>
          <span style="background: #3CC1FF;"></span>
          <span>年支出</span>
        </p>
        <p>{{RETrend.annualExpenditures}}</p>
      </div>
      <div class="item-right">
        <p>同比去年</p>
        <p>
          <i class="el-icon-bottom-left" style="color: #61E29D; background: rgba(97, 226, 157, .2)"></i>
          <span style=" color: #61E29D">{{RETrend.annualExpendituresLastYear}}</span>
        </p>
      </div>
    </div>
    <div class="trend-item">
      <div class="item-left">
        <p>
          <span style="background: #0A84FF;"></span>
          <span>年利润</span>
        </p>
        <p>{{RETrend.annualProfit}}</p>
      </div>
      <div class="item-right">
        <p>同比去年</p>
        <p>
          <i class="el-icon-minus" style="color: #DADEE1; background: rgba(218, 222, 225, .2)"></i>
          <span style="color: #D6EFF1">{{RETrend.annualProfitLastYear}}</span>
        </p>
      </div>
    </div>
  </div>
</template>

<script>
import { mockRETrend } from './mockData.js'
export default {
  name: 'RETrend',
  data() {
    return {
      RETrend: {
        annualIncome: '--',
        annualIncomeLastYear: '-',
        annualExpenditures: '--',
        annualExpendituresLastYear: '-',
        annualProfit: '--',
        annualProfitLastYear: '-'
      }
    }
  },
  computed: {

  },
  created() {

  },
  mounted() {
    if (__PATH.VUE_APP_HOSPITAL_NODE === 'outernet') {
      this.RETrend = mockRETrend
    }
  },
  methods: {

  }
}

</script>

<style lang="scss" scoped>
.RETrend {
  width: 100%;
  height: 100%;
  .trend-item {
    display: flex;
    align-items: center;
    background: rgba(53, 98, 219, 0.06);
    margin-bottom: 8px;
    .item-left {
      min-width: 30%;
      padding: 30px 0px 32px 24px;
      p:first-child{
        span:first-child {
          width: 12px;
          height: 12px;
          display: inline-block;
          border-radius: 2px;
        }
        span:last-child {
          font-size: 16px;
          font-weight: 500;
          color: #FFFFFF;
          margin-left: 4px;
        }
      }
      p:last-child {
        color: #FFCA64;
        font-size: 24px;
        line-height: 36px;
        margin-top: 8px;
        &::after {
          content: '元';
          font-size: 16px;
          margin-left: 4px;
          color: #A6AFBF;
        }
      }
    }
    .item-right {
      padding-left: 32px;
      border-left: 1px solid rgba(133, 145, 206, 0.15);
      p:first-child{
        font-size: 14px;
        font-weight: 400;
        color: #FFFFFF;
      }
      p:last-child {
        span{
          display: inline-block;
          margin-top: 10px;
          margin-left: 4px;
        }
      }
    }
  }
}
</style>
