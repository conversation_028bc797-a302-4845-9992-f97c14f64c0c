<template>
  <div class="form-detail">
    <el-tabs v-model="tabsActiveName" @tab-click="handleClick">
      <el-tab-pane label="基本信息" name="1">
        <div class="plan-content">
          <ul class="item-row">
            <li class="width33">
              <span class="li-first-span">风险点名称</span><span class="li-last-span">{{ detailsInfo.riskName }}</span>
            </li>
            <li class="width33">
              <span class="li-first-span">风险位置</span><span class="li-last-span">{{ detailsInfo.riskPlace }}</span>
            </li>
            <li class="width33">
              <span class="li-first-span">风险研判</span><span class="li-last-span">{{ detailsInfo.riskLevelName }}</span>
            </li>
          </ul>
          <ul class="item-row">
            <li class="width33">
              <span class="li-first-span">责任部门</span><span class="li-last-span">{{ detailsInfo.taskTeamName }}</span>
            </li>
            <li class="width33">
              <span class="li-first-span">责任人</span><span
                class="li-last-span">{{ detailsInfo.responsiblePersonName }}</span>
            </li>
            <li class="width33">
              <span class="li-first-span">风险点编号</span><span class="li-last-span">{{ detailsInfo.riskCode }}</span>
            </li>
          </ul>
          <ul class="item-row">
            <li class="width33">
              <span class="li-first-span">应急电话</span><span class="li-last-span">{{ detailsInfo.urgentPhone }}</span>
            </li>
            <li class="width33">
              <span class="li-first-span">应急联系电话</span><span
                class="li-last-span">{{ detailsInfo.urgentContactPhone }}</span>
            </li>
            <li class="width33">
              <span class="li-first-span">事故后果</span><span class="li-last-span">{{ detailsInfo.accidentTypeList.map(item =>
                item.dictLabel).join('，') }}</span>
            </li>
          </ul>
          <ul class="item-row">
            <li class="width33">
              <span class="li-first-span">风险类型</span><span class="li-last-span">{{ detailsInfo.riskTypeName }}</span>
            </li>
            <li class="width33">
              <span class="li-first-span">管控层级</span><span class="li-last-span">{{ detailsInfo.free1Name }}</span>
            </li>
          </ul>
          <p style="padding: 1rem"></p>
          <ul class="item-row">
            <li class="width95">
              <span class="li-first-span">图片</span>
              <p class="li-last-span">
                <span v-for="(img, index) in detailsInfo.attachmentUrl" :key="index">
                  <el-image style="width: 100px; height: 100px; margin-right: 15px" :src="img" :preview-src-list="[img]">
                  </el-image>
                </span>
              </p>
              <span></span>
            </li>
          </ul>
          <ul class="item-row">
            <li class="width95">
              <span class="li-first-span">预案流程图</span>
              <p class="li-last-span">
                <span v-for="(img, index) in detailsInfo.processUrl" :key="index">
                  <el-image style="width: 100px; height: 100px; margin-right: 15px" :src="img" :preview-src-list="[img]">
                  </el-image>
                </span>
              </p>
              <span></span>
            </li>
          </ul>
          <p style="padding: 1rem"></p>
          <!-- <el-table
            :data="tableData"
            :cell-style="$tools.setCell(3)"
            :header-cell-style="$tools.setHeaderCell(3)"
            style="width: 100%"
          >
            <el-table-column fixed type="index" width="80" show-overflow-tooltip label="序号"></el-table-column>
            <el-table-column fixed prop="alarmSource" show-overflow-tooltip label="报警来源"></el-table-column>
            <el-table-column fixed prop="alarmTypeName" show-overflow-tooltip label="报警类型"></el-table-column>
            <el-table-column fixed prop="deviceName" show-overflow-tooltip label="报警项名称"></el-table-column>
          </el-table> -->
          <!-- 模版1 -->
          <el-table v-if="isFirstTableShow" :data="tableData" :cell-style="$tools.setCell(3)"
            :header-cell-style="$tools.setHeaderCell(3)">
            <!-- :header-cell-style="$tools.setHeaderCell(3)" height="tableHeight"> -->
            <el-table-column type="index" width="100" label="序号"></el-table-column>
            <el-table-column :label="detailsInfo.riskType === 3 ? '作业步骤' : '检查项目'" property="checkItem">
              <template slot-scope="scope">
                <span>{{ scope.row.checkItem }}</span>
              </template>
            </el-table-column>
            <el-table-column :label="detailsInfo.riskType === 3 ? '危险源或潜事件' : '标准'" property="standard">
              <template slot-scope="scope">
                <span>{{ scope.row.standard }}</span>
              </template>
            </el-table-column>
            <el-table-column :label="detailsInfo.riskType === 3 ? '可能发生的事故类型及后果' : '不符合标准情况及后果'"
              property="result">
              <template slot-scope="scope">
                <span>{{ scope.row.result }}</span>
              </template>
            </el-table-column>
            <el-table-column label="现有控制措施">
              <el-table-column label="工程技术措施" property="technicalMeasure">
                <template slot-scope="scope">
                  <span>{{ scope.row.technicalMeasure }}</span>
                </template>
              </el-table-column>
              <el-table-column label="管理措施" property="manageMeasure">
                <template slot-scope="scope">
                  <span>{{ scope.row.manageMeasure }}</span>
                </template>
              </el-table-column>
              <el-table-column label="培训教育措施" property="educationMeasure">
                <template slot-scope="scope">
                  <span>{{ scope.row.educationMeasure }}</span>
                </template>
              </el-table-column>
              <el-table-column label="个体防护措施" property="protectiveMeasure">
                <template slot-scope="scope">
                  <span>{{ scope.row.protectiveMeasure }}</span>
                </template>
              </el-table-column>
              <el-table-column label="应急措施" property="emergencyMeasure">
                <template slot-scope="scope">
                  <span>{{ scope.row.emergencyMeasure }}</span>
                </template>
              </el-table-column>
            </el-table-column>
            <el-table-column label="建议改进(新增)措施" property="adviceMeasure">
              <template slot-scope="scope">
                <span>{{ scope.row.adviceMeasure }}</span>
              </template>
            </el-table-column>
          </el-table>
          <!-- 模版2 -->
          <el-table v-else :data="tableData2" :cell-style="$tools.setCell(3)"
            :header-cell-style="$tools.setHeaderCell(3)">
            <el-table-column type="index" width="80" label="序号"></el-table-column>
            <el-table-column label="主要风险因素" property="riskFactor">
              <template slot-scope="scope">
                <span>{{ scope.row.riskFactor }}</span>
              </template>
            </el-table-column>
            <el-table-column label="安全操作要点" property="operationElement">
              <template slot-scope="scope">
                <span>{{ scope.row.operationElement }}</span>
              </template>
            </el-table-column>
            <el-table-column label="主要风险管控措施" property="controlElement">
              <template slot-scope="scope">
                <span>{{ scope.row.controlElement }}</span>
              </template>
            </el-table-column>
            <el-table-column label="应急处置措施" property="handleElement">
              <template slot-scope="scope">
                <span>{{ scope.row.handleElement }}</span>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </el-tab-pane>
      <el-tab-pane label="风险巡检记录" name="2">
        <div class="search-box">
          <el-select clearable v-model="formInline.investigationType" placeholder="用户类别">
            <el-option v-for="(item, index) in typeList" :label="item.name" :value="item.id" :key="index"></el-option>
          </el-select>
          <el-date-picker style="marginLeft: 10px;" v-model="formInline.createtTmeStart" type="date" clearable
            format="yyyy-MM-dd" value-format="yyyy-MM-dd" placeholder="创建日期">
          </el-date-picker>
          <el-date-picker style="marginLeft: 10px;" v-model="formInline.createtTmeEnd" type="date" clearable
            format="yyyy-MM-dd" value-format="yyyy-MM-dd" placeholder="结束日期">
          </el-date-picker>
          <el-button style="marginLeft: 10px;" @click="checkRiskInvestigation(rowData.id)">查询</el-button>
          <el-button @click="reset">重置</el-button>
        </div>
        <el-table :data="riskInvestigationRecordData" :cell-style="$tools.setCell(3)"
          :header-cell-style="$tools.setHeaderCell(3)">
          <el-table-column label="序号" type="index" width="100" />
          <el-table-column prop="createPersonName" label="排查人" show-overflow-tooltip></el-table-column>
          <el-table-column prop="teamName" label="部门" show-overflow-tooltip></el-table-column>
          <el-table-column prop="createPersonPhone" label="联系方式" show-overflow-tooltip></el-table-column>
          <el-table-column prop="investigationType" show-overflow-tooltip label="用户类别">
            <template slot-scope="scope">
              <span>{{ scope.row.investigationType == 1 ? '匿名用户' : scope.row.investigationType == 2 ? '系统用户' : '' }}</span>
            </template>
          </el-table-column>
          <!-- <el-table-column prop="riskPlace" show-overflow-tooltip label="排查记录" width="180">
            <template slot-scope="scope">
              <span @click="lookDetail(scope.row)" style="color:#5188fc;cursor: pointer;">查看详情</span>
            </template>
          </el-table-column> -->
          <el-table-column prop="recordState" show-overflow-tooltip label="排查结果">
            <template slot-scope="scope">
              <span>{{ scope.row.recordState == 1 ? '合格' : scope.row.recordState == 2 ? '异常' : '' }}</span>
            </template>
          </el-table-column>
          <!-- <el-table-column prop="workOrderCode" show-overflow-tooltip label="隐患记录" width="130" >
            <template slot-scope="scope">
              <span v-if="scope.row.workOrderCode" @click="workOrderDetails(scope.row)" style="color:#5188fc;cursor: pointer;">{{scope.row.workOrderCode}}</span>
              <span v-else >无</span>
            </template>
          </el-table-column> -->
          <el-table-column prop="investigationTime" show-overflow-tooltip label="排查时间"></el-table-column>
        </el-table>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script>
import { GetRiskDetail } from '@/utils/centerScreenApi'
import { CheckRiskInvestigation, GetRiskInvestigationRecord } from '@/utils/spaceManage'

export default {
  name: 'riskPointDetailsList',
  props: {
    rowData: {
      type: Object,
      default: function () {
        return {}
      }
    }
  },
  data() {
    return {
      formInline: {
        investigationType: '',
        createtTmeStart: '',
        createtTmeEnd: ''
      },
      typeList: [
        {
          name: '匿名用户',
          id: 1
        },
        {
          name: '系统用户',
          id: 2
        }
      ],
      tabsActiveName: '1',
      detailsInfo: {
        accidentTypeList: []
      },
      tableData: [],
      tableData2: [],
      riskInvestigationRecordData: [],
      tableHeight: 0
    }
  },
  computed: {
    isFirstTableShow() {
      if ((this.detailsInfo.riskType === 1 || this.detailsInfo.riskType === 2 || this.detailsInfo.riskType === 3) && this.detailsInfo.analysisList[0].templateType === '1') {
        return true
      } else {
        return false
      }
    }
  },
  mounted() {
    if (this.tabsActiveName === '1') {
      this.getRiskDetail(this.rowData.id)
    } else {
      this.checkRiskInvestigation(this.rowData.id)
    }
    try {
      window.chrome.webview.hostObjects.sync.bridge.ShowDialog(true)
    } catch (error) { }
    this.calHeight()
  },
  beforeDestroy() {
    try {
      window.chrome.webview.hostObjects.sync.bridge.ShowDialog(false)
    } catch (error) { }
  },
  created() {
    // if (this.$route.query.id) {
    //   this.getRiskDetail(this.$route.query.id)
    // }
  },
  methods: {
    // 查询重置
    reset() {
      this.formInline.investigationType = ''
      this.formInline.createtTmeStart = ''
      this.formInline.createtTmeEnd = ''
      this.checkRiskInvestigation(this.rowData.id)
    },
    handleClick(tab) {
      if (tab === '1') {
        this.getRiskDetail(this.rowData.id)
      } else {
        this.checkRiskInvestigation(this.rowData.id)
      }
    },
    checkRiskInvestigation(id) {
      CheckRiskInvestigation({ riskId: id }).then(res => {
        if (res.data.code === '200' && res.data.data.riskInvestigationId) {
          GetRiskInvestigationRecord({ ...this.formInline, riskInvestigationId: res.data.data.riskInvestigationId, currentPage: 1, pageSize: 999 }).then(res => {
            if (res.data.code === '200') {
              this.riskInvestigationRecordData = res.data.data.list
            }
          })
        } else {
          this.$message.error(res.data.message)
        }
      })
    },
    getRiskDetail(id) {
      const analysisListArr = []
      const analysisListArr2 = []
      GetRiskDetail({ id }).then(res => {
        if (res.data.code === '200') {
          this.detailsInfo = res.data.data
          if (res.data.data.analysisList && res.data.data.analysisList.length) {
            res.data.data.analysisList.forEach((item) => {
              analysisListArr.push({
                checkItem: item.checkItem,
                standard: item.standard,
                result: item.result,
                technicalMeasure: item.technicalMeasure,
                manageMeasure: item.manageMeasure,
                educationMeasure: item.educationMeasure,
                protectiveMeasure: item.protectiveMeasure,
                emergencyMeasure: item.emergencyMeasure,
                adviceMeasure: item.adviceMeasure
              })
            })
            res.data.data.analysisList.forEach((item) => {
              analysisListArr2.push({
                riskFactor: item.riskFactor,
                operationElement: item.operationElement,
                controlElement: item.controlElement,
                handleElement: item.handleElement
              })
            })
          }
          this.tableData = analysisListArr
          this.tableData2 = analysisListArr2
        }
      })
    },
    calHeight() {
      this.$nextTick(() => {
        const rect = this.$refs.container.getBoundingClientRect()
        this.tableHeight = rect.tableHeight
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.form-detail {
  height: calc(100% - 30px);
  width: 100%;
  padding: 10px 25px;
  overflow-y: scroll;
  box-sizing: border-box;

  .plan-content {
    width: 100%;
    // padding: 20px 0px 20px 20px;
    color: #b5bacb;
    font-size: 13px;

    .item-row {
      width: 100%;
      display: flex;
      padding: 20px 0px 20px 30px;
      box-sizing: border-box;

      .width33 {
        width: 33%;
      }

      .width95 {
        width: 95%;
        display: flex;
      }

      ::v-deep .el-image__error,
      ::v-deep .el-image__placeholder {
        background: center;
      }

      .li-first-span {
        display: inline-block;
        width: 120px;
        // margin-right: 20px;
        font-size: 14px;
        font-family: PingFang-SC-Medium, PingFang-SC;
        font-weight: 500;
        color: #7eaef9;
      }

      .li-last-span {
        display: inline-block;
        width: calc(100% - 120px);
        font-size: 14px;
        font-family: PingFang-SC-Medium, PingFang-SC;
        font-weight: 500;
        color: #ffffff;
        // align-items: center;
      }

      #audio-box {
        display: flex;
      }

      #audio-box>audio {
        width: 260px;
        height: 30px;
      }

      #audio-box>a {
        width: 40px;
        text-align: center;
        background-color: #2cc7c5;
        height: 35px;
        line-height: 35px;
        color: #fff;
        border-radius: 5px;
        margin-left: 10px;
      }
    }
  }

  ::v-deep .el-tabs {
    .el-tabs__nav {
      .el-tabs__item {
        font-size: 14px;
        color: #FFFFFF;
      }

      .is-active {
        color: #FFE3A6;
      }
    }

    .el-tabs__active-bar {
      background-color: #FFE3A6;
    }

    .el-tabs__nav-wrap::after {
      height: 1px;
      background-color: #7EAEF9;
    }
  }

  .search-box {
    padding: 20px 10px;
  }
}
</style>
