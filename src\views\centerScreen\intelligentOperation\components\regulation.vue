<template>
  <div class="main">
    <el-dialog v-dialogDrag :visible.sync="regDialogShow" :close-on-click-modal="false" :before-close="closeDialog" class="personDialog">
      <template slot="title">
        <span class="dialog-title">{{ rowData.quipmentName }}调控</span>
      </template>
      <div class="dialog-content" style="width: 100%">
        <el-form ref="formInline" :model="formInline" :rules="rules" :inline="true" label-width="auto" size="medium">
          <div class="formRow" style="display: flex">
            <el-form-item label="送风温度：" style="width: 40%">
              <span class="form-detail-span">18</span>
            </el-form-item>
            <el-form-item label="送风温度设定：" prop="dictName">
              <el-input v-model.trim="formInline.dictName" placeholder="请输入"><i slot="suffix">℃</i></el-input>
            </el-form-item>
          </div>
          <div class="formRow" style="display: flex">
            <el-form-item label="送风湿度：" style="width: 40%">
              <span class="form-detail-span">25</span>
            </el-form-item>
            <el-form-item label="送风湿度设定：" prop="dictCode">
              <el-input v-model.trim="formInline.dictCode" placeholder="请输入"><i slot="suffix">℃</i></el-input>
            </el-form-item>
          </div>
        </el-form>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button class="sino-button-sure" @click="closeDialog">取 消</el-button>
        <el-button class="sino-button-sure" type="primary" @click="dictSaveFn">保 存</el-button>
      </span>
    </el-dialog>
  </div>
</template>
<script>
export default {
  name: 'regulation',
  props: {
    regDialogShow: {
      type: Boolean,
      default: false
    },
    rowData: {
      type: Object,
      default: function () {
        return {}
      }
    }
  },
  data() {
    return {
      formInline: {
        dictName: '',
        dictCode: ''
      },
      rules: {}
    }
  },
  watch: {},
  mounted() {
    // Object.assign(this.formInline, this.rowData)
  },
  methods: {
    // 取消按钮
    closeDialog() {
      this.$emit('closeDialog')
    },
    // 确认按钮
    dictSaveFn() {
      this.$emit('sure')
    }
  }
}
</script>

<style lang="scss" type="text/css" scoped>
::v-deep .el-dialog {
  width: 35%;
}
.formRow {
  width: 100%;
  display: flex;
  ::v-deep .el-form-item {
    // flex: 1;
    display: flex;
  }
  ::v-deep .el-form-item__content {
    flex: 1;
  }
}
</style>
