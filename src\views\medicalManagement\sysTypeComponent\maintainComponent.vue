<template>
  <div class='content'>
    <div class="insp">
      <div class="pieHeader">
        <div class="headerLeft">
          <span :class="pieType == 0 ? 'activType' : ''" @click="pieType = 0">一级巡检</span>
          <span :class="pieType == 1 ? 'activType' : ''" @click="pieType = 1">二级保养</span>
          <span :class="pieType == 2 ? 'activType' : ''" @click="pieType = 2">三级养护</span>
        </div>
        <div class="headerRight">
          <el-dropdown trigger="click" @command="(val) => (tagCurrent = val)">
            <span style="color: #fff;"> 今天 <i class="el-icon-arrow-down"></i> </span>
            <el-dropdown-menu slot="dropdown">
              <el-dropdown-item :command="'1'" :class="{ isBjxl: tagCurrent == '1' }">今天</el-dropdown-item>
              <el-dropdown-item :command="'2'" :class="{ isBjxl: tagCurrent == '2' }">本周</el-dropdown-item>
              <el-dropdown-item :command="'3'" :class="{ isBjxl: tagCurrent == '3' }">本月</el-dropdown-item>
              <el-dropdown-item :command="'4'" :class="{ isBjxl: tagCurrent == '4' }">本年</el-dropdown-item>
              <el-dropdown-item :command="'5'" :class="{ isBjxl: tagCurrent == '5' }">自定义</el-dropdown-item>
            </el-dropdown-menu>
          </el-dropdown>
          <svg-icon class="right-expand" name="right-expand" @click="showDialog" />
        </div>
      </div>
      <div id="workOrderStatisticsEcharts" ref="workOrderStatisticsEcharts"></div>
    </div>
    <ModuleCard ref="taskBox" title="巡检任务清单" :hasExpand="true" class="task-list module-container" @emit-expand="allTableChange('icis')">
      <div slot="content" class="task-list-box" style="height: 100%">
        <div class="statistics">
          <div v-for="(item, index) in statisticsData" :key="index" class="statisticsItem">
            <span style="font-size: 14px;">{{ item.name }}</span>
            <span class="count">{{ item.count }}</span>
          </div>
        </div>
        <div class="module-content" style="flex: 1; height: 100%">
          <el-table
            :data="tableData"
            height="calc(100% - 44px)"
            style="width: 100%">
            <el-table-column
              prop="name"
              width="80"
              label="任务名称">
            </el-table-column>
            <el-table-column
              prop="type"
              width="80"
              label="周期类型">
            </el-table-column>
            <el-table-column
              prop="date"
              label="任务期限">
            </el-table-column>
            <el-table-column
              prop="accomplishType"
              width="80"
              label="完成状态">
            </el-table-column>
          </el-table>
        </div>
      </div>
    </ModuleCard>
    <DialogFrame :visible.sync="dialogFrame.show"  :breadcrumb="breadcrumb" :title="dialogFrame.title" @update:visible="update" @back="back">
      <component :is="activeComponent" @openDetailComponent="openDetailComponent"></component>
    </DialogFrame>
  </div>
</template>
<script>
import * as echarts from 'echarts'
export default {
  components: {
    DialogFrame: () => import('@/components/common/DialogFrame'),
    maintainList: () => import('../components/maintain/maintainList.vue'),
    maintainDetail: () => import('../components/maintain/maintainDetail.vue')
  },
  data() {
    return {
      tagCurrent: '1',
      pieType: 0,
      dialogFrame: {
        show: false,
        title: ''
      },
      tableLoading: false,
      tableData: [
        {
          name: '基建科任务',
          type: '每日任务',
          date: '2024-08-30 08:00:00 ~ 202408-30 23:59:59',
          accomplishType: '已完成'
        },
        {
          name: '基建科任务',
          type: '每日任务',
          date: '2024-08-30 08:00:00 ~ 202408-30 23:59:59',
          accomplishType: '已完成'
        },
        {
          name: '基建科任务',
          type: '每周任务',
          date: '2024-08-30 08:00:00 ~ 202408-30 23:59:59',
          accomplishType: '已完成'
        },
        {
          name: '基建科任务',
          type: '每日任务',
          date: '2024-08-30 08:00:00 ~ 202408-30 23:59:59',
          accomplishType: '已完成'
        },
        {
          name: '基建科任务',
          type: '每月任务',
          date: '2024-08-30 08:00:00 ~ 202408-30 23:59:59',
          accomplishType: '已完成'
        }
      ],
      breadcrumb: [{ label: '养护任务', name: 'maintainList' }],
      activeComponent: 'maintainList',
      statisticsData: [
        {
          name: '全部任务',
          count: 40
        },
        {
          name: '未执行',
          count: 40
        },
        {
          name: '已执行',
          count: 40
        },
        {
          name: '已停用',
          count: 40
        },
        {
          name: '已过期',
          count: 40
        }
      ]
    }
  },
  created() {
    this.$nextTick(() => {
      this.getAlarmSourceEchart()
    })
  },
  methods: {
    showDialog() {
      this.dialogFrame.show = true
      this.dialogFrame.title = '养护任务'
    },
    getAlarmSourceEchart() {
      let arr = [
        {name: '已完成', count: 0, percentage: 0},
        {name: '进行中', count: 0, percentage: 0},
        {name: '未开始', count: 92, percentage: 58.97},
        {name: '超时', count: 64, percentage: 41.03}
      ]
      const getchart = echarts.init(this.$refs.workOrderStatisticsEcharts)
      const data = []
      var color = ['#8fe7ea', '#74e2a0', '#f2d988', '#e38e6f', '#f2d988']
      const xdata = Array.from(arr, ({ name }) => name)
      for (var i = 0; i < arr.length; i++) {
        const randomRgbColor = this.$tools.randomRgbColor('array')
        data.push({
          name: arr[i].name,
          value: arr[i].count,
          percentage: arr[i].percentage,
          itemStyle: {
            normal: {
              borderWidth: 1,
              shadowBlur: 200,
              color: color[i] ?? randomRgbColor[0],
              label: {
                normal: { show: true }
              }
            }
          }
        })
      }
      const option = {
        backgroundColor: '',
        tooltip: {
          trigger: 'item'
        },
        legend: {
          icon: 'circle',
          orient: 'vartical',
          x: 'left',
          top: 'center',
          left: '52%',
          bottom: '0%',
          data: xdata,
          itemWidth: 8,
          itemHeight: 8,
          itemGap: 16,
          textStyle: {
            color: '#B3C2DD' //  字体颜色
          },
          formatter: function (name) {
            var oa = option.series[0].data
            for (var i = 0; i < option.series[0].data.length; i++) {
              if (name === oa[i].name) {
                return ' ' + name + '     ' + oa[i].percentage + '%'
              }
            }
          }
        },
        series: [
          {
            name: '',
            type: 'pie',
            center: ['30%', '50%'],
            label: {
              show: false
            },
            data: data
          }
        ]
      }
      getchart.clear()
      getchart.setOption(option)
      // 随着屏幕大小调节图表
      window.addEventListener('resize', () => {
        getchart.resize()
      })
      getchart.dispatchAction({
        type: 'highlight',
        seriesIndex: 0,
        dataIndex: 0
      })
      getchart.on('mouseover', (e) => {
        // 取消默认高亮
        getchart.dispatchAction({
          type: 'downplay',
          seriesIndex: 0,
          dataIndex: 0
        })
      })
      // 鼠标移出后默认高亮
      getchart.on('mouseout', (e) => {
        getchart.dispatchAction({
          type: 'highlight',
          seriesIndex: 0,
          dataIndex: 0
        })
      })
    },
    allTableChange() {
      this.dialogFrame.show = true
      this.dialogFrame.title = '养护任务'
    },
    openTaskPointList() {},
    tableLoadEvent() {
      // if (this.pageParams.total > this.pageParams.pageNo * this.pageParams.pageSize) {
      //   this.pageParams.pageNo++
      //   this.getInspectionDatas()
      // }
    },
    back(data) {
      this.breadcrumb = [{ label: '养护任务', name: 'maintainList' }]
      this.activeComponent = 'maintainList'
    },
    update() {
      this.breadcrumb = [{ label: '养护任务', name: 'maintainList' }]
      this.activeComponent = 'maintainList'
    },
    openDetailComponent(data) {
      this.breadcrumb.push({ label: '任务详情', name: 'maintainDetail' })
      this.activeComponent = data
    }
  }
}
</script>
<style lang="scss" scoped>
.content {
  width: 100%;
  height: 100%;
  overflow-y: auto;
  .insp {
    width: 100%;
    height: 24%;
    .pieHeader {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 0 10px;
      height: 40px;
      .headerLeft {
        span {
          display: inline-block;
          margin-right: 10px;
          font-size: 14px;
        }
        .activType {
          color: #409eff;
        }
      }
      .right-expand {
        margin-left: 8px;
        font-size: 24px;
        cursor: pointer;
      }
    }
    #workOrderStatisticsEcharts {
      width: 100%;
      height: calc(100% - 40px);
    }
  }
  .task-list {
    height: 74%;
    .statistics {
      display: flex;
      justify-content: space-between;
      align-items: center;
      .statisticsItem {
        margin: 5px 0;
        display: flex;
        flex-direction: column;
        align-items: center;
        .count {
          margin-top: 10px;
          color: #ffca64;
          font-size: 20px;
          font-weight: bold;
        }
      }
    }
  }
}
</style>