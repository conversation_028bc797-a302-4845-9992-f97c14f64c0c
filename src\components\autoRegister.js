/*
 * @Author: error: error: git config user.name & please set dead value or install git && error: git config user.email & please set dead value or install git & please set dead value or install git
 * @Date: 2023-02-27 14:35:19
 * @LastEditors: Please set LastEditors
 * @LastEditTime: 2023-11-14 11:48:09
 * @FilePath: \ihcrs_client_iframe\src\components\autoRegister.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
/**
 * 全局组件自动注册
 *
 * 全局组件各个组件按文件夹区分，文件夹名称与组件名无关联，但建议与组件名保持一致
 * 文件夹内至少保留一个文件名为 index 的组件入口，例如 index.vue
 * 普通组件必须设置 name 并保证其唯一，自动注册会将组件的 name 设为组件名，可参考 SvgIcon 组件写法
 * 如果组件是通过 js 进行调用，则确保组件入口文件为 index.js，可参考 ExampleNotice 组件
 */

import Vue from 'vue'
const componentsContext = import.meta.globEager('./*/index.(vue|js)')
for (const file_name in componentsContext) {
  // 获取文件中的 default 模块
  const componentConfig = componentsContext[file_name].default
  if (/.vue$/.test(file_name)) {
    Vue.component(componentConfig.name, componentConfig)
  } else {
    Vue.use(componentConfig)
  }
}
