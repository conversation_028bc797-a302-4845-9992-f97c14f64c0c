<template>
  <div class="warehouse-monitor-container">
    <div class="bottom-content">
      <div class="bottom-title">
        <div>库房监控</div>
        <div>
          <el-button class="elevatorClass" type="primary" @click="changePollingDialog(true)">轮询设置</el-button>
          <el-button class="elevatorClass" type="primary" @click="changePollingTimer">{{ autoplay ? '停止' : '开始' }}</el-button>
        </div>
      </div>
      <div class="videoName">{{ videoName }}</div>
      <el-carousel ref="elevatorCarousel" class="elevator_carousel" :interval="oldPollingTimer * 1000" :autoplay="autoplay" arrow="always" @change="changeCarousel">
        <el-carousel-item v-for="item in elevatorOptions" :key="item.factoryCode" :name="item.factoryCode">
          <RtspCavans v-if="elevatorSelectId == item.factoryCode" ref="RtspCavans" :rtspUrl="videoUrl" :videoName="videoName" :hasCavas="Boolean(videoUrl)" class="video_preview"></RtspCavans>
        </el-carousel-item>
      </el-carousel>
    </div>
    <ModuleCard title="库房列表" style="height: calc(65%)" class="container-box">
      <div slot="title-right">
        <div class="chart-icon" @click="handleAllCollapsed">
          <span>全部{{ collapseText }}</span>
          <span style="margin-left: 5px" :class="collapseIcon"></span>
        </div>
      </div>
      <div slot="content" v-loading="deviceTableLoading" v-infinite-scroll="tableLoadMore" class="realTime-module" style="height: calc(100% - 10px)">
        <div v-for="(item,index) in storehouseData" :key="index" class="realTime-module-box">
          <div class="rtm-header-box" :style="{borderColor: item.alarmStatus == 1 ? '#FF2D55' : 'rgba(133,145,206,0.5)'}" @click="tabledblClick(item)">
            <div class="rtm-header-title">
              <p>
                <span>{{ item.assetsName }}</span>
                <span :style="{
                  color: item.alarmStatus == 1 ? '#FF2D55' : (item.onlineStatus == 1 ? '#61E29D' : '#D4DEEC'),
                  background: item.alarmStatus == 1 ? 'rgba(255, 45, 85, .2)' : (item.onlineStatus == 1 ? 'rgba(97, 226, 157, .2)' : 'rgba(212, 222, 236, .2)')
                }">{{ item.alarmStatus == 1 ? '报警' : (item.onlineStatus == 1 ? '在线' : '离线') }}</span>
              </p>
              <div class="rtm-header-btn-text" style="color: #8BDDF5;" @click.stop="handleCollapseClick(index)">
                <span>{{ item.collapsed ? '展开' : '收起' }}</span>
                <span style="margin-left: 0px" :class="item.collapsed ? 'el-icon-arrow-down' : 'el-icon-arrow-up'"></span>
              </div>
            </div>
            <div class="rtm-header-btn-text">
              <span style="color: #C4F3FE;">物联设备：</span>
              <span style="margin-left: 5px">{{ item.iotCount }}</span>
            </div>
          </div>
          <div v-if="!item.collapsed" class="rtm-table-box">
            <!-- v-el-table-infinite-scroll="tableLoadMore" -->
            <el-table
              v-scrollHideTooltip
              class="table-center-transfer"
              :data="item.iotPropertyList"
              height="100%"
              :cell-style="{ padding: ' 8px', backgroundColor: 'transparent', border: 'none', padding: '3px', color: '#fff'}"
              :header-cell-style="{ background: '#8591CE26!important', color: '#8BDDF5FF', padding: '3px', fontWeight: 'bold',}"
              style="width: 100%"
            >
              <el-table-column prop="metadataName" show-overflow-tooltip label="监测参数" width="100"></el-table-column>
              <el-table-column prop="alarmStatus" show-overflow-tooltip label="物联状态" width="100">
                <template slot-scope="scope">
                  {{ scope.row.alarmStatus == 1 ? '报警' : (scope.row.onlineStatus == 1 ? '在线' : '离线') }}
                </template>
              </el-table-column>
              <el-table-column prop="valueText" show-overflow-tooltip label="当前值" width="100"></el-table-column>
              <el-table-column prop="sectionName" show-overflow-tooltip label="状态" width="100">
                <span slot-scope="scope" :style="{color: scope.row.sectionColor}">
                  {{ scope.row.sectionName }}
                </span>
              </el-table-column>
            </el-table>
          </div>
        </div>
        <div v-if="!storehouseData.length" class="center-center">暂无数据</div>
      </div>
    </ModuleCard>
    <el-dialog
      v-if="PollingDialogVisible"
      v-dialogDrag
      custom-class="polling-dialog"
      :modal="false"
      :close-on-click-modal="false"
      :visible.sync="PollingDialogVisible"
      :before-close="closePollingForm"
    >
      <span slot="title"> 轮询设置 </span>
      <div class="polling-content">
        <label>轮询时间：</label>
        <el-input v-model="newPollingTimer" oninput="value=value.replace(/^0|[^0-9]/g,'')" placeholder="请输入轮询时间" style="width: 200px"><template slot="append">秒</template></el-input>
      </div>
      <span slot="footer">
        <el-button class="elevatorClass" type="primary" plain @click="closePollingForm">取消</el-button>
        <el-button class="elevatorClass" type="primary" @click="submitPollingForm">保存</el-button>
      </span>
    </el-dialog>
  </div>
</template>
<script>
import { GetMonitoringItemsList, GetMonitoringItemsParams, GetMonitorVideoList, GetVideoRTSPAddress } from '@/utils/spaceManage'
export default {
  name: 'warehouseMonitoringComponent',
  components: {
    RtspCavans: () => import('../elevator/components/monitorOverview/components/rtspCavans.vue')
  },
  props: {
    roomData: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      deviceTableLoading: false,
      allCollapsed: true,
      storehouseData: [],
      pagination: {
        total: 0,
        pageSize: 15,
        pageNo: 1
      },
      // 监控列表
      elevatorOptions: [],

      elevatorSelectId: '',
      videoName: '',
      videoUrl: '', // 摄像机视频地址
      videoList: [{}],
      autoplay: true,
      pollingIndex: 0,
      PollingDialogVisible: false,
      newPollingTimer: 20, // 摄像机轮询时间'
      oldPollingTimer: 20 // 摄像机轮询默认20s
    }
  },
  computed: {
    collapseText() {
      return this.allCollapsed ? '展开' : '收起'
    },
    collapseIcon() {
      return this.allCollapsed ? 'el-icon-arrow-down' : 'el-icon-arrow-up'
    }
  },
  watch: {
    roomData: {
      handler: function(val) {
        this.pagination.pageNo = 1
        this.getMonitoringItemsList()
        this.getMonitorVideoList()
      },
      deep: true
      // immediate: true
    }
  },
  created() {
    try {
      window.chrome.webview.hostObjects.sync.bridge.GetPollingTimer()
    } catch (err) {}
  },
  mounted() {
    try {
      window.chrome.webview.addEventListener('message', (event) => {
        const data = JSON.parse(event.data)
        if (data.type === 'Elevator') {
          this.oldPollingTimer = data.PollingTimer
        }
      })
    } catch (error) {}
    this.getMonitoringItemsList()
    this.getMonitorVideoList()
  },
  methods: {
    tabledblClick(row) {
      this.$emit('roomEvent', {
        type: 'move',
        assetId: row.id,
        assetName: row.assetsName,
        modelCode: row.modelCode,
        spaceLocation: row.spaceLocation
      })
      try {
        window.chrome.webview.hostObjects.sync.bridge.EquipmentRealView(JSON.stringify({
          assetsId: row.id,
          DeviceName: row.assetsName,
          DeviceCode: row.modelCode,
          spaceLocation: row.spaceLocation
        }))
      } catch (error) {}
    },
    // 监测列表
    getMonitoringItemsList() {
      let params = {
        page: this.pagination.pageNo,
        pageSize: this.pagination.pageSize,
        spaceId: this.roomData.ssmCodes?.split(',')?.at(-1),
        sysOfCode: this.roomData.projectCode
      }
      this.deviceTableLoading = true
      GetMonitoringItemsList(params).then((res) => {
        this.deviceTableLoading = false
        if (res.data.code == 200) {
          if (this.pagination.pageNo == 1) this.storehouseData = []
          res.data.data.records.forEach(async (item) => {
            item.collapsed = true
            item.iotPropertyList = await this.getMonitoringItemsParams(item.id).catch()
          })
          this.storehouseData = this.storehouseData.concat(res.data.data.records)
          this.pagination.total = res.data.data.total
        }
      }).catch(() => {
        this.deviceTableLoading = false
      })
    },
    // 获取检测参数
    getMonitoringItemsParams(assetsId) {
      return new Promise((resolve, reject) => {
        GetMonitoringItemsParams({assetsId}).then((res) => {
          if (res.data.code == 200) {
            resolve(res.data.data)
          } else {
            reject(res)
          }
        }).catch((err) => {
          reject(err)
        })
      })
    },
    /** 全部展开/收起 */
    handleAllCollapsed() {
      this.allCollapsed = !this.allCollapsed
      this.storehouseData.forEach((item, i) => {
        item.collapsed = this.allCollapsed
      })
    },
    /** 展开/收起 */
    handleCollapseClick(index) {
      this.storehouseData[index].collapsed = !this.storehouseData[index].collapsed
      this.allCollapsed = this.storehouseData.every(item => item.collapsed)
    },
    tableLoadMore() {
      if (this.pagination.total > this.pagination.pageNo * this.pagination.pageSize) {
        this.pagination.pageNo++
        this.getMonitoringItemsList()
      }
    },

    // 获取摄像机列表
    getMonitorVideoList() {
      GetMonitorVideoList({
        spaceId: this.roomData.ssmCodes?.split(',')?.at(-1),
        sysOfCode: this.roomData.projectCode
      }).then((res) => {
        if (res.data.code == 200) {
          res.data.data.forEach(async (item) => {
            item.url = await this.getVideoRTSPAddress(item.factoryCode).catch(() => '')
          })
          this.elevatorOptions = res.data.data
          if (this.elevatorOptions.length) {
            this.elevatorSelectId = this.elevatorOptions[0].factoryCode
            this.videoName = this.elevatorOptions[0].cameraName
            this.videoUrl = this.elevatorOptions[0].url
          }
        }
      })
    },
    // 获取摄像机RTSP地址
    getVideoRTSPAddress(factoryCode) {
      return new Promise((resolve, reject) => {
        GetVideoRTSPAddress(factoryCode).then((res) => {
          if (res.data.status == 200 && (Object.keys(res.data.result[0]).length && res.data.result[0].code == 0)) {
            resolve(res.data.result[0].url)
          } else {
            reject('')
          }
        }).catch((err) => {
          reject(err)
        })
      })
    },

    // 通过轮播图改变选中电梯
    changeCarousel(val) {
      const selectData = this.elevatorOptions[val]
      this.elevatorSelectId = selectData.factoryCode
      this.videoUrl = selectData.url
      this.videoName = selectData.cameraName
    },
    // 切换摄像机轮询
    changePollingTimer() {
      this.autoplay = !this.autoplay
    },
    // 打开轮训设置弹框
    changePollingDialog() {
      try {
        window.chrome.webview.hostObjects.sync.bridge.ShowDialog(true)
      } catch (error) {}
      this.PollingDialogVisible = true
      this.newPollingTimer = this.oldPollingTimer
    },
    // 保存轮询设置表单时长
    submitPollingForm() {
      try {
        window.chrome.webview.hostObjects.sync.bridge.SavePollingTimer(this.newPollingTimer)
        window.chrome.webview.hostObjects.sync.bridge.ShowDialog(false)
      } catch (error) {
      }
      this.oldPollingTimer = this.newPollingTimer
      this.PollingDialogVisible = false
      if (!this.videoList.length) {
        this.autoplay = false
      } else {
        this.autoplay = true
      }
    },
    // 关闭轮询设置弹框
    closePollingForm() {
      try {
        window.chrome.webview.hostObjects.sync.bridge.ShowDialog(false)
      } catch (error) {}
      this.PollingDialogVisible = false
      this.newPollingTimer = ''
    }
  }
}
</script>
<style lang="scss" scoped>
.warehouse-monitor-container {
  %flex-styles {
    display: flex;
    align-items: center;
  }
  .chart-icon {
    font-size: 14px;
    cursor: pointer;
    color: #8BDDF5;
  }
  .realTime-module {
    height: 100%;
    .realTime-module-box {
      background: #8591ce26;
      margin-bottom: 10px;
      .rtm-header-box {
        padding: 16px;
        cursor: pointer;
        border: 1px solid;
        .rtm-header-title {
          @extend %flex-styles;
          justify-content: space-between;
          margin-bottom: 16px;
          font-size: 15px;
          span:last-child{
            margin-left: 10px;
            font-size: 14px;
            padding: 3px 8px;
            border-radius: 100px;
            line-height: 16px;
            display: inline-block;
          }
        }
        .rtm-header-btn-text {
          font-size: 14px;
          cursor: pointer;
        }
      }
      .rtm-table-box {
        border: 1px solid rgba(133,145,206,0.5);
        border-top: none;
      }
    }
  }
  .bottom-content {
    width: 100%;
    height: 35%;
    .bottom-title {
      width: 100%;
      height: 42px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      background: rgba(133, 145, 206, 0.15);
      padding: 0 8px;
      .elevatorClass {
        border-radius: 0px;
        height: 28px;
      }
    }
    .videoName {
      width: 100%;
      height: 34px;
      line-height: 34px;
      padding-left: 16px;
      background: #030915;
    }
    .video_preview {
      width: 100%;
      height: 100%;
    }
  }
  .elevator_carousel {
    height: calc(100% - 94px);
    margin-bottom: 10px;
    ::v-deep .el-carousel__container {
      height: 100%;
      .el-carousel__item {
        display: flex;
        .video_preview {
          width: 100%;
          height: 100%;
          margin-bottom: 10px;
        }
      }
      .el-carousel__arrow {
        width: 32px;
        height: 32px;
        background-color: transparent;
      }
      .el-carousel__arrow:hover {
        background-color: rgba(255, 255, 255, 0.1);
      }
    }
    ::v-deep .el-carousel__indicators {
      display: none;
    }
  }
  ::v-deep .polling-dialog {
    width: 350px;
    background: url('~@/assets/images/elevator/polling-bg.png') no-repeat;
    background-size: 100% 100%;
    .el-dialog__header {
      padding: 10px 10px 10px 26px;
      span {
        font-size: 20px;
        font-family: HarmonyOS Sans SC-Medium, HarmonyOS Sans SC;
        font-weight: 500;
        color: #dce9ff;
      }
      .el-dialog__headerbtn .el-dialog__close {
        color: #7cd0ff;
        font-size: 20px;
      }
    }
    .polling-content {
      label {
        font-size: 14px;
        font-family: PingFang SC-Regular, PingFang SC;
        font-weight: 400;
        color: #ffffff;
      }
      .el-input {
        border-radius: 4px 4px 4px 4px;
        .el-input__inner {
          background: rgba(3, 23, 81, 0.5);
          border: 1px solid #193382;
          color: #fff;
        }
        .el-input-group__append,
        .el-input-group__prepend {
          padding: 0 10px;
          background: rgba(3, 23, 81, 0.5);
          border-color: #193382;
          color: #fff;
        }
      }
    }
  }
}
</style>
