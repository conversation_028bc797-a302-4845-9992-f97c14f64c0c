<template>
  <div class="lightingControl">
    <!-- <div class="title" v-if="ssmType == '4' || ssmType == '5'">
      <div @click="backToWPFhome()"><i class="el-icon-arrow-left"></i>返回</div>
    </div> -->
    <!-- <el-button @click="showSpaceDetailsDialog">showSpaceDetails</el-button> -->
    <!-- <el-button @click="ssmType = ssmType == '4' ? '5' : '4'">changeBuild</el-button> -->
    <div class="right-content" v-if="!loopTableShow">
      <div class="top_title">
        <span class="dialog-title">统计总览</span>
      </div>
      <div class="top_Statistics">
        <div class="top_loop_num"><span>回路总数</span><strong>{{overviewData.total || 0}}</strong><span style="color: #ffe3a6">台</span></div>
        <div class="center_loop_statistics">
          <div v-if="loopStatisticsShow" class="center-center">暂无数据</div>
          <div v-else style="width: 100%; height: 100%; position: relative">
            <div id="loopStatisticsEcharts"></div>
            <div class="pie_background"></div>
          </div>
        </div>
        <div v-if="['3', '4'].includes(ssmType)" class="bottom_control">
          <div class="top_loop_num"><span>控制模块总数</span><strong>{{overviewData.controlCount || 0}}</strong><span style="color: #ffe3a6">台</span></div>
          <div class="bottom_unline_num"><img :src="require('@/assets/images/center/ic-unline.png')" alt="" /><span>{{overviewData.alarmControlCount || 0}}</span></div>
        </div>
      </div>
      <hospitalWideControl ref="hospitalWideControl" v-if="ssmType == '3'" :requestParams="requestParams" @hospitalWideSelect="hospitalWideSelect" />
      <currentBuildingControl :requestParams="requestParams" :infoData="selectHospitalWideData" v-else-if="ssmType == '4'" :ssmType="ssmType" />
      <!-- <buildingControl v-if="ssmType == '4'" :ssmType="ssmType"></buildingControl> -->
      <floorControl v-else-if="ssmType == '5' || ssmType == '6'" :modelCode="modelCode" :ssmType="ssmType" :requestParams="requestParams"></floorControl>
      <div v-if="ssmType == '4' || ssmType == '2'" class="back_box" @click="turnLoopTable(true)">查看全部回路</div>
    </div>
    <div class="right-content" v-else>
      <floorControl :ssmType="ssmType" :requestParams="requestParams"></floorControl>
      <div v-if="ssmType == '4'" class="back_box" @click="turnLoopTable(false)">返回总览</div>
    </div>
    <template v-if="spaceDetailsShow">
      <spaceDetails :show="spaceDetailsShow" :requestParams="requestParams" @closeDialog="hiddenSpaceDetailsDialog" />
    </template>
  </div>
</template>
<script>
import * as echarts from 'echarts'
import { mapGetters } from 'vuex'
import spaceDetails from './components/spaceDetails.vue'
import { GetLightingStatistics } from '@/utils/centerScreenApi'
export default {
  inject: ['reload'],
  name: 'lightingControl',
  components: {
    hospitalWideControl: () => import('./lightComponents/hospitalWideControl.vue'),
    currentBuildingControl: () => import('./lightComponents/currentBuildingControl.vue'),
    floorControl: () => import('./lightComponents/floorControl.vue'),
    // buildingControl: () => import('./lightComponents/buildingControl.vue'),
    spaceDetails
  },
  data() {
    return {
      ssmCodes: '',
      ssmType: '3',
      modelCode: '',
      loopTableShow: false, // 回路表格显示
      loopStatisticsShow: false, // 回路统计图是否显示
      spaceDetailsShow: false, // 空间明细弹窗
      overviewData: {}, // 总览数据
      selectHospitalWideData: {}, // 选中建筑数据
      requestParams: {
        projectCode: '00f4ad80e5c04809aae72c7470e8be28',
        constructionId: '', // 建筑id
        constructionName: '', // 建筑名称
        floorId: '', // 楼层id
        floorName: '', // 楼层名称
        spaceId: '' // 空间id
      }
    }
  },
  computed: {
    ...mapGetters({
      socketIemcMsgs: 'socketIemcMsgs'
    })
  },
  watch: {
    socketIemcMsgs(data) {
      const projectData = JSON.parse(data)
      // 匹配设备相同的接收消息刷新页面
      if (projectData.projectCode === this.requestParams.projectCode && projectData.data === 'LightRefresh') {
        // 收到消息刷新页面
        console.log('匹配成功', projectData)
        if (!this.loopTableShow) {
          this.getLoopStatisticsData()
        }
      }
    }
  },
  created() {},
  mounted() {
    this.getLoopStatisticsData()
    try {
      window.chrome.webview.addEventListener('message', (event) => {
        // {
        //   type:"area",
        //   buildingName: "综合急诊急救楼",
        //   buildingCode: "01001",
        //   ssmType: "6",
        //   modelCode: "BJSJTYY0100104060",
        //   localtion: "0100104060",
        //   ssmCodes: "#,1574997196330250241,1574997196833566721,1574997197265580033,1574997401503019009",
        //   floorName: "1F",
        //   floorCode: "04",
        //   roomCode: "060"
        // }
        const data = JSON.parse(event.data)
        if (Object.hasOwn(data, 'hover')) return
        this.requestParams = {
          projectCode: '00f4ad80e5c04809aae72c7470e8be28',
          constructionId: '', // 建筑id
          constructionName: '', // 建筑名称
          floorId: '', // 楼层id
          floorName: '', // 楼层名称
          spaceId: '' // 空间id
        }
        this.modelCode = data.modelCode || ''
        this.ssmCodes = data.ssmCodes || ''
        this.ssmType = data.ssmType || ''
        const newSsmCodes = this.ssmCodes.split(',')
        if (data.type === 'init') {
          // 页面重载
          this.reload()
        }
        // 空间等级：2医院，3区域，4建筑，5楼层，6房间
        if (this.ssmType === '4') {
          this.requestParams.constructionId = newSsmCodes[newSsmCodes.length - 1]
          this.requestParams.constructionName = data.buildingName
          this.selectHospitalWideData = this.$refs.hospitalWideControl.dataList.find(item => item.constructId === this.requestParams.constructionId)
        } else if (this.ssmType === '5') {
          this.requestParams.floorId = newSsmCodes[newSsmCodes.length - 1]
          this.requestParams.floorName = data.floorName
        } else if (this.ssmType === '6') {
          this.requestParams.floorName = data.floorName
          this.requestParams.spaceId = newSsmCodes[newSsmCodes.length - 1]
          this.showSpaceDetailsDialog()
        }
        this.getLoopStatisticsData()
      })
    } catch (error) {}
  },
  methods: {
    // 选择建筑
    hospitalWideSelect(data) {
      this.selectHospitalWideData = data
      this.requestParams.constructionName = data.constructName
      try {
        window.chrome.webview.hostObjects.sync.bridge.GetLightingSsmCode(data.constructId)
      } catch (error) {}
    },
    // 获取回路统计数据
    getLoopStatisticsData() {
      const arr = [
        {
          name: '开启',
          value: 9,
          icon: 'image://' + require('@/assets/images/center/on-light.png')
        },
        {
          name: '关闭',
          value: 12,
          icon: 'image://' + require('@/assets/images/center/off-light.png')
        },
        {
          name: '其他',
          value: 4,
          icon: 'image://' + require('@/assets/images/center/other-light.png')
        }
      ]
      GetLightingStatistics(this.requestParams).then((res) => {
        if (res.data.code === '200') {
          this.overviewData = res.data.data
          if (Object.hasOwn(res.data.data, 'openCount')) {
            this.loopStatisticsShow = false
            arr[0].value = res.data.data.openCount || 0
            arr[1].value = res.data.data.closeCount || 0
            arr[2].value = res.data.data.alarmCount || 0
            this.$nextTick(() => {
              this.loopStatisticsEchart(arr)
            })
          } else {
            this.loopStatisticsShow = true
          }
        } else {
          this.loopStatisticsShow = true
        }
      })
    },
    // 统计图echarts
    loopStatisticsEchart(arr) {
      const getchart = echarts.init(document.getElementById('loopStatisticsEcharts'))
      const nameList = Array.from(arr, (item) => item.name)
      const valueList = Array.from(arr, (item) => item.value)
      const data = []
      var color = ['rgba(255,231,84,0.5)', 'rgba(255,255,255,0.48)', 'rgba(255,84,84,0.5)']
      var borderColor = ['#FFE754', '#FFFFFF', '#FF5454']
      for (var i = 0; i < nameList.length; i++) {
        data.push({
          value: valueList[i],
          name: nameList[i],
          itemStyle: {
            normal: {
              borderWidth: 1,
              shadowBlur: 200,
              borderColor: borderColor[i],
              color: color[i],
              label: {
                normal: { show: true }
              }
            }
          }
        })
      }
      var seriesObj = [
        {
          name: '',
          type: 'pie',
          clockWise: false,
          center: ['32%', '50%'],
          radius: '60%',
          hoverAnimation: true,
          label: {
            normal: {
              show: false
            },
            emphasis: {
              show: false
            }
          },
          data: data
        }
      ]
      const option = {
        backgroundColor: '',
        tooltip: {
          show: false
        },
        legend: {
          orient: 'vartical',
          x: 'left',
          top: 'center',
          left: '60%',
          data: arr,
          itemWidth: 17,
          itemHeight: 22,
          itemGap: 14,
          textStyle: {
            fontSize: 13, // 字体大小
            color: '#B3C2DD' //  字体颜色
          },
          formatter: function (name) {
            var oa = option.series[0].data
            for (var i = 0; i < oa.length; i++) {
              if (name === oa[i].name) {
                return '   ' + name + '    ' + oa[i].value
              }
            }
          }
        },
        toolbox: {
          show: false
        },
        series: seriesObj
      }
      getchart.clear()
      getchart.setOption(option)
      // 随着屏幕大小调节图表
      window.addEventListener('resize', () => {
        getchart.resize()
      })
    },
    showSpaceDetailsDialog() {
      this.spaceDetailsShow = true
      try {
        // window.chrome.webview.hostObjects.sync.bridge.GetSwichFloorIsShow(true)
        window.chrome.webview.hostObjects.sync.bridge.ShowDialog(true)
      } catch (error) {}
    },
    hiddenSpaceDetailsDialog() {
      this.spaceDetailsShow = false
      try {
        // window.chrome.webview.hostObjects.sync.bridge.GetSwichFloorIsShow(false)
        window.chrome.webview.hostObjects.sync.bridge.ShowDialog(false)
      } catch (error) {}
    },
    // 查看全部回路以及跳转总览
    turnLoopTable(flag) {
      this.loopTableShow = flag
      if (!flag) {
        this.getLoopStatisticsData()
      }
    },
    backToWPFhome() {
      this.$router.push({ path: '/intelligentOperation?projectCode=00f4ad80e5c04809aae72c7470e8be28&type=webgl' })
      try {
        // window.chrome.webview.hostObjects.sync.bridge.Back('safetyOverview')
      } catch (error) {}
    }
  }
}
</script>
<style lang="scss" scoped>
.lightingControl {
  width: 100%;
  height: 100%;
  position: relative;
  .title {
    height: 30px;
    position: relative;
    div {
      cursor: pointer;
      position: absolute;
      top: 5px;
      left: 0;
      width: 80px;
      height: 30px;
      line-height: 30px;
      text-align: center;
      color: #618ad3;
      font-size: 0.875rem;
      white-space: nowrap;
      background-image: url(../../../assets/images/peace/btn-back.png);
      background-size: 100% 100%;
      background-repeat: no-repeat;
    }
  }
  .right-content {
    position: absolute;
    top: 0;
    right: 0;
    width: 20%;
    margin-top: 2%;
    // height: auto;
    max-height: 95%;
    background: url('~@/assets/images/center/bg-space-mask.png') no-repeat;
    background-size: 100% 100%;
    box-sizing: border-box;
    padding: 10px 0px;
    .top_title {
      height: 2.5rem;
      line-height: 2.5rem;
      padding-left: 0.9375rem;
      .dialog-title {
        color: #fff;
        font-family: PingFangSC-Medium, PingFang SC;
      }

      .dialog-title::before {
        content: '';
        display: inline-block;
        width: 2px;
        height: 16px;
        background: #ffe3a6;
        margin-right: 10px;
        vertical-align: middle;
      }
    }
    .top_Statistics {
      // height: 14.75rem;
      margin: 10px;
      box-sizing: border-box;
      background-color: rgba(1, 11, 59, 0.5);
      .top_loop_num {
        height: 45px;
        line-height: 45px;
        font-size: 13px;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: #cfe1f7;
        // display: flex;
        text-align: center;
        strong {
          font-size: 20px;
          font-family: DIN-Medium, DIN;
          color: #ffe3a6;
          margin: 0 4px 0 15px;
        }
      }
      .center_loop_statistics {
        height: 11.9375rem;
        // display: flex;
        #loopStatisticsEcharts {
          width: 100%;
          height: 100%;
          z-index: 2;
        }
        .pie_background {
          // width: 55%;
          left: 5%;
          transform: translate(0, 0);
        }
      }
      .bottom_control {
        height: 45px;
        display: flex;
        justify-content: space-evenly;
        .bottom_unline_num {
          margin-top: 5px;
          width: 80px;
          height: 36px;
          background: rgba(242, 242, 242, 0.1);
          border-radius: 2px;
          display: flex;
          align-items: center;
          justify-content: space-evenly;
          img {
            width: 30px;
            height: 30px;
          }
          span {
            font-size: 20px;
            font-family: Roboto-Medium, Roboto;
            font-weight: 500;
            color: #ffffff;
          }
        }
      }
    }
    .back_box {
      position: absolute;
      top: 100%;
      width: 100%;
      height: 2.8rem;
      line-height: 2.8rem;
      margin-top: 5px;
      text-align: center;
      font-size: 14px;
      font-family: PingFangSC-Regular, PingFang SC;
      font-weight: 600;
      color: #93bdff;
      background: rgba(1, 11, 59, 0.8);
      cursor: pointer;
    }
  }
}
</style>
