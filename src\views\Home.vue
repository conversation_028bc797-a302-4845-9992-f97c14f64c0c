<!--
 * @Author: ycw
 * @Date: 2020-05-18 11:41:56
 * @LastEditors: chenx
 * @LastEditTime: 2025-05-07 09:51:36
 * @Description: 主页，页面架构
-->

<template>
  <div class="sino-container">
    <el-container>
      <!-- <el-main class="el-main transparentBgColor"> -->
      <!-- transparentBgColor: $route.meta.bgColor === 'transparent' && !envDev, -->
      <el-main class="el-main" :class="{
        transparentBgColor: true,
        'main-padding': $route.meta.padding === 0
      }">
        <template v-if="envDev">
          <div>
            <!-- <button @click="routeChange('userManagement')">用户</button>
            <button @click="routeChange('roleManagement')">角色</button>
            <button @click="routeChange('menuManagement')">菜单</button>
            <button @click="routeChange('warnManagement')">警情</button>
            <button @click="routeChange('dictManagement')">字典</button> -->
            <button @click="routeChange('peopleWorkOrder')">人员工单</button>
            <button @click="routeChange('EmergencyTeam')">值排班信息</button>
            <button @click="routeChange('operationMonitoring')">运行监测</button>
            <button @click="routeChange('EmergencyDisposal')">应急处置</button>
            <button @click="routeChange('EmergencyDisposalOld')">应急处置old</button>
            <!-- <button @click="routeChange('warnTable')">报警弹窗</button> -->
            <!-- <button @click="routeChange('businessIOMS')">一站式</button>
            <button @click="routeChange('businessIOMSalaysis')">一站式 区域分析</button>
            <button @click="routeChange('businessIMWS')">医废</button>
            <button @click="routeChange('businessIMWSalaysis')">医废 区域分析</button>
            <button @click="routeChange('businessIPAS')">巡检</button>
            <button @click="routeChange('businessIPASalaysis')">巡检 区域分析</button> -->
            <button @click="routeChange('energyConsumption')">能耗</button>
            <!-- <button @click="routeChange('safetyOverview')">安全态势总览</button> -->
            <!-- <button @click="routeChange('intelligentOperation')">智能运行监控</button> -->
            <!-- <button @click="routeChange('overTimeScreen')">超时工单</button> -->
            <!-- <button @click="routeChange('rearAssets')">资产</button> -->
            <button @click="routeChange('elevatorMonitor')">电梯监测</button>
            <button @click="routeChange('elevatorMonitorSJY')">电梯监测四军医</button>
            <button @click="routeChange('comprehensiveStatistics')">第四军医综合统计</button>
            <button @click="routeChange('runOverviewSJY')">第四军医运行总览</button>
            <!-- <button @click="routeChange('cameraTalkBox')">对讲</button> -->
            <!-- <button @click="routeChange('elevatorEmergencyDisposal?projectCode=713e24b03094410499db0b08a2eccbcc')">事件管理</button> -->
            <!-- <button @click="routeChange('elevatorDetail?surveyEntityCode=f27e52bf7436417e84a8de41eb3269e5&projectCode=713e24b03094410499db0b08a2eccbcc')">电梯详情</button> -->

            <button
              @click="routeChange('spaceManage?modelCode=BJSJTYY01&ssmCodes=1574997196057620481,1574997196330250241&localtion=01&ssmType=3')">空间</button>
            <button @click="routeChange(url)">设备设施</button>
            <button
              @click="routeChange('spaceManage?modelCode=SINOMIS01&ssmCodes=1724753192383705090,1724753192417259521&localtion=01&ssmType=3')">深圳空间</button>
            <button
              @click="routeChange('airconditionSystem?modelCode=SINOMIS01&ssmCodes=1724753192383705090,1724753192417259521&localtion=01&ssmType=3&tabName=Kongtiao&isSpace=1&menuCode=ca6bf326808146b5b37f745027f04666')">深圳设备设施</button>
            <button
              @click="routeChange('medicalManagement?modelCode=SINOMIS01&ssmCodes=1724753192383705090,1724753192417259521&localtion=01&ssmType=3&tabName=assetsManage&isSpace=1&menuCode=ca6bf326808146b5b37f745027f04666')">医工管理</button>
            <!-- <button @click="routeChange('riskHiddenDanger')">风险隐患</button> -->
            <!-- <button @click="routeChange('equipmentAntiTheft')">设备防盗</button> -->
            <!-- <button @click="routeChange('movementTrajectory')">移动轨迹</button> -->
            <!-- <button @click="routeChange('alarmDialogIframe')">战时报警</button> -->
          </div>
          <div style="width: 100%; height: calc(100% - 23px)">
            <keep-alive :include="$store.state.keepAliveList">
              <router-view></router-view>
            </keep-alive>
          </div>
        </template>
        <template v-else>
          <keep-alive :include="$store.state.keepAliveList">
            <router-view></router-view>
          </keep-alive>
        </template>
      </el-main>
    </el-container>
    <!-- 全局搜索 -->
    <globalSearch v-if="isGlobalSearch" :visible="isGlobalSearch" :globalParams="globalSearchData" @close="isGlobalSearch = false"/>
  </div>
</template>

<script>
import { getGasList } from '@/utils/spaceManage'
export default {
  name: 'Home',
  components: {
    globalSearch: () => import('@/views/globalView/globalSearch/index.vue')
  },
  data() {
    return {
      envDev: false,
      url: 'airconditionSystem?modelCode=SINOMIS01&ssmCodes=1724753192383705090,1724753192417259521&localtion=01&ssmType=3&tabName=space&menuCode=ca6bf326808146b5b37f745027f04666&isSpace=1&areaData={"ssmType":"1","parentId":"1724753192383705090","ssmName":"主院区","childList":["1724753192815718402","1724753193000267778","1724753193138679809","1724753193365172225","1731219360110956545"]}',
      elevatorUrl: 'elevatorMonitorSJY?areaData={"ssmType":"3","parentId":"1724753192815718402","ssmName":"门诊楼","childList":["1724753192866050049","1732358069012168706","1732358069012168707","1732358069012168708","1732358069012168709","1732358069012168710","1732358069012168711","1732358069012168712","1732358069012168713","1732358069012168714","1732358069012168715","1732358069012168716","1732358069012168717","1732358069012168718"]}&ssmCodes=1724753192383705090,1724753192417259521,1724753192815718402',
      isGlobalSearch: false, // 全局搜索
      globalSearchData: {
        searchType: '', // 0:设备列表 1:空间列表
        title: '',
        list: [],
        total: 0
      }
    }
  },
  created() {
    this.envDev = process.env.NODE_ENV === 'development'
    this.globalMessage()
  },
  mounted() { },
  methods: {
    // 全局监听wpf消息
    globalMessage() {
      try {
        window.chrome.webview.addEventListener('message', (event) => {
          const data = JSON.parse(event.data)
          // 全局搜索
          if (data.type === 'globalSearch') {
            this.openGlobleDialog(data)
          }
        })

      } catch (error) {
        console.warn('全局监听器添加失败', error)
      }
    },
    // 全局搜索逻辑处理
    openGlobleDialog(data) {
      // 这里产品要求，如果只有一条数据，那就直接定位，如果有多条数据，那就弹窗
      // 所以需要先请求接口，通过接口返回值是否等于1，来决定是先弹框，还是直接调用wpf方法
      if (data.searchType == 0) {
        this.getAssetsList(data)
      } else if (data.searchType == 1) {
        this.getSpaceList(data)
      }
    },
    // 获取空间列表
    getSpaceList(data) {
      let params = {
        current: 1,
        size: 15,
        spaceName: data.searchContent
      }
      getSpaceInfoPageByModelCode(params).then((res) => {
        if (res.data.data.records.length > 1) {
          // 打开弹框
          this.globalSearchData = {
            ...data,
            list: res.data.data.records,
            total: res.data.data.total,
            title: '空间列表'
          }
          this.isGlobalSearch = true
        } else if (res.data.data.records.length === 1) {
          // 直接调用wpf方法
          let modelCode = res.data.data.records[0].modelCode
          try {
            window.chrome.webview.hostObjects.sync.bridge.OpenRoomAndChangeColor(modelCode)
          } catch (error) {}
        } else {
          this.$message.warning('未查询到对应空间信息')
        }
      })
    },
    // 获取设备列表
    getAssetsList (data) {
      let params = {
        pageSize: 15,
        page: 1,
        assetName: data.searchContent
      }
      getGasList(params).then((res) => {
        if (res.data.code === '200') {
          if (res.data.data.list.length === 1) {
            // 直接调用wpf方法
            this.tableRowClick(res.data.data.list[0])
          } else if (res.data.data.list.length === 0) {
            this.$message.warning('未查询到对应设备')
          } else {
            // 打开弹框
            this.globalSearchData = {
              ...data,
              list: res.data.data.list,
              total: res.data.data.count,
              title: '设备列表'
            }
            this.isGlobalSearch = true
          }
        }
      })
    },
    tableRowClick (row) {
      let params = {}
      // 受filterName包含的为使用modelcode跳转（关联模型），否则使用assetsid跳转（关联表计）
      const filterName = hasModelCodeFilterProjectName
      const filterMonitorList = monitorTypeList.filter(e => filterName.includes(e.projectName)).map(v => v.projectCode)
      // 如果关联了设备即跳转设备详情页
      params = {
        DeviceCode: row.modelCode,
        DeviceName: row.assetName,
        assetsId: row.assetId || row.assetsId,
        projectCode: row?.projectCode,
        spaceCode: row?.regionCode
      }
      if (filterMonitorList.includes(params.projectCode)) {
        if (params.assetsId) {
          if (params.DeviceCode) { // 设备
            try {
              window.chrome.webview.hostObjects.sync.bridge.EquipmentRealView(JSON.stringify(params))
            } catch (error) {}
          } else {
            this.$message.warning('当前设备暂未录入模型编码!')
          }
        } else {
          this.$message.warning('当前设备暂未关联资产!')
        }
      } else {
        if (params.assetsId) { // 点位
          try {
            window.chrome.webview.hostObjects.sync.bridge.EquipmentRealView(JSON.stringify(params))
          } catch (error) {}
        } else {
          this.$message.warning('当前设备暂未关联资产!')
        }
      }
    },
    routeChange (path) {
      if (path == 'elevatorMonitorSJY') {
        this.$router.push({path: '/' + this.elevatorUrl})
      } else {

        this.$router.push({ path: '/' + path })
      }
    }
  }
}
</script>
<style lang="scss" scoped>
.transparentBgColor {
  background: center;
}
.main-padding {
  padding: 0;
}
</style>
