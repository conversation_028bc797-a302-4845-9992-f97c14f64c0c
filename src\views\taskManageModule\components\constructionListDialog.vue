<template>
  <dialogFrame
    :visible="isDialog"
    :breadcrumb="breadcrumb"
    :title="title"
    @back="back"
    @update:visible="closeDialogFrame"
  >
    <component :is="activeComponent" :roomData="roomData" @openDetailComponent="openDetailComponent"></component>
  </dialogFrame>
</template>

<script>
export default {
  name: 'constructionListDialog',
  components: {
    dialogFrame: () => import('@/components/common/DialogFrame'),
    constructionList: () => import('./constructionList.vue'),
    constructionFlow: () => import('./constructionFlow.vue')
  },
  props: {
    isDialog: {
      type: Boolean,
      default: false
    },
    roomData: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      breadcrumb: [{ label: '施工作业列表', name: 'constructionList' }],
      activeComponent: 'constructionList',
      title: '施工作业列表'
    }
  },
  computed: {
  },
  created() {

  },
  methods: {
    /** 打开组件 */
    openDetailComponent(params) {
      this.activeComponent = params
      let obj = {
        constructionFlow: { label: '流程详情', name: 'constructionFlow' }
      }
      this.breadcrumb.push(obj[params])
      this.title = obj[params].label
    },
    /** 关闭弹窗 */
    closeDialogFrame() {
      this.$emit('close', false)
      this.activeComponent = 'constructionList'
      this.title = '施工作业列表'
      this.breadcrumb = [{ label: '施工作业列表', name: 'constructionList' }]
    },
    /** 组件返回上一级 */
    back(name) {
      this.activeComponent = name
      let arr = []
      for (let i = 0; i < this.breadcrumb.length - 1; i++) {
        arr.push(this.breadcrumb[i])
        if (this.breadcrumb[i].name == name) {
          break
        }
      }
      this.breadcrumb = arr
      if (this.breadcrumb.length) {
        this.title = this.breadcrumb.find((item) => item.name === name).label
      }
    }
  }
}

</script>

<style lang="scss" scoped>

</style>
