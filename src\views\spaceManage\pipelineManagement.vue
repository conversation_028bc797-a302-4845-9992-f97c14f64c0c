<template>
  <div class="pipelineComponent">
    <!-- <div>{{setWpfData}}</div> -->
    <!-- 二级tab -->
    <div class="top-titleTab">
      <div>系统分类</div>
    </div>
    <div class="bg-title">
      <div class="box-type">
        <span
          v-for="(item, index) in systemTypeList"
          :id="item.value"
          :key="index"
          class="tags-item"
          :class="{ type_active: activeType === item.value }"
          @click="activeTypeEvent(item.value)"
        >
          {{ item.name }}
        </span>
      </div>
    </div>
    <div class="top-gx">
      <div>管线图例</div>
    </div>
    <div v-scrollbarHover class="middle-content-gx">
      <el-checkbox
        v-model="checkAllPipeline"
        :indeterminate="isIndeterminate"
        class="checkbox-qx"
        :class="{ isIconShow: isIndeterminate }"
        @change="handleCheckAllChange"
      >全选</el-checkbox
      >
      <div class="middle">
        <div v-for="item in pipelineData" :key="item.PipeId" class="middle-div">
          <el-checkbox-group
            v-model="checkedPipeline"
            @change="handleCheckedPipelineChange"
          >
            <div class="middle-checkbox">
              <el-checkbox
                :label="item.PipeId"
                :value="item.PipeId"
                class="checkbox-label"
              ></el-checkbox>
              <div
                class="checkbox-div"
                :style="{ background: item.BGclolor || 'rgb(255, 255, 255)' }"
              ></div>
              <div class="checkbox-title">{{ item.PipeName }}</div>
            </div>
          </el-checkbox-group>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { refrigeratorData, systemTypeList } from '@/assets/common/dict.js'
export default {
  name: 'pipelineComponent',
  components: {},
  props: {
    roomData: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      setWpfData: {},
      roomInfo: {},
      systemTypeList: Object.freeze(
        systemTypeList[__PATH.VUE_APP_HOSPITAL_NODE] || systemTypeList['szzlyy']
      ),
      refrigeratorData: Object.freeze(
        refrigeratorData[__PATH.VUE_APP_HOSPITAL_NODE] ||
          refrigeratorData['szzlyy']
      ),
      activeType: 'all',
      isIndeterminate: true,
      checkAllPipeline: false,
      checkedPipeline: [],
      pipelineData: [],
      pipelineParams: {},
      pipeFlow: false,
      checkAll: false
    }
  },
  mounted() {
    this.roomInfo = JSON.parse(JSON.stringify(this.roomData))
    try {
      window.chrome.webview.addEventListener('message', this.wpfEvent)
    } catch (errpr) {}
    this.getCategoryList()
    // 初始化监听
    this.handleResize()
  },
  destroyed() {
    try {
      window.chrome.webview.removeEventListener('message', this.wpfEvent)
    } catch (errpr) {}
  },
  methods: {
    wpfEvent(event) {
      this.roomInfo = JSON.parse(event.data)
      this.getCategoryList()
    },
    handleResize() {
      // 监听 .box-type 高度变化 如果变化重新设置middle-content-gx得高度
      const boxType = document.querySelector('.box-type')
      const middleContentGx = document.querySelector('.middle-content-gx')
      let Hight = middleContentGx.offsetHeight - (boxType.offsetHeight + 12)
      this.$nextTick(() => {
        middleContentGx.style.height = Hight + 'px'
      })
    },

    // 获取管线分类列表
    getCategoryList() {
      this.pipelineData = this.filterCategoryList(this.refrigeratorData)
      const allIDs = Array.from(this.pipelineData, ({ PipeId }) => PipeId)
      this.checkedPipeline = allIDs
      this.setCheckDataToWpf()
    },
    // 数据过滤
    filterCategoryList(list = this.pipelineData) {
      return list.filter((item) => {
        return item.parentId === this.activeType || this.activeType === 'all'
      })
    },
    activeTypeEvent(type) {
      this.activeType = type
      this.getCategoryList()
    },
    // 全选
    handleCheckAllChange(val) {
      this.checkedPipeline = val
        ? Array.from(this.pipelineData, ({ PipeId }) => PipeId)
        : []
      this.isIndeterminate = false
      this.setCheckDataToWpf()
    },
    handleCheckedPipelineChange(value) {
      const checkedCount = value.length
      this.checkAllPipeline = checkedCount === this.pipelineData.length
      this.isIndeterminate =
        checkedCount > 0 && checkedCount >= this.pipelineData.length
      this.setCheckDataToWpf()
    },
    // 传输数据给wpf
    setCheckDataToWpf() {
      const params = JSON.parse(JSON.stringify(this.pipelineData))
      // const sendMessage = params.map((item) => {
      //   const Visibility = this.checkedPipeline.includes(item.PipeId) ? 1 : 0
      //   return {
      //     BimCategoryType: item.PipeId,
      //     Visibility,
      //     CategoryCode: this.roomData.localtion
      //   }
      // })
      const sendMessage = []
      params.forEach((item) => {
        const Visibility = this.checkedPipeline.includes(item.PipeId) ? 1 : 0
        const filterIndex = sendMessage.findIndex(
          (e) => e.BimCategoryType == item.parentId
        )
        if (filterIndex > -1) {
          if (Visibility === 1) {
            sendMessage[filterIndex].Visibility = Visibility
          }
          return
        }
        sendMessage.push({
          BimCategoryType: item.parentId,
          Visibility,
          CategoryCode: this.roomData.localtion
        })
      })
      console.log(sendMessage)
      try {
        window.chrome.webview.hostObjects.sync.bridge.GetPipelineSelectData(
          JSON.stringify(sendMessage)
        )
      } catch (error) {
        console.log(error)
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.pipelineComponent {
  width: 100%;
  height: 100%;
  // 新
  .top-titleTab {
    // height: 18px;
    background: rgba(133, 145, 206, 0.15);
    // line-height: 18px;
    padding: 16px 8px 7px;
  }
  .top-titleTab div:nth-child(1) {
    height: 18px;
    font-size: 15px;
    font-family: HarmonyOS Sans SC-Medium, HarmonyOS Sans SC;
    font-weight: 500;
    color: #ffffff;
  }
  .bg-title {
    padding: 0;
    background: rgba(133, 145, 206, 0.15);
    overflow: hidden;
    color: #dceaff;
    font-family: HarmonyOS Sans SC-Regular, HarmonyOS Sans SC;
    flex-shrink: 0;
    // 不可被选中
    -webkit-user-select: none;
    display: flex;
    align-items: center;
    .box-type {
      //   width: calc(100% - 34px);
      display: flex;
      flex-wrap: wrap;
      //   overflow: hidden;
      .tags-item {
        flex-shrink: 0;
        margin-bottom: 12px;
        cursor: pointer;
        height: 26px;
        line-height: 26px;
        flex-shrink: 0;
        margin-left: 8px;
        padding: 0px 12px;
        background: rgba(255, 255, 255, 0.1)
          linear-gradient(
            90deg,
            rgba(10, 132, 255, 0) 0%,
            rgba(10, 132, 255, 0) 100%
          );
        font-size: 14px;
        font-weight: 400;
        color: #b0e3fa;
        border: 1px solid transparent;
      }
      .type_active {
        background: rgba(255, 255, 255, 0.1)
          linear-gradient(
            90deg,
            rgba(10, 132, 255, 0.4) 0%,
            rgba(10, 132, 255, 0) 100%
          );
        border: 1px solid;
        border-image: radial-gradient(
            circle,
            rgba(171, 240, 255, 1),
            rgba(226, 254, 255, 1),
            rgba(132, 196, 203, 1),
            rgba(48, 151, 166, 0)
          )
          1 1;
      }
    }
    .content-icon-collapse {
      margin: auto;
      width: 24px;
      height: 24px;
      flex-shrink: 0;
      display: flex;
      &:hover {
        cursor: pointer;
      }
      img {
        margin: auto;
      }
    }
  }
  .top-gx {
    height: 44px;
    padding: 7px 38px 7px;
    margin-top: 24px;
    background: url("@/assets/images/qhdsys/bg-bt.png") no-repeat;
    background-size: contain;
    display: flex;
    align-items: center;
  }
  .top-gx div:nth-child(1) {
    // width: 355px;
    font-size: 16px;
    font-family: HarmonyOS Sans SC-Bold, HarmonyOS Sans SC;
    font-weight: bold;
    color: #ffffff;
    text-shadow: 0px 0px 9px #158eff;
  }
  .middle-content-gx {
    height: calc(100% - 44px - 24px - 41px);
    // margin-top: 16px;
    padding: 16px 0 0 8px;
    // border: 1px solid #158eff;
    overflow: auto;
    .middle {
      display: flex;
      flex-wrap: wrap;
      // justify-content: space-between;
      margin-top: 8px;
      .middle-div {
        // min-width: 140px;
        width: 50%;
        flex-shrink: 0;
        height: 20px;
        // border: 1px solid red;
        margin-bottom: 12px;
        .middle-checkbox {
          display: flex;
          align-items: center;
        }
        .checkbox-div {
          width: 12px;
          height: 8px;
          margin: 0 8px;
          flex-shrink: 0;
        }
        .checkbox-title {
          height: 20px;
          font-size: 14px;
          font-family: PingFang SC-Medium, PingFang SC;
          font-weight: 500;
          color: #ffffff;
          line-height: 20px;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
        .checkbox-label {
          ::v-deep .el-checkbox__label {
            display: none;
          }
        }
      }
    }
    ::v-deep .el-checkbox__input {
      .el-checkbox__inner {
        box-shadow: 0px 0px 3px 0px #78fff8;
        opacity: 1;
        border: 1px solid #52fffc;
        background: transparent;
      }
    }
    .checkbox-qx {
      ::v-deep .el-checkbox__inner::before {
        display: none;
      }
      ::v-deep .el-checkbox__label {
        font-size: 14px;
        font-family: PingFang SC-Medium, PingFang SC;
        font-weight: 500;
        color: #ffffff;
      }
    }
    .isIconShow {
      ::v-deep .el-checkbox__input {
        .el-checkbox__inner {
          background: url("@/assets/images/qhdsys/bg-checkall.png") no-repeat !important;
          border: 0;
        }
      }
    }
  }
}
</style>
