<template>
  <el-select popper-class="selectTree" ref="select" :value="value" placeholder="请选择" size="medium" @visible-change="visibleChange" style="width: 100%">
    <el-option ref="option" class="option" :value="optionData.id" :label="optionData.name">
      <el-tree
        ref="tree"
        class="tree"
        :node-key="nodeKey"
        :data="data"
        :props="props"
        :default-expanded-keys="[value]"
        highlight-current
        :expand-on-click-node="false"
        @node-click="handleNodeClick"
      ></el-tree>
    </el-option>
  </el-select>
</template>

<script>
export default {
  name: 'TreeSelect',
  props: {
    // v-model绑定
    value: {
      type: [String, Number],
      default: ''
    },
    // 树形的数据
    data: {
      type: Array,
      default: function () {
        return []
      }
    },
    // 每个树节点用来作为唯一标识的属性
    nodeKey: {
      type: [String, Number],
      default: 'id'
    },
    // tree的props配置
    props: {
      type: Object,
      default: function () {
        return {
          label: 'label',
          children: 'children'
        }
      }
    },
    // 确认选择三级节点
    level: {
      type: String,
      default: ''
    },
    // 返回数据格式 是否拼接
    callBackData: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      optionData: {
        id: '',
        name: ''
      }
    }
  },
  watch: {
    value: function (val) {
      if (!this.isEmpty(this.data)) {
        this.init(val)
      }
    },
    data: function (val) {
      if (!this.isEmpty(val)) {
        this.init(this.value)
      }
    }
  },
  mounted() {
    if (!this.isEmpty(this.data)) {
      this.init(this.value)
    }
    this.$nextTick(() => {
      var ele = document.querySelector('.el-select-dropdown__wrap')
      ele.scrollTop = 0
    })
  },
  methods: {
    // 是否为空
    isEmpty(val) {
      for (const key in val) {
        return false
      }
      return true
    },
    handleNodeClick(data, node) {
      if (this.level === '3') {
        if (data.level !== '3') {
          this.$message({
            message: '请选择三级服务事项！',
            type: 'warning'
          })
          return false
        } else {
          this.$emit('input', node.data.id)
          this.optionData.id = node.data.id
          this.optionData.name = node.data.name
          this.$emit('getName', {
            id: node.data.id + '_' + node.parent.data.id + '_' + node.parent.parent.data.id,
            name: node.data.name + '_' + node.parent.data.name + '_' + node.parent.parent.data.name
          })
        }
      } else if (this.callBackData === 'split') {
        this.$emit('input', node.data.id)
        this.optionData.id = node.data.id
        this.optionData.name = node.data.name
        if (data.level === '1') {
          this.$emit('getName', {
            id: node.data.id,
            name: node.data.name
          })
        } else if (data.level === '2') {
          this.$emit('getName', {
            id: node.data.id + '_' + node.parent.data.id,
            name: node.data.name + '_' + node.parent.data.name
          })
        } else {
          this.$emit('getName', {
            id: node.data.id + '_' + node.parent.data.id + '_' + node.parent.parent.data.id,
            name: node.data.name + '_' + node.parent.data.name + '_' + node.parent.parent.data.name
          })
        }
      } else {
        const label = this.props.label || 'name'
        this.$emit('input', data[this.nodeKey])
        this.optionData.id = data[this.nodeKey]
        this.optionData.name = data[label]
        this.$emit('getName', data[label])
      }
      this.$refs.select.visible = false
    },
    init(val) {
      if (val) {
        this.$nextTick(() => {
          //   const label = this.props.label || 'name'
          this.$refs.tree.setCurrentKey(val)
          const node = this.$refs.tree.getNode(val)
          this.optionData.id = val
          this.optionData.name = node.label || node.name
        })
      } else {
        this.$refs.tree.setCurrentKey(null)
      }
    },
    visibleChange(e) {
      if (e) {
        let selectDom = document.querySelector('.is-current')
        if (selectDom === null) {
          selectDom = document.querySelector('.el-tree-node')
        }
        setTimeout(() => {
          this.$refs.select.scrollToOption({ $el: selectDom })
        }, 0)
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.option {
  height: auto;
  line-height: 1;
  padding: 0;
  background-color: #2b2b45;
}

.tree {
  padding: 4px 20px;
  font-weight: 400;
}
</style>
<style lang="scss">
@import '../../assets/sino-ui/common/var.scss';
.selectTree {
  .el-tree {
    background: #2b2b45;
    color: $color;
  }
  .el-tree--highlight-current .el-tree-node.is-current > .el-tree-node__content {
    color: $activeColor;
    background: #2b2b45;
  }
  .el-tree-node__content:hover {
    color: $activeColor;
    background: #2b2b45;
  }
  .el-tree-node:focus > .el-tree-node__content {
    background: #2b2b45;
  }
  .el-tree-node__content {
    height: 34px;
  }
}
</style>
