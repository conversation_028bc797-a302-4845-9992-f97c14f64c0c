<template>
  <el-dialog v-dialogDrag :modal="false" width="55%" :visible.sync="hiddenDangerDetailsListShow" custom-class="detailDialog main" :close-on-click-modal="false" :before-close="closed">
    <template slot="title">
      <span class="dialog-title">{{ systemType == 'xjrw' ? '巡检' : '保养' }}点详情</span>
    </template>
    <div v-loading="pageLoading" class="form-detail">
      <div class="list_content">
        <div class="linear-g">
          <span class="linear-g-span1">任务点</span>
          <span>{{ taskPoint.taskPointName }}</span>
        </div>
      </div>
      <div class="list_content">
        <div class="linear-g">
          <span class="linear-g-span1">{{ systemType == 'xjrw' ? '巡检' : '保养' }}内容</span>
        </div>
        <el-table
          :data="tableData"
          element-loading-background="rgba(0, 0, 0, 0.2)"
          :cell-style="{ padding: ' 8px', backgroundColor: 'transparent', border: 'none', padding: '3px' }"
          :header-cell-style="{ background: '#8591CE26!important', color: '#8BDDF5FF', padding: '4px 8px', fontWeight: 'bold' }"
          style="width: 100%; margin-bottom: 10px"
        >
          <el-table-column type="index" label="序号" width="100" align="center"></el-table-column>
          <el-table-column prop="detailName" show-overflow-tooltip :label="systemType == 'xjrw' ? '巡检项目' : '保养项目'" align="center"></el-table-column>
          <el-table-column prop="content" show-overflow-tooltip :label="systemType == 'xjrw' ? '巡检要点' : '保养要点'" align="center"> </el-table-column>
          <el-table-column show-overflow-tooltip :label="systemType == 'xjrw' ? '巡检内容' : '保养内容'" align="center">
            <template slot-scope="scope" :class="scope.row">
              <span v-if="scope.row.isNum == '0'"
                >正常值：{{ scope.row.rangeStart + '-' + scope.row.rangeEnd + ' ' + scope.row.einheitName
                }}{{ scope.row.contentStandard ? '（当前' + scope.row.contentStandard + ' ' + scope.row.einheitName + '）' : '' }}</span
              >
              <span v-if="scope.row.isNum == '3' || scope.row.isNum == '2'">
                <span>{{ scope.row.contentStandard || '无' }}</span>
              </span>
              <!--  巡检选项为'无' -->
              <span v-if="scope.row.isNum == '1'">无</span>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <!-- 巡检执行 -->
      <div class="list_content">
        <div class="linear-g">
          <span class="linear-g-span1">{{ systemType == 'xjrw' ? '巡检' : '保养' }}执行</span>
        </div>
        <div class="plan-content">
          <ul class="item-row">
            <li v-if="systemType == 'xjrw'" class="width35">
              <span class="li-first-span">巡检情况</span><span class="li-last-span">{{ excute.carryOutFlag == '0' ? '未巡' : excute.carryOutFlag == '1' ? '已巡' : '' }}</span>
            </li>
            <li v-if="systemType == 'byrw'" class="width30">
              <span class="li-first-span">保养情况</span><span class="li-last-span">{{ excute.carryOutFlag == '0' ? '未保' : excute.carryOutFlag == '1' ? '已保养' : '' }}</span>
            </li>
            <li class="width30">
              <span class="li-first-span">应{{ systemType == 'xjrw' ? '巡' : '保' }}部门/人员</span><span class="li-last-span">{{ excute.distributionTeamName || excute.planPersonName }}</span>
            </li>
            <li class="width30">
              <span class="li-first-span">执行人员</span><span class="li-last-span">{{ excute.implementPersonName }}</span>
            </li>
          </ul>
          <ul class="item-row">
            <li class="width35">
              <span class="li-first-span">实际{{ systemType == 'xjrw' ? '巡检' : '保养' }}时间</span><span class="li-last-span">{{ excute.excuteTime }}</span>
            </li>
            <li class="width30">
              <span class="li-first-span">定位状态</span><span class="li-last-span">{{ excute.spyScan }}</span>
            </li>
            <li v-if="systemType == 'byrw'" class="width35">
              <span class="li-first-span">应保养日期</span><span class="li-last-span">{{ moment(taskMap.taskStartTime).format('YYYY-MM-DD') }}</span>
            </li>
          </ul>
          <ul class="item-row">
            <li v-if="systemType == 'byrw'" class="width35">
              <span class="li-first-span">完成期限</span><span class="li-last-span">{{ taskMap.finalTime ? taskMap.finalTime + '天' : '' }}</span>
            </li>
          </ul>
        </div>
      </div>
      <!-- 巡检结果 -->
      <div class="list_content">
        <div class="linear-g">
          <span class="linear-g-span1">{{ systemType == 'xjrw' ? '巡检' : '保养' }}结果</span>
        </div>
        <div class="plan-content">
          <ul class="item-row">
            <li v-if="systemType == 'xjrw'" class="width90">
              <span class="li-first-span">结果</span><span class="li-last-span">{{ result.state == '2' ? '合格' : result.state == '3' ? '不合格' : result.state == '4' ? '异常报修' : '未巡检' }}</span>
            </li>
            <li v-if="systemType == 'byrw'" class="width90">
              <span class="li-first-span">结果</span><span class="li-last-span">{{ result.state == '2' ? '合格' : result.state == '3' ? '不合格' : result.state == '4' ? '异常报修' : '未保养' }}</span>
            </li>
          </ul>
          <ul class="item-row">
            <li class="width90">
              <span class="li-first-span">描述</span><span class="li-last-span">{{ result.desc }}</span>
            </li>
          </ul>
          <ul class="item-row">
            <li class="width90">
              <span class="li-first-span">语音</span>
              <div v-if="result.callerTapeUrl" id="audio-box">
                <audio ref="player" style="height: 40px" :src="result.callerTapeUrl" preload="true" controls="controls"></audio>
                <!-- <audio controls>
                    <source :src="workOrderDetail.olgTaskManagement.callerTapeUrl" />
                  </audio> -->
                <!-- src="https://sinomis.oss-cn-beijing.aliyuncs.com/ioms/ZKYXYY/question/2021/06/24/mp3/20210624104642208466.mp3?Expires=1649988786&OSSAccessKeyId=LTAIeUfTgrfT5j7Y&Signature=QI12blGWwn1KzARBWOu7TdeD0rI%3D" -->
              </div>
              <span v-else>暂无</span>
            </li>
          </ul>
          <ul class="item-row">
            <li class="width90">
              <span class="li-first-span">图片</span>
              <p v-if="result.attachmentUrlList">
                <span v-for="(img, index) in result.attachmentUrlList" :key="index">
                  <el-image style="width: 100px; height: 100px; margin-right: 15px" :src="img" :preview-src-list="[img]"> </el-image>
                </span>
              </p>
              <span v-else>暂无</span>
            </li>
          </ul>
        </div>
      </div>
    </div>
  </el-dialog>
</template>

<script>
import moment from 'moment'
import { getIPASpointDetail, getInspPointDetail } from '@/utils/centerScreenApi'
export default {
  name: 'taskPointDetail',
  props: {
    taskType: {
      type: String,
      default: 'insp'
    },
    systemType: {
      type: String,
      default: 'xjrw'
    },
    taskMap: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      moment,
      tableData: [],
      taskPoint: '',
      excute: '',
      result: '',
      hiddenDangerDetailsListShow: false,
      pageLoading: false
    }
  },
  mounted() {},
  methods: {
    getDataDetail(id) {
      const codeList = [
        {
          type: 'insp',
          code: getInspPointDetail
        },
        {
          type: 'ipsm',
          code: getIPASpointDetail
        }
      ]
      const code = codeList.find((e) => e.type == this.taskType).code
      this.pageLoading = true
      code({ id }).then((res) => {
        this.pageLoading = false
        const data = res.data
        if (data.code === '200') {
          Object.assign(this, {
            taskPoint: data.data.taskPoint,
            tableData: data.data.project.projectdetailsReleaseList,
            excute: data.data.excute,
            result: data.data.result
          })
          if (this.tableData && this.tableData.length) {
            this.tableData.forEach((val, index) => {
              if (val.isNum === '3') {
                val.radioTextArr = JSON.parse(val.termJson)
                // console.log(val.radioTextArr)
              }
            })
          }
        }
      })
    },
    closed() {
      this.hiddenDangerDetailsListShow = false
      this.$emit('closeDialog')
    }
  }
}
</script>

<style lang="scss" scoped>
.form-detail {
  height: calc(100% - 30px);
  width: 100%;
  padding: 10px 25px;
  overflow-y: scroll;
  box-sizing: border-box;
  .list_content {
    font-size: 14px;
    .linear-g {
      margin-left: 2px;
      margin-bottom: 10px;
      width: 100%;
      height: 100%;
      background: #263057;
      font-size: 13px;
      line-height: 30px;
      font-family: PingFangSC-Medium;
      color: #ffe3a6;
      border-radius: 6px;
      .linear-g-span1 {
        margin: 0 15px;
        font-weight: 600;
      }
    }
    .plan-content {
      width: calc(100% - 33px);
      // border-left: 1px solid #303758;
      margin-left: 11px;
      // padding: 20px 0px 20px 20px;
      color: #b5bacb;
      font-size: 13px;
      .item-row {
        width: 100%;
        display: flex;
        padding: 20px 0px 20px 30px;
        box-sizing: border-box;
        > li {
          display: flex;
        }
        .width30 {
          width: 30%;
        }
        .width35 {
          width: 35%;
        }
        .width90 {
          width: 90%;
          display: flex;
        }
        ::v-deep .el-image__error,
        ::v-deep .el-image__placeholder {
          background: center;
        }
        .li-first-span {
          display: inline-block;
          flex-shrink: 0;
          width: 120px;
          // margin-right: 20px;
          font-size: 14px;
          font-family: PingFang-SC-Medium, PingFang-SC;
          font-weight: 500;
          color: #7eaef9;
        }
        .li-last-span {
          font-size: 14px;
          font-family: PingFang-SC-Medium, PingFang-SC;
          font-weight: 500;
          color: #ffffff;
          display: block;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
          // align-items: center;
        }
        .recording-ALabel {
          color: #ffe3a6;
          font-size: 14px;
          text-decoration: none;
          i {
            margin: 0 3px 0 10px;
          }
        }
        #audio-box {
          display: flex;
        }
        #audio-box > audio {
          width: 260px;
          height: 30px;
        }
        #audio-box > a {
          width: 40px;
          text-align: center;
          background-color: #2cc7c5;
          height: 35px;
          line-height: 35px;
          color: #fff;
          border-radius: 5px;
          margin-left: 10px;
        }
      }
    }
  }
}
::v-deep .detailDialog {
  width: 80%;
  height: 80vh;
  margin-top: 7vh !important;
  background-color: transparent !important;
  background-image: url('@/assets/images/table-bg.png') !important;
  background-repeat: no-repeat;
  background-size: 100% 100%;
  border: none;
  pointer-events: auto;
  box-shadow: none;
  .el-dialog__body {
    padding: 10px 50px;
    height: calc(100% - 60px);
    max-height: 80vh;
  }
  .dialog-title {
    display: inline-block;
    width: 100%;
    text-align: center;
    transform: translateY(-6px);
    color: #cbdeed;
  }
  .dialog-title::before {
    display: none;
  }
  .el-dialog__headerbtn {
    transform: translateX(-36px);
    width: 25px;
    height: 25px;
    background-image: url('@/assets/images/close.png');
    background-repeat: no-repeat;
    background-size: 100% 100%;
    .el-dialog__close::before {
      display: none;
    }
  }
}
::v-deep .el-table {
  border: none !important;
  .el-table__header-wrapper {
    .cell {
      padding-left: 0;
      padding-right: 0;
      text-align: center;
      white-space: nowrap;
    }
  }
  .el-table__body-wrapper {
    td.el-table__cell div {
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
    }
  }
  .el-table__body {
    tr {
      background: center;
    }
    td.el-table__cell,
    th.el-table__cell.is-leaf {
      border-right: 2px solid #0a164e;
      border-bottom: 2px solid #0a164e;
      background: rgba(56, 103, 180, 0.2);
      color: #fff;
    }
    .el-table__row:nth-child(2n - 1) {
      background: rgba(168, 172, 171, 0.08);
    }
    .el-table__row:hover {
      border: 0;
      opacity: 1;
      cursor: pointer;

      td div {
        color: rgba(255, 202, 100, 1);
      }
    }
  }
}
</style>
