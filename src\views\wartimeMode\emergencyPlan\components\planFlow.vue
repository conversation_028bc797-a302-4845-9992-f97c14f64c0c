<template>
  <div class="planFlow">
    <div class="planFlow-left">
      <vue2-org-tree :data="treeData" :horizontal="false" label-class-name="bg-none" :render-content="renderContent" :collapsable="false" />
    </div>
    <div class="planFlow-right plan-info">
      <div class="info-content">
        <div class="content-title" style="padding: 14px 24px 14px 14px;">
          <div class="title-left">
            <img :src="alarmIcon" alt="报警类型" width="24" height="24">
            <span>{{ alarmData.alarmTypeName || '-' }}</span>
          </div>
          <div class="title-right">{{ alarmData.alarmObjectName || '-' }}</div>
        </div>
        <div class="alarm-content">
          <p class="title">应急流程</p>
          <div class="call-status">
            <p>
              <img src="../../../../assets/images/plan/phoneIcon1.png" alt="">
              <span>系统自动呼叫中…</span>
            </p>
            <p>系统跟进执行</p>
          </div>
          <p class="call-num">机器电话通知指挥人员、扑救人员到位，语音电话拨打{{ autoCallList.length || 0 }}位，已接通{{ callNum }}位</p>
        </div>
      </div>
      <div class="info-content">
        <div class="content-title" style="padding: 16px 24px 16px 16px;">
          <div class="title-left">
            <img :src="notifyIcon" alt="通知事件"  width="18" height="18">
            <span>通知事件</span>
          </div>
        </div>
        <div class="notify-content">
          <div v-for="item in notifyList" :key="item.id" class="notify-item">
            <img class="item-icon" src="../../../../assets/images/plan/phoneIcon2.png" alt="">
            <div class="item-info">
              <p class="info-title">{{ item.departCode ? item.departCode.split(',').map(v => depts[v]).join('，') : '通知' + item.personName + '电话'}}</p>
              <p v-if="item.noticeType == 0" class="info-main">
                <span class="info-label">联系方式：</span>
                <span class="info-value">{{ item.noticePhone }}</span>
              </p>
              <p v-if="item.noticeType == 0 && (item.noticeWay == 0 || item.noticeWay == 1)" class="info-main">
                <span class="info-label">通知：</span>
                <span class="info-value">{{item.noticeContent}}</span>
              </p>
            </div>
            <p v-if="item.noticeWay == 0" class="item-btn" @click="handCall(item)">
              <img src="../../../../assets/images/plan/phoneIcon3.png" alt="">
              <span>拨打电话</span>
            </p>
            <p v-else class="item-tip">系统自动通知</p>
          </div>
        </div>
      </div>
      <div class="info-content">
        <div class="content-title" style="padding: 17px 24px 17px 17px;">
          <div class="title-left">
            <img :src="warnIcon" alt="提示事件" width="18" height="18">
            <span>提示事件</span>
          </div>
        </div>
        <div class="warn-content">
          <div class="warn-list">
            <p v-for="item in warnList" :key="item.id" class="list-item">
              <span>{{ item.warnRoles }}职责：</span>
              <span>{{ item.warnContent }}</span>
            </p>
          </div>
        </div>
      </div>
      <div class="info-content">
        <div class="content-title" style="padding: 17px 24px 17px 17px;">
          <div class="title-left">
            <img :src="confirmIcon" alt="确认事件" width="18" height="18">
            <span>确认事件</span>
          </div>
        </div>
        <div class="confirm-content">
          <div v-for="(item, index) in confirmList" :key="item.id" class="confirm-item">
            <div class="item-content">
              <p class="item-info">
                <!-- 0-未确认 1已确认 -->
                <i class="el-icon-success" :style="{color: item.status == 0 ? '#C2C4C8' : '#61E29D'}"></i>
                <span>{{ item.confirmTitle }}</span>
              </p>
              <p v-if="item.status === ''" class="item-status">中控人员跟进执行</p>
              <p v-else class="item-status" :style="{color: item.status == 0 ? '#C2C4C8' : '#61E29D'}">{{ item.status == 0 ? '已忽略' : '已到位' }}</p>
            </div>
            <div v-if="item.status === ''" class="item-operate">
              <p class="operate-ignore" @click="saveConfirmRecord(0, item, index)">忽略</p>
              <p class="operate-confirm" @click="saveConfirmRecord(1, item, index)">已到位</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="jsx">
import Vue2OrgTree from 'vue2-org-tree'
import { GetSelectedDept, GetPlanDetail, GetPreplanStateByAlarmId, SavePreplanOperationState, SaveConfirmRecord, GetConfirmRecordList } from '@/utils/wartimeMode'
import alarmTypeIcon from '@/assets/images/plan/alarmTypeIcon.png'
import alarmIcon from '@/assets/images/plan/alarmIcon.png'
import notifyIcon from '@/assets/images/plan/notifyIcon.png'
import warnIcon from '@/assets/images/plan/warnIcon.png'
import confirmIcon from '@/assets/images/plan/confirmIcon.png'
import dayjs from 'dayjs'
import 'dayjs/locale/zh-cn'
dayjs.locale('zh-cn')
export default {
  name: 'planFlow',
  components: {
    Vue2OrgTree
  },
  props: {
    alarmData: {
      type: Object,
      default: () => {}
    },
    alarmType: { // 1演习 2确警
      type: Number,
      default: 1
    }
  },
  data() {
    return {
      alarmIcon,
      notifyIcon,
      warnIcon,
      confirmIcon,
      depts: {}, // 部门列表
      treeData: {},
      eventData: {
        1: {}, // 演习
        2: {}, // 演习-启动预案
        3: {}, // 演习-自定义
        4: {}, // 确警
        5: {}, // 确警-启动预案
        6: {} // 确警-自定义
      },
      currentStepId: [], // 预览当前步骤id
      notifyList: [], // 通知列表
      warnList: [], // 提示列表
      confirmList: [], // 确认列表
      stepTypes: {
        1: ['1', '2', '3', '8'], // 演习包含步骤
        2: ['4', '5', '6', '8'] // 确警包含步骤
      },
      activeStep: 0, // 当前预案执行到的步骤
      autoCallList: [], // 自动打电话列表
      callNum: 0
    }
  },
  computed: {

  },
  created() {
    this.getUseDeptList()
  },
  methods: {
    // 获取确认事件记录
    getConfirmRecordList() {
      GetConfirmRecordList({ alarmId: this.alarmData.alarmId }).then(res => {
        if (res.data.code == 200) {
          const data = {}
          res.data.data.forEach(item => {
            data[item.planConfirmEventId] = item.confirmStatus
          })
          const confirmList = this.confirmList.map(item => {
            return {
              ...item,
              status: data[item.id] || '',
              loading: false
            }
          })
          this.confirmList = confirmList
        }
      })
    },
    // 提交确认事件
    saveConfirmRecord(status, item, index) {
      if (this.confirmList[index].loading) return
      this.confirmList[index].loading = true
      const userInfo = this.$store.state.loginInfo.user
      const params = {
        alarmId: this.alarmData.alarmId,
        confirmStatus: status,
        createName: userInfo.staffName,
        planBasicId: item.planBasicId,
        planConfirmEventId: item.id,
        confirmTitle: item.confirmTitle
      }
      SaveConfirmRecord({ confirmRecords: [params] }).then(res => {
        if (res.data.code == 200) {
          this.confirmList[index].loading = false
          this.getConfirmRecordList()
        }
      })
    },
    // 提交当前预案执行到的步骤
    savePreplanOperationState(step) {
      const userInfo = this.$store.state.loginInfo.user
      const param = {
        alarmId: this.alarmData.alarmId,
        alarmAdress: this.alarmData.alarmSpaceName,
        alarmLevel: this.alarmData.alarmLevel,
        alarmTime: this.alarmData.alarmStartTime,
        // alarmType: this.alarmData.alarmEntityTypeName,
        alarmType: this.alarmData.incidentName,
        createName: userInfo.staffName,
        planBasicId: this.alarmData.planId,
        stepType: step || this.activeStep // 步骤类型（ 1演习 2演习-启动预案 3演习-自定义 4确警 5确警-启动预案 6确警-自定义 7误报 8解除警报）
      }
      if (!param.stepType) return
      SavePreplanOperationState(param).then(res => {
        if (res.data.code == 200) {
          this.getCallData()
        }
      })
    },
    // 获取当前预案执行到的步骤
    getPreplanStateByAlarmId() {
      GetPreplanStateByAlarmId({ alarmId: this.alarmData.alarmId }).then(res => {
        if (res.data.code == 200) {
          const states = res.data.data.states
          const steps = this.stepTypes[this.alarmType]
          // 判断为空新增0状态，作为前端判断回显流程图勾选
          if (!states.length) {
            this.savePreplanOperationState('0')
            return
          }
          console.log(states[0], steps.indexOf(states[0]))
          this.activeStep = steps[steps.indexOf(states[0]) + 1]
          // 当前执行完成步骤
          const stepApart = {
            1: {
              0: ['0'],
              1: ['0', '2'],
              2: ['0', '2', '2-2'],
              3: ['0', '2', '2-2', '2-2-1'],
              8: ['0', '2', '2-2', '2-2-1', '2-2-1-1']
            },
            2: {
              0: ['0'],
              4: ['0', '3'],
              5: ['0', '3', '3-2'],
              6: ['0', '3', '3-2', '3-2-1'],
              8: ['0', '3', '3-2', '3-2-1', '3-2-1-1']
            }
          }
          this.currentStepId = stepApart[this.alarmType][states[0]]
          console.log(1111111, this.currentStepId, this.activeStep)
          this.setTreeData()
          // 执行步骤4
          if (['3', '6'].includes(this.activeStep)) {
            this.savePreplanOperationState()
          }
          // 获取当前步骤自动打电话列表
          const callList = this.autoCallList.filter(v => v.stepType == this.activeStep)
          // 获取当前已打电话数量
          let callNum = 0
          steps.slice(0, steps.indexOf(states[0]) + 1).forEach(step => {
            callNum += this.autoCallList.filter(v => v.stepType == step).length
          })
          this.callNum = callNum
          console.log(steps.slice(0, steps.indexOf(states[0]) + 1), callNum)
          // execuNum:已打电话数量 sumNum:应该打电话数量
          if ((res.data.data.execuNum < res.data.data.sumNum) || (res.data.data.execuNum == 0 && res.data.data.sumNum == 0)) {
            this.autoCall(callList)
          }
        }
      })
    },
    // 手动电话
    handCall(item) {
      const param = {
        alarmId: this.alarmData.alarmId,
        name: item.personName,
        operation: '电话通知',
        phone: item.noticePhone,
        planNoticePersonId: item.noticePersonList[0].personId,
        noticeContent: item.noticeContent
      }
      try {
        window.chrome.webview.hostObjects.sync.bridge.HandCallJson(JSON.stringify(param))
      } catch (error) {}
    },
    // 自动电话
    autoCall(list) {
      console.log('已启动自动打电话', list)
      if (!list.length) return
      const param = list.map(v => {
        return {
          alarmId: this.alarmData.alarmId,
          name: v.personName,
          operation: '电话通知',
          // phone: v.textPhone,
          phone: v.noticePhone,
          planNoticePersonId: v.personId,
          noticeContent: v.noticeContent
        }
      })
      try {
        window.chrome.webview.hostObjects.sync.bridge.AutoCallJson(JSON.stringify(param))
      } catch (error) {}
    },
    // 获取当前步骤打电话列表
    getCallData() {
      const allCallList = []
      // 取出所有拨打电话数据
      this.stepTypes[this.alarmType].forEach(step => {
        if (step != 8) {
          this.eventData[step].noticeEventList.forEach(item => {
            if (item.noticePersonList.length && (item.noticeWay == 0 || item.noticeWay == 1)) {
              allCallList.push(...item.noticePersonList.map(v => {
                return {
                  ...v,
                  stepType: item.stepType,
                  checkFlag: item.checkFlag, // 是否通知
                  noticeContent: item.noticeContent, // 电话沟通内容
                  noticeWay: item.noticeWay, // 通知方式
                  noticeType: item.noticeType, // 通知类型
                  showFlag: item.showFlag, // 显示拨打按钮
                  textPhone: item.noticePhone
                }
              }))
            }
          })
        }
      })
      this.autoCallList = allCallList.filter(v => v.noticeWay == 1) // 自动打电话列表
      this.getPreplanStateByAlarmId()
    },
    setTreeData() {
      this.treeData = this.alarmType == 1 ? {
        id: '0',
        name: '系统报警',
        step: 1,
        children: [
          {
            id: '2',
            name: '演习',
            step: 2,
            tip: '点击演习按钮',
            notify: true,
            action: false,
            stepType: 1,
            children: [
              {
                id: '2-2',
                name: '启动预案',
                step: 3,
                tip: '点击确认按钮',
                notify: true,
                action: false,
                stepType: 2,
                children: [
                  {
                    id: '2-2-1',
                    name: '节点名称',
                    step: 4,
                    tip: '请添加节点动作',
                    notify: true,
                    action: true,
                    stepType: 3,
                    children: [
                      {
                        id: '2-2-1-1',
                        name: '解除报警',
                        step: 5,
                        tip: '点击解除报警按钮'
                      }
                    ]
                  }
                ]
              }
            ]
          }
        ]
      } : {
        id: '0',
        name: '系统报警',
        step: 1,
        children: [
          {
            id: '3',
            name: '确警',
            step: 2,
            tip: '点击确警按钮',
            notify: true,
            action: false,
            stepType: 4,
            children: [
              {
                id: '3-2',
                name: '启动预案',
                step: 3,
                tip: '点击确认按钮',
                notify: true,
                action: false,
                stepType: 5,
                children: [
                  {
                    id: '3-2-1',
                    name: '节点名称',
                    step: 4,
                    tip: '请添加节点动作',
                    notify: true,
                    action: true,
                    stepType: 6,
                    children: [
                      {
                        id: '3-2-1-1',
                        name: '解除报警',
                        step: 5,
                        tip: '点击解除报警按钮'
                      }
                    ]
                  }
                ]
              }
            ]
          }
        ]
      }
      const nodeData = this.alarmType == 1 ? this.eventData[3] : this.eventData[6]
      this.notifyList = nodeData.noticeEventList
      this.warnList = nodeData.warnEventList
      this.confirmList = nodeData.confirmEventList
      this.getConfirmRecordList()
    },
    // 获取预案详情
    getPlanDetail() {
      GetPlanDetail({ id: this.alarmData.planId }).then(res => {
        if (res.data.code == 200) {
          let { warnEventList, noticeEventList, confirmEventList } = res.data.data
          noticeEventList = noticeEventList.filter(v => v.checkFlag == 1)
          warnEventList = warnEventList.filter(v => v.checkFlag == 1)
          confirmEventList = confirmEventList.filter(v => v.checkFlag == 1)
          warnEventList.forEach(item => {
            if (Object.keys(this.eventData[item.stepType]).includes('warnEventList')) {
              this.eventData[item.stepType].warnEventList.push(item)
            } else {
              this.eventData[item.stepType].warnEventList = [item]
            }
          })
          noticeEventList.forEach(item => {
            item.noticeWay = Number(item.noticeWay)
            item.noticeType = Number(item.noticeType)
            if (Object.keys(this.eventData[item.stepType]).includes('noticeEventList')) {
              this.eventData[item.stepType].noticeEventList.push(item)
            } else {
              this.eventData[item.stepType].noticeEventList = [item]
            }
          })
          confirmEventList.forEach(item => {
            if (Object.keys(this.eventData[item.stepType]).includes('confirmEventList')) {
              this.eventData[item.stepType].confirmEventList.push(item)
            } else {
              this.eventData[item.stepType].confirmEventList = [item]
            }
          })
          this.getCallData()
        }
      })
    },
    // 获取科室
    getUseDeptList() {
      GetSelectedDept().then((res) => {
        if (res.data.code == '200') {
          this.$tools.transData(res.data.data, 'id', 'parentId', 'children').forEach(v => {
            this.depts[v.id] = v.deptName
          })
          this.getPlanDetail()
        }
      })
    },
    // 流程图渲染方法
    renderContent(h, data) {
      let currentNodeData, startContent, isNotice, isWarn, isConfirm, noticeContent, warnContent, confirmContent = ''
      if (data.name == '系统报警') {
        return (
          <div class={['tree-item', 'alarm-item', this.currentStepId.includes(data.id) ? 'current-step' : '']}>
            <p class="item-title">{data.name}</p>
            <p class="item-step">步骤{data.step}</p>
            <p class="item-tips">报警面板样式<span>可设置提示内容</span></p>
            <div class="item-main alarm-main">
              <p class="alarm-tyle">
                <img src={alarmTypeIcon} />
                <span>报警类型</span>
              </p>
              <p class="alarm-info">
                <span class="alarm-label">报警位置：</span>
                <span class="alarm-value">{this.alarmData.alarmSpaceName}</span>
              </p>
              <p class="alarm-info">
                <span class="alarm-label">报警设备：</span>
                <span class="alarm-value">{this.alarmData.alarmObjectName}</span>
              </p>
              <p class="alarm-info">
                <span class="alarm-label">报警时间：</span>
                <span class="alarm-value">{dayjs(this.alarmData.alarmStartTime).format('YYYY-MM-DD HH:mm:ss')}</span>
              </p>
              <p class="alarm-info">
                <span class="alarm-label">报警级别：</span>
                <span class="alarmLevel">{this.alarmData.alarmLevel}</span>
              </p>
              <div class="alarm-btns">
                <el-button >误报</el-button>
                <el-button type="primary">演习</el-button>
                <el-button type="primary">确警</el-button>
              </div>
            </div>
          </div>
        )
      } else if (data.name == '误报' || data.name == '解除报警' || data.name == '取消预案') {
        return (
          <div class={['tree-item', this.currentStepId.includes(data.id) ? 'current-step' : '']}>
            <p class="item-title">{data.name}</p>
            <p class="item-step">步骤{data.step}</p>
            <div class="item-main end-main">
              <p class="end-label">触发条件</p>
              <p class="end-value">{data.tip}</p>
            </div>
          </div>
        )
      } else if (data.name == '演习' || data.name == '确警' || data.name == '启动预案' || (data.name == '节点名称' || data.step == 4)) {
        currentNodeData = this.eventData[data.stepType]
        isNotice = Object.keys(currentNodeData).includes('noticeEventList') && currentNodeData.noticeEventList.length
        isWarn = Object.keys(currentNodeData).includes('warnEventList') && currentNodeData.warnEventList.length
        isConfirm = Object.keys(currentNodeData).includes('confirmEventList') && currentNodeData.confirmEventList.length
        if (!Object.keys(currentNodeData).length) {
          startContent = (
            <div class="item-main start-main">
              <p class="start-label">触发条件</p>
              <p class="start-value">{data.tip}</p>
            </div>
          )
        } else {
          if (isNotice) {
            noticeContent = (
              <div class="start-main-item">
                <p class="start-main-item-title">{currentNodeData.noticeEventList[0].eventName}</p>
                {
                  currentNodeData.noticeEventList.map(item => {
                    if (item.checkFlag == 1) {
                      return (
                        <p class="start-main-item-value">
                          <el-tooltip placement="right" content={item.noticeType == 0 ? '通知人员：' + item.personName : '通知部门：' + item.departCode.split(',').map(v => this.depts[v])}>
                            <span>{item.noticeType == 0 ? '通知人员：' + item.personName : '通知部门：' + item.departCode.split(',').map(v => this.depts[v])}</span>
                          </el-tooltip>
                          <i class="el-icon-arrow-right"></i>
                        </p>
                      )
                    }
                  })
                }
              </div>
            )
          }
          if (isWarn) {
            warnContent = (
              <div class="start-main-item">
                <p class="start-main-item-title">{currentNodeData.warnEventList[0].eventName}</p>
                {
                  currentNodeData.warnEventList.map(item => {
                    if (item.checkFlag == 1) {
                      return (
                        <p class="start-main-item-value">
                          <el-tooltip placement="right" content={item.warnContent}>
                            <span>{item.warnContent}</span>
                          </el-tooltip>
                          <i class="el-icon-arrow-right"></i>
                        </p>
                      )
                    }
                  })
                }
              </div>
            )
          }
          if (isConfirm) {
            confirmContent = (
              <div class="start-main-item">
                <p class="start-main-item-title">{currentNodeData.confirmEventList[0].eventName}</p>
                {
                  currentNodeData.confirmEventList.map(item => {
                    if (item.checkFlag == 1) {
                      return (
                        <p class="start-main-item-value">
                          <el-tooltip placement="left" content={item.confirmTitle}>
                            <span>{item.confirmTitle}</span>
                          </el-tooltip>
                          <i class="el-icon-arrow-right"></i>
                        </p>
                      )
                    }
                  })
                }
              </div>
            )
          }
          startContent = (
            <div>
              {
                isWarn ? <div class="item-main start-main">{warnContent}</div> : ''
              }
              {
                isNotice ? <div class="item-main start-main">{noticeContent}</div> : ''
              }
              {
                isConfirm ? <div class="item-main start-main">{confirmContent}</div> : ''
              }
            </div>
          )
        }
        return (
          <div class={['tree-item', 'start-item', !this.isPreview ? 'start-item-hover' : '', this.currentStepId.includes(data.id) ? 'current-step' : '']}>
            <p class="item-title">{isNotice ? currentNodeData.noticeEventList[0].stepName : data.name}</p>
            {(data.stepType == 3 || data.stepType == 6) && (!isNotice && !isWarn && !isConfirm) ? <i class="el-icon-warning"></i> : ''}
            <p class="item-step">步骤{data.step}</p>
            {startContent}
          </div>
        )
      }
    }
  }
}

</script>

<style lang="scss" scoped>
.planFlow {
  width: 100%;
  height: 100%;
  display: flex;
  overflow: hidden;
  // background: radial-gradient( 0% 129% at 135% 17%, #152C4A 0%, #061431 100%);
  .planFlow-left, .planFlow-right {
    width: 50%;
    height: 100%;
    overflow: auto;
  }
  .planFlow-left {
    text-align: center;
    padding: 16px;
  }
  .plan-info {
    background: rgba(53,98,219,0.06);
    display: flex;
    flex-direction: column;
    padding: 0px 16px 16px 16px;
    .info-content {
      margin-top: 16px;
      background: rgba(53,98,219,0.06);
      border-radius: 8px 8px 8px 8px;
      word-wrap: break-word;
      word-break: break-all;
    }
    .content-title {
      padding: 16px 24px 16px 16px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      border-bottom: 1px solid rgba(133,145,206,0.5);
      .title-left {
        display: flex;
        align-items: center;
        font-weight: 500;
        font-size: 16px;
        color: #FFFFFF;
        span {
          margin-left: 8px;
        }
      }
      .title-right {
        font-weight: 300;
        font-size: 14px;
        color: #FFFFFF;
      }
    }
    .alarm-content {
      padding: 0px 16px;
      .title {
        font-weight: bold;
        font-size: 16px;
        color: #FFFFFF;
        line-height: 19px;
        padding: 16px 0px;
      }
      .call-status {
        display: flex;
        justify-content: space-between;
        p:first-child {
          display: flex;
          align-items: center;
          img {
            width: 18px;
            height: 18px;
          }
          span {
            font-weight: 400;
            font-size: 14px;
            color: #8BDDF5;
            margin-left: 4px;
          }
        }
        p:last-child {
          font-weight: 300;
          font-size: 14px;
          color: #C2C4C8;
        }
      }
      .call-num {
        font-weight: 300;
        font-size: 14px;
        color: #FFFFFF;
        line-height: 22px;
        padding: 16px 0px;
      }
    }
    .notify-content {
      padding: 0px 16px 16px 16px;
      .notify-item {
        display: flex;
        align-items: center;
        padding: 16px 0px;
        border-bottom: 1px solid rgba(133,145,206,0.5);
        .item-icon {
          width: 30px;
          height: 30px;
        }
        .item-info {
          flex: 1;
          padding: 0px 16px;
          .info-title {
            font-weight: 400;
            font-size: 14px;
            color: #FFFFFF;
          }
          .info-main {
            display: flex;
            margin-top: 8px;
          }
          .info-label {
            display: inline-block;
            text-align: right;
            font-weight: 300;
            font-size: 14px;
            color: #B0E3FA;
            width: 70px;
          }
          .info-value {
            flex: 1;
            display: inline-block;
            font-weight: 500;
            font-size: 14px;
            color: #FFFFFF;
          }
        }
        .item-btn {
          cursor: pointer;
          background: url('@/assets/images/plan/btnBgd1.png') no-repeat center center / 100% 100%;
          padding: 6px 10px;
          display: flex;
          align-items: center
          img {
            width: 14px;
            height: 14px;
          }
          span {
            font-size: 14px;
            color: #A4AFC1;
            text-shadow: 0px 2px 8px rgba(5,28,55,0.42), 0px 0px 7px rgba(75,122,229,0.42);
          }
        }
        .item-tip {
          font-weight: 300;
          font-size: 12px;
          color: #C2C4C8;
        }
      }
    }
    .warn-content {
      padding: 16px;
      .warn-info {
        padding: 0px 16px 16px 0px;
        border-bottom: 1px solid rgba(133,145,206,0.5);
      }
      .info-item {
        margin-top: 16px;
        display: flex;
      }
      .item-label {
        display: inline-block;
        font-weight: 300;
        font-size: 14px;
        color: #B0E3FA;
        width: 115px;
        text-align: right;
      }
      .item-value{
        flex: 1;
        display: inline-block;
        font-weight: 400;
        font-size: 14px;
        color: #FFFFFF;
      }
      .warn-list {
        .list-item {
          border-bottom: 1px solid rgba(133,145,206,0.5);
          padding: 16px;
          span:first-child {
            display: inline-block;
            font-weight: 300;
            font-size: 14px;
            color: #B0E3FA;
          }
          span:last-child {
            display: inline-block;
            margin-top: 16px;
            font-weight: 400;
            font-size: 14px;
            color: #FFFFFF;
          }
        }
        .list-item:last-child {
          border-bottom: none;
        }
      }
    }
    .confirm-content {
      padding: 0px 16px;
      .confirm-item {
        padding: 16px 0px;
        border-bottom: 1px solid rgba(133,145,206,0.5);
      }
      .item-content {
        display: flex;
        justify-content: space-between;
        align-items: center;
        .item-info {
          font-weight: 400;
          font-size: 14px;
          color: #FFFFFF;
          i {
            font-size: 14px;
            margin-right: 16px;
          }
        }
        .item-status {
          color: #C2C4C8;
          font-weight: 400;
          font-size: 14px;
        }
      }
      .item-operate {
        text-align: right;
        margin-top: 8px;
        p {
          display: inline-block;
          cursor: pointer;
          font-weight: 400;
          font-size: 14px;
          color: #A4AFC1;
          text-shadow: 0px 2px 8px rgba(5,28,55,0.42), 0px 0px 7px rgba(75,122,229,0.42);
        }
        .operate-ignore {
          padding: 6px 10px;
          background: url('@/assets/images/plan/btnBgd2.png') no-repeat center center / 100% 100%;
        }
        .operate-confirm {
          margin-left: 10px;
          padding: 6px 9px;
          background: url('@/assets/images/plan/btnBgd1.png') no-repeat center center / 100% 100%;
        }
      }
    }
  }
}
</style>
<style lang="less">
@import './org-tree.less';
</style>
