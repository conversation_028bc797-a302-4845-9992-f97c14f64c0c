<template>
  <div class="keyAreasComponent">
    <div class="module-container" style="height: calc(22%)">
      <div class="module-header">
        <div class="title-left" style="width: 60%">
          <p class="title-left-text">重点区域统计</p>
        </div>
        <div class="title-right">
          <el-dropdown trigger="click" @command="(val) => (tagCurrent = val)">
            <span class="el-dropdown-link"> {{ tagList.find((v) => v.value == tagCurrent)?.text ?? '' }} <i class="el-icon-arrow-down"></i> </span>
            <el-dropdown-menu slot="dropdown">
              <el-dropdown-item v-for="item in tagList" :key="item.value" :command="item.value" :class="{ isBjxl: tagCurrent == item.value }">{{ item.text }}</el-dropdown-item>
            </el-dropdown-menu>
          </el-dropdown>
          <div class="icon-box" @click="viewMore(1)">
            <img src="../../../assets/images/order_more.png" class="order-more" />
          </div>
        </div>
      </div>
      <div class="module-content" style="height: calc(100% - 44px)">
        <div class="statistics_top_new">
          <div v-for="(item, index) in statisticsData" :key="index" class="statistics_top_item">
            <img src="@/assets/images/qhdsys/mj-tb.png" alt="" />
            <div class="statistics_top_content">
              <p class="statistics_top_title">{{ item.name }}</p>
              <div class="font_bg">
                <p class="color_font" :style="{ color: item.key == 'abnormalCount' ? '#FF2D55' : '' }">{{ keyAreasStatistics[item.key] || 0 }}</p>
                <p class="color_unit">{{ item?.unit ?? '' }}</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div v-show="roomData.ssmType < 5" class="module-container" style="height: calc(25%)">
      <div class="module-header">
        <div class="title-left">
          <p class="title-left-text">区域排行</p>
        </div>
      </div>
      <div class="module-content" style="height: calc(100% - 48px)">
        <div id="EChart" style="height: 100%"></div>
      </div>
    </div>
    <div class="module-container" :style="{height: `calc(${roomData.ssmType < 5 ? 25 : 38 }%)`}">
      <div class="module-header">
        <div class="title-left">
          <p class="title-left-text">重点区域列表</p>
        </div>
        <div class="title-right">
          <div class="icon-box" @click="viewMore(1)">
            <img src="../../../assets/images/order_more.png" class="order-more" />
          </div>
        </div>
      </div>
      <div v-loading="keyAreasTableLoading" class="module-content" style="height: calc(100% - 48px)">
        <el-table
          v-if="!keyAreasTableLoading"
          ref="keyAreasTable"
          v-el-table-infinite-scroll="tableLoadMore"
          v-scrollHideTooltip
          class="table-center-transfer"
          :data="keyAreasList"
          height="100%"
          :cell-style="{ padding: ' 8px', backgroundColor: 'transparent', border: 'none', padding: '3px' }"
          :header-cell-style="{ background: '#8591CE26!important', color: '#8BDDF5FF', padding: '4px 8px', fontWeight: 'bold' }"
          style="width: 100%"
          element-loading-background="rgba(0, 0, 0, 0.2)"
          @row-click="(row, column) => tableRowClick(1, row, column)"
        >
          <el-table-column v-for="(column, index) in keyAreasTableColumn" :key="index" :prop="column.prop" :label="column.label" :min-width="column.minWidth" show-overflow-tooltip>
            <template slot-scope="scope">
              <table-render v-if="column.render" :sc="scope" :row="scope.row" :render="column.render"></table-render>
              <div v-else-if="!column.formatter">
                {{ scope.row[column.prop] }}
              </div>
              <div v-else>
                {{ column.formatter(scope.row) }}
              </div>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </div>
    <div class="module-container" :style="{height: `calc(${roomData.ssmType < 5 ? 28 : 40}%)`}">
      <div class="module-header">
        <div class="title-left">
          <p class="title-left-text">人流监测设备</p>
        </div>
        <div class="title-right">
          <div class="icon-box" @click="viewMore(2)">
            <img src="../../../assets/images/order_more.png" class="order-more" />
          </div>
        </div>
      </div>
      <div class="module-content" style="height: calc(100% - 48px)">
        <el-table
          v-el-table-infinite-scroll="deviceTableLoadMore"
          v-scrollHideTooltip
          v-loading="deviceTableLoading"
          class="table-center-transfer"
          :data="deviceList"
          height="100%"
          :cell-style="{ padding: ' 8px', backgroundColor: 'transparent', border: 'none', padding: '3px' }"
          :header-cell-style="{ background: '#8591CE26!important', color: '#8BDDF5FF', padding: '4px 8px', fontWeight: 'bold' }"
          style="width: 100%"
          element-loading-background="rgba(0, 0, 0, 0.2)"
          @row-click="(row, column) => tableRowClick(2, row, column)"
        >
          <el-table-column v-for="(column, index) in deviceTableColumn" :key="index" :prop="column.prop" :label="column.label" :min-width="column.minWidth" show-overflow-tooltip>
            <template slot-scope="scope">
              <table-render v-if="column.render" :sc="scope" :row="scope.row" :render="column.render"></table-render>
              <div v-else-if="!column.formatter">
                {{ scope.row[column.prop] }}
              </div>
              <div v-else>
                {{ column.formatter(scope.row) }}
              </div>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </div>
    <keyAreasListDialog ref="keyAreasListDialog" :isDialog="isKeyAreasListDialog" :roomData="roomData" @change="keyAreasChange" @close="isKeyAreasListDialog = false" />
    <deviceDetails v-if="isDeviceDetails" :dialogShow="isDeviceDetails" :roomData="roomData" :deviceId="deviceId" @deviceDetailsClose="() => isDeviceDetails = false" />
    <monitorDeviceListDialog v-if="isMonitorDeviceList" :isDialog="isMonitorDeviceList" :roomData="roomData" :keyAreasSpaceId="keyAreasSpaceId" :deviceIds="keyAreasDeviceId" @close="() => isMonitorDeviceList = false" />
    <alarmTable v-if="isAlarmTable" :visible="isAlarmTable" :params="alarmInitSearchData" @showDetail="(alarm) => {alarmId = alarm.alarmId; isAlarmDetail = true}" @close="() => isAlarmTable = false" />
    <alarmDetailNew v-if="isAlarmDetail" :dialogShow="isAlarmDetail" :alarmId="alarmId" @close="isAlarmDetail = false"/>
  </div>
</template>

<script lang="jsx">
import { GetGridAreaStatistics, GetRridAreaRank, GetRridAreaList, GetRridAreaDeviceList, GetKeyAreasSpaceIdList, GetKeyAreasDeviceIdList } from '@/utils/spaceManage'
import icon_5 from '@/assets/images/icon-5.png'
import icon_6 from '@/assets/images/icon-6.png'
import icon_2 from '@/assets/images/icon-2.png'
import * as echarts from 'echarts'
import tableRender from '../components/tableRender.vue'
import moment from 'moment'
moment.locale('zh-cn')
import alarmTable from '@/views/surgeonGeneral/comprehensiveStatistics/components/alarmStatistics/detail.vue'
export default {
  name: 'keyAreasComponent',
  components: {
    tableRender,
    alarmTable,
    keyAreasListDialog: () => import('./components/keyAreas/keyAreasListDialog.vue'),
    deviceDetails: () => import('../components/deviceDetailsNew.vue'),
    monitorDeviceListDialog: () => import('../components/monitorDeviceListDialog.vue'),
    alarmDetailNew: () => import('./components/alarmDetailNew.vue')
  },
  props: {
    roomData: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      alarmId: '',
      isAlarmDetail: false,
      isAlarmTable: false,
      isMonitorDeviceList: false,
      isDeviceDetails: false,
      isKeyAreasListDialog: false,
      deviceId: '',
      icon_2,
      icon_5,
      icon_6,
      keyAreasTableLoading: false,
      deviceTableLoading: false,
      keyAreasStatistics: {}, // 重点区域统计
      keyAreasList: [], // 重点区域列表
      deviceList: [], // 设备列表
      statisticsData: [
        {
          name: '区域总数',
          key: 'allCount',
          unit: ''
        },
        {
          name: '正常区域',
          key: 'normalCount',
          unit: ''
        },
        {
          name: '异常区域',
          key: 'abnormalCount',
          unit: ''
        }
      ], // 统计数据
      keyAreasTableColumn: [
        {
          prop: 'areaName',
          label: '重点区域名称',
          minWidth: 110
        },
        {
          prop: 'throughCount',
          label: '人员流量'
        },
        {
          prop: 'abnormalCount',
          label: '设备状态',
          render: (h, row) => {
            return (
              <p>
                <span style="color:#FF2D55">{row.row.abnormalCount}</span>
                <span>/{row.row.allCount}</span>
              </p>
            )
          }
        },
        {
          prop: 'alarmCount',
          label: '区域报警数'
        }
      ],
      deviceTableColumn: [
        {
          prop: 'assetName',
          label: '设备名称'
        },
        {
          prop: 'regionName',
          label: '位置信息',
          render: (h, {row}) => {
            return (
              <div>
                {row?.regionName?.split('>').reverse().join(' > ')}
              </div>
            )
          }
        },
        {
          prop: 'throughCount',
          label: '人员流量'
        },
        {
          prop: 'status',
          label: '运行状态',
          render: (h, row) => {
            return (
              <div>
                {row.row.status == '0' && (
                  <div class="table-icon">
                    <img src={icon_5} />
                    <span style="color:#61E29D">{row.row.statusName}</span>
                  </div>
                )}
                {row.row.status == '6' && (
                  <div class="table-icon">
                    <img src={icon_6} />
                    <span style="color:#86909C">{row.row.statusName}</span>
                  </div>
                )}
                {row.row.status == '10' && (
                  <div class="table-icon">
                    <img src={icon_2} />
                    <span style="color:#FF2D55">{row.row.statusName}</span>
                  </div>
                )}
              </div>
            )
          }
        }
      ],
      pagination: {
        total: 0,
        pageSize: 15,
        pageNo: 1
      },
      devicePagination: {
        total: 0,
        pageSize: 15,
        pageNo: 1
      },
      chartData: [],
      ssmCodes: [],
      tagCurrent: 'day', // 日期类型
      tagList: [
        { text: '今日', value: 'day' },
        { text: '本周', value: 'week' },
        { text: '本月', value: 'month' },
        { text: '本年', value: 'year' }
      ],
      dateObj: {
        day: [moment().format('YYYY-MM-DD'), moment().format('YYYY-MM-DD')],
        week: [moment().startOf('isoWeek').format('YYYY-MM-DD'), moment().endOf('isoWeek').format('YYYY-MM-DD')],
        month: [moment().startOf('month').format('YYYY-MM-DD'), moment().endOf('month').format('YYYY-MM-DD')],
        year: [moment().startOf('year').format('YYYY-MM-DD'), moment().endOf('year').format('YYYY-MM-DD')]
      },
      selectSpaceId: '',
      keyAreasSpaceId: [], // 重点区域空间列表
      keyAreasDeviceId: [], // 重点区域设备列表
      alarmInitSearchData: {}
    }
  },
  watch: {
    // 监听模型层级数据变化
    roomData: {
      handler(newVal, oldVal) {
        this.initData()
      },
      deep: true
    },
    tagCurrent(val) {
      this.getRridAreaList()
      this.getRridAreaDeviceList()
    },
    isKeyAreasListDialog(val) {
      this.$tools.showDialog()
    },
    isAlarmTable(val) {
      this.$tools.showDialog()
    }
  },
  mounted() {
    // 初始化调用
    this.initData()
  },
  methods: {
    initData() {
      this.selectSpaceId = ''
      this.ssmCodes = this.roomData.ssmCodes?.split(',') || []
      this.pagination.pageNo = 1
      this.devicePagination.pageNo = 1
      this.getGridAreaStatistics()
      this.getRridAreaRank()
      this.getRridAreaList()
      this.getRridAreaDeviceList()
    },
    viewMore(type) {
      if (type == 1) {
        this.isKeyAreasListDialog = true
      } else {
        this.getKeyAreasDeviceIdList()
      }
    },
    // 获取重点区域下的空间id
    getKeyAreasSpaceIdList(keyAreasId = '', type = 'device') {
      GetKeyAreasSpaceIdList({id: keyAreasId}).then(res => {
        if (res.data.code == 200) {
          this.keyAreasSpaceId = res.data.data
          if (type == 'device') {
            this.isMonitorDeviceList = true
          } else {
            this.alarmInitSearchData = {
              dateType: this.tagCurrent
            }
            if (this.keyAreasSpaceId.length) {
              this.alarmInitSearchData.spaceIdList = this.keyAreasSpaceId
            } else {
              this.alarmInitSearchData.alarmSpaceId = this.ssmCodes.at(-1)
            }
            this.isAlarmTable = true
            //
          }
        }
      })
    },
    // 获取重点区域下的设备id
    getKeyAreasDeviceIdList() {
      GetKeyAreasDeviceIdList({}).then(res => {
        if (res.data.code == 200) {
          this.keyAreasDeviceId = res.data.data
          this.isMonitorDeviceList = true
        }
      })
    },
    // 获取区域排行
    getRridAreaRank() {
      GetRridAreaRank({}).then((res) => {
        if (res.data.code === '200') {
          this.chartData = res.data.data || []
          this.$nextTick(() => {
            this.getRenderer(this.chartData.reverse())
          })
        }
      })
    },
    // 获取重点区域统计
    getGridAreaStatistics() {
      const params = {
        regionCode: this.ssmCodes.at(-1)
      }
      GetGridAreaStatistics(params).then((res) => {
        if (res.data.code === '200') {
          this.keyAreasStatistics = res.data.data
        }
      })
    },
    // 初始化统计表
    getRenderer(data) {
      // 基于准备好的dom，初始化echarts实例
      const EChart = echarts.init(document.getElementById('EChart'))
      const barWidth = 14 /* 进度条宽度 */
      const normalData = [] /* 正常 */
      const abnormalData = [] /* 异常 */
      const attaVal = [] /* 进度条数值 */
      const topName = [] /* 进度条名称 */
      data.forEach((item, i) => {
        topName[i] = {
          value: item.floorName,
          textStyle: {
            color: '#FFF'
          }
        }
        attaVal[i] = item.allCount
        normalData[i] = item.normalCount
        abnormalData[i] = item.abnormalCount
      })
      // 配置参数
      let config
      if (data.length) {
        config = {
          background: '#ffff',
          tooltip: {
            show: false,
            textStyle: {
              fontSize: 16
            }
          },
          grid: {
            left: '2%',
            right: '10%',
            top: '13%',
            bottom: '0%',
            containLabel: true
          },
          dataZoom: [
            {
              yAxisIndex: [0, 1],
              orient: 'vertical',
              show: true,
              type: 'slider',
              start: 100,
              // end: 100,
              width: 8,
              left: '99%',
              borderColor: 'rgba(43,48,67,.1)',
              fillerColor: '#6580b8',
              zoomLock: true,
              showDataShadow: false,
              backgroundColor: 'rgba(43,48,67,.1)',
              showDetail: false,
              // realtime: true,
              filterMode: 'filter',
              handleIcon: 'M-9.35,34.56V42m0-40V9.5m-2,0h4a2,2,0,0,1,2,2v21a2,2,0,0,1-2,2h-4a2,2,0,0,1-2-2v-21A2,2,0,0,1-11.35,9.5Z',
              handleStyle: {
                color: '#6580b8',
                borderColor: '#6580b8'
              },
              maxValueSpan: 4,
              minValueSpan: 4,
              brushSelect: false
            },
            {
              type: 'inside',
              show: false,
              yAxisIndex: [0, 1],
              height: 8
            }
          ],
          legend: {
            show: true,
            top: '3%',
            itemHeight: 8,
            itemWidth: 8,
            itemGap: 20,
            textStyle: {
              fontSize: 12,
              color: '#fff'
            },
            selectedMode: false
          },
          xAxis: {
            show: false,
            type: 'value'
          },
          yAxis: [
            {
              type: 'category',
              // inverse: true,
              triggerEvent: true,
              axisTick: {
                show: false
              },
              axisLine: {
                show: false
              },
              axisLabel: {
                textStyle: {
                  color: '#fff',
                  fontSize: 14
                },
                interval: 0,
                width: 100,
                overflow: 'truncate',
                ellipsis: '...'
              },
              data: topName
            }
          ],
          series: [
            {
              name: '正常',
              stack: 'total',
              type: 'bar',
              barWidth: barWidth,
              data: normalData,
              itemStyle: {
                normal: {
                  color: ' rgba(101,234,162,0.6)',
                  borderColor: '#2C344C',
                  borderWidth: 1
                }
              }
            },
            {
              name: '异常',
              type: 'bar',
              barWidth: barWidth,
              stack: 'total',
              data: abnormalData,
              itemStyle: {
                normal: {
                  color: 'rgba(255,45,85,0.6)',
                  borderColor: '#2C344C',
                  borderWidth: 1
                }
              }
            },
            // total
            {
              type: 'bar',
              zlevel: 1,
              barWidth: barWidth,
              barGap: '-100%',
              label: {
                show: true,
                position: 'right',
                distance: 8,
                textStyle: { color: '#DADEE1', fontSize: 14 }
              },
              itemStyle: {
                color: 'transparent'
              },
              data: attaVal
            }
          ]
        }
      } else {
        config = {
          title: {
            text: '暂无数据',
            x: 'center',
            y: 'center',
            textStyle: {
              fontSize: 14,
              fontWeight: 'normal',
              color: '#999'
            }
          }
        }
      }
      if (data.length > 4) {
        data.dataZoom = [
          {
            yAxisIndex: [0],
            orient: 'vertical',
            show: true,
            type: 'slider',
            startValue: 100,
            // end: 100,
            width: 8,
            left: '99%',
            borderColor: 'rgba(43,48,67,.1)',
            fillerColor: '#6580b8',
            zoomLock: true,
            showDataShadow: false,
            backgroundColor: 'rgba(43,48,67,.1)',
            showDetail: false,
            // realtime: true,
            filterMode: 'filter',
            handleIcon: 'M-9.35,34.56V42m0-40V9.5m-2,0h4a2,2,0,0,1,2,2v21a2,2,0,0,1-2,2h-4a2,2,0,0,1-2-2v-21A2,2,0,0,1-11.35,9.5Z',
            handleStyle: {
              color: '#6580b8',
              borderColor: '#6580b8'
            },
            maxValueSpan: 4,
            minValueSpan: 4,
            brushSelect: false
          },
          {
            type: 'inside',
            show: false,
            yAxisIndex: [0],
            height: 8
          }
        ]
      }
      EChart.clear()
      // 设置参数
      EChart.setOption(config)
      // 先移除点击事件 解决点击事件重复绑定
      EChart.off('click')
      // 点击事件
      EChart.on('click', (params) => {
        const index = params.dataIndex
        // 新增变量 判断当前点击柱状图是选中还是取消选中
        let isSelected = false
        topName.map((e, i) => {
          // 选中的设置选中色
          if (i == index && e.textStyle.color != '#FFCA64FF') {
            e.textStyle.color = '#FFCA64FF'
            name = e.value
          } else {
            // 选中已选中的则为取消选中
            if (i == index && e.textStyle.color == '#FFCA64FF') {
              isSelected = true
            }
            // 其他的设为默认色
            e.textStyle.color = '#FFF'
          }
        })
        config.yAxis.data = JSON.parse(JSON.stringify(topName))
        EChart.setOption(config)
        // 取消选中 则恢复过滤条件
        this.selectSpaceId = ''
        this.pagination.pageNo = 1
        this.devicePagination.pageNo = 1
        // 取消选中 则恢复过滤条件
        if (!isSelected) {
          this.selectSpaceId = this.chartData[index].floorId || ''
        }
        this.getRridAreaList()
        this.getRridAreaDeviceList()
      })
    },
    // 获取重点区域列表
    getRridAreaList() {
      const params = {
        dateType: this.tagCurrent,
        beginDate: this.dateObj[this.tagCurrent][0],
        endDate: this.dateObj[this.tagCurrent][1],
        regionCode: this.selectSpaceId || this.ssmCodes.at(-1),
        pageNo: this.pagination.pageNo,
        pageSize: this.pagination.pageSize,
        model: 'moment'
      }
      this.keyAreasTableLoading = true
      GetRridAreaList(params).then((res) => {
        if (res.data.code === '200') {
          if (this.pagination.pageNo === 1) this.keyAreasList = []
          this.keyAreasList = this.keyAreasList.concat(res.data.data.list)
          this.pagination.total = Number(res.data.data.totalCount)
        }
      }).finally(() => {
        this.keyAreasTableLoading = false
      })
    },
    // 获取人流监测设备
    getRridAreaDeviceList() {
      const params = {
        dateType: this.tagCurrent,
        beginDate: this.dateObj[this.tagCurrent][0],
        endDate: this.dateObj[this.tagCurrent][1],
        regionCode: this.selectSpaceId || this.ssmCodes.at(-1),
        pageNo: this.devicePagination.pageNo,
        pageSize: this.devicePagination.pageSize
      }
      this.deviceTableLoading = true
      GetRridAreaDeviceList(params).then((res) => {
        if (res.data.code === '200') {
          if (this.devicePagination.pageNo === 1) this.deviceList = []
          this.deviceList = this.deviceList.concat(res.data.data.list)
          this.devicePagination.total = Number(res.data.data.totalCount)
        }
      }).finally(() => {
        this.deviceTableLoading = false
      })
    },
    keyAreasChange({key, data}) {
      if (key == 'abnormalCount') { // 设备状态
        this.getKeyAreasSpaceIdList(data, 'device')
      } else if (key == 'alarmCount') { // 区域报警数
        this.getKeyAreasSpaceIdList(data, 'alarm')
      }
    },
    tableRowClick(type, row, column) {
      if (type == 1) { // 重点区域
        console.log(column.property)
        if (column.property == 'areaName') { // 重点区域名称点击
          this.$emit('roomEvent', {
            type: 'move',
            assetId: row.id,
            assetName: row.areaName
          })
          try {
            window.chrome.webview.hostObjects.sync.bridge.FireProtectionZoneFireAreaId(JSON.stringify({
              keyAreasId: row.id,
              floorIds: row.floorIds
            }))
          } catch (error) {}
        } else if (column.property == 'throughCount') { // 人员流量
          this.isKeyAreasListDialog = true
          this.$refs.keyAreasListDialog.openDetailComponent({key: 'personnelFlowList', data: row})
        } else if (column.property == 'abnormalCount') { // 设备状态
          this.getKeyAreasSpaceIdList(row.id, 'device')
        } else if (column.property == 'alarmCount') { // 区域报警数
          this.getKeyAreasSpaceIdList(row.id, 'alarm')
        }
      } else { // 设备
        this.isDeviceDetails = true
        this.deviceId = row.assetId
      }
    },
    tableLoadMore() {
      if (this.pagination.total > this.pagination.pageNo * this.pagination.pageSize) {
        this.pagination.pageNo++
        this.getRridAreaList()
      }
    },
    deviceTableLoadMore() {
      if (this.devicePagination.total > this.devicePagination.pageNo * this.devicePagination.pageSize) {
        this.devicePagination.pageNo++
        this.getRridAreaDeviceList()
      }
    }
  }
}
</script>

<style lang="scss" scoped>
@import '../style/module.scss';
.statistics_top_new {
  height: 130px;
  display: flex;
  flex-wrap: wrap;
  .statistics_top_item {
    height: 50%;
    width: 50%;
    // padding: 16px 0;
    display: flex;
    justify-content: center;
    align-items: center;
    img {
      width: 50px;
      height: 50px;
    }
  }
  .statistics_top_content {
    .statistics_top_title {
      padding-left: 20px;
      font-size: 14px;
      font-family: HarmonyOS Sans SC-Regular, HarmonyOS Sans SC;
      font-weight: 400;
      color: #b0e3fa;
      margin-bottom: 6px;
    }
    .font_bg {
      width: 125px;
      height: 32px;
      background: url(./../../../assets/images/qhdsys/Group-new.png) no-repeat 100%;
      display: flex;
      align-items: center;
      justify-content: center;
      .color_font {
        font-size: 20px;
        font-family: HarmonyOS Sans SC-Bold, HarmonyOS Sans SC;
        font-weight: bold;
        color: #ffca64;
        margin-bottom: 3px;
      }
      .color_unit {
        margin-left: 3px;
        font-size: 14px;
        font-family: HarmonyOS Sans SC-Light, HarmonyOS Sans SC;
        font-weight: 300;
        color: rgba(255, 255, 255, 0.5);
      }
    }
  }
}
.keyAreasComponent {
  width: 100%;
  height: 100%;
  .table-icon {
    display: flex;
    align-items: center;
  }
  .table-icon img {
    width: 16px;
    margin-right: 3px;
  }
  .title-left {
    padding-left: 20px;
  }
  .title-detail {
    cursor: pointer;
    font-size: 14px;
    font-family: HarmonyOS Sans SC-Regular, HarmonyOS Sans SC;
    font-weight: 400;
    color: #8bddf5 !important;
    margin-right: 13px;
  }
  .icon-box {
    margin-right: 0;
    display: flex;
    align-items: center;
    position: relative;
  }
  ::v-deep .el-table {
    .el-table__body tr {
      cursor: pointer;
    }
  }
}
// 滚动条的宽度
::v-deep .el-table__body-wrapper::-webkit-scrollbar {
  width: 6px; // 横向滚动条
  height: 6px; // 纵向滚动条 必写
}

// 注意:hover
::v-deep .el-table__body-wrapper:hover::-webkit-scrollbar {
  width: 6px; // 横向滚动条
  height: 6px; // 纵向滚动条 必写
}
::v-deep .el-table__body-wrapper::-webkit-scrollbar-corner,
::v-deep .el-table__body-wrapper::-webkit-scrollbar-track {
  background-color: transparent;
}
.module-header {
  width: 100%;
  // padding-left: 50px;
  background: url('~@/assets/images/qhdsys/bg-bt.png') no-repeat;
}

.order-more {
  width: 24px;
  height: 24px;
  margin-right: 0 !important;
  margin-left: 8px;
  cursor: pointer;
}
</style>
