<template>
  <div v-if="ipasPointDetailShow">
    <ipasPointDetail ref="retrospect" :dialogShow="ipasPointDetailShow" :detailId="detailId" @retrospectCloseDialog="retrospectCloseDialog"> </ipasPointDetail>
  </div>
  <div v-else class="content">
    <div class="sham-content"></div>
    <div class="right-content" ref="rightContent">
      <div class="echarts-top">
        <div class="bg-title" style="padding: 0 1rem 0 3rem">
          <span>巡检任务分析</span><span class="center-legend"></span
          ><span class="right-search"
            ><span @click="activeTaskEvent('#taskDay', 'day')" id="taskDay">今日</span><i>|</i><span @click="activeTaskEvent('#taskWeek', 'week')" id="taskWeek">本周</span><i>|</i
            ><span id="taskMonth" @click="activeTaskEvent('#taskMonth', 'month')">本月</span></span
          >
        </div>
        <div class="bg-content" style="height: calc(100% - 2.5rem)">
          <div class="completion_rate">
            <div class="pop_tooltip" :style="{ 'margin-left': taskAnalysisData.percentage + '%' }">
              <div class="pop_box">
                <span>巡检完成率</span><span class="pop_text_num">{{ taskAnalysisData.percentage }}%</span>
              </div>
              <div class="pox_triangle"></div>
            </div>
            <div class="task-item-progress">
              <div class="task_circle" :style="{ left: 'calc(' + taskAnalysisData.percentage + '% - 4px)' }"></div>
              <el-progress :stroke-width="2" :percentage="taskAnalysisData.percentage" :show-text="false"></el-progress>
            </div>
          </div>
          <div class="completion_data">
            <div>
              应巡<span>{{ taskAnalysisData.sum }}</span>
            </div>
            <div>
              已巡<span>{{ taskAnalysisData.accomplishCount }}</span>
            </div>
          </div>
        </div>
      </div>
      <div class="echarts-center">
        <div class="bg-title" style="padding: 0 1rem 0 3rem">
          <span>部门任务分析</span><span class="center-legend"></span
          ><span class="right-search"
            ><span @click="activeDeptEvent('#deptDay', 'day')" id="deptDay">今日</span><i>|</i><span @click="activeDeptEvent('#deptWeek', 'week')" id="deptWeek">本周</span><i>|</i
            ><span id="deptMonth" @click="activeDeptEvent('#deptMonth', 'month')">本月</span></span
          >
        </div>
        <div class="bg-content" style="height: calc(100% - 3rem)">
          <el-table
            class="table-center-transfer"
            :data="tableData"
            height="100%"
            :cell-style="$tools.setCell(3)"
            :header-cell-style="$tools.setHeaderCell(3)"
            style="width: 100%"
            element-loading-background="rgba(0, 0, 0, 0.2)"
            v-loading="tableLoading"
            @row-click="goTaskList"
          >
            <el-table-column fixed prop="teamName" show-overflow-tooltip label="部门"></el-table-column>
            <el-table-column fixed prop="count" show-overflow-tooltip label="任务数"></el-table-column>
            <el-table-column fixed prop="hasCount" show-overflow-tooltip label="已巡"></el-table-column>
            <el-table-column fixed prop="percentage" show-overflow-tooltip label="完成率"></el-table-column>
          </el-table>
        </div>
      </div>
      <div class="echarts-bottom">
        <div class="bg-title" style="padding: 0 1rem 0 3rem">
          <span>巡检点分析</span><span class="center-legend"></span
          ><span class="right-search"
            ><span @click="activePointEvent('#pointDay', 'day')" id="pointDay">今日</span><i>|</i><span @click="activePointEvent('#pointWeek', 'week')" id="pointWeek">本周</span><i>|</i
            ><span id="pointMonth" @click="activePointEvent('#pointMonth', 'month')">本月</span></span
          >
        </div>
        <div class="bg-content" style="height: calc(100% - 2.5rem)">
          <div class="point_echarts">
            <div id="pointEcharts"></div>
            <span class="echarts_num">{{ pointAnalysisData.percentage }}</span>
          </div>
          <div class="point_center_text">巡检点完成率</div>
          <div class="point_num_box">
            <div>
              <span>应巡点总数</span>
              <span>{{ pointAnalysisData.sum }}</span>
            </div>
            <div>
              <span>异常点总数</span>
              <span style="color: #ff5454">{{ pointAnalysisData.abnormalCount }}</span>
            </div>
            <div>
              <span>漏巡点总数</span>
              <span>{{ pointAnalysisData.unfinishedCount }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>
    <!-- <el-button class="sino-button-sure" @click="workOrderListChange">showDialog</el-button> -->
    <template v-if="listShow">
      <taskList :dialogShow="listShow" :rowData="rowData" @closeListDialog="closeListDialog" @setTaskId="setTaskId"></taskList>
    </template>
    <template v-if="ipasPointRecordShow">
      <ipasPointRecord ref="ipasPointRecord" :dialogShow="ipasPointRecordShow" :location="location" :ssmType="ssmType" @configCloseDialog="closePointList"></ipasPointRecord>
    </template>
  </div>
</template>
<script>
import $ from 'jquery'
import * as echarts from 'echarts'
import { getTaskPointTimeDateList, getDepartmentTaskTimeDateList, getOnePersonTaskQuantity } from '@/utils/centerScreenApi'
import taskList from './component/taskListComponent.vue'
import ipasPointRecord from './component/ipasPointRecord.vue'
import ipasPointDetail from './component/ipasPointDetail.vue'
export default {
  name: 'businessIPAS',
  components: {
    taskList,
    ipasPointRecord,
    ipasPointDetail
  },
  data() {
    return {
      tableData: [],
      tableLoading: false,
      ratePopVisible: true,
      pointAnalysisData: {
        sum: 0,
        percentage: 0,
        abnormalCount: 0,
        unfinishedCount: 0
      },
      taskAnalysisData: {
        percentage: 0,
        sum: 0,
        accomplishCount: 0
      },
      timer: null,
      location: '',
      ssmType: '',
      listShow: false,
      rowData: '',
      dateType: 'day',
      ipasPointRecordShow: false,
      ipasPointDetailShow: false,
      detailId: ''
    }
  },
  mounted() {
    // 初始化 获取定位信息
    if (Object.hasOwn(this.$route.query, 'localtion') || Object.hasOwn(this.$route.query, 'ssmType')) {
      this.location = this.$route.query.localtion || ''
      this.ssmType = this.$route.query.ssmType || ''
    }
    try {
      window.chrome.webview.addEventListener('message', (event) => {
        const data = JSON.parse(event.data)
        this.location = data.localtion || ''
        this.ssmType = data.ssmType || ''
        if (data.type === 'area') {
          this.search()
        } else if (data.type === 'tag') {
          this.ipasPointRecordShow = true
          this.$nextTick(() => {
            this.$refs.ipasPointRecord.getPointDetailTableList()
          })
          window.chrome.webview.hostObjects.sync.bridge.ShowTable(true)
        }
      })
    } catch (errpr) {}
    this.search()
    this.timer = setInterval(() => {
      setTimeout(() => {
        this.search()
      }, 0)
    }, 1000 * timerNum)
    this.$once('hook:beforeDestroy', () => {
      clearInterval(this.timer)
      this.timer = null
    })
  },
  methods: {
    search() {
      this.activeTaskEvent('#taskDay', 'day')
      this.activeDeptEvent('#deptDay', 'day')
      this.activePointEvent('#pointDay', 'day')
    },
    toPoint(str) {
      var data = str.replace('%', '')
      return data / 100
    },
    getTaskAnalysisInfo(val) {
      getOnePersonTaskQuantity({ dateType: val, spaceCode: this.location, spaceLevel: this.ssmType }).then((res) => {
        const data = res.data
        if (data.code === '200') {
          this.taskAnalysisData = data.data.list[0]
        }
      })
    },
    // 巡检部门分析
    getDepartmentAnalysisInfo(val) {
      this.tableLoading = true
      getDepartmentTaskTimeDateList({ dateType: val, spaceCode: this.location, spaceLevel: this.ssmType }).then((res) => {
        const data = res.data
        this.tableLoading = false
        if (data.code === '200') {
          this.tableData = data.data.list
        }
      })
    },
    // 巡检点分析
    getPointAnalysisInfo(val) {
      getTaskPointTimeDateList({ dateType: val, spaceCode: this.location, spaceLevel: this.ssmType }).then((res) => {
        const data = res.data
        if (data.code === '200') {
          this.pointAnalysisData = data.data.list[0]
          this.$nextTick(() => {
            this.getPointAnalyasisEcharts(this.pointAnalysisData.percentage || 0)
          })
        }
      })
    },
    // 巡检点分析Echarts
    getPointAnalyasisEcharts(data) {
      const getchart = echarts.init(document.getElementById('pointEcharts'))
      getchart.resize()
      const placeHolderStyle = {
        normal: {
          label: {
            show: false
          },
          labelLine: {
            show: false
          },
          color: 'rgba(0,0,0,0)',
          borderWidth: 0
        },
        emphasis: {
          color: 'rgba(0,0,0,0)',
          borderWidth: 0
        }
      }
      const option = {
        backgroundColor: '',
        // 第一个图表
        series: [
          {
            type: 'pie',
            hoverAnimation: false, // 鼠标经过的特效
            radius: ['130%', '155%'],
            center: ['50%', '95%'],
            startAngle: 180,
            labelLine: {
              normal: {
                show: false
              }
            },
            data: [
              {
                name: '已完成',
                value: data,
                itemStyle: {
                  normal: {
                    color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                      {
                        offset: 0,
                        color: '#FFE3A6'
                      },
                      {
                        offset: 1,
                        color: '#FFA75F'
                      }
                    ])
                  }
                },
                label: {
                  normal: {
                    show: false
                  }
                }
              },
              {
                value: 100 - data,
                itemStyle: {
                  normal: {
                    color: 'rgba(126, 174, 249, .2)'
                  }
                }
              },
              {
                name: '总数',
                value: 100,
                itemStyle: placeHolderStyle
              }
            ]
          }
        ]
      }
      getchart.setOption(option)
      // 随着屏幕大小调节图表
      window.addEventListener('resize', () => {
        getchart.resize()
      })
    },
    activeTaskEvent(type, val) {
      $('#taskDay').removeClass('active')
      $('#taskWeek').removeClass('active')
      $('#taskMonth').removeClass('active')
      $(type).addClass('active')
      this.getTaskAnalysisInfo(val)
    },
    activeDeptEvent(type, val) {
      this.dateType = val
      $('#deptDay').removeClass('active')
      $('#deptWeek').removeClass('active')
      $('#deptMonth').removeClass('active')
      $(type).addClass('active')
      this.getDepartmentAnalysisInfo(val)
    },
    activePointEvent(type, val) {
      $('#pointDay').removeClass('active')
      $('#pointWeek').removeClass('active')
      $('#pointMonth').removeClass('active')
      $(type).addClass('active')
      this.getPointAnalysisInfo(val)
    },
    goTaskList(row, column, event) {
      const item = {
        deptId: row.id,
        dateType: this.dateType
      }
      this.rowData = item
      this.listShow = true
      try {
        window.chrome.webview.hostObjects.sync.bridge.ShowTable(this.listShow)
      } catch (error) {}
    },
    closeListDialog() {
      this.listShow = false
      try {
        window.chrome.webview.hostObjects.sync.bridge.ShowTable(this.listShow)
      } catch (error) {}
    },
    setTaskId(id) {
      this.listShow = false
      this.ipasPointRecordShow = true
      this.$nextTick(() => {
        this.$refs.ipasPointRecord.getPointDetailTableList('taskList', id)
      })
      try {
        // window.chrome.webview.hostObjects.sync.bridge.ShowTable(this.listShow)
        window.chrome.webview.hostObjects.sync.bridge.ShowTable(true)
      } catch (error) {}
    },
    closePointList() {
      this.ipasPointRecordShow = false
      try {
        window.chrome.webview.hostObjects.sync.bridge.ShowTable(false)
      } catch (error) {}
    },
    retrospectCloseDialog() {
      this.ipasPointDetailShow = false
      this.ipasPointRecordShow = false
      try {
        window.chrome.webview.hostObjects.sync.bridge.ShowTable(false)
        window.chrome.webview.hostObjects.sync.bridge.ShowDialog(false)
      } catch (error) {}
    },
    workOrderListChange() {
      this.ipasPointRecordShow = true
      this.$nextTick(() => {
        this.$refs.ipasPointRecord.getPointDetailTableList()
      })
      window.chrome.webview.hostObjects.sync.bridge.ShowTable(true)
    }
  }
}
</script>
<style lang="scss" scoped>
.content {
  width: 100%;
  height: 100%;
  position: relative;
  .active {
    background: url('~@/assets/images/center/light-yellow.png') no-repeat;
    background-size: 100% 100%;
    color: #ffe3a6 !important;
    outline: none;
  }
  .sham-content {
    pointer-events: none;
  }
  .right-content {
    position: absolute;
    top: 0;
    right: 0;
    width: 20%;
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    transition: width 0.3s;
    overflow: hidden;
    .echarts-top {
      width: 100%;
      height: 30%;
      background: url('~@/assets/images/peace/emergency-disposal-left.png') no-repeat;
      background-size: 100% 100%;
      box-sizing: border-box;
      .completion_rate {
        height: 65%;
        width: 80%;
        margin-left: 10%;
        display: flex;
        flex-direction: column;
        justify-content: space-evenly;
        .pop_tooltip {
          width: fit-content;
          height: 43px;
          background: #242a4f;
          border: 1px solid #69676b;
          padding: 4px 7px;
          margin-left: 45%;
          transform: translate(-50%);
          position: relative;
          .pop_box {
            display: flex;
            flex-direction: column;
            justify-content: space-around;
            span {
              height: 20px;
              line-height: 20px;
              color: #ffffff;
              text-align: center;
              font-size: 12px;
              font-family: PingFang-SC-Medium, PingFang-SC;
            }
            .pop_text_num {
              height: 23px;
              line-height: 23px;
              font-size: 14px;
              color: #ffe3a6;
            }
          }
          &::before {
            box-sizing: content-box;
            position: absolute;
            bottom: -12px;
            left: calc(50% - 3px);
            border-bottom: 6px solid transparent;
            border-top: 6px solid #242a4f;
            border-left: 6px solid transparent;
            border-right: 6px solid transparent;
            display: block;
            content: '';
            z-index: 2;
          }
          &::after {
            box-sizing: content-box;
            position: absolute;
            bottom: -13px;
            left: calc(50% - 3px);
            border-bottom: 6px solid transparent;
            border-top: 6px solid #69676b;
            border-left: 6px solid transparent;
            border-right: 6px solid transparent;
            display: block;
            content: '';
            z-index: 1;
          }
        }
        .task-item-progress {
          width: 100%;
          display: inline-block;
          // margin-left: 10%;
          position: relative;
          .task_circle {
            position: absolute;
            width: 7px;
            height: 7px;
            border-radius: 50%;
            top: -4px;
            // left: calc(45% - 4px);
            z-index: 2;
            background: linear-gradient(90deg, #ffe3a6 0%, #ffbb73 100%);
            border: 2px solid #051341;
            box-shadow: 0 0 0 3px rgba(255, 227, 166, 0.5);
          }
          // margin: auto;
          ::v-deep .el-progress-bar {
            .el-progress-bar__outer {
              border-radius: 0;
              overflow: visible;
              background: rgba(255, 255, 255, 0.17);
              .el-progress-bar__inner {
                border-radius: 0;
                height: 4px;
                top: -1px;
                background: linear-gradient(90deg, #ffe3a6 0%, #ffbb73 100%);
              }
            }
          }
        }
      }
      .completion_data {
        height: 35%;
        display: flex;
        justify-content: space-around;
        > div {
          margin: auto;
          color: #ffffff;
          font-size: 14px;
          font-family: PingFang-SC-Medium, PingFang-SC;
          vertical-align: middle;
          span {
            font-size: 22px;
            font-family: DIN-Medium, DIN;
            color: #ffe3a6;
            vertical-align: text-bottom;
            margin-left: 1rem;
          }
        }
      }
    }
    .echarts-center {
      width: 100%;
      height: 37%;
      background: url('~@/assets/images/center/bg-37.png') no-repeat;
      background-size: 100% 100%;
      padding: 2px;
      box-sizing: border-box;
      ::v-deep .el-table {
        border: none !important;
        .el-table__header .el-table__cell {
          padding: 5px 0 !important;
          color: #fff;
        }
        .el-table__body {
          tr {
            background: center;
          }
          td.el-table__cell,
          th.el-table__cell.is-leaf {
            border-right: 2px solid #0a164e;
            border-bottom: 2px solid #0a164e;
            background: rgba(56, 103, 180, 0.2);
            color: #fff;
          }
        }
      }
    }
    .echarts-bottom {
      width: 100%;
      height: 33%;
      background: url('~@/assets/images/center/bg-32.png') no-repeat;
      background-size: 100% 100%;
      .bg-content {
        display: flex;
        flex-direction: column;
        justify-content: space-evenly;
        .point_echarts {
          height: 6.5rem;
          width: 13rem;
          margin-left: calc(50% - 6.5rem);
          background: url('~@/assets/images/center/sector.png') no-repeat;
          background-size: 100% 100%;
          position: relative;
          .echarts_num {
            position: absolute;
            bottom: 0;
            left: 50%;
            transform: translate(-50%, -50%);
            font-size: 1.375rem;
            font-family: DIN-Medium, DIN;
            color: #ffe3a6;
            z-index: 3;
          }
        }
        .point_center_text {
          text-align: center;
          font-size: 14px;
          font-family: PingFang-SC-Medium, PingFang-SC;
          color: #ffffff;
        }
        .point_num_box {
          display: flex;
          justify-content: space-around;
          width: 100%;
          height: 30%;
          > div {
            width: 30%;
            height: 100%;
            background: url('~@/assets/images/center/point-group.png') no-repeat;
            background-size: 100% 100%;
            display: flex;
            flex-direction: column;
            justify-content: space-evenly;
            span {
              text-align: center;
              font-size: 12px;
              font-family: PingFang-SC-Medium, PingFang-SC;
              color: #dceaff;
            }
            span:last-child {
              font-size: 16px;
              font-family: DIN-Bold, DIN;
              font-weight: bold;
              color: #ffffff;
            }
          }
        }
      }
    }
  }
  .bg-title {
    margin-top: 5px;
    // height: 23%;
    height: 2.5rem;
    line-height: 2.5rem;
    color: #d4e3f9;
    padding: 0 5rem;
    font-family: TRENDS;
    /*设置为伸缩容器*/
    display: -webkit-box;
    display: -moz-box;
    display: -ms-flexbox;
    display: -webkit-flex;
    display: flex;
    /*垂直居中*/
    -webkit-box-align: center; /*旧版本*/
    -moz-box-align: center; /*旧版本*/
    -ms-flex-align: center; /*混合版本*/
    -webkit-align-items: center; /*新版本*/
    align-items: center; /*新版本*/
    justify-content: space-between;
    .right-search {
      span {
        display: inline-block;
        width: 35px;
        height: 30px;
        font-size: 14px;
        text-align: center;
        line-height: 26px;
        margin: 0 4px;
        color: #7eaef9;
        cursor: pointer;
      }
      span:hover {
        background: url('~@/assets/images/center/light-yellow.png') no-repeat;
        background-size: 100% 100%;
        color: #ffe3a6;
      }
      // span:focus {
      //   background: url('~@/assets/images/center/light-yellow.png') no-repeat;
      //   background-size: 100% 100%;
      //   color: #ffe3a6;
      //   outline: none;
      // }
      i {
        color: #7eaef9;
      }
    }
  }
  .bg-content {
    position: relative;
    box-sizing: border-box;
    padding: 10px 10px 15px 10px;
    width: 100%;
    height: 77%;
    #pointEcharts {
      width: 100%;
      height: 100%;
      // background: red;
    }
  }
}
</style>
