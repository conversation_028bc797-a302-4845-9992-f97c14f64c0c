@import './vue2-org-tree.less';

.org-tree {
    .org-tree-node {
        text-align: center;
        .org-tree-node-children{
            margin: 0 auto;
        }
    }
    .bg-none {
        padding: 0px !important;
        border-radius: 8px !important;
    }

    .tree-item {
        transition: all .3s;
        background: rgba(255, 255, 255, 0.05);
        border: 2px solid rgba(255, 255, 255, 0.05);
        border-radius: 8px;
        padding: 14px;
        position: relative;
        text-align: initial;

        .item-title {
            padding: 3px 0px 3px 11px;
            font-weight: 500;
            font-size: 16px;
            color: #FFFFFF;
            line-height: 19px;
            position: relative;
            padding-left: 11px;

            &::before {
                content: '';
                position: absolute;
                left: 0;
                top: 50%;
                transform: translateY(-50%);
                display: inline-block;
                width: 3px;
                height: 16px;
                background: #BBBBBB;
                border-radius: 999px;
            }
        }

        .item-step {
            padding: 3px 8px;
            font-weight: 400;
            font-size: 12px;
            color: #06102C;
            line-height: 14px;
            position: absolute;
            top: 17px;
            right: 2px;
            background: #B0E3FA;
            border-radius: 999px 0px 0px 999px;
        }

        .el-icon-warning {
            position: absolute;
            top: 17px;
            right: 57px;
            font-size: 20px;
            color: #FF7D00;
        }

        .item-tips {
            margin-top: 10px;
            font-size: 14px;
            color: #FFFFFF;
            line-height: 16px;

            span {
                font-size: 12px;
                color: #C2C4C8;
                line-height: 14px;
                margin-left: 10px;
            }
        }

        .item-main {
            min-width: 164px;
            background: rgba(53, 98, 219, 0.06);
            border-radius: 4px;
            padding: 16px;
            margin-top: 10px;
        }
    }

    .alarm-item {
        background: rgba(53, 98, 219, 0.2);
        border: 2px solid rgba(53, 98, 219, 0.2);

        .item-title {
            &::before {
                background: #8BDDF5;
            }
        }

        .alarm-main {
            width: 328px;
            margin-top: 8px !important;

            .alarm-tyle {
                line-height: 16px;
                font-size: 14px;
                color: #FFCA64;
                display: flex;
                align-items: center;
                margin-bottom: 6px;

                img {
                    width: 14px;
                    height: 13px;
                    margin-right: 8px;
                }
            }

            .alarm-info {
                margin-top: 2px;
                font-size: 12px;
                line-height: 21px;

                .alarm-label {
                    color: #B0E3FA;
                }

                .alarm-value {
                    color: #FFFFFF;
                }

                .alarmLevel {
                    color: #CB2634;
                    padding: 3px 8px;
                    background: #FFECE8;
                    border-radius: 4px;
                }
            }

            .alarm-btns {
                display: flex;
                align-items: center;
                justify-content: flex-end;

                button {
                    min-width: 48px;
                    height: 24px;
                    padding: 6px 11px;
                    font-size: 12px;
                    color: #A4AFC1;
                    background: url('../../../../assets/images/plan/btn_bgd1.png') no-repeat center / 100%;
                }

                button:first-child {
                    background: url('../../../../assets/images/plan/btn_bgd2.png') no-repeat center / 100%;
                }
            }
        }
    }

    .end-main {
        .end-label {
            font-weight: 400;
            font-size: 14px;
            color: #FFFFFF;
            line-height: 16px;
        }

        .end-value {
            margin-top: 10px;
            font-weight: 300;
            font-size: 14px;
            color: #C2C4C8;
            line-height: 21px;
        }
    }

    .start-item {
        border: 2px solid rgba(53, 98, 219, 0.2);
        background: rgba(53, 98, 219, 0.2);

        .item-title {
            &::before {
                background: #8BDDF5;
            }
        }

        .start-main {
            position: relative;

            .start-label {
                font-weight: 400;
                font-size: 14px;
                color: #FFFFFF;
                line-height: 16px;
            }

            .start-value {
                margin-top: 10px;
                font-weight: 300;
                font-size: 14px;
                color: #C2C4C8;
                line-height: 21px;
            }

            .start-main-item {
                display: flex;
                flex-direction: column;

                .start-main-item-title {
                    flex: 1;
                    font-weight: 400;
                    font-size: 14px;
                    color: #FFCA64;
                    line-height: 16px;
                    overflow: hidden;
                    text-overflow: ellipsis;
                    white-space: nowrap;
                    max-width: 220px;
                }

                .start-main-item-value {
                    flex: 1;
                    font-weight: 300;
                    font-size: 14px;
                    color: #FFFFFF;
                    line-height: 14px;
                    padding: 9px 0px 11px 0px;
                    border-bottom: 1px solid rgba(133, 145, 206, 0.5);
                    display: flex;
                    max-width: 220px;

                    span {
                        flex: 1;
                        overflow: hidden;
                        text-overflow: ellipsis;
                        white-space: nowrap;
                    }

                    .el-icon-arrow-right {
                        color: #8BDDF5;
                    }
                }

                & :last-child {
                    border-bottom: none;
                }
            }
        }
    }

    .start-item-hover:hover {
        // background: #FFE4BA;
        border: 2px solid #FFCA64;
    }

    .current-step {
        // background: #FFE4BA;
        border: 2px solid #FFCA64;
        &::after {
            content: '';
            position: absolute;
            width: 20px;
            height: 20px;
            right: 55px;
            top: 17px;
            background: url('../../../../assets/images/plan/finishIcon.png') no-repeat center / 100%;
        }
    }
}