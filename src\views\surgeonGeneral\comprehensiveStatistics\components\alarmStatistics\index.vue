<template>
  <div class="echarts-topRight">
    <BgTitle showMore @moreClick="showListDetail()">
      <template #title>报警统计</template>
      <template #right>
        <el-date-picker
          v-if="dateType === 'custom'"
          v-model="date"
          class="datePickerInput"
          popper-class="date-style"
          type="daterange"
          value-format="yyyy-MM-dd"
          :clearable="false"
        >
        </el-date-picker>
        <el-dropdown trigger="click" @command="dataTypeCommand">
          <span class="el-dropdown-link dropdown-title">
            {{ dataTypeList.find((v) => v.value == dateType)?.name ?? "" }}
            <i class="el-icon-caret-bottom"></i>
          </span>
          <el-dropdown-menu slot="dropdown">
            <el-dropdown-item
              v-for="item in dataTypeList"
              :key="item.value"
              :command="item.value"
              :class="{ isBjxl: dateType == item.value }"
            >
              {{ item.name }}
            </el-dropdown-item>
          </el-dropdown-menu>
        </el-dropdown>
      </template>
    </BgTitle>
    <div class="bg-content">
      <TopContent :date="date" :dateType="dateType" @itemClick="showListDetail"/>
      <BottomContent :date="date" :dateType="dateType" @itemClick="showListDetail"/>
    </div>
    <AlarmTable v-if="listVisible" :visible="listVisible" :params="detailParams" @showDetail="showDetail" @close="closeListVisible"/>
    <el-dialog
      v-dialogDrag
      :modal="false"
      :visible="detailVisible"
      custom-class="mainDialog main"
      append-to-body
      :close-on-click-modal="false"
      :before-close="closeDetail"
      class="all-table-componentList"
    >
      <EmergencyDetail
        v-if="detailVisible"
        :alarmId="currentAlarmId"
        :isView="false"
      >
      </EmergencyDetail>
    </el-dialog>
  </div>
</template>
<script>
import BgTitle from '../../components/common/bgTitle'
import TopContent from './topContent'
import BottomContent from './bottomContent'
import AlarmTable from './detail.vue'
import EmergencyDetail from '@/views/normalMode/leftScreen/EmergencyDisposalNew/components/EmergencyDetail.vue'
export default {
  components: {
    BgTitle,
    TopContent,
    BottomContent,
    AlarmTable,
    EmergencyDetail
  },
  data() {
    return {
      date: [],
      dateType: 'day',
      dataTypeList: [
        { value: 'day', name: '今日' },
        { value: 'week', name: '本周' },
        { value: 'month', name: '本月' },
        { value: 'year', name: '本年' },
        { value: 'custom', name: '自定义' }
      ],
      listVisible: false,
      detailParams: {
        dateType: this.dateType,
        data: {}
      },
      detailVisible: false,
      currentAlarmId: ''
    }
  },
  mounted() {},
  methods: {

    // 时间类型切换
    dataTypeCommand(val) {
      this.dateType = val
    },
    // 打开列表
    showListDetail (data) {
      this.detailParams = {
        dateType: this.dateType,
        ...data
      }
      this.listVisible = true

    },
    closeListVisible () {
      this.listVisible = false

    },
    showDetail (row) {
      this.currentAlarmId = row.alarmId
      this.detailVisible = true
    },
    closeDetail () {
      this.detailVisible = false
    }
  }
}
</script>
<style lang="scss" scoped>
.echarts-topRight {
  height: 100%;
  width: calc(33% - 8px);
  background: url("~@/assets/images/bg-content1.png") no-repeat;
  background-size: 100% 100%;
  // padding: 4px;
  box-sizing: border-box;
  .bg-content {
    position: relative;
    box-sizing: border-box;
    padding: 10px;
    width: 100%;
    height: calc(100% - 44px);
    display: flex;
    flex-wrap: wrap;
  }
  .dropdown-title{
    color: #fff;
    cursor: pointer;
  }
}
.statistics_item {
  width: calc(100% - 4px);
  height: calc(50% - 4px);
  // border: 1px solid #fff;
  margin-right: 8px;
  &:nth-child(even) {
    margin-right: 0;
  }
}
:deep(.mainDialog) {
  width: 80%;
  height: 84vh;
  margin-top: 6vh !important;
  // border: 1px solid #5996f9;
  background-color: transparent !important;
  pointer-events: auto;
  background-image: url("@/assets/images/table-bg.png");
  background-repeat: no-repeat;
  background-size: 100% 100%;
  box-shadow: none;
  .el-dialog__headerbtn {
    transform: translateX(-36px);
    width: 25px;
    height: 25px;
    background-image: url("@/assets/images/close.png");
    background-repeat: no-repeat;
    background-size: 100% 100%;
    .el-dialog__close::before {
      display: none;
    }
  }
  .dialog-title {
    display: inline-block;
    width: 100%;
    text-align: center;
    transform: translateY(-6px);
    color: #cbdeed;
  }
  .dialog-title::before {
    display: none;
  }

  .el-dialog__header {
    background-color: transparent;
  }
  .el-dialog__body {
    padding: 0;
    height: calc(100% - 60px);
    max-height: fit-content;
    overflow-y: hidden;
    background-color: transparent;
    padding: 0px 80px;
    .dialog-content {
      width: 100%;
      height: 100%;
      background-color: transparent;

      .el-table {
        background-color: transparent;
        border: 1px solid #202d4c;
        .el-table__header-wrapper {
          background-color: transparent !important;
        }
        .el-table__body-wrapper {
          background-color: transparent !important;
        }
        tr {
          background-color: transparent !important;
        }
        td {
          background-color: transparent !important;
        }
      }
    }
  }
}
:deep(.datePickerInput) {
  height: 34px;
  width: 250px;
  box-sizing: content-box;
  background: none;
  border: none;
  .el-input__inner {
    border: none;
  }
  .el-input__prefix {
    display: none;
  }
  .el-range-separator{
    color: #fff;

  }
  .el-range-input{
    background: none;
    color: #fff
  }
}
</style>
