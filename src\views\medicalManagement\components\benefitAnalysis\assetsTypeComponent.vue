<template>
  <div class="box-view">
    <div class="box-left">
      <div class="box-left-top">
        <div class="item-box-left">
          <div class="grossIncome">
            <div class="grossIncome-title">
              总收入
            </div>
            <div class="grossIncome-box">
              <div class="grossIncome-content" v-for="(item,index) in statisticalData" :key="index">
                <div class="grossIncome-content-left">
                  <p>
                    <span class="item-block" :style="{ background: item.background }"></span><span
                      class="item-label">{{item.name}}</span>
                  </p>
                  <p>
                    <span class="item-value">{{item.value}}</span> <span class="item-unit">{{item.unit}}</span>
                  </p>
                </div>
                <div class="grossIncome-content-right">
                  <p class="item-label">同比去年</p>
                  <p>
                    <img :src="item.img" alt="">
                    <span class="item-rate" :style="{ color: item.color }">12.3%</span>
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="item-box-right">
          <div id="rateDistributionEcharts" style="width: 100%; height: 100%"></div>
        </div>
      </div>
      <div class="box-left-bottom">
        <div class="title">
          收支明细/元
        </div>
        <div class="legend">
          <div v-for="(item,index) in legendData" :key="index" class="legendBox">
            <span :style="{ background: item.background }"></span>
            <span>{{item.label}}</span>
          </div>
        </div>
        <div class="item-3column-box">
          <div v-for="(item,index) in pieIdData" :key="index" class="column-box">
            <div :id="pieIdData[index]" style="width: 100%; height: 100%"></div>
          </div>
        </div>
      </div>
    </div>
    <div class="box-right">
      <div class="box-right-top">
        <div id="incomeAndExpendTrendEcharts" style="width: 100%; height: 100%"></div>
      </div>
      <div class="box-right-center">
        <div class="item-box-left">
          <div id="check_personTimeEcharts" style="width: 100%; height: 100%"></div>
        </div>
        <div class="item-box-right">
          <div id="repair_conditionEcharts" style="width: 100%; height: 100%"></div>
        </div>
      </div>
      <div class="box-right-bottom">
        <div class="item-box-left">
          <div id="timeDivision_personTimeEcharts" style="width: 100%; height: 100%"></div>
        </div>
        <div class="item-box-right">
          <div id="use_conditionEcharts" style="width: 100%; height: 100%"></div>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import * as echarts from 'echarts'
import "echarts-liquidfill/src/liquidFill.js"; // 在需要水滴图的页面js引入
import downImg from '@/assets/images/qhdsys/down.png'
import upImg from '@/assets/images/qhdsys/up.png'
import flatImg from '@/assets/images/flat.png'

export default {
  name: 'singleDeviceComponent',
  data() {
    return {
      pieIdData: [
        "pie_examine",
        "pie_maintain",
        "pie_maintenance",
        "pie_consumable",
        "pie_electricCharge",
      ],
      legendData: [
        {
          background: '#8BDDF5',
          label: '2LB1CT4'
        },
        {
          background: '#FFCA64',
          label: '门诊楼B1CT1'
        },
        {
          background: '#61E29D',
          label: '2LB1CT41'
        }
      ],
      statisticalData: [
        {
          name: '总收入',
          value: '123,330.120',
          unit: '元',
          background: '#8BDDF5',
          rate: '12.3%',
          color: '#FF2D55',
          img: upImg
        },
        {
          name: '总支出',
          value: '330.120',
          unit: '元',
          background: '#3CC1FF',
          rate: '12.3%',
          color: '#61E29D',
          img: downImg
        },
        {
          name: '总利润',
          value: '123,000.000',
          unit: '元',
          background: '#0A84FF',
          rate: '12.3%',
          color: '#D6EFF1',
          img: flatImg
        }
      ]
    };
  },
  // 监听全屏缩放 动态改变echarts图表大小
  watch: {
    '$store.state.dialogFullScreenState': {
      handler(val) {
        this.$nextTick(() => {
          const echartsDom = ['use_conditionEcharts', 'timeDivision_personTimeEcharts', 'repair_conditionEcharts', 'check_personTimeEcharts',
            'incomeAndExpendTrendEcharts', 'rateDistributionEcharts', ...this.pieIdData]
          setTimeout(() => {
            echartsDom.forEach((item) => {
              if (document.getElementById(item)) {
                echarts.init(document.getElementById(item)).resize()
              }
            })
          }, 200)
        })
      },
      deep: true
    }
  },
  mounted() {
    this.init()
  },
  methods: {
    //初始化
    init() {
      this.getLeftTop_right()
      this.getLeftBottom()
      this.getRightTop()
      this.getRightCenter_left()
      this.getRightCenter_right()
      this.getRightBottom_left()
      this.getRightBottom_right()
    },
    getLeftBottom() {
      this.pieIdData.forEach((item) => {
        this.initPie(item);
      });
    },
    initPie(item) {
      let _this = this;
      var chartDom = document.getElementById(item);
      var myChart = echarts.init(chartDom);
      var option;
      let dataObj = {
        pie_examine: [
          { value: 111, name: "收入1", color: "#8BDDF5" },
          { value: 125, name: "收入2", color: "#FFCA64" },
          { value: 312, name: "收入3", color: "#61E29D" }
        ],
        pie_maintain: [
          { value: 111, name: "支出1", color: "#8BDDF5" },
          { value: 125, name: "支出2", color: "#FFCA64" },
          { value: 312, name: "支出3", color: "#61E29D" }
        ],
        pie_maintenance: [
          { value: 111, name: "支出1", color: "#8BDDF5" },
          { value: 125, name: "支出2", color: "#FFCA64" },
          { value: 312, name: "支出3", color: "#61E29D" }
        ],
        pie_consumable: [
          { value: 111, name: "支出1", color: "#8BDDF5" },
          { value: 125, name: "支出2", color: "#FFCA64" },
          { value: 312, name: "支出3", color: "#61E29D" }
        ],
        pie_electricCharge: [
          { value: 111, name: "支出1", color: "#8BDDF5" },
          { value: 125, name: "支出2", color: "#FFCA64" },
          { value: 312, name: "支出3", color: "#61E29D" }
        ],
      };
      let titleObj = {
        pie_examine: { name: "检查收入", value: "" },
        pie_maintain: { name: "维修支出", value: "" },
        pie_maintenance: { name: "维保支出", value: "129" },
        pie_consumable: { name: "耗材支出", value: "", },
        pie_electricCharge: { name: "电费支出", value: "", },
      };
      option = {
        color: dataObj[item].map((item) => item.color),
        title:
        {
          text: titleObj[item].name ? titleObj[item].name : "{img|}",
          top: '2%',
          left: '2%',
          textStyle: {
            color: "#FFFFFF",
            fontSize: 14,
            fontWeight: "500",
          },
        },
        tooltip: {
          trigger: "item",
          formatter: function (params) {
            let marker = params.marker || "",
              name = params.name || "",
              value = params.value || "",
              percent = params.percent || "0";
            let spanVal = `<span style="padding: 0px 10px 0 5px;font-weight: bolder;color: #333;">${value}</span>`;
            return `${marker}${name}${spanVal}${percent}%`;
          },
        },
        legend: {
          orient: 'vertical',
          top: "center",
          right: "10%",
          textStyle: {
            color: "#fff",
            fontSize: 12,
          },
          itemWidth: 8,
          itemHeight: 8,
          itemGap: 5,
          selectedMode: false,
        },
        series: [
          {
            name: "",
            type: "pie",
            center: ["30%", "60%"],
            radius: ["40%", "60%"],
            avoidLabelOverlap: false,
            label: {
              show: false,
              position: "center",
            },
            emphasis: {
              label: {
                show: false,
                fontWeight: "bold",
              },
            },
            labelLine: {
              show: false,
            },
            data: dataObj[item],
          },
          {
            type: "gauge",
            center: ["30%", "60%"], // 饼状图的位置
            radius: "70%",
            // 如何是版本是4 这里是359.999；不能是360；否则圆环不能正常显示
            // 如果是版本是5，这里可以是360
            startAngle: 360,
            endAngle: 0,
            splitNumber: 25,
            zlevel: 10,
            axisLine: {
              show: false,
            },
            splitLine: {
              show: true, // 是否显示分隔线。 如果设置为true,外层虚线才能看见
              length: 1, // 分隔线与轴线的距离。这里表现是虚线的宽度
              lineStyle: {
                width: 5, // 分隔线线长。支持相对半径的百分比。
                color: "rgba(230,247,255,0.2)", // 线的颜色
              },
            },
            axisLabel: {
              //刻度标签
              show: false,
            },
            axisTick: {
              //刻度样式
              show: false,
            },
            detail: {
              show: false,
            },
            //仪表盘指针。
            pointer: {
              // 不显示仪表盘中的指针
              show: false,
            },
            data: [],
          },
        ],
      };
      myChart.resize();
      myChart.clear();
      option && myChart.setOption(option);
      window.addEventListener("resize", () => {
        myChart.resize();
      });
    },
    //近5年费率分布/%
    getLeftTop_right() {
      const getchart = echarts.init(document.getElementById('rateDistributionEcharts'))
      let xData = ['2020', '2021', '2022', '2023', '2024']
      let yData = [30, 22, 45, 29, 11]
      let y1Data = [26, 12, 53, 34, 43]
      let y2Data = [34, 32, 12, 45, 34]
      const option = {
        title: {
          text: '近5年费率分布/%',
          top: '2%',
          left: '2%',
          textStyle: {
            fontSize: "16px",
            color: '#ffffff',
            fontWeight: "500"
          },
        },
        tooltip: {
          trigger: 'axis',
          backgroundColor: '#0D2169',
          borderColor: '#727382',
          textStyle: {
            color: '#fff'
          },
          axisPointer: {
            type: "shadow",
          },
        },
        legend: {
          top: '15%',
          left: "center",
          itemHeight: 8,
          itemWidth: 8,
          textStyle: {
            "color": "#fff"
          }
        },
        grid: {
          left: '4%',
          right: '8%',
          bottom: '10%',
          top: '32%',
          containLabel: true
        },
        xAxis: {
          data: xData,
          axisLabel: {
            show: true,
            color: "#ffffff",
            interval: 0,
            textStyle: {
              color: 'rgba(255,255,255,0.6)',
              fontSize: 10,
              fontWeight: 500,
            }
          },
          axisLine: {
            lineStyle: {
              color: "rgba(255,255,255,0.2)",
            },
          }
        },
        yAxis: [{
          name: '',
          type: 'value',
          lineStyle: {
            show: "none",
          },
          axisTick: {
            show: false,
          },
          axisLabel: {
            show: true,
            color: "rgba(255,255,255,0.6)",
          },
          splitLine: {
            show: true,
            lineStyle: {
              color: "rgba(255,255,255,0.2)",
              type: 'dashed'
            },
          }
        }, {
          type: 'value',
          lineStyle: {
            show: "none",
          },
          axisTick: {
            show: false,
          },
          axisLine: {
            show: false,
          },
          axisLabel: {
            show: false,
          },
          splitLine: {
            show: false,
          }
        }],
        series: [
          {
            name: "利润率",
            type: "bar",
            barWidth: 10,
            itemStyle: {
              normal: {
                color: "#8BDDF5",
              }
            },
            data: yData
          },
          {
            name: "投资收益率",
            type: "bar",
            barWidth: 10,
            itemStyle: {
              normal: {
                color: "#3CC1FF",
              }
            },
            data: y1Data
          },
          {
            name: "维系费用率",
            type: "bar",
            barWidth: 10,
            itemStyle: {
              normal: {
                color: "#0A84FF",
              }
            },
            data: y2Data
          },
        ]
      }
      getchart.clear()
      getchart.setOption(option)
      // 随着屏幕大小调节图表
      window.addEventListener('resize', () => {
        getchart.resize()
      })
    },
    // 收支趋势/元
    getRightTop() {
      const getchart = echarts.init(document.getElementById('incomeAndExpendTrendEcharts'))
      var borderColor = ['#FFCA64', '#8BDDF5', '#61E29D']
      let dateArr = ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月']
      let echartsData = [
        {
          name: '总收入',
          value: [1400.5000, 4500, 2300, 3600, 7800, 4500, 5050, 6500, 4300, 4500, 7800, 7900]
        }, {
          name: '支出',
          value: [5400.2000, 3500, 4300, 5600, 5800, 7500, 6500, 5500, 6300, 3500, 4800, 6900]
        }, {
          name: '利润',
          value: [1400.3000, 2500, 5300, 7600, 4800, 5500, 8500, 2500, 5300, 6500, 4800, 5900]
        }
      ]
      const seriesObj = []
      echartsData.forEach((item, index) => {
        const randomRgbColor = this.$tools.randomRgbColor('array')
        seriesObj.push({
          name: item.name,
          type: 'line',
          smooth: false,
          showAllSymbol: true,
          symbol: 'circle',
          symbolSize: 0,
          itemStyle: {
            color: borderColor[index],
          },
          lineStyle: {
            width: 2,
            color: borderColor[index]
          },
          data: item.value
        })
      })
      const option = {
        title: {
          text: '收支趋势/元',
          top: '4%',
          left: '2%',
          textStyle: {
            fontSize: "16px",
            color: '#ffffff',
            fontWeight: "500"
          },
        },
        tooltip: {
          trigger: 'axis',
          backgroundColor: '#0D2169',
          borderColor: '#727382',
          textStyle: {
            color: '#fff'
          }
        },
        legend: {
          show: true,
          data: Array.from(echartsData, ({ name }) => name),
          type: 'scroll',
          orient: 'horizontal',
          left: 'center',
          top: '14%',
          itemGap: 16,
          textStyle: {
            color: '#ffffff'
          }
        },
        grid: {
          left: '5%',
          right: '5%',
          bottom: '8%',
          top: '27%',
          containLabel: true
        },
        xAxis: {
          type: 'category',
          data: dateArr,
          axisTick: {
            show: false,
          },
          splitLine: {
            show: true,
            lineStyle: {
              color: "rgba(255,255,255,0.2)",
              type: 'dashed'
            },
          },
          axisLabel: {
            show: true,
            color: "#ffffff",
            interval: 0,
            textStyle: {
              color: 'rgba(255,255,255,0.5)',
              fontSize: 10,
              fontWeight: 500,
            }
          },
          axisLine: {
            lineStyle: {
              color: "rgba(255,255,255,0.2)",
            },
          }
        },
        yAxis: [{
          name: '',
          type: 'value',
          lineStyle: {
            show: "none",
          },
          axisTick: {
            show: false,
          },
          axisLabel: {
            show: true,
            color: "rgba(255,255,255,0.6)",
          },
          splitLine: {
            show: true,
            lineStyle: {
              color: "rgba(255,255,255,0.2)",
              type: 'dashed'
            },
          }
        }, {
          type: 'value',
          lineStyle: {
            show: "none",
          },
          axisTick: {
            show: false,
          },
          axisLine: {
            show: false,
          },
          axisLabel: {
            show: false,
          },
          splitLine: {
            show: false,
          }
        }],
        series: seriesObj
      }
      getchart.clear()
      getchart.setOption(option)
      // 随着屏幕大小调节图表
      window.addEventListener('resize', () => {
        getchart.resize()
      })
    },
    //检查人次分布图
    getRightCenter_left() {
      const getchart = echarts.init(document.getElementById('check_personTimeEcharts'))
      var borderColor = ['#FFCA64', '#8BDDF5', '#61E29D']
      let dateArr = ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月']
      let echartsData = [
        {
          name: '测试1',
          value: [1400.5000, 4500, 2300, 3600, 7800, 4500, 5050, 6500, 4300, 4500, 7800, 7900]
        }, {
          name: '测试2',
          value: [5400.2000, 3500, 4300, 5600, 5800, 7500, 6500, 5500, 6300, 3500, 4800, 6900]
        }, {
          name: '测试3',
          value: [1400.3000, 2500, 5300, 7600, 4800, 5500, 8500, 2500, 5300, 6500, 4800, 5900]
        }
      ]
      const seriesObj = []
      echartsData.forEach((item, index) => {
        const randomRgbColor = this.$tools.randomRgbColor('array')
        seriesObj.push({
          name: item.name,
          type: 'line',
          smooth: false,
          showAllSymbol: true,
          symbol: 'circle',
          symbolSize: 0,
          itemStyle: {
            color: borderColor[index],
          },
          lineStyle: {
            width: 2,
            color: borderColor[index]
          },
          data: item.value
        })
      })
      const option = {
        title: {
          text: '检查人次分布图',
          top: '4%',
          left: '2%',
          textStyle: {
            fontSize: "16px",
            color: '#ffffff',
            fontWeight: "500"
          },
        },
        tooltip: {
          trigger: 'axis',
          backgroundColor: '#0D2169',
          borderColor: '#727382',
          textStyle: {
            color: '#fff'
          }
        },
        legend: {
          show: true,
          data: Array.from(echartsData, ({ name }) => name),
          type: 'scroll',
          orient: 'horizontal',
          left: 'center',
          top: '16%',
          itemGap: 16,
          textStyle: {
            color: '#ffffff'
          }
        },
        grid: {
          left: '5%',
          right: '5%',
          bottom: '8%',
          top: '30%',
          containLabel: true
        },
        xAxis: {
          type: 'category',
          data: dateArr,
          axisTick: {
            show: false,
          },
          splitLine: {
            show: true,
            lineStyle: {
              color: "rgba(255,255,255,0.2)",
              type: 'dashed'
            },
          },
          axisLabel: {
            show: true,
            color: "#ffffff",
            interval: 0,
            textStyle: {
              color: 'rgba(255,255,255,0.5)',
              fontSize: 10,
              fontWeight: 500,
            }
          },
          axisLine: {
            lineStyle: {
              color: "rgba(255,255,255,0.2)",
            },
          }
        },
        yAxis: [{
          name: '',
          type: 'value',
          lineStyle: {
            show: "none",
          },
          axisTick: {
            show: false,
          },
          axisLabel: {
            show: true,
            color: "rgba(255,255,255,0.6)",
          },
          splitLine: {
            show: true,
            lineStyle: {
              color: "rgba(255,255,255,0.2)",
              type: 'dashed'
            },
          }
        }, {
          type: 'value',
          lineStyle: {
            show: "none",
          },
          axisTick: {
            show: false,
          },
          axisLine: {
            show: false,
          },
          axisLabel: {
            show: false,
          },
          splitLine: {
            show: false,
          }
        }],
        series: seriesObj
      }
      getchart.clear()
      getchart.setOption(option)
      // 随着屏幕大小调节图表
      window.addEventListener('resize', () => {
        getchart.resize()
      })
    },
    //维修情况
    getRightCenter_right() {
      const getchart = echarts.init(document.getElementById('repair_conditionEcharts'))
      let xData = ['1月', '2月', '3月', '4月', '5月']
      let yData = [100, 200, 440, 234, 588]
      let y1Data = [200, 100, 240, 134, 278]
      let y2Data = [30, 40, 80, 44, 78]
      const option = {
        title: {
          text: '维修情况',
          top: '2%',
          left: '2%',
          textStyle: {
            fontSize: "16px",
            color: '#ffffff',
            fontWeight: "500"
          },
        },
        legend: {
          top: '15%',
          left: "center",
          itemHeight: 8,
          itemWidth: 8,
          textStyle: {
            "color": "#fff"
          }
        },
        tooltip: {
          trigger: 'axis',
          backgroundColor: '#0D2169',
          borderColor: '#727382',
          textStyle: {
            color: '#fff'
          },
          axisPointer: {
            type: "shadow",
          },
        },
        grid: {
          left: '4%',
          right: '8%',
          bottom: '5%',
          top: '40%',
          containLabel: true
        },
        dataZoom: [
          // {
          //   show: true,
          //   height: 6,
          //   bottom: "2%",
          //   right: "3%",
          //   showDetail: false,
          //   brushSelect: false,
          //   backgroundColor: "transparent", // 背景颜色
          //   showDataShadow: false,
          //   borderColor: "transparent",
          //   fillerColor: "#8591CE", //拉动框的颜色
          //   startValue: 0,
          //   endValue: 4,
          //   brushSelect: false,
          //   handleSize: "60%",
          //   zoomLock: true,
          //   // 画一个圆形
          //   // handleIcon:
          //   //   "path://M30.9,53.2C16.8,53.2,5.3,41.7,5.3,27.6S16.8,2,30.9,2C45,2,56.4,13.5,56.4,27.6S45,53.2,30.9,53.2z M30.9,3.5M36.9,35.8h-1.3z M27.8,35.8 h-1.3H27L27.8,35.8L27.8,35.8z",
          //   handleStyle: {
          //     // 两侧缩放手柄的样式配置。
          //     borderColor: "#transparent",
          //     borderWidth: "5",
          //     shadowBlur: 1,
          //     background: "#transparent",
          //     shadowColor: "#transparent",
          //   },
          // },
        ],
        xAxis: {
          data: xData,
          axisLine: {
            show: true, //隐藏X轴轴线
            lineStyle: {
              color: 'rgba(230,247,255,0.3)'
            }
          },
          axisTick: {
            show: true //隐藏X轴刻度
          },
          splitLine: {
            //坐标轴在 grid 区域中的分隔线。
            show: true,
            lineStyle: {
              color: 'rgba(230,247,255,0.2)',
              type: 'dashed'
            },
          },
        },
        yAxis: [
          {
            name: '次',
            nameTextStyle: {
              color: 'rgba(230,247,255,0.6)',
            },
            type: 'value',
            splitNumber: 3,
            axisLabel: {
              textStyle: {
                color: 'rgba(230,247,255,0.6)',
                fontStyle: 'normal',
                fontSize: 12,
              },
            },
            axisLine: {
              show: false,
            },
            axisTick: {
              show: false,
            },
            splitLine: {
              show: true,
              lineStyle: {
                color: 'rgba(230,247,255,0.1)',
                type: 'dashed'
              }
            },
          },
          {
            name: '自修率',
            nameTextStyle: {
              color: 'rgba(230,247,255,0.6)'
            },
            min: 0,
            splitLine: {
              show: true,
              lineStyle: {
                color: 'rgba(230,247,255,0.1)',
                type: 'dashed'
              }
            },
            axisLine: {
              show: true,
              lineStyle: {
                color: '#233653'
              }
            },
            axisLabel: {
              show: true,
              textStyle: {
                color: ' rgba(230,247,255,0.6)'
              },
              formatter: function (value) {
                return value + '%'
              }
            },
            axisTick: {
              show: false
            }
          }
        ],
        series: [
          {
            name: "科室维修次数",
            type: "bar",
            barWidth: 10,
            itemStyle: {
              normal: {
                color: "#61E29D",
              }
            },
            data: yData,
          },
          {
            name: "厂商维修次数",
            type: "bar",
            barWidth: 10,
            itemStyle: {
              normal: {
                color: "#FFCA64",
              }
            },
            data: y1Data,
          },
          {
            name: "自修率",
            type: "line",
            itemStyle: {
              normal: {
                color: "#FF2D55",
              }
            },
            data: y2Data,
            yAxisIndex: 1
          }
        ]
      }
      getchart.clear()
      getchart.setOption(option)
      // 随着屏幕大小调节图表
      window.addEventListener('resize', () => {
        getchart.resize()
      })
    },
    //分时段检查人次分布
    getRightBottom_left() {
      const getchart = echarts.init(document.getElementById('timeDivision_personTimeEcharts'))
      let hours = [];
      let days = ['9:00-10:00', '10:00-11:30', '11:30-14:00', '14:00-15:00', '15:00-16:30'];
      let data = [
        [0, 0, 126],
        [0, 1, 509],
        [0, 2, 11],
        [0, 3, 130],
        [0, 4, 236],
      ];
      const option = {
        title: {
          text: '分时段检查人次分布',
          top: '2%',
          left: '2%',
          textStyle: {
            fontSize: "16px",
            color: '#ffffff',
            fontWeight: "500"
          },
        },
        tooltip: {
          position: 'top',
        },
        animation: false,
        grid: {
          left: '4%',
          right: '8%',
          bottom: '14%',
          top: '34%',
          containLabel: true
        },
        xAxis: {
          type: 'category',
          data: hours,
          splitArea: {
            show: true
          },
          axisLabel: {
            color: 'rgba(230,247,255,0.5)',
            fontSize: 12
          },
          axisTick: {
            lineStyle: {
              color: 'rgba(230,247,255,0.5)'
            }
          }
        },
        yAxis: {
          type: 'category',
          data: days,
          splitArea: {
            show: true
          },
          axisLabel: {
            color: 'rgba(230,247,255,0.5)',
            fontSize: 12
          },
          axisTick: {
            show: false
          }
        },
        visualMap: {
          min: 0,
          max: 509,
          calculable: true,
          orient: 'horizontal',
          top: '12%',
          left: 'center',
          itemWidth: 14,
          itemHeight: 114,
          inRange: {
            color: ['#E1F3FF', '#0A84FF'],
            symbolSize: [100, 100],
          },
          textStyle: {
            color: 'rgba(230,247,255,0.5)',
          },
        },
        series: [{
          name: '',
          type: 'heatmap',
          data: data,
          label: {
            show: true
          },
          emphasis: {
            itemStyle: {
              shadowBlur: 10,
              shadowColor: 'rgba(0, 0, 0, 1)'
            }
          }
        }]
      }
      getchart.clear()
      getchart.setOption(option)
      // 随着屏幕大小调节图表
      window.addEventListener('resize', () => {
        getchart.resize()
      })
    },
    //使用情况
    getRightBottom_right() {
      const getchart = echarts.init(document.getElementById('use_conditionEcharts'))
      var borderColor = ['#8BDDF5', '#FF2D55']
      let dateArr = ['2LB1CT4', '门诊楼B1CT1', '2LB1CT41', '2LB1CT42']
      let echartsData = [
        {
          name: '使用天数',
          value: [14.50, 40, 50, 60,]
        }, {
          name: '故障天数',
          value: [4.10, 12, 14, 18,]
        },
      ]
      const seriesObj = []
      echartsData.forEach((item, index) => {
        const randomRgbColor = this.$tools.randomRgbColor('array')
        seriesObj.push({
          name: item.name,
          type: 'bar',
          barWidth: 12,
          stack: 'total',
          itemStyle: {
            color: borderColor[index],
          },
          data: item.value
        })
      })
      const option = {
        title: {
          text: '使用情况/天',
          top: '4%',
          left: '2%',
          textStyle: {
            fontSize: "16px",
            color: '#ffffff',
            fontWeight: "500"
          },
        },
        tooltip: {
          trigger: 'axis',
          backgroundColor: '#0D2169',
          borderColor: '#727382',
          textStyle: {
            color: '#fff'
          }
        },
        legend: {
          show: true,
          data: Array.from(echartsData, ({ name }) => name),
          type: 'scroll',
          orient: 'horizontal',
          left: 'center',
          top: '16%',
          itemHeight: 8,
          itemWidth: 8,
          itemGap: 16,
          textStyle: {
            color: '#ffffff'
          }
        },
        grid: {
          left: '5%',
          right: '5%',
          bottom: '8%',
          top: '30%',
          containLabel: true
        },
        xAxis: {
          type: 'value',
          axisTick: {
            show: false,
          },
          splitLine: {
            show: true,
            lineStyle: {
              color: "rgba(255,255,255,0.2)",
              type: 'dashed'
            },
          },
          axisLabel: {
            show: true,
            color: "#ffffff",
            interval: 0,
            textStyle: {
              color: 'rgba(255,255,255,0.5)',
              fontSize: 10,
              fontWeight: 500,
            }
          },
          axisLine: {
            lineStyle: {
              color: "rgba(255,255,255,0.2)",
            },
          }
        },
        yAxis: {
          data: dateArr,
          type: 'category',
          lineStyle: {
            show: "none",
          },
          axisTick: {
            show: false,
          },
          axisLabel: {
            show: true,
            color: "rgba(255,255,255,0.6)",
          },
          splitLine: {
            show: true,
            lineStyle: {
              color: "rgba(255,255,255,0.2)",
              type: 'dashed'
            },
          }
        },
        series: seriesObj
      }
      getchart.clear()
      getchart.setOption(option)
      // 随着屏幕大小调节图表
      window.addEventListener('resize', () => {
        getchart.resize()
      })
    }
  },
};
</script>
<style lang="scss" scoped>
%box-common-styles {
  flex-shrink: 0;
  box-sizing: border-box;
  background: rgba(53, 98, 219, 0.06);
  border-radius: 2p;
}
.box-view {
  position: relative;
  height: 100%;
  width: 100%;
  box-sizing: border-box;
  display: flex;
  .box-left,
  .box-right {
    width: calc(50% - 0.5rem);
    height: 100%;
  }
  .box-left {
    .box-left-top {
      height: calc(50% - 0.5rem);
      width: 100%;
      display: flex;
      .item-box-left {
        width: calc(50% - 0.5rem);
        @extend %box-common-styles;
        .grossIncome {
          width: 100%;
          height: 100%;
          padding: 12px;
          &-title {
            font-weight: 500;
            font-size: 16px;
            color: #ffffff;
          }
          &-box {
            height: calc(100% - 20px);
          }
          &-content {
            height: calc((100% / 3) - 8px);
            background: rgba(53, 98, 219, 0.06);
            margin-top: 10px;
            display: flex;
            align-items: center;
          }
          .grossIncome-content-left {
            width: 68%;
            padding: 2px 24px;
            border-right: 1px solid rgba(133, 145, 206, 0.15);
            .item-block {
              display: inline-block;
              width: 12px;
              height: 12px;
              background: #8bddf5;
              border-radius: 2px;
              margin-right: 5px;
            }
            .item-label {
              font-weight: 500;
              font-size: 16px;
              color: #ffffff;
            }
            .item-value {
              font-weight: bold;
              font-size: 20px;
              color: #ffca64;
            }
            .item-unit {
              font-weight: 400;
              font-size: 16px;
              color: #a6afbf;
              margin-left: 4px;
            }
          }
          p:nth-child(2) {
            margin-top: 10px;
          }
          .grossIncome-content-right {
            text-align: center;
            flex: 1;
            padding: 4px 8px;
            .item-rate {
              font-weight: 500;
              font-size: 16px;
              color: #61e29d;
              margin-left: 2px;
            }
            .item-label {
              font-weight: 400;
              font-size: 14px;
              color: #ffffff;
            }
            img {
              margin-left: 2px;
              width: 19px;
              height: 19px;
              vertical-align: middle;
            }
          }
        }
      }
      .item-box-right {
        width: calc(50% - 0.5rem);
        margin-left: 1rem;
        @extend %box-common-styles;
      }
    }
    .box-left-bottom {
      height: calc(50% - 0.5rem);
      width: 100%;
      margin-top: 1rem;
      @extend %box-common-styles;
      padding: 16px;
      .title {
        font-weight: 500;
        font-size: 16px;
        width: 100%;
        color: #ffffff;
      }
      .legend {
        display: flex;
        width: 100%;
        margin-top: 6px;
        justify-content: center;
        .legendBox {
          margin-right: 16px;
          span:nth-child(1) {
            width: 8px;
            height: 8px;
            background: #8bddf5;
            border-radius: 1px;
            display: inline-block;
          }
          span:nth-child(2) {
            font-weight: 400;
            font-size: 12px;
            color: #ffffff;
            margin-left: 6px;
          }
        }
      }
      .item-3column-box {
        width: 100%;
        height: calc(100% - 50px);
        display: flex;
        flex-wrap: wrap;
        .column-box {
          width: calc((100% / 3) - 0.7rem);
          height: calc(50% - 1rem);
          background: rgba(133, 145, 206, 0.05);
          margin-top: 1rem;
          margin-left: 1rem;
        }
        .column-box:nth-child(3n-2) {
          margin-left: 0rem !important;
        }
      }
    }
  }
  .box-right {
    margin-left: 1rem;
    .box-right-top {
      height: calc((100% / 3) - 0.7rem);
      @extend %box-common-styles;
    }
    .box-right-center {
      margin: 1rem 0;
      height: calc((100% / 3) - 0.7rem);
      display: flex;
      .item-box-left {
        height: 100%;
        width: calc(50% - 0.5rem);
        @extend %box-common-styles;
      }
      .item-box-right {
        width: calc(50% - 0.5rem);
        margin-left: 1rem;
        height: 100%;
        @extend %box-common-styles;
      }
    }
    .box-right-bottom {
      height: calc((100% / 3) - 0.6rem);
      display: flex;
      .item-box-left {
        height: 100%;
        width: calc(50% - 0.5rem);
        @extend %box-common-styles;
      }
      .item-box-right {
        width: calc(50% - 0.5rem);
        margin-left: 1rem;
        height: 100%;
        @extend %box-common-styles;
      }
    }
  }
}
</style>
