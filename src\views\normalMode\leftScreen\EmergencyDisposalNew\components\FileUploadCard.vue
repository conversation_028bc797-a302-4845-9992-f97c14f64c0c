<template>
  <div class="component FileUploadCard">
    <el-upload :file-list="fileList" :accept="fileAccept" action="" list-type="picture"
               :http-request="handleHttpRequest" :on-change="handleFileChange" :on-error="handleUploadError">
      <el-button class="sino-button-sure FileUploadCard__btn--local" icon="el-icon-plus">上传文件</el-button>
      <template #tip>
        <div class="FileUploadCard__tip">支持文件格式：jpg、png、pdf、doc、txt、xls，单个文件最大上传20MB</div>
      </template>
      <template #file="{ file }">
        <div class="FileUploadCard__item">
          <div v-if="/[jpg|png]$/.test(file.name)" class="FileUploadCard__item__image">
            <img class="FileUploadCard__item__image__img" :src="file.url">
            <div class="FileUploadCard__item__image__filename">
              {{ file.name }}
            </div>
          </div>
          <div v-else class="FileUploadCard__item__file">
            <img class="FileUploadCard__item__file__img" src="@/assets/images/common/icon-file.png">
            <div class="FileUploadCard__item__file__filename">
              {{ file.name }}
            </div>
          </div>
          <div class="FileUploadCard__item__action">
            <i v-if="/[jpg|png]$/.test(file.name)" class="el-icon-view" @click="handlePreview(file)"></i>
            <i class="el-icon-delete" @click="handleRemove(file)"></i>
          </div>
        </div>
      </template>
    </el-upload>
    <el-button class="sino-button-sure FileUploadCard__btn--scan " icon="iconfont icon-saoma"
               @click="onUploadScanClick">
      扫码上传
    </el-button>
    <el-image v-if="!!previewUrl" class="FileUploadCard__preview-image" :src="previewUrl" :preview-src-list="imageUrls"
              @load="showImageViewer"></el-image>

    <!-- 二维码弹窗 -->
    <DialogUploadCode :alarmId="alarmId" :visible.sync="showQrCode"/>
  </div>
</template>

<script>
import {emergencyUploadFiles, fileUploadSocket} from '@/utils/peaceLeftScreenApi'

import DialogUploadCode from './DialogUploadCode.vue'

export default {
  name: 'FileUploadCard',
  components: {
    DialogUploadCode
  },
  props: {
    fileData: {
      type: Array,
      default: () => ([])
    },
    alarmId: String
  },
  emits: ['update:fileData', 'scan-click'],
  data() {
    return {
      formModel: {
        files: [],
        description: '',
        innerAttendees: [],
        extraAttendee: '',
        executor: ''
      },
      rules: {},
      fileList: [],
      showQrCode: false,
      fileAccept: '.jpg,.png,.pdf,.doc,.txt,.xls',
      previewUrl: '',
      socketConnected: false, // 二维码上传，socket连接是否成功
      /**
       * 二维码上传，socket链接对象
       * @type {WebSocket}
       */
      // eslint-disable-next-line vue/no-reserved-keys
      _socket: null
    }
  },
  computed: {
    // 预览图片的url列表
    imageUrls: function () {
      return this.fileList
        .filter(x => {
          const extension = x.name.substring(x.name.lastIndexOf('.'))
          return '.png,.jpg'.includes(extension)
        })
        .map(x => x.url)
    }
  },
  beforeDestroy() {
    if (this._socket) {
      this._socket.close()
    }
  },
  methods: {
    handleRemove(file) {
      let index = this.fileList.findIndex(x => x.uid === file.uid)
      if (index < 0) return
      this.fileList.splice(index, 1)
      index = this.fileData.findIndex(x => x.id === file.uid)
      if (index < 0) return
      const newFileData = this.fileData.filter(x => x.id !== file.uid).map(x => Object.assign({}, x))
      this.$emit('update:fileData', newFileData)
    },
    // 预览文件
    handlePreview(file) {
      this.previewUrl = file.url
      this.showImageViewer()
    },
    showImageViewer() {
      const imgEl = document.querySelector('.FileUploadCard__preview-image>img')
      if (imgEl) {
        imgEl.click()
      }
    },
    // 文件上传代理
    handleHttpRequest(file) {
      return this.checkFile(file.file)
        .then(() => {
          const params = new FormData()
          params.append('file', file.file)
          return emergencyUploadFiles(params)
        })
        .then((res) => {
          if (res.data.code === '200') {
            this.$message({
              message: '上传成功',
              type: 'success'
            })
            const newFileData = [...this.fileData]
            newFileData.push({
              name: file.file.name,
              id: file.file.uid,
              url: res.data.data.picUrl
            })
            this.$emit('update:fileData', newFileData)
          } else {
            throw res.data.message
          }
        })
    },
    // 检测文件是否可以上传
    async checkFile(file) {
      if (this.fileList.filter(x => x.name === file.name).length > 1) {
        throw '已存在此文件，请重新选择'
      }
      const extension = file.name.substring(file.name.lastIndexOf('.'))
      if (!this.fileAccept.includes(extension)) {
        throw '请选择正确的文件类型'
      }
      if (file.size > 20971520) {
        throw '文件大小不能超过20MB'
      }
    },
    // 上传失败提示
    handleUploadError(err) {
      let errMsg = '上传失败'
      if (typeof err === 'string') {
        errMsg = err
      }
      this.$message.error(errMsg)
    },
    // 失踪同步文件列表
    handleFileChange(_, fileList) {
      this.fileList = fileList
    },
    onUploadScanClick() {
      this.showQrCode = true
      if (!this._socket) {
        this._socket = fileUploadSocket(this.alarmId)
        this._socket.onmessage = this.onUploadSocketMessage.bind(this)
        this._socket.onerror = () => {
          this.$message.error('二维码上传交互服务异常')
        }
      }
    },
    /**
     * 收到socket回传的文件信息
     * @param { MessageEvent } message
     */
    onUploadSocketMessage(message) {
      Promise.resolve(JSON.parse(message.data))
        .then(msgData => {
          return this.doConfirm('接收到扫码上传的文件，是否应用?')
            .then(() => msgData.data)
            .catch(() => {
              throw null
            })
        })
        .then(files => {
          // 要反显的文件
          const timeSpan = Date.now()
          const revertFiles = files.map((item, index) => ({
            status: 'success',
            uid: timeSpan + '-' + index,
            url: this.$tools.imgUrlTranslation(item.operationUrl),
            name: item.operationName
          }))
          // 文件列表
          this.fileList.push(...revertFiles)
          // 同步成功的文件列表
          const uploadedFiles = files.map((item, index) => ({
            id: timeSpan + '-' + index,
            url: item.operationUrl,
            name: item.operationName
          }))
          const newFileData = [...this.fileData].concat(uploadedFiles)
          this.$emit('update:fileData', newFileData)
        })
        .catch((err) => {
          console.error(err)
          if (err) {
            this.$message.error('接收到扫码上传的文件,但解析失败')
          }
        })
    },
    /**
     * 页面通用询问框
     * @param msg 询问内容
     */
    doConfirm(msg) {
      return this
        .$confirm(msg, '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          cancelButtonClass: 'new-edition',
          confirmButtonClass: 'new-edition',
          customClass: 'confirm-box-class',
          type: 'warning'
        })
    }
  }
}

</script>

<style lang='scss' scoped>
.FileUploadCard {
  position: relative;
  height: 100%;
  overflow: hidden;

  > div:first-child {
    display: flex;
    height: 100%;
    flex-flow: column nowrap;
  }

  &__btn--scan {
    position: absolute;
    top: 0;
    left: 105px;
  }

  &__tip {
    color: #A6AFBF;
    line-height: 1;
    margin: 8px 0;
  }

  ::v-deep .el-upload {
    text-align: left;
  }

  ::v-deep .el-upload-list {
    flex: 1;
    overflow: auto;
    display: flex;
    flex-flow: row wrap;

    &__item {
      height: 120px;
      width: 120px;
      margin: 0 10px 10px 0;
      background: rgba(133, 145, 206, 0.15);
      border-radius: 4px 4px 4px 4px;
      border: 1px solid rgba(133, 145, 206, 0.5);
      padding: 0;
    }
  }

  &__item {
    height: 100%;
    position: relative;

    &__image {
      height: 100%;
      text-align: center;

      &__filename {
        position: absolute;
        left: 0;
        bottom: 0;
        right: 0;
        background: rgba(0, 0, 0, 0.5);
        border-radius: 0px 0px 2px 2px;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
        padding: 4px 8px;
        color: #fff;
        font-size: 12px;
      }

      &__img {
        height: 100%;
        width: auto;
      }
    }

    &__file {
      text-align: center;
      height: 100%;

      &__img {
        margin-top: 26px;
      }

      &__filename {
        line-height: 1;
        margin: 8px;
        color: #fff;
        height: calc(100% - 68px);
        overflow: hidden;
        white-space: initial;
      }
    }

    &:hover {
      .FileUploadCard__item__action {
        display: flex;
      }
    }

    &__action {
      display: none;
      position: absolute;
      top: 0;
      right: 0;
      bottom: 0;
      left: 0;
      align-items: center;
      justify-content: space-around;
      padding: 24px;
      background: rgba(0, 0, 0, 0.3);

      > i {
        cursor: pointer;
        font-size: 20px;

        &.el-icon-view {
          color: #ffca64;
        }

        &.el-icon-delete {
          color: #8bddf5;
        }
      }
    }

  }

  &__image {
    height: 1px;
    width: 0;
  }

}
</style>
