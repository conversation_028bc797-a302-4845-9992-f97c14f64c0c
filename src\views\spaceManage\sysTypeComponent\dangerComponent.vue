<template>
  <div class="dangerComponent">
    <div class="module-container" style="height: 30%;">
      <div class="module-header">
        <div class="title-left">
          <p class="title-left-icon"></p>
          <p class="title-left-text">隐患等级分析</p>
        </div>
      </div>
      <div class="module-content" style="height: calc(100% - 2.8rem)">
        <div v-if="workOrderStatisticsShow" style="width: 100%; height: 100%">
          <div id="workOrderStatisticsEcharts"></div>
        </div>
        <div v-else class="center-center">暂无数据</div>
      </div>
    </div>

    <div class="module-container" style="height: 30%;">
      <div class="module-header">
        <div class="title-left">
          <p class="title-left-icon"></p>
          <p class="title-left-text">隐患状态分析</p>
        </div>
      </div>
      <div class="module-content" style="height: calc(100% - 2.8rem)">
        <div v-if="dangerStateAnalysisShow" style="width: 100%; height: 100%;position: relative;">
          <div id="dangerStateAnalysisEcharts"></div>
          <div class="pie_background"></div>
        </div>
        <div v-else class="center-center">暂无数据</div>
      </div>
    </div>

    <div class="module-container" style="height: 40%;">
      <div class="module-header">
        <div class="title-left">
          <p class="title-left-icon"></p>
          <p class="title-left-text">隐患清单</p>
        </div>
        <div class="title-right" @click="moreList">
          <span class="viewMore">更多<i class="el-icon-arrow-right"></i></span>
        </div>
      </div>
      <div class="module-content" style="height: calc(100% - 2.8rem)">
        <div class="statistics-top">
          <div v-for="(item, index) in statisticsData" :key="index">
            <p class="green-font">{{ item.value + (item?.unit ?? '') }}</p>
            <p>{{ item.name }}</p>
          </div>
        </div>
        <el-table
          v-loading="tableLoading"
          class="table-center-transfer"
          :data="tableData"
          height="calc(100% - 50px)"
          :cell-style="$tools.setCell(3)"
          :header-cell-style="$tools.setHeaderCell(3)"
          style="width: 100%"
          element-loading-background="rgba(0, 0, 0, 0.2)"
          @row-dblclick="toDetail"
        >
          <el-table-column fixed prop="questionDetailType" show-overflow-tooltip label="隐患分类"></el-table-column>
          <el-table-column fixed prop="riskName" show-overflow-tooltip label="隐患等级"></el-table-column>
          <!-- <el-table-column fixed prop="createByDeptName" show-overflow-tooltip label="反馈部门"></el-table-column> -->
          <el-table-column fixed prop="createTime" show-overflow-tooltip label="反馈时间"></el-table-column>
          <el-table-column fixed prop="flowType" show-overflow-tooltip label="隐患状态"></el-table-column>
        </el-table>
      </div>
    </div>
    <spaceManage ref="dangerList" style="position: absolute;bottom: 99999px;"></spaceManage>
    <el-dialog v-dialogDrag :modal="false" :visible.sync="hiddenDangerDetailsListShow" custom-class="detailDialog main" :close-on-click-modal="false" :before-close="hiddenDangerDetailCloseDialog">
      <template slot="title">
        <span class="dialog-title">隐患详情</span>
      </template>
      <hiddenDangerDetailsList v-if="hiddenDangerShow" ref="hiddenDanger" :rowData="detailObj"/>
    </el-dialog>
  </div>
</template>

<script>
import { GetStateStatistics, GetHiddenDangersList } from '@/utils/centerScreenApi'
import hiddenDangerDetailsList from '../../centerScreen/safetyOverview/components/hiddenDangerDetailsList.vue'
import spaceManage from '../index.vue'
import * as echarts from 'echarts'
export default {
  name: 'dangerComponent',
  components: {
    spaceManage,
    hiddenDangerDetailsList
  },
  props: {
    roomData: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      tableLoading: false,
      hiddenDangerDetailsListShow: false,
      tableData: [],
      workOrderStatisticsShow: true,
      dangerStateAnalysisShow: false,
      tagCurrent: 1,
      hiddenDangerShow: false,
      statisticsData: [
        {
          name: '一般隐患',
          value: 0
        },
        {
          name: '重点隐患',
          value: 0
        }
      ], // 统计数据
      detailObj: {
        id: ''
      }
    }
  },
  mounted() {
    this.activeMethods()
  },
  methods: {
    // 隐患状态分析
    getStatisticsData(id) {
      GetStateStatistics({ placeIds: id }).then((res) => {
        if (res.data.code === '200') {
          if (res.data.data.array.some(i => i.value !== 0)) {
            this.dangerStateAnalysisShow = true
            this.$nextTick(() => {
              this.getStatisticsEchart(res.data.data.array)
            })
          }
        } else {
          this.$message({
            message: res.data.msg,
            type: 'warning'
          })
        }
      })
    },
    // 隐患状态分析echarts
    getStatisticsEchart(list) {
      const getchart = echarts.init(document.getElementById('dangerStateAnalysisEcharts'))
      getchart.resize()
      var scaleData = list
      var xdata = list.map((item) => {
        return item.name
      })
      var data = []
      var color = ['rgba(31, 250, 255, 0.3)', 'rgba(255, 227, 166, 0.28)', 'rgba(147, 130, 255, 0.26)', 'rgba(0, 248, 114, 0.24)', 'rgba(46, 119, 251, 0.3)']
      var borderColor = ['rgba(31, 250, 255, 1)', 'rgba(255, 227, 166, 1)', 'rgba(147, 130, 255, 1)', 'rgba(0, 248, 114, 1)', 'rgba(46, 119, 251, 1)']
      for (var i = 0; i < scaleData.length; i++) {
        data.push({
          value: scaleData[i].value,
          name: scaleData[i].name,
          rate: scaleData[i].rate,
          itemStyle: {
            normal: {
              borderWidth: 1,
              shadowBlur: 200,
              borderColor: borderColor[i],
              color: color[i],
              label: {
                normal: { show: true }
              }
            }
          }
        })
      }
      var seriesObj = [
        {
          name: '',
          type: 'pie',
          clockWise: false,
          center: ['25%', '50%'],
          radius: ['40%', '60%'],
          hoverAnimation: true,
          label: {
            normal: {
              show: false,
              position: 'center',
              formatter: '{text|{b}}\n{c} ({d}%)',
              rich: {
                value: {
                  padding: 5,
                  align: 'center',
                  verticalAlign: 'middle',
                  fontSize: 20
                },
                label: {
                  align: 'center',
                  verticalAlign: 'middle',
                  fontSize: 12
                }
              }
            },
            emphasis: {
              show: true,
              textStyle: {
                fontSize: '12',
                color: '#fff'
              }
            }
          },
          data: data
        }
      ]
      const option = {
        backgroundColor: 'rgba(128, 128, 128, 0)',
        legend: {
          orient: 'vartical',
          x: 'left',
          top: 'center',
          left: '50%',
          bottom: '0%',
          data: xdata,
          itemWidth: 8,
          itemHeight: 8,
          itemGap: 16,
          textStyle: {
            //  fontSize: 18,//字体大小
            color: '#B3C2DD' //  字体颜色
          },
          formatter: function (name) {
            var oa = option.series[0].data
            for (var i = 0; i < option.series[0].data.length; i++) {
              if (name === oa[i].name) {
                return ' ' + name + ' (' + oa[i].value + ')     ' + oa[i].rate
              }
            }
          }
        },
        series: seriesObj
      }
      getchart.clear()
      getchart.setOption(option)
      getchart.dispatchAction({
        type: 'highlight',
        seriesIndex: 0,
        dataIndex: 0
      })
      getchart.on('mouseover', (e) => {
        // 取消默认高亮
        getchart.dispatchAction({
          type: 'downplay',
          seriesIndex: 0,
          dataIndex: 0
        })
      })
      // 鼠标移出后默认高亮
      getchart.on('mouseout', (e) => {
        getchart.dispatchAction({
          type: 'highlight',
          seriesIndex: 0,
          dataIndex: 0
        })
      })
      // 随着屏幕大小调节图表
      window.addEventListener('resize', () => {
        getchart.resize()
      })
    },
    // 获取隐患等级分析数据
    // getTypeAnalysisInfo(id) {
    //   GetHiddenDangerTypeAnalysis({ placeIds: id }).then((res) => {
    //     if (res.data.code === '200') {
    //       if (res.data.data.seriesData.length > 0 && res.data.data.xAxisData.length > 0) {
    //         this.workOrderStatisticsShow = true
    //         this.$nextTick(() => {
    //           this.dangerTypeAnalysisEchart(res.data.data)
    //         })
    //       } else {
    //         this.workOrderStatisticsShow = false
    //       }
    //     }
    //   })
    // },
    // 隐患等级分析echarts
    dangerTypeAnalysisEchart(obj) {
      const getchart = echarts.init(document.getElementById('workOrderStatisticsEcharts'))
      getchart.resize()
      const option = {
        backgroundColor: '',
        tooltip: {
          trigger: 'axis',
          backgroundColor: '#0D2169',
          borderColor: '#727382',
          textStyle: {
            color: '#fff'
          },
          axisPointer: {
            type: 'shadow'
          }
        },
        grid: {
          left: '12%',
          right: '5%',
          bottom: '15%',
          top: '10%'
        },
        xAxis: [
          {
            type: 'category',
            data: ['一般隐患', '重点隐患'],
            axisTick: {
              show: false
            },
            // splitLine: {
            //   show: false
            // },
            axisLabel: {
              textStyle: {
                color: '#878EA9'
              }
            }
          }
        ],
        yAxis: [
          {
            type: 'value',
            axisLabel: {
              show: true,
              textStyle: {
                color: '#878EA9'
              }
            },
            axisLine: {
              lineStyle: {
                type: 'solid',
                color: '#3862B7',
                width: '1'
              }
            },
            splitLine: {
              lineStyle: {
                color: '#314A89',
                width: 1,
                type: 'solid'
              }
            },
            axisTick: {
              show: false
            }
          }
        ],
        series: [
          {
            type: 'bar',
            barWidth: 7,
            itemStyle: {
              color: new echarts.graphic.LinearGradient(0, 1, 0, 0, [
                {
                  offset: 0,
                  color: 'rgba(21, 89, 159, 0.25)'
                },
                {
                  offset: 1,
                  color: 'rgba(9, 244, 196, 0.86)'
                }
              ])
            },
            data: [obj.generalCount, obj.greatCount]
          }
        ]
      }
      getchart.clear()
      getchart.setOption(option)
      // 随着屏幕大小调节图表
      window.addEventListener('resize', () => {
        getchart.resize()
      })
    },
    // 隐患清单数据
    getHiddenDangerData(id) {
      GetHiddenDangersList({ placeIds: id }).then(res => {
        if (res.data.code === '200') {
          this.tableData = res.data.data.list
          this.statisticsData[0].value = res.data.data.dataMap.generalCount
          this.statisticsData[1].value = res.data.data.dataMap.greatCount
          // if (res.data.data.dataMap.generalCount !== 0 && res.data.data.dataMap.greatCount !== 0) {
          //   this.workOrderStatisticsShow = true
          //   this.$nextTick(() => {
          //     this.dangerTypeAnalysisEchart(res.data.data.dataMap)
          //   })
          // } else {
          //   this.workOrderStatisticsShow = false
          // }
          this.workOrderStatisticsShow = true
          this.$nextTick(() => {
            this.dangerTypeAnalysisEchart(res.data.data.dataMap)
          })
        }
      })
    },
    // 详情
    toDetail(row) {
      this.hiddenDangerDetailsListShow = true
      this.detailObj.id = row.id
      this.hiddenDangerShow = true
      // this.$refs.hiddenDanger.getHistoryFollowInfo(row.id)
      try {
        window.chrome.webview.hostObjects.sync.bridge.GetSwichFloorIsShow(true)
        window.chrome.webview.hostObjects.sync.bridge.ShowDialog(true)
      } catch (error) {}
    },
    // 更多
    moreList() {
      this.$refs.dangerList.allTableChange('danger', { placeIds: this.roomData.ssmCodes.substring(this.roomData.ssmCodes.lastIndexOf(',') + 1) })
    },
    activeMethods() {
      // this.getTypeAnalysisInfo(this.roomData.ssmCodes.substring(this.roomData.ssmCodes.lastIndexOf(',') + 1))
      this.getStatisticsData(this.roomData.ssmCodes.substring(this.roomData.ssmCodes.lastIndexOf(',') + 1))
      this.getHiddenDangerData(this.roomData.ssmCodes.substring(this.roomData.ssmCodes.lastIndexOf(',') + 1))
    },
    hiddenDangerDetailCloseDialog() {
      this.hiddenDangerDetailsListShow = false
      this.hiddenDangerShow = false
      try {
        window.chrome.webview.hostObjects.sync.bridge.GetSwichFloorIsShow(false)
        window.chrome.webview.hostObjects.sync.bridge.ShowDialog(false)
      } catch (error) {}
    }
  }
}
</script>

<style lang="scss" scoped>
@import '../style/module.scss';
.dangerComponent {
  width: 100%;
  height: 100%;
  #workOrderStatisticsEcharts,
  #dangerStateAnalysisEcharts {
    width: 100%;
    height: 100%;
    position: relative;
    z-index: 2;
  }
  .statistics-top{
    justify-content: space-around !important;
  }
  .pie_background {
    left: 25%;
  }
}
::v-deep .detailDialog {
  width: 60%;
  height: 80vh;
  margin-top: 7vh !important;
  background: url('@/assets/images/table-bg.png') no-repeat;
  background-size: 100% 100%;
  pointer-events: auto;
  .el-dialog__body {
    padding: 10px 50px;
    height: calc(100% - 60px);
    max-height: 80vh;
  }
}
</style>
