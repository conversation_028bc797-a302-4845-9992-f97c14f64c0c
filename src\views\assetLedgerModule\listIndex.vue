<template>
  <div class="list-index-view">
    <div class="view-header-box">
      <div class="header-left">
        <el-form
          :model="searchForm"
          class="search-form"
          :class="{ collapsed }"
          inline
        >
          <el-form-item>
            <el-input
              size="small"
              v-model="searchForm.input"
              placeholder="资产名称/编码/通用名"
            ></el-input>
          </el-form-item>
          <el-form-item>
            <el-select
              size="small"
              v-model="searchForm.deptCode"
              placeholder="所属科室"
            >
              <el-option
                v-for="item in deptOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              >
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-select
              size="small"
              v-model="searchForm.brandCode"
              placeholder="品牌"
            >
              <el-option
                v-for="item in brandOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              >
              </el-option>
            </el-select>
          </el-form-item>
        </el-form>
      </div>
      <div class="header-right">
        <el-button class="sino-button-sure" @click="resetForm">重置</el-button>
        <el-button class="sino-button-sure" @click="handleSearchForm"
          >查询</el-button
        >
        <el-button class="sino-button-sure" @click="advancedSearch"
          >高级查询</el-button
        >
      </div>
    </div>
    <div class="view-nav-box">
      <div
        class="nav-box"
        :class="{ active: index == navIdx }"
        v-for="(item, index) in navOptions"
        :key="index"
        @click="handleNavIdx(index)"
      >
        <div class="nav-div">{{ item.label }}</div>
      </div>
    </div>
    <div class="view-content">
      <div class="table-view">
        <el-table
          ref="table"
          v-loading="tableLoading"
          :data="tableData"
          :resizable="false"
          height="calc(100% - 110px)"
          :cell-style="$tools.setCell(3)"
          :header-cell-style="$tools.setHeaderCell(3)"
          stripe
          element-loading-background="rgba(0, 0, 0, 0.2)"
        >
          <el-table-column label="生命周期轴" width="120px" type="expand">
            <template slot-scope="props">
              <div class="axis-big-box">
                <div class="axis-box">
                  <div class="axis"></div>
                  <div
                    class="axis-text-box"
                    v-for="(item, index) in lifeCycleData"
                    :key="index"
                  >
                    <div class="axis-text-box-top">{{ item.name }}</div>
                    <div class="axis-text-box-bot">{{ item.time }}</div>
                  </div>
                </div>
              </div>
            </template>
          </el-table-column>
          <el-table-column
            prop="assetsName"
            label="资产名称"
            show-overflow-tooltip
          ></el-table-column>
          <el-table-column
            prop="assetsCode"
            label="资产编码"
            show-overflow-tooltip
          ></el-table-column>
          <el-table-column
            prop="commonName"
            label="通用名"
            show-overflow-tooltip
          ></el-table-column>
          <el-table-column
            prop="belongDept"
            label="所属科室"
            show-overflow-tooltip
          ></el-table-column>
          <el-table-column
            prop="assetsStatus"
            label="资产状态"
            show-overflow-tooltip
          ></el-table-column>
          <el-table-column
            prop="maintenanceStatus"
            label="在保状态"
            show-overflow-tooltip
          ></el-table-column>
          <el-table-column
            prop="riskLevel"
            label="风险等级"
            show-overflow-tooltip
          ></el-table-column>
          <el-table-column
            prop="medicalInstrumentsCategory"
            label="医疗器械分类"
            width="150"
            show-overflow-tooltip
          ></el-table-column>
          <el-table-column
            prop="manufacturerBrand"
            label="品牌"
            show-overflow-tooltip
          ></el-table-column>
          <el-table-column
            prop="deviceModel"
            label="规格型号"
            show-overflow-tooltip
          ></el-table-column>
          <el-table-column
            prop="supplierName"
            label="供应商"
            show-overflow-tooltip
          ></el-table-column>
          <el-table-column label="操作" fixed="right" width="100px">
            <template slot-scope="scope">
              <span
                class="operation-span"
                @click="operating('detail', scope.row)"
                >详情</span
              >
            </template>
          </el-table-column>
        </el-table>
        <el-pagination
          :current-page="currentPage"
          :page-sizes="[15, 20, 30, 40]"
          :page-size="pageSize"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
          class="pagination"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        ></el-pagination>
      </div>
    </div>
    <!-- 高级搜索 -->
    <advancedSearchDialog
      v-if="advancedSearchVisible"
      ref="advancedSearchDialog"
      @closeAdvancedSearch="closeAdvancedSearch"
      @queryAdvancedSearchForm="queryAdvancedSearchForm"
    />
  </div>
</template>
<script>
import { assetsList, lifeCycleData } from "./json/mockJson";
export default {
  components: {
    advancedSearchDialog: () => import("./components/advancedSearchDialog"),
  },
  data() {
    return {
      advancedSearchVisible: false,
      tableLoading: false,
      collapsed: false,
      navOptions: [
        { label: "全部", value: "#" },
        { label: "账内资产", value: "inAccountAssets" },
        { label: "账外资产", value: "outAccountAssets" },
        { label: "正姿设备", value: "zzAssets" },
        { label: "低值资产", value: "lowValueAssets" },
        { label: "主资产", value: "mainAssets" },
        { label: "附属资产", value: "subAssets" },
      ],
      searchForm: {
        input: "",
        deptCode: "",
        brandCode: "",
      },
      navIdx: 0,
      currentPage: 1,
      pageSize: 15,
      total: 9,
      deptOptions: [],
      brandOptions: [],
      tableData: assetsList,
      lifeCycleData: lifeCycleData,
    };
  },
  methods: {
    handleClose(done) {
      done();
    },
    /** 重置 */
    resetForm() {},
    /** 查询 */
    handleSearchForm() {},
    /** 高级查询 */
    advancedSearch() {
      this.advancedSearchVisible = true;
    },
    /** 条数变化事件 */
    handleSizeChange() {},
    /** 页数变化事件 */
    handleCurrentChange() {},
    /** nav 切换事件 */
    handleNavIdx(idx) {
      this.navIdx = idx;
    },
    /** 获取数据 */
    getData() {},
    /** 关闭高级搜索 弹窗 */
    closeAdvancedSearch() {
      this.advancedSearchVisible = false;
    },
    /** 高级搜索 查询 */
    queryAdvancedSearchForm() {
      this.advancedSearchVisible = false;
    },
    /** 跳转详情 */
    operating(type, row) {
      switch (type) {
        case "detail":
          this.$emit("openDetailComponent", "assetsListDetails");
          break;

        default:
          break;
      }
    },
  },
};
</script>
<style lang="scss" scoped>
.list-index-view {
  position: relative;
  height: 100%;
  padding: 0px 40px;
}
.view-header-box {
  display: flex;
  justify-content: space-between;
}
.view-nav-box {
  margin-bottom: 1.5rem;
  display: flex;
  align-items: center;
  box-sizing: border-box;
  .nav-box {
    height: 2.5rem;
    line-height: 2.5rem;
    width: 12.38rem;
    font-size: 1rem;
    text-align: center;
    color: #a4afc1;
    cursor: pointer;
    box-sizing: border-box;
    border: 1px dashed #26314f;
    border-right: none;
    background: url(@/assets/images/nav-bg.png) no-repeat;
    background-size: cover;
  }
  .nav-box:last-child {
    border-right: 1px dashed #26314f;
  }
  .active {
    color: #b0e3fa;
    background: url(@/assets/images/nav-bg-xz.png) no-repeat;
    background-size: cover;
  }
}
.view-content {
  height: calc(100% - 4rem);
  .table-view {
    height: 100%;
  }
  .axis-big-box {
    width: 100%;
    height: 5.62rem;
    box-sizing: border-box;
    padding: 0px 20px;
    overflow-x: auto;
    display: flex;
  }
  .axis-box {
    height: 5rem;
    position: relative;
    display: flex;
    align-items: center;
    .axis {
      width: 100%;
      height: 1px;
      background: #ffca64;
      position: absolute;
      left: 0px;
      top: 45px;
    }
    .axis-text-box {
      margin-right: 1.25rem;
      font-size: 14px;
      div {
        width: 5.44rem;
        height: 1.25rem;
        line-height: 1.25rem;
        text-align: center;
      }
      &-top {
        font-size: 14px;
        color: #b0e3fa;
        margin-top: 0.44rem;
      }
      &-bot {
        margin-top: 1.44rem;
        border-radius: 2px;
        font-size: 12px;
        color: #ffca64;
        background: rgba(255, 202, 100, 0.2);
        position: relative;
      }
      &-bot::after {
        content: "";
        width: 1px;
        height: 0.75rem;
        background: #ffca64;
        position: absolute;
        left: 50%;
        top: -0.75rem;
      }
      &-bot::before {
        content: "";
        width: 5px;
        height: 5px;
        border-radius: 5px;
        background: #ffca64;
        position: absolute;
        left: 2.6rem;
        top: -0.75rem;
      }
    }
  }
}
.operation-span {
  cursor: pointer;
}
::v-deep .el-table--striped .el-table__body tr.el-table__row--striped td.el-table__cell{
  background: transparent;
}
</style>