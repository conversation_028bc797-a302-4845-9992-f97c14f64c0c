<!--
 * @Author: hedd
 * @Date: 2024-01-30 14:12:51
 * @LastEditTime: 2024-05-10 17:47:57
 * @FilePath: \ihcrs_client_iframe\src\views\spaceManage\sysTypeComponent\components\deviceKpi\invest.vue
 * @Description:
-->
<template>
  <div class="invest">
    <div id="invest_chart"></div>
  </div>
</template>

<script>
import * as echarts from 'echarts'
import 'echarts-liquidfill'
import { mockInvestData } from './mockData.js'
export default {
  name: 'invest',
  data() {
    return {
      investData: {
        recyclingYear: '-',
        balanceYear: '-',
        profit: '--'
      }
    }
  },
  computed: {},
  created() {},
  mounted() {
    if (__PATH.VUE_APP_HOSPITAL_NODE === 'outernet') {
      this.investData = mockInvestData
    }
    this.$nextTick(() => {
      this.setChart()
    })
  },
  methods: {
    setChart() {
      const getchart = echarts.init(document.getElementById('invest_chart'))
      const option = {
        // backgroundColor: 'rgba(133, 145, 206, 0.5)',
        title: [
          {
            text: '投资回收情况',
            left: '20',
            top: 20,
            textStyle: {
              color: '#fff',
              fontWeight: 'normal',
              fontSize: 16
            }
          },
          {
            text: `投资回收期：${this.investData.recyclingYear}年`,
            left: '50%',
            top: '28%',
            textAlign: 'center',
            textStyle: {
              color: '#fff',
              fontWeight: 'normal',
              fontSize: 16
            }
          },
          {
            text: `预计还有${this.investData.balanceYear}年达到盈亏平衡`,
            left: '50%',
            top: '42%',
            textAlign: 'center',
            textStyle: {
              color: '#fff',
              fontWeight: 'normal',
              fontSize: 14
            }
          },
          {
            text: '当前利润',
            left: '50%',
            top: '58%',
            textAlign: 'center',
            textStyle: {
              color: '#000',
              fontWeight: 'normal',
              fontSize: 14
            }
          },
          {
            text: this.investData.profit,
            left: '50%',
            top: '65%',
            textAlign: 'center',
            textStyle: {
              color: '#000',
              fontWeight: '500',
              fontSize: 28
            }
          }
        ],
        series: [
          {
            type: 'liquidFill',
            radius: '60%',
            data: [
              {
                value: 0.45,
                direction: 'left'
              }
            ],
            backgroundStyle: {
              borderWidth: 1,
              color: 'transparent'
            },
            label: {
              normal: {
                show: false,
                left: '50%',
                top: '20%',
                textStyle: {
                  fontSize: 28,
                  fontWeight: '500',
                  color: 'rgba(6, 16, 44, 1)'
                }
              }
            },
            color: ['rgba(255, 202, 100, 1)'],
            outline: {
              show: true,
              borderDistance: 0,
              itemStyle: {
                borderWidth: 0,
                color: 'rgba(133, 145, 206, 0.5)'
              }
            }
          }
        ]
      }
      getchart.clear()
      getchart.setOption(option)
      // 随着屏幕大小调节图表
      window.addEventListener('resize', () => {
        getchart.resize()
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.invest {
  width: 100%;
  height: 100%;
  background: rgba(53, 98, 219, 0.06);
  #invest_chart {
    width: 100%;
    height: 100%;
  }
}
</style>
