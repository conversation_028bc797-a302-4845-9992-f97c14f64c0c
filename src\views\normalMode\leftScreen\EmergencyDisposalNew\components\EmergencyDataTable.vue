<template>
  <!-- 应急处置-全部数据 -->
  <div v-loading="actionLoadingStatus" class="component EmergencyDataTable">
    <el-form :model="searchFrom" class="search-form" :class="{ collapsed }" inline>
      <el-form-item>
        <el-input v-model.trim="searchFrom.alarmObjectName" placeholder="报警对象/ID" style="width: 180px;"
                  clearable @keyup.enter.native.stop="searchForm"></el-input>
      </el-form-item>
      <el-form-item>
        <el-select v-model="searchFrom.projectCode" placeholder="报警系统" multiple collapse-tags clearable
                   @change="getAlarmTypeOptions" @clear="() => {
                     searchFrom.alarmType = []
                   }
                   ">
          <el-option v-for="item in alarmSourceOptions" :key="item.projectCode" :label="item.projectName"
                     :value="item.projectCode"> </el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-select v-model="searchFrom.alarmType" placeholder="报警类型" clearable multiple collapse-tags
                   :disabled="searchFrom.projectCode.length !== 1">
          <el-option v-for="item in alarmTypeOption" :key="item.id" :label="item.alarmDictName" :value="item.id">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-date-picker v-model="searchFrom.dataRange" popper-class="date-style" type="daterange" unlink-panels
                        range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期" value-format="yyyy-MM-dd"
                        :picker-options="pickerOptions">
        </el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-select v-model="searchFrom.alarmLevel" placeholder="报警等级" clearable>
          <el-option v-for="item in alarmLevelOptions" :key="item.value" :label="item.label" :value="item.value">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item v-show="!collapsed">
        <el-select v-model="searchFrom.alarmStatus" placeholder="处理状态" clearable>
          <el-option v-for="item in alarmStatusOptions" :key="item.value" :label="item.label" :value="item.value">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item v-show="!collapsed">
        <el-select v-model="searchFrom.alarmAffirm" placeholder="确警类型" clearable>
          <el-option v-for="item in alarmConfirmOptions" :key="item.value" :label="item.label" :value="item.value">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item v-show="!collapsed">
        <el-select ref="treeSelect" v-model="searchFrom.alarmSpaceId" clearable placeholder="报警位置" @clear="handleClear">
          <el-option hidden :value="searchFrom.alarmSpaceId" :label="areaName"> </el-option>
          <el-tree class="search-form-tree" :data="serverSpaces" :props="serverDefaultProps" :load="serverLoadNode" lazy
                   :expand-on-click-node="false" :check-on-click-node="true" @node-click="handleNodeClick">
          </el-tree>
        </el-select>
      </el-form-item>
      <el-form-item>
        <div style="display: inline-block;">
          <el-button class="sino-button-sure" @click="resetForm">重置</el-button>
          <el-button class="sino-button-sure" @click="searchForm">查询</el-button>
          <span class="search-form__collapse-btn" @click="onSearchCollapseClick">{{ collapseText }} <i
            :class="collapseIcon"></i></span>
        </div>
      </el-form-item>
    </el-form>
    <div class="batch-control">
      <el-button v-for="(item, index) in batchControlList" :key="index" :disabled="!selectAlarmList.length"
                 class="sino-button-sure" @click="batchControlEvent(item, selectAlarmList)">{{
                   item.label
                 }}</el-button>
    </div>
    <div class="table-list" :class="{ formCollapsed: collapsed }">
      <el-table ref="table" v-loading="tableLoading" :data="tableData" :resizable="false"
                height="calc(100% - 40px)" :cell-style="$tools.setCell(3)" :header-cell-style="$tools.setHeaderCell(3)" stripe
                element-loading-background="rgba(0, 0, 0, 0.2)" @selection-change="tableSelectChange"
                @cell-click="onTabCellClick">
        <el-table-column type="selection" width="60" align="center" :selectable="row => canExecute(row)"
                         class-name="EmergencyDataTable__cell--selection"></el-table-column>
        <el-table-column prop="alarmLevel" label="报警等级" width="95px">
          <span slot-scope="scope" :style="{ color: alarmLevelFormatter(scope.row).color }">
            {{ alarmLevelFormatter(scope.row).label }}
          </span>
        </el-table-column>
        <el-table-column prop="alarmId" label="报警ID" show-overflow-tooltip></el-table-column>
        <el-table-column prop="alarmSource" label="报警系统" show-overflow-tooltip width="95px"></el-table-column>
        <el-table-column prop="alarmType" label="报警类型" show-overflow-tooltip width="95px"></el-table-column>
        <el-table-column prop="alarmStartTime" label="报警时间" width="175px"></el-table-column>
        <el-table-column prop="completeRegionName" label="报警位置" show-overflow-tooltip width="95px"></el-table-column>
        <el-table-column prop="alarmObjectName" label="报警对象" show-overflow-tooltip width="95px"></el-table-column>
        <el-table-column prop="alarmDetails" label="报警描述" show-overflow-tooltip width="95px"></el-table-column>
        <el-table-column prop="alarmStatus" label="处理状态" width="95px">
          <template #default="{ row }">
            <span :style="{ color: alarmStatusFormatter(row).color }">
              <i :class="alarmStatusFormatter(row).icon"></i>
              {{ alarmStatusFormatter(row).label }}
            </span>
          </template>
        </el-table-column>
        <!-- 0：非经典案例，1：经典案例 -->
        <el-table-column prop="classic" label="" width="50px">
          <el-rate slot-scope="scope" :value="scope.row.classic" :max="1" text-color="#ff9900"
                   @change="onClassicChange(scope.row)">
          </el-rate>
        </el-table-column>
        <el-table-column prop="alarmAffirm" label="警情类型" width="95px" :formatter="alarmAffirmFormatter">
        </el-table-column>
        <el-table-column prop="workCount" label="关联工单" width="95px">
          <template #default="{ row }">
            <span v-if="row.workCount === 0">-</span>
            <span v-else-if="row.workCount === 1" :style="{ color: alarmWorkOrderFormatter(row).color }">
              <i :class="alarmWorkOrderFormatter(row).icon"></i>
              {{ alarmWorkOrderFormatter(row).label }}
            </span>
            <div v-else="row.workCount > 1" class="operationBtn" @click="onViewWorkOrder(row)">
              <span>{{ row.workCount }}</span>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="160px">
          <div slot-scope="scope" class="operationBtn">
            <!-- dispatch(派单) confirmAlarm(确警) remark(备注) shielded(屏蔽) close(关闭) viewDetails(查看详情) -->
            <!-- 客户端 未处理，处理中 都可以处理 -->
            <template v-if="scope.row.alarmStatus != 2">
              <!-- 配置了可操作平台才能操作 -->
              <span :disabled="!canExecute(scope.row)" style="margin-right: 10px;"
                    @click="operating('execute', scope.row)">开始处理</span>
            </template>
            <template v-else>
              <span :disabled="scope.row.alarmAffirm == 2" style="margin-right: 10px;"
                    @click="operating('summary', scope.row)">总结</span>
            </template>
            <!-- 已经派单了就不能执行 -->
            <span v-if="!scope.row.workNum" style="margin-right: 10px"
                  @click="operating('dispatch', scope.row)">派单</span>
            <span @click="operating('detail', scope.row)">查看</span>
          </div>
        </el-table-column>
      </el-table>
      <el-pagination :current-page="currentPage" :page-sizes="[15, 20, 30, 40]" :page-size="pageSize"
                     layout="total, sizes, prev, pager, next, jumper" :total="total" class="pagination"
                     @size-change="handleSizeChange" @current-change="handleCurrentChange"></el-pagination>
    </div>

    <!-- 批量确警/演习 -->
    <DialogEmergencyConfirm :visible.sync="confirmDialog.show" v-bind="confirmDialog" @success="search" />
    <!-- 批量屏蔽 -->
    <DialogEmergencyShield :visible.sync="shieldDialog.show" is-batch v-bind="shieldDialog" @success="search" />
  </div>
</template>
<script>
import {
  getSourceByEmpty,
  getSpaceInfoList,
  getStructureTree,
  GetAllAlarmRecord,
  AlarmAffirm,
  OneKeyDispatch,
  setAlarmRecordToClassics,
  queryFieldsConfigList
} from '@/utils/peaceLeftScreenApi'
import { monitorTypeList } from '@/assets/common/dict.js'
import moment from 'moment'

import {
  alarmWorkOrderConfig,
  alarmStatusConfig,
  alarmLevelConfig,
  alarmAffirmConfig,
  toDetailTab,
  batchControlList,
  batchControlType,
  alarmConfirmOptions,
  operatePlatform
} from '../emergency-constant'

moment.locale('zh-cn')
export default {
  name: 'EmergencyDataTable',
  components: {
    DialogEmergencyConfirm: () => import('./DialogEmergencyConfirm'),
    DialogEmergencyShield: () => import('./DialogEmergencyShield')
  },
  props: {
    show: Boolean,
    paramsData: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      collapsed: true,
      areaName: '', // 选中 下拉树的name
      alarmSourceOptions: [], // 报警来源
      alarmTypeOption: [], // 报警类型
      serverSpaces: [], // 空间位置
      spaces: [], // 空间位置
      serverDefaultProps: {
        label: 'ssmName',
        isLeaf: 'leaf',
        children: 'children'
      },
      searchFrom: {
        alarmObjectName: '', // 搜索关键字
        projectCode: [], // 报警来源
        alarmType: [], // 报警类型
        alarmLevel: '', // 报警等级
        alarmSpaceId: '', // 空间位置
        dataRange: [], // 时间范围
        alarmStatus: '', // 处理状态
        alarmAffirm: '' // 确警类型
      }, // 搜索条件
      alarmConfirmOptions, // 确警类型
      alarmLevelOptions: alarmLevelConfig, // 警情级别
      pickerOptions: {
        shortcuts: [
          {
            text: '最近一周',
            onClick(picker) {
              const end = new Date()
              const start = new Date()
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 6)
              picker.$emit('pick', [start, end])
            }
          },
          {
            text: '最近一个月',
            onClick(picker) {
              const end = new Date()
              const start = new Date()
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 30)
              picker.$emit('pick', [start, end])
            }
          }
        ]
      },
      // 报警处理状态表单项
      alarmStatusOptions: alarmStatusConfig,
      tableData: [],
      currentPage: 1,
      pageSize: 15,
      total: 0,
      tableLoading: false,
      warnAnalysis: [],
      warnAnalysisShow: true,
      timer: null,
      screenSmall: false, // 是否是小屏
      screenBack: false, // 是否是返回
      pageType: 1, // 1：默认 2：返回
      batchControlList,
      selectAlarmList: [], // 选中的报警列表
      selectAlarmItem: {}, // 选中报警项
      screenSelectItems: [], // 屏蔽列表
      alarmVideoShow: false,
      confirmDialog: {
        show: false, // 批量确警/演习弹窗
        tag: '',
        state: -1,
        ids: '',
        codes: ''
      },
      shieldDialog: {
        show: false, // 屏蔽弹窗
        ids: '',
        objectIds: '',
        types: ''
      },
      actionLoadingStatus: false // 加载状态
    }
  },
  computed: {
    collapseText() {
      return this.collapsed ? '展开' : '收起'
    },
    collapseIcon() {
      return this.collapsed ? 'el-icon-arrow-down' : 'el-icon-arrow-up'
    }
  },
  watch: {
    show(val) {
      // 再次展示table的时候执行刷新操作
      if (val) this.search()
    }
  },
  mounted() {
    Object.assign(this.searchFrom, this.paramsData)
    this.getAlarmSource()
    this.getTreeList()
    this.search()
  },
  methods: {
    alarmStatusFormatter(row) {
      return alarmStatusConfig.find(x => x.value == row.alarmStatus) || {}
    },
    alarmLevelFormatter(row) {
      return alarmLevelConfig.find(x => x.value == row.alarmLevel) || {}
    },
    alarmAffirmFormatter(row) {
      const item = alarmAffirmConfig.find(x => x.value == row.alarmAffirm) || {}
      return item.label || '-'
    },
    alarmWorkOrderFormatter(row) {
      const type = row.workType || '未派工'
      const item = alarmWorkOrderConfig.find(x => x.label == type) || {}
      return item
    },
    /** 案件可否进行操作 */
    canExecute(row) {
      return /0/.test(row.disposalTerminal ?? '0') && row.alarmStatus !== 2 && !row.alarmAffirm
    },
    onSearchCollapseClick() {
      this.collapsed = !this.collapsed
    },
    search() {
      this.initComponentData()
    },
    // 初始化组件数据
    initComponentData(tab = false) {
      this.selectAlarmList = []
      this.selectAlarmItem = {}
      this.getDataList()
    },
    // 一键派单
    oneKeyDispatch(row) {
      const userInfo = this.$store.state.loginInfo.user
      const param = {
        alarmId: row.alarmId,
        alarmLevel: row.alarmLevel,
        alarmSourceName: row.alarmSource,
        spaceId: row.alarmSpaceId,
        userName: userInfo.staffName,
        userId: userInfo.staffId,
        projectCode: row.projectCode,
        operationSource: operatePlatform.client
      }
      OneKeyDispatch(param).then((res) => {
        if (res.data.code === '200') {
          this.$message({
            message: '已派单',
            type: 'success'
          })
          this.initComponentData()
        } else {
          this.$message.error(res.data.msg || '派单失败')
        }
      })
    },
    // 批量处置按钮
    batchControlEvent(item, arr = this.selectAlarmList) {
      const ids = this.selectAlarmList.map(x => x.alarmId).join()
      const codes = this.selectAlarmList.map(x => x.projectCode).join()
      switch (item.state) {
        case batchControlType.confirm: // 确警
        case batchControlType.maneuver: // 演习
          this.confirmDialog.tag = item.label
          this.confirmDialog.show = true
          this.confirmDialog.state = item.state
          this.confirmDialog.ids = ids
          this.confirmDialog.codes = codes
          break
        case batchControlType.mistake: // 误报处理
          this.doConfirm(`是否将${arr.length}条报警记录确警为误报?`)
            .then(() => {
              this.actionLoadingStatus = true
              const prams = {
                alarmAffirm: batchControlType.mistake,
                alarmId: ids,
                projectCode: codes,
                remark: '误报',
                operationSource: operatePlatform.client
              }
              return AlarmAffirm(prams)
            })
            .then((res) => {
              if (res.data.code == 200) {
                this.$message.success('误报处理成功')
                this.search()
              } else {
                this.$message.error(res.data.msg || '误报处理失败')
              }
            })
            .catch(() => { })
            .finally(() => this.actionLoadingStatus = false)
          break
        case batchControlType.shield: // 屏蔽
          if (arr.some(x => x.alarmStatus === 1)) {
            this.$message.error('包含处理中的报警，不可屏蔽')
            return
          }
          this.shieldDialog.show = true
          this.shieldDialog.ids = ids
          this.shieldDialog.codes = codes
          this.shieldDialog.objectIds = this.selectAlarmList.map(x => x.alarmObjectId).join()
          this.shieldDialog.types = this.selectAlarmList.map(x => x.alarmFleldsConfigId).join()
          break
        default:
          break
      }
    },
    // 子/孙组件流程操作
    operating(type, selectItem) {
      this.selectAlarmItem = selectItem
      if (type === 'dispatch') {
        // dispatch(派单)
        // 派单
        this.doConfirm('确认派发确警工单?')
          .then(() => {
            this.oneKeyDispatch(selectItem)
          })
      } else if (type === 'execute') {
        if (!this.canExecute(selectItem)) {
          return
        }
        //  execute(警单处理)
        this.$emit('detail', selectItem.alarmId, toDetailTab.base)
        this.dispatchEquipmentView(selectItem)
      } else if (type === 'summary') {
        // 误报不处理
        if (selectItem.alarmAffirm == 2) {
          return
        }
        // 总结
        this.$emit('detail', selectItem.alarmId, toDetailTab.summary)
      } else if (type === 'detail') {
        // 查看详情
        this.$emit('detail', selectItem.alarmId, toDetailTab.base)
      }
    },
    // table选择
    tableSelectChange(val) {
      this.selectAlarmList = val
    },
    // table点击
    onTabCellClick(row, column) {
      if (column.property == 'alarmObjectName') {
        this.dispatchEquipmentView(row)
      }
    },
    // 触发关联设备展示
    dispatchEquipmentView(row) {
      const sendData = {
        DeviceCode: row.modelCode,
        assetsId: row.alarmDeviceId,
        assetsName: row.alarmDeviceName,
        spaceCode: row.alarmSpaceStr,
        projectCode: row.projectCode
      }
      this.$tools.alarmDataRowRealView(sendData)
      return
      // 如果关联了设备即跳转设备详情页
      if (row.modelCode) {
        const projectData = monitorTypeList.find(e => e.projectCode === row.projectCode)
        if (!projectData) return
        const params = {
          DeviceCode: row.modelCode,
          menuName: projectData.wpfKey,
          projectCode: projectData.projectCode
        }
        try {
          window.chrome.webview.hostObjects.sync.bridge.EquipmentRealView(JSON.stringify(params))
        } catch (error) { }
      } else {
        this.$message.info('暂未找到报警位置')
      }
    },
    // 获取全部报警记录
    getDataList() {
      const { projectCode, alarmType, alarmLevel, alarmSpaceId, dataRange, alarmObjectName } = this.searchFrom
      const params = {
        pageNo: this.currentPage,
        pageSize: this.pageSize,
        projectCode: projectCode.toString(),
        alarmType: alarmType.join(),
        alarmLevel,
        alarmSpaceId,
        startTime: dataRange[0],
        endTime: dataRange[1],
        alarmStatus: this.searchFrom.alarmStatus, // 处理状态
        alarmAffirm: this.searchFrom.alarmAffirm // 确警类型
      }
      // 关键字适应字段
      if (/^BJ/.test(alarmObjectName)) {
        params.alarmId = alarmObjectName
      } else {
        params.alarmObjectName = alarmObjectName
      }

      this.tableLoading = true
      GetAllAlarmRecord(params)
        .then((res) => {
          this.tableLoading = false
          if (res.data.code === '200') {
            const data = res.data.data?.records ?? []
            this.tableData = data
            this.total = res.data.data?.total ?? 0
            if (data.length) {
              const currentTime = new Date()
              const givenTime = new Date(this.tableData[0].alarmStartTime)
              const timeDifferenceInSeconds = Math.floor((currentTime - givenTime) / 1000)
              // if (timeDifferenceInSeconds <= timerNum) {
              if (timeDifferenceInSeconds <= 20000) {
                console.log('在30秒内')
                try {
                  window.chrome.webview.hostObjects.sync.bridge.IsPlayAlarmSound(true)
                } catch (error) { }
                const timer = setInterval(() => {
                  try {
                    window.chrome.webview.hostObjects.sync.bridge.IsPlayAlarmSound(false)
                  } catch (error) { }
                  console.log('报警结束')
                  clearInterval(timer)
                }, 3000)
              }
            }
          } else {
            this.tableData = []
            this.total = 0
          }
        })
        .catch(() => {
          this.tableLoading = false
        })
    },
    // 查询
    searchForm() {
      this.currentPage = 1
      this.getDataList()
    },
    // 重置查询
    resetForm() {
      this.handleClear()
      Object.assign(this.searchFrom, {
        projectCode: [], // 报警来源
        incidentType: [], // 事件类型
        alarmLevel: '', // 报警等级
        dataRange: [], // 时间范围
        objectId: '', // 报警对象
        alarmObjectName: '', // 搜索关键字
        alarmStatus: '', // 处理状态
        alarmAffirm: '' // 确警类型
      })
      this.currentPage = 1
      this.pageSize = 15
      this.total = 0
      this.getDataList()
    },
    // 获取服务空间树形结构
    getTreeList() {
      getStructureTree().then((res) => {
        if (res.data.code === 200) {
          this.spaces = res.data.data
          // 增加 懒加载节点
          res.data.data.map((e) => {
            e.leaf = false
          })
          // 转换为树形结构数据
          this.serverSpaces = this.$tools.transData(res.data.data, 'id', 'pid', 'children')
        }
      })
    },
    // 选择下拉树 数据
    handleNodeClick(data) {
      this.searchFrom.alarmSpaceId = data.id
      this.areaName = data.ssmName
      this.$refs.treeSelect.blur()
    },
    // 服务空间树结构点击获取自己及父节点ID并请求数据实现懒加载
    spaceTreeChange(value, resolve) {
      const child = this.$tools.ListTree(this.spaces, value.pid)
      child.push(value.id)
      const treeId = child.toString()
      const data = {
        current: 1,
        functionDictId: '',
        simCode: treeId,
        size: 999
      }
      getSpaceInfoList(data).then((res) => {
        if (res.data.code === 200) {
          if (typeof resolve === 'function') {
            var treeNodeData = JSON.parse(JSON.stringify(res.data.data.records))
            treeNodeData.map((e) => {
              e.leaf = true
            })
            resolve(treeNodeData)
          }
        }
      })
    },
    // 下拉树懒加载
    serverLoadNode(node, resolve) {
      console.log(node.level)
      if (node.level === 0) {
        return resolve(node.data)
      } else if (node.level > 0 && node.level < 4) {
        return resolve(node.data.children)
      } else {
        return this.spaceTreeChange(node.data, resolve)
      }
    },
    // 空间数据清除
    handleClear() {
      this.searchFrom.alarmSpaceId = ''
      this.areaName = ''
    },
    // 获取报警来源
    getAlarmSource() {
      getSourceByEmpty({}).then((res) => {
        if (res.data.code === '200') {
          this.alarmSourceOptions = res.data.data
        }
      })
    },
    // 获取事件类型以及报警来源分组
    getAlarmTypeOptions(val) {
      this.searchFrom.alarmType = []
      queryFieldsConfigList({ thirdSystemCode: val.toString() }).then((res) => {
        if (res.data.code === '200') {
          this.alarmTypeOption = res.data.data
        }
      })
    },
    handleSizeChange(val) {
      this.pageSize = val
      this.pageNo = 1
      this.getDataList()
    },
    handleCurrentChange(val) {
      this.currentPage = val
      this.getDataList()
    },
    /**
     * 设置/取消 经典案例
     * @param {*} value
     * @param {*} row
     */
    onClassicChange(row) {
      const question = row.classic === 1 ? '确定取消经典案例？' : '设置为经典案例？'
      this.doConfirm(question)
        .then(() => {
          return setAlarmRecordToClassics({
            classic: row.classic === 1 ? 0 : 1,
            alarmId: row.alarmId,
            operationSource: operatePlatform.client
          })
        })
        .then((res) => {
          if (res.data.code == 200) {
            const message = row.classic === 1 ? '已取消' : '已设置'
            this.$message.success(message)
            this.search()
          } else {
            this.$message.error(res.data.msg || '操作失败')
          }
        })
        .catch((err) => { console.error(err) })
    },
    onViewWorkOrder(row) {
      // 通知父级。切换到详情页， EmergencyWorkOrder 为工单tab
      this.$emit('detail', row.alarmId, toDetailTab.order)
    },
    /**
     * 页面通用询问框
     * @param msg 询问内容
     */
    doConfirm(msg) {
      return this
        .$confirm(msg, '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          cancelButtonClass: 'new-edition',
          confirmButtonClass: 'new-edition',
          customClass: 'confirm-box-class',
          type: 'warning'
        })
    }
  }
}
</script>
<style lang="scss" scoped>
.EmergencyDataTable {
  position: relative;
  width: 100%;
  height: 100%;
  padding: 0 40px;

  .search-form-tree {
    color: #fff !important;
    background: transparent !important;

    .el-tree-node__content:hover {
      background: transparent !important;

      .el-tree-node__label {
        color: #ffe3a6;
      }
    }
  }

  .search-form {
    height: 84px;
    width: 100%;

    &.collapsed {
      height: 33px;
    }

    .el-form-item {
      margin-bottom: 16px;
    }

    &__collapse-btn {
      margin-left: 12px;
      color: #fff;
      cursor: pointer;
    }

    &>div {
      margin-right: 10px;
    }

    ::v-deep .el-input__inner {
      background: center;
      border: 1px solid $input-border-color;
      border-radius: 0;
      color: $color;
      height: inherit;
      font-family: PingFangSC-Medium, PingFang SC;
    }

    ::v-deep .el-range-input {
      background: center;
      color: $color;
    }

    ::v-deep .el-range-separator,
    ::v-deep .el-range__icon {
      color: #3769be;
    }
  }

  .batch-control {
    height: 33px;
    margin: 16px 0;
  }

  .table-list {
    height: calc(100% - 160px);

    &.formCollapsed {
      height: calc(100% - 108px);
    }

    &__more-data {
      margin-left: 12px;
      cursor: pointer;
    }

    .work-tag {
      &--2 {
        color: #FF2D55;
      }
    }
  }
}
</style>
<style lang="scss">
.component.EmergencyDataTable {
  .EmergencyDataTable {
    &__cell--selection {
      .el-checkbox.is-disabled {
        display: none;
      }
    }
  }
}
</style>
