<template>
  <div class="regulationsText">
    <div class="regulationsText-main">
      <div class="main-title">
        <i class="el-icon-arrow-left" @click="$router.go(-1)"></i>
        <span>预案详情</span>
        <i class="el-icon-arrow-right"></i>
        <span style="color: #FFFFFF;">法规文案</span>
      </div>
      <div class="main-content">
        <span class="main-label">法规文案：</span>
        <span class="main-value" v-html="planData.regulationsText"></span>
      </div>
    </div>
  </div>
</template>

<script>
import { GetPlanDetail } from '@/utils/wartimeMode'
export default {
  name: '',
  data() {
    return {
      planData: {}
    }
  },
  computed: {

  },
  created() {
    this.getPlanDetail()
  },
  methods: {
    // 获取预案详情
    getPlanDetail() {
      GetPlanDetail({ id: this.$route.query?.id }).then(res => {
        if (res.data.code == 200) {
          this.planData = res.data.data
        }
      })
    }
  }
}

</script>

<style lang="scss" scoped>
.regulationsText {
  width: 100%;
  height: 100%;
  position: relative;
  .regulationsText-main {
    position: absolute;
    right: 0;
    top: 0;
    bottom: 0;
    width: 60%;
    background: url('@/assets/images/plan/planBgd.png') no-repeat center center / 100% 100%;
    display: flex;
    flex-direction: column;
    overflow: hidden;
  }
  .main-title {
    height: 40px;
    font-size: 14px;
    font-family: 'PingFang SC-Medium', 'PingFang SC';
    font-weight: 500;
    color: #B0E3FA;
    padding-left: 10px;
    display: flex;
    align-items: center;
    .el-icon-arrow-left {
      color: #8BDDF5;
      width: 25px;
      cursor: pointer;
      font-weight: 600;
    }
    .el-icon-arrow-right {
      font-size: 12px;
      margin: 0 4px;
    }
  }
  .main-content {
    display: flex;
    flex: 1;
    padding: 16px 60px;
    overflow: hidden;
    span {
      display: inline-block;
    }
    .main-label {
      font-weight: 400;
      font-size: 14px;
      color: #B0E3FA;
    }
    .main-value {
      flex: 1;
      overflow: auto;
      color: #fff;
    }
  }
}
</style>
