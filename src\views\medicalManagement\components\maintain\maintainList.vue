<template>
  <div class='content'>
    <el-row>
      <el-col :span="12">
        <div class="searchItem">
          <div class="itemTiele">计划信息</div>
          <el-input placeholder="计划名称/计划编码/计划名单号"></el-input>
        </div>
      </el-col>
      <el-col :span="12">
        <div class="searchItem">
          <div class="itemTiele">资产信息</div>
          <el-input placeholder="名称/编码/通用名"></el-input>
        </div>
      </el-col>
      <el-col :span="12">
        <div class="searchItem">
          <div class="itemTiele">合同编码</div>
          <el-input placeholder="合同编码"></el-input>
        </div>
      </el-col>
      <el-col :span="12">
        <div class="searchItem">
          <div class="itemTiele">执行状态</div>
          <el-input placeholder="请选择执行状态"></el-input>
        </div>
      </el-col>
      <el-col :span="12">
        <div class="searchItem">
          <div class="itemTiele">执行结果</div>
          <el-input placeholder="请选择执行结果"></el-input>
        </div>
      </el-col>
      <el-col :span="12">
        <div class="searchItem">
          <div class="itemTiele">任务日期</div>
          <el-date-picker
            v-model="value1"
            type="datetimerange"
            class="datePickerInput"
            popper-class="date-style"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期">
          </el-date-picker>
        </div>
      </el-col>
      <el-col :span="24" class="btnWrap">
        <el-button>重置</el-button>
        <el-button>查询</el-button>
        <el-link :underline="false">
          展开
          <i class="el-icon-arrow-down"></i>
        </el-link>
      </el-col>
    </el-row>
    <el-tabs v-model="activeName">
      <el-tab-pane label="巡检（一级）" name="first"></el-tab-pane>
      <el-tab-pane label="日常保养（二级）" name="second"></el-tab-pane>
      <el-tab-pane label="厂商维护（三级）" name="third"></el-tab-pane>
    </el-tabs>
    <div class="tableWrap">
      <div class="operationBtn">
        <el-button>批量巡检</el-button>
        <el-button>导出</el-button>
      </div>
      <el-table
        :data="tableData"
        height="calc(100% - 120px)"
        style="width: 100%">
        <el-table-column
          type="selection"
          width="55">
        </el-table-column>
        <el-table-column
          prop="name"
          label="计划名称"
          width="180">
        </el-table-column>
        <el-table-column
          prop="code"
          label="计划编码"
          width="180">
        </el-table-column>
        <el-table-column
          prop="taskCode"
          label="任务单号"
          width="180">
        </el-table-column>
        <el-table-column
          prop="type"
          label="执行状态">
          <template slot-scope="scope">
            <span :class="'type' + scope.row.typeCode">
              <span class="pie"></span>
              {{ scope.row.type }}
            </span>
          </template>
        </el-table-column>
        <el-table-column
          prop="result"
          label="执行结果">
          <template slot-scope="scope">
            <span :class="'type' + scope.row.resultCode">
              <span class="pie"></span>
              {{ scope.row.result }}
            </span>
          </template>
        </el-table-column>
        <el-table-column
          prop="date1"
          label="任务期限"
          sortable
          width="180">
        </el-table-column>
        <el-table-column
          prop="date2"
          label="实际执行时间"
          sortable
          width="180">
        </el-table-column>
        <el-table-column
          prop="asset"
          label="资产名称"
          width="180">
        </el-table-column>
        <el-table-column
          prop="assetCode"
          label="资产编号">
        </el-table-column>
        <el-table-column
          label="操作"
          fixed="right"
          width="180">
          <template slot-scope="scope">
            <el-link style="margin-right: 10px;" disabled>巡检</el-link>
            <el-link style="color: #409EFF;" @click="toDetailTab(scope.row)">详情</el-link>
          </template>
        </el-table-column>
      </el-table>
      <div class="sic-content-table-pagination">
        <el-pagination :current-page="currentPage" :page-sizes="[15, 20, 30, 40]"
                       :page-size="pageSize" layout="total, sizes, prev, pager, next, jumper" :total="total"
                       class="pagination" @size-change="handleSizeChange" @current-change="handleCurrentChange"></el-pagination>
      </div>
    </div>
  </div>
</template>
<script>
export default {
  data() {
    return {
      tableData: [
        {
          name: '资产巡检计划',
          code: 'YL2410301249101',
          taskCode: 'YX240232719242',
          typeCode: '0',
          type: '已执行',
          result: '合格',
          resultCode: '0',
          date1: '2024-10-30 10:40:23',
          date2: '2024-10-30 10:40:23',
          asset: '输注泵',
          assetCode: '00453659411'
        },
        {
          name: '资产巡检计划',
          code: 'YL2410301249101',
          taskCode: 'YX240232719242',
          typeCode: '0',
          type: '已执行',
          result: '合格',
          resultCode: '0',
          date1: '2024-10-30 10:40:23',
          date2: '2024-10-30 10:40:23',
          asset: '输注泵',
          assetCode: '00453659411'
        },
        {
          name: '资产巡检计划',
          code: 'YL2410301249101',
          taskCode: 'YX240232719242',
          typeCode: '1',
          type: '已过期',
          result: '--',
          resultCode: '1',
          date1: '2024-10-30 10:40:23',
          date2: '2024-10-30 10:40:23',
          asset: '输注泵',
          assetCode: '00453659411'
        },
        {
          name: '资产巡检计划',
          code: 'YL2410301249101',
          taskCode: 'YX240232719242',
          typeCode: '1',
          type: '已过期',
          result: '--',
          resultCode: '1',
          date1: '2024-10-30 10:40:23',
          date2: '2024-10-30 10:40:23',
          asset: '输注泵',
          assetCode: '00453659411'
        },
        {
          name: '资产巡检计划',
          code: 'YL2410301249101',
          taskCode: 'YX240232719242',
          typeCode: '1',
          type: '已过期',
          result: '--',
          resultCode: '1',
          date1: '2024-10-30 10:40:23',
          date2: '2024-10-30 10:40:23',
          asset: '输注泵',
          assetCode: '00453659411'
        }
      ],
      currentPage: 1,
      pageSize: 15,
      total: 5,
      value1: '',
      activeName: 'first'
    }
  },
  created() {},
  methods: {
    handleSizeChange() {},
    handleCurrentChange() {},
    toDetailTab() {
      this.$emit('openDetailComponent', 'maintainDetail')
    }
  }
}
</script>
<style lang="scss" scoped>
.content {
  padding: 20px;
  height: 100%;
  .searchItem {
    display: flex;
    align-items: center;
    margin-bottom: 20px;
    .itemTiele {
      width: 100px;
    }
    .el-input {
      max-width: calc(90% - 100px)
    }
  }
  ::v-deep .el-tabs__nav {
    .el-tabs__item {
      color: #fff !important;
    }
    .is-active {
      color: #409EFF !important;
    }
  }
  .btnWrap {
    display: flex;
    justify-content: flex-end;
    padding-right: calc(10% - 75px);
    .el-link {
      margin-left: 10px;
      color: #fff;
    }
  }
  ::v-deep .datePickerInput {
    flex: 1;
    padding: 8px 10px;
    height: 22px;
    box-sizing: content-box;
    border-radius: unset;
    background: none;
    max-width: calc(90% - 120px);
    border: 1px solid #3056A2;
    .el-input__icon,
    .el-range-separator {
      line-height: 16px;
      color: #b0e3fa;
    }
    .el-range-input {
      background: none;
      color: #a4afc1;
    }
  }
  .tableWrap {
    height: calc(100% - 280px);
    .operationBtn {
      margin-bottom: 10px;
    }
    .pie {
        display: inline-block;
        width: 8px;
        height: 8px;
        border-radius: 50%;
        margin-right: 4px;
      }
    .degree0 {
      color: #FA8B2C;
      .pie {
        background-color: #FA8B2C;
      }
    }
    .degree1 {
      color: #FF4D4F;
      .pie {
        background-color: #FF4D4F;
      }
    }
    .type0 {
      color: #52C41A;
      .pie {
        background-color: #52C41A;
      }
    }
    .type1 {
      color: #8C8C8C;
      .pie {
        background-color: #8C8C8C;
      }
    }
  }
}
</style>