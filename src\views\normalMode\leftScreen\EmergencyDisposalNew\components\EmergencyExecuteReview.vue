<template>
  <div class='component EmergencyExecuteReview'>
    <EmergencyExecuteBaseInfo table-height="220px" />

    <div class="EmergencyExecuteReview__record">
      <ul class="EmergencyExecuteReview__record__nav">
        <li :class="{ active: recordType === 'file' }" @click="switchRecordType('file')">现场记录文件</li>
        <li :class="{ active: recordType === 'talk' }" @click="switchRecordType('talk')">通话记录</li>
      </ul>
      <div class="EmergencyExecuteReview__record__content">
        <!-- 文件列表 -->
        <div v-show="recordType === 'file'" class="EmergencyExecuteReview__file">
          <FileListCard :file-list="fileList" />
        </div>
        <!-- 通话记录 -->
        <div v-show="recordType === 'talk'" class="EmergencyExecuteReview__talk">
          <el-table ref="tableRef" height="128px" :data="talkList" stripe>
            <el-table-column label="呼叫类型" prop="communicateTypeName" width="120"></el-table-column>
            <el-table-column label="通话开始时间" prop="operationStartTime"></el-table-column>
            <el-table-column label="通话时长" prop="callDuration" width="110"></el-table-column>
            <el-table-column label="通话人数" prop="callPersonNum" width="80"></el-table-column>
            <el-table-column label="操作" width="140">
              <template #default="{ row }">
                <div class="operationBtn">
                  <span style="margin-right: 10px" @click="onViewDetail(row)">详情</span>
                  <span>下载录音</span>
                </div>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>
    </div>

    <div class="EmergencyExecuteReview__comment">
      <ul class="description__content">
        <li class="full-row" style="margin-top:0">
          <div>处理结果</div>
          <div :title="operateInfo.remark">{{ operateInfo.remark | defaultVal }}</div>
        </li>
        <li class="full-row">
          <div>处理人</div>
          <div>{{ operateInfo.operationPersonName | defaultVal }}</div>
        </li>
      </ul>
    </div>

    <DialogTalkDetail :visible.sync="showDetail" :detail="talkDetail" />
  </div>
</template>


<script>

import EmergencyExecuteBaseInfo from "./EmergencyExecuteBaseInfo.vue";

export default {
  name: 'EmergencyExecuteReview',
  components: {
    DialogTalkDetail: () => import("./DialogTalkDetail"),
    FileListCard: () => import("./FileListCard"),
    EmergencyExecuteBaseInfo
  },
  data() {
    return {
      remark: '',
      recordType: 'file',
      showDetail: false,
      talkDetail: {}
    }
  },
  computed: {
    detail: function () {
      return this.$parent.detail || {}
    },
    handleProcessInfo: function () {
      return this.detail.handleProcessInfo || {}
    },
    talkList: function () {
      return this.handleProcessInfo.clientOperationLogVoList ?? []
    },
    fileList: function () {
      return this.handleProcessInfo.fileVoList ?? []
    },
    operateInfo: function () {
      return (this.detail.detail || []).find(x => x.operationType === 21) || {};
    }
  },
  filters: {
    defaultVal: function (val) {
      return val || "-"
    },
  },
  methods: {
    switchRecordType(type) {
      if (this.recordType === type) return;
      this.recordType = type;
      if (type === 'talk') {
        this.$nextTick(() => {
          this.$refs.tableRef.doLayout()
        })
      }
    },
    onViewDetail(row) {
      this.talkDetail = row;
      this.showDetail = true;
    }
  },
}
</script>

<style lang='scss' scoped>
.component.EmergencyExecuteReview {
  display: flex;
  flex-flow: column nowrap;
  height: 100%;
  overflow: hidden;

  .content-head {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px;
    height: 52px;
  }


  .description {
    &__content {
      display: flex;
      flex-flow: row wrap;

      >li {
        flex-basis: 50%;
        display: flex;
        flex-flow: row nowrap;
        margin-top: 24px;

        &.full-row {
          flex-basis: 100%;
        }

        >div:first-of-type {
          color: #B0E3FA;
          width: 100px;
          text-align: right;

          &::after {
            content: '：';
          }

        }

        >div:last-child {
          width: 0;
          flex: 1;
          overflow: hidden;
          white-space: nowrap;
          text-overflow: ellipsis;
        }
      }
    }
  }

  .EmergencyExecuteReview {
    display: flex;
    flex-flow: column nowrap;
    height: 100%;
    overflow: hidden;


    &__description {
      flex: 1;
      background: rgba(133, 145, 206, 0.15);
      overflow: hidden;
      display: flex;
      flex-flow: column nowrap;
      padding-bottom: 16px;

      &__content {
        padding: 0 16px;
        overflow: auto;
      }

      &__table {
        margin-top: 24px;

        &__tag {
          display: flex;
          align-items: center;

          >img {
            height: 16px;
            width: 16px;
            margin-right: 2px;
          }

          &--finish {
            >span {
              color: #61E29D;
            }
          }

          &--incomplete {
            >span {
              color: #FF2D55;
            }
          }
        }
      }
    }

    &__file {
      height: 100%;
      overflow: auto;

      .description__content {
        height: 100%;
        overflow: hidden;

        >li {
          height: 100%;
          overflow: hidden;
        }
      }

      &__list {
        height: 100%;
        overflow: auto !important;
        display: flex;
        flex-flow: row wrap;
      }

      &__item {
        height: 120px;
        width: 120px;
        overflow: hidden;
        margin: 0 10px 10px 0;

        >img {
          height: 100%;
          width: 100%;
          object-fit: cover;
          object-position: center;
        }
      }
    }




    &__comment {
      background: rgba(133, 145, 206, 0.15);
      margin-top: 16px;
      padding: 16px;
    }

    &__record {
      background: rgba(133, 145, 206, 0.15);
      margin-top: 16px;
      height: 200px;

      &__nav {
        display: flex;

        >li {
          flex: 1;
          height: 40px;
          line-height: 40px;
          text-align: center;
          font-size: 16px;
          color: #a4acb9;
          background: url('~@/assets/images/qhdsys/nav-bg.png') no-repeat;

          &.active {
            color: #b0e3fa;
            background: url('~@/assets/images/qhdsys/nav-bg-active.png') no-repeat;
          }

          &:first-child {
            background-position: -2px;
          }
        }
      }

      &__content {
        height: 160px;
        overflow: hidden;
        padding: 16px;
      }
    }
  }
}
</style>
