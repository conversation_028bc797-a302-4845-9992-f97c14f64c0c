<template>
  <div class="main">
    <el-dialog v-dialogDrag :modal="false" :visible.sync="dialogShow" custom-class="paramDetailsDialog main" append-to-body :close-on-click-modal="false" :before-close="closeDialog">
      <template slot="title">
        <span class="dialog-title">{{ paramData.parameterName }}</span>
      </template>
      <div class="dialog-content">
        <p class="content-title">{{ paramData.parameterName }}</p>
        <div class="search-box">
          <el-date-picker
            v-model="dateRange"
            type="daterange"
            unlink-panels
            popper-class="date-style"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            value-format="yyyy-MM-dd"
            @change="changeDataEvent"
          >
          </el-date-picker>
          <div class="date-type">
            <p v-for="item in dateTypeList" :key="item.value" class="type-item" :class="{'active-type': dateType == item.value}" @click="activeTabEvent(item.value)">{{ item.name }}</p>
          </div>
        </div>
        <div id="trend_chart" class="content-main">

        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { GetChartData } from '@/utils/centerScreenApi'
import moment from 'moment'
moment.locale('zh-cn')
import * as echarts from 'echarts'
export default {
  name: 'paramDetails',
  props: {
    dialogShow: {
      type: Boolean,
      default: false
    },
    paramData: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      // parameterId
      dateRange: [],
      dateType: 1,
      dateTypeList: [
        { name: '今天', value: 1 },
        { name: '近7天', value: 2 },
        { name: '近30天', value: 3 }
      ]
    }
  },
  created() {
    this.activeTabEvent(1)
  },
  methods: {
    getParmaData() {
      const params = {
        harvesterId: this.paramData.harvesterCode,
        paramId: this.paramData.parameterId,
        surveyCode: this.paramData.surveyCode,
        type: this.dateType == 3 ? 2 : this.dateType,
        startTime: this.dateRange[0],
        endTime: this.dateRange[1]
      }
      GetChartData(params).then(res => {
        if (res.data.code == 200) {
          this.chartOptions(res.data.data[0].data)
        }
      })
    },
    chartOptions(item) {
      const getchart = echarts.init(document.getElementById('trend_chart'))
      let option
      if (item.length && item[0].dataList.length) {
        const seriesData = []
        const xAxisData = []
        const color = ['#FA403C', '#00BC6D', '#3562DB']
        item.forEach(e => {
          xAxisData.push(e.time)
          e.dataList.forEach((params, i) => {
            if (seriesData.length < e.dataList.length) {
              seriesData.push([])
              seriesData[i] = {
                name: params.name,
                type: 'line',
                data: [{
                  name: e.time,
                  value: params.value,
                  unit: params.unit
                }],
                itemStyle: {
                  normal: {
                    borderWidth: 5,
                    color: color[i] || this.$tools.randomRgbColor()
                  }
                }
              }
            } else {
              seriesData[i].data.push({
                name: e.time,
                value: params.value || '0',
                unit: params.unit
              })
            }
          })
        })
        option = {
          color: ['#73A0FA', '#73DEB3', '#FFB761'],
          tooltip: {
            trigger: 'axis',
            axisPointer: {
              type: 'cross',
              crossStyle: {
                color: '#999'
              },
              lineStyle: {
                type: 'dashed'
              }
            },
            formatter: function (params) {
              let returnStr = params[0].name + '<br>'
              params.forEach(item => {
                returnStr += item.seriesName + ': ' + (item.data.value || '-') + ' ' + (item.data.unit || '') + '<br>'
              })
              return returnStr
            }
          },
          dataZoom: [
            {
              type: 'inside',
              start: 0,
              end: 100
            }
          ],
          grid: {
            left: '20',
            right: '70',
            bottom: '25',
            top: '20',
            containLabel: true
          },
          xAxis: {
            // type: 'time',
            data: xAxisData,
            boundaryGap: false,
            splitLine: {
              show: false
            },
            axisTick: {
              show: false
            },
            axisLine: {
              show: false
            }
          },
          yAxis: {
            type: 'value',
            axisLabel: {
              color: 'rgba(230,247,255,0.5)',
              textStyle: {
                fontSize: 12
              }
            },
            splitLine: {
              show: true,
              lineStyle: {
                type: 'dashed',
                color: 'rgba(230,247,255,0.2)'
              }
            },
            axisTick: {
              show: false
            },
            axisLine: {
              show: false
            }
          },
          series: seriesData
        }
      } else {
        option = {
          title: {
            text: '暂无数据',
            x: 'center',
            y: 'center',
            textStyle: {
              fontSize: 14,
              fontWeight: 'normal',
              color: '#999'
            }
          }
        }
      }
      getchart.clear()
      getchart.setOption(option)
      // 随着屏幕大小调节图表
      window.addEventListener('resize', () => {
        getchart.resize()
      })
    },
    activeTabEvent(val) {
      const dateList = {
        1: [moment().startOf('day').format('YYYY-MM-DD'), moment().format('YYYY-MM-DD')],
        2: [moment().subtract(6, 'days').format('YYYY-MM-DD'), moment().format('YYYY-MM-DD')],
        3: [moment().subtract(29, 'days').format('YYYY-MM-DD'), moment().format('YYYY-MM-DD')]
      }
      this.dateType = val
      this.dateRange = dateList[val]
      this.getParmaData()
    },
    changeDataEvent() {
      this.getParmaData()
    },
    // 取消按钮
    closeDialog() {
      this.$emit('paramDetailsClose')
    }
  }
}
</script>

<style lang="scss" scoped>
::v-deep(.paramDetailsDialog) {
  width: 50%;
  height: 70vh;
  margin-top: 6vh !important;
  // border: 1px solid #5996f9;
  background-color: transparent !important;
  pointer-events: auto;
  background-image: url('@/assets/images/min-bg.png');
  background-repeat: no-repeat;
  background-size: 100% 100%;
  box-shadow: none;
  .el-dialog__headerbtn {
    transform: translateX(-36px);
    width: 25px;
    height: 25px;
    background-image: url('@/assets/images/close.png');
    background-repeat: no-repeat;
    background-size: 100% 100%;
    .el-dialog__close::before {
      display: none;
    }
  }
  .dialog-title {
    margin-top: 5px;
    display: inline-block;
    width: 100%;
    text-align: center;
    transform: translateY(-6px);
    color: #cbdeed;
  }
  .dialog-title::before {
    display: none;
  }
  .el-dialog__header {
    background-color: transparent;
  }
  .el-dialog__body {
    height: calc(100% - 60px);
    max-height: fit-content;
    overflow-y: hidden;
    background-color: transparent;
    padding: 0px 80px;
  }
  .dialog-content {
    width: 100%;
    height: 88%;
    margin-top: 20px;
    background: rgba(53,98,219,0.06);
    display: flex;
    flex-direction: column;
    padding: 24px;
    .content-title{
      font-weight: 500;
      font-size: 16px;
      color: #FFFFFF;
    }
    .search-box {
      padding: 24px 0px 10px 0px;
      display: flex;
      align-items: center;
      .date-type {
        margin-left: 15px;
        display: flex;
        .type-item {
          cursor: pointer;
          padding: 8px 12px;
          font-weight: 400;
          font-size: 14px;
          color: #B0E3FA;
          margin-right: 8px;
          border: 1px solid transparent;
          background: linear-gradient( 90deg, rgba(10,132,255,0) 0%, rgba(10,132,255,0) 100%), rgba(255,255,255,0.1);
        }
        .active-type {
          color: #8BDDF5;
          background: linear-gradient( 90deg, rgba(10,132,255,0.4) 0%, rgba(10,132,255,0) 100%), rgba(255,255,255,0.1);
          border: 1px solid;
          border-image: radial-gradient(circle, rgba(171, 240, 255, 1), rgba(226, 254, 255, 1), rgba(132, 196, 203, 1), rgba(48, 151, 166, 0)) 1 1;
        }
      }
    }
    .content-main {
      flex: 1;
    }
    .el-date-editor {
      width: 300px;
      height: 35px;
      background-color: #14233e;
      border-color: rgba(133, 145, 206, 0.5);
      .el-input__icon {
        transform: translateY(-2px);
      }
      .el-range-input {
        background-color: #14233e;
        color: rgba(255, 255, 255, 0.8);
      }
      .el-range-separator {
        color: rgba(255, 255, 255, 0.8);
        transform: translateY(-2px);
      }
    }
  }
}
</style>
