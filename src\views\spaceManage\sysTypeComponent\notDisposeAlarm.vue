<template>
  <div class="notDisposeAlarm module-container">
    <ModuleCard title="报警统计" class="module-container" :floorName="!['Kongtiao', 'Electricity'].includes(roomData.tabName) ? roomData.title : ''" style="height: 30%">
      <div slot="title-right" class="title-right">
        <el-dropdown trigger="click" @command="(val) => (tagCurrent = val)">
          <span class="el-dropdown-link"> {{ tagList.find((v) => v.value == tagCurrent)?.text ?? '' }} <i class="el-icon-arrow-down"></i> </span>
          <el-dropdown-menu slot="dropdown">
            <el-dropdown-item v-for="item in tagList" :key="item.value" :command="item.value" :class="{ isBjxl: tagCurrent == item.value }">{{ item.text }}</el-dropdown-item>
          </el-dropdown-menu>
        </el-dropdown>
        <span class="viewMore" @click="onWhole(1)"></span>
      </div>
      <div slot="content" class="module-content" style="height: 100%;">
        <div id="alarmTypeCount" style="width: 100%; height: 100%"></div>
      </div>
    </ModuleCard>
    <ModuleCard :title="roomData.ssmType != 5 ? '报警排名' : '显示内容'" style="height: 35%">
      <div slot="title-right" class="title-right">
        <div class="top_type">
          <div class="top_type_item" :class="{ activeType: alarmType === 1 }" @click="alarmTypeChange(1)">
            设备类型
          </div>
          <div v-if="roomData.ssmType < 5" class="top_type_item" :class="{ activeType: alarmType === 0 }" @click="alarmTypeChange(0)">
            {{ roomData.ssmType == 4 ? '楼层' : '建筑' }}
          </div>
        </div>
      </div>
      <template slot="content">
        <div v-show="roomData.ssmType != 5" class="module-content" style="height: calc(100%)">
          <div class="main-ranking-list">
            <div v-for="item in alarmRankingList" :key="item.name" class="list-item" @click="alarmRankingSelect(item)">
              <p class="list-item-name" :style="{ color: selectAlarmRanking == item.id ? '#FFCA64' : '#fff' }">{{ item.name }}</p>
              <el-progress :percentage="item.ratio" :show-text="false" define-back-color="rgba(255,255,255,0.2)" :color="selectAlarmRanking == item.id ? '#FFCA64' : '#0A84FF'"></el-progress>
              <p class="list-item-value">{{ item.value + '个' }}</p>
            </div>
          </div>
        </div>
        <div v-show="roomData.ssmType == 5" class="module-content" style="height: calc(100%); padding: 8px 8px">
          <el-checkbox v-model="checkAll" :indeterminate="isIndeterminate" @change="changeCheckAll">全选</el-checkbox>
          <el-checkbox-group v-model="checkList" class="middle-xz" @change="changeCheckBox">
            <div v-for="(item, index) in alarmRankingList" :key="index" class="fiex-all">
              <el-checkbox :key="item.id" :label="item.id" :value="item.id" class="checkbox"></el-checkbox>
              <div class="fiex-div-font">{{ item.name }}</div>
            </div>
          </el-checkbox-group>
        </div>
      </template>
    </ModuleCard>
    <ModuleCard title="设备报警排序" style="height: 35%">
      <div slot="title-right" class="title-right">
        <span class="viewMore" @click="onWhole(2)"></span>
      </div>
      <div slot="content" class="module-content" style="height: 100%">
        <el-table v-el-table-infinite-scroll="tableLoadEvent" class="bottom-el-table" :data="tableData" height="calc(100%)" style="width: 100%" element-loading-background="rgba(0, 0, 0, 0.2)" @row-click="viewDevice">
          <!-- @row-dblclick="tableDblclick" -->
          <el-table-column prop="imsName" show-overflow-tooltip label="设备名称"></el-table-column>
          <el-table-column prop="menuCount" show-overflow-tooltip label="报警次数" sortable width="100">
            <span slot-scope="scope"> {{ scope.row.menuCount }}个 </span>
          </el-table-column>
        </el-table>
      </div>
    </ModuleCard>
    <!-- <template v-if="isAlarmList">
      <alarmListDialog :dialogVisible="isAlarmList" :initSearchData="alarmInitSearchData" @dialogVisible="configCloseTableDialog" />
    </template> -->
    <alarmTable v-if="isAlarmTable" :visible="isAlarmTable" :params="alarmInitSearchData" @showDetail="(alarm) => {alarmId = alarm.alarmId; isAlarmDetail = true}" @close="configCloseTableDialog"/>
    <alarmDetailNew v-if="isAlarmDetail" :dialogShow="isAlarmDetail" :alarmId="alarmId" @close="isAlarmDetail = false"/>
  </div>
</template>

<script>
import { GetSecureAlarmRank, GetSecureAlarmSort, GetAlarmTypeList } from '@/utils/spaceManage'
import { refrigeratorParams } from '@/assets/common/dict.js'
// import alarmListDialog from '../components/alarmListDialog.vue'
import alarmTable from '@/views/surgeonGeneral/comprehensiveStatistics/components/alarmStatistics/detail.vue'
import moment from 'moment'
moment.locale('zh-cn')
import * as echarts from 'echarts'
export default {
  name: 'notDisposeAlarm',
  components: {
    // alarmListDialog,
    alarmTable,
    alarmDetailNew: () => import('./components/alarmDetailNew.vue')
  },
  props: {
    roomData: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      alarmId: '',
      isAlarmDetail: false, // 报警弹窗
      isAlarmTable: false, // 报警弹窗
      // isAlarmList: false, // 报警弹窗
      tagCurrent: 1, // 日期类型
      alarmType: 1, // 1设备类型 0建筑
      tagList: [
        { text: '今日', value: 1 },
        { text: '本周', value: 2 },
        { text: '本月', value: 3 },
        { text: '本年', value: 4 }
      ],
      tableData: [],
      refrigeratorParams: JSON.parse(JSON.stringify(refrigeratorParams)),
      pagination: {
        total: 0,
        pageSize: 20,
        pageNo: 1
      },

      alarmRankingList: [], // 排行列表
      selectAlarmRanking: '',
      checkAll: false, // 全选
      checkList: [], // 选中数组
      isIndeterminate: true,
      alarmInitSearchData: {},
      dateObj: {
        1: [moment().format('YYYY-MM-DD'), moment().format('YYYY-MM-DD')],
        2: [moment().startOf('isoWeek').format('YYYY-MM-DD'), moment().endOf('isoWeek').format('YYYY-MM-DD')],
        3: [moment().startOf('month').format('YYYY-MM-DD'), moment().endOf('month').format('YYYY-MM-DD')],
        4: [moment().startOf('year').format('YYYY-MM-DD'), moment().endOf('year').format('YYYY-MM-DD')]
      }
    }
  },
  computed: {},
  watch: {
    roomData: {
      handler(val, oldVal) {
        this.initData()
      },
      deep: true
    },
    tagCurrent(val) {
      this.initData()
    }
  },
  mounted() {
    const params = JSON.parse(JSON.stringify(this.refrigeratorParams))
    params.forEach((i) => {
      i.Visibility = 1
    })
    try {
      window.chrome.webview.hostObjects.sync.bridge.GetPipelineSelectData(JSON.stringify(params))
    } catch (error) {}
    this.initData()
  },
  methods: {
    // 获取报警占比
    getRightCenter() {
      const ssmArr = this.roomData.ssmCodes ? this.roomData.ssmCodes.split(',') : ['0']
      const params = {
        projectCode: this.roomData.projectCode,
        startTime: this.dateObj[this.tagCurrent][0],
        endTime: this.dateObj[this.tagCurrent][1],
        searchType: 'all',
        spaceId: ssmArr[ssmArr.length - 1]
      }
      GetAlarmTypeList(params).then((res) => {
        if (res.data.code == '200') {
          this.alarmTypeCountChart(res.data.data.policeList.map(v => {
            return {
              value: v.count,
              name: v.alarmTypeName
            }
          }), res.data.data.total)
        }
      })
    },
    // 点击打开弹窗
    onWhole(type) {
      const ssmArr = this.roomData.ssmCodes ? this.roomData.ssmCodes.split(',') : ['0']
      let dateObj = {
        1: 'day',
        2: 'week',
        3: 'month',
        4: 'year'
      }
      const params = {
        projectCode: this.roomData.projectCode,
        dateType: dateObj[this.tagCurrent]
      }
      if (type == 1) {
        this.alarmInitSearchData = {
          alarmSpaceId: ssmArr[ssmArr.length - 1],
          ...params
        }
      } else {
        this.alarmInitSearchData = {
          alarmType: this.alarmType == 1 ? this.selectAlarmRanking : '',
          alarmSpaceId: this.alarmType == 1 ? ssmArr[ssmArr.length - 1] : this.selectAlarmRanking,
          ...params
        }
      }
      // this.isAlarmList = true
      this.isAlarmTable = true
      try {
        window.chrome.webview.hostObjects.sync.bridge.ShowDialog(true)
      } catch (error) {}
    },
    // 关闭弹窗
    configCloseTableDialog() {
      // this.isAlarmList = false
      this.isAlarmTable = false
      try {
        window.chrome.webview.hostObjects.sync.bridge.ShowDialog(false)
      } catch (error) {}
    },
    setCheckDataToWpf() {
      this.$emit('sendWpfData', {
        btnType: this.tagCurrent,
        spaceId: this.alarmType == 1 ? '' : this.checkList.join(','),
        entityTypeId: this.alarmType == 1 ? this.checkList.join(',') : ''
      })
    },
    // 全选
    changeCheckAll(val) {
      this.checkList = val ? Array.from(this.alarmRankingList, ({ id }) => id) : []
      this.isIndeterminate = false
      this.setCheckDataToWpf()
    },
    changeCheckBox(value) {
      const checkedCount = value.length
      this.checkAll = checkedCount === this.alarmRankingList.length
      this.isIndeterminate = checkedCount > 0 && checkedCount < this.alarmRankingList.length
      this.setCheckDataToWpf()
    },
    // 报警排名选择
    alarmRankingSelect(item) {
      this.selectAlarmRanking = item.id
      this.getSecureAlarmSort()
    },
    // 报警排名类型切换
    alarmTypeChange(type) {
      this.alarmType = type
      this.getSecureAlarmRank()
    },
    // 获取报警排序
    getSecureAlarmSort() {
      const ssmArr = this.roomData.ssmCodes ? this.roomData.ssmCodes.split(',') : ['0']
      let params = {
        spaceId: this.alarmType == 1 ? ssmArr[ssmArr.length - 1] : this.selectAlarmRanking,
        alarmEntityTypeId: this.alarmType == 1 ? this.selectAlarmRanking : '',
        // menuCode: this.roomData.menuCode,
        // modelCoding: this.roomData.modelCoding,
        projectCode: this.roomData.projectCode.split(','),
        dateType: this.tagCurrent,
        page: {
          current: this.pagination.pageNo,
          size: this.pagination.pageSize
        },
        monitorFlag: 1
      }
      if (this.roomData.isDevice == 0) params.alarmObjectId = this.roomData.deviceId // 根据单独设备（设备视角）
      if (this.pagination.pageNo === 1) this.tableData = []
      console.log(11111, this.tableData)
      GetSecureAlarmSort(params).then((res) => {
        if (res.data.code === '200') {
          res.data.data.list.map(item => item.menuCount = Number(item.menuCount))
          this.tableData = this.tableData.concat(res.data.data.list)
          this.pagination.total = res.data.data.totalCount
        }
      })
    },
    tableRowClick(row) {
      return
      // 如果关联了设备即跳转设备详情页(5/20演示模式数据)
      const demoData = {
        '汇流排二氧化碳': {
          name: '二氧化碳汇流排',
          code: '0100103_-182782'
        },
        '储罐1': {
          name: '低温液体贮槽（1号）',
          code: '01_-28564'
        }
      }
      if (Object.keys(demoData).includes(row.imsName)) {
        const params = {
          DeviceCode: demoData[row.imsName].code,
          DeviceName: demoData[row.imsName].name,
          projectCode: this.roomData.projectCode
        }
        try {
          window.chrome.webview.hostObjects.sync.bridge.EquipmentRealView(JSON.stringify(params))
        } catch (error) {}
      } else {
        // this.$message.warning('该设备未关联资产!')
      }
    },
    // 获取报警排名
    getSecureAlarmRank() {
      const ssmArr = this.roomData.ssmCodes ? this.roomData.ssmCodes.split(',') : ['0']
      let params = {
        spaceId: ssmArr[ssmArr.length - 1],
        // menuCode: this.roomData.menuCode,
        // modelCoding: this.roomData.modelCoding,
        projectCode: this.roomData.projectCode.split(','),
        dateType: this.tagCurrent,
        monitorFlag: 1,
        spaceLevel: this.roomData.ssmType
      }
      if (this.roomData.isDevice == 0) params.alarmObjectId = this.roomData.deviceId // 根据单独设备（设备视角）
      this.alarmRankingList = []
      GetSecureAlarmRank(params)
        .then((res) => {
          const resData = res.data
          if (resData.code === '200') {
            if (resData.data?.typeList?.length || resData.data?.unitList?.length) {
              const arr = resData.data[this.alarmType == 1 ? 'typeList' : 'unitList'].map((v) => {
                return {
                  id: v[this.alarmType == 1 ? 'alarmEntityTypeId' : 'unitId'],
                  name: v[this.alarmType == 1 ? 'alarmEntityTypeName' : 'unitName'],
                  value: Number(v.menuCount)
                }
              })
              arr.sort((a, b) => {
                return b.value - a.value
              })
              const total = arr.reduce((sum, num) => {
                return sum + num.value
              }, 0)
              arr.forEach((item) => {
                item.ratio = (item.value / total) * 100
              })
              this.alarmRankingList = arr
              this.selectAlarmRanking = arr[0]?.id ?? ''
              this.checkAll = true
              this.isIndeterminate = false
              this.checkList = this.alarmRankingList.map((v) => v.id)
            } else {
              this.alarmRankingList = []
              this.selectAlarmRanking = ''
              this.checkAll = false
              this.isIndeterminate = false
              this.checkList = []
            }
            this.getSecureAlarmSort()
            this.$emit('sendWpfData', {
              btnType: this.tagCurrent,
              spaceId: this.alarmType == 1 ? '' : this.checkList.join(','),
              entityTypeId: this.alarmType == 1 ? this.checkList.join(',') : ''
            })
          }
        })
        .catch(() => {
          this.$emit('sendWpfData', {
            btnType: this.tagCurrent,
            spaceId: '',
            entityTypeId: ''
          })
        })
    },
    // 显示设备位置
    viewDevice(item) {
      // 殊情况判断
      // detailsNav：由具体模块控制详情内显示什么导航
      let detailsNav = []
      if (this.roomData.tabName == 'SpaceChair') {
        detailsNav = [
          { name: '详情', component: 'assetDetails' },
          {name: '报警', component: 'assetAlarmDetails' }
        ]
      } else if (this.roomData.tabName == 'SecuritySys') {
        detailsNav = [
          { name: '详情', component: 'basicAccountComponent' },
          {name: '报警', component: 'assetAlarmDetails' }
        ]
      }
      if (item.alarmObjectId) {
        let params = {
          type: detailsNav.length ? 'manyMove' : 'move',
          assetsId: item?.alarmObjectId ?? '',
          assetName: item?.imsName,
          detailsNav
        }
        this.$emit('roomEvent', params)
        try {
          window.chrome.webview.hostObjects.sync.bridge.EquipmentRealView(JSON.stringify(params))
        } catch (error) {}
      } else {
        // this.$message.warning('该设备未关联资产!')
      }
    },
    alarmTypeCountChart(list, sum) {
      const myChart = echarts.init(document.getElementById('alarmTypeCount'))
      let option
      if (list.length) {
        option = {
          // backgroundColor: '',
          color: ['#E88D6B', '#FFCA64', '#5E89EE', '#0A84FF', '#61E29D'],
          title: {
            text: '{a|' + sum + '}',
            subtext: '{a|报警总数}',
            x: '13%',
            y: '40%',
            textStyle: {
              width: 90,
              rich: {
                a: {
                  align: 'center',
                  fontSize: 13,
                  color: '#FFFFFF'
                }
              }
            },
            subtextStyle: {
              width: 90,
              rich: {
                a: {
                  align: 'center',
                  color: 'rgba(255,255,255,0.6)',
                  fontSize: 12
                }
              }
            }
          },
          legend: {
            type: 'scroll',
            orient: 'vertical',
            top: 'center',
            left: '50%',
            width: '60%',
            height: '90%',
            data: list.map(v => v.name),
            itemWidth: 8,
            itemHeight: 8,
            itemGap: 16,
            pageTextStyle: {
              color: '#fff'
            },
            textStyle: {
              //  fontSize: 18,//字体大小
              color: '#B3C2DD' //  字体颜色
            },
            formatter: function (name) {
              var oa = option.series[2].data
              var num = oa.reduce((sum, e) => sum + e.value, 0)
              for (var i = 0; i < option.series[2].data.length; i++) {
                if (name === oa[i].name) {
                  return ' ' + name + '  ' + oa[i].value + '次  ' + ((oa[i].value / num) * 100).toFixed(2) + '%'
                }
              }
            }
          },
          tooltip: {
            show: false
          },
          series: [
            {
              type: 'pie',
              name: 'TypeB', // 内层细圆环2
              radius: ['51%', '52%'],
              center: ['25%', '50%'],
              hoverAnimation: false,
              clockWise: false,
              itemStyle: {
                color: 'rgba(255,255,255,0.2)'
              },
              label: {
                show: false
              },
              data: [100]
            },
            {
              type: 'pie',
              name: 'TypeA', // 最外层细圆环
              hoverAnimation: false,
              clockWise: false,
              radius: ['70%', '71%'],
              center: ['25%', '50%'],
              itemStyle: {
                color: 'rgba(255,255,255,0.2)'
              },
              label: {
                show: false
              },
              data: [100]
            },
            {
              name: 'content',
              type: 'pie',
              clockWise: false,
              radius: ['55%', '67%'],
              center: ['25%', '50%'],
              hoverAnimation: true,
              data: list,
              label: {
                show: false
              },
              itemStyle: {
                normal: {
                  borderWidth: 2, // 间距的宽度
                  borderColor: 'rgba(40,48,65,1)' // 背景色
                }
              }
            },
            {
              // 内圆
              type: 'pie',
              radius: '47%',
              center: ['25%', '50%'],
              hoverAnimation: false,
              itemStyle: {
                color: 'rgba(133,145,206,0.15)'
              },
              label: {
                show: false
              },
              tooltip: {
                show: false
              },
              data: [100]
            }
          ]
        }
      } else {
        option = {
          title: {
            text: '暂无数据',
            x: 'center',
            y: 'center',
            textStyle: {
              fontSize: 14,
              fontWeight: 'normal',
              color: '#999'
            }
          }
        }
      }
      myChart.off('mouseover')
      myChart.off('mouseout')
      myChart.clear()
      myChart.setOption(option)
      // 随着屏幕大小调节图表
      window.addEventListener('resize', () => {
        myChart.resize()
      })
      myChart.on('mouseover', (e) => {
        if (e.seriesName == 'content') {
          option.title.text = '{a|' + list[e.dataIndex].value + '}'
          option.title.subtext = '{a|' + list[e.dataIndex].name + '}'
          myChart.setOption(option)
        }
      })
      myChart.on('mouseout', (e) => {
        if (e.seriesName == 'content') {
          option.title.text = '{a|' + sum + '}'
          option.title.subtext = '{a|报警总数}'
          myChart.setOption(option)
        }
      })
      myChart.off('click')
      myChart.on('click', (params) => {
        // this.stopRecord(params, 'elevatorStopCount1')
      })
      myChart.off('legendselectchanged')
      myChart.on('legendselectchanged', (params) => {
        // this.stopRecord(params, 'elevatorStopCount2')
      })
    },
    initData() {
      this.pagination.pageNo = 1
      this.getSecureAlarmRank()
      this.getRightCenter()
    },
    tableLoadEvent() {
      if (this.pagination.total > this.pagination.pageNo * this.pagination.pageSize) {
        this.pagination.pageNo++
        this.getSecureAlarmSort()
      }
    },
    tableDblclick(row) {
      this.goTo('/EmergencyDisposal', { projectCode: this.roomData.projectCode, pageType: 2 })
    },
    goTo(path, query) {
      this.$router.push({
        path,
        query
      })
    }
  }
}
</script>

<style lang="scss" scoped>
@import '../style/module.scss';
.notDisposeAlarm {
  width: 100%;
  height: 100%;
  ::v-deep .el-image {
    height: 100%;
    .el-image__error {
      background: transparent !important;
    }
  }
  .title-right {
    display: flex;
    align-items: center;
  }
  .top_type {
    width: 100%;
    margin: 0 auto;
    /* background-color: rgba(1, 11, 59, 0.5); */
    display: flex;
    justify-content: space-evenly;
    flex-wrap: wrap;
    .top_type_item {
      cursor: pointer;
      margin-right: 6px;
      padding: 6px 8px;
      background: rgba(255, 255, 255, 0.1) linear-gradient(90deg, rgba(10, 132, 255, 0) 0%, rgba(10, 132, 255, 0) 100%);
      font-size: 14px;
      font-weight: 400;
      color: #b0e3fa;
      line-height: 14px;
      border: 1px solid transparent;
    }
    .activeType {
      background: rgba(255, 255, 255, 0.1) linear-gradient(90deg, rgba(10, 132, 255, 0.4) 0%, rgba(10, 132, 255, 0) 100%);
      border: 1px solid;
      border-image: radial-gradient(circle, rgba(171, 240, 255, 1), rgba(226, 254, 255, 1), rgba(132, 196, 203, 1), rgba(48, 151, 166, 0)) 1 1;
    }
  }
  .main-ranking-list {
    width: 100%;
    height: calc(100%);
    padding: 8px 8px;
    box-sizing: border-box;
    overflow: auto;
    .list-item {
      cursor: pointer;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-bottom: 8px;
      .list-item-name {
        width: 92px;
        font-size: 14px;
        font-weight: 400;
        color: #ffffff;
        line-height: 20px;
        text-align: right;
      }
      .list-item-value {
        font-size: 14px;
        font-weight: 400;
        color: rgba(255, 255, 255, 0.8);
        text-align: right;
      }
      .el-progress {
        flex: 1;
        margin: 0px 16px;
      }
    }
  }
  .main-ranking-list::-webkit-scrollbar {
    display: none; /* Chrome Safari */
  }
  ::v-deep .middle-xz {
    display: flex;
    flex-direction: column;
    max-height: calc(100% - 16px);
    overflow-y: auto;
    .fiex-all {
      display: flex;
      align-items: center;
      margin-top: 8px;
      .fiex-div-font {
        height: 20px;
        font-size: 14px;
        font-family: PingFang SC-Medium, PingFang SC;
        font-weight: 500;
        color: #ffffff;
        line-height: 20px;
        text-overflow: ellipsis;
        white-space: nowrap;
        overflow: hidden;
        margin-left: 10px;
      }

      .checkbox {
        width: 14px;
        height: 14px;

        .el-checkbox__input {
          .el-checkbox__inner {
            box-shadow: 0px 0px 3px 0px #78fff8;
            opacity: 1;
            border: 1px solid #52fffc;
          }
        }
      }
      .el-checkbox__label {
        display: none;
      }
    }
  }
}
// 滚动条的宽度
::v-deep .el-table__body-wrapper::-webkit-scrollbar {
  width: 6px; // 横向滚动条
  height: 6px; // 纵向滚动条 必写
}

// 注意:hover
::v-deep .el-table__body-wrapper:hover::-webkit-scrollbar {
  width: 6px; // 横向滚动条
  height: 6px; // 纵向滚动条 必写
}
::v-deep .el-table__body-wrapper::-webkit-scrollbar-corner,
::v-deep .el-table__body-wrapper::-webkit-scrollbar-track {
  background-color: transparent;
}
// 滚动条的滑块
// ::v-deep .el-table__body-wrapper::-webkit-scrollbar-thumb {
//   background-color: transparent;
// }
// ::v-deep .el-table__body-wrapper:hover::-webkit-scrollbar {
//   background-color: transparent;
// }
// .module-header {
//   width: 100%;
//   background: url('~@/assets/images/qhdsys/bg-bt.png') no-repeat center / 100% 100%;
.title-right {
  align-items: center;
}
.el-dropdown-link {
  font-size: 14px;
  font-weight: 300;
  color: #ffffff;
  line-height: 16px;
  .el-icon-arrow-down {
    font-size: 12px;
  }
}
.viewMore {
  cursor: pointer;
  display: inline-block;
  width: 23px;
  height: 23.5px;
  margin-left: 10px;
  background: url('@/assets/images/order_more.png') no-repeat center / 100% 100%;
}
// }
.title-left {
  padding-left: 20px;
  .showFloorName {
    width: 100%;
    text-overflow: ellipsis;
    white-space: nowrap;
    overflow: hidden;
  }
}
.middle-right {
  display: flex;
  margin-right: 7px;
}
.is-activeTab {
  background: url('@/assets/images/qhdsys/bg-kj.png') no-repeat !important;
}
.middle-right div:hover {
  cursor: pointer;
}
.middle-right div {
  width: 55px;
  height: 26px;
  line-height: 26px;
  text-align: center;
  background: rgba(255, 255, 255, 0.1) linear-gradient(90deg, rgba(10, 132, 255, 0) 0%, rgba(10, 132, 255, 0) 100%);
  border-radius: 0px 0px 0px 0px;
  opacity: 1;
  font-size: 14px;
  font-family: HarmonyOS Sans SC-Regular, HarmonyOS Sans SC;
  font-weight: 400;
  color: #8bddf5;
}

.middle-right div:nth-child(2) {
  margin: 0 3px;
}
.middle-right div:nth-child(3) {
  margin-right: 3px;
}
::v-deep .bottom-el-table {
  border: 0 !important;

  .el-table__header-wrapper {
    .el-table__header {
      .has-gutter {
        tr {
          background: transparent;
        }

        tr th {
          padding: 0;
          height: 32px;
          background: rgba(133, 145, 206, 0.15) !important;
          border-bottom: 1px solid rgba(255, 255, 255, 0.5);

          .cell {
            font-size: 14px;
            font-family: HarmonyOS Sans SC-Bold, HarmonyOS Sans SC;
            font-weight: bold;
            color: #8bddf5;
          }
        }
      }
    }
  }
}
::v-deep .el-table__body-wrapper {
  background: transparent;
  // height: 222px !important;
  overflow: hidden;
  overflow-y: auto;
  td.el-table__cell {
    border: 0 !important;
  }
  .el-table__body {
    background: transparent;

    tbody {
      background: transparent;

      .el-table__row {
        // background-color: rgba(255, 255, 255, 0.2);
        background: transparent;
        border: 0;

        td {
          border: 0;
          padding: 0;
          height: 30px;

          div {
            line-height: 30px;
            font-size: 14px;
            font-family: HarmonyOS Sans SC-Regular, HarmonyOS Sans SC;
            font-weight: 400;
            color: #ffffff;
          }
        }
      }

      .el-table__row:nth-child(2n - 1) {
        background: rgba(168, 172, 171, 0.08);
      }

      .el-table__row:hover {
        border: 0;
        opacity: 1;
        cursor: pointer;

        td div {
          color: rgba(255, 202, 100, 1);
        }
      }
    }
  }
}
</style>
