<template>
  <div class="main">
    <el-dialog
      :modal="false"
      :visible.sync="dialogShow"
      custom-class="mainDialog main"
      append-to-body
      :close-on-click-modal="false"
      :before-close="closeDialog"
      class="all-table-componentList"
    >
      <template slot="title">
        <span class="dialog-title">巡检任务列表</span>
      </template>
      <div class="dialog-content">
        <el-table
          :data="tableData"
          height="calc(100% - 40px)"
          :cell-style="$tools.setCell(3)"
          :header-cell-style="$tools.setHeaderCell(3)"
          style="width: 100%"
          v-loading="tableLoading"
          element-loading-background="rgba(0, 0, 0, 0.2)"
        >
          <el-table-column label="序号" type="index" width="80" align="center">
            <template slot-scope="scope">
              <span>{{ (currentPage - 1) * pageSize + scope.$index + 1 }}</span>
            </template>
          </el-table-column>
          <el-table-column align="center" prop="taskName" show-overflow-tooltip label="任务名称"></el-table-column>
          <!-- <el-table-column align="center" prop="planTypeName" show-overflow-tooltip label="计划类型"></el-table-column> -->
          <!-- <el-table-column align="center" prop="planName" show-overflow-tooltip label="计划名称"></el-table-column> -->
          <el-table-column align="center" prop show-overflow-tooltip label="周期类型" width="80">
            <template slot-scope="scope">
              <span>{{ cycleTypeFn(scope.row) }}</span>
            </template>
          </el-table-column>
          <el-table-column align="center" prop="taskEndTime" show-overflow-tooltip label="应巡日期"></el-table-column>
          <!-- <el-table-column align="center" prop show-overflow-tooltip label="应巡日期">
            <template slot-scope="scope">
              <span>{{ scope.row.taskStartTime + '-' + scope.row.taskEndTime }}</span>
            </template>
          </el-table-column> -->
          <el-table-column align="center" show-overflow-tooltip label="巡检小组/人员">
            <template slot-scope="scope">
              <span>{{ scope.row.planPersonName || filterDepartment(scope.row) }}</span>
            </template>
          </el-table-column>
          <el-table-column align="center" prop="totalCount" show-overflow-tooltip label="应巡点数" width="80"></el-table-column>
          <el-table-column align="center" prop="hasCount" show-overflow-tooltip label="实巡点数" width="80"></el-table-column>
          <el-table-column align="center" prop="anomalyCount" show-overflow-tooltip label="异常点数" width="80"></el-table-column>
          <el-table-column align="center" show-overflow-tooltip label="完成状态">
            <template slot-scope="scope">
              <span
                class="table_using"
                :class="{
                  font_color: scope.row.taskStatus == '1'
                }"
                >{{ scope.row.taskStatus == '1' ? '未完成' : '已完成' }}</span
              >
            </template>
          </el-table-column>
          <el-table-column align="center" prop="upDepartName" show-overflow-tooltip label="操作" width="80">
            <template slot-scope="scope">
              <el-link type="primary" :underline="false" style="color: #FFE3A6;" @click="viewDetails(scope.row)">详情</el-link>
            </template>
          </el-table-column>
        </el-table>
        <el-pagination
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="currentPage"
          :page-sizes="[15, 20, 30, 40]"
          :page-size="pageSize"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
          class="pagination"
        ></el-pagination>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import { getDeptTaskList } from '@/utils/centerScreenApi.js'
export default {
  components: {},
  props: {
    dialogShow: {
      type: Boolean,
      default: false
    },
    rowData: {
      type: Object,
      default: () => {
        return {
          deptId: '',
          dateType: ''
        }
      }
    }
  },
  data() {
    return {
      tableLoading: false,
      tableData: [],
      currentPage: 1,
      pageSize: 15,
      total: 0,
      cycleTypeArr: [
        {
          cycleType: 8,
          label: '单次'
        },
        {
          cycleType: 6,
          label: '每日'
        },
        {
          cycleType: 0,
          label: '每周'
        },
        {
          cycleType: 2,
          label: '每月'
        },
        {
          cycleType: 3,
          label: '季度'
        },
        {
          cycleType: 5,
          label: '全年'
        }
      ]
    }
  },
  created() {
    this.getListData()
  },
  methods: {
    closeDialog() {
      this.$emit('closeListDialog')
    },
    handleSizeChange(val) {
      this.pageSize = val
      this.getListData()
    },
    handleCurrentChange(val) {
      this.currentPage = val
      this.getListData()
    },
    getListData() {
      const params = {
        pageNo: this.currentPage,
        pageSize: this.pageSize,
        distributionTeamId: this.rowData.deptId,
        dateType: this.rowData.dateType
      }
      getDeptTaskList(params).then((res) => {
        const data = res.data
        this.tableLoading = false
        if (data.code === '200') {
          this.tableData = data.data.list
          this.total = data.data.sum
        } else {
          this.$message({
            message: data.msg,
            type: 'warning'
          })
        }
      })
    },
    // 周期类型
    cycleTypeFn(row) {
      const item = this.cycleTypeArr.filter((i) => i.cycleType === row.cycleType)
      return item[0].label
    },
    // 科室去重
    filterDepartment(department) {
      const nameArr = department.distributionTeamName.split(',')
      const teamName = Array.from(new Set(nameArr))
      return teamName.toString()
    },
    viewDetails(row) {
      this.$emit('setTaskId', row.id)
    }
  }
}
</script>
<style lang="scss" scoped>
.all-table-componentList {
  pointer-events: none;
  ::v-deep .el-image__error {
    background: center;
  }
}
.preview-image {
  z-index: 9999 !important;
  ::v-deep .el-image-viewer__canvas {
    color: #fff;
  }
}
::v-deep .mainDialog {
  width: 70%;
  height: 88vh;
  margin-top: 6vh !important;
  background: url('@/assets/images/qhdsys/bottom-table-bg.png') no-repeat;
  background-size: 100% 100%;
  padding: 0 35px;
  pointer-events: auto;
  .dialog-title {
    display: inline-block;
    width: 38%;
  }
  .title-tabs {
    .el-tabs__header {
      margin: 0;
      .el-tabs__active-bar {
        background: #ffe3a6;
      }
      .el-tabs__item.is-active {
        color: #ffe3a6;
      }
      .el-tabs__item {
        font-size: 14px;
        font-family: PingFang-SC-Medium, PingFang-SC;
        font-weight: 500;
        color: #ffffff;
      }
    }
    .el-tabs__nav-wrap::after {
      display: none;
    }
  }
  .el-dialog__body {
    padding: 0;
    height: calc(100% - 60px);
    max-height: fit-content;
    overflow-y: hidden;
    .dialog-content {
      width: 100%;
      height: 100%;
      .statistics-top {
        height: 150px;
        width: 100%;
        display: flex;
        justify-content: space-around;
        > div {
          width: max-content;
          // width: 10%;
          height: 100%;
          padding: 10px 0;
          box-sizing: border-box;
          display: flex;
          justify-content: space-evenly;
          flex-direction: column;
          p {
            text-align: center;
            font-size: 1rem;
            font-family: DIN-Bold, DIN;
            font-weight: bold;
            color: #ffffff;
          }
          .green-font {
            font-size: 1.25rem;
            color: #ffe3a6;
          }
        }
      }
      .el-table {
        border: none !important;
      }
    }
  }
}
::v-deep .detailDialog {
  width: 60%;
  height: 80vh;
  margin-top: 7vh !important;
  background: url('@/assets/images/table-bg.png') no-repeat;
  background-size: 100% 100%;
  pointer-events: auto;
  .el-dialog__body {
    padding: 10px 50px;
    height: calc(100% - 60px);
    max-height: 80vh;
    .el-table th.el-table__cell > .cell {
      width: max-content;
    }
    .el-table td.el-table__cell .cell div {
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
  }
}
</style>
