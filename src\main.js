/*
 * @Author: <PERSON>
 * @Date: 2021-12-14 15:17:56
 * @Last Modified by: <PERSON>
 * @Last Modified time: 2022-03-31 11:49:27
 */
import Vue from 'vue'
import './assets/common/polyfills.js'
// 引入element-ui组件库
import ElementUI from 'element-ui'
import App from './App.vue'
import store from './store'
import router from './router'
import Tool from '@/assets/common/utils'
import '@/assets/common/jsmpeg.min.js'
import 'meta2d-vue/style.css'
import 'element-ui/lib/theme-chalk/index.css'
import '@assets/sino-ui/src/index.scss'
import '@assets/sino-ui/iconfont/iconfont.css'
import '@assets/sino-ui/iconfont/iconfont.js'
import './router/config'
import '@assets/common/dialogDrag'
import '@assets/common/rem'
import elTableInfiniteScroll from 'el-table-infinite-scroll'
// ----引入vue-video-player-------
// import VideoPlayer from 'vue-video-player'
// require('video.js/dist/video-js.css')
// require('vue-video-player/src/custom-theme.css')
// Vue.use(VideoPlayer)
// const hls = require('videojs-contrib-hls')
// Vue.use(hls)

// 升级成主应用
import microApp from '@micro-zoe/micro-app'
microApp.start()

// 全局组件自动注册
import './components/autoRegister'

import 'virtual:svg-icons-register'

import meta2d from 'meta2d-vue/meta2d-vue.es.js'
Vue.use(meta2d)

Vue.use(ElementUI)

Vue.use(elTableInfiniteScroll)
// -------------end--------------

Vue.prototype.$tools = Tool

Vue.config.productionTip = false
var EventBus = new Vue()
Object.defineProperties(Vue.prototype, {
  $bus: {
    get: function () {
      return EventBus
    }
  }
})

// 输出版本信息
try {
  console.log('%c ===== 客户端系统版本信息 =====', 'background:#35495e; color:#fff; padding: 5px;')
  console.log('%c 打包分支: ' + GIT_BRANCH, 'color:#e74c3c; font-weight:bold;')
  console.log('%c 提交ID: ' + GIT_COMMIT, 'color:#e74c3c; font-weight:bold;')
  console.log('%c 最后提交时间: ' + GIT_LAST_COMMIT_TIME, 'color:#e74c3c; font-weight:bold;')
  console.log('%c 打包时间: ' + BUILD_TIME, 'color:#e74c3c; font-weight:bold;')
  console.log('%c 打包用户: ' + GIT_USER, 'color:#e74c3c; font-weight:bold;')
  console.log('%c 运行环境: ' + NODE_ENV, 'color:#e74c3c; font-weight:bold;')
  console.log('%c ==================================', 'background:#35495e; color:#fff; padding: 5px;')
} catch (e) {
  console.log('版本信息获取失败', e)
}

new Vue({
  router,
  store,
  render: (h) => h(App)
}).$mount('#app')
