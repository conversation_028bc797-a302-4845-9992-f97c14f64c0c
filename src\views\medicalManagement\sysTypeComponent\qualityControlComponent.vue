<template>
  <div class='content'>
    <div class="insp">
      <div class="pieHeader">
        <div class="headerLeft">
          <span :class="pieType == 0 ? 'activType' : ''" @click="cahngePieType(0)">计量任务</span>
          <span :class="pieType == 1 ? 'activType' : ''" @click="cahngePieType(1)">不良事件</span>
          <span :class="pieType == 2 ? 'activType' : ''" @click="cahngePieType(2)">性能检测</span>
        </div>
        <div class="headerRight">
          <el-dropdown trigger="click" @command="(val) => (tagCurrent = val)">
            <span style="color: #fff;"> 本年 <i class="el-icon-arrow-down"></i> </span>
            <el-dropdown-menu slot="dropdown">
              <el-dropdown-item :command="'1'" :class="{ isBjxl: tagCurrent == '1' }">今天</el-dropdown-item>
              <el-dropdown-item :command="'2'" :class="{ isBjxl: tagCurrent == '2' }">本周</el-dropdown-item>
              <el-dropdown-item :command="'3'" :class="{ isBjxl: tagCurrent == '3' }">本月</el-dropdown-item>
              <el-dropdown-item :command="'4'" :class="{ isBjxl: tagCurrent == '4' }">本年</el-dropdown-item>
              <el-dropdown-item :command="'5'" :class="{ isBjxl: tagCurrent == '5' }">自定义</el-dropdown-item>
            </el-dropdown-menu>
          </el-dropdown>
          <svg-icon class="right-expand" name="right-expand" @click="allTableChange" />
        </div>
      </div>
      <div v-if="pieType != 1" id="workOrderStatisticsEcharts" ref="workOrderStatisticsEcharts"></div>
      <template v-else>
        <div class="pieTitle">
          <span>事件状态统计&nbsp;&nbsp;</span>
          <el-dropdown trigger="click" @command="(val) => (tagCurrent = val)">
            <span style="color: #fff;"> 全部科室 <i class="el-icon-arrow-down"></i> </span>
            <el-dropdown-menu slot="dropdown">
              <el-dropdown-item :command="'1'" :class="{ isBjxl: tagCurrent == '3' }">全部科室</el-dropdown-item>
            </el-dropdown-menu>
          </el-dropdown>
        </div>
        <div id="typeStatistics" ref="typeStatistics">
        </div>
        <div class="pieTitle">
          <span>事件上报状态</span>
        </div>
        <div id="reportedStatistics" ref="reportedStatistics">
        </div>
      </template>
    </div>
    <ModuleCard v-if="pieType != 1" ref="taskBox" title="巡检任务清单" :hasExpand="true" class="task-list module-container"
      @emit-expand="allTableChange()">
      <div slot="content" class="task-list-box" style="height: 100%">
        <div class="statistics">
          <div v-for="(item, index) in statisticsData" :key="index" class="statisticsItem">
            <span style="font-size: 14px;">{{ item.name }}</span>
            <span class="count">{{ item.count }}</span>
          </div>
        </div>
        <div class="module-content" style="flex: 1; height: 100%">
          <el-table :data="tableData" height="calc(100% - 44px)" style="width: 100%">
            <el-table-column prop="name" width="80" label="任务名称">
            </el-table-column>
            <el-table-column prop="type" width="80" label="周期类型">
            </el-table-column>
            <el-table-column prop="date" label="任务期限">
            </el-table-column>
            <el-table-column prop="accomplishType" width="80" label="完成状态">
            </el-table-column>
          </el-table>
        </div>
      </div>
    </ModuleCard>
    <ModuleCard v-else ref="taskBox" title="不良事件列表" :hasExpand="true" class="event-list module-container"
      @emit-expand="allTableChange()">
      <div slot="content" class="task-list-box" style="height: 100%">
        <div class="module-content" style="flex: 1; height: 100%">
          <el-table :data="eventTableData" height="calc(100% - 44px)" style="width: 100%">
            <el-table-column prop="name" width="80" label="资产名称">
            </el-table-column>
            <el-table-column prop="type" width="80" label="资产状态">
            </el-table-column>
            <el-table-column prop="date" label="审核状态">
            </el-table-column>
            <el-table-column prop="accomplishType" width="80" label="上报状态">
            </el-table-column>
          </el-table>
        </div>
      </div>
    </ModuleCard>
    <DialogFrame :visible.sync="dialogFrame.show" :breadcrumb="breadcrumb" :title="dialogFrame.title"
      @update:visible="update" @back="back">
      <component :is="activeComponent" :pieType="pieType" @openDetailComponent="openDetailComponent"></component>
    </DialogFrame>
  </div>
</template>
<script>
import * as echarts from 'echarts'
export default {
  components: {
    DialogFrame: () => import('@/components/common/DialogFrame'),
    meteringList: () => import('../components/metering/meteringList.vue'),
    maintainDetail: () => import('../components/maintain/maintainDetail.vue')
  },
  data() {
    return {
      tagCurrent: '4',
      pieType: 0,
      dialogFrame: {
        show: false,
        title: ''
      },
      tableLoading: false,
      tableData: [
        {
          name: '2024年09月申请单',
          type: '每日任务',
          date: '2024-08-30 08:00:00 ~ 202408-30 23:59:59',
          accomplishType: '已完成'
        },
        {
          name: '2024年01月申请单',
          type: '每日任务',
          date: '2024-08-30 08:00:00 ~ 202408-30 23:59:59',
          accomplishType: '已完成'
        },
        {
          name: 'jiliang',
          type: '每周任务',
          date: '2024-08-30 08:00:00 ~ 202408-30 23:59:59',
          accomplishType: '已完成'
        },
        {
          name: 'JL',
          type: '每日任务',
          date: '2024-08-30 08:00:00 ~ 202408-30 23:59:59',
          accomplishType: '已完成'
        },
        {
          name: '临时计划',
          type: '每月任务',
          date: '2024-08-30 08:00:00 ~ 202408-30 23:59:59',
          accomplishType: '已完成'
        }
      ],
      breadcrumb: [{ label: '计量任务', name: 'meteringList' }],
      activeComponent: 'meteringList',
      statisticsData: [
        {
          name: '全部任务',
          count: 40
        },
        {
          name: '未执行',
          count: 40
        },
        {
          name: '已执行',
          count: 40
        },
        {
          name: '已停用',
          count: 40
        },
        {
          name: '已过期',
          count: 40
        }
      ],
      selectValue: '0',
      options: [
        {
          value: '0',
          label: '全部科室'
        }
      ],
      eventTableData: [
        {
          name: '监护仪',
          type: '使用中',
          date: '已通过',
          accomplishType: '未上报'
        },
        {
          name: '监护仪',
          type: '使用中',
          date: '已通过',
          accomplishType: '未上报'
        },
        {
          name: '监护仪',
          type: '使用中',
          date: '已通过',
          accomplishType: '未上报'
        },
        {
          name: '监护仪',
          type: '使用中',
          date: '已通过',
          accomplishType: '未上报'
        }

      ]
    }
  },
  created() {
    this.$nextTick(() => {
      this.getAlarmSourceEchart()
    })
  },
  methods: {
    showDialog() {
      this.dialogFrame.show = true
      this.dialogFrame.title = '养护任务'
    },
    getAlarmSourceEchart() {
      let arr = [
        { name: '未执行', count: 38, percentage: 38 },
        { name: '已执行', count: 32, percentage: 32 },
        { name: '已停用', count: 20, percentage: 20 },
        { name: '已超期', count: 10, percentage: 10 }
      ]
      const getchart = echarts.init(this.$refs.workOrderStatisticsEcharts)
      const data = []
      var color = ['#8fe7ea', '#74e2a0', '#f2d988', '#e38e6f', '#f2d988']
      const xdata = Array.from(arr, ({ name }) => name)
      for (var i = 0; i < arr.length; i++) {
        const randomRgbColor = this.$tools.randomRgbColor('array')
        data.push({
          name: arr[i].name,
          value: arr[i].count,
          percentage: arr[i].percentage,
          itemStyle: {
            normal: {
              borderWidth: 1,
              shadowBlur: 200,
              color: color[i] ?? randomRgbColor[0],
              label: {
                normal: { show: true }
              }
            }
          }
        })
      }
      const option = {
        backgroundColor: '',
        tooltip: {
          trigger: 'item'
        },
        legend: {
          icon: 'circle',
          orient: 'vartical',
          x: 'left',
          top: 'center',
          left: '52%',
          bottom: '0%',
          data: xdata,
          itemWidth: 8,
          itemHeight: 8,
          itemGap: 16,
          textStyle: {
            color: '#B3C2DD' //  字体颜色
          },
          formatter: function (name) {
            var oa = option.series[0].data
            for (var i = 0; i < option.series[0].data.length; i++) {
              if (name === oa[i].name) {
                return ' ' + name + '     ' + oa[i].percentage + '%'
              }
            }
          }
        },
        series: [
          {
            name: '',
            type: 'pie',
            center: ['30%', '50%'],
            label: {
              show: false
            },
            data: data
          }
        ]
      }
      getchart.clear()
      getchart.setOption(option)
      // 随着屏幕大小调节图表
      window.addEventListener('resize', () => {
        getchart.resize()
      })
      getchart.dispatchAction({
        type: 'highlight',
        seriesIndex: 0,
        dataIndex: 0
      })
      getchart.on('mouseover', (e) => {
        // 取消默认高亮
        getchart.dispatchAction({
          type: 'downplay',
          seriesIndex: 0,
          dataIndex: 0
        })
      })
      // 鼠标移出后默认高亮
      getchart.on('mouseout', (e) => {
        getchart.dispatchAction({
          type: 'highlight',
          seriesIndex: 0,
          dataIndex: 0
        })
      })
    },
    cahngePieType(type) {
      this.pieType = type
      if (type == 1) {
        this.$nextTick(() => {
          const ele = document.getElementsByClassName('pieTitle')[0]
          ele.firstChild.style.display = 'none'
          let arr1 = [
            { name: '待审核数', count: 150, percentage: 30 },
            { name: '已通过数', count: 66, percentage: 25 },
            { name: '未通过数', count: 50, percentage: 20 }
          ]
          let arr2 = [
            { name: '已上报', count: 144, percentage: 56 },
            { name: '未上报', count: 122, percentage: 44 }
          ]
          this.getEchart(this.$refs.typeStatistics, arr1, 0)
          this.getEchart(this.$refs.reportedStatistics, arr2, 1)
        })
      } else {
        this.$nextTick(() => {
          const ele = document.getElementById('workOrderStatisticsEcharts')
          ele.firstChild.style.display = 'block'
          this.getAlarmSourceEchart()
        })
      }
      let webviewType = type === 0 ? 'TaskOfMeasurement' : type === 1 ? 'AdverseEvents' : 'PerformanceTesting'
      try {
        window.chrome.webview.hostObjects.sync.bridge.MedicalFourLevelMenuSwitch(webviewType)
      } catch (error) { }
    },
    getEchart(dom, arr, index) {
      const getchart = echarts.init(dom)
      const data = []
      var color = ['#8fe7ea', '#74e2a0', '#f2d988', '#e38e6f', '#f2d988']
      const xdata = Array.from(arr, ({ name }) => name)
      for (var i = 0; i < arr.length; i++) {
        const randomRgbColor = this.$tools.randomRgbColor('array')
        data.push({
          name: arr[i].name,
          value: arr[i].count,
          percentage: arr[i].percentage,
          itemStyle: {
            normal: {
              borderWidth: 1,
              shadowBlur: 200,
              color: color[i] ?? randomRgbColor[0],
              label: {
                normal: { show: true }
              }
            }
          }
        })
      }
      let center = ['30%', '50%']
      if (index == 0) {
        center = ['30%', '50%']
      } else {
        center = '50%'
      }
      const option = {
        backgroundColor: '',
        tooltip: {
          show: true
        },
        legend: {
          show: index == 0,
          // 取消legend和echarts的联动
          selectedMode: false,
          icon: 'circle',
          orient: 'vartical',
          x: 'left',
          top: 'center',
          left: '52%',
          bottom: '0%',
          data: xdata,
          itemWidth: 8,
          itemHeight: 8,
          itemGap: 16,
          textStyle: {
            color: '#B3C2DD' //  字体颜色
          },
          formatter: function (name) {
            var oa = option.series[0].data
            for (var i = 0; i < option.series[0].data.length; i++) {
              if (name === oa[i].name) {
                return ' ' + name + '     ' + oa[i].percentage + '%'
              }
            }
          }
        },
        series: [
          {
            name: '',
            type: 'pie',
            clockWise: false,
            center: center,
            radius: ['58%', '70%'],
            label: {
              normal: {
                show: false,
                position: 'center',
                formatter: '{value|266}\n{label|事件总数}',
                rich: {
                  value: {
                    padding: 5,
                    align: 'center',
                    verticalAlign: 'middle',
                    fontSize: 20
                  },
                  label: {
                    align: 'center',
                    verticalAlign: 'middle',
                    fontSize: 14
                  }
                }
              },
              emphasis: {
                show: true,
                textStyle: {
                  fontSize: '12',
                  color: '#fff'
                }
              }
            },
            data: data
          }
        ]
      }
      getchart.clear()
      getchart.setOption(option)
      // 随着屏幕大小调节图表
      window.addEventListener('resize', () => {
        getchart.resize()
      })
      getchart.dispatchAction({
        type: 'highlight',
        seriesIndex: 0,
        dataIndex: 0
      })
      getchart.on('mouseover', (e) => {
        // 取消默认高亮
        getchart.dispatchAction({
          type: 'downplay',
          seriesIndex: 0,
          dataIndex: 0
        })
      })
      // 鼠标移出后默认高亮
      getchart.on('mouseout', (e) => {
        getchart.dispatchAction({
          type: 'highlight',
          seriesIndex: 0,
          dataIndex: 0
        })
      })
    },
    allTableChange() {
      this.dialogFrame.show = true
      if (this.pieType == 0) {
        this.dialogFrame.title = '计量任务'
      } else if (this.pieType == 1) {
        this.dialogFrame.title = '不良事件列表'
      } else {
        this.dialogFrame.title = '性能检测任务'
      }
    },
    openTaskPointList() { },
    tableLoadEvent() {
      // if (this.pageParams.total > this.pageParams.pageNo * this.pageParams.pageSize) {
      //   this.pageParams.pageNo++
      //   this.getInspectionDatas()
      // }
    },
    back(data) {
      this.breadcrumb = [{ label: '计量任务', name: 'meteringList' }]
      this.activeComponent = 'meteringList'
    },
    update() {
      this.breadcrumb = [{ label: '计量任务', name: 'meteringList' }]
      this.activeComponent = 'meteringList'
    },
    openDetailComponent(data) {
      this.breadcrumb.push({ label: '任务详情', name: 'maintainDetail' })
      this.activeComponent = data
    }
  }
}
</script>
<style lang="scss" scoped>
.content {
  width: 100%;
  height: 100%;
  overflow-y: auto;
  .insp {
    width: 100%;
    height: 24%;
    .pieHeader {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 0 10px;
      height: 40px;
      .headerLeft {
        span {
          display: inline-block;
          margin-right: 10px;
          font-size: 14px;
        }
        .activType {
          color: #409eff;
        }
      }
      .right-expand {
        margin-left: 8px;
        font-size: 24px;
        cursor: pointer;
      }
    }
    #workOrderStatisticsEcharts {
      width: 100%;
      height: calc(100% - 40px);
    }
  }
  .pieTitle {
    font-size: 14px;
    position: relative;
  }
  #typeStatistics,
  #reportedStatistics {
    width: 100%;
    height: calc(100% - 40px);
    position: relative;
  }
  .task-list {
    height: 74%;
    .statistics {
      display: flex;
      justify-content: space-between;
      align-items: center;
      .statisticsItem {
        margin: 5px 0;
        display: flex;
        flex-direction: column;
        align-items: center;
        .count {
          margin-top: 10px;
          color: #ffca64;
          font-size: 20px;
          font-weight: bold;
        }
      }
    }
  }
  .event-list {
    margin-top: 44%;
    height: 52%;
  }
}
</style>
