<template>
  <div class="statistics_item first" style="margin-bottom: 8px">
    <div class="statistics_current">
      <div class="item_title">实时监测</div>
      <div class="item_content">
        <div
          v-for="item in currentElevator"
          :key="item.key"
          class="content_item left"
        >
          <img :src="item.icon" width="50" height="56" />
          <div style="width: calc(100% - 50px); height: 100%">
            <div class="title">{{ item.name }}</div>
            <div class="value">{{ item.value }}</div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script>

import total from '@/assets/images/comprehensiveStatistics/total.png'
import online from '@/assets/images/comprehensiveStatistics/online.png'
import offline from '@/assets/images/comprehensiveStatistics/offline.png'
import userNum from '@/assets/images/comprehensiveStatistics/userNum.png'
import opendoorNum from '@/assets/images/comprehensiveStatistics/opendoorNum.png'
import runFloorNum from '@/assets/images/comprehensiveStatistics/runFloorNum.png'
import {
  newIotElevatorData
} from '@/utils/comprehensiveStatistics'
export default {

  data() {
    return {
      currentElevator: [
        { name: '电梯总数', key: 'total', value: 119, icon: total },
        {
          name: '在线数',
          key: 'onlineNum',
          value: '--',
          icon: online
        },
        { name: '离线数', key: 'offlineNum', value: '--', icon: offline },
        { name: '累计载人数', key: 'rideNum', value: '--', icon: userNum },
        { name: '开关门总次数', key: 'floorCount', value: '--', icon: opendoorNum },
        {
          name: '运行楼层总数',
          key: 'doorOpenCount',
          value: '--',
          icon: runFloorNum
        }
      ]
    }
  },
  mounted() {
    this.getCurrentElevatorInfo()
  },
  methods: {
    getCurrentElevatorInfo() {
      newIotElevatorData().then((res) => {
        if (res.data.code == 200) {
          this.currentElevator.forEach((item) => {
            item.value = res.data.data[item.key]
          })
        }
      })
    },

    showDetail() {
      this.$emit('showDetail', params)
    }
  }
}
</script>
<style lang="scss" scoped>
.first {
  display: flex;
  .statistics_current {
    width: 100%;
    height: 100%;
    margin-right: 8px;
    &:last-child {
      margin-right: 0;
    }

    .item_content {
      width: 100%;
      height: calc(100% - 26px);
      display: flex;
      flex-wrap: wrap;
      .content_item {
        display: flex;
        align-items: center;
        background: rgba(133, 145, 206, 0.05);
        margin-right: 4px;
        text-align: center;
        font-size: 12px;
        color: #fff;
        padding: 10px;

        .title{
          height: 50%;
          padding-bottom: 5px;
          color: #B0E3FA;
          font-size: 14px;
          display: flex;
          align-items: center; /* 垂直居中 */
          justify-content: center; /* 水平居中 */
        }
        .value {
          height: 50%;
          background: url("~@/assets/images/comprehensiveStatistics/item_bg.png")
            no-repeat;
          background-size: 100% 100%;
          color: #ffca64;
          display: flex;
          align-items: center; /* 垂直居中 */
          justify-content: center; /* 水平居中 */
          font-size: 20px;
        }
      }
      .left {
        width: calc(33% - 4px);
        height: calc(50% - 4px);
        // &:nth-child(even) {
        //   margin-right: 0;
        // }
      }
    }
  }
}
.item_title {
  font-weight: 500;
  font-size: 16px;
  color: #b0e3fa;
  margin-bottom: 10px;
}
</style>
