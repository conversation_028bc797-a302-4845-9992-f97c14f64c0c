import http from './http'
import publicParams from './publicParams'
// import qs from 'qs'

const iemcElevatorApi = __PATH.VUE_APP_IEMC_ELEVATOR_API
const iemcElevatorWS = __PATH.VUE_APP_IEMC_ELEVATOR_WS
const icisApi = __PATH.VUE_APP_INSP_API
const spaceApi = __PATH.VUE_APP_SPACE_API
const alarmApi = __PATH.VUE_APP_WARN_API
const newElevatorIot = __PATH.VUE_ELEVATOR_API
// socket电梯推送
export function elevatorWebsocket(id) {
  return http.websocketService(`${iemcElevatorWS}/iemcEleServer/${id}`)
}
// 大屏监控左上角统计数据
export function getElevatorStatistics(params) {
  return http.postRequest(`${iemcElevatorApi}/elevator/getStatistics`, {
    ...params
  })
}
// 获取摄像机列表
export function getCameraListByProjectCode(params) {
  return http.postRequest(`${iemcElevatorApi}/cameraManage/getCameraListByProjectCode`, {
    ...params
  })
}
// 大屏监控资产统计图
export function getIaasStatistics(params) {
  return http.postParamsQS(`${icisApi}/asset/assetDetails/getElevatorBrand`, {
    ...params
  })
}
// 资产详情
export function getAssetDetailsByAssetIds(params) {
  return http.postParamsQS(`${icisApi}/asset/assetDetails/getAssetDetailsByAssetIds`, {
    ...params
  })
}
// 大屏监控右侧统计图集合
export function getElevatorMonitoringList(params) {
  return http.postRequest(`${iemcElevatorApi}/elevator/getBoardHistogram`, {
    ...params
  })
}
// 大屏监控右侧统计图集合(楼层)
export function getElevatorFloorList(params) {
  return http.postRequest(`${iemcElevatorApi}/elevator/getFloorHistogrgetam`, {
    ...params
  })
}
// 获取按类型统计饼图
export function getReasonStatisticPie(params) {
  return http.postRequest(`${iemcElevatorApi}/policeHistory/getReasonStatisticPie`, {
    ...params
  })
}
// 获取按类型统计饼图
export function getAlarmStatisticPie(params) {
  return http.postRequest(`${alarmApi}/alarm/statistics/getReasonStatisticPie`, {
    ...params
  })
}
export function alarmTypeData(params) {
  return http.getRequest(`${alarmApi}/alarm/statistics/alarmTypeList`, {
    ...params
  })
}
// 实时监测 监测实体详情
export function getElevatorParticulars(params) {
  return http.postRequest(`${iemcElevatorApi}/elevator/selectElevatorParticulars`, {
    ...params
  })
}
// 未处理+当日报警分页查询
export function getStaticPoliceList(params) {
  return http.postRequest(`${iemcElevatorApi}/policeHistory/getStaticPoliceList`, {
    ...params
  })
}
// 摄像机列表
export function getVideoList(params) {
  return http.postRequest(`${iemcElevatorApi}/elevator/getVideoList`, {
    ...params
  })
}
// 根据id获取对应视屏地址
export function getHlvAddress(params) {
  return http.postRequest(`${iemcElevatorApi}/elevator/getHlvAddress`, {
    ...params
  })
}
// 30天开关门次数
export function getOpenNumByDays(params) {
  return http.postRequest(`${iemcElevatorApi}/elevator/getOpenNumByDays`, {
    ...params
  })
}
// 偏移趋势
export function getDeviationByDays(params) {
  return http.postRequest(`${iemcElevatorApi}/elevator/getDeviationByDays`, {
    ...params
  })
}
// 提交处置
export function disposePolice(params) {
  return http.postFormData(`${iemcElevatorApi}/policeHistory/disposePolice`, {
    ...params
  })
}
// 获取处置详情
export function getPoliceView(params) {
  return http.postRequest(`${iemcElevatorApi}/policeHistory/getPoliceView`, {
    ...params
  })
}

// 获取报警类型列表
export function alarmTypeList (params) {
  return http.getRequest(`${iemcElevatorApi}/policeHistory/clinePoliceType`, {
    ...params
  })
}
// 获取电梯报警数据
export function getElevatorFaultData (params) {
  return http.postRequest(`${alarmApi}/alarm/statistics/getPoliceGroup`, {
    ...params
  })
}
// 获取报警统计数据
export function getMonitorStatistic (params) {
  return http.postRequest(`${iemcElevatorApi}/policeHistory/getMonitorStatistic`, {
    ...params
  })
}
// 获取电梯报警排名
export function clinePoliceCount (params) {
  return http.postRequest(`${iemcElevatorApi}/policeHistory/clinePoliceCount`, {
    ...params
  })
}
// 获取电梯报警排名
export function elevatorMonitorList (params) {
  return http.postRequest(`${iemcElevatorApi}/elevator/getPullDownList`, {
    ...params
  })
}
// 获取开关门统计
export function GetOpenCount (params) {
  return http.postRequest(`${iemcElevatorApi}/client/getOpenCount`, {
    ...params
  })
}
// 获取电梯时段开门次数统计
export function GetOpenCountTimePeriod (params) {
  return http.postRequest(`${iemcElevatorApi}/client/getOpenCountTimePeriod`, {
    ...params
  })
}
// 获取电梯开门占比数据
export function GetOpenCountTimeRatio (params) {
  return http.postRequest(`${iemcElevatorApi}/client/getOpenCountTimeRatio`, {
    ...params
  })
}
// 获取电梯开门次数
export function GetOpenCountGroupSurvey (params) {
  return http.postRequest(`${iemcElevatorApi}/client/getOpenCountGroupSurvey`, {
    ...params
  })
}
// 根据区域ID获取楼宇列表
export function getBuildByAreaID (params) {
  return http.getRequest(`${spaceApi}/space/structure/lookUpBuildByArea`, {
    ...params
  })
}
// 获取电梯监测实体列表
export function GetSurveyByRegion (params) {
  return http.getRequest(`${iemcElevatorApi}/client/getSurveyByRegion`, {
    ...params
  })
}
// 获取开门记录列表
export function GetOpenDoorList (params) {
  return http.postRequest(`${iemcElevatorApi}/client/getOpenDoorList`, {
    ...params
  })
}
// 根据楼栋ID获取楼层列表
export function GetFloorByBuild (params) {
  return http.getRequest(`${spaceApi}/space/structure/lookUpFloorByBuild`, {
    ...params
  })
}
// 电梯停靠统计
export function GetSurveyStopFloorGroup (params) {
  return http.postRequest(`${iemcElevatorApi}/client/getSurveyStopFloorGroup`, {
    ...params
  })
}
// 电梯停靠楼层占比
export function GetStopFloorGroup (params) {
  return http.postRequest(`${iemcElevatorApi}/client/getStopFloorGroup`, {
    ...params
  })
}
// 获取停靠楼层统计
export function GetStopFloorStatistics (params) {
  return http.postRequest(`${iemcElevatorApi}/client/getStopFloorStatistics`, {
    ...params
  })
}
// 获取停靠楼层统计
export function GetStopFloorList (params) {
  return http.postRequest(`${iemcElevatorApi}/client/getStopFloorList`, {
    ...params
  })
}
// 获取开门/停靠记录列表
export function elevatorStopFloorList (params) {
  return http.postRequest(`${newElevatorIot}/adsliftstatusrecord/adsLiftStatusRecord/list`, {
    ...params
  })
}
// 获取开门/停靠记录列表
export function elevatorStopFloorRecord (params) {
  return http.postRequest(`${newElevatorIot}/adsliftruncount/adsLiftRunCount/doorOpenedCount`, {
    ...params
  })
}

// 获取直梯扶梯数量
export function getNumberOfElevators (params) {
  return http.postRequest(`${iemcElevatorApi}/elevator/getCount`, {
    ...params
  })
}
