<template>
  <div class="inspectionComponent">
    <div class="module-container" style="height: calc(25%)">
      <div class="module-header">
        <div class="title-left">
          <p class="title-left-text">作业统计</p>
        </div>
        <div class="title-right">
          <img src="../../assets/images/order_more.png" class="order-more" @click="allTableChange" />
        </div>
      </div>
      <div class="module-content jobStatistics" style="height: calc(100% - 44px)">
        <div v-for="(item,index) in jobStatisticsList" :key="index" class="jobStatistics-item">
          <span class="label">{{item.label}}</span>
          <span class="value">{{item.value}}</span>
        </div>
      </div>
    </div>
    <div class="module-container" style="height: calc(25%)">
      <div class="module-header">
        <div class="title-left">
          <p class="title-left-text">作业排序</p>
        </div>
        <div class="title-right">
          <el-dropdown trigger="click" @command="(e) => handleCommand(e)">
            <span class="el-dropdown-link">{{ jobDateList.find(v => v.val == jobType).name }} <i class="el-icon-arrow-down"></i> </span>
            <el-dropdown-menu slot="dropdown">
              <el-dropdown-item v-for="(item, index) in jobDateList" :key="index" :command="item.val" :class="{ isBjxl: jobType == item.val }">{{ item.name }}</el-dropdown-item>
            </el-dropdown-menu>
          </el-dropdown>
          <img src="../../assets/images/order_more.png" class="order-more" @click="allTableChange" />
        </div>
      </div>
      <div class="module-content " style="height: calc(100% - 44px)">
        <div id="kf_goodsEchart" style="height: 100%; overflow: hidden"></div>
      </div>
    </div>
    <div class="module-container" style="height: calc(25%)">
      <div class="module-header">
        <div class="title-left">
          <p class="title-left-text">作业台账</p>
        </div>
        <div class="title-right">
          <div class="icon-box">
            <img src="../../assets/images/filter.png" class="filter" @click="showCheckGroup = !showCheckGroup" />
            <div v-show="showCheckGroup" v-scrollbarHover class="panel-s">
              <el-checkbox-group v-model="checkTypeList" fill="#52FFFC" @change="checkBoxChanged">
                <el-checkbox v-for="item in typeList" :key="item.value" :label="item.label"></el-checkbox>
              </el-checkbox-group>
            </div>
          </div>
          <div>
            <img src="../../assets/images/order_more.png" class="order-more" @click="allTableChange" />
          </div>
        </div>
      </div>
      <div class="module-content" style="height: calc(100% - 44px)">
        <el-table v-el-table-infinite-scroll="tableLoadMore" v-loading="tableLoading" class="table-center-transfer"
                  :data="tableData" height="100%"
                  :cell-style="{ padding: ' 8px', backgroundColor: 'transparent', border: 'none', padding: '3px' }"
                  :header-cell-style="{ background: '#8591CE26!important', color: '#8BDDF5FF', padding: '4px 8px', fontWeight: 'bold' }"
                  style="width: 100%" element-loading-background="rgba(0, 0, 0, 0.2)" @row-dblclick="tabledblClick">
          <el-table-column prop="projectName" label="作业名称" show-overflow-tooltip></el-table-column>
          <el-table-column prop="projectStartTime" label="作业日期" show-overflow-tooltip>
            <template slot-scope="scope">
              {{ (scope.row?.projectStartTime ?? '') + '-' + (scope.row?.projectEndTime ?? '') }}
            </template>
          </el-table-column>
          <el-table-column prop="businessFormName" width="60px" label="类型" show-overflow-tooltip></el-table-column>
          <el-table-column prop="statusName" width="80px" label="状态" show-overflow-tooltip></el-table-column>
        </el-table>
      </div>
    </div>
    <div class="module-container" style="height: calc(25%)">
      <div class="module-header">
        <div class="title-left">
          <p class="title-left-text">图例</p>
        </div>
      </div>
      <div class="module-content legendData" style="height: calc(100% - 44px)">
        <el-checkbox-group v-model="checkedLegend" @change="handleCheckedLegendChange">
          <div v-for="(item, index) in legendData" :key="index" class="middle-checkbox">
            <el-checkbox :key="index" :label="item.label" :value="item.value" class="checkbox-label"></el-checkbox>
            <div class="checkbox-div" :style="{ background: colorList[index] || 'rgb(255, 255, 255)' }"></div>
            <div class="checkbox-title">{{ item.label || '' }}</div>
          </div>
        </el-checkbox-group>
      </div>
    </div>
    <constructionListDialog v-if="isDialog" :isDialog="isDialog" :roomData="roomData" @close="isDialog = false" />
  </div>
</template>
<script >
import * as echarts from 'echarts'
import { getAssignmentStateStatistics, getAssignmentTypeStatistics, getAssignmentDeptStatistics, getAssignmentInfoByPage } from '@/utils/spaceManage'
export default {
  name: 'inspectionComponent',
  components: {
    constructionListDialog: () => import('./components/constructionListDialog.vue')
  },
  props: {
    roomData: {
      type: Object,
      default: () => {}
    },
    assetsList: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      isDialog: false,
      showCheckGroup: false,
      checkTypeList: ['全部'],
      typeList: [
        { label: '全部', value: '' },
        { label: '未开始', value: 0 },
        { label: '进行中', value: 1 },
        { label: '超时', value: 3 },
        { label: '作业结束', value: 2 }
      ],
      jobDateList: [
        { name: '施工类型', val: 0},
        { name: '申请科室', val: 1 }
      ],
      jobStatisticsList: [
        { label: '未开始', value: 0},
        { label: '进行中', value: 0},
        { label: '超时', value: 0},
        { label: '作业结束', value: 0}
      ],
      checkedLegend: ['进行中', '超时'],
      legendData: [
        { label: '未开始', value: 0 },
        { label: '进行中', value: 1 },
        { label: '超时', value: 3 },
        { label: '作业结束', value: 2 }
      ],
      // 颜色列表
      colorList: ['#2166EB', '#9BC3FF', '#F53577', '#B18BC7', '#9BC5BD', '#E3AC4C', '#EE6D58', '#9B41D0', '#4CCBE3', '#726899'],
      jobType: 0,
      ssmCodes: [],
      tableLoading: false,
      tableData: [],
      pagination: {
        total: 0,
        pageSize: 15,
        pageNo: 1
      },
      selectTaskId: ''
    }
  },
  watch: {
    isDialog(val) {
      try {
        window.chrome.webview.hostObjects.sync.bridge.ShowDialog(val)
      } catch (error) {}
    },
    // 监听模型层级数据变化
    roomData: {
      handler(newVal, oldVal) {
        this.initData()
      },
      deep: true
    }
  },
  mounted() {
    // 初始化调用
    this.initData()
  },
  methods: {
    // 列表双击进入详情
    tabledblClick(row) {
      this.$emit('roomEvent', {
        type: 'move',
        assetId: row.projectCode,
        assetName: row.projectName,
        modelCode: row.projectCode
        // entityTypeId: ''
      })
      try {
        window.chrome.webview.hostObjects.sync.bridge.EquipmentRealView(JSON.stringify({taskId: row.projectCode}))
      } catch (error) {}
    },
    handleCommand(data) {
      this.jobType = data
      this.getAssignmentTypeStatistics()
    },
    // 图例筛选
    handleCheckedLegendChange() {
      try {
        window.chrome.webview.hostObjects.sync.bridge.ConstructionCondition(this.legendData.filter(v => this.checkedLegend.includes(v.label)).map(v => v.value).join(','))
      } catch (error) {}
    },
    // 状态筛选过滤
    checkBoxChanged() {
      this.checkTypeList = [this.checkTypeList[this.checkTypeList.length - 1]]
      this.showCheckGroup = false
      const codeList = []
      this.typeList.forEach((item) => {
        if (this.checkTypeList[0] == item.label) {
          codeList.push(item.value)
        }
      })
      this.pagination.pageNo = 1
      this.getAssignmentInfoByPage()
    },
    initData() {
      this.pagination.pageNo = 1
      this.ssmCodes = this.roomData.ssmCodes?.split(',') || []
      this.getAssignmentTypeStatistics()
      this.getAssignmentStateStatistics()
      this.getAssignmentInfoByPage()
    },
    tableLoadMore() {
      if (this.pagination.total > this.pagination.pageNo * this.pagination.pageSize) {
        this.pagination.pageNo++
        this.getAssignmentInfoByPage()
      }
    },
    // 作业统计
    getAssignmentStateStatistics() {
      let params = {
        spaceCode: this.ssmCodes[this.ssmCodes.length - 1]
      }
      getAssignmentStateStatistics(params).then(res => {
        if (res.data.code == 200) {
          this.jobStatisticsList[0].value = res.data.data.haveNotStarted
          this.jobStatisticsList[1].value = res.data.data.underwayCount
          this.jobStatisticsList[2].value = res.data.data.overtimeCount
          this.jobStatisticsList[3].value = res.data.data.finishCount
        }
      })
    },
    // 作业排序
    getAssignmentTypeStatistics() {
      let params = {
        spaceCode: this.ssmCodes[this.ssmCodes.length - 1]
      }
      let obj = {
        0: getAssignmentTypeStatistics,
        1: getAssignmentDeptStatistics
      }
      obj[this.jobType](params).then(res => {
        if (res.data.code == 200) {
          this.$nextTick(() => {
            this.getTaskTypeData(res.data.data.map(v => {
              return {
                id: v.businessFormId,
                name: v.businessFormName,
                value: v.sum
              }
            }))
          })
        }
      })
    },
    // 作业台账列表
    getAssignmentInfoByPage() {
      let params = {
        assignmentStatus: this.typeList.find(v => v.label == this.checkTypeList[0]).value,
        page: this.pagination.pageNo,
        pageSize: this.pagination.pageSize,
        spaceCode: this.ssmCodes[this.ssmCodes.length - 1]
      }
      if (this.jobType == 1) {
        params.applicantDepartment = this.selectTaskId
      } else {
        params.businessFormId = this.selectTaskId
      }
      this.tableLoading = true
      if (this.pagination.pageNo) this.tableData = []
      getAssignmentInfoByPage(params).then(res => {
        this.tableLoading = false
        if (res.data.code == 200) {
          this.tableData = this.tableData.concat(res.data.data.records)
          this.pagination.total = res.data.data.total
        }
      }).catch(() => {
        this.tableLoading = false
      })
    },
    // 作业排序图表
    getTaskTypeData(data) {
      // 基于准备好的dom，初始化echarts实例
      let EChart = echarts.init(document.getElementById('kf_goodsEchart'))
      //   将data数据中得value值从大到小排序，然后按顺序将value放入dataValue数组中 name放入dataName数组中 name和value一一对应
      data.sort(function (a, b) {
        return a.value - b.value
      })
      const dataName = Array.from(data, (item) => {
        item.name = item.name || '未命名'
        const match = item.name.match(this.regex)
        const name = item.name
        return {
          value: name,
          textStyle: {
            color: '#FFF'
          }
        }
      })
      // 配置参数
      let config = {
        legend: {},
        grid: {
          top: '5%',
          left: '3%',
          right: '8%',
          bottom: '0%',
          containLabel: true
        },
        xAxis: {
          type: 'value',
          show: false
        },
        yAxis: [
          {
            type: 'category',
            data: dataName,
            axisTick: {
              show: false // 不显示坐标轴刻度线
            },
            axisLine: {
              show: false // 不显示坐标轴线
            },
            splitLine: {
              // 网格线
              show: false
            },
            axisLabel: { textStyle: { color: '#fff', fontSize: '14' } }
          },
          {
            type: 'category', // 坐标轴类型
            // inverse: true, // 是否是反向坐标轴
            axisTick: {
              show: false // 不显示坐标轴刻度线
            },
            axisLine: {
              show: false // 不显示坐标轴线
            },
            splitLine: {
              // 网格线
              show: false
            },
            axisLabel: {
              textStyle: {
                color: '#FFFFFFCC',
                fontSize: '14'
              },
              formatter: (value) => {
                return value
              }
            },
            data: data
          }
        ],
        series: [
          {
            type: 'bar',
            label: {
              show: false
            },
            emphasis: {
              focus: 'series'
            },
            data: data,
            barWidth: 8,
            itemStyle: {
              normal: {
                // barBorderRadius: [15, 15, 15, 15],
                color: function (params) {
                  // 通过返回值的下标一一对应将颜色赋给柱子上
                  return '#0A84FF'
                }
              },
              // 鼠标移入改变颜色
              emphasis: {
                color: '#FFCA64FF'
              }
            },
            showBackground: true, // 显示背景色
            backgroundStyle: {
              color: '#384156'
            }
          }
        ],
        dataZoom: [
          {
            yAxisIndex: [0, 1],
            orient: 'vertical',
            show: data.length > 5,
            type: 'slider',
            start: 100,
            // end: 100,
            width: 8,
            left: '99%',
            borderColor: 'rgba(43,48,67,.1)',
            fillerColor: '#6580b8',
            zoomLock: true,
            showDataShadow: false,
            backgroundColor: 'rgba(43,48,67,.1)',
            showDetail: false,
            // realtime: true,
            filterMode: 'filter',
            handleIcon: 'M-9.35,34.56V42m0-40V9.5m-2,0h4a2,2,0,0,1,2,2v21a2,2,0,0,1-2,2h-4a2,2,0,0,1-2-2v-21A2,2,0,0,1-11.35,9.5Z',
            handleStyle: {
              color: '#6580b8',
              borderColor: '#6580b8'
            },
            maxValueSpan: 4,
            minValueSpan: 4,
            brushSelect: false
          },
          {
            type: 'inside',
            show: false,
            yAxisIndex: [0, 1],
            height: 8
          }
        ]
      }
      // 设置参数
      EChart.setOption(config)
      // 先移除点击事件 解决点击事件重复绑定
      EChart.getZr().off('click')
      // 点击事件
      EChart.getZr().on('click', (params) => {
        var pointInPixel = [params.offsetX, params.offsetY]
        if (EChart.containPixel('grid', pointInPixel)) {
          // 获取当前点击的索引值
          // 注意：若柱状图为纵向则获取x轴的索引，若柱状图为横向则需获取y轴的索引(见下方注释)
          // var xIndex=EChart.convertFromPixel({seriesIndex:0},[params.offsetX, params.offsetY])[0];
          // var xData=config.xAxis.data[xIndex];//当前点击柱子的数据
          var yIndex = EChart.convertFromPixel({ seriesIndex: 0 }, [params.offsetX, params.offsetY])[1]
          var yData = config.yAxis[1].data[yIndex] // 当前点击柱子的数据
          // 新增变量 判断当前点击柱状图是选中还是取消选中
          let isSelected = false
          dataName.map((e, i) => {
            // 选中的设置选中色
            if (i == yIndex && e.textStyle.color != '#FFCA64FF') {
              e.textStyle.color = '#FFCA64FF'
              this.selectTaskId = yData.id
              this.getAssignmentInfoByPage()
            } else {
              // 选中已选中的则为取消选中
              if (i == yIndex && e.textStyle.color == '#FFCA64FF') {
                this.selectTaskId = ''
                this.getAssignmentInfoByPage()
              }
              // 其他的设为默认色
              e.textStyle.color = '#FFF'
            }
          })
          config.yAxis[0].data = JSON.parse(JSON.stringify(dataName))
          config.dataZoom[0].start = (yIndex / dataName.length) * 100
          EChart.setOption(config)
        }
      })
    },
    allTableChange() {
      this.isDialog = true
    }
  }
}
</script>

  <style lang="scss" scoped>
@import "./style/module.scss";
.inspectionComponent {
  width: 100%;
  height: 100%;
  .el-dropdown-link {
    color: #fff !important;
    margin-left: 10px;
  }
  .title-left {
    padding-left: 20px;
  }
  .chartContent {
    padding: 16px 0;
    height: calc(100% - 44px);
    display: flex;
    width: 100%;
  }
}
.module-header {
  width: 100%;
  // padding-left: 50px;
  background: url("~@/assets/images/qhdsys/bg-bt.png") no-repeat;
}
.order-more {
  width: 24px;
  height: 24px;
  margin-right: 0 !important;
  margin-left: 8px;
  cursor: pointer;
}
.filter {
  width: 24px;
  height: 24px;
}
.jobStatistics {
  display: flex;
  flex-wrap: wrap;
  padding: 10px 0;
  .jobStatistics-item {
    width: calc(50% - 0.5rem);
    height: calc(50% - 0.5rem);
    background: rgba(53, 98, 219, 0.06);
    border-radius: 4px;
    display: flex;
    justify-content: center;
    align-items: center;
    .label {
      font-weight: 500;
      font-size: 15px;
      color: #ffffff;
    }
    .value {
      font-weight: bold;
      font-size: 24px;
      color: #ffca64;
      margin-left: 10px;
    }
  }
  .jobStatistics-item:nth-child(2n) {
    margin-left: 1rem;
  }
  .jobStatistics-item:nth-child(3),
  .jobStatistics-item:nth-child(4) {
    margin-top: 1rem;
  }
}
.icon-box {
  margin-right: 0;
  display: flex;
  align-items: center;
  position: relative;
  .panel-s {
    position: absolute !important;
    right: 0 !important;
    top: 28px !important;
    background-color: #374b79 !important;
    z-index: 9 !important;
    min-height: 20px !important;
    overflow: auto;
    .el-checkbox-group {
      display: flex !important;
      flex-direction: column;
      align-items: flex-end;
      padding: 8px;
      margin-right: 0 !important;
      .el-checkbox {
        width: 100%;
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-right: 0;
        & + .el-checkbox {
          margin-top: 5px;
        }
        .el-checkbox__label {
          color: #a3a9c0 !important;
        }
      }
    }
  }
}
::v-deep .el-checkbox__input.is-checked span.el-checkbox__inner {
  background: url("@/assets/images/<EMAIL>") !important;
  background-size: 100% 100% !important;
}
::v-deep span.el-checkbox__inner {
  border-color: #c5fff9 !important;
}
::v-deep .el-checkbox__input.is-checked + .el-checkbox__label {
  color: #8bddf5;
}
.legendData {
  padding: 16px;
  .middle-checkbox {
    width: 100%;
    display: flex;
    align-items: center;
    margin-bottom: 6px;
  }
  .checkbox-div {
    width: 12px;
    height: 8px;
    margin: 0 8px;
    flex-shrink: 0;
  }
  .checkbox-title {
    height: 20px;
    font-size: 14px;
    font-family: PingFang SC-Medium, PingFang SC;
    font-weight: 500;
    color: #ffffff;
    line-height: 20px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
  .checkbox-label {
    ::v-deep .el-checkbox__label {
      display: none;
    }
  }
}
</style>
