<template>
  <div class="statistics_item">
    <div class="item_title">挂号趋势</div>
    <div class="checkbox-contain">
      <el-checkbox-group
        v-model="checkList"
        fill="#52FFFC"
        class="checkedBoxGroup"
        @change="checkedChange"
      >
        <el-checkbox
          v-for="(item, index) in areaList"
          :key="index"
          :label="item.id"
          :disabled="index == 0"
        >
          {{ item.register }}
        </el-checkbox>
      </el-checkbox-group>
      <img src="@/assets/images/checkbox_add.png" @click="switchPanel" />
      <div v-show="showPanel" v-if="originList.length" class="panel">
        <el-checkbox-group
          v-model="originCheckList"
          fill="#52FFFC"
          @change="originChange"
        >
          <el-checkbox
            v-for="item in originList"
            :key="item.id"
            :label="item.id"
            :disabled="areaList.length > 5"
          >
            {{ item.register }}
          </el-checkbox>
        </el-checkbox-group>
      </div>
    </div>
    <div id="registrationTrend"></div>
  </div>
</template>
<script>
import * as echarts from 'echarts'
import { departmentRegisterTrend } from '@/utils/comprehensiveStatistics'
import dayjs from 'dayjs'
export default {
  data() {
    return {
      areaList: [],
      checkList: [],
      originList: [],
      originCheckList: [],
      xValue: [],
      showPanel: false,
      myChart: null
    }
  },
  watch: {
    date(val) {
      this.getdepartmentRegisterTrend()
    }
  },
  mounted() {
    this.getdepartmentRegisterTrend()
  },
  methods: {
    switchPanel() {
      this.showPanel = !this.showPanel
    },

    getdepartmentRegisterTrend() {
      departmentRegisterTrend({ time: dayjs().format('YYYY-MM-DD') }).then((res) => {
        if (res.data.code == 200) {
          this.originList = res.data.data.data
          if (this.originList.length > 6) {
            this.areaList = this.originList.splice(0, 6)
          } else {
            this.areaList = res.data.data.data
            this.originList = []
          }
          this.checkList = this.areaList.map((item) => item.id)
          this.xValue = res.data.data.xaxisList
          this.initLine()
        }
      })
    },
    checkedChange(val) {
      // 这个方法只会出现取消勾选某一项
      let item = this.areaList.filter((item) => {
        return !val.includes(item.id)
      })
      // 如果找到了，那么就从area里面删除这一项
      this.areaList = this.areaList.filter((el) => {
        return el.id !== item[0].id
      })
      // 然后需要给originList里面添加到头部
      this.originList.unshift(item[0])
      this.initLine()
      this.$forceUpdate()
    },
    originChange(val) {
      let item = this.originList.filter((item) => {
        return val.includes(item.id)
      })
      this.areaList = this.areaList.concat(item)
      this.checkList.push(item[0].id)
      this.originCheckList = []
      this.originList = this.originList.filter((el) => {
        return el.id !== item[0].id
      })
      this.initLine()
      this.$forceUpdate()
    },
    initLine () {
      if (this.myChart) {
        this.myChart.dispose()
        this.myChart = null
      }
      let chartDom = document.getElementById('registrationTrend')
      this.myChart = echarts.init(chartDom)
      let option = {}
      this.myChart.showLoading()
      option = {
        tooltip: {
          show: true,
          trigger: 'axis',
          formatter: (params) => {
            console.log(params)
            let tooltip = params[0].name + '<br/>'
            params.forEach(item => {
              let color = item.color
              let colorCircle = `<span style="display:inline-block;margin-right:5px;border-radius:50%;width:10px;height:10px;background-color:${color};"></span>`
              tooltip += `${colorCircle}${item.seriesName}: ${item.value}<br/>`
            })
            return tooltip
          }
        },

        grid: {
          left: '0', // 调整左侧边距
          right: '5%', // 调整右侧边距
          top: '10px', // 调整顶部边距，为图例留出空间
          bottom: '0', // 调整底部边距
          containLabel: true
        },
        xAxis: {
          type: 'category',
          data: this.xValue
        },
        yAxis: {
          type: 'value',
          splitLine: {
            show: true,
            lineStyle: {
              color: 'rgba(230,247,255,0.2)'
            }
          }
        },
        series: []
      }
      this.areaList.forEach((el) => {
        let obj = {
          name: el.register,
          type: 'line',
          data: el.data
        }
        option.series.push(obj)
      })
      this.myChart.setOption(option)
      this.myChart.off('click')
      // 图例点击事件
      this.myChart.on('click', (params) => {
        this.$emit('itemClick', params.data)
      })
      this.myChart.hideLoading()
    }
  }
}
</script>
<style lang="scss" scoped>
.statistics_item {
  background: rgba(133,145,206,0.05);
  padding: 16px 8px 0;
}
#registrationTrend {
  width: 100%;
  height: calc(100% - 50px);
}
.item_title {
  font-weight: 500;
  font-size: 16px;
  color: #b0e3fa;
  margin-bottom: 10px;
}
.checkbox-contain {
  width: 100%;
  display: flex;
  justify-content: flex-end;
  align-items: center;
  position: relative;
  img {
    width: 14px;
    height: 14px;
    // margin-left: 12px;
    cursor: pointer;
  }
  .panel {
    position: absolute;
    right: 0;
    top: 20px;
    background-color: #374b79;
    padding: 8px;
    z-index: 9;
    height: 100px;
    overflow: auto;
    .el-checkbox-group {
      display: flex;
      flex-direction: column;
      align-items: flex-end;
      .el-checkbox {
        width: 100%;
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-right: 0;
        margin-bottom: 5px;
      }
    }
  }
}
.checkedBoxGroup{
  display: flex;
  justify-content: flex-end;
  align-items: center;
  width: 100%;
  .el-checkbox{
    margin-right: 10px;
    display: flex;
    align-items: center;
    :deep(.el-checkbox__label){
      color: #8BDDF5;
      padding-left: 5px;
      display: inline-block;
      width: 65px;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
    }
  }
}
</style>
