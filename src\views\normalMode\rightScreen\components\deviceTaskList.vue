<template>
  <el-dialog v-dialogDrag :visible="visible" :modal="false" :before-close="handleClose" :close-on-click-modal="false" custom-class="main" width="70%">
    <template slot="title">
      <span class="dialog-title">设备任务</span>
    </template>
    <div class="outermost">
      <div class="left" v-loading="deviceLoading">
        <div class="leftTitle">设备列表</div>
        <div class="deviceList">
          <div v-for="(item, index) in deviceList" :key="index" @click="selectDevice(item, index)" class="deviceItem" :class="{ active: activeDevice === index }">
            <el-tooltip effect="dark" :disabled="showTooltip" :content="item.assetName" placement="top">
              <div @mouseover="onMouseOver(item.id)" class="deviceName"><span :ref="`nodeLabel${item.id}`">{{item.assetName}}</span></div>
            </el-tooltip>
          </div>
        </div>
      </div>
      <div class="right">
        <el-table ref="table" v-loading="taskLoading" :data="taskList" height="calc(100% - 32px)" :row-key="getRowKeys" style="width: 100%" stripe>
          <el-table-column prop="taskName" show-overflow-tooltip label="任务名称" align="center">
            <template slot-scope="scope">
              <el-link type="primary" @click="handlOperation(scope.row)">{{ scope.row.taskName }}</el-link>
            </template>
          </el-table-column>
          <el-table-column prop="cycleType" show-overflow-tooltip label="周期类型" align="center">
            <template slot-scope="scope">
              <span>{{ cycleTypeFn(scope.row.cycleType) }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="taskStartTime" show-overflow-tooltip :label="taskType == '1' ? '应巡日期' : '应保养日期'" align="center"></el-table-column>
          <el-table-column prop="distributionTeamName" show-overflow-tooltip :label="taskType == '1' ? '巡检部门' : '保养部门'" align="center"></el-table-column>
          <el-table-column prop="totalCount" show-overflow-tooltip :label="taskType == '1' ? '应巡点数' : '应保养点数'" align="center"></el-table-column>
          <el-table-column prop="hasCount" show-overflow-tooltip :label="taskType == '1' ? '实巡点数' : '实保养点数'" align="center"></el-table-column>
          <el-table-column
            prop="taskStatus"
            show-overflow-tooltip
            label="状态"
            :formatter="
              (row, column, cellValue, index) => {
                return cellValue === null ? '' : cellValue === 1 ? '未完成' : '已完成'
              }
            "
            align="center"
          ></el-table-column>
          <!-- <el-table-column show-overflow-tooltip label="操作">
              <template slot-scope="scope">
                <div class="operationBtn">
                  <span @click="handlOperation(scope.row)">详情</span>
                </div>
              </template>
            </el-table-column> -->
        </el-table>
        <el-pagination
          class="pagination"
          :current-page="params.page"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
          :page-size="params.size"
          :page-sizes="[15, 30, 50]"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        ></el-pagination>
      </div>
    </div>
    <!-- 巡检保养详情 -->
    <InspectionDetail ref="inspectionDetail" :dataInfo="currentTask" :systemType="taskType == '1' ? 'xjrw' : 'byrw'"></InspectionDetail>
  </el-dialog>
</template>
<script>
import { monitorTypeList } from '@/assets/common/dict.js'
import InspectionDetail from '../../../spaceManage/sysTypeComponent/components/inspectionDetail.vue'
import { getAssetDetails } from '@/utils/centerScreenApi'
import { getInspectionData } from '@/utils/centerScreenApi'
export default {
  components: {
    InspectionDetail
  },
  model: {
    prop: 'visible',
    event: 'close'
  },
  props: {
    visible: {
      type: Boolean
    },
    // 设备类型
    deviceType: {
      type: String,
    },
    // 任务类型
    taskType:{
      type: String,
      default: '1'
    },
    // 日期类型
    taskDateType:{
      type: String,
      default:'month'
    }
  },
  data() {
    return {
      deviceLoading: false,
      taskLoading: false,
      deviceList: [],
      activeDevice: '',
      params: {
        pageSize: 15,
        pageNo: 1
      },
      total: 0,
      taskList: [
        {
          taskName: '测速任务',
          id: '1'
        }
      ],
      currentTask: {},
      cycleTypeFn(scope) {
        if (scope === 8) return '单次'
        if (scope === 6) return '每日'
        if (scope === 0) return '每周'
        if (scope === 2) return '每月'
        if (scope === 3) return '季度'
        if (scope === 5) return '全年'
      },
      showTooltip: true
    }
  },

  mounted() {
    this.getDeviceList()
  },
  methods: {
    getRowKeys(row) {
      return row.id
    },
    getDeviceList() {
      this.deviceLoading = true
      const params = {
        currentPage: 1,
        pageSize: 99,
        assetStatusCode: this.deviceType
      }
      getAssetDetails(params).then((res) => {
        if (res.data.code === '200') {
          this.deviceList = res.data.data.assetDetailsList
          this.total = res.data.data.sum
          this.deviceLoading = false
          let arr = this.deviceList.map(el => {
            return el.assetsId
          })
          this.getTaskList(arr.join(','))
        }
      })
    },

    getTaskList(id) {
        this.taskLoading = true
        const codeData = {
          dateType: this.taskDateType,
          taskStatus: 6,
          ...this.params,
          systemCode: this.taskType,
          deviceId: id
        }
        getInspectionData(codeData).then((res) => {
          if (res.data.code === '200') {
            this.taskLoading = false
            this.taskList = res.data.data.list
            this.total = res.data.data.sum.allCount
          }
        })
    },
    selectDevice(item, index) {
      this.activeDevice = index
      // 设备设施对应医用气体系统UPS的假数据，后期可删除
      const deviceData = [
        {
          name: '氧气站房',
          menuName: 'Yqxt',
          modelCodes: ['01_-28564', '01_-28580', '01_-28596', '01_-28588', '0100201_-764250', '0100103_-182784']
        },
        {
          name: 'UPS',
          menuName: 'Ups',
          modelCodes: ['0100102_2837920', '0100104_2844580', '0100105_2847916']
        }
      ]
      const findData = deviceData.find(e => e.modelCodes.includes(item.modelCode))
      if (findData && Object.keys(findData).length) {
        const params = {
          DeviceCode: item.modelCode,
          menuName: findData.menuName,
          projectCode: monitorTypeList.find(e => e.wpfKey === findData.menuName).projectCode
        }
        console.log(params);
        try {
          window.chrome.webview.hostObjects.sync.bridge.EquipmentRealView(JSON.stringify(params))
        } catch (error) {}
      }
      this.getTaskList(item.assetsId)
    },

    handleClose() {
      this.$emit('close')
    },

    // 分页
    handleSizeChange(val) {
      this.params.pageNo = 1
      this.pageSize = val
      this.getTaskList()
    },
    handleCurrentChange(val) {
      this.params.pageNo = val
      this.getTaskList()
    },
    // 任务详情
    handlOperation(row) {
      this.currentTask = row
      this.$refs.inspectionDetail.hiddenDangerDetailsListShow = true
      this.$refs.inspectionDetail.GetTaskPointReleaseList(row.id, 'insp')
    },
    onMouseOver(id) {
      const parentWidth = this.$refs[`nodeLabel${id}`][0].parentNode.offsetWidth // 获取元素父级可视宽度
      const contentWidth = this.$refs[`nodeLabel${id}`][0].offsetWidth // 获取元素可视宽度
      this.showTooltip = contentWidth <= parentWidth
    }
  }
}
</script>

<style lang="scss" scoped>
::v-deep .el-dialog {
  height: 70vh;
  border: none;
  pointer-events: auto;
  box-shadow: none;
  background: url('@/assets/images/qhdsys/big-mask-bg.png') no-repeat center center / 100% 100%;
  .dialog-title {
    display: inline-block;
    width: 100%;
    text-align: center;
    transform: translateY(-4px);
    color: #cbdeed;
  }
  .dialog-title::before {
    display: none;
  }
  .el-dialog__header {
    height: 3.125rem;
    line-height: 3.125rem;
    padding: 0;
  }
  .el-dialog__body {
    padding: 10px 50px 10px 50px;
    height: calc(100% - 130px);
  }

  .outermost {
    width: 100%;
    height: 100%;
    padding: 10px;
    display: flex;
    .left {
      margin-right: 10px;
      padding: 16px 24px;
      width: 230px;
      height: 100%;
      background: #0a1b3c;
      .leftTitle {
        margin-bottom: 24px;
      }
      .deviceList {
        height: calc(100% - 38px);
        overflow: auto;
        .deviceItem {
          width: 100%;
          height: 36px;
          line-height: 36px;
          padding-left: 10px;
          color: #b0e3fa;
          cursor: pointer;

          .deviceName {
            width: 100%;
            text-overflow: ellipsis;
            overflow: hidden;
            white-space: nowrap;
          }
        }
        .active {
          background: #272f40;
          color: #ffca64;
        }
      }
    }
    .right {
      box-sizing: border-box;
      width: calc(100% - 230px);
      height: 100%;
    }
  }
}
</style>
