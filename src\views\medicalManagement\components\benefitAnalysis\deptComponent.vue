<template>
  <div class="box-view">
    <div class="box-top">
      <div class="box-top-left">
        <div id="top_left_Echarts" style="width: 100%; height: 100%"></div>
      </div>
      <div class="box-top-center">
        <div class="grossIncome">
          <div class="grossIncome-title">
            总收入
          </div>
          <div class="grossIncome-box">
            <div class="grossIncome-content" v-for="(item,index) in statisticalData" :key="index">
              <div class="grossIncome-content-left">
                <p>
                  <span class="item-block" :style="{ background: item.background }"></span><span
                    class="item-label">{{item.name}}</span>
                </p>
                <p>
                  <span class="item-value">{{item.value}}</span> <span class="item-unit">{{item.unit}}</span>
                </p>
              </div>
              <div class="grossIncome-content-right">
                <p class="item-label">同比去年</p>
                <p>
                  <img :src="item.img" alt="">
                  <span class="item-rate" :style="{ color: item.color }">12.3%</span>
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="box-top-right">
        <div id="top_right_Echarts" style="width: 100%; height: 100%"></div>
      </div>
    </div>
    <div class="box-bottom">
      <div class="box-bottom-left">
        <div id="bottom_left_Echarts" style="width: 100%; height: 100%"></div>
      </div>
      <div class="box-bottom-right">
        <div id="bottom_right_Echarts" style="width: 100%; height: 100%"></div>
      </div>
    </div>
  </div>
</template>
<script>
import * as echarts from 'echarts'
import "echarts-liquidfill/src/liquidFill.js"; // 在需要水滴图的页面js引入
import downImg from '@/assets/images/qhdsys/down.png'
import upImg from '@/assets/images/qhdsys/up.png'
import flatImg from '@/assets/images/flat.png'

export default {
  name: 'singleDeviceComponent',
  data() {
    return {
      pieIdData: [
        "pie_useCondition",
        "pie_enableYear",
      ],
      statisticalData: [
        {
          name: '总收入',
          value: '123,330.120',
          unit: '元',
          background: '#8BDDF5',
          rate: '12.3%',
          color: '#FF2D55',
          img: upImg
        },
        {
          name: '总支出',
          value: '330.120',
          unit: '元',
          background: '#3CC1FF',
          rate: '12.3%',
          color: '#61E29D',
          img: downImg
        },
        {
          name: '总利润',
          value: '123,000.000',
          unit: '元',
          background: '#0A84FF',
          rate: '12.3%',
          color: '#D6EFF1',
          img: flatImg
        }
      ]
    };
  },
   // 监听全屏缩放 动态改变echarts图表大小
  watch: {
    '$store.state.dialogFullScreenState': {
      handler(val) {
        this.$nextTick(() => {
          const echartsDom = ['top_left_Echarts', 'top_right_Echarts', 'bottom_left_Echarts', 'bottom_right_Echarts']
          setTimeout(() => {
            echartsDom.forEach((item) => {
              if (document.getElementById(item)) {
                echarts.init(document.getElementById(item)).resize()
              }
            })
          }, 200)
        })
      },
      deep: true
    }
  },
  mounted() {
    this.init()
  },
  methods: {
    //初始化
    init() {
      this.getTopLeft()
      this.getTopRight()
      this.getBottomLeft()
      this.getBottomRight()
    },
    // 收支趋势/万元
    getTopLeft() {
      const getchart = echarts.init(document.getElementById('top_left_Echarts'))
      var borderColor = ['#FFCA64', '#8BDDF5', '#61E29D']
      let dateArr = ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月']
      let echartsData = [
        {
          name: '总收入',
          value: [1400.5000, 4500, 2300, 3600, 7800, 4500, 5050, 6500, 4300, 4500, 7800, 7900]
        }, {
          name: '支出',
          value: [5400.2000, 3500, 4300, 5600, 5800, 7500, 6500, 5500, 6300, 3500, 4800, 6900]
        }, {
          name: '利润',
          value: [1400.3000, 2500, 5300, 7600, 4800, 5500, 8500, 2500, 5300, 6500, 4800, 5900]
        }
      ]
      const seriesObj = []
      echartsData.forEach((item, index) => {
        const randomRgbColor = this.$tools.randomRgbColor('array')
        seriesObj.push({
          name: item.name,
          type: 'line',
          smooth: false,
          showAllSymbol: true,
          symbol: 'circle',
          symbolSize: 0,
          itemStyle: {
            color: borderColor[index],
          },
          lineStyle: {
            width: 2,
            color: borderColor[index]
          },
          data: item.value
        })
      })
      const option = {
        title: {
          text: '收支趋势/元',
          top: '4%',
          left: '2%',
          textStyle: {
            fontSize: "16px",
            color: '#ffffff',
            fontWeight: "500"
          },
        },
        tooltip: {
          trigger: 'axis',
          backgroundColor: '#0D2169',
          borderColor: '#727382',
          textStyle: {
            color: '#fff'
          }
        },
        legend: {
          show: true,
          data: Array.from(echartsData, ({ name }) => name),
          type: 'scroll',
          orient: 'horizontal',
          left: 'center',
          top: '14%',
          itemGap: 16,
          textStyle: {
            color: '#ffffff'
          }
        },
        grid: {
          left: '5%',
          right: '5%',
          bottom: '8%',
          top: '27%',
          containLabel: true
        },
        xAxis: {
          type: 'category',
          data: dateArr,
          axisTick: {
            show: false,
          },
          splitLine: {
            show: true,
            lineStyle: {
              color: "rgba(255,255,255,0.2)",
              type: 'dashed'
            },
          },
          axisLabel: {
            show: true,
            color: "#ffffff",
            interval: 0,
            textStyle: {
              color: 'rgba(255,255,255,0.5)',
              fontSize: 10,
              fontWeight: 500,
            }
          },
          axisLine: {
            lineStyle: {
              color: "rgba(255,255,255,0.2)",
            },
          }
        },
        yAxis: [{
          name: '',
          type: 'value',
          lineStyle: {
            show: "none",
          },
          axisTick: {
            show: false,
          },
          axisLabel: {
            show: true,
            color: "rgba(255,255,255,0.6)",
          },
          splitLine: {
            show: true,
            lineStyle: {
              color: "rgba(255,255,255,0.2)",
              type: 'dashed'
            },
          }
        }, {
          type: 'value',
          lineStyle: {
            show: "none",
          },
          axisTick: {
            show: false,
          },
          axisLine: {
            show: false,
          },
          axisLabel: {
            show: false,
          },
          splitLine: {
            show: false,
          }
        }],
        series: seriesObj
      }
      getchart.clear()
      getchart.setOption(option)
      // 随着屏幕大小调节图表
      window.addEventListener('resize', () => {
        getchart.resize()
      })
    },
    //近5年费率分布/%
    getTopRight() {
      const getchart = echarts.init(document.getElementById('top_right_Echarts'))
      let xData = ['2020', '2021', '2022', '2023', '2024']
      let yData = [30, 22, 45, 29, 11]
      let y1Data = [26, 12, 53, 34, 43]
      let y2Data = [34, 32, 12, 45, 34]
      const option = {
        title: {
          text: '近5年费率分布/%',
          top: '2%',
          left: '2%',
          textStyle: {
            fontSize: "16px",
            color: '#ffffff',
            fontWeight: "500"
          },
        },
        tooltip: {
          trigger: 'axis',
          backgroundColor: '#0D2169',
          borderColor: '#727382',
          textStyle: {
            color: '#fff'
          },
          axisPointer: {
            type: "shadow",
          },
        },
        legend: {
          top: '15%',
          left: "center",
          itemHeight: 8,
          itemWidth: 8,
          textStyle: {
            "color": "#fff"
          }
        },
        grid: {
          left: '4%',
          right: '8%',
          bottom: '10%',
          top: '32%',
          containLabel: true
        },
        xAxis: {
          data: xData,
          axisLabel: {
            show: true,
            color: "#ffffff",
            interval: 0,
            textStyle: {
              color: 'rgba(255,255,255,0.6)',
              fontSize: 10,
              fontWeight: 500,
            }
          },
          axisLine: {
            lineStyle: {
              color: "rgba(255,255,255,0.2)",
            },
          }
        },
        yAxis: [{
          name: '',
          type: 'value',
          lineStyle: {
            show: "none",
          },
          axisTick: {
            show: false,
          },
          axisLabel: {
            show: true,
            color: "rgba(255,255,255,0.6)",
          },
          splitLine: {
            show: true,
            lineStyle: {
              color: "rgba(255,255,255,0.2)",
              type: 'dashed'
            },
          }
        }, {
          type: 'value',
          lineStyle: {
            show: "none",
          },
          axisTick: {
            show: false,
          },
          axisLine: {
            show: false,
          },
          axisLabel: {
            show: false,
          },
          splitLine: {
            show: false,
          }
        }],
        series: [
          {
            name: "利润率",
            type: "bar",
            barWidth: 10,
            itemStyle: {
              normal: {
                color: "#8BDDF5",
              }
            },
            data: yData
          },
          {
            name: "投资收益率",
            type: "bar",
            barWidth: 10,
            itemStyle: {
              normal: {
                color: "#3CC1FF",
              }
            },
            data: y1Data
          },
          {
            name: "维系费用率",
            type: "bar",
            barWidth: 10,
            itemStyle: {
              normal: {
                color: "#0A84FF",
              }
            },
            data: y2Data
          },
        ]
      }
      getchart.clear()
      getchart.setOption(option)
      // 随着屏幕大小调节图表
      window.addEventListener('resize', () => {
        getchart.resize()
      })
    },
    //在保资产统计
    getBottomLeft() {
      const getchart = echarts.init(document.getElementById('bottom_left_Echarts'))
      let xData = ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月']
      let yData = [1000, 2000, 4400, 2344, 5788, 4566, 3455, 5432, 5677, 3455, 5777, 5999]
      let y1Data = [2000, 1000, 2400, 1344, 2788, 1566, 1455, 2432, 1677, 4421, 4566, 6733]
      let y2Data = [3000, 4000, 1400, 4344, 4788, 2566, 5455, 4432, 4677, 2355, 3533, 4567]
      let y3Data = [1000, 5000, 4400, 5344, 1788, 3566, 2455, 1432, 3677, 2344, 4566, 3552]
      const option = {
        title: {
          text: '收支明细/元',
          top: '2%',
          left: '2%',
          textStyle: {
            fontSize: "16px",
            color: '#ffffff',
            fontWeight: "500"
          },
        },
        tooltip: {
          trigger: 'axis',
          backgroundColor: '#0D2169',
          borderColor: '#727382',
          textStyle: {
            color: '#fff'
          },
          axisPointer: {
            type: "shadow",
          },
        },
        legend: {
          top: '15%',
          left: "center",
          itemHeight: 8,
          itemWidth: 8,
          textStyle: {
            "color": "#fff"
          }
        },
        grid: {
          left: '4%',
          right: '8%',
          bottom: '18%',
          top: '32%',
          containLabel: true
        },
        dataZoom: [
          {
            show: true,
            height: 6,
            bottom: "10%",
            right: "3%",
            showDetail: false,
            brushSelect: false,
            backgroundColor: "transparent", // 背景颜色
            showDataShadow: false,
            borderColor: "transparent",
            fillerColor: "#8591CE", //拉动框的颜色
            startValue: 0,
            endValue: 8,
            brushSelect: false,
            handleSize: "60%",
            zoomLock: true,
            // 画一个圆形
            // handleIcon:
            //   "path://M30.9,53.2C16.8,53.2,5.3,41.7,5.3,27.6S16.8,2,30.9,2C45,2,56.4,13.5,56.4,27.6S45,53.2,30.9,53.2z M30.9,3.5M36.9,35.8h-1.3z M27.8,35.8 h-1.3H27L27.8,35.8L27.8,35.8z",
            handleStyle: {
              // 两侧缩放手柄的样式配置。
              borderColor: "#transparent",
              borderWidth: "5",
              shadowBlur: 1,
              background: "#transparent",
              shadowColor: "#transparent",
            },
          },
        ],
        xAxis: {
          type: 'category',
          data: xData,
          axisTick: {
            show: false,
          },
          splitLine: {
            show: true,
            lineStyle: {
              color: "rgba(255,255,255,0.2)",
              type: 'dashed'
            },
          },
          axisLabel: {
            show: true,
            color: "#ffffff",
            interval: 0,
            textStyle: {
              color: 'rgba(255,255,255,0.5)',
              fontSize: 10,
              fontWeight: 500,
            }
          },
          axisLine: {
            lineStyle: {
              color: "rgba(255,255,255,0.2)",
            },
          }
        },
        yAxis: [{
          name: '',
          type: 'value',
          lineStyle: {
            show: "none",
          },
          axisTick: {
            show: false,
          },
          axisLabel: {
            show: true,
            color: "rgba(255,255,255,0.6)",
          },
          splitLine: {
            show: true,
            lineStyle: {
              color: "rgba(255,255,255,0.2)",
              type: 'dashed'
            },
          }
        }, {
          type: 'value',
          lineStyle: {
            show: "none",
          },
          axisTick: {
            show: false,
          },
          axisLine: {
            show: false,
          },
          axisLabel: {
            show: false,
          },
          splitLine: {
            show: false,
          }
        }],
        series: [
          {
            name: "检查收入",
            type: "bar",
            barWidth: 10,
            itemStyle: {
              normal: {
                color: "#8BDDF5",
              }
            },
            data: yData
          },
          {
            name: "药品收入",
            type: "bar",
            barWidth: 10,
            itemStyle: {
              normal: {
                color: "#FFCA64",
              }
            },
            data: y1Data
          },
          {
            name: "耗材收入",
            type: "bar",
            barWidth: 10,
            itemStyle: {
              normal: {
                color: "#61E29D",
              }
            },
            data: y2Data
          },
          {
            name: "治疗收入",
            type: "bar",
            barWidth: 10,
            itemStyle: {
              normal: {
                color: "#FF2D55",
              }
            },
            data: y3Data
          }
        ]
      }
      getchart.clear()
      getchart.setOption(option)
      // 随着屏幕大小调节图表
      window.addEventListener('resize', () => {
        getchart.resize()
      })
    },
    //支出明细/元
    getBottomRight() {
      const getchart = echarts.init(document.getElementById('bottom_right_Echarts'))
      var borderColor = ['#61E29D', '#D9AC55', '#FD2D55', '#3CC1FF', '#0A84FF', '#FFCAD4', '#D6EFF1', '#FBCC8A', '#D2AEFF', '#9ED32B', '#20D4C1', '#A966FF']
      let dateArr = ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月']
      let echartsData = [
        {
          name: '药品',
          value: [1400.5000, 4500, 2300, 3600, 7800, 4500, 5050, 6500, 4300, 4500, 7800, 7900]
        },
        {
          name: '耗材',
          value: [5400.2000, 3500, 4300, 5600, 5800, 7500, 6500, 5500, 6300, 3500, 4800, 6900]
        },
        {
          name: '人员经费',
          value: [1400.3000, 2500, 5300, 7600, 4800, 5500, 8500, 2500, 5300, 6500, 4800, 5900]
        },
        {
          name: '维保',
          value: []
        },
        {
          name: '维修',
          value: []
        },
        {
          name: '电费',
          value: []
        },
        {
          name: '水费',
          value: []
        },
        {
          name: '设备折旧',
          value: []
        },
        {
          name: '房屋折旧',
          value: []
        },
        {
          name: '场地维修',
          value: []
        },
        {
          name: '物业管理',
          value: []
        },
        {
          name: '计量支出',
          value: []
        }
      ]
      const seriesObj = []
      echartsData.forEach((item, index) => {
        const randomRgbColor = this.$tools.randomRgbColor('array')
        seriesObj.push({
          name: item.name,
          type: 'line',
          smooth: false,
          showAllSymbol: true,
          symbol: 'circle',
          symbolSize: 0,
          itemStyle: {
            color: borderColor[index],
          },
          lineStyle: {
            width: 2,
            color: borderColor[index]
          },
          data: item.value
        })
      })
      const option = {
        title: {
          text: '支出明细/元',
          top: '4%',
          left: '2%',
          textStyle: {
            fontSize: "16px",
            color: '#ffffff',
            fontWeight: "500"
          },
        },
        tooltip: {
          trigger: 'axis',
          backgroundColor: '#0D2169',
          borderColor: '#727382',
          textStyle: {
            color: '#fff'
          }
        },
        legend: {
          show: true,
          data: Array.from(echartsData, ({ name }) => name),
          orient: 'horizontal',
          left: 'center',
          top: '14%',
          itemGap: 16,
          textStyle: {
            color: '#ffffff'
          }
        },
        grid: {
          left: '5%',
          right: '5%',
          bottom: '8%',
          top: '35%',
          containLabel: true
        },
        xAxis: {
          type: 'category',
          data: dateArr,
          axisTick: {
            show: false,
          },
          splitLine: {
            show: true,
            lineStyle: {
              color: "rgba(255,255,255,0.2)",
              type: 'dashed'
            },
          },
          axisLabel: {
            show: true,
            color: "#ffffff",
            interval: 0,
            textStyle: {
              color: 'rgba(255,255,255,0.5)',
              fontSize: 10,
              fontWeight: 500,
            }
          },
          axisLine: {
            lineStyle: {
              color: "rgba(255,255,255,0.2)",
            },
          }
        },
        yAxis: [{
          name: '',
          type: 'value',
          lineStyle: {
            show: "none",
          },
          axisTick: {
            show: false,
          },
          axisLabel: {
            show: true,
            color: "rgba(255,255,255,0.6)",
          },
          splitLine: {
            show: true,
            lineStyle: {
              color: "rgba(255,255,255,0.2)",
              type: 'dashed'
            },
          }
        }, {
          type: 'value',
          lineStyle: {
            show: "none",
          },
          axisTick: {
            show: false,
          },
          axisLine: {
            show: false,
          },
          axisLabel: {
            show: false,
          },
          splitLine: {
            show: false,
          }
        }],
        series: seriesObj
      }
      getchart.clear()
      getchart.setOption(option)
      // 随着屏幕大小调节图表
      window.addEventListener('resize', () => {
        getchart.resize()
      })
    },
  },
};
</script>
<style lang="scss" scoped>
%box-common-styles {
  flex-shrink: 0;
  box-sizing: border-box;
  background: rgba(53, 98, 219, 0.06);
  border-radius: 2p;
}
.box-view {
  position: relative;
  height: 100%;
  width: 100%;
  box-sizing: border-box;
  .box-top,
  .box-bottom {
    width: 100%;
    height: calc(50% - 0.5rem);
  }
  .box-top {
    width: 100%;
    display: flex;
    .box-top-left {
      width: calc((100% / 3) - 0.7rem);
      @extend %box-common-styles;
    }
    .box-top-center {
      margin: 0 1rem;
      width: calc((100% / 3) - 0.7rem);
      @extend %box-common-styles;
      .grossIncome {
        width: 100%;
        height: 100%;
        padding: 12px;
        &-title {
          font-weight: 500;
          font-size: 16px;
          color: #ffffff;
        }
        &-box {
          height: calc(100% - 20px);
        }
        &-content {
          height: calc((100% / 3) - 8px);
          background: rgba(53, 98, 219, 0.06);
          margin-top: 10px;
          display: flex;
          align-items: center;
        }
        .grossIncome-content-left {
          width: 68%;
          padding: 2px 24px;
          border-right: 1px solid rgba(133, 145, 206, 0.15);
          .item-block {
            display: inline-block;
            width: 12px;
            height: 12px;
            background: #8bddf5;
            border-radius: 2px;
            margin-right: 5px;
          }
          .item-label {
            font-weight: 500;
            font-size: 16px;
            color: #ffffff;
          }
          .item-value {
            font-weight: bold;
            font-size: 20px;
            color: #ffca64;
          }
          .item-unit {
            font-weight: 400;
            font-size: 16px;
            color: #a6afbf;
            margin-left: 4px;
          }
        }
        p:nth-child(2) {
          margin-top: 10px;
        }
        .grossIncome-content-right {
          text-align: center;
          flex: 1;
          padding: 4px 8px;
          .item-rate {
            font-weight: 500;
            font-size: 16px;
            color: #61e29d;
            margin-left: 2px;
          }
          .item-label {
            font-weight: 400;
            font-size: 14px;
            color: #ffffff;
          }
          img {
            margin-left: 2px;
            width: 19px;
            height: 19px;
            vertical-align: middle;
          }
        }
      }
    }
    .box-top-right {
      width: calc((100% / 3) - 0.7rem);
      @extend %box-common-styles;
    }
  }
  .box-bottom {
    margin-top: 1rem;
    display: flex;
    .box-bottom-left,
    .box-bottom-right {
      height: 100%;
      width: calc(50% - 0.5rem);
      @extend %box-common-styles;
    }
    .box-bottom-right {
      margin-left: 1rem;
    }
  }
}
</style>
