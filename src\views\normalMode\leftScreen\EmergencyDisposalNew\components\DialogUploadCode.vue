<template>
  <el-dialog class="component DialogUploadCode" v-if="visible" center v-dialogDrag :close-on-click-modal="false"
    :modal="false" :show-close="false" title="扫码上传文件" width="672px" custom-class="DialogUploadCode__dialog"
    :visible="visible" :before-close="closeDialog">
    <div class="DialogUploadCode__head">
      <div class="DialogUploadCode__close" @click="closeDialog"></div>
    </div>
    <div class="DialogUploadCode__content">
      <img class="DialogUploadCode__img" :src="imgData" alt="a qr-code image">
      <p class="DialogUploadCode__tip">
        请移动设备与客户端保持同一网段 <br>
        支持文件格式：jpg、png、pdf、doc、txt、xls
      </p>
    </div>
  </el-dialog>
</template>
<script>
import { queryAlarmAffirmQrCodeBase64 } from '@/utils/peaceLeftScreenApi';

export default {
  name: 'DialogUploadCode',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    alarmId: String
  },
  data() {
    return {
      imgData: '/tmp/qrcode.png',
    }
  },
  mounted() { },
  watch: {
    visible: {
      handler: function (val) {
        if (val) this.getQRcode()
      },
      immediate: true
    }
  },
  methods: {
    // 关闭弹窗
    closeDialog() {
      this.$emit('update:visible', false)
    },
    getQRcode() {
      if (!this.alarmId) return;
      const params = {
        alarmId: this.alarmId
      }
      queryAlarmAffirmQrCodeBase64(params)
        .then(res => {
          if (res.data.code == 200) {
            this.imgData = 'data:image/png;base64,' + res.data.data
          } else {
            this.$message.error(res.data.msg || '生成二维码失败')
          }
        })
    }
  }
}
</script>
<style lang="scss" scoped>
.component.DialogUploadCode {
  background: rgba(0, 0, 0, 0.6);

  ::v-deep .DialogUploadCode__dialog.el-dialog {
    background: url('~@/assets/images/qhdsys/672×672.png') no-repeat center center / 100% 100%;
    height: 672px;
    overflow: hidden;

    .el-dialog__header {
      padding: 10px 20px 10px;
      text-align: center;

      .el-dialog__title {
        height: 20px;
        font-size: 18px;
        font-family: Source Han Sans CN-Regular, Source Han Sans CN;
        font-weight: 400;
        color: #cbdeed;
        line-height: 18px;
      }
    }

    .el-dialog__body {
      height: calc(100% - 40px);
    }

    .el-dialog__footer {
      padding-top: 0;
      padding-bottom: 40px;
      padding-right: 40px;
    }
  }

  .DialogUploadCode {
    &__head {
      position: absolute;
      top: 18px;
      right: 52px;
      display: flex;
    }

    &__close {
      width: 36px;
      height: 36px;
      background: url('@/assets/images/qhdsys/bg-icon-gb.png') no-repeat;
      cursor: pointer;
    }

    &__content {
      height: 100%;
      display: flex;
      flex-flow: column nowrap;
      justify-content: center;
      align-items: center;
    }

    &__img {
      height: 338px;
      width: 338px;
      -webkit-user-drag: none;
      border-radius: 8px;
    }

    &__tip {
      margin-top: 24px;
      line-height: 24px;
      text-align: center;
      color: #FFFFFF;
    }
  }
}
</style>
