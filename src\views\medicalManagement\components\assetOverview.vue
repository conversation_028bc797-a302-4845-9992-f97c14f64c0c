<template>
  <div class="assetOverview clear">
    <simpleCard
      title="资产分析"
      class="fl"
      style="
        height: calc(100% / 2);
        width: calc(100% / 3 - 7px);
        margin-bottom: 8px;
      "
    >
      <div slot="title-right" class="card-title-right">
        <p class="view-more">详情 <i class="el-icon-arrow-right"></i></p>
      </div>
      <div slot="content" style="padding: 10px; height: 100%">
        <div class="count-list">
          <div
            v-for="item in asseCountList"
            :key="item.name"
            class="count-list-item"
          >
            <p class="item-name">{{ item.name }}</p>
            <p class="item-cont" :style="{ color: item.color }">
              <el-statistic
                :value-style="{ color: item.color }"
                group-separator=","
                :precision="0"
                :value="item.value"
                :title="''"
              ></el-statistic>
            </p>
          </div>
        </div>
        <div class="echart-box">
          <div class="e-box">
            <div class="e-box-title">资产品牌分布</div>
            <div class="e-box-div" style="height: calc(100% - 34px)">
              <div id="pie_brand" style="width: 100%; height: 100%"></div>
            </div>
          </div>
          <div class="e-box">
            <div class="e-box-title">设备价值统计</div>
            <div class="e-box-div" style="height: calc(100% - 34px)">
              <div id="pie_merit" style="width: 100%; height: 100%"></div>
            </div>
          </div>
        </div>
      </div>
    </simpleCard>
    <simpleCard
      title="监测设备统计"
      class="fl"
      style="
        height: calc(100% / 2);
        width: calc(100% / 3 - 7px);
        margin-left: 8px;
        margin-bottom: 8px;
      "
    >
      <div slot="title-right" class="card-title-right">
        <p class="view-more">详情 <i class="el-icon-arrow-right"></i></p>
      </div>
      <div slot="content" style="padding: 10px; height: 100%">
        <div class="count-list">
          <div
            v-for="item in monitorCountList"
            :key="item.name"
            class="count-list-item"
          >
            <p class="item-name">{{ item.name }}</p>
            <p class="item-cont" :style="{ color: item.color }">
              <el-statistic
                :value-style="{ color: item.color }"
                group-separator=","
                :precision="0"
                :value="item.value"
                :title="''"
              ></el-statistic>
            </p>
          </div>
        </div>
        <div class="echart-box">
          <div class="e-box">
            <div class="e-box-title">运行状态统计</div>
            <div class="e-box-div" style="height: calc(100% - 34px)">
              <div
                id="pie_runningState"
                style="width: 100%; height: 100%"
              ></div>
            </div>
          </div>
          <div class="e-box">
            <div class="e-box-title">监测状态统计</div>
            <div class="e-box-div" style="height: calc(100% - 34px)">
              <div
                id="pie_monitorState"
                style="width: 100%; height: 100%"
              ></div>
            </div>
          </div>
        </div>
      </div>
    </simpleCard>
    <simpleCard
      title="资产分布排序"
      class="fl"
      style="
        height: calc(100% / 2);
        width: calc(100% / 3 - 7px);
        margin-left: 8px;
        margin-bottom: 8px;
      "
    >
      <div slot="title-right" class="card-title-right">
        <p class="view-select">科室 <i class="el-icon-arrow-down"></i></p>
        <p class="view-more">详情 <i class="el-icon-arrow-right"></i></p>
      </div>
      <div slot="content" style="padding: 10px; height: 100%">
        <div class="echart-box" style="height: 100%">
          <div id="bar_assetScatter" style="width: 100%; height: 100%"></div>
        </div>
      </div>
    </simpleCard>
    <simpleCard
      title="平均使用时长"
      class="fl"
      style="height: calc(50% - 8px); width: calc(100% / 3 - 7px)"
    >
      <div slot="title-right" class="card-title-right">
        <p class="view-select">设备类型 <i class="el-icon-arrow-down"></i></p>
        <p class="view-select">本周 <i class="el-icon-arrow-down"></i></p>
      </div>
      <div slot="content" style="padding: 10px; height: 100%">
        <div class="count-list">
          <div
            v-for="item in averageUsageDuration"
            :key="item.name"
            class="count-list-item"
          >
            <p class="item-name">{{ item.name }}</p>
            <p class="item-cont">
              <span :style="{ color: item.color }">{{ item.value }}</span
              ><span>小时</span>
            </p>
          </div>
        </div>
        <div class="echart-box" style="display: block">
          <div class="echart-title-box">
            <p>
              <i style="background: #ff2d55; width: 5px; height: 5px"></i
              >高于平均使用时长
            </p>
            <p>
              <i style="background: #61e29d; width: 5px; height: 5px"></i
              >低于平均使用时长
            </p>
            <p><i style="color: #ffca64">---</i>平均线</p>
          </div>
          <div class="et-box">
            <div id="bar_useDuration" style="width: 100%; height: 100%"></div>
          </div>
        </div>
      </div>
    </simpleCard>
    <simpleCard
      title="平均开机时长"
      class="fl"
      style="
        height: calc(50% - 8px);
        width: calc(100% / 3 - 7px);
        margin-left: 8px;
      "
    >
      <div slot="title-right" class="card-title-right">
        <p class="view-select">科室 <i class="el-icon-arrow-down"></i></p>
        <p class="view-select">本周 <i class="el-icon-arrow-down"></i></p>
      </div>
      <div slot="content" style="padding: 10px; height: 100%">
        <div class="count-list">
          <div
            v-for="item in averageBootTime"
            :key="item.name"
            class="count-list-item"
          >
            <p class="item-name">{{ item.name }}</p>
            <p class="item-cont">
              <span :style="{ color: item.color }">{{ item.value }}</span
              ><span>小时</span>
            </p>
          </div>
        </div>
        <div class="echart-box" style="display: block">
          <div class="echart-title-box">
            <p>
              <i style="background: #ff2d55; width: 5px; height: 5px"></i
              >高于平均开机时长
            </p>
            <p>
              <i style="background: #61e29d; width: 5px; height: 5px"></i
              >低于平均开机时长
            </p>
            <p><i style="color: #ffca64">---</i>平均线</p>
          </div>
          <div class="et-box">
            <div
              id="bar_startupDuration"
              style="width: 100%; height: 100%"
            ></div>
          </div>
        </div>
      </div>
    </simpleCard>
    <simpleCard
      title="日平均开机top5"
      class="fl"
      style="
        height: calc(25% - 4px);
        width: calc(100% / 3 - 7px);
        margin-left: 8px;
        margin-bottom: 8px;
      "
    >
      <div slot="title-right" class="card-title-right">
        <p class="view-select">设备类型 <i class="el-icon-arrow-down"></i></p>
        <p class="view-select">本周 <i class="el-icon-arrow-down"></i></p>
      </div>
      <div slot="content" style="padding: 10px; height: 100%">
        <div class="echart-box" style="height: 100%; padding: 0px">
          <div id="bar_dayTop5" style="width: 100%; height: 100%"></div>
        </div>
      </div>
    </simpleCard>
    <simpleCard
      title="累计平均开机top5"
      class="fl"
      style="
        height: calc(25% - 12px);
        width: calc(100% / 3 - 7px);
        margin-left: 8px;
      "
    >
      <div slot="title-right" class="card-title-right">
        <p class="view-select">设备类型 <i class="el-icon-arrow-down"></i></p>
        <p class="view-select">本周 <i class="el-icon-arrow-down"></i></p>
      </div>
      <div slot="content" style="padding: 10px; height: 100%">
        <div class="echart-box" style="height: 100%; padding: 0px">
          <div id="bar_addUpTop5" style="width: 100%; height: 100%"></div>
        </div>
      </div>
    </simpleCard>
  </div>
</template>

<script>
import * as echarts from "echarts";
export default {
  name: "assetOverview",
  data() {
    return {
      asseCountList: [
        { name: "资产总数", value: 145, color: "#FFCA64" },
        { name: "使用中", value: 145, color: "#FFCA64" },
        { name: "维系中", value: 145, color: "#FFCA64" },
        { name: "闲置中", value: 145, color: "#FFCA64" },
        { name: "已报废", value: 145, color: "#FFCA64" },
        { name: "已外借", value: 145, color: "#FF2D55" },
        { name: "已红冲", value: 145, color: "#FF2D55" },
      ],
      monitorCountList: [
        { name: "监测总数", value: 145, color: "#FFCA64" },
        { name: "使用中", value: 145, color: "#FFCA64" },
        { name: "维系中", value: 145, color: "#FFCA64" },
        { name: "闲置中", value: 145, color: "#FFCA64" },
        { name: "已报废", value: 145, color: "#FFCA64" },
        { name: "已外借", value: 145, color: "#FF2D55" },
        { name: "已红冲", value: 145, color: "#FF2D55" },
      ],
      averageUsageDuration: [
        { name: "平均使用时长", value: 145, color: "#FFCA64" },
        { name: "高于平均使用时长", value: 145, color: "#FF2D55" },
        { name: "低于平均使用时长", value: 145, color: "#61E29D" },
      ],
      averageBootTime: [
        { name: "平均开机时长", value: 145, color: "#FFCA64" },
        { name: "高于平均开机时长", value: 145, color: "#FF2D55" },
        { name: "低于平均开机时长", value: 145, color: "#61E29D" },
      ],
      pieIdData: [
        "pie_brand",
        "pie_merit",
        "pie_runningState",
        "pie_monitorState",
      ],
      barDuration: ["bar_useDuration", "bar_startupDuration"],
      barDouble: ["bar_dayTop5", "bar_addUpTop5"],
    };
  },
  computed: {},
  // 监听全屏缩放 动态改变echarts图表大小
  watch: {
    "$store.state.dialogFullScreenState": {
      handler(val) {
        this.$nextTick(() => {
          setTimeout(() => {
            this.pieIdData.forEach((item) => this.initAssetBrandScatter(item));
            this.initAssetDistribution();
            this.barDuration.forEach((item) => this.initBarChart(item));
            this.barDouble.forEach((item) => this.initDoubleBarChart(item));
          }, 200);
        });
      },
      deep: true,
    },
  },
  mounted() {
    this.pieIdData.forEach((item) => this.initAssetBrandScatter(item));
    this.initAssetDistribution();
    this.barDuration.forEach((item) => this.initBarChart(item));
    this.barDouble.forEach((item) => this.initDoubleBarChart(item));
  },
  methods: {
    /** 初始化 饼图 */
    initAssetBrandScatter(item) {
      let dataObj = {
        pie_brand: [
          {
            count: 150,
            name: "品牌名1",
            percentage: 30,
          },
          {
            count: 69,
            name: "品牌名2",
            percentage: 0,
          },
          {
            count: 50,
            name: "品牌名3",
            percentage: 0,
          },
          {
            count: 10,
            name: "品牌名4",
            percentage: 0,
          },
        ],
        pie_merit: [
          {
            count: 150,
            name: "大型设备",
            percentage: 30,
          },
          {
            count: 150,
            name: "低值设备",
            percentage: 30,
          },
          {
            count: 150,
            name: "其他设备",
            percentage: 30,
          },
        ],
        pie_runningState: [
          {
            count: 150,
            name: "开机中",
            percentage: 30,
          },
          {
            count: 150,
            name: "关机中",
            percentage: 30,
          },
          {
            count: 150,
            name: "待机中",
            percentage: 30,
          },
          {
            count: 150,
            name: "工作中",
            percentage: 30,
          },
          // {
          //   count: 150,
          //   name: "离线",
          //   percentage: 30,
          // },
        ],
        pie_monitorState: [
          {
            count: 150,
            name: "正常",
            percentage: 30,
          },
          {
            count: 150,
            name: "预警",
            percentage: 30,
          },
          {
            count: 150,
            name: "异常",
            percentage: 30,
          },
        ],
      };
      // 基于准备好的dom，初始化echarts实例
      let getchart = echarts.init(document.getElementById(item));
      const data = [];
      var color = ["#FF2D55", "#61E29D ", "#FFAD65", "#e38e6f", "#f2d988"];
      const xdata = Array.from(dataObj[item], ({ name }) => name);
      for (var i = 0; i < dataObj[item].length; i++) {
        const randomRgbColor = this.$tools.randomRgbColor("array");
        data.push({
          name: dataObj[item][i].name,
          value: dataObj[item][i].count,
          percentage: dataObj[item][i].percentage,
          itemStyle: {
            normal: {
              borderWidth: 1,
              shadowBlur: 200,
              color: color[i] ?? randomRgbColor[0],
              label: {
                normal: { show: true },
              },
            },
          },
        });
      }
      const option = {
        tooltip: {
          trigger: "item",
          axisPointer: {
            type: "shadow",
          },
          backgroundColor: '#0D2169',
          borderColor: '#727382',
          textStyle: {
            color: '#fff'
          },
        },
        backgroundColor: "",
        title: {
          text: "{name|" + "设备总数" + "}\n{val|" + 269 + "}",
          top: "28%",
          left: "47%",
          textStyle: {
            rich: {
              name: {
                fontSize: 14,
                fontWeight: "normal",
                color: "#ffffff",
                padding: [5, 0],
              },
              val: {
                fontSize: 16,
                fontWeight: "bold",
                color: "#ffffff",
                padding: [0, 10],
              },
            },
          },
          textAlign: "center",
          textVerticalAlign: "center",
        },
        legend: {
          // 取消legend和echarts的联动
          selectedMode: false,
          icon: "circle",
          orient: "vartical",
          x: "left",
          left: "center",
          bottom: "0%",
          data: xdata,
          itemWidth: 8,
          itemHeight: 8,
          itemGap: 5,
          type: "scroll",
          orient: "vertical",
          // height: 30,
          textStyle: {
            color: "#B3C2DD", //  字体颜色
          },
          formatter: function (name) {
            var oa = option.series[0].data;
            for (var i = 0; i < option.series[0].data.length; i++) {
              if (name === oa[i].name) {
                return (
                  " " +
                  name +
                  "    " +
                  oa[i].value +
                  "    " +
                  oa[i].percentage +
                  "%"
                );
              }
            }
          },
        },
        series: [
          {
            name: "",
            type: "pie",
            clockWise: false,
            center: ["50%", "28%"],
            radius: ["38%", "50%"],
            label: {
              normal: {
                show: false,
                position: "center",
                formatter: "{value|{c}}\n{label|{b}}",
                rich: {
                  value: {
                    padding: 5,
                    align: "center",
                    verticalAlign: "middle",
                    fontSize: 20,
                  },
                  label: {
                    align: "center",
                    verticalAlign: "middle",
                    fontSize: 14,
                  },
                },
              },
            },
            // hoverAnimation: false,
            data: data,
          },
        ],
      };
      getchart.clear();
      getchart.resize();
      getchart.setOption(option);
      // 随着屏幕大小调节图表
      window.addEventListener("resize", () => {
        getchart.resize();
      });
    },
    /** 初始化资产分布 横向柱状图 */
    initAssetDistribution() {
      let data = [
        {
          id: "1",
          name: "科室名称1",
          value: 4000,
        },
        {
          id: "2",
          name: "科室名称2",
          value: 3000,
        },
        {
          id: "3",
          name: "科室名称3",
          value: 2500,
        },
        {
          id: "4",
          name: "品牌名称4",
          value: 2000,
        },
        {
          id: "5",
          name: "科室名称5",
          value: 1500,
        },
        {
          id: "6",
          name: "科室名称6",
          value: 1000,
        },
        {
          id: "7",
          name: "科室名称7",
          value: 1000,
        },
        {
          id: "8",
          name: "品牌名称8",
          value: 800,
        },
        {
          id: "9",
          name: "科室名称9",
          value: 700,
        },
        {
          id: "10",
          name: "科室名称10",
          value: 550,
        },
        {
          id: "11",
          name: "科室名称11",
          value: 400,
        },
      ];
      // 基于准备好的dom，初始化echarts实例
      let EChart = echarts.init(document.getElementById("bar_assetScatter"));
      //   将data数据中得value值从大到小排序，然后按顺序将value放入dataValue数组中 name放入dataName数组中 name和value一一对应
      data.sort(function (a, b) {
        return a.value - b.value;
      });
      const dataName = Array.from(data, (item) => {
        item.name = item.name || "未命名";
        const match = item.name.match(this.regex);
        const name = item.name;
        return {
          value: name,
          textStyle: {
            color: "#FFF",
          },
        };
      });
      // 配置参数
      let config = {
        legend: {},
        grid: {
          top: "5%",
          left: "3%",
          right: "8%",
          bottom: "0%",
          containLabel: true,
        },
        xAxis: {
          type: "value",
          show: false,
        },
        yAxis: [
          {
            type: "category",
            data: dataName,
            axisTick: {
              show: false, // 不显示坐标轴刻度线
            },
            axisLine: {
              show: false, // 不显示坐标轴线
            },
            splitLine: {
              // 网格线
              show: false,
            },
            axisLabel: { textStyle: { color: "#fff", fontSize: "14" } },
          },
          {
            type: "category", // 坐标轴类型
            // inverse: true, // 是否是反向坐标轴
            axisTick: {
              show: false, // 不显示坐标轴刻度线
            },
            axisLine: {
              show: false, // 不显示坐标轴线
            },
            splitLine: {
              // 网格线
              show: false,
            },
            axisLabel: {
              textStyle: {
                color: "#FFFFFFCC",
                fontSize: "14",
              },
              formatter: (value) => {
                return 4000;
              },
            },
            data: data,
          },
        ],
        series: [
          {
            type: "bar",
            stack: "total",
            label: {
              show: false,
            },
            emphasis: {
              focus: "series",
            },
            data: data,
            barWidth: 10,
            itemStyle: {
              normal: {
                // barBorderRadius: [15, 15, 15, 15],
                color: function (params) {
                  // 通过返回值的下标一一对应将颜色赋给柱子上
                  return "rgb(97, 165, 232)";
                },
              },
              // 鼠标移入改变颜色
              emphasis: {
                color: "rgb(97, 165, 232)",
              },
            },
            showBackground: true, // 显示背景色
            backgroundStyle: {
              color: "#384156",
            },
          },
        ],
        dataZoom: [
          {
            yAxisIndex: [0, 1],
            orient: "vertical",
            show: true,
            type: "slider",
            start: 100,
            // end: 100,
            width: 8,
            left: "99%",
            borderColor: "rgba(43,48,67,.1)",
            fillerColor: "#6580b8",
            zoomLock: true,
            showDataShadow: false,
            backgroundColor: "rgba(43,48,67,.1)",
            showDetail: false,
            // realtime: true,
            filterMode: "filter",
            handleIcon:
              "M-9.35,34.56V42m0-40V9.5m-2,0h4a2,2,0,0,1,2,2v21a2,2,0,0,1-2,2h-4a2,2,0,0,1-2-2v-21A2,2,0,0,1-11.35,9.5Z",
            handleStyle: {
              color: "#6580b8",
              borderColor: "#6580b8",
            },
            maxValueSpan: 9,
            minValueSpan: 9,
            brushSelect: false,
          },
          {
            type: "inside",
            show: false,
            yAxisIndex: [0, 1],
            height: 8,
          },
        ],
      };
      EChart.clear();
      EChart.resize();
      // 设置参数
      EChart.setOption(config);
      // 先移除点击事件 解决点击事件重复绑定
      EChart.getZr().off("click");
      // 点击事件
      EChart.getZr().on("click", (params) => {
        var pointInPixel = [params.offsetX, params.offsetY];
        if (EChart.containPixel("grid", pointInPixel)) {
          // 获取当前点击的索引值
          // 注意：若柱状图为纵向则获取x轴的索引，若柱状图为横向则需获取y轴的索引(见下方注释)
          // var xIndex=EChart.convertFromPixel({seriesIndex:0},[params.offsetX, params.offsetY])[0];
          // var xData=config.xAxis.data[xIndex];//当前点击柱子的数据
          var yIndex = EChart.convertFromPixel({ seriesIndex: 0 }, [
            params.offsetX,
            params.offsetY,
          ])[1];
          var yData = config.yAxis[1].data[yIndex]; // 当前点击柱子的数据
          // 新增变量 判断当前点击柱状图是选中还是取消选中
          let isSelected = false;
          dataName.map((e, i) => {
            // 选中的设置选中色
            if (i == yIndex && e.textStyle.color != "rgb(97, 165, 232)") {
              e.textStyle.color = "rgb(97, 165, 232)";
            } else {
              // 选中已选中的则为取消选中
              if (i == yIndex && e.textStyle.color == "rgb(97, 165, 232)") {
                isSelected = true;
              }
              // 其他的设为默认色
              e.textStyle.color = "#FFF";
            }
          });
          config.yAxis[0].data = JSON.parse(JSON.stringify(dataName));
          config.dataZoom[0].start = (yIndex / dataName.length) * 100;
          EChart.setOption(config);
          // ....  业务逻辑
          // 取消选中 则恢复过滤条件
          let id = !isSelected ? yData.id : "";
          if (this.isType === "bm") {
            this.paramsData = {
              deptId: id,
              spaceState: "",
            };
            this.getRoomCountByDeptIdList(this.isCommand, id);
          }
        }
      });
    },
    /** 初始化 平均使用/开机时长 横向柱状图 */
    initBarChart(item) {
      var chartDom = document.getElementById(item);
      var myChart = echarts.init(chartDom);
      var option;
      option = {
        tooltip: {
          trigger: "axis",
          axisPointer: {
            type: "shadow",
          },
          backgroundColor: '#0D2169',
          borderColor: '#727382',
          textStyle: {
            color: '#fff'
          },
        },
        legend: {
          show: false,
        },
        grid: {
          top: "10%",
          left: "3%",
          right: "8%",
          bottom: "0%",
          containLabel: true,
        },
        xAxis: {
          type: "value",
          show: false,
          axisTick: {
            show: false,
          },
          axisLabel: {
            textStyle: {
              color: "rgba(230,247,255,0.6)", //坐标值的具体的颜色
            },
          },
          splitLine: {
            show: true,
            lineStyle: {
              color: ["rgba(233, 233, 233,0.1)"], //分割线的颜色
              type: "dashed",
            },
          },
        },
        yAxis: {
          type: "category",
          axisLine: {
            show: true,
            lineStyle: {
              color: ["rgba(233, 233, 233,0.1)"], //分割线的颜色
              type: "dashed",
            },
          },
          axisLabel: {
            textStyle: {
              color: "rgba(230,247,255,0.6)", //坐标值的具体的颜色
            },
          },
          splitLine: {
            show: true,
            lineStyle: {
              color: ["rgba(233, 233, 233,0.1)"], //分割线的颜色
              type: "dashed",
            },
          },
          data: [
            "设备类型1",
            "设备类型2",
            "设备类型3",
            "设备类型4",
            "设备类型5",
            "设备类型6",
          ],
        },
        series: [
          {
            name: "2011",
            type: "bar",
            data: [10, 20, 30, 40, 50, 60],
            barWidth: 10,
            label: {
              show: true,
              position: "right",
              color: "#fff",
            },
            itemStyle: {
              color: function (params) {
                // 自定义颜色数组
                const colors = ["#FF2D55", "#61E29D"];
                return params.data > 35 ? colors[0] : colors[1];
              },
            },
            markLine: {
              silent: true,
              symbol: "none",
              label: {
                show: false,
                color: "#FFCA64",
              },
              itemStyle: {
                normal: {
                  lineStyle: {
                    color: "#FFCA64",
                  },
                },
              },
              data: [{ type: "average", name: "", symbol: "none" }],
            },
          },
        ],
      };
      myChart.resize();
      myChart.clear();
      option && myChart.setOption(option);
      window.addEventListener("resize", () => {
        myChart.resize();
      });
    },
    /** 初始化 日/累计时长 top5 柱状图 */
    initDoubleBarChart(item) {
      var chartDom = document.getElementById(item);
      var myChart = echarts.init(chartDom);
      var option;

      option = {
        grid: {
          top: "25%",
          left: "3%",
          right: "3%",
          bottom: "0%",
          containLabel: true,
        },
        tooltip: {
          trigger: "axis",
          axisPointer: {
            type: "shadow",
          },
          backgroundColor: '#0D2169',
          borderColor: '#727382',
          textStyle: {
            color: '#fff'
          },
        },
        legend: {
          textStyle: {
            color: "#fff",
            fontSize: 12,
          },
          top: "0%",
          itemWidth: 8,
          itemHeight: 8,
          itemGap: 20,
          selectedMode: false,
        },
        xAxis: {
          type: "category",
          data: ["麻醉机", "宫腔镜", "呼吸机", "CT", "手术机器人"],
          nameTextStyle: {
            color: "#fff",
            fontSize: 12,
          },
          axisLabel: {
            interval: 0,
            textStyle: {
              color: "rgba(230,247,255,0.6)", //坐标值的具体的颜色
            },
          },
          splitLine: {
            show: true,
            lineStyle: {
              color: ["rgba(233, 233, 233,0.1)"], //分割线的颜色
              type: "dashed",
            },
          },
        },
        yAxis: {
          type: "value",
          interval: "auto",
          axisLabel: {
            textStyle: {
              color: "rgba(230,247,255,0.6)", //坐标值的具体的颜色
            },
          },
          splitLine: {
            show: true,
            lineStyle: {
              color: ["rgba(233, 233, 233,0.1)"], //分割线的颜色
              type: "dashed",
            },
          },
        },
        series: [
          {
            name: "平均开机时长",
            type: "bar",
            data: [100, 90, 80, 60, 40],
            barWidth: "10",
            itemStyle: {
              normal: {
                color: "#8BDDF5",
              },
            },
          },
          {
            name: "平均工作时长",
            type: "bar",
            data: [80, 75, 55, 30, 15],
            barWidth: "10",
            itemStyle: {
              normal: {
                color: "#FFCA64",
              },
            },
          },
        ],
      };

      myChart.resize();
      myChart.clear();
      option && myChart.setOption(option);
      window.addEventListener("resize", () => {
        myChart.resize();
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.assetOverview {
  height: 100%;
  width: 100%;
  box-sizing: border-box;
  padding: 0px 40px;
  overflow: hidden;
  .count-list {
    background: rgba(133, 145, 206, 0.05);
    display: flex;
    justify-content: space-between;
    padding: 10px;
    .count-list-item {
      .item-name {
        font-weight: 400;
        font-size: 12px;
        color: #ffffff;
        line-height: 14px;
      }
      .item-cont {
        margin-top: 5px;
        font-weight: bold;
        font-size: 16px;
        line-height: 19px;
        text-align: center;
        span:nth-child(2) {
          color: #ffffff;
          font-size: 12px;
          margin-left: 4px;
          font-weight: 500;
        }
      }
    }
  }
  .card-title-right {
    display: flex;
    .view-select {
      color: #fff;
      font-size: 14px;
      cursor: pointer;
    }
    .view-more {
      color: #8bddf5;
      font-size: 14px;
      cursor: pointer;
    }
    p {
      margin-left: 16px;
    }
  }
  .fl {
    float: left;
  }

  .fr {
    float: right;
  }
  .clear:after {
    content: ".";
    display: block;
    clear: both;
    height: 0;
    overflow: hidden;
    visibility: hidden;
  }
  .echart-box {
    height: calc(100% - 3.75rem);
    box-sizing: border-box;
    padding-top: 0.5rem;
    display: flex;
    .e-box {
      width: 49.5%;
      height: 100%;
      background: #8591ce0d;
      &-title {
        padding: 10px;
        color: #fff;
      }
    }
    .e-box:nth-child(1) {
      margin-right: 0.5rem;
    }
    .echart-title-box {
      width: 100%;
      height: 12px;
      font-size: 12px;
      display: flex;
      justify-content: space-around;
      p {
        display: flex;
        align-items: center;
        i {
          margin-right: 5px;
        }
      }
    }
    .et-box {
      height: calc(100% - 12px);
    }
  }
}
</style>
