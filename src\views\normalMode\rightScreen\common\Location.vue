<template>
  <div class="main">
    <el-dialog v-dialogDrag custom-class="mainDialog main" append-to-body :visible.sync="changeLocationShow" :close-on-click-modal="false" :before-close="closeDialog" class="personDialog">
      <template slot="title">
        <span class="dialog-title">服务地点选择</span>
      </template>
      <div class="dialog-content">
        <el-input style="margin-bottom: 8px" v-model="filterText" placeholder="请输入关键词"></el-input>
        <el-radio-group size="mini" @change="locationChange" v-model="localRadio">
          <el-radio-button :label="0">一级区域</el-radio-button>
          <el-radio-button :label="1">二级区域</el-radio-button>
          <el-radio-button :label="2">三级区域</el-radio-button>
          <el-radio-button :label="3">四级区域</el-radio-button>
        </el-radio-group>
        <div :title="selectService.name" class="select-servive">{{ selectService.name }}</div>
        <el-tree
          class="tree"
          :default-expanded-keys="expendData"
          :filter-node-method="filterNode"
          ref="tree"
          node-key="id"
          :data="itemTreeData"
          :props="defaultProps"
          @node-click="handleNodeClick"
          v-loading="treeLoading"
          element-loading-background="rgba(0, 0, 0, 0.2)"
        ></el-tree>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button class="sino-button-sure" @click="closeDialog">取 消</el-button>
        <el-button class="sino-button-sure" type="primary" @click="savePeople">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>
<script>
import { getGridTreeData } from '@/utils/peaceRightScreenApi'
export default {
  name: 'Location',
  props: {
    changeLocationShow: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      filterText: '',
      itemTreeData: [],
      defaultProps: {
        label: 'ssmName',
        children: 'children'
      },
      selectRow: {},
      selectService: {},
      localRadio: '',
      expendData: [],
      expendDataList: [[]],
      treeLoading: false
    }
  },
  watch: {
    filterText(val) {
      this.$refs.tree.filter(val)
    }
  },
  mounted() {
    this.getGridTreeData()
  },
  methods: {
    getGridTreeData() {
      this.treeLoading = true
      getGridTreeData({}).then((res) => {
        const data = res.data
        if (data.code === '200') {
          this.expendDataList.push(this.getLevelData(2, data.data))
          this.expendDataList.push(this.getLevelData(3, data.data))
          this.expendDataList.push(this.getLevelData(4, data.data))
          this.itemTreeData = this.$tools.listToTree(data.data, 'id', 'parentId')
          this.treeLoading = false
        } else {
          this.$message({
            message: data.message,
            type: 'warning'
          })
        }
      })
      // getAssociationList(params).then((res) => {})
    },
    getLevelData(level, data) {
      const filterData = data.filter((e) => e.ssmType === level)
      if (filterData.length) {
        return Array.from(filterData, ({ id }) => id)
      } else {
        return []
      }
    },
    locationChange(val) {
      for (var i = 0; i < this.$refs.tree.store._getAllNodes().length; i++) {
        this.$refs.tree.store._getAllNodes()[i].expanded = false
      }
      this.expendData = this.expendDataList[val]
    },
    handleNodeClick(data, node) {
      this.selectRow = data
      if (data.ssmType === 3) {
        this.selectService = {
          name: node.parent.data.ssmName + data.ssmName,
          id: node.parent.data.id + '_' + data.id
        }
      } else if (data.ssmType === 4) {
        this.selectService = {
          name: node.parent.parent.data.ssmName + node.parent.data.ssmName + data.ssmName,
          id: node.parent.parent.data.id + '_' + node.parent.data.id + '_' + data.id
        }
      } else if (data.ssmType === 5) {
        this.selectService = {
          name: node.parent.parent.parent.data.ssmName + node.parent.parent.data.ssmName + node.parent.data.ssmName + data.ssmName,
          id: node.parent.parent.parent.data.id + '_' + node.parent.parent.data.id + '_' + node.parent.data.id + '_' + data.id
        }
      }
    },
    filterNode(value, data) {
      if (!value) return true
      return data.name.indexOf(value) !== -1
    },
    // 取消按钮
    closeDialog() {
      this.$emit('closeDialog')
    },
    // 确认按钮
    savePeople() {
      if (this.selectRow.ssmType < 3 || JSON.stringify(this.selectService) === '{}') {
        return this.$message({
          message: '请选择区域以下节点关联！',
          type: 'warning'
        })
      }
      this.$emit('localSure', this.selectService)
      // }
    }
  }
}
</script>

<style lang="scss" type="text/css" scoped>
::v-deep .mainDialog {
  width: 35%;
  background: #031553;
  .el-dialog__body {
    padding: 10px 13% 10px 5%;
    box-sizing: border-box;
    height: 60vh;
    color: #a3a9c0;
  }
  .el-dialog__footer {
    padding-right: 30px;
  }
  .dialog-title {
    color: #fff;
    font-family: PingFangSC-Medium, PingFang SC;
  }
  .dialog-title::before {
    content: '';
    display: inline-block;
    width: 2px;
    border-radius: 1px;
    height: 13px;
    background: #ffe3a6;
    margin-right: 10px;
  }
  .dialog-content {
    width: 100%;
    height: 100%;
    .tree {
      height: calc(100% - 110px);
      overflow-y: scroll;
    }
    .select-servive {
      height: 30px;
      line-height: 30px;
      width: 100%;
      font-size: 15px;
      color: #ffe3a6;
      margin-left: 15px;
      display: inline-block;
      margin-top: 5px;
      // background: rgba(17, 45, 138, 0.5);
      // padding: 0 12px;
    }
    .el-radio-button__orig-radio:checked + .el-radio-button__inner {
      background: rgba(255, 176, 55, 0.2);
      border: none;
      color: #fff;
    }
    .el-radio-button {
      margin-right: 8px;
      border: none;
    }
    .el-radio-button__inner {
      background: rgba(17, 45, 138, 0.5);
      border: none;
      box-shadow: none;
      color: #fff;
    }
    .el-radio-button:last-child .el-radio-button__inner,
    .el-radio-button:first-child .el-radio-button__inner {
      border-radius: 0;
    }
    .el-radio-button:focus:not(.is-focus):not(:active):not(.is-disabled) {
      box-shadow: none;
      border: none;
    }
  }
}
</style>
