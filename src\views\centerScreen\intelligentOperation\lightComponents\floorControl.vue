<template>
  <div class="floorControl">
    <div class="top_type" v-if="ssmType != '4'">
      <div class="top_type_left">{{requestParams.floorName}}</div>
      <div class="top_type_right">
        <!-- <div class="top_type_item" @click="changeType(0)">
          <span>全关</span>
        </div>
        <div class="top_type_item" @click="changeType(1)">
          <span>全开</span>
        </div> -->
      </div>
    </div>
    <div class="bottom_table">
      <el-input class="search_ipt" prefix-icon="el-icon-search" placeholder="请输入回路名称" @input="handleInput" v-model.trim="loopName"></el-input>
      <div class="scoll_table" :style="{ 'max-height': ssmType == '4' ? 'calc(95vh - 10rem)' : 'calc(95vh - 29.5rem)' }">
        <div class="loop_table" v-for="(item, index) in tableData" :key="index">
          <div class="table_title">
            <span class="table_title_box"></span>
            <div>{{item.actuatorName}}</div>
          </div>
          <el-table
            class="table-center-transfer"
            :data="item.lightingBySpace"
            :cell-style="$tools.setCell(3)"
            :header-cell-style="$tools.setHeaderCell(3)"
            style="width: 100%"
            element-loading-background="rgba(0, 0, 0, 0.2)"
          >
            <el-table-column :key="1" align="center" fixed prop="actuatorOutPutName" show-overflow-tooltip label="回路名称"></el-table-column>
            <el-table-column :key="2" align="center" fixed prop="spaceName" show-overflow-tooltip label="服务空间"></el-table-column>
            <el-table-column :key="4" align="center" fixed width="80" show-overflow-tooltip label="状态">
              <template slot-scope="scope">
                <div class="operation">
                  <img style="cursor: auto;" v-if="scope.row.lightingStatus == 1" :src="require('@/assets/images/center/on-light.png')" />
                  <img style="cursor: auto;" v-else :src="require('@/assets/images/center/off-light.png')" />
                </div>
              </template>
            </el-table-column>
            <!-- <el-table-column :key="4" align="center" fixed width="80" show-overflow-tooltip label="操作">
              <template slot-scope="scope">
                <el-tooltip popper-class="tooltip" effect="light" placement="right">
                  <div slot="content" class="light-opeartion">
                    <div v-if="scope.row.lightingStatus == 1" @click="switchOperation(0, scope.row)">关</div>
                    <div v-else @click="switchOperation(1, scope.row)">开</div>
                    <div @click="switchOperation(3, scope.row)">强开</div>
                    <div @click="switchOperation(4, scope.row)">强关</div>
                  </div>
                  <div class="operation">
                    <img v-if="scope.row.lightingStatus == 1" :src="require('@/assets/images/center/on-light.png')" />
                    <img v-else :src="require('@/assets/images/center/off-light.png')" />
                  </div>
                </el-tooltip>
              </template>
            </el-table-column> -->
          </el-table>
        </div>
      </div>
    </div>
    <template v-if="controlReportDialogShow">
      <controlReportDialog :controlReportDialogShow="controlReportDialogShow" :dialogData="controlDialogData" @closeControlDialog="closeControlDialog" />
    </template>
  </div>
</template>
<script>
import { GetLightingByConstruction, GetLightingByFloor, LightOpenOrClose } from '@/utils/centerScreenApi'
import { mapGetters } from 'vuex'
export default {
  components: {
    controlReportDialog: () => import('./controlReportDialog.vue')
  },
  props: {
    // 5:楼层 4:楼栋
    ssmType: {
      type: String,
      default: '5'
    },
    requestParams: {
      type: Object,
      default: () => {
        return {}
      }
    },
    modelCode: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      controlReportDialogShow: false, // 控制报表弹窗显示
      controlDialogData: {}, // 控制报表弹窗数据
      tableData: [],
      loopName: '',
      inputTimer: null // 节流定时器
    }
  },
  computed: {
    ...mapGetters({
      socketIemcMsgs: 'socketIemcMsgs'
    })
  },
  watch: {
    requestParams: {
      handler: function(newV, oldV) {
        this.searchTableData()
      },
      deep: true
    },
    socketIemcMsgs(data) {
      const projectData = JSON.parse(data)
      // 匹配设备相同的接收消息刷新页面
      if (projectData.projectCode === this.requestParams.projectCode && projectData.data === 'LightRefresh') {
        // 收到消息刷新页面
        console.log('匹配成功', projectData)
        if (this.ssmType === '4' || this.ssmType === '5') {
          this.searchTableData()
          try {
            window.chrome.webview.hostObjects.sync.bridge.LightingRefresh(this.modelCode)
          } catch (error) {}
        }
      }
    }
  },
  created() {
    this.searchTableData()
  },
  methods: {
    switchOperation(status, item) {
      const param = {
        type: 3,
        forceSwitch: status === 3 || status === 4 ? status : null,
        outputStatus: status === 3 ? 1 : (status === 4 ? 0 : status),
        groupControl: item.surveyCode
      }
      LightOpenOrClose(param).then(res => {
        if (res.data.code === '200') {
          this.searchTableData()
        } else {
          this.$message.warning(res.data.message)
        }
      })
    },
    changeType(type) {
      const param = {
        type: 4,
        forceSwitch: null,
        outputStatus: type,
        serviceSpaceId: this.requestParams.floorId
      }
      try {
        window.chrome.webview.hostObjects.sync.bridge.ShowDialog(true)
      } catch (error) {}
      this.$confirm('您正在强制执行开/关操作，当前操作数量N。确认是否执行？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        cancelButtonClass: 'sino-button-sure',
        confirmButtonClass: 'sino-button-sure',
        customClass: 'confirm-box-class',
        type: 'warning'
      }).then(() => {
        try {
          window.chrome.webview.hostObjects.sync.bridge.ShowDialog(false)
        } catch (error) {}
        LightOpenOrClose(param).then(res => {
          if (res.data.code === '200') {
            this.controlDialogData = res.data.data
            this.controlReportDialogShow = true
          } else {
            this.$message.warning(res.data.message)
          }
        })
      }).catch(() => {
        try {
          window.chrome.webview.hostObjects.sync.bridge.ShowDialog(false)
        } catch (error) {}
      })
    },
    // 关闭报表弹窗
    closeControlDialog() {
      this.controlReportDialogShow = false
      this.searchTableData()
    },
    // 输入框节流
    handleInput() {
      if (this.inputTimer) {
        clearTimeout(this.inputTimer)
      }
      this.inputTimer = setTimeout(() => {
        this.inputTimer = null
        this.searchTableData()
      }, 1000)
    },
    // 获取列表回路数据
    searchTableData() {
      let param = {}
      const newArr = []
      this.tableData = []
      if (this.ssmType === '4') { // 建筑
        param = {
          projectCode: this.requestParams.projectCode,
          actuatorOutPutName: this.loopName,
          constructionId: this.requestParams.constructionId,
          name: this.requestParams.constructionName
        }
        GetLightingByConstruction(param).then(res => {
          if (res.data.code === '200') {
            this.tableData = res.data.data
            this.tableData.forEach(item => {
              item.lightingBySpace.forEach(v => {
                newArr.push(v.spaceInfo.spaceId)
              })
            })
            try {
              window.chrome.webview.hostObjects.sync.bridge.SetLightingBackgroundColor(newArr.join(','))
            } catch (error) {}
          }
        })
      } else if (this.ssmType === '5') {
        param = {
          projectCode: this.requestParams.projectCode,
          actuatorOutPutName: this.loopName,
          floorId: this.requestParams.floorId,
          name: this.requestParams.floorName
        }
        GetLightingByFloor(param).then(res => {
          if (res.data.code === '200') {
            this.tableData = [res.data.data]
            this.tableData.forEach(item => {
              item.lightingBySpace.forEach(v => {
                newArr.push(v.spaceInfo.spaceId)
              })
            })
            try {
              window.chrome.webview.hostObjects.sync.bridge.SetLightingBackgroundColor(newArr.join(','))
            } catch (error) {}
          }
        })
      }
    }
  }
}
</script>
<style lang="scss" scoped>
.top_type {
  width: calc(100% - 20px);
  padding: 5px 15px;
  box-sizing: border-box;
  // height: 4.75rem;
  margin: 10px auto;
  background-color: rgba(1, 11, 59, 0.5);
  display: flex;
  justify-content: space-between;
  .top_type_left {
    font-size: 16px;
    font-family: PingFangSC-Regular, PingFang SC;
    width: fit-content;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    line-height: 2.5rem;
  }
  .top_type_right {
    display: flex;
    .top_type_item {
      width: 4.5rem;
      margin: 3px 2%;
      height: 2.2rem;
      line-height: 2.2rem;
      text-align: center;
      font-size: 14px;
      font-family: PingFang-SC-Medium, PingFang-SC;
      color: #93bdff;
      background: url('~@/assets/images/center/block-bg.png') no-repeat;
      background-size: 100% 100%;
      cursor: pointer;
      &:hover {
        color: #fff;
        background: url('~@/assets/images/center/block-bg-active.png') no-repeat;
        background-size: 100% 100%;
      }
    }
  }
}
.center_tab {
  padding: 15px 0;
  text-align: center;
  span {
    display: inline-block;
    width: 50px;
    height: 25px;
    font-size: 14px;
    text-align: center;
    line-height: 26px;
    color: #7eaef9;
    cursor: pointer;
  }
  span:hover,
  .activeTab {
    background: url('~@/assets/images/center/light-yellow.png') no-repeat;
    background-size: 100% 100%;
    color: #ffe3a6;
  }
  i {
    color: #7eaef9;
  }
}
.bottom_table {
  // max-height: calc(95vh - 27rem);
  // overflow-y: auto;
  ::v-deep .search_ipt {
    height: 2.5rem;
    width: calc(100% - 20px);
    margin-left: 10px;
    padding-bottom: 5px;
    .el-input__inner {
      background-color: rgba(1, 11, 59, 0.5);
    }
    .el-input__icon {
      color: #5996f9;
    }
  }
  .scoll_table {
    max-height: calc(95vh - 29.5rem);
    overflow-y: auto;
    .operation {
      img {
        width: 17px;
        height: 22px;
        cursor: pointer;
      }
    }
  }
  .table_title {
    height: 2rem;
    line-height: 2rem;
    display: flex;
    margin-top: 6px;
    span {
      display: block;
      width: 4px;
      height: 4px;
      background: #ffe3a6;
      margin: auto 10px auto 13px;
    }
    div {
      font-size: 14px;
      font-family: PingFangSC-Regular, PingFang SC;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
    }
  }
  ::v-deep .el-table {
    border: none !important;
    .el-table__header thead th {
      background: rgba(15, 34, 103, 0.45) !important;
    }
    .el-table__body tr {
      background: rgba(17, 27, 77, 0.45) !important;
    }
  }
}
.light-opeartion {
  height: auto;
  width: auto;
  color: #fff;
  div {
    padding: 8px 15px;
    font-size: 14px;
    cursor: pointer;
    &:hover {
      color: #ffe3a6;
    }
  }
}
</style>
