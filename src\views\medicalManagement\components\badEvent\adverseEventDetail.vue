<template>
  <div class="statistics-view">
    <div class="sic-search-box">
      <div class="sic-search-left">
        <el-form :model="searchForm" class="search-form" inline ref="formRef">
          <el-form-item>
            <el-select v-model="searchForm.year" size="small" placeholder="统计年度">
              <el-option v-for="item in brandOptions" :key="item.value" :label="item.label" :value="item.value">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-select v-model="searchForm.deptCode" size="small" placeholder="科室">
              <el-option v-for="item in deptOptions" :key="item.value" :label="item.label" :value="item.value">
              </el-option>
            </el-select>
          </el-form-item>
        </el-form>
      </div>
      <div class="sic-search-right">
        <el-button class="sino-button-sure" @click="resetForm">重置</el-button>
        <el-button class="sino-button-sure" @click="handleSearchForm">查询</el-button>
      </div>
    </div>
    <div class="sic-content">
      <div class="sic-content-4column">
        <div class="sic-content-4column-box">
          <div class="sic-title-box">
            <span>事件状态统计</span>
          </div>
          <div class="sic-charts-box">
            <div id="eventStateEchartNew" style="width: 100%;height: 100%;"></div>
          </div>
        </div>
        <div class="sic-content-4column-box">
          <div class="sic-title-box">
            <span>事件上报状态</span>
          </div>
          <div class="sic-charts-box">
            <div id="reportStatusEchartNew" style="width: 100%;height: 100%;"></div>
          </div>
        </div>
        <div class="sic-content-4column-box">
          <div class="sic-title-box">
            <span>近5年不良事件趋势</span>
          </div>
          <div class="sic-charts-box">
            <div id="adverseEventTrendEchartNew" style="width: 100%;height: 100%;"></div>
          </div>
        </div>
        <div class="sic-content-4column-box">
          <div class="sic-title-box">
            <span>品牌不良事件排行/次</span>
          </div>
          <div class="sic-charts-box">
            <div id="adverseEventEchartNew" style="width: 100%;height: 100%;"></div>
          </div>
        </div>
      </div>
      <div class="sic-content-table">
        <div class="sic-content-table-toggle">
          <div class="title">不良事件汇总表</div>
          <div class="toggle">
            <div :class="{ 'is-activeTab': isType === 'month' }" @click="toggleChange('month')">月度</div>
            <div :class="{ 'is-activeTab': isType === 'year' }" @click="toggleChange('year')">年度</div>
          </div>
        </div>
        <div class="sic-content-tableHeight">
          <el-table :data="tableData" height="calc(100%)" :cell-style="$tools.setCell(3)"
            :header-cell-style="$tools.setHeaderCell(3)" style="width: 100%"
            element-loading-background="rgba(0, 0, 0, 0.2)" v-loading="tableLoading">
            <el-table-column prop="time" show-overflow-tooltip label="时间"></el-table-column>
            <el-table-column prop="count" show-overflow-tooltip label="不良事件数"></el-table-column>
            <el-table-column prop="examine" show-overflow-tooltip label="待审核数"></el-table-column>
            <el-table-column prop="passNumber" show-overflow-tooltip label="已通过数"></el-table-column>
            <el-table-column prop="unPassNumber" show-overflow-tooltip label="未通过数"></el-table-column>
            <el-table-column prop="unReportNumber" show-overflow-tooltip label="未上报数"></el-table-column>
            <el-table-column prop="reportNumber" show-overflow-tooltip label="已上报数"></el-table-column>
            <el-table-column prop="brandNumber" show-overflow-tooltip label="品牌次数"></el-table-column>
          </el-table>
          <div class="sic-content-table-pagination">
            <el-pagination @size-change="handleSizeChange" @current-change="handleCurrentChange"
              :current-page="currentPage" :page-sizes="[15, 20, 30, 40]" :page-size="pageSize"
              layout="total, sizes, prev, pager, next, jumper" :total="total" class="pagination"></el-pagination>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import * as echarts from 'echarts'
export default {
  data() {
    return {
      searchForm: {
        year: '2024',
        deptCode: '',
      },
      deptOptions: [],
      brandOptions: [{
        label: '2024',
        value: '2024'
      }],
      tableData: [],
      tableLoading: false,
      currentPage: 1,
      pageSize: 15,
      total: 5,
      isType: 'month'
    };
  },
  // 监听全屏缩放 动态改变echarts图表大小
  watch: {
    '$store.state.dialogFullScreenState': {
      handler(val) {
        this.$nextTick(() => {
          const echartsDom = ['eventStateEchartNew', 'reportStatusEchartNew', 'adverseEventTrendEchartNew', 'adverseEventEchartNew']
          setTimeout(() => {
            echartsDom.forEach((item) => {
              if (document.getElementById(item)) {
                echarts.init(document.getElementById(item)).resize()
              }
            })
          }, 200)
        })
      },
      deep: true
    }
  },
  mounted() {
    this.initData()
    this.getListData()
  },
  methods: {
    /** 重置 */
    resetForm() {
      this.$refs.formRef.resetFields()
      this.initData()
    },
    /** 查询 */
    handleSearchForm() {
      this.initData()
    },
    //图表
    initData() {
      this.$nextTick(() => {
        this.getBadPersonalLeaveList()
        this.getEventStateList()
        this.getAdverseEventTrendData()
        this.getReportStatusList()
      })
    },
    //事件上报状态
    getReportStatusList() {
      let arr = [
        {
          count: 144,
          name: "未上报",
          percentage: 30
        }, {
          count: 122,
          name: "已上报",
          percentage: 25
        }
      ]
      // 基于准备好的dom，初始化echarts实例
      let getchart = echarts.init(document.getElementById('reportStatusEchartNew'))
      const data = []
      var color = ['#FF2D55', '#61E29D ', '#FFAD65', '#e38e6f', '#f2d988']
      const xdata = Array.from(arr, ({ name }) => name)
      for (var i = 0; i < arr.length; i++) {
        const randomRgbColor = this.$tools.randomRgbColor('array')
        data.push({
          name: arr[i].name,
          value: arr[i].count,
          percentage: arr[i].percentage,
          itemStyle: {
            borderWidth: 1,
            shadowBlur: 200,
            color: color[i] ?? randomRgbColor[0],
            label: {
              show: true
            }
          }
        })
      }
      const option = {
        backgroundColor: '',
        title: {
          text: "{name|" + '事件总数' + "}\n{val|" + 13269 + "}",
          top: "45%",
          left: "43%",
          textStyle: {
            rich: {
              name: {
                fontSize: 14,
                fontWeight: "normal",
                color: '#ffffff',
                padding: [10, 10],
              },
              val: {
                fontSize: 18,
                fontWeight: "bold",
                color: "#ffffff",
                padding: [0, 10],
              },
            },
          },
          textAlign: 'center',
          textVerticalAlign: 'center',
        },
        series: [
          {
            name: '',
            type: 'pie',
            clockWise: false,
            minAngle: 15,//最小角度
            startAngle: 180, //起始角度
            center: ['44%', '50%'],
            radius: ['58%', '70%'],
            labelLine: {
              length: 10,
              length2: 20
            },
            label: {
              formatter: params => {
                return (
                  '{name|' + params.name + '  ' + params.data.percentage + '%' + '}\n{value|' + params.data.value + '}'
                );
              },
              rich: {
                name: {
                  fontSize: 12,
                  padding: [0, 0, 0, 10],
                  color: '#ffffff'
                },
                value: {
                  fontSize: 12,
                  fontWeight: 'bolder',
                  padding: [10, 0, 0, 20],
                  color: '#ffffff'
                }
              }
            },
            // hoverAnimation: false,
            data: data
          }
        ]
      }
      getchart.setOption(option)
      // 随着屏幕大小调节图表
      window.addEventListener('resize', () => {
        getchart.resize()
      })
    },
    //事件状态
    getEventStateList() {
      let arr = [
        {
          count: 150,
          name: "待审核数",
          percentage: 30
        }, {
          count: 69,
          name: "已通过数",
          percentage: 0
        }, {
          count: 50,
          name: "未通过数",
          percentage: 0
        }
      ]
      // 基于准备好的dom，初始化echarts实例
      let getchart = echarts.init(document.getElementById('eventStateEchartNew'))
      const data = []
      var color = ['#FF2D55', '#61E29D ', '#FFAD65', '#e38e6f', '#f2d988']
      const xdata = Array.from(arr, ({ name }) => name)
      for (var i = 0; i < arr.length; i++) {
        const randomRgbColor = this.$tools.randomRgbColor('array')
        data.push({
          name: arr[i].name,
          value: arr[i].count,
          percentage: arr[i].percentage,
          itemStyle: {
            borderWidth: 1,
            shadowBlur: 200,
            color: color[i] ?? randomRgbColor[0],
            label: {
              show: true
            }
          }
        })
      }
      const option = {
        backgroundColor: '',
        title: {
          text: "{name|" + '事件总数' + "}\n{val|" + 269 + "}",
          top: "45%",
          left: "26%",
          textStyle: {
            rich: {
              name: {
                fontSize: 14,
                fontWeight: "normal",
                color: '#ffffff',
                padding: [10, 0],
              },
              val: {
                fontSize: 18,
                fontWeight: "bold",
                color: "#ffffff",
                padding: [0, 10],
              },
            },
          },
          textAlign: 'center',
          textVerticalAlign: 'center',
        },
        legend: {
          // 取消legend和echarts的联动
          selectedMode: false,
          icon: 'circle',
          orient: 'vartical',
          x: 'left',
          top: 'center',
          left: '50%',
          bottom: '0%',
          data: xdata,
          itemWidth: 8,
          itemHeight: 8,
          itemGap: 16,
          textStyle: {
            color: '#B3C2DD' //  字体颜色
          },
          formatter: function (name) {
            var oa = option.series[0].data
            for (var i = 0; i < option.series[0].data.length; i++) {
              if (name === oa[i].name) {
                return ' ' + name + '    ' + oa[i].value + '    ' + oa[i].percentage + '%'
              }
            }
          }
        },
        series: [
          {
            name: '',
            type: 'pie',
            clockWise: false,
            center: ['27%', '50%'],
            radius: ['58%', '70%'],
            label: {
              show: false,
              position: 'center',
              formatter: '{value|{c}}\n{label|{b}}',
              rich: {
                value: {
                  padding: 5,
                  align: 'center',
                  verticalAlign: 'middle',
                  fontSize: 20
                },
                label: {
                  align: 'center',
                  verticalAlign: 'middle',
                  fontSize: 14
                }
              },
            },
            // hoverAnimation: false,
            data: data
          }
        ]
      }
      getchart.clear()
      getchart.setOption(option)
      // 随着屏幕大小调节图表
      window.addEventListener('resize', () => {
        getchart.resize()
      })
    },
    // 不良事件趋势
    getAdverseEventTrendData() {
      let resData = {
        xAxisData: ['2020', '2021', '2022', '2023', '2024'],
        seriesData: [5, 10, 16, 17, 30],
      }
      let maxArr = []
      let maxArr2 = []
      if (!resData || !resData.xAxisData || resData.xAxisData.length == 0) {
        resData = {
          xAxisData: [''],
          seriesData: [0]
        }
        maxArr = [100000]
      } else {
        const max = Math.max(...resData.seriesData)
        for (let i = 0; i < resData.seriesData.length; i++) {
          maxArr.push(100000)
          maxArr2.push(max + 5)
        }
      }
      // 基于准备好的dom，初始化echarts实例
      let getchart = echarts.init(document.getElementById('adverseEventTrendEchartNew'))
      const option = {
        backgroundColor: '',
        tooltip: {
          trigger: 'axis',
        },
        grid: {
          left: "20px",
          right: "20px",
          top: "10px",
          bottom: "10px",
          containLabel: true,
        },
        xAxis: {
          type: 'category',
          data: resData.xAxisData,
          axisTick: {
            show: false,
          },
          splitLine: {
            show: true,
            lineStyle: {
              color: "rgba(255,255,255,0.2)",
              type: 'dashed'
            },
          },
          axisLabel: {
            show: true,
            color: "#ffffff",
            interval: 0,
            formatter: function (value) {
              if (value && value.length > 5) {
                value = value.replace(/\s/g, '').replace(/(.{5})/g, "$1\n");
                value = value.slice(0, value.length - 2)
              }
              return value
            },
            textStyle: {
              color: 'rgba(255,255,255,0.5)',
              fontSize: 10,
              fontWeight: 500,
            }
          },
          axisLine: {
            lineStyle: {
              color: "rgba(255,255,255,0.2)",
            },
          }
        },
        yAxis: [{
          name: '',
          nameTextStyle: { color: 'rgba(255,255,255,0.2)' },
          type: 'value',
          lineStyle: {
            show: "none",
          },
          axisTick: {
            show: false,
          },
          axisLine: {
            show: true,
            lineStyle: {
              color: "rgba(255,255,255,0.2)",
            },
          },
          axisLabel: {
            show: true,
            color: "rgba(255,255,255,0.5)",
          },
          splitLine: {
            show: true,
            lineStyle: {
              color: "rgba(255,255,255,0.2)",
              type: 'dashed'
            },
          }
        }, {
          type: 'value',
          lineStyle: {
            show: "none",
          },
          axisTick: {
            show: false,
          },
          axisLine: {
            show: false,
          },
          axisLabel: {
            show: false,
          },
          splitLine: {
            show: false,
          }
        }],
        series: [
          {
            name: '近5年不良事件趋势',
            type: 'bar',
            barWidth: 24,
            barMinHeight: 3,
            z: 12,
            barGap: '10%',
            itemStyle: {
              color: "#8BDDF5",
            },
            emphasis: {
              disabled: true,
            },
            label: {
              show: false,
              position: 'top',
              fontSize: 12
            },
            data: resData.seriesData,
          }, {
            name: '背景1',
            yAxisIndex: 0,
            tooltip: {
              show: false
            },
            type: 'bar',
            barWidth: 24,
            barMinHeight: 3,
            barGap: '-100%',
            barCateGoryGap: '10%',
            itemStyle: {
              color: 'rgba(255,255,255,0.1)'
            },
            emphasis: {
              disabled: true,
            },
            data: maxArr2
          }, {
            name: '背景2',
            yAxisIndex: 1,
            tooltip: {
              show: false
            },
            type: 'bar',
            barWidth: 24,
            barMinHeight: 3,
            barGap: '-100%',
            barCateGoryGap: '10%',
            itemStyle: {
              color: 'rgba(255,255,255,0.1)'
            },
            emphasis: {
              disabled: true,
            },
            data: maxArr
          }
        ]
      }
      getchart.clear()
      getchart.setOption(option)
      // 随着屏幕大小调节图表
      window.addEventListener('resize', () => {
        getchart.resize()
      })
    },
    // 品牌不良事件排行
    getBadPersonalLeaveList() {
      let data = [
        {
          id: "1",
          name: '品牌名称1',
          value: 4000,
        }, {
          id: "2",
          name: '品牌名称2',
          value: 3000,
        }, {
          id: "3",
          name: '品牌名称3',
          value: 2500,
        }, {
          id: "4",
          name: '品牌名称4',
          value: 2000,
        }, {
          id: "5",
          name: '品牌名称5',
          value: 1500,
        }, {
          id: "6",
          name: '品牌名称6',
          value: 1000,
        }, {
          id: "7",
          name: '品牌名称7',
          value: 1000,
        }
      ]
      // 基于准备好的dom，初始化echarts实例
      let EChart = echarts.init(document.getElementById('adverseEventEchartNew'))
      //   将data数据中得value值从大到小排序，然后按顺序将value放入dataValue数组中 name放入dataName数组中 name和value一一对应
      data.sort(function (a, b) {
        return a.value - b.value
      })
      const dataName = Array.from(data, (item) => {
        item.name = item.name || '未命名'
        const match = item.name.match(this.regex)
        const name = item.name
        return {
          value: name,
          textStyle: {
            color: '#FFF'
          }
        }
      })
      // 配置参数
      let config = {
        legend: {},
        grid: {
          top: '5%',
          left: '3%',
          right: '8%',
          bottom: '0%',
          containLabel: true
        },
        xAxis: {
          type: 'value',
          show: false
        },
        yAxis: [
          {
            type: 'category',
            data: dataName,
            axisTick: {
              show: false // 不显示坐标轴刻度线
            },
            axisLine: {
              show: false // 不显示坐标轴线
            },
            splitLine: {
              // 网格线
              show: false
            },
            axisLabel: { textStyle: { color: '#fff', fontSize: '14' } }
          },
          {
            type: 'category', // 坐标轴类型
            // inverse: true, // 是否是反向坐标轴
            axisTick: {
              show: false // 不显示坐标轴刻度线
            },
            axisLine: {
              show: false // 不显示坐标轴线
            },
            splitLine: {
              // 网格线
              show: false
            },
            axisLabel: {
              textStyle: {
                color: '#FFFFFFCC',
                fontSize: '14'
              },
              formatter: (value) => {
                return 4000
              }
            },
            data: data
          }
        ],
        series: [
          {
            type: 'bar',
            stack: 'total',
            label: {
              show: false
            },
            emphasis: {
              focus: 'series'
            },
            data: data,
            barWidth: 8,
            itemStyle: {

              // barBorderRadius: [15, 15, 15, 15],
              color: function (params) {
                // 通过返回值的下标一一对应将颜色赋给柱子上
                return '#FFCA64'
              },
              // 鼠标移入改变颜色
              emphasis: {
                color: '#FFCA64FF'
              }
            },
            showBackground: true, // 显示背景色
            backgroundStyle: {
              color: '#384156'
            }
          }
        ],
        dataZoom: [
          {
            yAxisIndex: [0, 1],
            orient: 'vertical',
            show: true,
            type: 'slider',
            start: 100,
            // end: 100,
            width: 8,
            left: '99%',
            borderColor: 'rgba(43,48,67,.1)',
            fillerColor: '#6580b8',
            zoomLock: true,
            showDataShadow: false,
            backgroundColor: 'rgba(43,48,67,.1)',
            showDetail: false,
            // realtime: true,
            filterMode: 'filter',
            handleIcon: 'M-9.35,34.56V42m0-40V9.5m-2,0h4a2,2,0,0,1,2,2v21a2,2,0,0,1-2,2h-4a2,2,0,0,1-2-2v-21A2,2,0,0,1-11.35,9.5Z',
            handleStyle: {
              color: '#6580b8',
              borderColor: '#6580b8'
            },
            maxValueSpan: 4,
            minValueSpan: 4,
            brushSelect: false
          },
          {
            type: 'inside',
            show: false,
            yAxisIndex: [0, 1],
            height: 8
          }
        ]
      }
      // 设置参数
      EChart.setOption(config)
      // 先移除点击事件 解决点击事件重复绑定
      EChart.getZr().off('click')
      // 点击事件
      EChart.getZr().on('click', (params) => {
        var pointInPixel = [params.offsetX, params.offsetY]
        if (EChart.containPixel('grid', pointInPixel)) {
          // 获取当前点击的索引值
          // 注意：若柱状图为纵向则获取x轴的索引，若柱状图为横向则需获取y轴的索引(见下方注释)
          // var xIndex=EChart.convertFromPixel({seriesIndex:0},[params.offsetX, params.offsetY])[0];
          // var xData=config.xAxis.data[xIndex];//当前点击柱子的数据
          var yIndex = EChart.convertFromPixel({ seriesIndex: 0 }, [params.offsetX, params.offsetY])[1]
          var yData = config.yAxis[1].data[yIndex] // 当前点击柱子的数据
          // 新增变量 判断当前点击柱状图是选中还是取消选中
          let isSelected = false
          dataName.map((e, i) => {
            // 选中的设置选中色
            if (i == yIndex && e.textStyle.color != '#FFCA64FF') {
              e.textStyle.color = '#FFCA64FF'
            } else {
              // 选中已选中的则为取消选中
              if (i == yIndex && e.textStyle.color == '#FFCA64FF') {
                isSelected = true
              }
              // 其他的设为默认色
              e.textStyle.color = '#FFF'
            }
          })
          config.yAxis[0].data = JSON.parse(JSON.stringify(dataName))
          config.dataZoom[0].start = (yIndex / dataName.length) * 100
          EChart.setOption(config)
          // ....  业务逻辑
          // 取消选中 则恢复过滤条件
          let id = !isSelected ? yData.id : ''
          if (this.isType === 'bm') {
            this.paramsData = {
              deptId: id,
              spaceState: ''
            }
            this.getRoomCountByDeptIdList(this.isCommand, id)
          }
        }
      })
    },
    //table
    getListData() {
      this.tableData = [
        {
          time: '2024-10-28 19:37:35',
          count: 298,
          examine: 144,
          passNumber: 496,
          unPassNumber: 386,
          unReportNumber: 474,
          reportNumber: 364,
          brandNumber: 540,
        },
        {
          time: '2024-10-31 16:47:01',
          count: 122,
          examine: 166,
          passNumber: 408,
          unPassNumber: 100,
          unReportNumber: 166,
          reportNumber: 232,
          brandNumber: 628,
        },
        {
          time: '2024-10-17 16:16:42',
          count: 100,
          examine: 276,
          passNumber: 518,
          unPassNumber: 122,
          unReportNumber: 188,
          reportNumber: 254,
          brandNumber: 606,
        },
        {
          time: '2024-10-16 03:51:13',
          count: 188,
          examine: 232,
          passNumber: 430,
          unPassNumber: 144,
          unReportNumber: 210,
          reportNumber: 276,
          brandNumber: 584,
        },
        {
          time: '2024-11-03 09:34:42',
          count: 210,
          examine: 254,
          passNumber: 452,
          unPassNumber: 320,
          unReportNumber: 342,
          reportNumber: 298,
          brandNumber: 562,
        }
      ]
    },
    toggleChange(type) {
      this.isType = type
      this.getListData()
    },
    handleSizeChange(val) {
      this.pageSize = val
      this.getListData()
    },
    handleCurrentChange(val) {
      this.currentPage = val
      this.getListData()
    },
  },
};
</script>
<style lang="scss" scoped>
%sic-common-styles {
  flex-shrink: 0;
  margin-right: 1rem;
  box-sizing: border-box;
  background: rgba(53, 98, 219, 0.06);
}
.statistics-view {
  height: 100%;
  padding: 0px 40px;
  .sic-search-box {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 1.25rem;
  }
  .sic-content {
    height: calc(100% - 60px);
    width: 100%;
    .sic-content-4column {
      height: 33%;
      display: flex;
      flex-wrap: wrap;
      &-box {
        width: calc(25% - 0.8rem);
        height: 100%;
        @extend %sic-common-styles;
        display: flex;
        flex-direction: column;
        .sic-charts-box {
          flex: 1;
        }
      }
      &-box:nth-child(4n) {
        margin-right: 0px !important;
      }
    }
    .sic-title-box {
      padding: 0.75rem;
      font-size: 0.875rem;
    }
    .sic-content-table {
      margin-top: 1rem;
      background: rgba(53, 98, 219, 0.06);
      height: 57%;
    }
    .sic-content-table-toggle {
      display: flex;
      justify-content: space-between;
      padding: 10px 0;
      .title {
        font-weight: 500;
        font-size: 16px;
        color: #ffffff;
      }
      .toggle {
        display: flex;
        div {
          cursor: pointer;
          width: 52px;
          height: 26px;
          line-height: 26px;
          text-align: center;
          background: rgba(255, 255, 255, 0.1)
            linear-gradient(
              90deg,
              rgba(10, 132, 255, 0) 0%,
              rgba(10, 132, 255, 0) 100%
            );
          border-radius: 0px 0px 0px 0px;
          opacity: 1;
          font-size: 14px;
          font-family: HarmonyOS Sans SC-Regular, HarmonyOS Sans SC;
          font-weight: 400;
          color: #8bddf5;
        }
        div:nth-child(2) {
          margin: 0 6px;
        }
      }
      .is-activeTab {
        background: url("@/assets/images/qhdsys/bg-kj.png") no-repeat !important;
      }
    }
    .sic-content-tableHeight {
      height: calc(100% - 130px);
    }
    .sic-content-table-pagination {
      margin-top: 16px;
    }
  }
}
.el-form-item {
  margin-bottom: 0px;
}
</style>
