<template>
  <ModuleCard
    title="电梯列表"
    class="middle-content"
    :style="{ height: '50%' }"
  >
    <div slot="title-right" class="middle-right">
      <img
        src="@/assets/images/qhdsys/bg-gd.png"
        alt=""
        style="margin-left: 10px"
        @click="showDeviceList"
      />
    </div>
    <div slot="content" style="height: 98%">
      <el-table
        :data="list"
        class="table-center-transfer"
        height="100%"
        :cell-style="$tools.setCell(4)"
        :header-cell-style="$tools.setHeaderCell(4)"
        style="width: 100%"
        element-loading-background="rgba(0, 0, 0, 0.2)"
        @row-click="rowClick"
      >
        <el-table-column
          prop="assetsName"
          label="电梯名称"
          width="105"
          show-overflow-tooltip=""
        >
        </el-table-column>
        <el-table-column label="当前层" show-overflow-tooltip  width="75">
          <template slot-scope="scope">
            {{ setLabel("floor", scope.row.iotPropertyList) }}
          </template>
        </el-table-column>
        <el-table-column label="载人数" show-overflow-tooltip  width="75">
          <template slot-scope="scope">
            {{ setLabel("realTimeTrafficFlow", scope.row.iotPropertyList) }}
          </template>
        </el-table-column>
        <el-table-column label="上下行" show-overflow-tooltip width="75">
          <template slot-scope="scope">
            {{ setLabel("operationStatus", scope.row.iotPropertyList) }}
          </template>
        </el-table-column>
        <el-table-column label="开门次数" show-overflow-tooltip width="80">
          <template slot-scope="scope">
            {{ setLabel("doorOpenedCnt", scope.row.iotPropertyList) }}
          </template>
        </el-table-column>
      </el-table>
      <MonitorDeviceListDialog
        v-if="isDeviceList"
        :isDialog="isDeviceList"
        :roomData="roomData"
        @close="closeDialog"
      />
    </div>
  </ModuleCard>
</template>
<script>
import { GetMonitoringItemsList, GetDepartmentAndChairListWebsocket } from '@/utils/spaceManage'
import dayjs from 'dayjs'
import 'dayjs/locale/zh-cn'

dayjs.locale('zh-cn')
export default {
  components: {
    MonitorDeviceListDialog: () =>
      import('@/views/spaceManage/components/monitorDeviceListDialog.vue')
  },
  props: {
    projectCode: {
      type: String,
      default: ''
    },
    roomData: {
      type: Object,
      default: () => {}
    }
  },

  data() {
    return {
      list: [],
      allAlarmVisible: false,
      paramsData: {},
      isDeviceList: false,
      // websocket相关
      webSocket: null, // WebSocket连接对象
      webSocketConnected: false, // WebSocket连接状态
      reconnectAttempts: 0, // 重连尝试次数
      maxReconnectAttempts: 3, // 最大重连次数
      webSocketObj: {},
      reconnectTimer: null, // 重连定时器
      createWebSocketTimer: null // 创建WebSocket定时器
    }
  },
  watch: {},
  mounted () {
    this.createWebSocket()
    this.getElevatorList()
  },
  methods: {
    // 创建WebSocket连接
    createWebSocket () {
      // 关闭之前的连接
      this.closeWebSocket()

      try {
        // 创建新的WebSocket连接
        const socket = GetDepartmentAndChairListWebsocket(this.$store.state.loginInfo.user.staffId)
        socket.onopen = () => {
          console.log('WebSocket连接已建立')
          this.webSocketConnected = true
          this.reconnectAttempts = 0 // 重置重连次数
        }
        socket.onmessage = this.onMessage
        socket.onerror = (error) => {
          console.error('WebSocket连接错误:', error)
          this.webSocketConnected = false
          // 连接错误时尝试重连
          this.reconnectWebSocket()
        }
        socket.onclose = () => {
          console.log('WebSocket连接已关闭')
          this.webSocketConnected = false
          // 如果不是主动关闭的连接，尝试重连
          if (this.webSocket) {
            this.reconnectWebSocket()
          }
        }
        this.webSocket = socket
      } catch (error) {
        console.error('创建WebSocket连接失败:', error)
        this.webSocketConnected = false
        // 创建连接失败时尝试重连
        // 清除之前的定时器
        if (this.createWebSocketTimer) {
          clearTimeout(this.createWebSocketTimer)
        }
        this.createWebSocketTimer = setTimeout(() => {
          this.reconnectWebSocket()
        }, 2000) // 延迟2秒后重连
      }
    },
    // 重新连接WebSocket
    reconnectWebSocket() {
      if (this.reconnectAttempts < this.maxReconnectAttempts) {
        console.log(`尝试重新连接WebSocket，第${this.reconnectAttempts + 1}次重连`)
        this.reconnectAttempts++
        // 清除之前的定时器
        if (this.reconnectTimer) {
          clearTimeout(this.reconnectTimer)
        }
        this.reconnectTimer = setTimeout(() => {
          this.createWebSocket()
        }, 2000) // 重连间隔
        return true
      } else {
        console.error(`WebSocket连接失败，已达到最大重连次数(${this.maxReconnectAttempts})`)
        return false
      }
    },
    // 接收WebSocket消息
    onMessage (event) {
      console.log(event)
      try {
        const data = JSON.parse(event.data)
        console.log(data, '=======')
        // 判断是不是椅位监测
        if (data?.msgTxt?.sysOfCode === 'DTXT') {
          console.log('收到WebSocket消息:', data)

          // 更新统计数据
          this.handleSocketData(data.msgTxt)
        }
      } catch (error) {
        console.error('解析WebSocket消息出错:', error)
      }
    },
    // 关闭WebSocket连接
    closeWebSocket() {
      // 清除所有定时器
      if (this.reconnectTimer) {
        clearTimeout(this.reconnectTimer)
        this.reconnectTimer = null
      }
      if (this.createWebSocketTimer) {
        clearTimeout(this.createWebSocketTimer)
        this.createWebSocketTimer = null
      }
      // 清除WebSocket连接
      if (this.webSocket) {
        this.webSocket.close()
        this.webSocket = null
      }
    },
    setLabel(key, arr) {
      let obj = arr.find((el) => el.metadataTag === key)
      return obj ? obj.valueText : '-'
    },
    handleSocketData (data) {
      data.forEach(item => {
        const targetIndex = this.list.findIndex(el => el.id === item.id)
        if (targetIndex !== -1) {
          // 使用 Vue 的 $set 方法更新整个 iotPropertyList
          this.$set(this.list[targetIndex], 'iotPropertyList', item.propertyList || [])
          console.log(`更新了设备 ${item.id} 的属性列表`)
        } else {
          console.warn(`未找到ID为 ${item.id} 的设备`)
        }
      })
      this.$forceUpdate()
    },
    // 获取电梯报警数据
    getElevatorList() {
      const params = {
        page: 1,
        pageSize: 999,
        sysOfCode: 'DTXT'
      }
      GetMonitoringItemsList(params).then((res) => {
        if (res.data.code === '200') {
          this.list = res.data.data.records
        }
      })
    },
    showDeviceList () {
      try {
        window.chrome.webview.hostObjects.sync.bridge.showDialog(true)
      } catch (error) {}
      this.isDeviceList = true
    },
    closeDialog() {
      this.isDeviceList = false
      try {
        window.chrome.webview.hostObjects.sync.bridge.showDialog(false)
      } catch (error) {}
    },
    rowClick (row) {
      try {
        window.chrome.webview.hostObjects.sync.bridge.ObtainElevatorCode(row.id)
      } catch (error) {}
    }

  }
}
</script>
<style lang="scss" scoped>
.middle-right {
  line-height: 50%;
}
:deep(.el-table__body-wrapper) {
  height: calc(100% - 48px);
}
:deep(.el-table--striped) .el-table__body tr.el-table__row--striped td {
  background-color: rgba(168, 172, 171, 0.08) !important;
  /* def2ff f2faff */
}
</style>
