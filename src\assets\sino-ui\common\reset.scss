@import './var.scss';

html,
body,
div,
span,
applet,
object,
iframe,
h1,
h2,
h3,
h4,
h5,
h6,
p,
blockquote,
pre,
a,
abbr,
acronym,
address,
big,
cite,
code,
del,
dfn,
em,
img,
ins,
kbd,
q,
s,
samp,
small,
strike,
strong,
sub,
sup,
tt,
var,
b,
u,
i,
center,
dl,
dt,
dd,
ol,
ul,
li,
fieldset,
form,
label,
legend,
table,
caption,
tbody,
tfoot,
thead,
tr,
th,
td,
article,
aside,
canvas,
details,
embed,
figure,
figcaption,
footer,
header,
hgroup,
menu,
nav,
output,
ruby,
section,
summary,
time,
mark,
audio,
video {
  margin: 0;
  padding: 0;
  border: 0;
  font-size: 100%;
  font: inherit;
  vertical-align: baseline;
  font-family: HarmonyOS Sans SC-Regular, HarmonyOS Sans SC;
}

/* HTML5 display-role reset for older browsers */
article,
aside,
details,
figcaption,
figure,
footer,
header,
hgroup,
menu,
nav,
section {
  display: block;
}

body {
  line-height: 1;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

ol,
ul {
  list-style: none;
}

blockquote,
q {
  quotes: none;
}

blockquote:before,
blockquote:after,
q:before,
q:after {
  content: '';
  content: none;
}

table {
  border-collapse: collapse;
  border-spacing: 0;
}

@font-face {
  font-family: PingFangSC-Medium;
  src: url(~@/assets/font/PingFangMedium.ttf) format('truetype');
}

@font-face {
  font-family: TRENDS;
  src: url(~@/assets/font/TRENDS.ttf) format('truetype');
}

@font-face {
  font-family: DIN;
  src: url(~@/assets/font/DIN-Regular-2.otf) format('truetype');
}

@font-face {
  font-family: HarmonyOS Sans SC-Bold;
  src: url(~@/assets/font/HarmonyOS_Sans_SC_Bold.ttf) format('truetype');
}

@font-face {
  font-family: HarmonyOS_Sans_SC_Light;
  src: url(~@/assets/font/HarmonyOS_Sans_SC_Light.ttf) format('truetype');
}

@font-face {
  font-family: HarmonyOS_Sans_SC_Medium;
  src: url(~@/assets/font/HarmonyOS_Sans_SC_Medium.ttf) format('truetype');
}

@font-face {
  font-family: HarmonyOS Sans SC-Regular;
  src: url(~@/assets/font/HarmonyOS_Sans_SC_Regular.ttf) format('truetype');
}

@font-face {
  font-family: HarmonyOS_Sans_SC_Black;
  src: url(~@/assets/font/HarmonyOS_Sans_SC_Black.ttf) format('truetype');
}

@font-face {
  font-family: HarmonyOS_Sans_SC_Thin;
  src: url(~@/assets/font/HarmonyOS_Sans_SC_Thin.ttf) format('truetype');
}

@keyframes jump {
  to {
    transform: translateY(-0.3rem);
  }
}

button.el-button {
  padding: 2px 10px;
  min-width: 80px;
  height: 40px;
  background: $btnBgColor;
  border: $btnColor;
  color: $btnColor;
  font-size: 14px;
  font-family: PingFangSC-Medium;
  border-radius: 0;
  /* font-weight: 600; */
}

button.el-button:hover {
  color: $activeColor;
  border-color: $btnColor;
  background-color: $btnBgColor;
}

button.el-button:focus {
  border: $btnColor;
  background: $btnBgColor url('~@/assets/images/qhdsys/btn-bg-hover.png') no-repeat;
  background-size: 100% 100%;
  color: $btnColor;
}

button.el-button .iconfont {
  font-size: 14px;
}

// 主要按钮
.elevatorClass.el-button--primary,
.elevatorClass.el-button--default {
  color: #fff;
  border: none;
  background: url('~@/assets/images/common/button-primary.png') no-repeat;
  background-size: 100% 100%;
  font-weight: 400;
  font-size: 14px;
  padding: 8px 22px;
  font-family: PingFangSC-Regular;

  &:hover,
  &:focus {
    color: #7CD0FF;
    font-family: PingFangSC-Regular;
    background: url('~@/assets/images/common/button-primary-hover.png') no-repeat;
    background-size: 100% 100%;
    font-weight: 500;
  }

  &.is-disabled {
    background: url('~@/assets/images/common/button-primary-hover.png') no-repeat;
    background-size: 100% 100%;
    font-weight: 500;

    &:hover {
      background: url('~@/assets/images/common/button-primary-hover.png') no-repeat;
      background-size: 100% 100%;
      font-weight: 500;
    }
  }
}

// 副要按钮
.elevatorClass.el-button--primary.is-plain,
.elevatorClass.el-button--default.is-plain {
  color: #FFFFFF;
  background: url('~@/assets/images/common/button-primary-plain.png') no-repeat;
  background-size: 100% 100%;
  font-size: 14px;
  font-family: PingFangSC-Regular;
  padding: 8px 22px;

  &:hover,
  &:focus {
    color: #FFFFFF;
    background: url('~@/assets/images/common/button-primary-plain-hover.png') no-repeat;
    background-size: 100% 100%;
    font-family: PingFangSC-Regular-Blod, PingFang SC;
  }
}

.new-edition.el-button {
  background-image: url('@/assets/images/btn.png');
  background-repeat: no-repeat;
  background-size: 100% 100%;
  height: 35px;
}

div.el-form-item__content .el-select {
  width: 100%;
}

div.el-select .el-input.is-focus input.el-input__inner,
div.el-select:hover .el-input__inner,
div.el-cascader:not(.is-disabled):hover .el-input__inner,
.el-cascader .el-input .el-input__inner:focus {
  border-color: $activeColor;
}

input.el-input__inner,
textarea.el-textarea__inner {
  background: center;
  border: 1px solid $input-border-color;
  border-radius: 0;
  color: $color;
  height: inherit;
  font-family: PingFangSC-Medium, PingFang SC;
}

// .el-select:hover,
input.el-input__inner:hover,
textarea.el-textarea__inner:hover {
  border: 1px solid $activeColor;
}

input.el-input__inner:focus,
textarea.el-textarea__inner:focus {
  border: 1px solid $activeColor;
}

.el-textarea span.el-input__count {
  background: center;
}

.el-input .el-input-group__append {
  background: $bcgColor;
  border-color: #325ea3;
  color: #fff;
}

.confirm-box-class.el-message-box {
  background: $bcgColor;
  border: 1px solid #325ea3;

  .el-message-box__title,
  .el-message-box__content {
    color: $color;
  }
}

div.el-message--warning {
  background-color: #53516B;
  border-color: #EBD2A0;
  color: #CEBA95;
}

div.el-message--error {
  background-color: #55335F;
  border-color: #EA777C;
  color: #D66E78;
}

div.el-message--success {
  background-color: #2E5B72;
  border-color: #71ECB8;
  color: #69D7AE;
}

button.sino-button-sure,
button.el-picker-panel__link-btn {
  height: 40px;
  color: $btnColor;
  background: $btnBgColor url('~@/assets/images/qhdsys/btn-bg.png') no-repeat;
  background-size: 100% 100%;
  position: relative;
  font-size: 14px;
  border-radius: 0;

  div {
    display: inline-block;
    width: 14px;
    height: 14px;
    margin-right: 5px;

    vertical-align: top;
  }

  .img-add-icon {
    background: url('~@/assets/images/sys/btn-add.png') no-repeat;
    background-size: 14px 14px;
  }

  .img-export-icon {
    background: url('~@/assets/images/sys/btn-export.png') no-repeat;
    background-size: 14px 14px;
  }
}

li.el-upload-list__item:hover {
  background: center;
}

a.el-upload-list__item-name {
  color: #A7ACC0;
}

button.sino-button-sure:hover {
  // border: 1px solid $btnColor;
  background: $btnBgColor url('~@/assets/images/qhdsys/btn-bg-hover.png') no-repeat;
  background-size: 100% 100%;
  color: $btnColor;

  .img-add-icon {
    background: url('~@/assets/images/sys/btn-add-hover.png') no-repeat;
    background-size: 14px 14px;
  }

  .img-export-icon {
    background: url('~@/assets/images/sys/btn-export-hover.png') no-repeat;
    background-size: 14px 14px;
  }
}

button.sino-button-sure.is-disabled {
  color: $btnColor;
  background: $btnBgColor url('~@/assets/images/sys/btn-bg.png') no-repeat;
  background-size: 100% 100%;

  &:hover {
    background: $btnBgColor url('~@/assets/images/qhdsys/btn-bg-hover.png') no-repeat;
    background-size: 100% 100%;
    color: $btnColor;
  }
}

button.el-picker-panel__link-btn.is-disabled {
  background: $btnBgColor url('~@/assets/images/sys/btn-bg.png') no-repeat !important;
  background-size: 100% 100% !important;
  color: $btnColor !important;
}

button.el-picker-panel__link-btn:hover {
  background: $btnBgColor url('~@/assets/images/qhdsys/btn-bg-hover.png') no-repeat !important;
  background-size: 100% 100% !important;
  color: $activeColor !important;
}

button.sino-button-sure:focus,
button.el-picker-panel__link-btn:focus {
  .img-add-icon {
    background: url('~@/assets/images/sys/btn-add-hover.png') no-repeat;
    background-size: 14px 14px;
  }

  .img-export-icon {
    background: url('~@/assets/images/sys/btn-export-hover.png') no-repeat;
    background-size: 14px 14px;
  }
}

.el-radio__input.is-checked span.el-radio__inner {
  background: url('~@/assets/images/sys/radio-select.png') no-repeat !important;
  border: 0 !important;
  background-size: 100% 100%;
}

.el-checkbox__input.is-checked span.el-checkbox__inner {
  //   background: url('~@/assets/images/sys/checkbox-select.png') no-repeat !important;
  background: url('@/assets/images/qhdsys/bg-checkall.png') no-repeat !important;
  border: 0 !important;
}

span.el-radio__label,
span.el-checkbox__label {
  color: #A3A9C0;
}

span.el-radio__inner::after,
span.el-checkbox__inner::after {
  // background: center;
  display: none;
}

span.el-radio__inner,
span.el-checkbox__inner,
span.el-checkbox__input.is-disabled .el-checkbox__inner,
div.el-input.is-disabled .el-input__inner,
div.el-textarea.is-disabled .el-textarea__inner {
  border: 1px solid #2e529e;
  background: center;
}

div.el-table {
  // border: 1px solid rgba(89, 150, 249, 0.6) !important;
  color: #a8acbd;
  font-size: 0.875rem;
  font-family: PingFang-SC-Medium, PingFang-SC;
}

div.el-table::before {
  height: 0;
}

div.el-table,
td.el-table__expanded-cell {
  background: center;
}

table.el-table__header thead th {
  color: $btnColor;
  background: rgba(133, 145, 206, 0.22) !important;
  color: #8BDDF5;
  font-weight: bold;
  // border-bottom: 1px solid rgba(255,255,255,0.5);
}

div.el-table__fixed {
  height: calc(100% + 1px) !important;
}

div.el-table__fixed-body-wrapper,
div.el-table__body-wrapper {
  background: #101f3c;
}

div.el-table tr {
  background-color: transparent;
}

div.el-table td.el-table__cell,
div.el-table th.el-table__cell.is-leaf {
  border-bottom: 1px solid transparent;
}

div.el-table .el-table__body-wrapper td.el-table__cell .el-tooltip>div {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

table.el-table__body {

  // border-collapse:collapse;
  tr.hover-row,
  tr.current-row,
  tr:hover {
    box-sizing: border-box;

    // border: 1px solid #FFE3A6!important;
    // border-bottom: 0;
    td.el-table__cell {
      background-color: rgba(255, 202, 100, 0.12) !important;
      color: #FFCA64 !important;
      // border-top: 1px solid #FFE3A6;
      // border-bottom: 1px solid #FFE3A6;
      //   background-color: #343C62 !important;
    }
  }
}

// .el-table__body tr.current-row>td.el-table__cell {
// }
.pagination.el-pagination {
  text-align: right;
  margin: 10px;
}

.pagination.el-pagination .el-pager li.active {
  color: $activeColor;
}

span.el-pagination__total,
span.el-pagination__sizes .el-input__inner,
span.el-pagination__jump,
span.el-pagination__jump .el-input__inner {
  color: $color;
}

.pagination.el-pagination button:disabled,
ul.el-pager li,
.pagination.el-pagination .btn-prev,
.pagination.el-pagination .btn-next {
  // background-color: $bcgColor;
  background: center;
  color: $color;
}

.el-pager li.btn-quicknext,
.el-pager li.btn-quickprev {
  color: $color;
}

div.el-select-dropdown,
div.el-cascader__dropdown {
  background: #2B2B45;
  border: 1px solid #AD9F93;
  border-radius: 0;
}

li.el-select-dropdown__item,
li.el-cascader-node {
  color: $color;
}

.el-select-dropdown.is-multiple {

  // background: #2B2B45;
  // color: $color;
  // font-weight: 500;
  li.el-select-dropdown__item.selected {
    background: #2B2B45;
    color: $activeColor;
    font-weight: 500;
  }
}

li.el-cascader-node:not(.is-disabled):focus,
li.el-cascader-node:not(.is-disabled):hover {
  background: #2B2B45;
  color: $activeColor;
  font-weight: 500;
}

.el-popper[x-placement^=bottom] .popper__arrow,
.el-popper[x-placement^=top] .popper__arrow {
  display: none;
}

.el-popper[x-placement^=bottom] {
  margin-top: 5px;
}

// .el-dropdown-menu{
//   background-color: #2B2B45 !important;
//   border: 1px solid #9D928D !important;
//   .el-dropdown-menu__item{
//     color: #fff !important;
//   }
//   .el-dropdown-menu__item:focus, .el-dropdown-menu__item:not(.is-disabled):hover{
//     background-color: transparent !important;
//     color: #ffe3a6 !important;
//   }
// }
.el-popper {
  padding: 0;
  padding: 8px 8px 0;
  background: #374b79 !important;
  border: 0;

  .el-dropdown-menu__item {
    min-width: 78px;
    height: 24px;
    line-height: 24px;
    text-align: center;
    padding: 0 6px;
    background: rgba(255, 255, 255, 0.1) linear-gradient(90deg, rgba(10, 132, 255, 0) 0%, rgba(10, 132, 255, 0) 100%);
    margin-bottom: 8px;
    font-size: 14px;
    font-family: HarmonyOS Sans SC-Regular, HarmonyOS Sans SC;
    font-weight: 400;
    color: #b0e3fa !important;
  }

  .isBjxl {
    background: url('@/assets/images/qhdsys/bj-xl.png') no-repeat center;
    background-size: 100% 100%
  }

  .el-dropdown-menu__item:hover {
    color: #b0e3fa !important;
    background: rgba(255, 255, 255, 0.1) linear-gradient(90deg, rgba(10, 132, 255, 0) 0%, rgba(10, 132, 255, 0) 100%) !important;
  }
  .is-disabled {
    color: #9e9e9e !important;
  }
}

li.el-select-dropdown__item.selected,
li.el-cascader-node.is-active {
  color: $activeColor;
}

.el-select-dropdown.is-multiple li.el-select-dropdown__item.selected.hover,
li.el-select-dropdown__item.hover,
li.el-select-dropdown__item:hover {
  background: #2B2B45;
  color: $activeColor;
}

div.el-tree--highlight-current .el-tree-node.is-current>.el-tree-node__content,
div.el-tree-node:focus>.el-tree-node__content {
  background: center;
}

.operationBtn {
  color: $activeColor;

  span {
    cursor: pointer;

    &[disabled] {
      cursor: not-allowed;
      color: #808591;
    }
  }
}

// .timePicker.el-picker-panel {
// }
.kxy-popper-class.el-picker-panel {
  left: 2% !important;
}

// 能耗新样式 全局更改更换popper-class  记：更改能耗也popper-class
.new_timePicker.el-picker-panel {
  // width: auto !important;
  border: 0.0625rem solid #325ea3;
  background: rgba(5, 19, 68, 0.5);

  .el-picker-panel__body-wrapper {}

  table.el-date-table tbody .current span {
    border-radius: 50% !important;
    color: black !important;
  }

  .el-picker-panel__sidebar {
    background: rgba(5, 19, 68, 0.5);
    ;
    border: 0;
    width: 100%;
    position: relative !important;
    display: flex;
    justify-content: center;

    .el-picker-panel__shortcut {
      width: 25%;
      display: flex;
      justify-content: center;
      align-items: center;
      padding: 10px 0;
      color: #DCEAFF;
      font-weight: 500;
    }

    .el-picker-panel__shortcut:hover {
      color: #FFE3A6;
      border-bottom: 2px solid #FFE3A6;
    }
  }

  .el-picker-panel__body {
    margin-left: 0;
  }

  div.el-date-range-picker__header {
    color: #fff;
  }

  table.el-date-table tbody {
    tr:first-child th {
      color: #fff;
    }

    .prev-month .next-month {
      color: #606266;
    }

    .available {
      color: #fff;
    }

    td span {
      border-radius: 0;
    }

    .today span {
      color: #FFE3A6;
    }
  }

  table.is-week-mode {
    .available:hover {
      color: #FFE3A6 !important;
    }
  }

  div.el-date-range-picker__content.is-left,
  div.el-date-range-picker__content.is-right,
  div.el-picker-panel__body {
    // background: $bcgColor;
    background: rgba(5, 19, 68, 0.5);
    ;
    border-top: 1px solid rgba(255, 255, 255, .5);
  }

  .el-date-table td.disabled div {
    background: lightslategray;
  }

  div.el-date-range-picker__content.is-left {
    border-right: 0.0625rem solid #325ea3;
  }

  .el-date-table td.in-range div,
  .el-date-table td.in-range div:hover,
  .el-date-table.is-week-mode .el-date-table__row.current div,
  .el-date-table.is-week-mode .el-date-table__row:hover div {
    background-color: #6B6262;
  }

  .el-date-table td.end-date span,
  .el-date-table td.start-date span,
  .el-date-table td.current:not(.disabled) span {
    background-color: #FEE4A8;
    color: rgba(5, 19, 68, 0.5);
    ;
  }

  .el-date-table td.start-date div,
  .el-date-table td.end-date div,
  .el-month-table td .cell {
    border-radius: 0;
  }

  .el-date-table th,
  .el-date-picker__time-header,
  .el-date-picker__header--bordered {
    border-bottom: none;
  }

  .el-picker-panel__icon-btn,
  .el-date-picker__header-label {
    color: #fff
  }

  .el-date-table th {
    color: #7eaef9;
  }

  .el-month-table td .cell {
    color: #fff;
  }

  .el-month-table td.today .cell,
  .el-month-table td .cell:hover {
    color: #FFE3A6;
  }

  .el-month-table td.current:not(.disabled) .cell {
    background-color: #FEE4A8;
    color: rgba(5, 19, 68, 0.5);
  }

  .el-month-table td.disabled .cell {
    background-color: lightslategray;
    color: #C0C4CC;
  }

  .el-date-table td.end-date:hover span {
    color: rgba(5, 19, 68, 0.5);
    ;
  }

  table.el-date-table tbody {

    .end-date,
    .start-date {
      span {
        color: rgba(5, 19, 68, 0.5) !important;
      }
    }
  }

  .el-picker-panel__footer {
    border-top: none;
    background: rgba(5, 19, 68, 0.5);
  }

  .el-time-panel {
    background: rgba(5, 19, 68, 0.5);
    ;

    .el-time-spinner__item {
      color: #fff;
    }

    .el-time-spinner__item:hover:not(.disabled):not(.active) {
      background: center;
      color: #FFE3A6;
    }

    .active {
      color: #FFE3A6;
    }

    .el-time-panel__footer {
      .cancel {
        color: #FFF;
      }
    }
  }
}

.timePicker.el-picker-panel {
  border: 0.0625rem solid #325ea3;
  background: $bcgColor;

  div.el-date-range-picker__header {
    color: #fff;
  }

  table.el-date-table tbody {
    tr:first-child th {
      color: #fff;
    }

    .prev-month .next-month {
      color: #606266;
    }

    .available {
      color: #fff;
    }

    td span {
      border-radius: 0;
    }

    .today span {
      color: #FFE3A6;
    }
  }

  table.is-week-mode {
    .available:hover {
      color: #FFE3A6 !important;
    }
  }

  div.el-date-range-picker__content.is-left,
  div.el-date-range-picker__content.is-right,
  div.el-picker-panel__body {
    // background: $bcgColor;
    background: #2B2B45;
    border: 1px solid #9D928D;
  }

  .el-date-table td.disabled div {
    background: lightslategray;
  }

  div.el-date-range-picker__content.is-left {
    border-right: 0.0625rem solid #325ea3;
  }

  .el-date-table td.in-range div,
  .el-date-table td.in-range div:hover,
  .el-date-table.is-week-mode .el-date-table__row.current div,
  .el-date-table.is-week-mode .el-date-table__row:hover div {
    background-color: #6B6262;
  }

  .el-date-table td.end-date span,
  .el-date-table td.start-date span,
  .el-date-table td.current:not(.disabled) span {
    background-color: #FEE4A8;
    color: #2B2B45;
  }

  .el-date-table td.start-date div,
  .el-date-table td.end-date div,
  .el-month-table td .cell {
    border-radius: 0;
  }

  .el-date-table th,
  .el-date-picker__time-header,
  .el-date-picker__header--bordered {
    border-bottom: none;
  }

  .el-picker-panel__icon-btn,
  .el-date-picker__header-label {
    color: #fff
  }

  .el-date-table th {
    color: #7eaef9;
  }

  .el-month-table td .cell {
    color: #fff;
  }

  .el-month-table td.today .cell,
  .el-month-table td .cell:hover {
    color: #FFE3A6;
  }

  .el-month-table td.current:not(.disabled) .cell {
    background-color: #FEE4A8;
    color: #2B2B45;
  }

  .el-month-table td.disabled .cell {
    background-color: lightslategray;
    color: #C0C4CC;
  }

  .el-date-table td.end-date:hover span {
    color: #2B2B45;
  }

  table.el-date-table tbody {

    .end-date,
    .start-date {
      span {
        color: #2B2B45 !important;
      }
    }
  }

  .el-picker-panel__footer {
    border-top: none;
    background: $bcgColor;
  }

  .el-time-panel {
    background: #2B2B45;

    .el-time-spinner__item {
      color: #fff;
    }

    .el-time-spinner__item:hover:not(.disabled):not(.active) {
      background: center;
      color: red;
    }

    .active {
      color: #FFE3A6;
    }

    .el-time-panel__footer {
      .cancel {
        color: #FFF;
      }
    }
  }
}

.main {
  .el-dialog {
    width: 30%;
    border: 1px solid #325ea3;
    background: $bcgColor;
    // box-shadow:0 0 0 8px rgba(40, 101, 192, 0.1);
  }

  .el-dialog__header {
    // border-bottom: 1px solid #d8dee7 !important;
    padding: 10px 20px;
    box-sizing: border-box;
    height: 50px;
    display: flex;
    align-items: center;
  }

  .el-dialog__body {
    padding: 30px 13% 10px 5%;
    box-sizing: border-box;
    max-height: 60vh;
    overflow-y: scroll;
    color: #A3A9C0;
  }

  .el-dialog__footer {
    padding-right: 13%;
    box-sizing: border-box;
  }

  .el-dialog__headerbtn {
    transform: translateX(-36px);
    width: 25px;
    height: 25px;
    background-image: url('@/assets/images/close.png');
    background-repeat: no-repeat;
    background-size: 100% 100%;

    .el-dialog__close::before {
      display: none;
    }
  }

  .el-dialog__headerbtn .el-dialog__close {
    color: #4c82df;
    font-size: 16px;
  }

  .el-icon-close:before {
    font-size: 20px;
  }

  .el-form-item__label {
    color: #7eaef9;
  }

  .el-form-item__label {
    font-family: PingFangSC-Medium, PingFang SC;
  }

  // .dialog-title {
  //   color: #fff;
  // }
  .dialog-title {
    font-family: PingFangSC-Medium, PingFang SC;
    display: inline-block;
    width: 100%;
    text-align: center;
    transform: translateY(-6px);
    color: #cbdeed;

    &::before {
      display: none;
    }
  }

  .dialog-title {
    display: inline-block;
    width: 100%;
    text-align: center;
    transform: translateY(-6px);
    color: #cbdeed;

    &::before {
      display: none;
    }
  }

  .dialog-title::before {
    // content: '';
    // display: inline-block;
    // width: 2px;
    // border-radius: 1px;
    // height: 13px;
    // background: #ffe3a6;
    // margin-right: 10px;
  }
}

.dialog-content {
  .el-tree {
    background: center;
    color: $color;
  }

  .el-tree--highlight-current .el-tree-node.is-current>.el-tree-node__content {
    color: $activeColor;
    background: #263057;
  }

  .el-tree-node__content:hover {
    color: $activeColor;
    background: #263057;
  }

  .el-tree-node:focus>.el-tree-node__content {
    background: #263057;
    color: $activeColor;
  }

  .el-tree-node__content {
    height: 34px;
  }
}

.center-center {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #4d5880;
  font-size: 14px;
}

div.el-upload--picture-card {
  background-color: transparent;
}

.table-center-transfer {

  div.el-table__fixed-body-wrapper,
  div.el-table__body-wrapper {
    background: center;
  }

  .el-table__fixed-header-wrapper .el-table__header tr {
    background: center;
  }

  table.el-table__header thead th {
    background: transparent !important;
  }

  table.el-table__header thead tr {
    background-color: transparent;
  }
}

.tooltip {
  padding: 5px 2px;
  background: #2b2b45 !important;
  border: 1px solid #9d928d !important;
  width: auto !important;

  .popper__arrow::after {
    border-right-color: #2b2b45 !important;
  }

  .el-tooltip__popper {
    border-radius: 0;
  }

  .work-order-type>div {
    padding: 8px 10px;
    text-align: center;
    cursor: pointer;
    color: #fff;

    &:hover {
      color: #FFE3A6;
    }
  }
}

.pie_background {
  position: absolute;
  top: 0;
  left: 50%;
  transform: translate(-50%, 0);
  height: 100%;
  aspect-ratio: 1/1;
  margin: auto;
  background: url('~@/assets/images/peace/people-right-top-bg.png') no-repeat;
  background-size: 100% 100%;
}

/*滚动条样式*/
::-webkit-scrollbar {
  width: 8px;
  height: 10px;
}

/* 垂直滚动条的滑动块 */
::-webkit-scrollbar-thumb {
  /*:vertical*/
  border-radius: 5px;
  -webkit-box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.3);
  //   background-color: #6580b8;
  background: rgba(133, 145, 206, 0.50);
}

/*定义滚动条轨道 内阴影*/
::-webkit-scrollbar-track {
  box-shadow: inset 0 0 5px rgba(0, 0, 0, 0);
  //   background-color: #031553;
  background: transparent;
}

//只需要加上这一行
::-webkit-scrollbar-corner {
  background-color: #031553;
}

.fl {
  float: left;
}

.fr {
  float: right;
}

.clear:after {
  content: ".";
  display: block;
  clear: both;
  height: 0;
  overflow: hidden;
  visibility: hidden;
}

.model-dialog {
  border: 1px solid #325ea3;
  background: $bcgColor;
  border-radius: 4px;
  overflow: hidden;

  * {
    box-sizing: border-box;
  }

  .el-dialog__header {
    height: 56px;
    padding: 15px 20px;
    background: $bcgColor;
    // border-bottom: 1px solid #325ea3;
    box-shadow: 0 0 12px 3px rgba(0, 0, 0, 0.1);

    .el-dialog__title,
    .dialog-title {
      font-size: 18px;
      font-weight: 500;
      color: #fff;
    }

    .el-dialog__close {
      color: #4c82df;
      font-weight: 600;
      font-size: 18px;
    }
  }

  .el-dialog__header::before {
    content: '';
    display: inline-block;
    width: 2px;
    border-radius: 1px;
    height: 13px;
    background: #ffe3a6;
    margin-right: 10px;
  }

  .el-dialog__body {
    padding: 13px;
    max-height: calc(75vh - 110px);
    overflow-y: auto;
    display: flex;

    .dialog-content {
      flex: 1;

      .el-row {
        height: 100%;
      }
    }

    .el-form-item__label {
      color: #7eaef9;
    }

    .el-form-item__label {
      font-family: PingFangSC-Medium, PingFang SC;
    }

    .el-radio-button {
      margin-right: 8px;
    }

    .el-radio-button__orig-radio:checked+.el-radio-button__inner {
      background-color: #3562db !important;
    }

    .el-radio-button__inner {
      border-radius: 0;
      background: rgba(17, 45, 138, 0.5);
      border: none;
      box-shadow: none;
      color: #fff;
    }
  }

  .el-dialog__footer {
    padding: 20px;
  }
}

.el-loading-mask {
  background: rgb(13, 27, 54, 0.9) !important;
}

.popover-space-icon-collapse {
  width: fit-content;
  min-width: 0;
  background: #374B79;
  padding: 10px 8px;
}

.abnormal-color {
  color: $abnormal-state-color;
}

.normal-color {
  color: $normal-state-color;
}

.el-date-editor {
  .el-range-input {
    height: 33px;
  }
}