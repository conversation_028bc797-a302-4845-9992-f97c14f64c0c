<template>
  <div v-if="visible" class="container">
    <div class="left-box">
      <div class="container-title">设备显示</div>
      <el-checkbox v-model="checkAll" :indeterminate="isIndeterminate" @change="changeCheckAll"><span style="color: #ffe3a6">全部</span></el-checkbox>
      <div class="checkBoxAll">
        <div class="middle-div">
          <el-checkbox-group v-model="checkList" @change="changeCheckBox">
            <div v-for="(item, index) in pipelineList" :key="index" class="middle-checkbox">
              <el-checkbox :key="item.PipeId" :label="item.PipeId" :value="item.PipeId" class="checkbox-label"></el-checkbox>
              <div class="checkbox-div" :style="{ background: item.BGclolor }"></div>
              <div class="checkbox-title">{{ item.PipeName }}</div>
            </div>
          </el-checkbox-group>
        </div>
      </div>
    </div>
    <div v-if="roomData.tabName == 'Kongtiao'" class="right-box">
      <div class="container-title">管线流向动画</div>
      <div class="middle-div">
        <el-switch
          :value="isSwitc"
          :width="36"
          inactive-text="动画开关"
          active-color="#0A84FF"
          inactive-color="#1A284A"
          @change="changeSwitch"
        />
      </div>
    </div>
  </div>
</template>

<script>
import { refrigeratorData } from '@/assets/common/dict.js'
export default {
  name: 'pipelineSelect',
  components: {},
  props: {
    roomData: {
      type: Object,
      default: () => {}
    },
    visible: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      isSwitc: false,
      checkAll: false, // 全选
      checkList: [], // 选中数组
      isIndeterminate: true,
      pipelineList: []
    }
  },
  watch: {
    roomData: {
      handler(val) {
        this.isSwitc = false
        this.pipelineList = refrigeratorData[__PATH.VUE_APP_HOSPITAL_NODE] || refrigeratorData['szzlyy']
        this.checkList = Array.from(this.pipelineList, ({ PipeId }) => PipeId)
        this.checkAll = true
        this.isIndeterminate = false
        this.setCheckDataToWpf()
      },
      deep: true,
      immediate: true
    }
  },
  mounted() {},
  methods: {
    changeSwitch() {
      this.isSwitc = !this.isSwitc
      this.setWpfFlowDate()

    },
    changeCheckAll(val) {
      this.checkList = val ? Array.from(this.pipelineList, ({ PipeId }) => PipeId) : []
      this.isIndeterminate = false
      this.setCheckDataToWpf()
    },
    changeCheckBox(value) {
      const checkedCount = value.length
      this.checkAll = checkedCount === this.pipelineList.length
      this.isIndeterminate = checkedCount > 0 && checkedCount < this.pipelineList.length
      this.setCheckDataToWpf()
    },
    setCheckDataToWpf() {
      const params = JSON.parse(JSON.stringify(this.pipelineList))
      const sendMessage = params.map((item) => {
        const Visibility = this.checkList.includes(item.PipeId) ? 1 : 0
        return {
          BimCategoryType: item.PipeId,
          Visibility,
          CategoryCode: this.roomData.localtion
        }
      })
      console.log(sendMessage, 'sendMessage')
      try {
        window.chrome.webview.hostObjects.sync.bridge.GetPipelineSelectData(JSON.stringify(sendMessage))
      } catch (error) {}
    },
    setWpfFlowDate() {
      try {
        window.chrome.webview.hostObjects.sync.bridge.PipelineFlowDirection(this.isSwitc)
      } catch (error) {}
    }
  }
}
</script>

<style lang="scss" scoped>
.container {
  width: 100%;
  height: 100%;
  display: flex;
  .container-title {
    padding: 7px 8px;
    background: rgba(133, 145, 206, 0.15);
    margin: 16px 0 6px 0px;
    font-size: 15px;
    font-family: HarmonyOS Sans SC-Medium, HarmonyOS Sans SC;
    font-weight: 500;
    color: #ffffff;
  }
  .left-box {
    flex: 1;
    // min-width: 48%;
    overflow-y: auto;
    box-sizing: border-box;
  }
  .right-box {
    flex: 1;
    // width: 48%;
    margin-left: 16px;
  }
  .middle-div {
    // min-width: 140px;
    .middle-checkbox {
      display: flex;
      align-items: center;
    }
    .checkbox-div {
      width: 12px;
      height: 8px;
      margin: 0 8px;
      flex-shrink: 0;
    }
    .checkbox-title {
      height: 20px;
      font-size: 14px;
      font-family: PingFang SC-Medium, PingFang SC;
      font-weight: 500;
      color: #ffffff;
      line-height: 20px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
    .checkbox-label {
      ::v-deep .el-checkbox__label {
        display: none;
      }
    }
    ::v-deep .el-switch{
      .el-switch__label {
        color: #fff !important;
      }
      .el-switch__core::after {
        width: 18px;
        height: 18px;
        top: 0px;
        background-color: #1E3370 !important;
      }
    }
    ::v-deep .el-switch.is-checked .el-switch__core::after {
      left: calc(100% - 1px) !important;
    }
  }
  .checkBoxAll {
    height: calc(100% - 75px);
    overflow-y: scroll;
  }
}
.middle-div:nth-child(1) {
  margin-top: 2px !important;
}
::v-deep .el-checkbox__input {
  .el-checkbox__inner {
    box-shadow: 0px 0px 3px 0px #78fff8;
    opacity: 1;
    border: 1px solid #52fffc;
    background: transparent;
  }
}
::v-deep .el-checkbox-group {
  flex-wrap: wrap;
  .el-checkbox {
    display: block;
    margin: 3px 0;
    padding: 3px 5px 3px 0px;
    box-sizing: border-box;
    border-radius: 6px;
    .el-checkbox__label {
      font-family: PingFangSC-Medium, PingFang SC;
      font-size: 14px;
      // overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
    }
  }
}
</style>
