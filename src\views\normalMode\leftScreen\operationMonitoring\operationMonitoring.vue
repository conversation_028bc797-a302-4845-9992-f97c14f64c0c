<template>
  <div class="content">
    <div class="eahart-left">
      <div class="echarts-leftTop">
        <div class="bg-title" style="margin-top: 0">
          <span> 污水运行监测 </span>
        </div>
        <div class="bg-content" style="padding: 0">
          <el-carousel ref="sewageCarousel" class="sewageBox-carousel" :interval="10000" indicator-position="outside"
                       arrow="always">
            <el-carousel-item v-for="(item, index) in sewageGroupedArray" :key="index" :loop="false">
              <div class="sewage-group">
                <div v-for="(t, i) in item" :key="i" class="sewage-list-item">
                  <div v-if="t.parameterException == 1" class="error-tag">异常</div>
                  <span class="item-name" :title="t.parameterName || ''">{{ t.parameterName || '' }}</span>
                  <span class="item-value" :title="t.parameterValue || 0">{{ t.parameterValue || 0 }} <span
                    class="item-unit" :title="t.parameterUnit || ''">{{ t.parameterUnit || '' }}</span>
                  </span>
                </div>
              </div>
            </el-carousel-item>
          </el-carousel>
        </div>
      </div>
      <div class="echarts-leftCenter">
        <div class="bg-title" style="margin-top: 0">
          <span>UPS运行监测</span>
          <span class="bg-titleDetail" @click="goUpsDetail('ups')">详情</span>
        </div>
        <div class="bg-content" style="padding: 0.625rem 0 0.9375rem 0">
          <div class="upsBox">
            <div class="upsBox-img">
              <p class="ups-content">
                <span class="ups-title">设备总数</span>
                <span class="ups-value">
                  <span>{{ upsTotal }}</span>
                  <span class="ups-unit">台</span>
                </span>
              </p>
            </div>
            <div class="upsBox-content">
              <progress-bar :stateList="upsStateList" :barHeight="'8px'" :gridGap="'4px'" :isShowBgColor="true" />
            </div>
          </div>
        </div>
      </div>
      <div class="echarts-leftBottom">
        <div class="bg-title" style="margin-top: 0">医用气体运行监测</div>
        <div class="bg-contentNew" style="padding: 0.625rem 0 0.9375rem 0">
          <div class="medicalGasBox">
            <div class="medicalGasBox-left">
              <span class="deviceTotal">设备总数</span>
              <span class="medicalGasBox-value">{{ medicalGasTotal }} <span class="unit">台</span></span>
            </div>
            <div class="medicalGasBox-right">
              <progress-bar :stateList="medicalGasList" :barHeight="'8px'" :gridGap="'4px'" :isShowBgColor="true"
                            :cMax="60" />
            </div>
          </div>

          <div class="medicalGasBox-carousel">
            <el-carousel ref="carousel" class="carousel" :interval="1000000" arrow="always">
              <el-carousel-item v-for="(item, pIndex) in medicalGasGroupedArray" :key="pIndex" :loop="false">
                <div class="gas-group">
                  <div v-for="(val, key, index) in item" :key="key" class="gas-group-item"
                       :class="{ 'right-line': key !== Object.keys(item).length - 1 }"
                       :style="{ width: 'calc(100% /' + 3 + ')' }" @click="viewRealisticScenery(val.modelCode)">
                    <div class="dept-monitor" :style="{ width: index == 0 ? '100%' : '' }"><span></span>{{
                      val.surveyEntityName }}</div>
                    <div v-scrollbarHover class="gas-list">
                      <div v-for="(t, i) in val.parameterList" :key="i" class="gas-list-item">
                        <span class="item-name">{{ t.dictAlias || t.parameterName || '-' }}</span>
                        <span class="item-value">{{ t.parameterValue || 0 }} <span class="item-unit">{{ t.parameterUnit
                          || '' }}</span>
                        </span>
                      </div>
                    </div>
                  </div>
                </div>
              </el-carousel-item>
            </el-carousel>
          </div>
        </div>
      </div>
    </div>
    <div class="eahart-center">
      <div class="echarts-centerTop">
        <div class="bg-title" style="margin-top: 0">智能楼宇监测</div>
        <div class="bg-content" style="padding: 0.625rem 0 0.9375rem 0">
          <div v-for="item in monitorBuildingList" :key="item.menuName" class="content-item">
            <div class="item-main">
              <div class="main-left">
                <div class="subtitle">{{ item.projectName }}</div>
                <div class="main-img" @click="goDetailList('building', item)">
                  <img :src="item.img" />
                </div>
              </div>
              <div class="main-right">
                <div class="right-textTop">
                  <span class="right-title">总数</span>
                  <span>
                    <span class="right-total">{{ item.total }}</span>
                    <span class="right-unit">台</span>
                  </span>
                </div>
                <div class="right-textBootom">
                  <div class="right-text">
                    <p class="right-title">正常</p>
                    <p>
                      <span class="right-value">{{ item.running }}</span>
                      <span class="right-unit">台</span>
                    </p>
                  </div>
                  <div class="right-text">
                    <p class="right-title">离线</p>
                    <p>
                      <span class="right-value">{{ item.offline }}</span>
                      <span class="right-unit">台</span>
                    </p>
                  </div>
                  <div class="right-text">
                    <p class="right-title">报警</p>
                    <p>
                      <span class="right-value right-value-red">{{ item.alarm }}</span>
                      <span class="right-unit" style="color: #ff5454">台</span>
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="echarts-centerBottom">
        <div class="bg-title" style="margin-top: 0">
          <span>配电运行监测</span>
          <span class="bg-titleDetail" @click="goDetailList('distribution', '')">详情</span>
        </div>
        <div class="bg-content" style="padding: 0.625rem 0 0.9375rem 0; overflow-y: auto">
          <div v-for="item in monitorDistributionList" :key="item.menuName" class="content-item">
            <div class="item-main" @click="goDetailList('distribution', item)">
              <div class="main-left">
                <div class="subtitle">{{ item.projectName }}</div>
                <div class="main-img">
                  <img :src="item.img" />
                </div>
              </div>
              <div class="main-right">
                <div class="right-textTop">
                  <span class="right-title">总数</span>
                  <span>
                    <span class="right-total">{{ item.total || '0' }}</span>
                    <span class="right-unit">部</span>
                  </span>
                </div>
                <div class="right-textBootom">
                  <div class="right-text">
                    <p class="right-title">正常</p>
                    <p>
                      <span class="right-value">{{ item.running || '0' }}</span>
                      <span class="right-unit">台</span>
                    </p>
                  </div>
                  <div class="right-text">
                    <p class="right-title">离线</p>
                    <p>
                      <span class="right-value">{{ item.offline || '0' }}</span>
                      <span class="right-unit">台</span>
                    </p>
                  </div>
                  <div class="right-text">
                    <p class="right-title">报警</p>
                    <p>
                      <span class="right-value right-value-red">{{ item.alarm || '0' }}</span>
                      <span class="right-unit" style="color: #ff5454">台</span>
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="eahart-right">
      <div class="echarts-rightTop">
        <div class="bg-title" style="margin-top: 0">实时报警分析</div>
        <div class="bg-content" style="padding: 0.625rem 0 0.9375rem 0">
          <div class="realTimeBox">
            <div class="realTimeBox-img"><img :src="require('@/assets/images/qhdsys/monitor-realTime.png')" /></div>
            <div class="realTimeBox-content">
              <div v-for="item in warnAnalysis" :key="item.name" class="right-item"
                   @click="warnListRouter({ dataType: item.timeType })">
                <p class="right-title">{{ item.name }}</p>
                <div class="right-value">
                  <p class="right-num">{{ item.count || 0 }}</p>
                  <p v-if="item.timeType != 3" class="right-ratio">
                    <span style="color: #fff; margin-right: 3px">环比</span>
                    <img v-if="item.ringRatioType == 1" src="@/assets/images/qhdsys/down.png" />
                    <img v-else src="@/assets/images/qhdsys/up.png" />
                    <span :style="{ color: item.ringRatioType == 1 ? '#00F872' : '#FF5454', fontStyle: 'oblique' }">{{
                      item.ringRatio }}</span>
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="echarts-rightCenter">
        <div class="bg-title" style="margin-top: 0">报警占比分析</div>
        <div class="bg-content" style="padding: 0.625rem 0 0.9375rem 0">
          <div id="alarmProportionEchart" style="height: 100%; width: 100%"></div>
        </div>
      </div>
      <div class="echarts-rightBottom">
        <div class="bg-title" style="margin-top: 0">
          <span>报警详情记录</span>
          <span class="bg-titleDetail" @click="warnListRouter">详情</span>
        </div>
        <div class="bg-content" style="padding: 0.625rem 0 0.9375rem 0">
          <el-table v-loading="tableLoading" stripe :data="tableData" height="calc(100%)"
                    :cell-style="$tools.setCell(3)" :header-cell-style="$tools.setHeaderCell(3)" style="width: 100%"
                    element-loading-background="rgba(0, 0, 0, 0.2)">
            <el-table-column prop="alarmLevel" label="报警等级" width="95px">
              <span slot-scope="scope" :style="{ color: alarmLevelFormatter(scope.row).color }">
                {{ alarmLevelFormatter(scope.row).label }}
              </span>
            </el-table-column>
            <el-table-column prop="alarmSource" show-overflow-tooltip label="报警系统" width="95px"></el-table-column>
            <el-table-column prop="alarmType" show-overflow-tooltip label="报警类型"></el-table-column>
            <el-table-column prop="alarmStartTime" label="报警时间" width="175px"></el-table-column>
            <el-table-column prop="devicePosition" label="状态" width="100px">
              <template #default="{ row }">
                <span :style="{ color: alarmStatusFormatter(row).color }">
                  <i :class="alarmStatusFormatter(row).icon"></i>
                  {{ alarmStatusFormatter(row).label }}
                </span>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>
    </div>
    <template v-if="searchTableShow">
      <searchComponentList ref="searchComponentList" :dialogData="tableCompenentData" :dialogShow="searchTableShow"
                           @configCloseDialog="configCloseTableDialog"></searchComponentList>
    </template>

    <DialogEmergencyFrame :visible.sync="dialogEmergencyFrameShow" @update:visible="getRightBottom" />
  </div>
</template>
<script>
import { monitorTypeList } from '@/assets/common/dict.js'
import { GetAllAlarmRecord, GetPoliceInfoByApp } from '@/utils/peaceLeftScreenApi'
import { getAllSysData, getAllRealData, getSewageRealList, getMonitoringAlarmRatio, getMonitoringAlarmRecordList } from '@/utils/spaceManage'
import * as echarts from 'echarts'
// import $ from 'jquery'
import progressBar from '../components/progressBar.vue'
import searchComponentList from './components/seaechTable.vue'
import icon_5 from '@/assets/images/icon-5.png'
import icon_3 from '@/assets/images/icon-3.png'
import icon_7 from '@/assets/images/icon-7.png'
import icon_6 from '@/assets/images/icon-6.png'
import icon_2 from '@/assets/images/icon-2.png'
import { alarmLevelConfig, alarmStatusConfig } from '../EmergencyDisposalNew/emergency-constant'
export default {
  components: {
    progressBar,
    searchComponentList,
    DialogEmergencyFrame: () => import('../EmergencyDisposalNew/components/DialogEmergencyFrame')
  },
  data() {
    return {
      searchTableShow: false,
      tableCompenentData: {},
      icon_5,
      icon_3,
      icon_7,
      icon_6,
      icon_2,
      upsStateList: [], // UPS统计
      upsTotal: '', // ups运行监测总数
      medicalGasList: [], // 医用气体统计
      groupedArray: [],
      medicalGasTotal: '', // 医用气体设备总数
      medicalGasListGroup: [],
      medicalGasGroupedArray: [],
      deptStateList: [],
      monitorBuildingList: [], // 智能楼宇list
      monitorDistributionList: [], // 配电监测list
      sewageGroupedArray: [], // 污水监测
      alarmAnalysisTotal: '', // 报警分析总数
      tableData: [],
      warnAnalysis: [],
      monitoringListGroup: [],
      tableLoading: false,
      sewageInfo: {}, // 污水监测info
      timer: null,
      dialogEmergencyFrameShow: false // 展示全部警情
    }
  },
  mounted() {
    // this.groupedArray = this.group(this.groupedArray, 3)
    // this.grouped2Array = this.group(this.grouped2Array, 2)
    this.search()
    this.timer = setInterval(() => {
      setTimeout(() => {
        this.search()
      }, 0)
    }, 1000 * timerNum)
    this.$once('hook:beforeDestroy', () => {
      clearInterval(this.timer)
      this.timer = null
    })
  },
  methods: {
    alarmStatusFormatter(row) {
      return alarmStatusConfig.find(x => x.value == row.alarmStatus)
    },
    alarmLevelFormatter(row) {
      return alarmLevelConfig.find(x => x.value == row.alarmLevel)
    },
    // 查看实景
    viewRealisticScenery(modelCode) {
      // 如果关联了设备即跳转设备详情页
      if (modelCode) {
        const projectData = monitorTypeList.find(e => e.projectName === '医用气体')
        const params = {
          DeviceCode: modelCode,
          menuName: projectData.wpfKey,
          projectCode: projectData.projectCode
        }
        try {
          window.chrome.webview.hostObjects.sync.bridge.EquipmentRealView(JSON.stringify(params))
        } catch (error) { }
      } else {
        this.$message.warning('当前设备暂未录入模型编码!')
      }
    },
    search() {
      this.getLeftTop()
      this.getLeftCenter()
      this.getLeftBottom()
      this.getCenterTop()
      this.getCenterBottom()
      this.getRightTop()
      this.getRightCenter()
      this.getRightBottom()
    },
    // 获取污水监测数据
    getLeftTop() {
      const params = {
        projectCode: monitorTypeList.find(e => e.projectName === '污水监测').projectCode
      }
      getSewageRealList(params).then((res) => {
        const resData = res.data
        if (resData.code == '200' && resData.data) {
          this.sewageGroupedArray = this.group(resData.data, 9)
        }
      })
    },
    // 获取ups监测数据
    getLeftCenter() {
      const params = {
        projectCode: monitorTypeList.find(e => e.projectName === 'UPS监测').projectCode
      }
      getAllSysData(params).then((res) => {
        const data = res.data
        if (data.code === '200' && data.data) {
          this.upsStateList = [
            { value: data.data[0].running, name: '正常', lineColor: 'rgba(97,226,157,0.4)', color: '#61E29D', borderColor: '#61E29D' },
            { value: data.data[0].alarm, name: '报警', lineColor: 'rgba(255,45,85,0.4)', color: '#FF2D55', borderColor: '#FF2D55' },
            { value: data.data[0].offline, name: '离线', lineColor: 'rgba(214,239,241,0.4)', color: '#D6EFF1 ', borderColor: '#D6EFF1 ' }
          ]
          this.upsTotal = data.data[0].total
        }
      })
    },
    // 获取医用气体运行监测
    getLeftBottom() {
      const params = {
        projectCode: monitorTypeList.find(e => e.projectName === '医用气体').projectCode,
        page: 1,
        pageSize: 999
      }
      getAllRealData(params).then((res) => {
        const data = res.data
        if (data.code === '200' && data.data.list) {
          this.medicalGasGroupedArray = this.group(data.data.list, 3)
        }
      })
      getAllSysData(params).then((res) => {
        const data = res.data
        if (data.code === '200' && data.data) {
          this.medicalGasList = [
            { value: data.data[0].running, name: '正常', lineColor: 'rgba(97,226,157,0.4)', color: '#61E29D', borderColor: '#61E29D' },
            { value: data.data[0].alarm, name: '报警', lineColor: 'rgba(255,45,85,0.4)', color: '#FF2D55', borderColor: '#FF2D55' },
            { value: data.data[0].offline, name: '离线', lineColor: 'rgba(214,239,241,0.4)', color: '#D6EFF1', borderColor: '#D6EFF1' }
          ]
          this.medicalGasTotal = data.data[0].total
        }
      })
    },
    // 获取智能楼宇数据
    getCenterTop() {
      const filterName = ['空调监测', '给排水监测', '冷热源监测', '风冷热泵监测', '照明监测']
      const filterMonitorList = monitorTypeList.filter(e => filterName.includes(e.projectName))
      const params = {
        projectCode: Array.from(filterMonitorList, ({ projectCode }) => projectCode).toString()
      }
      const newArr = [
        { name: '空调', img: require('@/assets/images/qhdsys/monitor-airConditioner.png') },
        { name: '排风', img: require('@/assets/images/qhdsys/monitor-exhaustAir.png') },
        { name: '排水', img: require('@/assets/images/qhdsys/monitor-waterSupply.png') },
        { name: '冷热', img: require('@/assets/images/qhdsys/monitor-heatPump.png') },
        { name: '冷源', img: require('@/assets/images/qhdsys/monitor-coldSource.png') },
        { name: '照明', img: require('@/assets/images/qhdsys/monitor-illumination.png') }
      ]
      getAllSysData(params).then((res) => {
        const data = res.data
        if (data.code === '200') {
          this.monitorBuildingList = data.data
          this.monitorBuildingList.forEach((e) => {
            newArr.forEach((v) => {
              if (e.projectName.indexOf(v.name) !== -1) {
                e.img = v.img
              }
            })
          })
        }
      })
    },
    // 获取配电运行监测
    getCenterBottom() {
      // const filterName = ['配电监测', '电表监测']
      const filterName = ['配电监测']
      const filterMonitorList = monitorTypeList.filter(e => filterName.includes(e.projectName))
      const params = {
        projectCode: Array.from(filterMonitorList, ({ projectCode }) => projectCode).toString()
      }
      getAllSysData(params).then((res) => {
        const data = res.data
        const newArr = [
          { name: '高压', img: require('@/assets/images/qhdsys/monitor-highTension.png') },
          { name: '低压', img: require('@/assets/images/qhdsys/monitor-highTension.png') },
          { name: '电表', img: require('@/assets/images/qhdsys/monitor-transformer.png') },
          { name: '直流屏', img: require('@/assets/images/qhdsys/monitor-dcPanel.png') },
          { name: '变压器', img: require('@/assets/images/qhdsys/monitor-dcPanel.png') }
        ]
        if (data.code === '200') {
          this.monitorDistributionList = data.data
          // this.monitorDistributionList = this.monitorDistributionList.slice(0, 4)
          this.monitorDistributionList.forEach((e) => {
            e.img = require('@/assets/images/qhdsys/monitor-highTension.png')
            newArr.forEach((v) => {
              console.log(e.projectName, v.name, e.projectName.includes(v.name))
              if (e.projectName.includes(v.name)) {
                e.img = v.img
              }
            })
          })
        }
      })
    },
    // 获取实时报警分析
    getRightTop() {
      const newArr = ['本日', '本周', '本月', '本年']
      GetPoliceInfoByApp().then((res) => {
        const data = res.data
        if (data.code === '200') {
          this.warnAnalysis = data.data.countInfo
          this.warnAnalysis.forEach((item) => {
            item.name = newArr[item.timeType]
          })
        } else {
          // this.$message({
          //   message: data.msg,
          //   type: 'warning'
          // })
        }
      })
    },
    // 获取报警占比
    getRightCenter() {
      const params = {
        projectCode: ''
      }
      getMonitoringAlarmRatio(params).then((res) => {
        const data = res.data
        if (data.code === '200' && data.data.policeList) {
          this.alarmAnalysisTotal = data.data.total
          this.getAlarmProportionEchart(data.data.policeList)
        }
      })
    },
    // 获取报警记录列表数据
    getRightBottom() {
      const params = {
        pageNo: 1,
        pageSize: 15,
        entityTypeId: ''
      }
      this.tableLoading = true
      GetAllAlarmRecord(params).then((res) => {
        const data = res.data
        this.tableLoading = false
        if (data.code === '200') {
          this.tableData = data.data.records
        } else {
          // this.$message({
          //   message: data.msg,
          //   type: 'warning'
          // })
        }
      })
    },
    // 关闭弹窗
    configCloseTableDialog() {
      this.searchTableShow = false
    },
    // 轮播图数据分组
    group(array, subGroupLength) {
      let index = 0
      const newArray = []
      while (index < array.length) {
        newArray.push(array.slice(index, (index += subGroupLength)))
      }
      return newArray
    },
    getAlarmProportionEchart(list) {
      const getchart = echarts.init(document.getElementById('alarmProportionEchart'))
      getchart.resize()
      var scaleData = list
      var xdata = list.map((item) => {
        return item.entityTypeName || ''
      })
      var data = []
      var colorList = ['#E88D6B', '#61E29D', '#5E89EE', '#0A84FF', '#F4D982']
      for (var i = 0; i < scaleData.length; i++) {
        data.push({
          value: scaleData[i].count,
          name: scaleData[i].entityTypeName ? scaleData[i].entityTypeName : '',
          rate: scaleData[i].policeRate,
          itemStyle: {
            normal: {
              borderWidth: 2,
              shadowBlur: 200,
              borderColor: '#0A1732',
              color: colorList[i],
              label: {
                normal: { show: true }
              }
            }
          }
        })
      }
      var seriesObj = [
        {
          name: '',
          type: 'pie',
          clockWise: false,
          center: ['25%', '50%'],
          radius: ['65%', '80%'],
          hoverAnimation: true,
          label: {
            normal: {
              show: false,
              position: 'center',
              formatter: '{text|{b}}\n{c} ({d}%)',
              rich: {
                value: {
                  padding: 5,
                  align: 'center',
                  verticalAlign: 'middle',
                  fontSize: 20
                },
                label: {
                  align: 'center',
                  verticalAlign: 'middle',
                  fontSize: 12
                }
              }
            }
          },
          data: data
        },
        {
          type: 'pie',
          radius: ['56%', '60%'],
          center: ['25%', '50%'],
          animation: false,
          hoverAnimation: false,
          data: [
            {
              value: 100
            }
          ],
          label: {
            show: false
          },
          itemStyle: {
            normal: {
              color: 'rgba(133,145,206,0.15)'
            }
          }
        },
        // 外边框
        {
          name: '外边框',
          type: 'pie',
          clockWise: false, // 顺时加载
          hoverAnimation: false, // 鼠标移入变大
          center: ['25%', '50%'],
          radius: ['85%', '84%'],
          label: {
            normal: {
              show: false
            }
          },
          data: [
            {
              value: 0,
              name: '',
              itemStyle: {
                normal: {
                  borderWidth: 1,
                  color: 'rgba(133,145,206,0.15)',
                  borderColor: 'rgba(133,145,206,0.15)'
                }
              }
            }
          ]
        },
        {
          name: '',
          type: 'pie',
          startAngle: 90,
          radius: '50%',
          animation: false,
          hoverAnimation: false,
          center: ['25%', '50%'],
          itemStyle: {
            normal: {
              labelLine: {
                show: false
              },
              color: new echarts.graphic.RadialGradient(0.5, 0.5, 1, [
                {
                  offset: 1,
                  color: 'rgba(133,145,206,0.15)'
                },
                {
                  offset: 0,
                  color: 'rgba(133,145,206,0.15)'
                }
              ]),
              shadowBlur: 60
            }
          },
          data: [
            {
              value: 100
            }
          ]
        }
      ]
      const option = {
        backgroundColor: 'rgba(128, 128, 128, 0)',
        title: {
          show: true,
          text: this.alarmAnalysisTotal,
          subtext: '报警总数',
          x: '24%',
          y: '37%',
          itemGap: 6,
          textStyle: {
            color: '#fff',
            fontSize: 23,
            fontWeight: '400',
            lineHeight: 20
          },
          subtextStyle: {
            color: 'rgba(255,255,255,0.6)',
            fontSize: 16,
            fontWeight: '400',
            lineHeight: 20
          },
          textAlign: 'center'
        },
        legend: {
          orient: 'vertical',
          type: 'scroll',
          x: 'left',
          top: '2%',
          left: '50%',
          bottom: '0%',
          data: xdata,
          itemWidth: 8,
          itemHeight: 8,
          itemGap: 16,
          pageTextStyle: {
            color: '#fff'
          },
          textStyle: {
            //  fontSize: 18,//字体大小
            color: '#B3C2DD' //  字体颜色
          },
          formatter: function (name) {
            var oa = option.series[0].data
            // var num = oa.reduce((sum, e) => sum + e.value, 0)
            for (var i = 0; i < option.series[0].data.length; i++) {
              if (name === oa[i].name) {
                return ' ' + name + ' (' + oa[i].value + '件)     ' + oa[i].rate
                // return ' ' + name + ' (' + oa[i].value + '件)     ' + ((oa[i].value / num) * 100).toFixed(2) + '%'
              }
            }
          }
        },
        series: seriesObj
      }
      getchart.setOption(option)
      // 随着屏幕大小调节图表
      window.addEventListener('resize', () => {
        getchart.resize()
      })
      getchart.on('legendselectchanged', (e) => {
        this.warnListRouter()
      })
    },
    // 列表跳转
    warnListRouter(param = {}) {
      this.dialogEmergencyFrameShow = true
    },
    // ups详情
    goUpsDetail(type) {
      this.searchTableShow = true
      Object.assign(this.tableCompenentData, {
        title: 'ups运行监测',
        type: type,
        height: 'calc(100% - 120px)'
      })
    },
    // 智能楼宇  配电详情list
    goDetailList(type, val) {
      const filterName = ['配电监测', '电表监测']
      const filterMonitorList = monitorTypeList.filter(e => filterName.includes(e.projectName))
      const projectCodes = Array.from(filterMonitorList, ({ projectCode }) => projectCode).toString()
      this.searchTableShow = true
      Object.assign(this.tableCompenentData, {
        title: type == 'distribution' ? '配电运行监测' : '智能楼宇运行监测',
        type: type,
        height: 'calc(100% - 120px)',
        queryInfo: val || { projectCode: projectCodes }
      })
    }
  }
}
</script>
<style lang="scss" scoped>
@import '~@/assets/sino-ui/common/var.scss';

.content {
  position: relative;
  // background-color: #031553;
  // background: url('~@/assets/images/qhdsys/bj.png') no-repeat;
  box-sizing: border-box;
  display: flex;
  width: 100%;
  height: 100%;
  padding: 1.25rem 0.625rem 0.625rem 0.625rem;

  //justify-content: space-between;
  .bg-title {
    // margin-top: 5px;
    height: 2.5rem;
    line-height: 2.5rem;
    font-size: 1rem;
    font-weight: 500;
    color: #dceaff;
    display: flex;
    justify-content: space-between;
    padding-left: 2rem;

    .bg-titleDetail {
      font-size: 0.9rem;
      padding-right: 1rem;
      color: #8bddf5;
      cursor: pointer;

      .bg-titleDetail-value {
        color: #ffffff;
        font-family: HarmonyOS Sans SC-Bold;
      }
    }
  }

  .status-box {
    display: flex;
    align-items: center;
  }

  .table-icon {
    width: 16px;
    margin-right: 3px;
  }

  .subtitle {
    font-size: 1rem;
    width: 80px;
    font-weight: 600;
    color: #ffffff;
    text-align: center;
    margin-bottom: 10px;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
  }

  .eahart-left {
    width: 29.3%;
    height: 100%;
    display: flex;
    flex-direction: column;

    .echarts-leftTop {
      height: calc((100% / 3) + 68px);
      background: url('~@/assets/images/qhdsys/monitor-bg377.png') no-repeat;
      background-size: 100% 100%;
      // padding: 4px;
      box-sizing: border-box;

      .bg-content {
        align-items: center;
        flex-wrap: wrap;
      }

      ::v-deep .sewageBox-carousel {
        height: 100%;
        width: 100%;
        padding: 10px 16px 0 16px;

        .el-carousel__container {
          height: calc(100% - 26px);
        }

        .el-carousel__arrow {
          width: 24px;
          height: 16px;
          border-radius: 0;

          &>i {
            display: none;
          }
        }

        .el-carousel__arrow--left {
          top: 100%;
          left: 35%;
          transform: translateY(25%);
          background: url('~@/assets/images/common/carousel-default-left.png') no-repeat;
          background-size: 100% 100%;

          &:hover {
            background: url('~@/assets/images/common/carousel-active-left.png') no-repeat;
            background-size: 100% 100%;
          }
        }

        .el-carousel__arrow--right {
          top: 100%;
          right: 35%;
          transform: translateY(25%);
          background: url('~@/assets/images/common/carousel-default-right.png') no-repeat;
          background-size: 100% 100%;

          &:hover {
            background: url('~@/assets/images/common/carousel-active-right.png') no-repeat;
            background-size: 100% 100%;
          }
        }

        .el-carousel__indicators {
          .el-carousel__indicator {
            padding: 4px;

            .el-carousel__button {
              width: 5px;
              height: 5px;
              border-radius: 50%;
              background: rgba(133, 145, 206, 0.5);
            }

            &.is-active .el-carousel__button {
              background: #ffca64;
            }
          }
        }

        .sewage-group {
          height: 100%;
          width: 100%;
          // display: flex;
          // align-items: center;
          // justify-content: space-between;
          // flex-wrap: wrap;
          display: grid;
          grid-template-columns: repeat(3, calc(33.3% - 7px));
          grid-template-rows: repeat(3, calc(33.3% - 7px));
          grid-column-gap: 10px;
          grid-row-gap: 10px;

          .sewage-list-item {
            // flex-shrink: 0;
            // width: calc(33.3% - 10px);
            // height: calc(33.3% - 10px);
            background: rgba(133, 145, 206, 0.05);
            border-radius: 8px;
            padding: 20px 16px 10px 16px;
            display: flex;
            flex-direction: column;
            justify-content: space-between;
            position: relative;

            .error-tag {
              position: absolute;
              right: 0;
              top: 0;
              width: 40px;
              height: 20px;
              background: #ff2d55;
              border-radius: 0px 4px 0px 4px;
              text-align: center;
              line-height: 20px;
              font-size: 12px;
              font-family: PingFang SC, PingFang SC;
              font-weight: 500;
              color: #ffffff;
            }

            &>span {
              overflow: hidden;
              white-space: nowrap;
              text-overflow: ellipsis;
            }

            .item-value {
              display: block;
              font-size: 1.1rem;
              font-weight: bold;
              color: #ffffff;
              margin-top: 4px;
              line-height: 20px;

              i {
                font-size: 0.8rem;
              }
            }

            .item-name {
              display: block;
              font-size: 0.9rem;
              margin-top: 2px;
              color: #7eaef9;
            }

            .item-unit {
              font-size: 0.8rem;
            }
          }
        }
      }
    }

    .echarts-leftCenter {
      height: calc((100% / 3) - 96px);
      background: url('~@/assets/images/qhdsys/monitor-bg209.png') no-repeat;
      background-size: 100% 100%;
      // padding: 4px;
      margin-top: 16px;
      box-sizing: border-box;

      .bg-content {
        position: relative;
        box-sizing: border-box;
        padding: 10px 10px 15px 10px;
        width: 100%;
        height: calc(100% - 1.5rem);
        margin: auto;
      }

      .upsBox {
        display: flex;
        padding-left: 24px;
        width: 100%;

        .upsBox-img {
          width: 135px;
          background: url('~@/assets/images/qhdsys/monitor-turntable.png') no-repeat;
          background-size: 100% 100%;
          animation: jump 1s ease-out infinite alternate-reverse;
          position: relative;

          .ups-content {
            position: absolute;
            top: 21%;
            left: calc(50% - 50px);
            height: 41px;

            .ups-title {
              width: 100px;
              text-align: center;
              display: inline-block;
              font-size: 1rem;
              font-weight: 500;
              color: #b0e3fa;
            }

            .ups-value {
              display: inline-block;
              text-align: center;
              width: 100px;
              margin-top: 10px;

              span:nth-child(1) {
                font-size: 1.2rem;
                font-weight: bold;
                color: #ffca64;
              }

              .ups-unit {
                font-size: 0.9rem;
                font-weight: 400;
                color: #ffffff !important;
              }
            }
          }
        }

        .upsBox-content {
          // width: 320px;
          width: 60%;
          margin: auto;
        }
      }
    }

    .echarts-leftBottom {
      height: calc(100% / 3);
      background: url('~@/assets/images/qhdsys/monitor-bg330.png') no-repeat;
      background-size: 100% 100%;
      // padding: 4px;
      margin-top: 16px;
      box-sizing: border-box;

      .bg-contentNew {
        box-sizing: border-box;
        padding: 10px 10px 15px 10px;
        width: 100%;
        height: calc(100% - 1.5rem);
      }

      .medicalGasBox {
        width: 450px;
        height: 84px;
        padding: 16px 46px 16px 24px;
        margin: auto;
        display: flex;
        background: rgba(133, 145, 206, 0.05);

        .medicalGasBox-left {
          margin: auto 0;
          width: 20%;
          text-align: center;
          margin-right: 20px;

          .deviceTotal {
            display: block;
            font-size: 0.9rem;
            font-weight: 400;
            color: #ffffff;
          }

          .medicalGasBox-value {
            margin-top: 10px;
            font-size: 1.2rem;
            display: block;
            font-weight: bold;
            color: #ffca64;
          }

          .unit {
            font-size: 0.9rem;
            font-weight: 400;
            color: rgba(255, 255, 255, 0.5);
          }
        }

        .medicalGasBox-right {
          flex: 1;
          margin: auto;
        }
      }

      .medicalGasBox-carousel {
        height: calc(100% - 100px);

        .gas-group {
          height: 100%;
          display: flex;
          justify-content: start;

          .gas-group-item {
            padding: 0 20px;
            display: flex;
            flex-direction: column;
            align-items: center;
            cursor: pointer;
          }
        }

        .dept-monitor {
          height: 30px;
          line-height: 30px;
          flex-shrink: 0;
          // margin: 10px 0;
          width: 100%;
          white-space: nowrap;
          text-overflow: ellipsis;
          font-size: 1rem;
          font-family: PingFangSC-Medium, PingFang SC;
          overflow: hidden;
          color: #ffffff;

          >span {
            display: inline-block;
            width: 14px;
            height: 14px;
            background: url('~@/assets/images/peace/arrow-right.png') no-repeat;
            background-size: 100% 100%;
            margin-right: 6px;
          }
        }

        .gas-list {
          flex-wrap: wrap;
          justify-content: center;
          height: 100px;
          width: 100%;
          text-align: center;
          overflow-y: scroll;

          .gas-list-item {
            flex-direction: column;
            margin-bottom: 5px;
            align-items: center;

            .item-value {
              display: block;
              font-size: 1.1rem;
              font-weight: bold;
              color: #ffffff;
              margin-top: 4px;
              line-height: 20px;

              i {
                font-size: 0.8rem;
              }
            }

            .item-name {
              display: block;
              font-size: 0.9rem;
              margin-top: 2px;
              color: #7eaef9;
            }

            .item-unit {
              font-size: 0.8rem;
            }
          }
        }

        .right-line {
          position: relative;

          &::before {
            content: '';
            position: absolute;
            width: 1px;
            height: 80%;
            background: #2756b2;
            top: 50%;
            right: 0;
            transform: translateY(-50%);
          }
        }

        .right-line:nth-child(3n) {
          position: relative;

          &::before {
            display: none;
            content: '';
            position: absolute;
            width: 1px;
            height: 80%;
            background: #2756b2;
            top: 50%;
            right: 0;
            transform: translateY(-50%);
          }
        }
      }
    }
  }

  .eahart-center {
    width: 37%;
    height: 100%;
    display: flex;
    margin: 0 16px;
    flex-direction: column;

    .content-item {
      width: calc(50%);

      .item-main {
        display: flex;
        align-items: center;

        .main-left {
          margin-left: 20px;
          width: 30%;

          .main-img {
            cursor: pointer;
            width: 70px;
            height: 68px;

            // background: url('~@/assets/images/peace/center-item-bgd.png');
            //position: relative;
            img {
              // position: absolute;
              // bottom: 15%;
              // left: calc(50% - 12px);
              width: 100%;
              height: 100%;
              // animation: jump 1s ease-out infinite alternate-reverse;
            }
          }
        }

        .main-right {
          margin-left: 10px;
          flex-wrap: wrap;
          flex: 1;

          .right-textBootom {
            display: flex;
            margin-top: 20px;
          }

          .right-text {
            flex: 1;
          }

          .right-title {
            font-size: 0.9rem;
            font-weight: 400;
            color: #7eaef9;
            line-height: 16px;
            margin-bottom: 8px;
          }

          .right-total {
            font-size: 1.2rem;
            font-family: DIN, DIN;
            margin-left: 8px;
            font-weight: bold;
            color: #ffca64;
          }

          .right-value {
            font-size: 1.1rem;
            font-weight: bold;
            color: #ffffff;
            line-height: 20px;
            text-shadow: 0px 0px 10px #0081ff;
          }

          .right-value-red {
            color: #ff5454 !important;
            text-shadow: none !important;
          }

          .right-unit {
            margin-left: 4px;
            font-size: 0.8rem;
            font-weight: 400;
            color: #ffffff;
            line-height: 14px;
          }
        }
      }
    }

    .echarts-centerTop {
      height: calc(((100% / 3) * 2) - 16px);
      background: url('~@/assets/images/qhdsys/monitor-bg602.png') no-repeat;
      background-size: 100% 100%;
      // padding: 4px;
      box-sizing: border-box;

      .bg-content {
        padding: 16px 32px;
        align-items: center;
        flex-wrap: wrap;
      }
    }

    .echarts-centerBottom {
      height: calc(100% / 3);
      background: url('~@/assets/images/qhdsys/monitor-bg330.png') no-repeat;
      background-size: 100% 100%;
      // padding: 4px;
      margin-top: 16px;
      box-sizing: border-box;

      .bg-content {
        padding: 16px 32px;
        align-items: center;
        flex-wrap: wrap;
      }
    }
  }

  .eahart-right {
    width: 32%;
    height: 100%;
    display: flex;
    flex-direction: column;

    .echarts-rightTop {
      height: calc((100% / 3) + 16px);
      background: url('~@/assets/images/qhdsys/monitor-bg301.png') no-repeat;
      background-size: 100% 100%;
      // padding: 4px;
      box-sizing: border-box;

      .bg-content {
        position: relative;
        box-sizing: border-box;
        padding: 10px 10px 15px 10px;
        width: 100%;
        height: calc(100% - 1.5rem);
        margin: auto;
      }

      .realTimeBox {
        display: flex;

        .realTimeBox-img {
          width: 170px;
          margin: auto;
        }

        .realTimeBox-content {
          flex: 1;
          display: flex;
          padding-top: 25px;
          overflow-x: hidden; //超出隐藏
          flex-wrap: wrap; //超出自动换行
        }
      }

      .right-item {
        width: calc(100% / 2);
        cursor: pointer;
      }

      .right-title {
        font-size: 1rem;
        font-weight: 500;
        color: #7eaef9;
        line-height: 19px;
      }

      .right-value {
        margin-top: 3px;
        display: flex;
        align-items: center;
      }

      .right-num {
        font-size: 1.5rem;
        font-weight: bold;
        color: #ffffff;
        line-height: 30px;
        font-style: oblique;
      }

      .right-ratio {
        margin-left: 10px;
        padding-left: 7px;
        font-size: 0.8rem;
        font-weight: 400;
        height: 17px;
        line-height: 17px;

        img {
          margin-left: 2px;
          width: 19px;
          height: 19px;
          vertical-align: bottom;
        }
      }
    }

    .echarts-rightCenter {
      height: calc((100% / 3) - 32px);
      background: url('~@/assets/images/qhdsys/monitor-bg285.png') no-repeat;
      background-size: 100% 100%;
      // padding: 4px;
      margin-top: 16px;
      box-sizing: border-box;
    }

    .echarts-rightBottom {
      height: calc(100% / 3);
      background: url('~@/assets/images/qhdsys/monitor-bg330.png') no-repeat;
      background-size: 100% 100%;
      // padding: 4px;
      margin-top: 16px;
      box-sizing: border-box;

      .bg-content {
        padding: 0 !important;
      }
    }
  }

  .bg-content {
    position: relative;
    box-sizing: border-box;
    padding: 10px 10px 15px 10px;
    width: 100%;
    height: calc(100% - 2.5rem);
    display: flex;
  }

  ::v-deep .carousel {
    width: 100%;
    height: 100%;

    .el-carousel__indicators {
      display: none;
    }

    .el-carousel__container {
      height: 100%;
    }

    .el-carousel__arrow {
      width: 30px;
      height: 64px;
      background-color: #163274;
      color: #5996f9;
      border-radius: 0;
      font-size: 1.4rem;
    }

    .el-carousel__arrow:hover {
      background-color: #373e5f;
      color: #eed6a0;
    }

    .el-carousel__arrow--left {
      left: -1px;
    }

    .el-carousel__arrow--right {
      right: -1px;
    }
  }
}

::v-deep .el-table--striped .el-table__body tr.el-table__row--striped td {
  background-color: rgba(168, 172, 171, 0.08) !important;
  /* def2ff f2faff */
}
</style>
