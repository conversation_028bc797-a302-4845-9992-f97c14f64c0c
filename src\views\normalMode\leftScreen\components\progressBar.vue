<template>
  <div class="progressBar">
    <div :class="{ bgColor: isShowBgColor }">
      <div class="chart" :style="{ 'grid-template-columns': getLineWidth(stateList, 98, 1), gridGap: gridGap }">
        <div v-for="(v, i) in stateList" :key="i + 'eqTypeArrA'" class="chart_line">
          <div class="line" :style="{ background: v.lineColor, height: barHeight, borderColor: v.borderColor }"></div>
        </div>
      </div>
    </div>

    <div class="chart" :style="{ 'grid-template-columns': getLineWidth(stateList, cMax, cMin) }">
      <div v-for="(v, i) in stateList" :key="i + 'eqTypeArrB'" class="chart_line">
        <p>{{ v.name }}</p>
        <span :style="{ color: v.color }">{{ v.value }}</span>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'progressBar',
  props: {
    stateList: {
      type: Array,
      default: () => []
    },
    cMax: {
      type: Number,
      default: 70
    },
    cMin: {
      type: Number,
      default: 15
    },
    barHeight: {
      type: String,
      default: '4px'
    },
    gridGap: {
      type: String,
      default: '1px'
    },
    isShowBgColor: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {}
  },
  computed: {},
  created() {},
  methods: {
    getLineWidth(eqTypeArr, cMax, cMin) {
      const total = eqTypeArr.reduce((a, b) => a + b.value, 0)
      let str = ''
      eqTypeArr.forEach((ele) => {
        const num = (ele.value / total) * 100
        // 最大值不得超过cMax   为其他元素留位置   最小值不得小于cMin
        const max = Math.max(num, cMin)
        const min = Math.min(max, cMax)
        // str += `${min.toFixed(2)}% `
        str += `${parseInt(min)}% `
      })
      return str
    }
  }
}
</script>

<style lang="scss" scoped>
.progressBar {
  width: 100%;
  .bgColor {
    background: rgba(133, 145, 206, 0.08);
    padding: 10px 20px 0px 20px;
    margin-bottom: 6px;
  }
  .chart {
    display: grid;
    grid-template-columns: auto auto auto;
    .chart_line {
      width: 100%;
      .line {
        width: 100%;
        margin: 0px 0px 10px 0px;
        border: 1px solid;
      }
      p {
        display: inline-block;
        font-size: 12px;
        color: #ffffff;
      }
      span {
        font-size: 12px;
        margin-left: 8px;
      }
    }
  }
}
</style>
