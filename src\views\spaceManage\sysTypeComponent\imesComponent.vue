<template>
  <div class="imesComponent">
    <ModuleCard :title="roomData.title + ' - 资产总览'" class="module-container" style="height: 17%">
      <div slot="content" class="statistics-top" style="height: 100%; padding: 10px 8px">
        <div v-for="(item, index) in statisticsData" :key="index">
          <p class="green-font">{{ (item.value ?? 0) + (item?.unit ?? '') }}</p>
          <p>{{ item.name }}</p>
        </div>
      </div>
    </ModuleCard>
    <ModuleCard title="资产总览" class="module-container" style="height: 30%">
      <div slot="content" class="box-content" style="height: 100%">
        <div v-if="assetServiceLifeShow" class="center-center">暂无数据</div>
        <div v-else style="width: 100%; height: 100%">
          <div id="assetServiceLifeEcharts"></div>
          <div class="pie_background"></div>
        </div>
      </div>
    </ModuleCard>
    <ModuleCard title="资产台账" class="module-container" style="height: 53%">
      <div slot="content" style="height: 100%">
        <el-table
          v-loading="tableLoading"
          v-el-table-infinite-scroll="tableLoadEvent"
          class="bottom-el-table"
          :data="assetData"
          height="calc(100% - 0px)"
          :cell-style="$tools.setCell(3)"
          :header-cell-style="$tools.setHeaderCell(3)"
          style="width: 100%"
          element-loading-background="rgba(0, 0, 0, 0.2)"
          @row-dblclick="selectConfigRowData"
        >
          <el-table-column v-for="(column, index) in tableColumn" :key="index" :prop="column.prop" :label="column.label" :min-width="column.minWidth" show-overflow-tooltip>
            <template slot-scope="scope">
              <table-render v-if="column.render" :sc="scope" :row="scope.row" :render="column.render"></table-render>
              <div v-else-if="!column.formatter">
                {{ scope.row[column.prop] }}
              </div>
              <div v-else>
                {{ column.formatter(scope.row) }}
              </div>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </ModuleCard>
    <template v-if="equipmentDetailShow">
      <equipmentDetailCopy :dialogShow="equipmentDetailShow" :deviceId="deviceId" @configCloseDialog="configCloseTableDialog"></equipmentDetailCopy>
    </template>
  </div>
</template>

<script>
import equipmentDetailCopy from '../components/equipmentDetailCopy.vue'
import {allAssetData, allStatisAssetsData} from '../components/assetData.js'
// import equipmentDetailTable from '../components/equipmentDetail.vue'
import { GetAssetslistData, GetAssetsStatisticsData } from '@/utils/spaceManage.js'
import * as echarts from 'echarts'
export default {
  name: 'imesComponent',
  components: {
    equipmentDetailCopy
  },
  props: {
    roomData: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      assetData: allAssetData[__PATH.VUE_APP_HOSPITAL_NODE] || allAssetData['szzlyy'],
      statisAssetsData: allStatisAssetsData[__PATH.VUE_APP_HOSPITAL_NODE] || allStatisAssetsData['szzlyy'],
      statisticsData: [
        {
          name: '资产总数',
          value: 0,
          unit: '件',
          field: 'total'
        },
        {
          name: '闲置',
          value: 0,
          unit: '件',
          field: 'unused'
        },
        {
          name: '正常',
          value: 0,
          unit: '件',
          field: 'normal'
        },
        {
          name: '报废',
          value: 0,
          unit: '件',
          field: 'scrap'
        },
        {
          name: '资产总价值',
          value: 0,
          unit: '元',
          field: 'money'
        }
      ],
      assetServiceLifeShow: false, // 资产使用年限是否显示
      tableData: [], // 列表数据
      tableColumn: [
        {
          prop: '资产名称',
          label: '资产名称'
        },
        {
          prop: '资产大类',
          label: '资产大类'
        },
        {
          prop: '规格型号',
          label: '规格型号'
        },
        {
          prop: '使用状态',
          label: '使用状态'
        }
      ], // 列表列数据
      tableLoading: false, // 列表加载框
      equipmentDetailShow: false,
      ssmCodeList: '',
      pagination: {
        total: 0,
        pageSize: 15,
        pageNo: 1
      }
    }
  },
  mounted() {
    this.statisticsData.forEach((item) => {
      item.value = this.statisAssetsData[item.field]
    })
    const chartdata = [
      {
        name: '0-3年',
        value: this.statisAssetsData['0-3'] ?? 0
      },
      {
        name: '3-10年',
        value: this.statisAssetsData['3-10'] ?? 0
      },
      {
        name: '10年以上',
        value: this.statisAssetsData['10-'] ?? 0
      }
    ]
    this.$nextTick(() => {
      this.assetServiceLifeEchart(chartdata)
    })
    this.tableData = [
      {
        assetsName: '多媒体控制柜',
        assetsClassesName: '通用设备',
        model: '台',
        useStatusName: '在用'
      },
      {
        assetsName: '会议桌',
        assetsClassesName: '通用设备',
        model: '台',
        useStatusName: '在用'
      },
      {
        assetsName: '创维电视',
        assetsClassesName: '通用设备',
        model: '台',
        useStatusName: '在用'
      },
      {
        assetsName: '创维电视',
        assetsClassesName: '通用设备',
        model: '台',
        useStatusName: '在用'
      },
      {
        assetsName: '松下投影仪',
        assetsClassesName: '通用设备',
        model: '台',
        useStatusName: '在用'
      }
    ]
    this.pagination.total = this.tableData.length
    return
    const ssmCodeList = this.roomData.ssmCodes.split(',')
    // ssmCodeList[0] === '#' 删除第一位元素
    if (ssmCodeList[0] === '#') {
      ssmCodeList.shift()
    }
    this.ssmCodeList = ssmCodeList
    this.GetAssetsStatisticsData(ssmCodeList)
    this.GetAssetsTableData()
  },
  methods: {
    // 获取知产总览数据
    GetAssetsStatisticsData(ssmCodeList) {
      GetAssetsStatisticsData({ spaceId: ssmCodeList.toString() }).then((res) => {
        const data = res.data
        if (data.code === '200') {
          this.statisticsData.forEach((item) => {
            item.value = data.data[item.field]
          })
          // 获取资产使用年限
          const chartdata = [
            {
              name: '0-3年',
              value: data.data['0-3'] ?? 0
            },
            {
              name: '3-10年',
              value: data.data['3-10'] ?? 0
            },
            {
              name: '10年以上',
              value: data.data['10-'] ?? 0
            }
          ]
          this.$nextTick(() => {
            this.assetServiceLifeEchart(chartdata)
          })
        }
      })
    },
    // 获取资产台账
    GetAssetsTableData() {
      const params = {
        spaceId: this.ssmCodeList.toString(),
        currentPage: this.pagination.pageNo,
        pageSize: this.pagination.pageSize,
        sort: 'desc',
        orderBy: 'assetsCode'
      }
      GetAssetslistData(params).then((res) => {
        const data = res.data
        if (data.code === '200') {
          this.tableData = data.data.list
          this.pagination.total = parseInt(res.data.sum)
        }
      })
    },
    tableLoadEvent() {
      if (this.pagination.total > this.pagination.pageNo * this.pagination.pageSize) {
        this.pagination.pageNo++
        this.GetAssetsTableData()
      }
    },
    // 资产使用年限echarts
    assetServiceLifeEchart(chartdata) {
      const getchart = echarts.init(document.getElementById('assetServiceLifeEcharts'))
      const nameList = Array.from(chartdata, ({ name }) => name)
      const data = []
      var color = ['rgba(31, 250, 255, 0.3)', 'rgba(255, 227, 166, 0.28)', 'rgba(147, 130, 255, 0.26)', 'rgba(0, 248, 114, 0.24)', 'rgba(46, 119, 251, 0.3)']
      var borderColor = ['rgba(31, 250, 255, 1)', 'rgba(255, 227, 166, 1)', 'rgba(147, 130, 255, 1)', 'rgba(0, 248, 114, 1)', 'rgba(46, 119, 251, 1)']
      for (var i = 0; i < chartdata.length; i++) {
        const randomRgbColor = this.$tools.randomRgbColor('array')
        data.push({
          name: chartdata[i].name,
          value: chartdata[i].value,
          itemStyle: {
            normal: {
              borderWidth: 1,
              shadowBlur: 200,
              borderColor: borderColor[i] ?? randomRgbColor[1],
              color: color[i] ?? randomRgbColor[0],
              label: {
                normal: { show: true }
              }
            }
          }
        })
      }
      const option = {
        backgroundColor: '',
        legend: {
          orient: 'vartical',
          x: 'left',
          top: 'center',
          left: '60%',
          bottom: '0%',
          data: nameList,
          itemWidth: 8,
          itemHeight: 8,
          itemGap: 16,
          textStyle: {
            color: '#B3C2DD' //  字体颜色
          },
          formatter: function (name) {
            var oa = option.series[0].data
            for (var i = 0; i < option.series[0].data.length; i++) {
              if (name === oa[i].name) {
                return ' ' + name + '     ' + oa[i].value + '个'
              }
            }
          }
        },
        series: [
          {
            name: '',
            type: 'pie',
            clockWise: false,
            center: ['25%', '50%'],
            radius: ['47%', '70%'],
            // hoverAnimation: false,
            label: {
              normal: {
                show: false,
                position: 'center',
                formatter: '{value|{c}}\n{label|{b}}',
                rich: {
                  value: {
                    padding: 5,
                    align: 'center',
                    verticalAlign: 'middle',
                    fontSize: 20
                  },
                  label: {
                    align: 'center',
                    verticalAlign: 'middle',
                    fontSize: 12
                  }
                }
              },
              emphasis: {
                show: true,
                textStyle: {
                  fontSize: '12',
                  color: '#fff'
                }
              }
            },
            data: data
          }
        ]
      }
      getchart.clear()
      getchart.setOption(option)
      // 随着屏幕大小调节图表
      window.addEventListener('resize', () => {
        getchart.resize()
      })
      getchart.dispatchAction({
        type: 'highlight',
        seriesIndex: 0,
        dataIndex: 0
      })
      getchart.on('mouseover', (e) => {
        // 取消默认高亮
        getchart.dispatchAction({
          type: 'downplay',
          seriesIndex: 0,
          dataIndex: 0
        })
      })
      // 鼠标移出后默认高亮
      getchart.on('mouseout', (e) => {
        getchart.dispatchAction({
          type: 'highlight',
          seriesIndex: 0,
          dataIndex: 0
        })
      })
    },
    // 跳转资产 设备详情页面
    selectConfigRowData(row) {
      // return false
      this.deviceId = row.id
      console.log(this.deviceId)
      this.equipmentDetailShow = true
      try {
        window.chrome.webview.hostObjects.sync.bridge.GetSwichFloorIsShow(true)
        window.chrome.webview.hostObjects.sync.bridge.ShowDialog(true)
      } catch (error) {}
    },
    configCloseTableDialog() {
      this.equipmentDetailShow = false
      try {
        window.chrome.webview.hostObjects.sync.bridge.GetSwichFloorIsShow(false)
        window.chrome.webview.hostObjects.sync.bridge.ShowDialog(false)
      } catch (error) {}
    }
  }
}
</script>

<style lang="scss" scoped>
.imesComponent {
  width: 100%;
  height: 100%;
  .statistics-top {
    height: 100%;
    width: 100%;
    display: flex;
    justify-content: space-between;
    > div {
      width: max-content;
      // width: 20%;
      height: 100%;
      // padding: 10px 0;
      box-sizing: border-box;
      display: flex;
      justify-content: space-around;
      flex-direction: column;
      p {
        text-align: center;
        font-size: 0.75rem;
        font-family: DIN-Bold, DIN;
        color: #ffffff;
      }
      .green-font {
        font-size: 0.875rem;
        color: #ffe3a6;
        font-weight: bold;
      }
    }
  }
  .asset-service-life {
    width: 100%;
    height: calc(35% - 20px);
    border-top: 1px solid #2e4989;
    border-bottom: 1px solid #2e4989;
    padding: 10px 0;
  }
  .assets-ledger-table {
    width: 100%;
    height: calc(50% - 10px);
    padding-top: 10px;
    ::v-deep .el-table {
      border: none !important;
      .el-table__header-wrapper {
        .cell {
          padding-left: 0;
          padding-right: 0;
          text-align: center;
          white-space: nowrap;
        }
      }
      .el-table__body-wrapper {
        td.el-table__cell div {
          overflow: hidden;
          white-space: nowrap;
          text-overflow: ellipsis;
        }
      }
    }
  }
  .box-title {
    height: 30px;
    line-height: 30px;
    font-size: 14px;
    font-family: PingFangSC-Medium, PingFang SC;
    color: #ffffff;
    display: flex;
    justify-content: space-between;
    :last-child {
      margin-right: 10px;
      font-size: 14px;
      font-family: PingFang-SC-Medium, PingFang-SC;
      color: #7eaef9;
      cursor: pointer;
    }
    > div {
      > span {
        display: inline-block;
        // width: 15px;
        // height: 15px;
        // background: url('~@/assets/images/peace/arrow-bottom.png') no-repeat;
        // background-size: 100% 100%;
        width: 2px;
        height: 16px;
        background: #ffe3a6;
        margin-right: 6px;
        vertical-align: middle;
      }
    }
  }
  .box-content {
    width: 100%;
    height: calc(100% - 30px);
    position: relative;
    box-sizing: border-box;
    padding: 10px 10px 15px 10px;
    .pie_background {
      left: 26%;
    }
    #assetServiceLifeEcharts {
      position: relative;
      width: 100%;
      height: 100%;
      z-index: 2;
    }
  }
  ::v-deep .el-table__body-wrapper {
    background: transparent;
    overflow: hidden;
    overflow-y: auto;
    .el-table__body {
      background: transparent;
      tbody {
        background: transparent;
        .el-table__row {
          // background-color: rgba(255, 255, 255, 0.2);
          background: transparent;
          border: 0;
          td {
            border: 0;
            padding: 0;
            height: 30px;
            div {
              font-size: 14px;
              font-family: HarmonyOS Sans SC-Regular, HarmonyOS Sans SC;
              font-weight: 400;
              color: #ffffff;
            }
          }
        }
        .el-table__row:nth-child(2n - 1) {
          background: rgba(168, 172, 171, 0.08);
        }
        .el-table__row:hover {
          border: 0;
          opacity: 1;
          cursor: pointer;
          td div {
            color: rgba(255, 202, 100, 1);
          }
        }
      }
    }
  }
}
</style>
