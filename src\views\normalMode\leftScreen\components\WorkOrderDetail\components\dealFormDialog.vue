<template>
  <el-dialog v-dialogDrag :modal="false" :show-close="false" :close-on-click-modal="false" title="工单处置" width="40%" append-to-body :visible.sync="formVisible" custom-class="DealFormDialog" :before-close="closeDialog">
    <div class="dialog-right">
      <div class="dialog-tc" @click="closeDialog"></div>
    </div>
    <div class="dialog-content">
      <el-form ref="formInline" class="content-form" :model="formInline" :rules="rules" :inline="true" label-width="auto" size="medium">
        <!-- 督办 回复 回访 -->
        <div v-if="taskDetailReturnVisitFlag">
          <div class="formRow">
            <el-form-item :label="returnVisitName + '说明：'" prop="feedbackFlagExplain">
              <el-input
                v-model.trim="formInline.feedbackFlagExplain"
                maxlength="500"
                placeholder="请输入描述，限制五百字"
                type="textarea"
                :rows="9"
                onkeyup="if(value.length>500)value=value.slice(0,500)"
                show-word-limit
              ></el-input>
            </el-form-item>
          </div>
          <div v-if="dialogDetail.olgTaskManagement.flowcode === '5'" class="formRow">
            <el-form-item label="来电号码：" prop="autoCallNum">
              <el-input v-model.trim="formInline.autoCallNum" maxlength="20" placeholder="请输入来电号码" onkeyup="value=value.replace(/[^\d-]/g,'')"></el-input>
            </el-form-item>
          </div>
        </div>
        <!-- 取消 -->
        <div v-if="taskDetailCancelFlag">
          <div class="formRow">
            <el-form-item label="取消理由：" prop="cancelReasonId">
              <el-select v-model="formInline.cancelReasonId" placeholder="请选择取消理由">
                <el-option v-for="item in cancelReasonOptions" :key="item.value" :label="item.label" :value="item.value"> </el-option>
              </el-select>
            </el-form-item>
          </div>
          <div class="formRow">
            <el-form-item label="取消说明：" prop="cancelExplain">
              <el-input
                v-model.trim="formInline.cancelExplain"
                maxlength="500"
                placeholder="请输入取消说明，限制五百字"
                type="textarea"
                onkeyup="if(value.length>500)value=value.slice(0,500)"
                show-word-limit
              ></el-input>
            </el-form-item>
          </div>
        </div>
        <!-- 转派 -->
        <div v-if="taskDetailTransferFlag">
          <div class="formRow">
            <el-form-item label="指派班组：" prop="designateDeptCodeTransfer">
              <el-select v-model="formInline.designateDeptCodeTransfer" placeholder="请选择班组" @change="changeDept">
                <el-option v-for="item in teamsOptions" :key="item.id" :label="item.team_name" :value="item.id + '_' + item.team_name"> </el-option>
              </el-select>
            </el-form-item>
          </div>
          <div class="formRow">
            <el-form-item label="服务人员：" prop="designatePerson">
              <el-input v-model="formInline.designatePerson" readonly="readonly" placeholder="请选择服务人员" @focus="designPersonTra('turnTo')"></el-input>
            </el-form-item>
          </div>
          <div class="formRow">
            <el-form-item label="消息通知：" prop="disMessageFlag">
              <el-checkbox v-model="formInline.disMessageFlag"></el-checkbox>
            </el-form-item>
          </div>
          <div class="formRow">
            <el-form-item label="" prop="">
              <table v-if="selectPeopleRow.length" class="maint-table" style="table-layout: fixed;">
                <tbody>
                  <tr>
                    <td style="width: 42%; color: #7EAEF9;">服务人员</td>
                    <td style="width: 42%; color: #7EAEF9;">人员电话</td>
                    <td style="width: 16%; color: #7EAEF9;">操作</td>
                  </tr>
                  <tr v-for="(item, index) in selectPeopleRow" :key="index">
                    <td>
                      <div :title="item.member_name" class="one-line">{{ item.member_name }}</div>
                    </td>
                    <td>
                      <div :title="item.phone" class="one-line">{{ item.phone }}</div>
                    </td>
                    <td>
                      <div class="one-line scope-del" @click="peopleDel(item.id)">删除</div>
                    </td>
                  </tr>
                </tbody>
              </table>
            </el-form-item>
          </div>
          <div class="formRow">
            <el-form-item label="转派说明：" prop="feedbackExplain">
              <el-input
                v-model.trim="formInline.feedbackExplain"
                maxlength="500"
                placeholder="请输入转派说明，限制五百字"
                type="textarea"
                onkeyup="if(value.length>500)value=value.slice(0,500)"
                show-word-limit
              ></el-input>
            </el-form-item>
          </div>
        </div>
        <!-- 指派 -->
        <div v-if="taskDetailToPersonFlag">
          <div class="formRow">
            <el-form-item label="服务人员：" prop="designatePerson">
              <el-input v-model.trim="formInline.designatePerson" readonly="readonly" placeholder="请选择服务人员" @focus="designPersonTra('pointTo')"></el-input>
            </el-form-item>
          </div>
          <div class="formRow">
            <el-form-item label="消息通知：" prop="disMessageFlag">
              <el-checkbox v-model="formInline.disMessageFlag"></el-checkbox>
            </el-form-item>
          </div>
          <div class="formRow">
            <el-form-item label="" prop="">
              <table v-if="selectPeopleRow.length" class="maint-table" style="table-layout: fixed;">
                <tbody>
                  <tr>
                    <td style="width: 42%; color: #7EAEF9;">服务人员</td>
                    <td style="width: 42%; color: #7EAEF9;">人员电话</td>
                    <td style="width: 16%; color: #7EAEF9;">操作</td>
                  </tr>
                  <tr v-for="(item, index) in selectPeopleRow" :key="index">
                    <td>
                      <div :title="item.member_name" class="one-line">{{ item.member_name }}</div>
                    </td>
                    <td>
                      <div :title="item.phone" class="one-line">{{ item.phone }}</div>
                    </td>
                    <td>
                      <div class="one-line scope-del" @click="peopleDel(item.id)">删除</div>
                    </td>
                  </tr>
                </tbody>
              </table>
            </el-form-item>
          </div>
        </div>
        <!-- 挂单 -->
        <div v-if="taskDetailEntryOrderFlag">
          <div class="formRow">
            <el-form-item label="挂单说明：" prop="disEntryOrdersReasonCode">
              <el-select v-model="formInline.disEntryOrdersReasonCode" placeholder="请选择挂单说明">
                <el-option v-for="item in EntryOrderOptions" :key="item.id" :label="item.name" :value="item.id"> </el-option>
              </el-select>
            </el-form-item>
          </div>
          <div class="formRow">
            <el-form-item label="解决说明：" prop="disEntryOrdersSolution">
              <el-input
                v-model.trim="formInline.disEntryOrdersSolution"
                maxlength="500"
                placeholder="请输入解决说明，限制五百字"
                type="textarea"
                onkeyup="if(value.length>500)value=value.slice(0,500)"
                show-word-limit
              ></el-input>
            </el-form-item>
          </div>
          <div class="formRow">
            <el-form-item label="预计解决：" prop="planSolutionTime">
              <el-date-picker v-model="formInline.planSolutionTime" value-format="yyyy-MM-dd" popper-class="timePicker" type="date" :picker-options="pickerOptions" placeholder="选择日期">
              </el-date-picker>
            </el-form-item>
          </div>
        </div>
        <!-- 修改 -->
        <div v-if="taskDetailUpdateOrderFlag">
          <div v-if="dialogDetail.isItem === 'Y'" class="formRow">
            <el-form-item label="服务事项：" prop="itemServiceCode">
              <SelectTree v-model="itemServiceCode" :level="'3'" :data="itemTreeData" :props="{ label: 'name', children: 'children' }" @getName="getItemServiceName"></SelectTree>
            </el-form-item>
          </div>
          <!-- 不等于 已完工 -->
          <div v-if="dialogDetail.olgTaskManagement.flowcode !== '5'" class="formRow">
            <el-form-item label="服务部门：" prop="designateDeptCodeUpdate">
              <el-select v-model="formInline.designateDeptCodeUpdate" placeholder="请选择服务部门">
                <el-option v-for="item in teamsOptions" :key="item.id" :label="item.team_name" :value="item.id + '_' + item.team_name"> </el-option>
              </el-select>
            </el-form-item>
          </div>
          <div v-if="dialogDetail.olgTaskManagement.flowcode === '5' && dialogDetail.completePrice === '1'" class="formRow">
            <el-form-item label="总服务费：" prop="completePrice">
              <el-input v-model.trim="formInline.completePrice" maxlength="10" placeholder="请输入总服务费" onkeyup="value=value.replace(/[^\d-]/g,'')"></el-input
              ><span style="margin-left: 15px;">元</span>
            </el-form-item>
          </div>
          <div class="formRow">
            <el-form-item label="申报描述：" prop="questionDescription">
              <el-input
                v-model.trim="formInline.questionDescription"
                maxlength="500"
                placeholder="请输入描述，限制五百字"
                type="textarea"
                onkeyup="if(value.length>500)value=value.slice(0,500)"
                show-word-limit
              ></el-input>
            </el-form-item>
          </div>
        </div>
        <!-- 完工 -->
        <div v-if="taskDetailFinishOrderFlag">
          <div class="formRow">
            <el-form-item label="完工时间：" prop="finishTime">
              <el-input v-show="false" v-model="formInline.finishTime"></el-input><span>{{ nowTime }}</span>
            </el-form-item>
          </div>
          <div v-if="!dialogDetail.olgTaskManagement.designatePersonName" class="formRow">
            <el-form-item label="服务人员：" prop="designatePerson">
              <el-input v-model.trim="formInline.designatePerson" readonly="readonly" placeholder="请选择服务人员" @focus="designPersonTra('pointTo')"></el-input>
            </el-form-item>
          </div>
          <div v-if="selectPeopleRow.length" class="formRow">
            <el-form-item label="" prop="">
              <table class="maint-table" style="table-layout: fixed;">
                <tbody>
                  <tr>
                    <td style="width: 42%; color: #7EAEF9;">服务人员</td>
                    <td style="width: 42%; color: #7EAEF9;">人员电话</td>
                    <td style="width: 16%; color: #7EAEF9;">操作</td>
                  </tr>
                  <tr v-for="(item, index) in selectPeopleRow" :key="index">
                    <td>
                      <div :title="item.member_name" class="one-line">{{ item.member_name }}</div>
                    </td>
                    <td>
                      <div :title="item.phone" class="one-line">{{ item.phone }}</div>
                    </td>
                    <td>
                      <div class="one-line scope-del" @click="peopleDel(item.id)">删除</div>
                    </td>
                  </tr>
                </tbody>
              </table>
            </el-form-item>
          </div>
          <div class="formRow">
            <el-form-item label="满意度评价：" prop="score">
              <el-rate v-model="formInline.score" :colors="['#3562db', '#3562db', '#3562db']" show-text text-color="#fff" :texts="rateTexts"> </el-rate>
            </el-form-item>
          </div>
          <div class="formRow">
            <el-form-item label="耗材实际使用：" prop="factMaterialUse" class="fact-materia">
              <div v-for="(factMaterial, ind) in factMaterialList" :key="ind" class="fact-materia-form">
                <div class="fact-materia-first">
                  <span v-if="ind === 0" class="span-plus" @click="spanPlus">+</span>
                  <span v-else class="span-reduce" @click="spanReduce(ind)">-</span>
                  <el-input v-model="formInline.factMaterialUse[ind]" class="fact-materia-input" readonly="readonly" placeholder="请选择耗材" @focus="materialUsed(ind)"></el-input>
                  <el-input v-show="false" v-model="materialId[ind]"></el-input>
                  <el-input v-show="false" v-model="factMaterialPrice[ind]"></el-input>
                  <el-input v-show="false" v-model="specification[ind]"></el-input>
                </div>
                <div v-if="formInline.factMaterialUse[ind]">
                  <span style="margin-left: 15px;">数量：</span>
                  <el-input-number v-model="factMaterialNum[ind]" size="mini" :min="1"></el-input-number>
                </div>
              </div>
            </el-form-item>
          </div>
          <div class="formRow">
            <el-form-item label="总服务费：" prop="completePrice">
              <el-input v-model.trim="formInline.completePrice" maxlength="10" placeholder="请输入总服务费" onkeyup="value=value.replace(/[^\d-]/g,'')"></el-input
              ><span style="margin-left: 15px;">元</span>
            </el-form-item>
          </div>
          <div class="formRow">
            <el-form-item label="故障原因和维修方法：">
              <div>
                <span :style="{ color: '#3562db', cursor: 'pointer' }" @click="changeMalfunction">请选择</span>
                <table v-if="newReason.length" class="maint-table" style="table-layout: fixed; margin-left: 0;">
                  <tbody>
                    <tr>
                      <td style=" color: #7EAEF9;">故障原因</td>
                      <td style=" color: #7EAEF9;">维修方法</td>
                      <td style=" color: #7EAEF9;">操作</td>
                    </tr>
                    <tr v-for="(item, index) in newReason" :key="index">
                      <td :title="item.reasonName">
                        <div class="one-line">{{ item.reasonName }}</div>
                      </td>
                      <td>
                        <div v-for="(methodListObj, i) in item.methodName.split(',')" :key="i" class="one-line" :title="methodListObj">{{ methodListObj }}</div>
                      </td>
                      <td>
                        <div v-for="(methodListObj, i) in item.methodId.split(',')" :key="i" class="one-line scope-del" @click="malDel(methodListObj)">删除</div>
                      </td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </el-form-item>
          </div>
          <div class="formRow">
            <el-form-item label="完工说明：" prop="finishRemark">
              <el-input
                v-model.trim="formInline.finishRemark"
                maxlength="500"
                placeholder="请输入描述，限制五百字"
                type="textarea"
                onkeyup="if(value.length>500)value=value.slice(0,500)"
                show-word-limit
              ></el-input>
            </el-form-item>
          </div>
          <div class="formRow">
            <el-form-item label="工单附件选择上传：" prop="">
              <el-upload
                action=""
                list-type="picture-card"
                :file-list="fileList"
                multiple
                accept=".png,.jpg,.jpeg,.gif"
                :http-request="httpRequset"
                :on-preview="handlePictureCardPreview"
                :on-remove="handleRemove"
              >
                <i class="el-icon-plus"></i>
              </el-upload>
            </el-form-item>
          </div>
          <el-dialog v-dialogDrag :modal="false" :visible.sync="dialogVisible">
            <img width="100%" :src="dialogImageUrl" alt="" />
          </el-dialog>
        </div>
        <!-- 评价 -->
        <div v-if="taskDetailDegreeFlag">
          <div class="formRow">
            <el-form-item label="满意度评价：" prop="disDegreeNew">
              <el-rate v-model="formInline.disDegreeNew" :colors="['#3562db', '#3562db', '#3562db']" show-text text-color="#fff" :texts="rateTexts"> </el-rate>
            </el-form-item>
          </div>
          <div class="formRow">
            <el-form-item label="备注说明：" prop="remark">
              <el-input
                v-model.trim="formInline.remark"
                maxlength="500"
                placeholder="请输入描述，限制五百字"
                type="textarea"
                onkeyup="if(value.length>500)value=value.slice(0,500)"
                show-word-limit
              ></el-input>
            </el-form-item>
          </div>
        </div>
        <!-- <div v-if="taskDetailDoPrintFlag">打印</div> -->
      </el-form>
      <!-- 选择班组人员弹框 -->
      <template v-if="changeTeamsPeopleShow">
        <teamsPeople ref="changeTeamsPeople" :selectTeamsData="selectTeamsData" :changeTeamsPeopleShow="changeTeamsPeopleShow" @peopleSure="peopleSure" @closeDialog="closePeopleDialog"></teamsPeople>
      </template>
      <!-- 选择耗材弹窗 -->
      <template v-if="changefactMaterialShow">
        <factMaterial
          ref="changefactMaterial"
          :factMaterialData="factMaterialData"
          :changefactMaterialShow="changefactMaterialShow"
          @factMaterialSure="factMaterialSure"
          @closeDialog="closefactMaterialDialog"
        ></factMaterial>
      </template>
      <!-- 选择故障维修弹窗 -->
      <template v-if="changeMalfunctionShow">
        <malfunction
          ref="changemalfunction"
          :malfunctionData="malfunctionData"
          :itemServiceCode="itemServiceCodeToMal"
          :changeMalfunctionShow="changeMalfunctionShow"
          @malfunctionSure="malfunctionSure"
          @closeDialog="closemalfunctionDialog"
        ></malfunction>
      </template>
    </div>
    <span slot="footer" class="dialog-footer">
      <el-button class="sino-button-sure" @click="closeDialog">取 消</el-button>
      <el-button class="sino-button-sure" @click="userSaveFn">保 存</el-button>
    </span>
  </el-dialog>
</template>
<script>
import {
  workOrderOperOrder,
  placeAndCancelOrder,
  toTeamsChangeTask,
  addFeedback,
  updateTask,
  pdEvaluationOk,
  getDataByTypeTeam,
  getItemTreeData,
  getIomsDictList,
  uploadFiles
} from '@/utils/peaceLeftScreenApi'
import SelectTree from '../../selectTree'
import teamsPeople from '../../CreatedWorkOrder/common/teamsPeople'
import factMaterial from './factMaterial.vue'
import malfunction from './malfunction.vue'
import moment from 'moment'
export default {
  name: 'DealFormDialog',
  components: {
    SelectTree,
    teamsPeople,
    factMaterial,
    malfunction
  },
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    showBtnType: {
      type: String,
      default: ''
    },
    dialogDetail: {
      type: Object,
      default: () => {}
    },
    returnVisitName: {
      type: String,
      default: ''
    },
    projectCode: {
      type: String,
      default: ''
    }
  },
  data() {
    var validatePhoneNumber = (rule, value, callback) => {
      // 这里每一种情况都要callback，不然表单验证会失效
      // const Cellpho = /^[1][3,4,5,7,8][0-9]{9}$/
      var regexpGH = /(\d{4}-)?\d{6,8}/ // 固话正则
      var regexpSJ = /^1[3456789]\d{9}$/ // 手机号正则
      if (value === '') {
        callback()
      } else if (!regexpGH.test(value) && !regexpSJ.test(value)) {
        callback(new Error('电话格式填写错误!'))
      } else {
        callback()
      }
    }
    return {
      nowTime: moment().format('YYYY-MM-DD HH:mm:ss'),
      taskDetailCancelFlag: false,
      taskDetailReturnVisitFlag: false,
      taskDetailTransferFlag: false,
      taskDetailToPersonFlag: false,
      taskDetailEntryOrderFlag: false,
      taskDetailUpdateOrderFlag: false,
      taskDetailFinishOrderFlag: false,
      taskDetailDegreeFlag: false,
      taskDetailDoPrintFlag: false,
      // returnVisitName: '', // 督办 回复 说明
      formVisible: false,
      dialogImageUrl: '',
      dialogVisible: false,
      fileList: [],
      fileData: [],
      formInline: {
        feedbackFlagExplain: '',
        autoCallNum: '',
        designateDeptCodeTransfer: '',
        designatePerson: null,
        remark: '',
        disDegreeNew: 0,
        designateDeptCodeUpdate: '',
        completePrice: null,
        questionDescription: '',
        disMessageFlag: true,
        feedbackExplain: '',
        disEntryOrdersReasonCode: '',
        disEntryOrdersSolution: '',
        planSolutionTime: '',
        cancelExplain: '',
        cancelReasonId: '',
        finishTime: '',
        factMaterialUse: [],
        finishRemark: '',
        score: ''
      },
      materialId: [],
      factMaterialPrice: [],
      specification: [],
      factMaterialNum: [],
      designatePersonValTransfer: '', // 选中转派人员
      itemServiceCode: '', // 服务事项id
      itemServiceName: '', // 服务事项name
      rules: {
        feedbackFlagExplain: [
          { required: true, message: '请输入描述', trigger: 'blur' },
          { min: 1, max: 500, message: '长度在 1 到 500 个字符', trigger: 'blur' }
        ],
        designateDeptCodeTransfer: [{ required: true, message: '请选择服务部门', trigger: 'blur' }],
        designatePerson: [{ required: true, message: '请选择服务人员' }],
        disEntryOrdersReasonCode: [{ required: true, message: '请选择挂单说明', trigger: 'blur' }],
        disEntryOrdersSolution: [{ required: true, message: '请输入解决说明', trigger: 'blur' }],
        finishRemark: [{ required: true, message: '请输入完工说明', trigger: 'blur' }],
        cancelReasonId: [{ required: true, message: '请选择取消理由', trigger: 'blur' }],
        designateDeptCodeUpdate: [{ required: true, message: '请选择服务部门', trigger: 'blur' }],
        autoCallNum: [{ required: false }, { validator: validatePhoneNumber, trigger: 'blur' }]
      },
      changeTeamsPeopleShow: false, // 列表弹窗
      selectTeamsData: {}, // 选中班组的信息
      selectPeopleRow: [], // 选中人员的信息
      changefactMaterialShow: false, // 耗材弹窗
      selectFactIndex: 0, // 选中 耗材表单的角标
      factMaterialData: {},
      changeMalfunctionShow: false, // 选择 故障维修
      itemServiceCodeToMal: '', // 服务事项ok to 故障维修
      reason: [], // 故障原因 维修方法
      newReason: [], // table
      malfunctionData: {},
      workOrderDealShow: false, // 工单弹窗
      teamsOptions: [], // 班组
      rateTexts: ['非常差', '差', '一般', '满意', '非常满意'],
      DeptOptions: [
        {
          id: '1',
          name: '1'
        },
        {
          id: '2',
          name: '2'
        },
        {
          id: '3',
          name: '3'
        },
        {
          id: '4',
          name: '4'
        }
      ],
      cancelReasonOptions: [],
      EntryOrderOptions: [
        {
          id: '5',
          name: '暂无耗材'
        },
        {
          id: '4',
          name: '申报有误'
        },
        {
          id: '3',
          name: '指派有误'
        },
        {
          id: '2',
          name: '重复申报'
        },
        {
          id: '1',
          name: '延期处理'
        },
        {
          id: '0',
          name: '其他'
        }
      ],
      factMaterialList: 0,
      itemTreeData: [],
      pickerOptions: {
        disabledDate(time) {
          return time.getTime() < Date.now()
        }
      }
    }
  },
  mounted() {
    this.formVisible = this.visible
    switch (this.showBtnType) {
      case 'taskDetailCancelFlag': // 取消
        this.getIomsDictList('cancel_reason')
        break
      case 'taskDetailReturnVisitFlag': // 回访 督办 回复
        break
      case 'taskDetailTransferFlag': // 转派
        this.getTeamsByWorkTypeCode()
        break
      case 'taskDetailToPersonFlag': // 指派
        break
      case 'taskDetailEntryOrderFlag': // 挂单
        break
      case 'taskDetailFinishOrderFlag': // 完工
        // 一键完工 并且确警 不显示弹框 一键操作
        if (this.dialogDetail.olgTaskManagement.workTypeCode === '16') {
          this.formVisible = false
          this.OnekeyCompletion()
          return
        }
        this.factMaterialList = 0
        this.formInline.completePrice = this.dialogDetail.olgTaskManagement.completePrice
        this.spanPlus()
        break
      case 'taskDetailUpdateOrderFlag': // 修改
        this.getItemTreeData()
        this.getTeamsByWorkTypeCode(false, 'update')
        this.formInline.completePrice = this.dialogDetail.olgTaskManagement.completePrice
        this.formInline.questionDescription = this.dialogDetail.olgTaskManagement.questionDescription
        this.formInline.designateDeptCodeUpdate = this.dialogDetail.olgTaskManagement.designateDeptCode + '_' + this.dialogDetail.olgTaskManagement.designateDeptName
        break
      case 'taskDetailDegreeFlag': // 评价
        break
    }
    this.resetData()
    this[this.showBtnType] = true
  },
  methods: {
    // 指派提交
    taskDetailToPersonSave(params) {
      Object.assign(params)
      // Object.assign(params, __PATH.USER_CODE)
      workOrderOperOrder(params).then((res) => {
        if (res.data.success) {
          this.$message({
            message: res.data.msg,
            type: 'success'
          })
          this.$emit('submit')
        } else {
          this.$message({
            message: res.data.msg,
            type: 'warning'
          })
        }
      })
    },
    // 取消 提交
    taskDetailCanceSave(params) {
      placeAndCancelOrder(params).then((res) => {
        if (res.data.success) {
          this.$message({
            message: res.data.msg,
            type: 'success'
          })
          this.$emit('submit')
        } else {
          this.$message({
            message: res.data.msg,
            type: 'warning'
          })
        }
      })
    },
    // 转派 提交
    taskDetailTransferSave(params) {
      toTeamsChangeTask(params).then((res) => {
        if (res.data.success) {
          this.$message({
            message: res.data.msg,
            type: 'success'
          })
          this.$emit('submit')
        } else {
          this.$message({
            message: res.data.msg,
            type: 'warning'
          })
        }
      })
    },
    // 督办 提交
    taskDetailReturnVisitSave(params) {
      params.typeSources = '1'
      addFeedback(params).then((res) => {
        if (res.data.success) {
          this.$message({
            message: res.data.msg,
            type: 'success'
          })
          this.$emit('submit')
        } else {
          this.$message({
            message: res.data.msg,
            type: 'warning'
          })
        }
      })
    },
    // 修改 提交
    taskDetailUpdateOrderSave(params) {
      updateTask(params).then((res) => {
        if (res.data.success) {
          this.$message({
            message: res.data.msg,
            type: 'success'
          })
          this.$emit('submit')
        } else {
          this.$message({
            message: res.data.msg,
            type: 'warning'
          })
        }
      })
    },
    // 评价 提交
    taskDetailEvaluateSave(params) {
      pdEvaluationOk(params).then((res) => {
        if (res.data.success) {
          this.$message({
            message: res.data.msg,
            type: 'success'
          })
          this.$emit('submit')
        } else {
          this.$message({
            message: res.data.msg,
            type: 'warning'
          })
        }
      })
    },
    // 一键完工提交
    OnekeyCompletion() {
      this.formInline.finishRemark = '确警工单快速完工'
      this.$confirm('是否确认完成该工单？', '提示', {
        cancelButtonClass: 'el-button--primary is-plain',
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          this.userSaveFn('oneKeyFinish')
        })
        .catch(() => {
          this.closeDialog()
        })
    },
    // 获取班组
    getTeamsByWorkTypeCode(itemCode = false, btnType) {
      let localtionId = this.dialogDetail.taskDetail?.localtion
      if (btnType === 'update') {
        if (this.dialogDetail.olgTaskManagement.workTypeCode === '3') {
          localtionId = this.dialogDetail.taskDetail.transportStartLocal
        } else {
          localtionId = ''
        }
      }
      const params = {
        localtionId: localtionId,
        workTypeCode: this.dialogDetail.olgTaskManagement.workTypeCode,
        matterId: itemCode || this.dialogDetail.taskDetail?.itemTypeCode
      }
      getDataByTypeTeam(params).then((res) => {
        if (res.data.code === '200') {
          this.teamsOptions = res.data.data.list
        }
      })
    },
    // 服务事项返回数据
    getItemServiceName(item) {
      console.log(item)
      this.itemServiceCode = item.id
      this.itemServiceName = item.name
      this.formInline.designateDeptCodeUpdate = ''
      const itemCode = this.itemServiceCode.split('_').at(-1)
      this.getTeamsByWorkTypeCode(itemCode, 'update')
    },
    // 获取服务事项
    getItemTreeData() {
      const params = {
        workTypeCode: this.dialogDetail.olgTaskManagement.workTypeCode,
        free1: this.projectCode
      }
      getItemTreeData(params).then((res) => {
        this.itemTreeData = this.$tools.listToTree(res.data, 'id', 'parent')
      })
    },
    // 获取字典
    getIomsDictList(type) {
      const params = {
        type: type
      }
      getIomsDictList(params).then((res) => {
        this.cancelReasonOptions = res.data
      })
    },
    // 改变班组 人员置空
    changeDept() {
      this.formInline.designatePersonValTransfer = ''
      this.formInline.designatePerson = ''
      this.selectPeopleRow = []
    },
    // 选择人员 打开人员选择弹窗
    designPersonTra(type) {
      if (type === 'turnTo') {
        if (!this.formInline.designateDeptCodeTransfer) {
          this.$message({
            message: '请选择班组',
            type: 'warning'
          })
          return
        }
        this.selectTeamsData = {
          deptName: this.formInline.designateDeptCodeTransfer.split('_')[1],
          id: this.formInline.designateDeptCodeTransfer.split('_')[0]
        }
      } else if (type === 'pointTo') {
        this.selectTeamsData = {
          deptName: this.dialogDetail.olgTaskManagement.designateDeptName,
          id: this.dialogDetail.olgTaskManagement.designateDeptCode
        }
      }
      this.changeTeamsPeopleShow = true
    },
    // 选择耗材 打开耗材选择弹窗
    materialUsed(index) {
      this.changefactMaterialShow = true
      this.selectFactIndex = index
      // this.formInline.factMaterialUse[index] = index.toString()
      this.$forceUpdate()
    },
    // 选择 故障原因 维修方法 打开 弹窗
    changeMalfunction() {
      this.itemServiceCodeToMal = this.dialogDetail.wxDetail[0][8]
      this.changeMalfunctionShow = true
    },
    // 关闭当前弹窗
    closeDialog() {
      this.$emit('closeDialog')
    },
    userSaveFn(type) {
      const userInfo = this.$store.state.loginInfo.user
      const params = {
        userId: userInfo.staffId,
        userName: userInfo.staffName,
        officeName: '',
        disFlowcode: this.dialogDetail.olgTaskManagement.disFlowcode,
        id: this.dialogDetail.olgTaskManagement.id,
        operSource: this.dialogDetail.operSource,
        print: this.dialogDetail.print,
        workTypeCode: this.dialogDetail.olgTaskManagement.workTypeCode,
        flowcode: this.dialogDetail.olgTaskManagement.flowcode,
        workNum: this.dialogDetail.olgTaskManagement.workNum
      }
      // 一键完工
      if (type === 'oneKeyFinish') {
        Object.assign(params, {
          operFlag: '1',
          operType: 'operatorFinish',
          finishRemark: this.formInline.finishRemark
        })
        if (!this.dialogDetail.olgTaskManagement.designatePersonName) {
          Object.assign(params, {
            designatePersonValFinish: this.designatePersonValTransfer,
            designatePerson: this.formInline.designatePerson
          })
        }
        this.taskDetailToPersonSave(params)
      } else {
        this.$refs.formInline.validate((valid) => {
          if (valid) {
            // alert('submit!');
            // 转派
            if (this.taskDetailTransferFlag) {
              Object.assign(params, {
                designatePerson: this.formInline.designatePerson,
                designateDeptCodeTransfer: this.formInline.designateDeptCodeTransfer,
                designatePersonValTransfer: this.designatePersonValTransfer,
                disMessageFlag: this.formInline.disMessageFlag ? 'on' : 'off',
                feedbackExplain: this.formInline.feedbackExplain
              })
              this.taskDetailTransferSave(params)
            }
            // 督办 回访 回复
            if (this.taskDetailReturnVisitFlag) {
              Object.assign(params, { isAssignor: true, feedbackFlagExplain: this.formInline.feedbackFlagExplain })
              this.taskDetailReturnVisitSave(params)
            }
            // 指派人员
            if (this.taskDetailToPersonFlag) {
              Object.assign(params, {
                designatePerson: this.formInline.designatePerson,
                designatePersonVal: this.designatePersonValTransfer,
                messageFlag: this.formInline.disMessageFlag ? 'on' : 'off',
                dealType: 'toPerson',
                operFlag: '',
                disFlowcode: '1'
              })
              // 未派工的以重复派工走 如果不是重复派工，则时间轴需要补全已派工时间轴内容，否则新增已派工时间轴
              if (
                this.dialogDetail.olgTaskManagement.flowcode !== '1' ||
                (this.dialogDetail.olgTaskManagement.designatePersonCode !== undefined && this.dialogDetail.olgTaskManagement.designatePersonCode !== '')
              ) {
                Object.assign(params, { isAssignor: true })
              }
              this.taskDetailToPersonSave(params)
              if (this.dialogDetail.autoPrintSetting === '1') {
                this.$parent.doPrintOperator()
              }
            }
            // 挂单
            if (this.taskDetailEntryOrderFlag) {
              Object.assign(params, {
                disEntryOrdersReasonCode: this.formInline.disEntryOrdersReasonCode,
                disEntryOrdersSolution: this.formInline.disEntryOrdersSolution,
                planSolutionTime: this.formInline.planSolutionTime,
                operFlag: '2'
              })
              this.taskDetailToPersonSave(params)
            }
            // 取消
            if (this.taskDetailCancelFlag) {
              Object.assign(params, {
                cancelReasonId: this.formInline.cancelReasonId,
                cancelExplain: this.formInline.cancelExplain,
                operType: 'cancelOrder'
              })
              this.taskDetailCanceSave(params)
            }
            // 修改
            if (this.taskDetailUpdateOrderFlag) {
              Object.assign(params, {
                itemServiceCode: this.itemServiceCode.split('_')[0],
                itemServiceName: this.itemServiceName.split('_')[0],
                detailCode: this.itemServiceCode.split('_')[1],
                detailName: this.itemServiceName.split('_')[1],
                itemCode: this.itemServiceCode.split('_')[2],
                itemName: this.itemServiceName.split('_')[2],
                completePrice: this.formInline.completePrice,
                designateDeptCodeUpdate: this.formInline.designateDeptCodeUpdate,
                questionDescription: this.formInline.questionDescription
              })
              this.taskDetailUpdateOrderSave(params)
            }
            // 完工
            if (this.taskDetailFinishOrderFlag) {
              Object.assign(params, {
                finishTime: this.formInline.finishTime,
                factMaterialUse: this.formInline.factMaterialUse.toString(),
                materialId: this.materialId.toString(),
                factMaterialPrice: this.factMaterialPrice.toString(),
                specification: this.specification.toString(),
                factMaterialNum: this.factMaterialNum.toString(),
                operFlag: '1',
                operType: 'operatorFinish',
                finishRemark: this.formInline.finishRemark,
                reason: JSON.stringify(this.reason),
                score: this.formInline.score,
                completePrice: this.formInline.completePrice,
                attachmentUrl: this.fileData.length ? Array.from(this.fileData).map((item) => item.url).join(',') : ''
              })
              if (!this.dialogDetail.olgTaskManagement.designatePersonName) {
                Object.assign(params, {
                  designatePersonValFinish: this.designatePersonValTransfer,
                  designatePerson: this.formInline.designatePerson
                })
              }
              this.taskDetailToPersonSave(params)
            }
            // 评价
            if (this.taskDetailDegreeFlag) {
              Object.assign(params, {
                score: this.formInline.disDegreeNew,
                remark: this.formInline.remark,
                isSubmit: 0
              })
              this.taskDetailEvaluateSave(params)
            }
          } else {
            console.log('error submit!!')
            return false
          }
        })
      }
    },
    // 重置 data
    resetData() {
      this.taskDetailCancelFlag = false
      this.taskDetailReturnVisitFlag = false
      this.taskDetailTransferFlag = false
      this.taskDetailToPersonFlag = false
      this.taskDetailEntryOrderFlag = false
      this.taskDetailUpdateOrderFlag = false
      this.taskDetailFinishOrderFlag = false
      this.taskDetailDegreeFlag = false
      this.taskDetailDoPrintFlag = false
      // this.returnVisitName = '' // 督办 回复 说明
      this.dialogImageUrl = ''
      this.dialogVisible = false
      this.fileList = []
      this.fileData = []
      this.formInline = {
        feedbackFlagExplain: '',
        autoCallNum: '',
        designateDeptCodeTransfer: '',
        designatePerson: null,
        remark: '',
        disDegreeNew: '',
        designateDeptCodeUpdate: '',
        completePrice: null,
        questionDescription: '',
        disMessageFlag: true,
        feedbackExplain: '',
        disEntryOrdersReasonCode: '',
        disEntryOrdersSolution: '',
        planSolutionTime: '',
        cancelExplain: '',
        cancelReasonId: '',
        finishTime: '',
        factMaterialUse: [],
        finishRemark: '',
        score: 0
      }
      this.materialId = []
      this.factMaterialPrice = []
      this.specification = []
      this.factMaterialNum = []
      this.designatePersonValTransfer = ''
      this.itemServiceCode = ''
      this.itemServiceName = ''
      this.changeTeamsPeopleShow = false
      this.selectTeamsData = {}
      this.selectPeopleRow = []
      this.changefactMaterialShow = false
      this.selectFactIndex = 0
      this.factMaterialData = {}
      this.changeMalfunctionShow = false
      this.itemServiceCodeToMal = ''
      this.reason = []
      this.newReason = []
      this.malfunctionData = {}
      this.$nextTick(() => {
        this.$refs.formInline.resetFields()
        this.$refs.formInline.clearValidate()
      })
    },
    closePeopleDialog() {
      this.changeTeamsPeopleShow = false
    },
    peopleSure(item) {
      this.changeTeamsPeopleShow = false
      this.selectPeopleRow = item
      // this.formInline.designatePerson = item.designatePerson
      this.setFormPeopleData(this.selectPeopleRow)
    },
    closefactMaterialDialog() {
      this.changefactMaterialShow = false
    },
    factMaterialSure(item) {
      if (this.materialId.includes(item.id)) {
        return this.$message({
          message: '耗材选择重复！',
          type: 'warning'
        })
      }
      const index = this.selectFactIndex
      this.formInline.factMaterialUse[index] = item.depProductName
      this.materialId[index] = item.id
      this.factMaterialPrice[index] = item.price
      this.specification[index] = item.specification
      this.factMaterialNum[index] = 0
      this.changefactMaterialShow = false
    },
    malfunctionSure(item) {
      this.reason = item
      this.newReason = this.obj_merge(item)
      this.changeMalfunctionShow = false
    },
    closemalfunctionDialog() {
      this.changeMalfunctionShow = false
    },
    // 根据id删除故障维修
    malDel(id) {
      const obj = JSON.parse(JSON.stringify(this.reason))
      this.reason = obj.filter((e) => e.methodId !== id)
      this.newReason = this.obj_merge(this.reason)
    },
    // 根据id删除人员
    peopleDel(id) {
      this.selectPeopleRow = this.selectPeopleRow.filter((e) => e.id !== id)
      this.setFormPeopleData(this.selectPeopleRow)
    },
    // 选中人员 提交数据重组
    setFormPeopleData(selection) {
      // console.log(selection)
      if (selection.length) {
        const person = Array.from(selection, ({ member_name }) => member_name) // eslint-disable-line camelcase
        const personSplit = selection.map((e) => {
          return e.id + '_' + e.member_name + '_' + e.phone
        })
        this.formInline.designatePerson = person.toString()
        this.designatePersonValTransfer = personSplit.toString()
      } else {
        this.formInline.designatePerson = ''
        this.designatePersonValTransfer = ''
      }
    },
    // 增加 耗材
    spanPlus() {
      this.formInline.factMaterialUse.push('')
      this.materialId.push('')
      this.factMaterialPrice.push('')
      this.specification.push('')
      this.factMaterialNum.push(0)
      this.factMaterialList++
    },
    spanReduce(ind) {
      if (this.factMaterialList === 1) {
        return false
      }
      this.formInline.factMaterialUse.splice(ind, 1)
      this.materialId.splice(ind, 1)
      this.factMaterialPrice.splice(ind, 1)
      this.specification.splice(ind, 1)
      this.factMaterialNum.splice(ind, 1)
      this.factMaterialList--
    },
    httpRequset(file) {
      console.log(file, 'add')
      const params = new FormData()
      params.append('file', file.file)
      uploadFiles(params).then((res) => {
        if (res.data.code === '200') {
          this.$message({
            message: '上传成功',
            type: 'success'
          })
          this.fileData.push({
            id: file.file.uid,
            url: res.data.data.fileKey
          })
        } else {
          this.$message({
            message: res.data.message,
            type: 'warning'
          })
        }
      })
    },
    handleRemove(file, fileList) {
      this.fileList = fileList
      this.fileData = this.fileData.filter((e) => e.id !== file.uid)
    },
    handlePictureCardPreview(file) {
      console.log(file, 'preview')
      this.dialogImageUrl = file.url
      this.dialogVisible = true
    },
    obj_merge(obj) {
      const oldObj = JSON.parse(JSON.stringify(obj))
      const newObj = []
      oldObj.forEach((ele, index) => {
        if (index === 0) {
          newObj.push(ele)
        } else {
          const i = newObj.findIndex((e) => e.reasonId === ele.reasonId)
          if (i !== -1) {
            newObj[i].methodId = newObj[i].methodId + ',' + ele.methodId
            newObj[i].methodName = newObj[i].methodName + ',' + ele.methodName
          } else {
            newObj.push(ele)
          }
        }
      })
      return newObj
    }
  }
}
</script>
<style lang="scss" scoped>
::v-deep .el-dialog{
  background: url('@/assets/images/qhdsys/768×498.png') no-repeat center center / 100% 100%;
  .el-dialog__header {
    padding: 10px 20px 10px;
    text-align: center;
    .el-dialog__title {
      height: 20px;
      font-size: 18px;
      font-family: Source Han Sans CN-Regular, Source Han Sans CN;
      font-weight: 400;
      color: #cbdeed;
      line-height: 18px;
    }
  }

  .el-dialog__footer {
    padding-top: 0;
    padding-bottom: 40px;
    padding-right: 40px;
  }
}
.DealFormDialog {
  .dialog-right {
    position: absolute;
    top: 17px;
    right: 30px;
    display: flex;
    .dialog-tc {
      width: 36px;
      height: 36px;
      background: url('@/assets/images/qhdsys/bg-icon-gb.png') no-repeat;
    }
    .dialog-tc:hover {
      cursor: pointer;
    }
  }
  .dialog-content {
    height: 320px;
    overflow-y: auto;
    .content-form {
      padding: 15px 10px;
      box-sizing: border-box;
      // background: #fff;
    }
    ::v-deep .formRow {
      width: 80%;
      padding-left: 7%;
      display: flex;
      font-family: PingFang SC-Regular, PingFang SC;
      .el-form-item {
        flex: 1;
        display: flex;
      }
      .el-form-item__content {
        flex: 1;
        display: flex;
        .el-rate {
          margin: auto 0;
        }
        > .el-textarea {
          width: 100%;
        }
        > .el-select,
        > .el-input {
          width: 100%;
        }
      }
      .fact-materia {
        .el-form-item__content {
          flex-direction: column;
        }
        .fact-materia-form {
          display: flex;
          margin-bottom: 15px;
          > .el-input,
          > div {
            width: 100%;
          }
          .fact-materia-first {
            padding-left: 15px;
            position: relative;
            .span-plus {
              position: absolute;
              top: 0;
              left: -10px;
              font-size: 24px;
              color: red;
              cursor: pointer;
              // height: 50%;
              // line-height: 13px;
            }
            .span-reduce {
              position: absolute;
              top: 0;
              left: -10px;
              font-size: 32px;
              color: #f00;
              cursor: pointer;
              // height: 50%;
              // line-height: 13px;
            }
          }
          .el-input-number--mini {
            width: 6rem;
          }
        }
      }
    }
    .maint-table {
      width: 70%;
      margin: 0 0 20px 80px;
      border: 1px solid #7EAEF9;
      border-collapse: collapse;
      td {
        padding: 5px 0 5px 10px;
        border: 1px solid #7EAEF9;
        height: 25px;
        line-height: 25px;
        vertical-align: middle;
        div{
          color: #fff;
        }
      }
      tr:first-child {
        background: rgba(65, 62, 194, 0.1);
        // background-color: #7EAEF9;
      }
      td:first-child {
        width: 35%;
      }
      .one-line {
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
      .scope-del {
        color: #FFE3A6;
        cursor: pointer;
      }
    }
  }
}
</style>
