<template>
  <div class="component TextTagArea">
    <div v-if="showTag" class="TextTagArea__tag">
      <span>{{ tag }}</span>
      <span class="TextTagArea__tag__remove" v-if="removable" @click="updateTag('')">
        <img class="TextTagArea__tag__img" src="@/assets/images/common/icon-close.png">
      </span>
    </div>
    <el-input type="textarea" class="TextTagArea__textarea" :rows="rows" :maxlength="limit" show-word-limit
      :style="{ '--textIndent': textIndent }" resize="none" :placeholder="placeholder" :value="textValue"
      @input="emitTextValue">
    </el-input>
    <div v-if="showOption" class="TextTagArea__op-tags">
      <span class="TextTagArea__op-tag" v-for="opTag of options" :key="opTag" @click="updateTag(opTag)">{{ opTag
        }}</span>
    </div>
  </div>
</template>

<script>
export default {
  name: 'TextTagArea',
  model: {
    prop: 'textValue',
    event: 'change'
  },
  props: {
    tag: {
      type: String,
    },
    removable: {
      type: Boolean,
      default: false
    },
    textValue: {
      type: String,
      required: true
    },
    placeholder: {
      type: String,
      default: '请输入'
    },
    rows: {
      type: Number,
      default: 2
    },
    limit: {
      type: Number,
      default: 300
    },
    options: {
      type: Array,
      default: () => ([])
    }
  },
  computed: {
    showTag: function () {
      return this.tag && this.tag.length;
    },
    textIndent: function () {
      let indent = 0;
      if (this.showTag) {
        indent = this.tag.length * 14 + 24;

        if (this.removable) {
          indent += 24;
        }
      }
      return indent + 'px';
    },
    showOption: function () {
      return Array.isArray(this.options) && this.options.length;
    }
  },
  methods: {
    emitTextValue(value) {
      this.$emit('change', value.trim());
    },
    updateTag(value) {
      this.$emit('update:tag', value)
    }
  }

}
</script>

<style lang="scss" scoped>
.TextTagArea {
  width: 100%;
  resize: none;
  position: relative;
  line-height: 24px;

  &__tag {
    position: absolute;
    z-index: 1;
    top: 8px;
    left: 14px;
    color: #fff;
    background: rgba(133, 145, 206, 0.5);
    border-radius: 4px;
    padding: 0 10px;

    &__remove {
      margin-left: 8px;
      cursor: pointer;
    }

    &__img {
      vertical-align: middle;
      margin-top: -3px;
    }
  }

  &__textarea {


    ::v-deep .el-textarea__inner {
      border-color: #3056A2;
      text-indent: var(--textIndent);
      padding: 8px 14px;

      &:hover {
        border-color: #FFE3A6;
      }

      &::placeholder {
        color: #A6AFBF;
      }
    }
  }

  &__op-tags {
    position: absolute;
    bottom: 8px;
    left: 14px;
  }

  &__op-tag {
    color: #FFFFFF;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 4px;
    padding: 2px 10px;
    cursor: pointer;

    +.TextTagArea__op-tag {
      margin-left: 10px;
    }
  }
}
</style>
