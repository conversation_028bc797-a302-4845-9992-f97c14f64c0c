<template>
  <div class="content">
    <!-- <el-button type="primary" @click="operating('shielded', selectAlarmItem)">屏蔽</el-button>
    <el-button type="primary" @click="operating('confirmAlarm', selectAlarmItem)">确警</el-button>
    <el-button type="primary" @click="operating('dispatch', selectAlarmItem)">派单</el-button>
    <el-button type="primary" @click="selectWorkOrderType(olgTaskManagement)">新建工单</el-button> -->
    <screenDialog v-if="scrDialog" :selectItems="[selectAlarmItem]" :visible.sync="scrDialog" />
    <confirmAlarmDialog v-if="showConfirmAlarm" :visible.sync="showConfirmAlarm" :item="selectAlarmItem" />
    <template v-if="workOrderDealShow">
      <CreatedWorkOrder
        :workOrderDealShow.sync="workOrderDealShow"
        :workTypeCode="olgTaskManagement.workTypeCode"
        :workTypeName="olgTaskManagement.workTypeName"
        :alarmId="selectAlarmItem.alarmId"
        :projectCode="selectAlarmItem.projectCode"
        :spaceId="selectAlarmItem.alarmSpaceId"
        dealType="add"
        @workOrderSure="workOrderSure"
      />
    </template>
  </div>
</template>
<script>
import { OneKeyDispatch, GetAlarmDetails } from '@/utils/peaceLeftScreenApi'
// import { OneKeyDispatch, shield } from '@/utils/peaceLeftScreenApi'
export default {
  name: 'alarmDialogIframe',
  components: {
    screenDialog: () => import('../components/screenDialog'),
    confirmAlarmDialog: () => import('../components/confirmAlarmDialog'),
    CreatedWorkOrder: () => import('../components/CreatedWorkOrder')
  },
  data() {
    return {
      selectAlarmItem: {
        alarmId: 'BJ2023921629401693643380088',
        projectCode: '73e7aab447b34971b9ae6d8dae034aa3',
        shield: 0,
        alarmObjectId: '',
        incidentType: '1'
      },
      scrDialog: false, // 屏蔽弹窗
      showConfirmAlarm: false, // 确警弹窗
      workOrderDealShow: false, // 新建工单弹窗
      olgTaskManagement: {
        // 新增工单弹窗数据
        workTypeCode: '',
        workTypeName: ''
      },
      alertType: ''
    }
  },
  watch: {
    scrDialog(val) {
      if (!val) {
        this.initComponentData()
      }
    },
    showConfirmAlarm(val) {
      if (!val) {
        this.getAlarmDetails()
      }
    },
    workOrderDealShow(val) {
      if (!val) {
        this.initComponentData()
      }
    }
  },
  mounted() {
    const alarm = JSON.parse(this.$route.query?.alarm ?? '{}')
    if (alarm.type) {
      const alarmDealArr = ['dispatch', 'confirmAlarm', 'shielded']
      if (alarmDealArr.includes(alarm.type)) {
        this.selectAlarmItem = alarm.data
        this.operating(alarm.type, alarm.data)
      }
      const createOrderArr = ['selectWorkOrderType']
      if (createOrderArr.includes(alarm.type)) {
        this.selectWorkOrderType(alarm.workTypeCode, alarm.workTypeName)
      }
    }
    // 初始化 接收wpf传递过来的selectAlarmItem及需按钮的类型
    // 一站式需要类型名称及报警数据
    // this.selectWorkOrderType(id, name)
    // try {
    //   window.chrome.webview.addEventListener('message', (event) => {
    //     const data = JSON.parse(event.data)
    //     const alarmDealArr = ['dispatch', 'confirmAlarm', 'shielded']
    //     if (alarmDealArr.includes(data.type)) {
    //       this.selectAlarmItem = data.data
    //       this.operating(data.type, data.data)
    //     }
    //     const createOrderArr = ['selectWorkOrderType']
    //     if (createOrderArr.includes(data.type)) {
    //       this.selectWorkOrderType(data.workTypeCode, data.workTypeName)
    //     }
    //   })
    // } catch (errpr) {}
  },
  methods: {
    // 获取报警详情
    getAlarmDetails() {
      GetAlarmDetails({ alarmId: this.selectAlarmItem.alarmId }).then((res) => {
        if (res.data.code === '200') {
          console.log('this=================', this)
          window.chrome.webview.hostObjects.sync.bridge.DisposalComplete(res.data.data.record.alarmAffirm !== 0, this.alertType)
          this.initComponentData()
        }
      })
    },
    // 初始化组件数据
    initComponentData() {
      this.selectAlarmItem = {}
      // 告诉wpf功能已执行完毕
      window.chrome.webview.hostObjects.sync.bridge.CloseWindow()
    },
    // 一键派单
    oneKeyDispatch(row) {
      const userInfo = this.$store.state.loginInfo.user
      const param = {
        alarmId: row.alarmId,
        alarmLevel: row.alarmLevel,
        alarmSourceName: row.alarmSource,
        incidentName: row.incidentName,
        spaceId: row.alarmSpaceId,
        userName: userInfo.staffName,
        userId: userInfo.staffId,
        projectCode: row.projectCode
      }
      OneKeyDispatch(param).then((res) => {
        if (res.data.code === '200') {
          this.$message({
            message: '已派单',
            type: 'success'
          })
          this.initComponentData()
        } else {
          this.$message.error(res.data.msg)
        }
      })
    },
    // 子/孙组件流程操作
    operating(type, selectItem) {
      // dispatch(派单) confirmAlarm(确警) remark(备注) shielded(屏蔽) close(关闭) viewDetails(查看详情)
      this.selectAlarmItem = selectItem
      this.alertType = type
      console.log(11111, type, selectItem)
      if (type === 'dispatch') {
        // 派单
        this.$confirm('是否确认派发确警工单?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          cancelButtonClass: 'sino-button-sure',
          confirmButtonClass: 'sino-button-sure',
          customClass: 'confirm-box-class',
          type: 'warning'
        }).then(() => {
          try {
            window.chrome.webview.hostObjects.sync.bridge.DisposalComplete(true, this.alertType)
          } catch (err) {
          }
          this.oneKeyDispatch(selectItem)
        }).catch(() => {
          try {
            window.chrome.webview.hostObjects.sync.bridge.DisposalComplete(false, this.alertType)
          } catch (err) {
          }
          this.initComponentData()
        })
      }
      // 确警
      if (type === 'confirmAlarm') {
        this.showConfirmAlarm = !this.showConfirmAlarm
      }
      // 屏蔽
      if (type === 'shielded') {
        // if (selectItem.shield === 1) {
        //   this.$confirm('是否取消屏蔽当前报警?', '提示', {
        //     confirmButtonText: '确定',
        //     cancelButtonText: '取消',
        //     cancelButtonClass: 'sino-button-sure',
        //     confirmButtonClass: 'sino-button-sure',
        //     customClass: 'confirm-box-class',
        //     type: 'warning'
        //   }).then(() => {
        //     shield({
        //       alarmId: selectItem.alarmId,
        //       alarmObjectId: selectItem.alarmObjectId,
        //       incidentType: selectItem.incidentType,
        //       shield: false
        //     }).then((res) => {
        //       if (res.data.code === '200') {
        //         this.$message({
        //           message: '已取消屏蔽',
        //           type: 'success'
        //         })
        //         this.initComponentData()
        //       }
        //     })
        //   })
        // } else {
        //   this.scrDialog = !this.scrDialog
        // }
        this.scrDialog = !this.scrDialog
      }
    },
    // 新建工单 打开弹窗
    selectWorkOrderType(id, name) {
      this.alertType = 'selectWorkOrderType'
      this.olgTaskManagement = {
        workTypeCode: id,
        workTypeName: name
      }
      this.workOrderDealShow = true
    },
    // 新建工单提交
    workOrderSure() {
      this.workOrderDealShow = false
      this.initComponentData()
    }
  }
}
</script>
<style lang="scss" scoped>
@import '../../../../assets/sino-ui/common/var.scss';
html,
.content {
  position: relative;
  box-sizing: border-box;
  width: 100%;
  height: 100%;
  padding: 1.25rem 0.625rem 0.625rem 0.625rem;
  .title {
    height: 30px;
    // position: relative;
    position: absolute;
    left: 16px;
    top: 10px;
    div {
      cursor: pointer;
      position: absolute;
      top: 5px;
      left: 0;
      width: 80px;
      height: 30px;
      line-height: 30px;
      text-align: center;
      color: #618ad3;
      font-size: 0.875rem;
      white-space: nowrap;
      background-image: url('~@/assets/images/peace/btn-back.png');
      background-size: 100% 100%;
      background-repeat: no-repeat;
    }
  }
  .screen_back {
    .title {
      position: initial;
      div {
        left: 10px;
        top: 10px;
      }
    }
  }
  .search-form {
    height: 2.5rem;
    width: 100%;
    & > div {
      margin-right: 10px;
    }
    ::v-deep .el-input__inner {
      background: center;
      border: 1px solid $input-border-color;
      border-radius: 0;
      color: $color;
      height: inherit;
      font-family: PingFangSC-Medium, PingFang SC;
    }
    ::v-deep .el-range-input {
      background: center;
      color: $color;
    }
    ::v-deep .el-range-separator,
    ::v-deep .el-range__icon {
      color: #3769be;
    }
  }
  .eahart-list {
    width: 100%;
    height: 35%;
    margin-top: 0.7rem;
    display: flex;
    justify-content: space-between;
    .echarts-left {
      width: 20%;
      background: url('~@/assets/images/peace/emergency-disposal-left.png') no-repeat;
      background-size: 100% 100%;
      padding: 4px;
      box-sizing: border-box;
      .bg-content {
        display: flex;
        flex-wrap: wrap;
        align-content: space-around;
        padding-left: 35px;
        .right-item {
          width: 50%;
        }
        .right-title {
          font-size: 16px;
          font-weight: 500;
          color: #7eaef9;
          line-height: 19px;
        }
        .right-value {
          margin-top: 3px;
          display: flex;
          align-items: center;
        }
        .right-num {
          font-size: 26px;
          font-weight: bold;
          color: #ffffff;
          line-height: 30px;
          font-style: oblique;
        }
        .right-ratio {
          margin-left: 10px;
          padding-left: 7px;
          font-size: 14px;
          font-weight: 400;
          height: 17px;
          line-height: 17px;
          background: linear-gradient(90deg, rgba(106, 143, 211, 0.41) 0%, rgba(106, 143, 211, 0) 94%);
          img {
            margin-left: 2px;
            width: 6px;
            height: 9px;
          }
        }
      }
    }
    .echarts-center {
      width: 49%;
      background: url('~@/assets/images/peace/emergency-disposal-center.png') no-repeat;
      background-size: 100% 100%;
      padding: 3px;
      box-sizing: border-box;
    }
    .echarts-right {
      width: 30%;
      background: url('~@/assets/images/peace/emergency-disposal-right.png') no-repeat;
      background-size: 100% 100%;
      padding: 3px;
      box-sizing: border-box;
    }
    .bg-title {
      margin-top: 5px;
      height: 2.5rem;
      line-height: 2.5rem;
      color: #d4e3f9;
      padding-left: 3rem;
      font-family: TRENDS;
    }
    .center-center {
      width: 100%;
      height: 100%;
      display: flex;
      align-items: center;
      justify-content: center;
      color: #4d5880;
      font-size: 16px;
    }
    .bg-content {
      position: relative;
      box-sizing: border-box;
      padding: 10px 10px 15px 10px;
      width: 100%;
      height: calc(100% - 2.5rem);
      display: flex;
      #trendEchart,
      #alarmSourceEchart {
        width: 100%;
        height: 100%;
        position: relative;
        z-index: 2;
      }
      .alarm-bg {
        width: 15rem;
        height: 15rem;
        position: absolute;
        top: calc(50% - 7.6rem);
        left: 4.5%;
        background: url('~@/assets/images/peace/left-operation-warn-bg.png') no-repeat;
        background-size: 100% 100%;
      }
      .case-anim-icon {
        position: absolute;
        top: calc(50% - 1rem);
        left: calc(25% - 0.5rem);
        width: 2rem;
        height: 2rem;
        background: url('~@/assets/images/peace/icon-warn.png') no-repeat;
        background-size: 100% 100%;
      }
    }
  }
  .batch-control {
    height: 2.5rem;
    margin-top: 1rem;
    .is-disabled {
      background-image: url('~@/assets/images/sys/btn-bg.png') !important;
    }
  }
  .table-list {
    width: 100%;
    height: calc(65% - 7.5rem);
    margin-top: 0.8rem;
  }
}
</style>
<style lang="scss">
.search-form-tree {
  color: #fff !important;
  background: transparent !important;
  .el-tree-node__content:hover {
    background: transparent !important;
    .el-tree-node__label {
      color: #ffe3a6;
    }
  }
}
</style>
