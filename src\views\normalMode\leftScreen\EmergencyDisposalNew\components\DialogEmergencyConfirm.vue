<template>
  <el-dialog v-if="visible" v-dialogDrag v-loading="loadingStatus" class="component DialogEmergencyConfirm" center
             :close-on-click-modal="false" :modal="false" :show-close="false" :title="title" width="768px"
             custom-class="DialogEmergencyConfirm__dialog" :visible="visible" :before-close="closeDialog">
    <div class="DialogEmergencyConfirm__head">
      <div class="DialogEmergencyConfirm__close" @click="closeDialog"></div>
    </div>
    <div class="DialogEmergencyConfirm__content">
      <el-form ref="formRef" class="DialogEmergencyConfirm__form" :model="formModel" :rules="rules" label-width="82px"
               label-suffix=":">
        <el-form-item label="处理结果" prop="reason">
          <TextTagArea v-model="formModel.reason" :rows="4" :tag="tag">
          </TextTagArea>
        </el-form-item>
        <el-form-item label="处理人" prop="createUser">
          <EmergencyUserSelect v-model="formModel.createUser" />
        </el-form-item>
      </el-form>
      <div class="DialogEmergencyConfirm__action">
        <el-button class="sino-button-sure" @click="closeDialog">取消</el-button>
        <el-button class="sino-button-sure" @click="onSubmit">确定</el-button>
      </div>
    </div>
  </el-dialog>
</template>
<script>

import { AlarmAffirm } from '@/utils/peaceLeftScreenApi'
import { operatePlatform } from '../emergency-constant'

export default {
  name: 'DialogEmergencyConfirm',
  components: {
    TextTagArea: () => import('./TextTagArea'),
    EmergencyUserSelect: () => import('./EmergencyUserSelect')
  },
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    tag: String,
    state: Number,
    ids: String,
    codes: String
  },
  emits: ['success', 'update:visible'],
  data() {
    return {
      formModel: {
        reason: '',
        createUser: []
      },
      rules: {
        reason: [{ required: true, message: '请输入处理说明' }]
      },
      loadingStatus: false
    }
  },
  computed: {
    isBatch: function () {
      return this.ids.split(',').length > 1
    },
    title: function () {
      return this.isBatch ? '批量' + this.tag : this.tag
    }
  },
  watch: {
    visible() {
      this.$refs.formRef?.resetFields()
      this.loadingStatus = false
    }
  },
  mounted() {
    const userInfo = this.$store.state.loginInfo.user
    this.formModel.createUser = [
      {
        id: userInfo.id,
        staffNumber: userInfo.staffNumber,
        staffName: userInfo.staffName
      }
    ]
  },
  methods: {
    // 关闭弹窗
    closeDialog() {
      this.$emit('update:visible', false)
    },
    // 点击提交建议表单并调用接口
    onSubmit() {
      this.$refs.formRef.validate()
        .then(() => this.loadingStatus = true)
        .then(this.doUpdateStatus)
        .then(() => {
          this.$emit('success')
          this.closeDialog()
        })
        .catch(() => { })
        .finally(() => this.loadingStatus = false)
    },
    // 更新状态
    doUpdateStatus() {
      const defaultError = this.isBatch ? '批量操作失败' : '操作失败'
      const [user] = this.formModel.createUser
      if (!user) return
      let createName = user.staffName
      if (user.staffNumber) {
        createName += `(${user.staffNumber})`
      }

      return AlarmAffirm({
        alarmAffirm: this.state,
        alarmId: this.ids,
        projectCode: this.codes,
        remark: this.tag + this.formModel.reason,
        operationSource: operatePlatform.client,
        createName
      })
        .then(res => {
          if (res.data.code == 200) {
            this.$message.success('操作成功')
          } else {
            throw res.data.msg || defaultError
          }
        })
        .catch(err => {
          this.$message.error(err)
          return Promise.reject()
        })
    }
  }
}
</script>
<style lang="scss" scoped>
::v-deep .DialogEmergencyConfirm__dialog.el-dialog {
  background: url('~@/assets/images/qhdsys/768×453.png') no-repeat center center / 100% 100%;
  height: 453px;
  overflow: hidden;

  .el-dialog__header {
    padding: 10px 20px 10px;
    text-align: center;

    .el-dialog__title {
      height: 20px;
      font-size: 18px;
      font-family: Source Han Sans CN-Regular, Source Han Sans CN;
      font-weight: 400;
      color: #cbdeed;
      line-height: 18px;
    }
  }

  .el-dialog__body {
    height: calc(100% - 40px);
  }

  .el-dialog__footer {
    padding-top: 0;
    padding-bottom: 40px;
    padding-right: 40px;
  }
}

.DialogEmergencyConfirm {
  background: rgba(0, 0, 0, 0.6);

  &__head {
    position: absolute;
    top: 18px;
    right: 52px;
    display: flex;
  }

  &__close {
    width: 36px;
    height: 36px;
    background: url('@/assets/images/qhdsys/bg-icon-gb.png') no-repeat;
    cursor: pointer;
  }

  &__content {
    height: 100%;
    display: flex;
    flex-flow: column nowrap;
  }

  &__form {
    flex: 1;
    padding: 30px 35px 0;

    ::v-deep .el-form-item__label {
      color: #B0E3FA;
    }
  }

  &__action {
    text-align: right;
    padding: 30px 35px;
  }
}
</style>
