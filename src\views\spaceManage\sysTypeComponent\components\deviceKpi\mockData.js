/*
 * @Description:
 */
/*
 * @Description:
 */
export const mockRETrend = {
  annualIncome: '123330.120',
  annualIncomeLastYear: '12.3%',
  annualExpenditures: '330.120',
  annualExpendituresLastYear: '12.3',
  annualProfit: '123000.000',
  annualProfitLastYear: '12.3'
}
export const mockREStatisData = {
  nameList: ['2019', '2020', '2021', '2022', '2023'],
  income: [25, 22, 29, 18, 21],
  expenditure: [18, 22, 14, 15, 17],
  profit: [7, 4, 7, 4, 4]
}
export const mockIncomeData = {
  legendList: ['检查收入', '药品收入', '耗材收入', '治疗收入'],
  nameList: ['2019', '2020', '2021', '2022', '2023'],
  checkIncome: [25, 22, 29, 18, 21],
  drugIncome: [18, 22, 14, 15, 17],
  consumableIncome: [7, 4, 7, 4, 4],
  treatmentIncome: [8, 12, 15, 7, 11]
}
export const mockInvestData = {
  recyclingYear: '5',
  balanceYear: '2',
  profit: '1,233,372'
}
export const mockRateData = {
  legendList: ['利润率', '投资收益率', '维修费用率'],
  nameList: ['2019', '2020', '2021', '2022', '2023'],
  profitRate: [8, 10, 9, 15, 9],
  investmentIncomeRate: [11, 14, 14, 18, 12],
  maintenanceCostRate: [12, 16, 14, 17, 13]
}
export const mockInspectData = {
  yCates: ['20:00-22:00', '16:00-18:00', '12:00-14:00', '08:00-10:00', '04:00-06:00', '00:00-02:00'],
  xCates: ['2022-08-08', '2022-06-06', '2022-05-14', '2022-03-22', '2022-02-13'],
  data: [
    [0, 0, 10],
    [0, 1, 58],
    [0, 2, 123],
    [0, 3, 58],
    [0, 4, 92],
    [1, 0, 19],
    [1, 1, 78],
    [1, 2, 64],
    [1, 3, 78],
    [1, 4, 58],
    [2, 0, 8],
    [2, 1, 117],
    [2, 2, 52],
    [2, 3, 117],
    [2, 4, 78],
    [3, 0, 24],
    [3, 1, 48],
    [3, 2, 72],
    [3, 3, 48],
    [3, 4, 117],
    [4, 0, 67],
    [4, 1, 35],
    [4, 2, 132],
    [4, 3, 35],
    [4, 4, 48],
    [5, 0, 92],
    [5, 1, 15],
    [5, 2, 114],
    [5, 3, 15],
    [5, 4, 35]
  ]
}
export const mockPeopleNumTrendData = {
  legendList: ['检查人次', '检查阳性人次', '阳性率'],
  nameList: ['2019', '2020', '2021', '2022', '2023'],
  checkNum: [121, 142, 161, 142, 20],
  checkPositiveNum: [12, 52, 120, 1, 0],
  positiveRate: [10, 36, 80, 1, 0]
}
export const mockUsageData = {
  useDays: 375,
  faultDays: 15
}
export const mockUsefulLifeData = {
  usedLife: 5,
  usefulLife: 10
}
