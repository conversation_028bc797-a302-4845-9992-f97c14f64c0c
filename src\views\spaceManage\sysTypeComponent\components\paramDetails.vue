<template>
  <div class="main">
    <el-dialog v-dialogDrag :modal="false" :visible.sync="dialogShow" custom-class="paramDetailsDialog main" append-to-body :close-on-click-modal="false" :before-close="closeDialog">
      <template slot="title">
        <span class="dialog-title">历史记录</span>
      </template>
      <div class="dialog-content">
        <div class="search-box">
          <div style="display: flex; align-items: center;">
            <el-select v-model="searchForm.deviceId" filterable placeholder="请选择物联设备" @change="changeDevice">
              <el-option v-for="item in deviceList" :key="item.factoryCode" :label="item.assetsName" :value="item.factoryCode"></el-option>
            </el-select>
            <el-select v-model="searchForm.metadataTag" :disabled="!searchForm.deviceId" filterable placeholder="请选择监测参数" @change="init">
              <el-option v-for="item in monitorParamsList" :key="item.metadataTag" :label="item.metadataName" :value="item.metadataTag"></el-option>
            </el-select>
            <el-date-picker
              v-model="dateRange"
              type="daterange"
              unlink-panels
              popper-class="date-style"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              value-format="yyyy-MM-dd"
              @change="changeDataEvent"
            >
            </el-date-picker>
            <div class="date-type">
              <p v-for="item in dateTypeList" :key="item.value" class="type-item" :class="{'active-type': dateType == item.value}" @click="activeTabEvent(item.value)">{{ item.name }}</p>
            </div>
          </div>
          <el-dropdown trigger="click" @command="tagChange">
            <span class="el-dropdown-link"> {{ tagCurrent == 1 ? '图表' : '列表'}} <i class="el-icon-arrow-down"></i> </span>
            <el-dropdown-menu slot="dropdown">
              <el-dropdown-item command="1" :class="{ isBjxl: tagCurrent == 1 }">图表</el-dropdown-item>
              <el-dropdown-item command="2" :class="{ isBjxl: tagCurrent == 2 }">列表</el-dropdown-item>
            </el-dropdown-menu>
          </el-dropdown>
        </div>
        <div v-show="tagCurrent == 1" id="trend_chart" class="chart-main"></div>
        <div v-show="tagCurrent == 2" class="table-main">
          <el-table
            v-loading="tableLoading"
            :data="tableData"
            height="100%"
            :cell-style="$tools.setCell(3)"
            :header-cell-style="$tools.setHeaderCell(3)"
            style="width: 100%"
            element-loading-background="rgba(0, 0, 0, 0.2)"
          >
            <el-table-column type="index" label="序号" width="100" align="center"></el-table-column>
            <el-table-column prop="timestamp" label="日期时间" width="180"></el-table-column>
            <el-table-column prop="assetsName" label="物联设备"  width="240"></el-table-column>
            <el-table-column prop="propertiesText" label="监测参数"></el-table-column>
          </el-table>
        </div>
        <el-pagination
          v-show="tagCurrent == 2"
          :current-page="currentPage"
          :page-sizes="[15, 20, 30, 40]"
          :page-size="pageSize"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
          class="pagination"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        ></el-pagination>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { GetAssetDeviceList, GetDeviceParamsList, GetMonitoringHistoryDataOld, GetMonitoringHistoryDataNew, GetMonitoringHistoryList} from '@/utils/spaceManage'
import moment from 'moment'
moment.locale('zh-cn')
import * as echarts from 'echarts'
export default {
  name: 'paramDetails',
  props: {
    dialogShow: {
      type: Boolean,
      default: false
    },
    paramData: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      searchForm: {
        deviceId: '', // 设备id
        metadataTag: '' // 物模型tag
      },
      tagCurrent: 1,
      dateRange: [moment().format('YYYY-MM-DD'), moment().format('YYYY-MM-DD')],
      dateType: 'day',
      deviceList: [],
      monitorParamsList: [],
      dateTypeList: [
        { name: '今天', value: 'day' },
        { name: '本周', value: 'week' },
        { name: '本月', value: 'month' },
        { name: '本年', value: 'year' }
      ],
      tableLoading: false,
      tableData: [],
      currentPage: 1,
      pageSize: 15,
      total: 0
    }
  },
  created() {
    this.getAssetDeviceList()
  },
  methods: {
    init() {
      if (this.tagCurrent == 1) {
        this.getMonitoringHistoryData()
      } else {
        this.getMonitoringHistoryList()
      }
    },
    // 获取设备参数列表
    getMonitoringHistoryList() {
      let params = {
        id: this.searchForm.deviceId,
        property: this.searchForm.metadataTag,
        belongAssetsName: '',
        jsonObject: {
          pageSize: this.pageSize,
          pageIndex: this.currentPage - 1,
          terms: [
            {
              column: 'timestamp$BTW',
              value: [moment(this.dateRange[0] + ' 00:00:00').valueOf(), moment(this.dateRange[1] + ' 23:59:59').valueOf()]
            }
          ]
        }
      }
      this.tableLoading = true
      GetMonitoringHistoryList(params).then(res => {
        this.tableLoading = false
        if (res.data.code == 200) {
          res.data.data.data.forEach(v => {
            v.timestamp = moment(v.timestamp).format('YYYY-MM-DD HH:mm:ss')
          })
          this.tableData = res.data.data.data
          this.total = res.data.data.total
        }
      }).catch(() => {
        this.tableLoading = false
      })
    },
    tagChange(val) {
      this.tagCurrent = val
      if (this.tagCurrent == 1) {
        this.searchForm.metadataTag = this.monitorParamsList[0].metadataTag
      } else {
        this.searchForm.metadataTag = ''
      }
      this.init()
    },
    // 图表接口请求
    getMonitoringHistoryData() {
      let isOld = __PATH.VUE_APP_HOSPITAL_NODE != 'sjydkqyy'
      let obj = {
        day: 1,
        week: 2,
        month: 3,
        year: 4
      }
      let paramsNew = {
        monitorDeviceId: this.paramData.id,
        deviceId: this.searchForm.deviceId,
        startTime: this.dateRange[0] + ' 00:00:00',
        endTime: this.dateRange[1] + ' 23:59:59',
        dateType: this.dateType,
        metadataTag: this.searchForm.metadataTag,
        product: this.monitorParamsList.find(v => v.metadataTag == this.searchForm.metadataTag).product
      }
      let paramsOld = {
        deviceId: this.searchForm.deviceId,
        startTime: this.dateRange[0],
        endTime: this.dateRange[1],
        timeType: obj[this.dateType],
        metadataTag: this.searchForm.metadataTag,
        productId: this.monitorParamsList.find(v => v.metadataTag == this.searchForm.metadataTag).product
      }
      console.log('isOld', isOld)
      const fetchData = isOld ? GetMonitoringHistoryDataOld : GetMonitoringHistoryDataNew
      fetchData(isOld ? paramsOld : paramsNew).then(res => {
        if (res.data.code == 200) {
          this.chartOptions(isOld ? res.data.data : res.data.result)
        } else {
          this.chartOptions({})
        }
      }).catch((res) => {
        this.chartOptions({})
      })
    },
    changeDevice(data) {
      if (!data) return
      this.getDeviceParamsList(data)
    },
    // 获取设备列表
    getAssetDeviceList() {
      GetAssetDeviceList(this.paramData.id).then(res => {
        if (res.data.code == 200) {
          this.searchForm.deviceId = res.data.data[0].factoryCode
          this.deviceList = res.data.data
          this.getDeviceParamsList(res.data.data[0].factoryCode)
        }
      })
    },
    // 获取检测参数列表
    getDeviceParamsList(factoryCode) {
      let params = {
        id: this.paramData.id,
        params: {
          factoryCode
        }
      }
      GetDeviceParamsList(params).then(res => {
        if (res.data.code == 200) {
          this.monitorParamsList = res.data.data
          this.searchForm.metadataTag = res.data.data[0].metadataTag
          this.getMonitoringHistoryData()
        }
      })
    },
    chartOptions(data) {
      let getchart = echarts.init(document.getElementById('trend_chart'))
      let option
      let xAxis = data.xAxis ? data.xAxis : data.xaxis
      if (xAxis && xAxis.data.length) {
        let seriesData = []
        let color = ['#FA403C', '#00BC6D', '#3562DB']
        data.series.forEach((item, index) => {
          if (item.data.length) {
            seriesData.push({
              name: item.name,
              type: item.type,
              data: item.data,
              itemStyle: {
                normal: {
                  borderWidth: 5,
                  color: color[index] || this.$tools.randomRgbColor()
                }
              }
            })
          }
        })
        option = {
          color: ['#73A0FA', '#73DEB3', '#FFB761'],
          tooltip: {
            trigger: data.tooltip?.trigger || 'axis',
            axisPointer: {
              type: 'cross',
              crossStyle: {
                color: '#999'
              },
              lineStyle: {
                type: 'dashed'
              }
            },
            formatter: function (params) {
              let returnStr = params[0].name + '<br>'
              params.forEach(item => {
                // returnStr += item.seriesName + ': ' + (item.data.value || '-') + ' ' + (item.data.unit || '') + '<br>'
                returnStr += item.seriesName + ': ' + (item.data || '-') + '<br>'
              })
              return returnStr
            }
          },
          dataZoom: [
            {
              type: 'inside',
              start: 0,
              end: 100
            }
          ],
          grid: {
            left: '20',
            right: '70',
            bottom: '25',
            top: '20',
            containLabel: true
          },
          xAxis: {
            // type: 'time',
            type: xAxis.type || 'category',
            data: xAxis.data,
            boundaryGap: false,
            splitLine: {
              show: false
            },
            axisTick: {
              show: false
            },
            axisLine: {
              show: false
            }
          },
          yAxis: {
            type: 'value',
            axisLabel: {
              color: 'rgba(230,247,255,0.5)',
              textStyle: {
                fontSize: 12
              }
            },
            splitLine: {
              show: true,
              lineStyle: {
                type: 'dashed',
                color: 'rgba(230,247,255,0.2)'
              }
            },
            axisTick: {
              show: false
            },
            axisLine: {
              show: false
            }
          },
          series: seriesData
        }
      } else {
        option = {
          title: {
            text: '暂无数据',
            x: 'center',
            y: 'center',
            textStyle: {
              fontSize: 14,
              fontWeight: 'normal',
              color: '#999'
            }
          }
        }
      }
      getchart.clear()
      getchart.setOption(option)
      // 随着屏幕大小调节图表
      window.addEventListener('resize', () => {
        getchart.resize()
      })
    },
    activeTabEvent(val) {
      const dateList = {
        day: [moment().format('YYYY-MM-DD'), moment().format('YYYY-MM-DD')],
        week: [moment().startOf('isoWeek').format('YYYY-MM-DD'), moment().endOf('isoWeek').format('YYYY-MM-DD')],
        month: [moment().startOf('month').format('YYYY-MM-DD'), moment().endOf('month').format('YYYY-MM-DD')],
        year: [moment().startOf('year').format('YYYY-MM-DD'), moment().endOf('year').format('YYYY-MM-DD')]
        // 1: [moment().startOf('day').format('YYYY-MM-DD'), moment().format('YYYY-MM-DD')],
        // 2: [moment().subtract(6, 'days').format('YYYY-MM-DD'), moment().format('YYYY-MM-DD')],
        // 3: [moment().subtract(29, 'days').format('YYYY-MM-DD'), moment().format('YYYY-MM-DD')],
        // 4: [moment().startOf('year').format('YYYY-MM-DD'), moment().endOf('year').format('YYYY-MM-DD')]
      }
      this.dateType = val
      this.dateRange = dateList[val]
      this.init()
    },
    changeDataEvent() {
      this.dateType = 'custom'
      this.init()
    },
    // 取消按钮
    closeDialog() {
      this.$emit('paramDetailsClose', false)
    },
    handleSizeChange(val) {
      this.pageSize = val
      this.getMonitoringHistoryList()
    },
    handleCurrentChange(val) {
      this.currentPage = val
      this.getMonitoringHistoryList()
    }
  }
}
</script>

<style lang="scss" scoped>
::v-deep(.paramDetailsDialog) {
  width: 80%;
  height: 88vh;
  margin-top: 6vh !important;
  // border: 1px solid #5996f9;
  background-color: transparent !important;
  pointer-events: auto;
  background-image: url('@/assets/images/big_dialog_bg.png');
  background-repeat: no-repeat;
  background-size: 100% 100%;
  box-shadow: none;
  .el-dialog__headerbtn {
    transform: translateX(-36px);
    width: 25px;
    height: 25px;
    background-image: url('@/assets/images/close.png');
    background-repeat: no-repeat;
    background-size: 100% 100%;
    .el-dialog__close::before {
      display: none;
    }
  }
  .dialog-title {
    margin-top: 5px;
    display: inline-block;
    width: 100%;
    text-align: center;
    transform: translateY(-6px);
    color: #cbdeed;
  }
  .dialog-title::before {
    display: none;
  }
  .el-dialog__header {
    background-color: transparent;
  }
  .el-dialog__body {
    height: calc(100% - 60px);
    max-height: fit-content;
    overflow-y: hidden;
    background-color: transparent;
    padding: 0px 80px;
  }
  .dialog-content {
    width: 100%;
    height: 88%;
    margin-top: 20px;
    background: rgba(53,98,219,0.06);
    display: flex;
    flex-direction: column;
    padding: 16px 16px 0px 16px;
    .search-box {
      padding: 0px 0px 10px 0px;
      display: flex;
      align-items: center;
      justify-content: space-between;
      .date-type {
        margin-left: 10px;
        display: flex;
        .type-item {
          cursor: pointer;
          padding: 8px 12px;
          font-weight: 400;
          font-size: 14px;
          color: #B0E3FA;
          margin-right: 8px;
          border: 1px solid transparent;
          background: linear-gradient( 90deg, rgba(10,132,255,0) 0%, rgba(10,132,255,0) 100%), rgba(255,255,255,0.1);
        }
        .active-type {
          color: #8BDDF5;
          background: linear-gradient( 90deg, rgba(10,132,255,0.4) 0%, rgba(10,132,255,0) 100%), rgba(255,255,255,0.1);
          border: 1px solid;
          border-image: radial-gradient(circle, rgba(171, 240, 255, 1), rgba(226, 254, 255, 1), rgba(132, 196, 203, 1), rgba(48, 151, 166, 0)) 1 1;
        }
      }
      .el-select {
        margin-right: 10px;
        .el-input .el-input__inner {
          background-color: #14233e;
          border-radius: 4px;
          border-color: rgba(133, 145, 206, 0.5);
          color: rgba(255, 255, 255, 0.8);
          width: 150px;
          height: 35px;
        }
        .el-input__icon {
          line-height: 35px;
        }
      }
      .el-dropdown-link {
        cursor: pointer;
        font-size: 16px;
        font-weight: 300;
        color: #ffffff;
        line-height: 16px;
        .el-icon-arrow-down {
          font-size: 14px;
          margin-left: 4px;
        }
      }
    }
    .chart-main {
      flex: 1;
    }
    .table-main {
      flex: 1;
      overflow: hidden;
    }
    .el-date-editor {
      width: 300px;
      height: 35px;
      background-color: #14233e;
      border-color: rgba(133, 145, 206, 0.5);
      .el-input__icon {
        transform: translateY(-2px);
      }
      .el-range-input {
        background-color: #14233e;
        color: rgba(255, 255, 255, 0.8);
      }
      .el-range-separator {
        color: rgba(255, 255, 255, 0.8);
        transform: translateY(-2px);
      }
    }
  }
}
</style>
