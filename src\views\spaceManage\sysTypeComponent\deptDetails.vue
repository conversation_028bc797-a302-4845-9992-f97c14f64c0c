<template>
  <div class="deptDetails">
    <div class="module-container" style="height: calc(45%); background: rgba(133,145,206,0.15);margin-bottom: 16px;">
      <div class="info-header clear">
        <p class="info-header-text fl">基础信息</p>
      </div>
      <div class="module-content info-list" style="height: calc(100% - 44px)">
        <div v-for="item in infoList" :key="item.label" class="info-list-item">
          <p class="item-label">{{ item.label }}：</p>
          <p class="item-value">{{ detailsInfo[item.key] || '---' }}</p>
        </div>
      </div>
    </div>
    <div class="module-container" style="height: calc(55%)">
      <div class="module-header">
        <div class="title-left">
          <p class="title-left-text showFloorName">椅位列表</p>
        </div>
      </div>
      <div class="module-content" style="height: calc(100% - 44px)">
        <el-table
          v-el-table-infinite-scroll="tableLoadMore"
          v-scrollHideTooltip
          v-loading="tableLoading"
          class="table-center-transfer"
          :data="tableList"
          height="100%"
          :cell-style="{ padding: ' 8px', backgroundColor: 'transparent', border: 'none', padding: '3px' }"
          :header-cell-style="{ background: '#8591CE26!important', color: '#8BDDF5FF', padding: '4px 8px', fontWeight: 'bold' }"
          style="width: 100%" element-loading-background="rgba(0, 0, 0, 0.2)"
        >
          <el-table-column prop="assetsName" label="椅位名称" show-overflow-tooltip/>
          <el-table-column prop="relevantDeptName" label="所在科室" show-overflow-tooltip/>
          <el-table-column prop="spaceName" label="所属位置" show-overflow-tooltip>
            <template slot-scope="scope">
              {{ scope.row?.spaceName?.split('>').reverse().join(' > ')}}
            </template>
          </el-table-column>
          <el-table-column prop="chairStatus" label="状态" width="70" show-overflow-tooltip/>
        </el-table>
      </div>
    </div>
  </div>
</template>

<script>
import { GetDeptDetails, GetMonitoringItemsList } from '@/utils/spaceManage'
export default {
  name: 'deptDetails',
  props: {
    roomData: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      infoList: [
        {label: '科室名称', key: 'deptName'},
        {label: '归属单位', key: 'unitName'},
        {label: '上级科室', key: 'pidName'},
        {label: '科室性质', key: 'natureName'},
        {label: '科室电话', key: 'deptPhone'},
        {label: '科室负责人', key: 'principalName'},
        {label: '负责人电话', key: 'principalPhone'},
        {label: '科室联系人', key: 'departLiaison'},
        {label: '联系人电话', key: 'departLiaisonPhone'}
      ],
      detailsInfo: {},
      tableList: [],
      tableLoading: false,
      pagination: {
        total: 0,
        pageSize: 15,
        pageNo: 1
      }
    }
  },
  computed: {

  },
  created() {
    console.log(1111, this.roomData)
    this.getDeptDetails()
    this.getMonitoringItemsList()
  },
  methods: {
    // 获取椅位列表
    getMonitoringItemsList() {
      let params = {
        equipAttr: 2,
        page: this.pagination.pageNo,
        pageSize: this.pagination.pageSize,
        sysOfCode: this.roomData.projectCode,
        sysOf1Code: this.roomData.categoryCode,
        relevantDept: this.roomData.deviceId,
        spaceId: this.roomData.ssmCodes?.split(',')?.at(-1)
      }
      this.tableLoading = true
      GetMonitoringItemsList(params).then((res) => {
        if (res.data.code == 200) {
          if (this.pagination.pageNo == 1) this.tableList = []
          res.data.data.records.forEach(item => {
            if (item.iotPropertyList.length) {
              item.chairStatus = item.iotPropertyList.find(v => v.metadataTag == 'operatingStatus')?.valueText ?? '-'
            }
          })
          this.tableList = this.tableList.concat(res.data.data.records)
          this.pagination.total = res.data.data.total
        }
      }).finally(() => {
        this.tableLoading = false
      })
    },
    getDeptDetails() {
      GetDeptDetails({id: this.roomData.deviceId}).then(res => {
        if (res.data.code == 200) {
          this.detailsInfo = res.data.data
        }
      })
    },
    tableLoadMore() {
      if (this.pagination.total > this.pagination.pageNo * this.pagination.pageSize) {
        this.pagination.pageNo++
        this.getMonitoringItemsList()
      }
    }
  }
}

</script>

<style lang="scss" scoped>
@import '../style/module.scss';
.deptDetails {
  padding: 5px 0px 0px 0px;
  .module-header {
    padding-left: 30px;
    padding-right: 10px;
    width: 100%;
    background: url("@/assets/images/qhdsys/bg-bt.png") no-repeat;
    background-size: contain;
  }
  .info-header {
    padding: 6px 8px;
    line-height: 22px;
    background: rgba(133,145,206,0.15);
    .info-header-text {
      font-weight: 500;
      font-size: 15px;
      color: #FFFFFF;
    }
  }
  .info-list {
    padding: 0px 16px;
    .info-list-item {
      display: flex;
      .item-label {
        font-weight: 400;
        font-size: 14px;
        color: #B0E3FA;
        line-height: 30px;
        width: 85px;
      }
      .item-value {
        flex: 1;
        font-weight: 400;
        font-size: 14px;
        color: #FFFFFF;
        line-height: 30px;
      }
    }
  }
  .title-right {
    font-size: 14px;
    align-items: center;
  }
  .el-dropdown-link {
    font-size: 14px;
    font-weight: 300;
    color: #ffffff;
    line-height: 16px;
    .el-icon-arrow-down {
      font-size: 12px;
    }
  }
  .viewMore {
    display: inline-block;
    width: 24px;
    height: 24px;
    margin-left: 10px;
    background: url('@/assets/images/qhdsys/bg-gd.png') no-repeat center / 100% 100%;
  }
}
</style>
