<template>
  <div class="task-container">
    <div class="task-container-header-box">
      <div v-scrollMove class="box-type">
        <el-badge v-for="(item, index) in roomTypeFilterList" :key="index"
                  :hidden="item.badgeValue < 1 ? true : item.badgeHidden" :value="item.badgeValue" :max="99">
          <span :id="item.Dom" class="tags-item" :class="{ type_active: activeType === item.Dom }"
                @click="activeTypeEvent(item.Dom)">{{ item.name }}</span>
        </el-badge>
      </div>
    </div>
    <div class="task-container-content-box">
      <div class="sys-box">
        <component :is="currentComponent" :ref="currentComponent" :roomData="roomData" :assetsList="assetsList"
                   type="device" :deviceId="deviceIds" class="sys-box-content" @roomEvent="roomEvent" @childControl="childControl"
                   @sendWpfData="setWPFdeviceIdAndTab"></component>
      </div>
    </div>
  </div>
</template>
<script>
export default {
  name: 'taskManage',
  components: {
    overviewComponent: () => import('@/views/taskManageModule/overviewComponent.vue'),
    inspectionComponent: () => import('@/views/taskManageModule/inspectionComponent.vue')
  },
  props: {
    roomData: {
      type: Object,
      default: () => {}
    },
    assetsList: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      roomTypeList: [
        {
          Dom: 'overview',
          name: '概览',
          component: 'overviewComponent',
          index: 0,
          badgeHidden: true,
          badgeValue: 0,
          is: ['ElevatorSystem']
        },
        {
          Dom: 'inspection',
          name: '巡检',
          component: 'inspectionComponent',
          index: 3,
          badgeHidden: true,
          badgeValue: 0,
          is: ['ElevatorSystem']
        }
      ],
      activeType: 'overview',
      deviceIds: ''
    }
  },
  computed: {
    currentComponent() {
      return (
        this.roomTypeList.find((e) => e.Dom === this.activeType)?.component ||
        ''
      )
    },
    roomTypeFilterList() {
      return this.roomTypeList.filter(
        (e) => !e.is.includes(this.activeIemcTab)
      )
    }
  },
  mounted() {
    this.activeTypeEvent('overview')
  },
  methods: {
    activeTypeEvent(type) {
      this.activeType = type
      try {
        window.chrome.webview.hostObjects.sync.bridge.SpaceThreeLevelMenuSwitch(
          this.activeType)
      } catch (error) { }
    },
    childControl(data) {
      this.$emit('childControl', data)
    },
    roomEvent(data) {
      this.$emit('roomEvent', data)
    },
    setWPFdeviceIdAndTab() { }
  }
}
</script>
  <style lang="scss" scoped>
.task-container {
  height: 100%;
}
.task-container-header-box {
  .box-type {
    width: calc(100% - 8px);
    padding-top: 13px;
    display: flex;
    overflow: hidden;
    ::v-deep .el-badge {
      flex-shrink: 0;
      .el-badge__content {
        z-index: 1;
        background: #ff2d55;
        border: none;
      }
    }
    .tags-item {
      cursor: pointer;
      height: 26px;
      line-height: 26px;
      flex-shrink: 0;
      margin-left: 8px;
      padding: 0px 12px;
      background: rgba(255, 255, 255, 0.1)
        linear-gradient(
          90deg,
          rgba(10, 132, 255, 0) 0%,
          rgba(10, 132, 255, 0) 100%
        );
      font-size: 14px;
      font-weight: 400;
      color: #b0e3fa;
      // line-height: 14px;
      border: 1px solid transparent;
    }
    .type_active {
      background: rgba(255, 255, 255, 0.1)
        linear-gradient(
          90deg,
          rgba(10, 132, 255, 0.4) 0%,
          rgba(10, 132, 255, 0) 100%
        );
      border: 1px solid;
      border-image: radial-gradient(
          circle,
          rgba(171, 240, 255, 1),
          rgba(226, 254, 255, 1),
          rgba(132, 196, 203, 1),
          rgba(48, 151, 166, 0)
        )
        1 1;
    }
  }
}
.task-container-content-box {
  height: calc(100% - 40px);
  .sys-box {
    height: 100%;
    .sys-box-content {
      width: 100%;
      height: 100%;
      padding: 5px 0px 0 0px;
      box-sizing: border-box;
    }
  }
}
</style>
