<template>
  <div class="energy">
    <!-- <div style="color: red;word-wrap:break-word;">{{ wpfData }}</div> -->
    <div class="energy-main">
      <div v-show="spaceLevel == 4" class="main-title">
        <p class="main-title-text">{{ monitorData.emodelName ? monitorData.emodelName : spaceName }}</p>
        <el-dropdown trigger="click" @command="(val) => (activeType = val)">
          <span class="el-dropdown-link"> {{ typeList.find((v) => v.value == activeType)?.name ?? '' }} <i class="el-icon-arrow-down"></i> </span>
          <el-dropdown-menu slot="dropdown">
            <el-dropdown-item v-for="item in typeList" :key="item.value" :command="item.value" :class="{ isBjxl: activeType == item.value }">{{ item.name }}</el-dropdown-item>
          </el-dropdown-menu>
        </el-dropdown>
      </div>
      <div class="main-search">
        <div class="search-tabs">
          <div
            v-for="(v, i) in menuList"
            :key="i"
            class="tabs-item"
            :style="{ width: 'calc(100% / ' + menuList.length + ')' }"
            :class="{ 'tabs-active': menuActive === i }"
            @click="activeEnergyEvent(i)"
          >
            {{ v.name }}
          </div>
        </div>
        <div v-if="tabChildren.length" class="search-tags">
          <div v-for="(v, i) in tabChildren" :key="i" class="tags-item" :class="{ 'tags-active': menuTitleActive === i }" @click="selectMenuTitle(i)">
            {{ filterName(v.name) }}
          </div>
        </div>
        <div class="search-data">
          <el-dropdown trigger="click" @command="dataTypeCommand">
            <span class="el-dropdown-link"> {{ dataTypeList.find((v) => v.value == statisticalType)?.name ?? '' }} <i class="el-icon-caret-bottom"></i> </span>
            <el-dropdown-menu slot="dropdown">
              <el-dropdown-item v-for="item in dataTypeList" :key="item.value" :command="item.value" :class="{ isBjxl: statisticalType == item.value }">{{ item.name }}</el-dropdown-item>
            </el-dropdown-menu>
          </el-dropdown>
          <el-date-picker
            v-model="statisticalDate"
            class="datePickerInput"
            popper-class="date-style"
            type="daterange"
            value-format="yyyy-MM-dd"
            :disabled="statisticalType != 5"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            @change="setWPFdata"
            @focus="setWPFBgShow()"
            @blur="setWPFBgHide()"
          >
          </el-date-picker>
        </div>
      </div>
      <div class="main-monitor">
        <div class="monitor-head">
          <img class="head-icon" :src="icons[energyId] || dianIcon" />
          <div class="head-info">
            <p class="head-info-name">{{ monitorData.emodelName ? monitorData.emodelName : spaceName }}</p>
            <p class="head-info-value">
              <span class="head-info-value-num">{{ total ? filterValue(total) : monitorData.value }}</span>
              <!-- <span class="head-info-value-num">{{ monitorData.value }}</span> -->
              <span class="head-info-value-unit">{{ unitItems[energyId] }}</span>
            </p>
          </div>
        </div>
        <div class="monitor-ratio">
          <div class="ratio-item">
            <p class="ratio-item-value">
              <span>{{ monitorData.yoyValue || '-' }}</span>
            </p>
            <p class="ratio-item-name">去年同期</p>
          </div>
          <div class="ratio-item">
            <p class="ratio-item-value">
              <i
                :class="monitorData.yoyValueRatio > 0 ? 'el-icon-top-right' : 'el-icon-bottom-left'"
                :style="{ color: monitorData.yoyValueRatio > 0 ? '#FF2D55' : '#61E29D', background: monitorData.yoyValueRatio > 0 ? 'rgba(255, 45, 85, .2)' : 'rgba(97, 226, 157, .2)' }"
              ></i>
              <span :style="{ color: monitorData.yoyValueRatio > 0 ? '#FF2D55' : '#61E29D' }">{{ monitorData.yoyValueRatio || '-' }}%</span>
            </p>
            <p class="ratio-item-name">同比</p>
          </div>
          <div class="ratio-item">
            <p class="ratio-item-value">
              <i
                :class="monitorData.momValueRatio > 0 ? 'el-icon-top-right' : 'el-icon-bottom-left'"
                :style="{ color: monitorData.momValueRatio > 0 ? '#FF2D55' : '#61E29D', background: monitorData.momValueRatio > 0 ? 'rgba(255, 45, 85, .2)' : 'rgba(97, 226, 157, .2)' }"
              ></i>
              <span :style="{ color: monitorData.momValueRatio > 0 ? '#FF2D55' : '#61E29D' }">{{ monitorData.momValueRatio || '-' }}%</span>
            </p>
            <p class="ratio-item-name">环比</p>
          </div>
          <div class="ratio-item">
            <p class="ratio-item-value">
              <span>{{ monitorData.momValue || '-' }}</span>
            </p>
            <p class="ratio-item-name">能耗定额</p>
          </div>
        </div>
      </div>
      <div class="main-module" style="height: 27%">
        <div class="main-module-title">用能类型分析</div>
        <div class="main-module-content main-analysis" :style="{ justifyContent: typeAnalysisList.length ? '' : 'center' }">
          <div id="analysis_chart" class="analysis-content-chart"></div>
          <div v-show="typeAnalysisList.length" class="analysis-content-list">
            <div v-for="item in typeAnalysisList" :key="item.name" class="list-item">
              <span class="list-item-color" :style="{ background: item.color }"></span>
              <p class="list-item-name">{{ filterName(item.name) }}</p>
              <p class="list-item-value">{{ filterValue(item.value) + item.unit }}</p>
            </div>
          </div>
        </div>
      </div>
      <div class="main-module" :style="{ height: tabChildren.length ? (spaceLevel == 4 ? 'calc(26% - 40px)' : '26%') : spaceLevel == 4 ? 'calc(26% + 12px)' : 'calc(26% + 52px)' }">
        <div class="main-module-title">用能排行</div>
        <div v-show="areaRankingList.length" class="main-module-content main-ranking">
          <div class="main-ranking-color"></div>
          <div class="main-ranking-list">
            <div v-for="(item, index) in areaRankingList" :key="item.emodelName" class="list-item">
              <p class="list-item-name">{{ item.emodelName }}</p>
              <p class="list-item-line">
                <span :style="{ width: item.ratio + '%', background: rankingColor[index] ? rankingColor[index] : '#272937' }"></span>
              </p>
              <p class="list-item-value">{{ filterValue(item.value) + item.energyUnitEn + ' ' + filterValue(item.ratio, 'toFixed') + '%' }}</p>
            </div>
          </div>
        </div>
        <div v-show="!areaRankingList.length" id="ranking_chart" class="main-module-content main-ranking">
          <p class="nodeData">暂无数据</p>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import { KgceLogin, GetEnergyAndClassifyTree, GetModelEnergyDataList } from '@/utils/energyConsumption'
import dianIcon from '@/assets/images/qhdsys/monitor_head_dian.png'
import shuiIcon from '@/assets/images/qhdsys/monitor_head_shui.png'
import reIcon from '@/assets/images/qhdsys/monitor_head_re.png'
import qiIcon from '@/assets/images/qhdsys/monitor_head_qi.png'
import moment from 'moment'
import * as echarts from 'echarts'
moment.locale('zh-cn')
// import 'echarts-gl'
export default {
  name: 'energyConsumption',
  components: {},
  props: {
    roomData: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      dianIcon,
      // wpfData: 'asda',
      total: '',
      activeType: 1,
      typeList: [
        { value: 1, name: '按区域' }
        // { value: 2, name: '按科室' }
      ],
      energyId: '', // 选中一级能耗id
      menuActive: 0, // 一级用能类型
      menuTitleActive: 0, // 二级用能类型
      showContentType: '', // 用能类型名称
      menuList: [], // 一级用能类型列表
      tabChildren: [], // 二级用能类型列表
      emodelId: '', // 模型id
      emodelIdAll: '', // 模型id
      // activeEnergyUnit: '', // 当前选中菜单对应的耗能单位
      unitItems: {
        SU035: 'kWh', // 电
        CM020: 't', // 水
        '4c78a70e0b7deb51b0a227fb2cd9196f': 'GJ', // 热
        '66655f36bc0dc86adbaf2cab80d9dce2': 'm³' // 气
      }, // 能量单位
      kgceToken: '', // 能耗用户token
      // spaceLevel: '4', // 空间等级：1医院，2区域，3建筑，4楼层，5房间
      spaceLevel: '1', // 空间等级：1医院，2区域，3建筑，4楼层，5房间
      statisticalType: 3, // 选中日期类型
      statisticalDate: [moment().startOf('month').format('YYYY-MM-DD') + ' 00:00:00', moment().endOf('month').format('YYYY-MM-DD') + ' 23:59:59'], // 选中日期
      dataTypeList: [
        { value: 1, name: '今日' },
        { value: 2, name: '本周' },
        { value: 3, name: '本月' },
        { value: 4, name: '本年' },
        { value: 5, name: '自定义' }
      ],
      icons: {
        SU035: dianIcon,
        CM020: shuiIcon,
        '4c78a70e0b7deb51b0a227fb2cd9196f': reIcon,
        '66655f36bc0dc86adbaf2cab80d9dce2': qiIcon
      },
      monitorData: {}, // 实时监测数据
      typeAnalysisList: [], // 类型分析数据
      areaRankingList: [], // 排行列表
      rankingColor: ['#FFCA64', '#DDB15D', '#B39355', '#88734C', '#625644', '#3C3A3C', '#272937'],
      spaceName: ''
    }
  },
  computed: {
    filterValue() {
      return (value, type) => {
        // return value ? value.toFixed(2) : 0
        value = value && !isNaN(Number(value)) ? Number(value) : 0
        if (type == 'toFixed') {
          return value.toFixed(2)
        } else {
          if (value > 1000) {
            return (value / 10000).toFixed(2) + '万'
          } else {
            return value.toFixed(2)
          }
        }
      }
    },
    filterName() {
      return (name) => {
        name = name.replace(/用水/g, '').replace(/用电/g, '')
        return name
      }
    }
  },
  watch: {
    // 监听模型层级数据变化
    roomData: {
      handler(newVal, oldVal) {
        if (newVal.areaData) {
          // this.wpfData = this.roomData.areaData
          const data = JSON.parse(newVal.areaData)
          this.emodelId = data.parentId
          this.emodelIdAll = data.childList.join(',')
          this.spaceLevel = data.ssmType
          this.spaceName = data.ssmName
          if (this.spaceLevel == 4) {
            // this.activeType = 2
          } else {
            this.activeType = 1
          }
        }
        this.setWPFdata()
      },
      deep: true
    }
  },
  mounted() {
    if (this.roomData.areaData && this.roomData.areaData.length) {
      const data = JSON.parse(this.roomData.areaData)
      this.emodelId = data.parentId
      this.emodelIdAll = data.childList.join(',')
      this.spaceLevel = data.ssmType
      this.spaceName = data.ssmName
      if (this.spaceLevel == 4) {
        // this.activeType = 2
      } else {
        this.activeType = 1
      }
    }
    this.$nextTick(() => {
      this.kgceLogin()
    })
  },
  methods: {
    setWPFBgShow() {
      window.chrome.webview.hostObjects.sync.bridge.DateMask(true)
    },
    setWPFBgHide() {
      window.chrome.webview.hostObjects.sync.bridge.DateMask(false)
    },
    // 时间类型切换
    dataTypeCommand(val) {
      const date = {
        1: [moment().format('YYYY-MM-DD') + ' 00:00:00', moment().format('YYYY-MM-DD') + ' 23:59:59'],
        2: [moment().startOf('isoWeek').format('YYYY-MM-DD') + ' 00:00:00', moment().endOf('isoWeek').format('YYYY-MM-DD') + ' 23:59:59'],
        3: [moment().startOf('month').format('YYYY-MM-DD') + ' 00:00:00', moment().endOf('month').format('YYYY-MM-DD') + ' 23:59:59'],
        4: [moment().startOf('year').format('YYYY-MM-DD') + ' 00:00:00', moment().endOf('year').format('YYYY-MM-DD') + ' 23:59:59'],
        5: []
      }
      this.statisticalDate = date[val]
      this.statisticalType = val
      if (val != 5) {
        this.setWPFdata()
      }
    },
    // 区域排行
    regionalRanking() {
      // if (this.spaceLevel == 4 && this.activeType == 2) {
      //   const obj = {
      //     '1574997197731147778': {
      //       电力: [
      //         { emodelName: '泌尿外科', value: 164, energyUnitEn: 'kWh' },
      //         { emodelName: '口腔科', value: 100, energyUnitEn: 'kWh' }
      //       ],
      //       用水: [
      //         { emodelName: '泌尿外科', value: 132, energyUnitEn: 't' },
      //         { emodelName: '口腔科', value: 145, energyUnitEn: 't' }
      //       ],
      //       热能: [
      //         { emodelName: '泌尿外科', value: 156, energyUnitEn: 'GJ' },
      //         { emodelName: '口腔科', value: 182, energyUnitEn: 'GJ' }
      //       ]
      //     },
      //     '1574997197185888258': {
      //       电力: [
      //         { emodelName: '心脏外科', value: 123, energyUnitEn: 'kWh' },
      //         { emodelName: '淋巴外科', value: 100, energyUnitEn: 'kWh' },
      //         { emodelName: '介入治疗科', value: 156, energyUnitEn: 'kWh' }
      //       ],
      //       用水: [
      //         { emodelName: '泌尿外科', value: 175, energyUnitEn: 't' },
      //         { emodelName: '口腔科', value: 115, energyUnitEn: 't' },
      //         { emodelName: '介入治疗科', value: 138, energyUnitEn: 't' }
      //       ],
      //       热能: [
      //         { emodelName: '泌尿外科', value: 146, energyUnitEn: 'GJ' },
      //         { emodelName: '口腔科', value: 172, energyUnitEn: 'GJ' },
      //         { emodelName: '介入治疗科', value: 121, energyUnitEn: 'GJ' }
      //       ]
      //     }
      //   }
      //   const newArr = [
      //     { emodelName: '心脏外科', value: 100, energyUnitEn: this.unitItems[this.energyId] },
      //     { emodelName: '淋巴外科', value: 100, energyUnitEn: this.unitItems[this.energyId] },
      //     { emodelName: '介入治疗科', value: 100, energyUnitEn: this.unitItems[this.energyId] },
      //     { emodelName: '泌尿外科', value: 100, energyUnitEn: this.unitItems[this.energyId] },
      //     { emodelName: '口腔科', value: 100, energyUnitEn: this.unitItems[this.energyId] }
      //   ]
      //   let arr = obj[this.emodelId] ? (obj[this.emodelId][this.showContentType] ? obj[this.emodelId][this.showContentType] : []) : []
      //   arr.sort((a, b) => {
      //     return b.value - a.value
      //   })
      //   let total = arr.reduce((sum, num) => {
      //     return sum + num.value
      //   }, 0)
      //   arr.forEach((item) => {
      //     item.ratio = (item.value / total) * 100
      //   })
      //   this.areaRankingList = arr
      //   return
      // }
      const dataType = {
        1: 'day',
        2: 'day',
        3: 'month',
        4: 'year',
        5: 'day'
      }
      const param = {
        aggregation: 'sum',
        valueName: 'value',
        groupBy: 'emodelId',
        timeCategory: dataType[this.statisticalType],
        startDate: this.statisticalDate[0],
        endDate: this.statisticalDate[1],
        emodelId: this.spaceLevel == 4 ? this.emodelId : this.emodelIdAll, // 模型id
        energyId: this.menuList[this.menuActive].children.length ? this.menuList[this.menuActive].children[this.menuTitleActive].id : this.menuList[this.menuActive].id
      }
      this.getModelEnergyDataList(param)
        .then((res) => {
          let arr = []
          // if(this.spaceLevel == 4 && this.emodelId == '1724753193415503873') {
          //   arr = [
          //     {
          //       emodelId: '12fdq',
          //       value: (res.data.data[0].value / 100) * 45,
          //       emodelName: '12F住院楼东区',
          //       energyUnitEn: res.data.data[0].energyUnitEn
          //     },{
          //       emodelId: '12fxq',
          //       value: (res.data.data[0].value / 100) * 55,
          //       emodelName: '12F住院楼西区',
          //       energyUnitEn: res.data.data[0].energyUnitEn
          //     }
          //   ]
          // } else {
          //   arr = res.data.data
          // }
          arr = res.data.data
          arr.sort((a, b) => {
            return b.value - a.value
          })
          const total = arr.reduce((sum, num) => {
            return sum + num.value
          }, 0)
          this.total = total
          arr.forEach((item) => {
            item.ratio = (item.value / total) * 100
          })
          this.areaRankingList = arr
          var newArr = []
          arr.forEach((item, index) => {
            newArr.push({
              energyName: this.showContentType,
              areaid: item.emodelId,
              areaData: item.value,
              unit: this.unitItems[this.energyId]
              // areaData: index === 0 ? 100 : ((item.value / arr[0].value) * 100).toFixed(2)
            })
          })
          try {
            window.chrome.webview.hostObjects.sync.bridge.AreaData(JSON.stringify(newArr))
          } catch (errpr) {}
        })
        .catch()
    },
    // 饼图
    electricityPieEchart(data, total) {
      const myChart = echarts.init(document.getElementById('analysis_chart'))
      const nameObj = {
        SU035: '总用电量',
        CM020: '总用水量',
        '4c78a70e0b7deb51b0a227fb2cd9196f': '总用热量',
        '66655f36bc0dc86adbaf2cab80d9dce2': '总蒸汽用量'
      }
      let option
      if (data.length) {
        option = {
          // backgroundColor: '',
          color: data.map((v) => v.color),
          title: {
            text: '{a|' + this.filterValue(total) + '}{c|' + this.unitItems[this.energyId] + '}',
            subtext: nameObj[this.energyId],
            x: 'center',
            y: '40%',
            textStyle: {
              rich: {
                a: {
                  fontSize: 13,
                  color: '#FFFFFF'
                },
                c: {
                  fontSize: 12,
                  color: '#FFFFFF'
                }
              }
            },
            subtextStyle: {
              color: 'rgba(255,255,255,0.6)',
              fontSize: 12
            }
          },
          tooltip: {
            show: false
          },
          series: [
            {
              type: 'pie',
              name: 'TypeB', // 内层细圆环2
              radius: ['46%', '47%'],
              hoverAnimation: false,
              clockWise: false,
              itemStyle: {
                color: 'rgba(255,255,255,0.2)'
              },
              label: {
                show: false
              },
              data: [100]
            },
            {
              type: 'pie',
              name: 'TypeA', // 最外层细圆环
              hoverAnimation: false,
              clockWise: false,
              radius: ['63%', '64%'],
              itemStyle: {
                color: 'rgba(255,255,255,0.2)'
              },
              label: {
                show: false
              },
              data: [100]
            },
            {
              name: 'content',
              type: 'pie',
              clockWise: false,
              radius: ['50%', '60%'],
              hoverAnimation: true,
              data: data,
              label: {
                show: false
              },
              itemStyle: {
                normal: {
                  borderWidth: 2, // 间距的宽度
                  borderColor: 'rgba(40,48,65,1)' // 背景色
                }
              }
            },
            {
              // 内圆
              type: 'pie',
              radius: '42%',
              center: ['50%', '50%'],
              hoverAnimation: false,
              itemStyle: {
                color: 'rgba(133,145,206,0.15)'
              },
              label: {
                show: false
              },
              tooltip: {
                show: false
              },
              data: [100]
            }
          ]
        }
      } else {
        option = {
          title: {
            text: '暂无数据',
            x: 'center',
            y: 'center',
            textStyle: {
              fontSize: 14,
              fontWeight: 'normal',
              color: '#999'
            }
          }
        }
      }
      myChart.off('mouseover')
      myChart.off('mouseout')
      myChart.clear()
      myChart.setOption(option)
      // 随着屏幕大小调节图表
      window.addEventListener('resize', () => {
        myChart.resize()
      })
      myChart.on('mouseover', (e) => {
        if (e.seriesName == 'content') {
          option.title.text = '{a|' + this.filterValue(data[e.dataIndex].value) + '}{c|' + data[e.dataIndex].unit + '}'
          option.title.subtext = this.filterName(data[e.dataIndex].name)
          myChart.setOption(option)
        }
      })
      myChart.on('mouseout', (e) => {
        if (e.seriesName == 'content') {
          option.title.text = '{a|' + this.filterValue(total) + '}{c|' + this.unitItems[this.energyId] + '}'
          option.title.subtext = nameObj[this.energyId]
          myChart.setOption(option)
        }
      })
    },
    // 类型分析
    typeAnalysis() {
      const dataType = {
        1: 'day',
        2: 'day',
        3: 'month',
        4: 'year',
        5: 'day'
      }
      const param = {
        aggregation: 'sum',
        valueName: 'value',
        groupBy: 'energyId',
        timeCategory: dataType[this.statisticalType],
        startDate: this.statisticalDate[0],
        endDate: this.statisticalDate[1],
        emodelId: this.emodelId, // 模型id
        energyId: this.menuList[this.menuActive].children.length
          ? this.menuTitleActive === 0
            ? this.menuList[this.menuActive].children[this.menuTitleActive].allId
            : this.menuList[this.menuActive].children[this.menuTitleActive].id
          : this.menuList[this.menuActive].id
      }
      const color = ['#E88D6B', '#FFCA64', '#5E89EE', '#0A84FF', '#61E29D']
      let newData = []
      this.getModelEnergyDataList(param)
        .then((res) => {
          newData = res.data.data.map((v, i) => {
            return {
              value: v.value,
              name: v.energyName,
              color: color[i],
              unit: v.energyUnitEn
            }
          })
          this.typeAnalysisList = newData
          const total = newData.reduce((prev, cur) => {
            return prev + Number(cur.value) ?? 0
          }, 0)
          setTimeout(() => {
            this.$nextTick(() => {
              this.electricityPieEchart(newData, this.total ? this.total : total)
            })
          }, 100)
        })
        .catch()
    },
    // 实时监测
    realTimeMonitor() {
      // if (this.spaceLevel == 4 && this.activeType == 2) {
      //   const obj = {
      //     1: {
      //       value: 212.83, // 当前值
      //       momValueRatio: 5.36, // 环比比率
      //       yoyValueRatio: -15.59, // 同比比率
      //       yoyValue: 252, // 同比值 去年同期
      //       momValue: '93.2%' // 环比值 定额
      //     },
      //     2: {
      //       value: 1362.7, // 当前值
      //       momValueRatio: 3.11, // 环比比率
      //       yoyValueRatio: 17.85, // 同比比率
      //       yoyValue: 15523.22, // 同比值 去年同期
      //       momValue: '73%' // 环比值 定额
      //     },
      //     3: {
      //       value: 7634.12, // 当前值
      //       momValueRatio: 1.32, // 环比比率
      //       yoyValueRatio: 11.5, // 同比比率
      //       yoyValue: 6834.12, // 同比值 去年同期
      //       momValue: '120%' // 环比值 定额
      //     },
      //     4: {
      //       value: 98823.5, // 当前值
      //       momValueRatio: null, // 环比比率
      //       yoyValueRatio: null, // 同比比率
      //       yoyValue: null, // 同比值 去年同期
      //       momValue: '82.43%' // 环比值 定额
      //     }
      //   }
      //   this.monitorData = obj[this.statisticalType]
      //   return
      // }
      const dataType = {
        1: 'day',
        2: 'day',
        3: 'month',
        4: 'year',
        5: 'day'
      }
      const params = {
        emodelId: this.emodelId, // 模型id
        energyId: this.menuList[this.menuActive].children.length ? this.menuList[this.menuActive].children[this.menuTitleActive].id : this.menuList[this.menuActive].id,
        timeCategory: dataType[this.statisticalType],
        startDate: this.statisticalDate[0],
        endDate: this.statisticalDate[1]
        // timeCategory: '' // 数据类型 （hour小时数据 day日数据 week周数据  month月数据 year年数据）
      }
      this.getModelEnergyDataList(params)
        .then((res) => {
          // this.monitorData = {
          //   value: res.data.data.length ? this.filterValue(res.data.data[0].value) : 0, // 当前值
          //   momValueRatio: res.data.data.length ? this.filterValue(res.data.data[0].momValueRatio) : 0, // 环比比率
          //   yoyValueRatio: res.data.data.length ? this.filterValue(res.data.data[0].yoyValueRatio) : 0, // 同比比率
          //   yoyValue: res.data.data.length ? this.filterValue(res.data.data[0].yoyValue) : 0, // 同比值
          //   momValue: res.data.data.length ? this.filterValue(res.data.data[0].momValue) : 0, // 环比值
          //   emodelName: res.data.data.length ? res.data.data[0]?.emodelName ?? '' : '' // 模型名称
          // }
          const resData = res.data.data
          if (resData.length) {
            this.monitorData = {
              value: this.getListFieldSum(resData, 'value'), // 当前值
              momValueRatio: this.getListFieldSum(resData, 'momValueRatio', resData.length > 1, 'toFixed'), // 环比比率
              yoyValueRatio: this.getListFieldSum(resData, 'yoyValueRatio', resData.length > 1, 'toFixed'), // 同比比率
              yoyValue: this.getListFieldSum(resData, 'yoyValue', resData.length > 1), // 同比值
              momValue: this.getListFieldSum(resData, 'momValue', resData.length > 1), // 环比值
              emodelName: resData[0].emodelName ?? '', // 模型名称
              valueCost: this.getListFieldSum(resData, 'valueCost') // 费用总数
            }
          } else {
            this.monitorData = {
              value: 0,
              momValueRatio: 0,
              yoyValueRatio: 0,
              yoyValue: 0,
              momValue: 0,
              emodelName: '',
              valueCost: 0
            }
          }
        })
        .catch()
    },
    getListFieldSum(list, field, isRatio = false, type = '') {
      if (isRatio) return 0
      const sum = list.reduce((prev, cur) => {
        return prev + Number(cur[field]) ?? 0
      }, 0)
      return this.filterValue(sum, type)
    },
    // 获取模型能源数据列表
    getModelEnergyDataList(params) {
      return new Promise((resolve, reject) => {
        GetModelEnergyDataList(params, this.kgceToken)
          .then((res) => {
            if (res.data.code === 200) {
              if (res.data.data.length) {
                res.data.data.forEach((item) => {
                  this.unitItems[this.energyId] = item.energyUnitEn
                })
                console.log(this.unitItems)
              }
              resolve(res)
            } else {
              reject(res)
            }
          })
          .catch((err) => {
            reject(err)
          })
      })
    },
    // wpf数据传输
    setWPFdata(val) {
      if (val && val.length) {
        this.statisticalDate = [val[0] + ' 00:00:00', val[1] + ' 23:59:59']
      }
      if (!this.statisticalDate.length) {
        this.statisticalType = 1
        this.statisticalDate = [moment().format('YYYY-MM-DD') + ' 00:00:00', moment().format('YYYY-MM-DD') + ' 23:59:59']
      }
      this.$emit('sendWpfData', {
        type: 'energy',
        params: {
          btnType: this.statisticalType,
          startTime: this.statisticalDate[0],
          endTime: this.statisticalDate[1]
        }
      })
      this.regionalRanking()
      this.realTimeMonitor()
      this.typeAnalysis()
    },
    // 选中 二级菜单
    selectMenuTitle(index) {
      this.menuTitleActive = index
      this.setWPFdata()
    },
    // 选中 一级菜单
    activeEnergyEvent(index) {
      this.tabChildren = this.menuList[index].children
      this.showContentType = this.menuList[index].name
      try {
        window.chrome.webview.hostObjects.sync.bridge.AreaDataSwitch(JSON.stringify({ type: this.showContentType }))
      } catch (errpr) {}
      this.menuActive = index
      this.energyId = this.menuList[index].id
      this.selectMenuTitle(0)
    },
    // 获取能源及分类的树结构
    getEnergyAndClassifyTree() {
      GetEnergyAndClassifyTree({}, this.kgceToken).then((res) => {
        if (res.data.code === 200) {
          res.data.data.forEach((item) => {
            if (item.children.length == 1) {
              item.children[0].allId = item.id
            } else if (item.children.length >= 2) {
              item.children.unshift({ id: item.id, allId: item.children.map((v) => v.id).join(','), name: '总览' })
              item.children = item.children.filter((v) => v.name !== '设备用电')
            }
          })
          this.menuList = res.data.data
          this.menuList = res.data.data.filter((item) => item.name !== '综合能耗' && item.name !== '蒸汽')
          this.activeEnergyEvent(0)
        }
      })
    },
    // 能耗登录
    kgceLogin() {
      // eslint-disable-next-line no-undef
      KgceLogin(KECG_ACCOUNT).then((res) => {
        if (res.data.code === 200) {
          this.kgceToken = res.data.result.token
          this.getEnergyAndClassifyTree()
        }
      })
    }
  }
}
</script>
<style lang="scss" scoped>
.energy {
  width: 100%;
  height: 100%;
  background: center;
  .energy-main {
    width: 100%;
    height: 100%;
    box-sizing: border-box;
    position: relative;
    transition: width 0.3s linear;
    overflow: hidden;
    .main-title {
      padding: 10px 8px;
      background: rgba(255, 224, 152, 0.12);
      display: flex;
      align-items: center;
      justify-content: space-between;
      .main-title-text {
        font-size: 18px;
        font-weight: 500;
        color: #ffca64;
        line-height: 20px;
      }
      .el-dropdown-link {
        font-size: 14px;
        font-weight: 300;
        color: #ffffff;
        cursor: pointer;
        .el-icon-arrow-down {
          font-size: 12px;
        }
      }
    }
    .main-search {
      padding: 12px 0px;

      .search-tabs {
        display: flex;
        .tabs-item {
          cursor: pointer;
          padding: 10px 0;
          text-align: center;
          font-size: 16px;
          font-weight: 400;
          color: #a4afc1;
          line-height: 19px;
          overflow: hidden;
          background: linear-gradient(360deg, #334572 0%, rgba(38, 49, 79, 0.14) 57%, rgba(36, 46, 73, 0) 100%);
          border-left: 1px solid;
          border-bottom: 1px solid;
          border-image: linear-gradient(180deg, rgba(113, 125, 168, 0), rgba(113, 128, 168, 1)) 1 1;
          font-family: HarmonyOS_Sans_SC_Medium;
        }
        .tabs-item:last-child {
          border-right: 1px solid;
        }
        .tabs-active {
          position: relative;
          color: #b0e3fa;
          background: linear-gradient(360deg, #3a668e 0%, rgba(36, 46, 73, 0) 100%);
        }
        .tabs-active::after {
          content: '';
          position: absolute;
          left: 50%;
          transform: translateX(-50%);
          width: 36px;
          height: 3px;
          bottom: 10px;
          background: #6ee1f9;
          filter: blur(10.87px);
        }
      }
      .search-tags {
        padding: 4px 0px 12px 0px;
        flex-wrap: wrap;
        display: flex;
        background: rgba(133, 145, 206, 0.15);
        .tags-item {
          cursor: pointer;
          margin-left: 8px;
          margin-top: 8px;
          padding: 6px 12px;
          background: rgba(255, 255, 255, 0.1) linear-gradient(90deg, rgba(10, 132, 255, 0) 0%, rgba(10, 132, 255, 0) 100%);
          font-size: 14px;
          font-weight: 400;
          color: #b0e3fa;
          line-height: 14px;
          border: 1px solid transparent;
          font-family: HarmonyOS Sans SC-Regular;
        }
        .tags-active {
          background: rgba(255, 255, 255, 0.1) linear-gradient(90deg, rgba(10, 132, 255, 0.4) 0%, rgba(10, 132, 255, 0) 100%);
          border: 1px solid;
          border-image: radial-gradient(circle, rgba(171, 240, 255, 1), rgba(226, 254, 255, 1), rgba(132, 196, 203, 1), rgba(48, 151, 166, 0)) 1 1;
        }
      }
      .search-data {
        display: flex;
        background: rgba(133, 145, 206, 0.15);
        padding: 0px 10px;
        ::v-deep .el-dropdown {
          padding: 7px 6px;
          .el-dropdown-link {
            font-size: 14px;
            font-weight: 500;
            color: #8bddf5;
            line-height: 16px;
            position: relative;
            cursor: pointer;
          }
          .el-dropdown-link::after {
            content: '';
            position: absolute;
            width: 1px;
            height: 12px;
            background: rgba(133, 145, 206, 0.5);
            top: 50%;
            right: -6px;
            transform: translateY(-50%);
          }
        }
        ::v-deep .datePickerInput {
          flex: 1;
          padding: 8px 10px;
          height: 16px;
          box-sizing: content-box;
          background: none;
          border: none;
          .el-input__icon,
          .el-range-separator {
            line-height: 16px;
            color: #b0e3fa;
          }
          .el-range-input {
            background: none;
            color: #a4afc1;
          }
        }
      }
    }
    .main-monitor {
      padding: 12px 0px 32px 0px;
      .monitor-head {
        padding: 14px 0px 14px 60px;
        background: url('~@/assets/images/qhdsys/monitor_head_bgd.png') no-repeat center / 100% 100%;
        display: flex;
        align-items: center;
      }
      .head-icon {
        width: 69px;
        height: 69px;
      }
      .head-info {
        margin-left: 32px;
        .head-info-name {
          font-size: 16px;
          font-weight: 400;
          color: #b0e3fa;
          line-height: 19px;
        }
        .head-info-value {
          margin-top: 12px;
        }
        .head-info-value-num {
          font-size: 24px;
          font-weight: 500;
          color: #ffca64;
          line-height: 28px;
        }
        .head-info-value-unit {
          margin-left: 9px;
          font-size: 14px;
          font-weight: 400;
          color: rgba(164, 172, 185, 0.3);
          line-height: 16px;
        }
      }
      .monitor-ratio {
        padding: 0 48px 0px 48px;
        display: flex;
        flex-wrap: wrap;
        justify-content: space-between;
      }
      .ratio-item {
        min-width: 110px;
        margin-top: 16px;
        background: url('~@/assets/images/qhdsys/monitor_ratio_line.png') no-repeat left 24px / 96.6px 9.6px;
      }
      .ratio-item-value {
        i {
          margin-right: 4px;
          font-size: 14px;
          border-radius: 2px;
        }
        span {
          font-size: 14px;
          font-weight: bold;
          color: #fff;
          line-height: 24px;
        }
      }
      .ratio-item-name {
        margin-top: 12px;
        color: #a4acb9;
        font-size: 14px;
        font-weight: 400;
        line-height: 16px;
      }
    }
    .main-module {
      .main-module-title {
        padding: 7px 0px 7px 38px;
        font-size: 16px;
        font-weight: bold;
        font-family: HarmonyOS Sans SC-Bold;
        color: #ffffff;
        line-height: 30px;
        text-shadow: 0px 0px 9px #158eff;
        background: url('~@/assets/images/qhdsys/bg-bt.png') no-repeat center / 100% 100%;
      }
      .main-module-content {
        height: calc(100% - 44px);
      }
    }
    .main-analysis {
      display: flex;
      .analysis-content-chart {
        height: 100%;
        width: 50%;
      }
      .analysis-content-list {
        height: 100%;
        width: 50%;
        padding: 16px 16px 16px 0;
        display: flex;
        flex-direction: column;
        box-sizing: border-box;
        justify-content: space-evenly;
        .list-item {
          position: relative;
          display: flex;
          align-items: center;
          padding: 4px 0px 4px 27px;
          background: rgba(42, 54, 68, 0.4);
          .list-item-color {
            width: 7px;
            height: 7px;
            border-radius: 50%;
            position: absolute;
            top: 50%;
            transform: translateY(-50%);
            left: 10px;
          }
          .list-item-name {
            font-size: 12px;
            font-weight: 400;
            color: #a4acb9;
            line-height: 14px;
            min-width: 40px;
          }
          .list-item-value {
            font-size: 16px;
            font-weight: 400;
            color: #ffffff;
            line-height: 19px;
          }
        }
        .list-item::before {
          content: '';
          width: 2px;
          height: 2px;
          position: absolute;
          background: #9bb8c7;
          left: 0;
          top: 0;
        }
      }
    }
    .main-ranking {
      padding-top: 16px;
      box-sizing: border-box;
      position: relative;
      .main-ranking-color {
        margin: 0 auto 0;
        width: 100px;
        height: 8px;
        background: linear-gradient(270deg, #ffca64 0%, rgba(255, 202, 100, 0) 100%);
        position: relative;
      }
      .main-ranking-color::before {
        content: '低';
        position: absolute;
        top: 50%;
        left: -20px;
        transform: translateY(-50%);
        font-size: 12px;
        font-weight: 400;
        color: #ffffff;
        line-height: 14px;
      }
      .main-ranking-color::after {
        content: '高';
        position: absolute;
        top: 50%;
        right: -20px;
        transform: translateY(-50%);
        font-size: 12px;
        font-weight: 400;
        color: #ffffff;
        line-height: 14px;
      }
      .main-ranking-list {
        width: 100%;
        height: calc(100% - 8px);
        padding: 0px 8px;
        box-sizing: border-box;
        overflow: auto;
        .list-item {
          display: flex;
          align-items: center;
          justify-content: center;
          margin-bottom: 8px;
          .list-item-name {
            width: 110px;
            font-size: 14px;
            font-weight: 400;
            color: #ffffff;
            line-height: 20px;
            text-align: right;
          }
          .list-item-line {
            margin: 0px 8px;
            flex: 1;
            span {
              display: inline-block;
              height: 8px;
            }
          }
          .list-item-value {
            width: 130px;
            font-size: 12px;
            font-weight: 400;
            color: #ffffff;
            line-height: 14px;
          }
        }
      }
      .main-ranking-list::-webkit-scrollbar {
        display: none; /* Chrome Safari */
      }
      .nodeData {
        font-size: 14px;
        color: #999;
        position: absolute;
        left: 50%;
        top: 50%;
        transform: translate(-50%, -50%);
      }
    }
  }
}
</style>
