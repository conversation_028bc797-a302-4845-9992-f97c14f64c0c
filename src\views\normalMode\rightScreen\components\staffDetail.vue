<template>
  <div class="main">
    <el-dialog
      v-dialogDrag
      :modal="false"
      :visible.sync="dialogShow"
      custom-class="mainDialog main"
      append-to-body
      :close-on-click-modal="false"
      :before-close="closeDialog"
      class="searchComponentList"
    >
      <template slot="title">
        <span class="dialog-title">{{ dialogData.title }}</span>
      </template>
      <div class="dialog-content">
        <el-table
          height="100%"
          stripe
          :data="tableData"
          style="width: 100%; margin-bottom: 10px"
          :cell-style="$tools.setCell(3)"
          :header-cell-style="$tools.setHeaderCell(3)"
          element-loading-background="rgba(0, 0, 0, 0.2)"
        >
          <el-table-column type="index" label="序号" width="100" align="center"></el-table-column>
          <el-table-column prop="staffName" show-overflow-tooltip label="姓名" align="center"></el-table-column>
          <el-table-column prop="mobile" show-overflow-tooltip label="手机号" align="center"></el-table-column>
          <el-table-column prop="officeName" show-overflow-tooltip label="所属部门" align="center"></el-table-column>
          <el-table-column prop="postName" show-overflow-tooltip label="岗位" align="center"></el-table-column>
          <el-table-column prop="jobName" show-overflow-tooltip label="职位" align="center"></el-table-column>
        </el-table>
      </div>
    </el-dialog>
  </div>
</template>
<script lang="jsx">
import { getStaffInfoByIds, getOCTeamMemberInfo } from '@/utils/spaceManage'
export default {
  name: 'staffDetail',
  props: {
    dialogShow: {
      type: Boolean,
      default: false
    },
    dialogData: {
      type: Object,
      default: () => {
        return {
          title: '人员详情',
          staffIds: ''
        }
      }
    }
  },
  data() {
    return {
      // 列表
      tableData: [],
      tableLoading: false
    }
  },
  watch: {
    dialogShow(val) {
      if (val) {
        this.getUserIdData()
      }
    }
  },
  created() {},
  mounted() {
    this.getUserIdData()
  },
  methods: {
    getUserIdData() {
      const { staffIds } = this.dialogData
      const params = {
        id: staffIds
      }
      getOCTeamMemberInfo(params).then((res) => {
        const data = res.data
        if (res.data.code === '200') {
          let arr = []
          data.data.list.forEach((item) => {
            arr.push(item.user_id)
          })
          if (arr && arr.length) {
            let userIds = arr.join(',')
            this.getTableData(userIds)
          }
        }
      })
    },
    getTableData(val) {
      const { staffIds } = this.dialogData
      this.tableLoading = true
      const params = {
        staffIds: val,
        type: '',
        userIds: ''
      }
      getStaffInfoByIds(params).then((res) => {
        if (res.data.code == '200') {
          this.tableData = res.data.data || []
        }
        this.tableLoading = false
      })
    },
    // 取消按钮
    closeDialog() {
      this.$emit('configCloseDialog')
    }
  }
}
</script>

<style lang="scss" type="text/css" scoped>
.searchComponentList {
  pointer-events: none;
}
::v-deep .mainDialog {
  width: 60%;
  height: 59vh;
  margin-top: 19vh !important;
  // border: 1px solid #5996f9;
  background-color: transparent !important;
  pointer-events: auto;
  background-image: url('@/assets/images/table-bg-small.png');
  background-repeat: no-repeat;
  background-size: 100% 100%;
  box-shadow: none;
  .dialog-title::before {
    display: none;
  }
  .el-dialog__header {
    background-color: transparent;
    padding-top: 15px;
    height: 50px !important;
  }
  .el-dialog__body {
    padding: 0;
    height: calc(100% - 100px);
    max-height: fit-content;
    overflow-y: hidden;
    background-color: transparent;
    padding: 0px 80px;
    .dialog-content {
      width: 100%;
      height: 100%;
      background-color: transparent;
    }
  }
}
::v-deep div.el-table__fixed-body-wrapper {
  background: center;
}
::v-deep .el-table {
  border: none !important;
}
// ::v-deep .el-table--enable-row-hover .el-table__body tr:hover {
//   box-sizing: border-box;
//   td.el-table__cell {
//     background-color: #343c62 !important;
//     border-top: 1px solid #ffe3a6;
//     border-bottom: 1px solid #ffe3a6;
//   }
// }

::v-deep .el-table--striped .el-table__body tr.el-table__row--striped td {
  background-color: rgba(168, 172, 171, 0.08) !important; /* def2ff f2faff */
}
</style>
