<template>
    <div class="content">
      <!-- <el-button class="sino-button-sure" @click="goTo('/patrolInspectionDetails', { id: 1576618011660005376 })">巡检详情</el-button> -->
      <!-- <el-button class="sino-button-sure" @click="goTo('/riskPointDetails', { id: 1 })">风险点详情</el-button> -->
      <!-- <el-button class="sino-button-sure" @click="allTableChange('ioms')">showIomsDialog</el-button>
      <el-button class="sino-button-sure" @click="allTableChange('imws')">showImwsDialog</el-button>
      <el-button class="sino-button-sure" @click="allTableChange('danger')">showDangerDialog</el-button>
      <el-button class="sino-button-sure" @click="allTableChange('space')">showSpaceDialog</el-button> -->
      <el-button class="sino-button-sure" @click="roomInfoManagementChange('room')">showRoomDialog</el-button>
      <el-button class="sino-button-sure" @click="roomInfoManagementChange('floor')">showFloorDialog</el-button>
      <el-button class="sino-button-sure" @click="spaceInfoChange('floor')">changeFloorData</el-button>
      <el-button class="sino-button-sure" @click="spaceInfoChange('room')">changeRoomData</el-button>
      <!-- <el-button class="sino-button-sure" @click="showCenterTableDialog()">changeTableDialog</el-button> -->
      <!-- <div style="color: #ffe3a6">{{ WPFmessage }}</div> -->
      <!-- <div style="color: #ffe3a6">{{ toWPFdata }}</div> -->
      <div class="sham-content"></div>
      <div @click="collapseHeight" v-show="collapseShow" :class="collapseFlag ? 'circle-btn-top' : 'circle-btn-bottom'" class="circle-btn circle-btn-top"></div>
      <div class="right-content" ref="collapseHeight">
        <div class="top_title" v-show="collapseFlag">
          <span class="dialog-title">{{ spaceName ?? '空间管理' }}</span>
        </div>
        <div class="room-dept-content" v-show="collapseFlag">
          <div class="top_statistics" @click="allTableChange('space')">
            <div>
              <span><img :src="require('@/assets/images/center/ic-room.png')" alt="" /> 房间总数</span> <span style="cursor: pointer">{{ spaceData.totalCount }}间</span>
            </div>
            <div>
              <span><img :src="require('@/assets/images/center/ic-room.png')" alt="" /> 闲置房间</span> <span>{{ spaceData.idleCount }}间</span>
            </div>
            <div>
              <span><img :src="require('@/assets/images/center/ic-area.png')" alt="" /> 建筑面积</span> <span>{{ spaceData.totalArea }}㎡</span>
            </div>
            <div>
              <span><img :src="require('@/assets/images/center/ic-area.png')" alt="" /> 公区面积</span> <span>{{ spaceData.publicArea }}㎡</span>
            </div>
            <div>
              <span><img :src="require('@/assets/images/center/ic-area.png')" alt="" /> 使用面积</span> <span>{{ spaceData.useArea }}㎡</span>
            </div>
          </div>
          <div class="top_type">
            <div class="top_type_item" @click="changeType('dept')" :class="{ activeType: activeType === 'dept' }">
              <span>部门</span>
            </div>
            <div class="top_type_item" @click="changeType('function')" :class="{ activeType: activeType === 'function' }">
              <span>用途</span>
            </div>
          </div>
          <div class="center_tab">
            <span @click="changeTab('1')" :class="{ activeTab: activeTab === '1' }">房间数</span>
            <i>|</i>
            <span @click="changeTab('2')" :class="{ activeTab: activeTab === '2' }">建筑面积</span>
            <i>|</i>
            <span @click="changeTab('3')" :class="{ activeTab: activeTab === '3' }">使用面积</span>
          </div>
          <div class="bottom_echarts">
            <div v-if="analysisBarShow" class="center-center">暂无数据</div>
            <div v-else style="width: 100%; height: 100%">
              <div id="analysisBarEcharts"></div>
            </div>
          </div>
        </div>
      </div>
      <template v-if="allTableComponentListShow">
        <allTableComponentList ref="allTableComponentList" :dialogData="tableCompenentData" :dialogShow="allTableComponentListShow" @configCloseDialog="configCloseTableDialog"></allTableComponentList>
      </template>
      <template v-if="roomInfoManagementShow">
        <roomInfoManagement ref="roomInfoManagement" :dialogData="roomInfo" :dialogShow="roomInfoManagementShow" @configCloseDialog="configCloseRoomDialog"></roomInfoManagement>
      </template>
      <template v-if="centerTableDialogShow">
        <component
          :is="centerTableComponent"
          :ref="centerTableComponent"
          :ssmCodes="tagSsmCodes"
          :location="localtion"
          :ssmType="ssmType"
          :type="hazardType"
          :dialogShow="centerTableDialogShow"
          @configCloseDialog="configCloseCenterTableDialog"
        ></component>
      </template>
      <template v-if="ipasPointDetailShow">
        <ipasPointDetail ref="retrospect" :dialogShow="ipasPointDetailShow" :detailId="detailId" @retrospectCloseDialog="retrospectCloseDialog"> </ipasPointDetail>
      </template>
    </div>
  </template>
  
  <script>
  import { getRoomAreaList, getRoomCountAndArea, getRoomCountList, getSpaceDeptList, getSpaceFunctionList } from '@/utils/spaceManage.js'
  import * as echarts from 'echarts'
  import allTableComponentList from './components/allTableComponentList.vue'
  import roomInfoManagement from './roomInfoManagement.vue'
  export default {
    inject: ['reload'],
    name: 'spaceManage',
    components: {
      allTableComponentList,
      roomInfoManagement,
      workOrderCenterList: () => import('@/views/centerScreen/businessManagement/component/workOrderCenterList.vue'),
      ipasPointRecord: () => import('@/views/centerScreen/businessManagement/component/ipasPointRecord.vue'),
      collectRecord: () => import('@/views/centerScreen/businessManagement/component/collectRecord.vue'),
      ipasPointDetail: () => import('@/views/centerScreen/businessManagement/component/ipasPointDetail.vue'),
      hazardList: () => import('@/views/centerScreen/safetyOverview/components/hazardList.vue')
    },
    data() {
      return {
        WPFmessage: '',
        toWPFdata: '',
        spaceDataList: [], // 空间数据(科室，功能)
        spaceName: null, // 所在空间名称
        modelCode: '', // 楼层空间模型编码
        localtion: '', // 空间编码
        ssmType: null, // 空间等级
        activeType: 'dept',
        activeTab: '1',
        collapseFlag: true,
        collapseShow: true,
        allTableComponentListShow: false,
        roomInfoManagementShow: false,
        // table组件参数
        tableCompenentData: {
          title: '',
          type: ''
        },
        // 房间组件参数
        roomInfo: {
          title: '',
          localtion: '',
          ssmType: '',
          ssmCodes: ''
        },
        dialogTypeList: [
          {
            title: '服务工单台账',
            type: 'ioms',
            height: 'calc(100% - 200px)'
          },
          {
            title: '科室医废收集记录',
            type: 'imws',
            height: 'calc(100% - 200px)'
          },
          {
            title: '隐患清单',
            type: 'danger',
            height: 'calc(100% - 200px)'
          },
          {
            title: '空间清单',
            type: 'space',
            height: 'calc(100% - 50px)'
          }
        ],
        getchart: null,
        analysisBarShow: false,
        spaceData: {
          idleCount: 0,
          publicArea: 0,
          totalArea: 0,
          totalCount: 0,
          useArea: 0
        }, // 空间管理数据
        ipasPointDetailShow: false,
        detailId: '',
        hazardType: '',
        centerTableDialogTypeList: [
          {
            label: '工单',
            type: 'ioms',
            component: 'workOrderCenterList'
          },
          {
            label: '巡检',
            type: 'icis',
            component: 'ipasPointRecord'
          },
          {
            label: '医废',
            type: 'imws',
            component: 'collectRecord'
          },
          {
            label: '隐患',
            type: 'danger',
            component: 'hazardList'
          },
          {
            label: '风险',
            type: 'risk',
            component: 'hazardList'
          },
          {
            Dom: 'imes',
            name: '资产',
            component: 'imesComponent'
          }
        ],
        centerTableDialogShow: false, // 中间表格弹窗
        tagSsmCodes: '' // tag标签对应table的ssmCodes 与roomInfo中的ssmCodes一致 只是为了区分
      }
    },
    created() {},
    mounted() {
      // 初始化获取modelCode
      if (Object.hasOwn(this.$route.query, 'modelCode')) {
        this.modelCode = this.$route.query.modelCode || ''
      }
      try {
        window.chrome.webview.addEventListener('message', (event) => {
          this.WPFmessage = event.data
          const data = JSON.parse(event.data)
          if (data.type === 'init') {
            // 页面重载
            this.reload()
          } else if (data.type === 'area') {
            // 区域
            data.ssmType = data.ssmType ? Number(data.ssmType) : 3
            this.modelCode = data.modelCode || ''
            this.localtion = data.localtion || ''
            this.ssmType = data.ssmType || ''
            if (data.ssmType <= 4) {
              if (data.ssmType === 3) {
                // 室外
                this.spaceName = '空间管理'
              }
              if (data.ssmType === 4) {
                // 建筑物
                this.spaceName = data.buildingName
              }
              if (!this.collapseFlag) {
                setTimeout(() => {
                  this.collapseFlag = !this.collapseFlag
                }, 200)
                this.$nextTick(() => {
                  this.$refs.collapseHeight.style.width = '22%'
                  this.$refs.collapseHeight.style.padding = '10px 10px 20px 10px'
                })
              }
              this.collapseShow = true
              this.roomInfoManagementShow = false
              this.getAnalysisBarData()
              this.getRoomCountAndArea()
            } else {
              // 房间 楼层 关闭空间管理页面 打卡 楼层弹窗
              this.roomInfo = {
                title: data.buildingName + ' > ' + data.floorName,
                modelCode: data.modelCode,
                localtion: data.localtion,
                ssmType: data.ssmType,
                ssmCodes: data.ssmCodes
              }
              if (this.collapseFlag) {
                this.collapseFlag = !this.collapseFlag
                this.$nextTick(() => {
                  this.$refs.collapseHeight.style.width = '0'
                  this.$refs.collapseHeight.style.padding = '0'
                })
              }
              this.collapseShow = false
              this.roomInfoManagementShow = true
              this.$nextTick(() => {
                this.$refs.roomInfoManagement.initRoomInfo()
              })
            }
          } else if (data.type === 'tag') {
            // 标签
            this.localtion = data.localtion
            this.modelCode = data.modelCode
            this.ssmType = data.ssmType
            this.tagSsmCodes = data.ssmCodes
            // 判断是否是空间管理页面
            // if (this.collapseShow) {
            //   this.allTableChange('space')
            // } else {
            //   // 获取当前选中的标签对应列表
            //   this.showCenterTableDialog()
            // }
            if (data.typeName === 'space') {
              const param = data.spaceState === '0' ? { spaceState: 0 } : {}
              this.allTableChange('space', param)
            } else {
              // 获取当前选中的标签对应列表
              this.showCenterTableDialog()
            }
          }
        })
      } catch (errpr) {}
      this.getAnalysisBarData()
      this.getRoomCountAndArea()
    },
    methods: {
      // 获取空间信息房间数据
      getRoomCountAndArea() {
        const params = {
          modelCode: this.modelCode,
          haveModel: 0
        }
        getRoomCountAndArea(params).then((res) => {
          const data = res.data
          if (data.code === 200) {
            Object.assign(this.spaceData, data.data)
            // 计算公共区域面积 = 建筑面积 - 使用面积
            this.spaceData.publicArea = this.spaceData.publicArea ? this.spaceData.publicArea : this.spaceData.totalArea - this.spaceData.useArea
            // 保留两位小数
            const num = this.spaceData.publicArea?.toString()
            if (num.indexOf('.') !== -1) {
              this.spaceData.publicArea = num.substring(0, num.indexOf('.') + 3)
            }
          }
        })
      },
      // 获取空间管理柱状图数据
      getAnalysisBarData() {
        const params = {
          modelCode: this.modelCode,
          queryType: this.activeType,
          ascColumn: '',
          descColumn: ''
        }
        if (this.activeTab !== '1') {
          getRoomAreaList(params).then((res) => {
            const data = res.data
            if (data.code === 200 && data.data.length > 0) {
              this.analysisBarShow = false
              // const nameList = Array.from(data.data, ({ dataName }) => dataName ?? '未启用')
              const valueData = data.data.map((e) => {
                return {
                  value: e.roomAllArea ?? 0,
                  name: e.dataName ?? '未启用',
                  id: e.dataNameId
                }
              })
              this.$nextTick(() => {
                this.analysisBarEcharts(valueData, '㎡')
              })
            } else {
              this.analysisBarShow = true
            }
          })
        } else {
          getRoomCountList(params).then((res) => {
            const data = res.data
            if (data.code === 200 && data.data.length > 0) {
              this.analysisBarShow = false
              // const nameList = Array.from(data.data, ({ dataName }) => dataName ?? '未启用')
              const valueData = data.data.map((e) => {
                return {
                  value: e.roomAllCount ?? 0,
                  name: e.dataName ?? '未启用',
                  id: e.dataNameId
                }
              })
              this.$nextTick(() => {
                this.analysisBarEcharts(valueData, '间')
              })
            } else {
              this.analysisBarShow = true
            }
          })
        }
      },
      analysisBarEcharts(data, unit) {
        this.getchart = echarts.init(document.getElementById('analysisBarEcharts'))
        // valueData 按照value排序
        data.sort((a, b) => {
          return a.value - b.value
        })
        const nameList = Array.from(data, ({ name }) => name)
        this.getchart.resize()
        const option = {
          backgroundColor: '',
          tooltip: {
            trigger: 'axis',
            confine: true,
            backgroundColor: '#0D2169',
            borderColor: '#727382',
            textStyle: {
              color: '#fff'
            },
            axisPointer: {
              type: 'shadow'
            }
          },
          grid: {
            left: '5%',
            right: '15%',
            top: '1%',
            bottom: '5%',
            containLabel: true
          },
          xAxis: [
            {
              type: 'value',
              name: unit,
              axisLabel: {
                textStyle: {
                  color: '#878EA9'
                }
              },
              axisLine: {
                lineStyle: {
                  type: 'solid',
                  color: '#3862B7',
                  width: '1'
                }
              },
              splitLine: {
                lineStyle: {
                  color: ['#314A89'],
                  width: 1,
                  type: 'solid'
                }
              },
              position: 'top'
            }
          ],
          yAxis: [
            {
              type: 'category',
              data: nameList,
              axisLine: {
                lineStyle: {
                  type: 'solid',
                  color: '#3862B7',
                  width: '1'
                }
              },
              axisLabel: {
                show: true,
                textStyle: {
                  color: '#878EA9'
                },
                formatter: function (val) {
                  var strs = val.split('') // 字符串数组
                  var str = ''
                  // strs循环 forEach 每五个字符增加换行符
                  strs.forEach((item, index) => {
                    if (index % 8 === 0 && index !== 0) {
                      str += '\n'
                    }
                    str += item
                  })
                  return str
                }
              },
              axisTick: {
                show: false
              }
            }
          ],
          series: [
            {
              type: 'bar',
              barWidth: 20,
              data: data,
              itemStyle: {
                color: new echarts.graphic.LinearGradient(0, 1, 0, 0, [
                  {
                    offset: 0,
                    color: 'rgba(21, 89, 159, 0.25)'
                  },
                  {
                    offset: 1,
                    color: 'rgba(9, 244, 196, 0.86)'
                  }
                ])
              }
            }
          ],
          dataZoom: [
            {
              yAxisIndex: [0],
              orient: 'vertical',
              show: true,
              type: 'slider',
              start: 100,
              // end: 100,
              // startValue: 0,
              // endValue: 7,
              width: 5,
              left: '95%',
              borderColor: 'rgba(43,48,67,.1)',
              fillerColor: '#6580b8',
              zoomLock: true,
              showDataShadow: false,
              backgroundColor: 'rgba(43,48,67,.1)',
              showDetail: false,
              // realtime: true,
              filterMode: 'filter',
              handleIcon: 'M-9.35,34.56V42m0-40V9.5m-2,0h4a2,2,0,0,1,2,2v21a2,2,0,0,1-2,2h-4a2,2,0,0,1-2-2v-21A2,2,0,0,1-11.35,9.5Z',
              handleStyle: {
                color: '#6580b8',
                borderColor: '#6580b8'
              },
              // moveOnMouseMove: true,
              maxValueSpan: 7,
              minValueSpan: 7,
              brushSelect: false
            },
            {
              type: 'inside',
              show: false,
              yAxisIndex: [0, 1],
              height: 8
            }
          ]
        }
        this.getchart.clear()
        this.getchart.setOption(option)
        const that = this
        this.getchart.off('click')
        this.getchart.on('click', function (param) {
          // param.name x轴值,param.data y轴值
          that.allTableChange('space', {
            deptId: that.activeType === 'dept' ? param.data.id : '',
            functionDictId: that.activeType === 'function' ? param.data.id : ''
          })
        })
        // 随着屏幕大小调节图表
        window.addEventListener('resize', () => {
          that.getchart.resize()
        })
      },
      allTableChange(type, params = {}) {
        this.tableCompenentData = {
          title: '',
          type: ''
        }
        Object.assign(this.tableCompenentData, {
          ...this.dialogTypeList.find((e) => e.type === type),
          localtion: this.localtion,
          modelCode: this.modelCode,
          deptId: '',
          functionDictId: '',
          ...params
        })
        console.log(this.tableCompenentData)
        this.allTableComponentListShow = true
      },
      // 改变 box 高度
      collapseHeight() {
        this.$nextTick(() => {
          if (this.collapseFlag) {
            this.collapseFlag = !this.collapseFlag
            this.$refs.collapseHeight.style.width = '0'
            this.$refs.collapseHeight.style.padding = '0'
          } else {
            setTimeout(() => {
              this.collapseFlag = !this.collapseFlag
            }, 200)
            this.$refs.collapseHeight.style.width = '22%'
            this.$refs.collapseHeight.style.padding = '10px 10px 20px 10px'
          }
        })
      },
      // 获取空间功能或者部门信息的列表
      getSpaceMessageList() {
        const params = {
          modelCode: this.modelCode
        }
        if (this.activeLeftType === 'dept') {
          getSpaceDeptList(params).then((res) => {
            const data = res.data
            if (data.code === 200) {
              this.spaceDataList = data.data?.map((e) => {
                return {
                  ...e,
                  name: e.deptNameCount,
                  id: e.roomCodes,
                  color: '255,255,255'
                }
              })
            }
          })
        } else {
          getSpaceFunctionList(params).then((res) => {
            const data = res.data
            if (data.code === 200) {
              this.spaceDataList = data.data?.map((e) => {
                return {
                  ...e,
                  name: e.functionDictName,
                  id: e.roomCodes,
                  color: e.functionColourBy255 ?? '255,255,255'
                }
              })
            }
          })
        }
      },
      changeType(type) {
        this.activeType = type
        this.getAnalysisBarData()
      },
      changeTab(tab) {
        this.activeTab = tab
        this.getAnalysisBarData()
      },
      configCloseTableDialog() {
        this.allTableComponentListShow = false
      },
      configCloseRoomDialog() {
        this.roomInfoManagementShow = false
      },
      roomInfoManagementChange(type) {
        if (type === 'room') {
          Object.assign(this.roomInfo, {
            title: '门诊急诊综合楼综合楼 > 1F',
            modelCode: 'BJSJTYY0100101066',
            localtion: '0100101066',
            ssmType: 6,
            ssmCodes: '#,1574997196057620481,1574997196330250241,1574997196833566721,1574997196959395842,1574997211278749698'
          })
        } else {
          Object.assign(this.roomInfo, {
            title: '门诊急诊综合楼综合楼 > B1',
            modelCode: 'BJSJTYY0100103',
            localtion: '0100103',
            ssmType: 5,
            ssmCodes: '#,1574997196057620481,1574997196330250241,1574997196833566721,1574997197185888258'
          })
        }
        this.collapseShow = false
        if (this.collapseFlag) {
          this.collapseFlag = !this.collapseFlag
          this.$refs.collapseHeight.style.width = '0'
          this.$refs.collapseHeight.style.padding = '0'
        }
        if (this.roomInfoManagementShow) {
          this.$refs.roomInfoManagement.initRoomInfo()
        } else {
          this.roomInfoManagementShow = true
        }
      },
      spaceInfoChange(type) {
        if (type === 'room') {
          Object.assign(this.roomInfo, {
            title: '门诊急诊综合楼综合楼 > B3',
            modelCode: 'BJSJTYY0100101056',
            localtion: '0100101056',
            ssmType: 6,
            ssmCodes: '#,1574997196057620481,1574997196330250241,1574997196833566721,1574997196959395842,1574997219298258945'
          })
        } else {
          Object.assign(this.roomInfo, {
            title: '门诊急诊综合楼综合楼 > 9F',
            modelCode: 'BJSJTYY0100112',
            localtion: '0100112',
            ssmType: 5,
            ssmCodes: '#,1574997196057620481,1574997196330250241,1574997196833566721,1574997197995388929'
          })
        }
        this.collapseShow = false
        if (this.collapseFlag) {
          this.collapseFlag = !this.collapseFlag
          this.$refs.collapseHeight.style.width = '0'
          this.$refs.collapseHeight.style.padding = '0'
        }
        if (this.roomInfoManagementShow) {
          this.$refs.roomInfoManagement.initRoomInfo()
        }
        this.roomInfoManagementShow = true
      },
      // 打开中屏table弹窗
      showCenterTableDialog() {
        // this.localtion = '0100111037'
        // this.ssmType = '6'
        // this.tagSsmCodes = '#,1574997196330250241,1574997196833566721,1574997197915697154,1574997198909747201'
        const type = this.$refs.roomInfoManagement.activeType
        const filterComponent = this.centerTableDialogTypeList.find((e) => e.type === type)
        this.centerTableComponent = filterComponent.component
        this.centerTableDialogShow = true
        this.hazardType = filterComponent.type || ''
        this.$nextTick(() => {
          setTimeout(() => {
            // 巡检单独处理
            if (this.centerTableComponent === 'ipasPointRecord') {
              this.$refs[this.centerTableComponent].getPointDetailTableList()
            } else if (this.centerTableComponent === 'collectRecord') {
              this.$refs[this.centerTableComponent].getDepartMedicalWasteTableList()
            } else if (this.centerTableComponent === 'hazardList') {
              this.$refs[this.centerTableComponent].getWorkOrderTableData({ placeIds: this.localtion })
            } else if (this.centerTableComponent === 'workOrderCenterList') {
              this.$refs[this.centerTableComponent].getWorkOrderTableData()
            }
          }, 250)
        })
        try {
          window.chrome.webview.hostObjects.sync.bridge.ShowTable(this.centerTableDialogShow)
        } catch (error) {}
        // this.configCloseCenterTableDialog()
      },
      configCloseCenterTableDialog() {
        this.centerTableDialogShow = false
        try {
          window.chrome.webview.hostObjects.sync.bridge.ShowTable(this.centerTableDialogShow)
        } catch (error) {}
      },
      retrospectCloseDialog() {
        this.ipasPointDetailShow = false
        // this.ipasPointRecordShow = false
        try {
          // window.chrome.webview.hostObjects.sync.bridge.ShowTable(false)
          window.chrome.webview.hostObjects.sync.bridge.ShowDialog(false)
        } catch (error) {}
      },
      goTo(path, query) {
        this.$router.push({
          path,
          query
        })
      }
    }
  }
  </script>
  
  <style lang="scss" scoped>
  .content {
    width: 100%;
    height: 100%;
    position: relative;
    .center-center {
      width: 100%;
      height: 100%;
      display: flex;
      align-items: center;
      justify-content: center;
      color: #4d5880;
      font-size: 16px;
    }
    .sham-content {
      pointer-events: none;
    }
    .circle-btn {
      position: absolute;
      top: calc(3% + 25px);
      right: 0;
      width: 26px;
      height: 26px;
      cursor: pointer;
      margin: auto 0;
      margin-right: 10px;
      z-index: 2;
    }
    .circle-btn-top {
      background: url('~@/assets/images/center/btn-fold.png') no-repeat;
      background-size: 100% 100%;
    }
    .circle-btn-bottom {
      width: 44px;
      height: 44px;
      background: url('~@/assets/images/center/btn-unfold.png') no-repeat;
      background-size: 100% 100%;
    }
    .top_type {
      width: 100%;
      margin: 0 auto;
      padding: 15px 0;
      background-color: rgba(1, 11, 59, 0.5);
      display: flex;
      justify-content: space-evenly;
      flex-wrap: wrap;
      .top_type_item {
        width: 25%;
        margin: 3px 2%;
        height: 2.5rem;
        line-height: 2.5rem;
        text-align: center;
        font-size: 14px;
        font-family: PingFang-SC-Medium, PingFang-SC;
        color: #93bdff;
        background: url('~@/assets/images/center/block-bg.png') no-repeat;
        background-size: 100% 100%;
        cursor: pointer;
      }
      .activeType {
        background: url('~@/assets/images/center/block-bg-active.png') no-repeat;
        background-size: 100% 100%;
        color: #ffe3a6;
      }
    }
    .left-content {
      position: absolute;
      top: 0;
      left: 7%;
      width: 12%;
      height: 60%;
      max-height: 75%;
      margin-top: 6%;
      background: url('~@/assets/images/center/bg-mask-small.png') no-repeat;
      background-size: 100% 100%;
      box-sizing: border-box;
      padding: 10px 10px 20px 10px;
      .top_type_item {
        width: 40%;
      }
      .bottom_checkBox {
        width: 100%;
        height: calc(100% - 5rem);
        padding: 0 10px;
        box-sizing: border-box;
        overflow-y: auto;
        ::v-deep .el-checkbox {
          display: block;
          margin: 10px 0;
          .el-checkbox__label {
            color: #fff;
          }
        }
      }
    }
    .right-content {
      position: absolute;
      top: 0;
      right: 0;
      width: 22%;
      height: 100%;
      margin-top: 2%;
      // height: auto;
      max-height: 95%;
      background: url('~@/assets/images/center/bg-space-mask.png') no-repeat;
      background-size: 100% 100%;
      box-sizing: border-box;
      padding: 10px 10px 20px 10px;
      transition: width 0.3s linear;
      overflow: hidden;
      .top_title {
        height: 2.5rem;
        line-height: 2.5rem;
        padding-left: 0.9375rem;
        // display: flex;
        // justify-content: space-between;
        .dialog-title {
          color: #fff;
          font-family: PingFangSC-Medium, PingFang SC;
        }
  
        .dialog-title::before {
          content: '';
          display: inline-block;
          width: 2px;
          height: 16px;
          background: #ffe3a6;
          margin-right: 10px;
          vertical-align: middle;
        }
      }
      .room-dept-content {
        height: calc(100% - 2.5rem);
      }
      .top_statistics {
        height: 25%;
        padding: 10px 50px;
        display: flex;
        justify-content: space-around;
        flex-direction: column;
        background-color: rgba(1, 11, 59, 0.5);
        margin-bottom: 10px;
        > div {
          display: flex;
          justify-content: space-between;
          color: #fff;
          span:last-child {
            flex: 1;
            padding-left: 50px;
            font-size: 15px;
            color: #ffe3a6;
            font-family: PingFangSC-Medium, PingFang SC;
          }
          img {
            vertical-align: top;
            margin-right: 5px;
            width: 18px;
            height: 18px;
          }
        }
      }
      .center_tab {
        padding-bottom: 15px;
        text-align: center;
        background-color: rgba(1, 11, 59, 0.5);
        span {
          display: inline-block;
          width: fit-content;
          padding: 0 15px;
          height: 25px;
          font-size: 14px;
          text-align: center;
          line-height: 26px;
          color: #7eaef9;
          cursor: pointer;
        }
        span:hover,
        .activeTab {
          background: url('~@/assets/images/center/light-yellow.png') no-repeat;
          background-size: 100% 100%;
          color: #ffe3a6;
        }
        i {
          color: #7eaef9;
        }
      }
      .bottom_echarts {
        height: calc(75% - 140px);
        background-color: rgba(1, 11, 59, 0.5);
        #analysisBarEcharts {
          width: 100%;
          height: 100%;
          z-index: 2;
        }
      }
    }
  }
  </style>
  