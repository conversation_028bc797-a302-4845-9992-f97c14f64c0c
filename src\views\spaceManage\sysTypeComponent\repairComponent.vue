<template>
  <div class="repairComponent">
    <ModuleCard title="维修统计" :floorName="!['Kongtiao', 'Electricity'].includes(roomData.tabName) ? roomData.title : ''" class="work-order-stat">
      <div slot="content" style="height: 100%">
        <div class="work-order-type">
          <div class="search-data">
            <el-dropdown trigger="click" @command="dataTypeCommand">
              <span class="el-dropdown-link"> {{ dataTypeList.find((v) => v.value == btnType)?.name ?? '' }} <i class="el-icon-caret-bottom"></i> </span>
              <el-dropdown-menu slot="dropdown">
                <el-dropdown-item v-for="item in dataTypeList" :key="item.value" :command="item.value" :class="{ isBjxl: btnType == item.value }">{{ item.name }}</el-dropdown-item>
              </el-dropdown-menu>
            </el-dropdown>
            <el-date-picker
              v-model="datePickerData"
              class="datePickerInput"
              popper-class="date-style"
              unlink-panels
              type="daterange"
              :picker-options="{
                firstDayOfWeek: 1
              }"
              value-format="yyyy-MM-dd HH:mm:ss"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              @change="dataPickerValChange()"
              @focus="setWPFBgShow()"
              @blur="setWPFBgHide()"
            >
            </el-date-picker>
          </div>
          <div class="box-content">
            <div style="width: 100%; height: 100%" class="dd">
              <div class="tj-item">
                <div class="u-item">
                  <span>{{ btnType == 5 ? '' : dataTypeList[btnType - 1].name }}工单数&ensp;</span>
                  <span class="yello-color">{{ orderStatistics?.allOrderCount || 0 }}&ensp;</span>
                  <span>件</span>
                </div>
                <div class="d-item">
                  <span>同比</span>
                  <img v-if="orderStatistics?.allOrderTB?.startsWith('-')" src="@/assets/images/xia.png" />
                  <img v-else-if="orderStatistics?.allOrderTB?.startsWith('0%')" src="@/assets/images/flat.png" />
                  <img v-else src="@/assets/images/shang.png" />
                  <!-- <span :class="orderStatistics?.allOrderTB?.startsWith('-') ? 'green-color' : 'red-color'">{{ orderStatistics?.allOrderTB }}</span> -->
                  <span :class="orderStatistics?.allOrderTB?.startsWith('-') ? 'green-color' : orderStatistics?.allOrderTB?.startsWith('0%') ? '' : 'red-color'">{{ orderStatistics?.allOrderTB }}</span>
                </div>
              </div>
              <div class="tj-item">
                <div class="u-item">
                  <span>未完成数&ensp;</span>
                  <span class="yello-color">{{ orderStatistics?.unFinishOrderCount || 0 }}&ensp;</span>
                  <span>件</span>
                </div>
                <div class="d-item">
                  <span>同比</span>
                  <img v-if="orderStatistics?.unfinishOrderTB?.startsWith('-')" src="@/assets/images/xia.png" />
                  <img v-else-if="orderStatistics?.unFinishOrderTB?.startsWith('0%')" src="@/assets/images/flat.png" />
                  <img v-else src="@/assets/images/shang.png" />
                  <!-- <span :class="orderStatistics?.unfinishOrderTB?.startsWith('-') ? 'green-color' : 'red-color'">{{ orderStatistics?.unfinishOrderTB }}</span> -->
                  <span :class="orderStatistics?.unfinishOrderTB?.startsWith('-') ? 'green-color' : orderStatistics?.unfinishOrderTB?.startsWith('0%') ? '' : 'red-color'">{{
                orderStatistics?.unfinishOrderTB
                  }}</span>
                </div>
              </div>
              <div class="tj-item">
                <div class="u-item">
                  <span>挂单数&ensp;</span>
                  <span class="yello-color">{{ orderStatistics?.placeCount || 0 }}&ensp;</span>
                  <span>件</span>
                </div>
                <div class="d-item">
                  <span>同比</span>
                  <img v-if="orderStatistics?.placeOrderTB?.startsWith('-')" src="@/assets/images/xia.png" />
                  <img v-else-if="orderStatistics?.placeOrderTB?.startsWith('0%')" src="@/assets/images/flat.png" />
                  <img v-else src="@/assets/images/shang.png" />
                  <!-- <span :class="orderStatistics?.placeOrderTB?.startsWith('-') ? 'green-color' : 'red-color'">{{ orderStatistics?.placeOrderTB }}</span> -->
                  <span :class="orderStatistics?.placeOrderTB?.startsWith('-') ? 'green-color' : orderStatistics?.placeOrderTB?.startsWith('0%') ? '' : 'red-color'">{{
                orderStatistics?.placeOrderTB
                  }}</span>
                </div>
              </div>
              <div class="tj-item">
                <div class="u-item">
                  <span>超时工单数&ensp;</span>
                  <span class="yello-color">{{ orderStatistics?.overTimeCount || 0 }}&ensp;</span>
                  <span>件</span>
                </div>
                <div class="d-item">
                  <span>同比</span>
                  <img v-if="orderStatistics?.overTimeTB?.startsWith('-')" src="@/assets/images/xia.png" />
                  <img v-else-if="orderStatistics?.overTimeTB?.startsWith('0%')" src="@/assets/images/flat.png" />
                  <img v-else src="@/assets/images/shang.png" />
                  <!-- <span :class="orderStatistics?.overTimeTB?.startsWith('-') ? 'green-color' : 'red-color'">{{ orderStatistics?.overTimeTB }}</span> -->
                  <span :class="orderStatistics?.overTimeTB?.startsWith('-') ? 'green-color' : orderStatistics?.overTimeTB?.startsWith('0%') ? '' : 'red-color'">{{ orderStatistics?.overTimeTB }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="work-order-trend">
          <div class="box-title">
            <div><span></span>工单趋势（件）</div>
            <span></span>
          </div>
          <div class="box-content">
            <div v-if="!workOderTrendShow" class="center-center">暂无数据</div>
            <div v-else class="trend-box" style="width: 100%; height: 100%">
              <div class="checkbox-contain">
                <el-checkbox v-model="checkAll" fill="#52FFFC" :indeterminate="isIndeterminate" @change="changeCheckAll">全部工单</el-checkbox>
                <el-checkbox-group v-model="checkList" fill="#52FFFC" @change="changeCheckBox">
                  <div v-for="(item, index) in workOrderTypeList.slice(0, 2)" :key="index">
                    <el-checkbox :key="item.code" :label="item.name"></el-checkbox>
                  </div>
                </el-checkbox-group>
                <img src="@/assets/images/checkbox_add.png" @click="showPanel = !showPanel" />
                <div v-show="showPanel" class="panel">
                  <el-checkbox v-model="checkAll" fill="#52FFFC" :indeterminate="isIndeterminate" @change="changeCheckAll">全部工单</el-checkbox>
                  <el-checkbox-group v-model="checkList" fill="#52FFFC">
                    <el-checkbox v-for="item in workOrderTypeList" :key="item.code" :label="item.name"></el-checkbox>
                  </el-checkbox-group>
                </div>
              </div>
              <div id="workOdertTrendEcharts"></div>
            </div>
          </div>
        </div>
      </div>
    </ModuleCard>
    <ModuleCard v-if="roomData.ssmType != 5" title="维修排名" class="work-order-paiming">
      <div slot="content" style="height: 100%">
        <div class="box-content box-content2 scroll-box">
          <div v-for="(item, index) in sortTypeData" :key="index" class="jindutiao" @click="changeActivePro(item, index)">
            <span class="progress-label">{{ item.name }}</span>
            <el-progress
              :percentage="(item.count > 100 ? 100 : item.count) || 0"
              :color="[activeProgress == index ? '#f6de88' : '#0A84FF']"
              :show-text="false"
              define-back-color="#394257"
            ></el-progress>
            <span class="progress-label">{{ item.count }}个</span>
          </div>
        </div>
      </div>
    </ModuleCard>
    <ModuleCard v-else title="标签显示" class="work-order-tag">
      <div slot="content" style="height: 100%">
        <div class="box-content box-content2 scroll-box pipeline">
          <el-checkbox v-model="checkTagAll" :indeterminate="isTagIndeterminate" @change="changeCheckTagAll">全选</el-checkbox>
          <el-checkbox-group v-model="checkTagList" class="middle-xz" @change="changeTagCheckBox">
            <div v-for="(item, index) in sortTypeData" :key="index" class="fiex-all">
              <el-checkbox :key="item.code" :label="item.code" :value="item.code" class="checkbox"></el-checkbox>
              <div class="fiex-div-xz"></div>
              <div class="fiex-div-font">{{ item.name }}</div>
            </div>
          </el-checkbox-group>
        </div>
      </div>
    </ModuleCard>
    <ModuleCard title="工单状态" :hasExpand="true" class="work-order-table" @emit-expand="allTableChange('ioms')">
      <div slot="title-right" class="icon-box">
        <svg-icon name="right-filter" @click="showCheckGroup = !showCheckGroup" />
        <div v-show="showCheckGroup" class="panel-s">
          <el-checkbox-group v-model="checkTypeList" fill="#52FFFC" @change="checkBoxChanged">
            <el-checkbox v-for="item in flowList" :key="item.value" :label="item.label"></el-checkbox>
          </el-checkbox-group>
        </div>
      </div>
      <div slot="content" style="height: 100%">
        <div class="box-content box-content2" style="padding: 5px 0px">
          <div class="statistics-top">
            <div v-for="(item, index) in statisticsData" :key="index">
              <p>{{ item.name }}</p>
              <p class="green-font">{{ item.value + (item?.unit ?? '') }}</p>
            </div>
          </div>
          <el-table
            v-loading="tableLoading"
            v-el-table-infinite-scroll="tableLoadEvent"
            class="table-center-transfer"
            :data="tableData"
            height="calc(100% - 50px)"
            :cell-style="{ padding: ' 8px', backgroundColor: 'transparent', border: 'none', padding: '3px' }"
            :header-cell-style="{ background: '#8591CE26!important', color: '#8BDDF5FF', padding: '4px 8px', fontWeight: 'bold' }"
            style="width: 100%"
            element-loading-background="rgba(0, 0, 0, 0.2)"
            @row-dblclick="selectConfigRowData"
          >
            <el-table-column v-for="(column, index) in tableColumn" :key="index" :prop="column.prop" :label="column.label" :min-width="column.minWidth" show-overflow-tooltip>
              <template slot-scope="scope">
                <table-render v-if="column.render" :sc="scope" :row="scope.row" :render="column.render"></table-render>
                <div v-else-if="!column.formatter">
                  {{ scope.row[column.prop] }}
                </div>
                <div v-else>
                  {{ column.formatter(scope.row) }}
                </div>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>
    </ModuleCard>
    <template v-if="allTableComponentListShow">
      <allTableComponentList ref="allTableComponentList" :dialogData="tableCompenentData" :dialogShow="allTableComponentListShow" @configCloseDialog="configCloseTableDialog"></allTableComponentList>
    </template>
    <template v-if="workOrderDetailCenterShow">
      <el-dialog v-dialogDrag :modal="false" :visible.sync="workOrderDetailCenterShow" custom-class="detailDialog main" :close-on-click-modal="false" :before-close="workOrderDetailCloseDialog">
        <template slot="title">
          <span class="dialog-title">后勤设备（{{ iomsDetailObj.flowtype }}）</span>
        </template>
        <workOrderDetailList :rowData="iomsDetailObj" />
      </el-dialog>
    </template>
  </div>
</template>

<script lang="jsx">
import moment from 'moment'
moment.locale('zh-cn', {week: {dow: 1}})
import allTableComponentList from '../components/allTableComponentList.vue'
import workOrderDetailList from '@views/normalMode/rightScreen/components/workOrderDetailList.vue'
import { dialogTypeList, iomsWorkOrderParams } from '@/assets/common/dict.js'
import tableRender from '../components/tableRender.vue'
import icon_5 from '@/assets/images/icon-5.png'
import icon_3 from '@/assets/images/icon-3.png'
import icon_7 from '@/assets/images/icon-7.png'
import icon_6 from '@/assets/images/icon-6.png'
import icon_2 from '@/assets/images/icon-2.png'
import { GetWorkOrderListBySpaceRuiAn, GerReckonCount, getWorkOrderTypeStatisticsBySpaceRuiAn, getDeviceWorkOrderCount, getDeviceWorkOrderList, getDeviceWorkOrderChange } from '@/utils/spaceManage'
import * as echarts from 'echarts'

export default {
  name: 'repairComponent',
  components: {
    'table-render': tableRender,
    allTableComponentList,
    workOrderDetailList
  },
  props: {
    roomData: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      icon_2,
      icon_3,
      icon_5,
      icon_6,
      icon_7,
      workOderTrendShow: true, // 近12个月工单走势echarts是否显示
      allTableComponentListShow: false, // 一站式弹窗显示
      tableCompenentData: {}, // 一站式弹窗数据
      workOrderDetailCenterShow: false, // 工单详情弹窗显示
      iomsDetailObj: {}, // 工单详情弹窗数据
      dialogTypeList: dialogTypeList, // 过滤选中弹窗类型
      statisticsData: [
        {
          name: '工单总数',
          value: 0,
          field: 'all'
        },
        {
          name: '完成率',
          value: 0,
          field: 'completionRate'
        },
        {
          name: '平均响应',
          value: 0,
          field: 'response'
        },
        {
          name: '平均完工',
          value: 0,
          field: 'finishTime'
        }
      ], // 统计数据
      tableData: [], // 表格数据
      tableColumn: [
        {
          prop: 'workTypeName',
          label: '工单类型'
        },
        {
          prop: 'itemServiceName',
          label: '服务事项'
        },
        {
          prop: 'designatePersonName',
          label: '服务人员'
        },
        {
          prop: 'flowtype',
          label: '工单状态',
          render: (h, row) => {
            return (
              <div>
                {row.row.flowcode == '5' && (
                  <div class="status-box">
                    <img class="table-icon" src={icon_5} />
                    <span style="color:#61E29D">{row.row.flowtype}</span>
                  </div>
                )}
                {row.row.flowcode == '3' && (
                  <div class="status-box">
                    <img class="table-icon" src={icon_3} />
                    <span style="color:#3CC1FF">{row.row.flowtype}</span>
                  </div>
                )}
                {(row.row.flowcode == '7' || row.row.flowcode == '1' || row.row.flowcode == '4') && (
                  <div class="status-box">
                    <img class="table-icon" src={icon_7} />
                    <span style="color:#D25F00">{row.row.flowtype}</span>
                  </div>
                )}
                {row.row.flowcode == '6' && (
                  <div class="status-box">
                    <img class="table-icon" src={icon_6} />
                    <span style="color:#86909C">{row.row.flowtype}</span>
                  </div>
                )}
                {row.row.flowcode == '2' && (
                  <div class="status-box">
                    <img class="table-icon" src={icon_2} />
                    <span style="color:#FF2D55">{row.row.flowtype}</span>
                  </div>
                )}
                {row.row.flowcode != '1' &&
                  row.row.flowcode != '2' &&
                  row.row.flowcode != '3' &&
                  row.row.flowcode != '4' &&
                  row.row.flowcode != '5' &&
                  row.row.flowcode != '6' &&
                  row.row.flowcode != '7' && (
                  <div class="status-box">
                    <span>{row.row.flowtype}</span>
                  </div>
                )}
              </div>
            )
          }
        }
      ], // 表格列
      tableLoading: false, // 表格加载
      iomsWorkOrderParams: iomsWorkOrderParams, // 查询列表参数
      checkAll: false, // 全选
      isIndeterminate: false,
      checkList: [],
      dataTypeList: [
        { value: 1, name: '今日' },
        { value: 2, name: '本周' },
        { value: 3, name: '本月' },
        { value: 4, name: '本年' },
        { value: 5, name: '自定义' }
      ],
      datePickerData: [moment().startOf('week').format('YYYY-MM-DD') + ' 00:00:00', moment().endOf('week').format('YYYY-MM-DD') + ' 23:59:59'],
      regex: /^(\d{8})\s(.*)/,
      btnType: '2',
      orderStatistics: {},
      sortTypeData: [],
      activeProgress: null,
      startTime: '',
      endTime: '',
      showPanel: false,
      workOrderTypeList: [],
      checkedCodeTypeList: [],
      showCheckGroup: false,
      checkTypeList: ['全部'],
      flowList: [
        {
          label: '全部',
          value: ''
        },
        {
          label: '未受理',
          value: '1'
        },
        {
          label: '暂存',
          value: '7'
        },
        {
          label: '未派工',
          value: '2'
        },
        {
          label: '已派工',
          value: '3'
        },
        {
          label: '已挂单',
          value: '4'
        },
        {
          label: '已完工',
          value: '5'
        },
        {
          label: '已取消',
          value: '6'
        }
      ],
      checkTagAll: false,
      isTagIndeterminate: false,
      checkTagList: [],
      tagGroupList: [],
      pagination: {
        total: 0,
        pageSize: 20,
        pageNo: 1
      }
    }
  },
  computed: {
    btnTypeText() {
      switch (this.btnType) {
        case '1':
          return '今天'
        case '2':
          return '本周'
        case '3':
          return '本月'
        case '4':
          return '本年'
        case '5':
          return '自定义'
        default:
          return '今天'
      }
    }
  },
  watch: {
    btnType: {
      handler(val) {
        // 5为自定义时间 选择时间后才调用接口
        if (val != 5) {
          this.initData()
        }
      },
      deep: true
    },
    // 监听模型层级数据变化
    roomData: {
      handler(val) {
        this.initData()
      },
      deep: true
    }
  },
  mounted() {
    // 服务工单动态
    this.initData()
  },
  methods: {
    // 初始化调用
    initData() {
      this.sendDataToWPFEvent('time')
      this.pagination.pageNo = 1
      // 有设备id才调用接口 无设备id不调用
      if (this.roomData.deviceId) {
        this.getDeviceWorkOrderCount()
        this.getDeviceWorkOrderList()
        this.getWorkOrderListBySpaceRuiAn()
        this.gerReckonCount()
      } else {
        this.orderStatistics = {}
        this.sortTypeData = []
        this.statisticsData.forEach((item) => {
          item.value = 0
        })
        this.tableData = []
        this.checkList = []
      }
    },
    setWPFBgShow() {
      window.chrome.webview.hostObjects.sync.bridge.DateMask(true)
    },
    setWPFBgHide() {
      window.chrome.webview.hostObjects.sync.bridge.DateMask(false)
    },
    // 时间类型切换
    dataTypeCommand(val) {
      const date = {
        1: [moment().format('YYYY-MM-DD') + ' 00:00:00', moment().format('YYYY-MM-DD') + ' 23:59:59'],
        2: [moment().startOf('week').format('YYYY-MM-DD') + ' 00:00:00', moment().endOf('week').format('YYYY-MM-DD') + ' 23:59:59'],
        3: [moment().startOf('month').format('YYYY-MM-DD') + ' 00:00:00', moment().endOf('month').format('YYYY-MM-DD') + ' 23:59:59'],
        4: [moment().startOf('year').format('YYYY-MM-DD') + ' 00:00:00', moment().endOf('year').format('YYYY-MM-DD') + ' 23:59:59'],
        5: []
      }
      this.datePickerData = date[val]
      this.btnType = val
      if (val != 5) {
        this.sendDataToWPFEvent('time')
      }
    },
    // 改变选中维修排名的进度条
    changeActivePro(item, index) {
      this.activeProgress = this.activeProgress == index ? null : index
      this.sendDataToWPFEvent('params', {
        deviceIds: item.deviceId
      })
    },
    dataPickerValChange() {
      this.btnType = '5'
      this.startTime = this.datePickerData[0]
      this.endTime = this.datePickerData[1]
      this.initData()
      this.sendDataToWPFEvent('time')
    },
    changeCheckAll(val) {
      this.checkList = val ? Array.from(this.workOrderTypeList, ({ name }) => name) : []
      this.isIndeterminate = val
      this.getWorkOderTrend()
    },
    changeCheckBox(value) {
      const checkedCount = value.length
      this.checkAll = checkedCount === this.workOrderTypeList.length
      this.isIndeterminate = checkedCount > 0 && checkedCount < this.workOrderTypeList.length
      this.getWorkOderTrend()
    },
    sendDataToWPFEvent(type, params = {}) {
      let sendParams = {}
      // time 用vue实例绑定的全局数据 params表示用传入的参数替换
      if (type == 'time') {
        sendParams = {
          startTime: this.startTime,
          endTime: this.endTime,
          btnType: this.btnType
        }
      } else {
        sendParams = params
      }
      this.$emit('sendWpfData', sendParams)
    },
    // 后勤设备维修统计
    getDeviceWorkOrderCount() {
      const params = {
        btnType: this.btnType,
        startTime: this.startTime,
        endTime: this.endTime,
        deviceId: this.roomData.deviceId,
        spatialId: this.roomData?.ssmCodes?.split(',').at(-1)
      }
      getDeviceWorkOrderCount(params).then((res) => {
        if (res.data.code == '200') {
          this.orderStatistics = res.data.data
        }
      })
    },
    // 维修排名
    getDeviceWorkOrderList() {
      getDeviceWorkOrderList({
        btnType: this.btnType,
        startTime: this.startTime,
        endTime: this.endTime,
        deviceId: this.roomData.deviceId,
        spatialId: this.roomData?.ssmCodes?.split(',').at(-1)
      }).then((res) => {
        if (res.data.code == '200') {
          if (res.data.data && res.data.data.length) {
            this.workOrderTypeList = res.data.data.map((e) => {
              e.count = Number(e.count) || 0
              return {
                name: e.name,
                code: e.code
              }
            })
            // 按照工单数量排序
            this.sortTypeData = res.data.data.sort((a, b) => {
              return b.count - a.count
            })
            this.checkAll = true
            this.checkList = Array.from(this.workOrderTypeList, ({ name }) => name)
            this.isIndeterminate = true
          } else {
            this.sortTypeData = []
          }
          this.getWorkOderTrend()
        }
      })
    },
    // 工单走势
    getWorkOderTrend() {
      const codeList = []
      this.workOrderTypeList.forEach((item) => {
        this.checkList.forEach((e) => {
          if (item.name == e) {
            codeList.push(item.code)
          }
        })
      })
      let params = {
        btnType: this.btnType,
        spatialId: this.roomData?.ssmCodes?.split(',').at(-1),
        startTime: this.startTime,
        endTime: this.endTime,
        deviceId: this.roomData.deviceId,
        assetSubcategoryCode: codeList.join(',')
      }
      getDeviceWorkOrderChange(params).then((res) => {
        if (res.data.code == '200') {
          if (!res.data.data.xAxisData || res.data.data.xAxisData.length == 0) {
            this.workOderTrendShow = false
          } else {
            this.workOderTrendShow = true
            if (this.btnType == '2' || this.btnType == '3' || this.btnType == '5') {
              res.data.data.xAxisData.forEach((item, index) => {
                res.data.data.xAxisData[index] = item.substring(5)
              })
            }
            this.workOderTrendEchart(res.data.data.xAxisData, res.data.data.seriesData)
          }
        }
      })
    },
    // 工单走势echarts
    workOderTrendEchart(dateArr, echartsData) {
      const getchart = echarts.init(document.getElementById('workOdertTrendEcharts'))
      var color = ['rgba(31, 250, 255, 0.3)', 'rgba(255, 227, 166, 0.28)', 'rgba(147, 130, 255, 0.26)', 'rgba(0, 248, 114, 0.24)', 'rgba(46, 119, 251, 0.3)']
      var borderColor = ['rgba(31, 250, 255, 1)', 'rgba(255, 227, 166, 1)', 'rgba(147, 130, 255, 1)', 'rgba(0, 248, 114, 1)', 'rgba(46, 119, 251, 1)']
      const seriesObj = []
      echartsData.forEach((item, index) => {
        const randomRgbColor = this.$tools.randomRgbColor('array')
        seriesObj.push({
          name: item.name,
          type: 'line',
          smooth: false,
          showAllSymbol: true,
          symbol: 'circle',
          symbolSize: 0,
          itemStyle: {
            color: '#3eb5dd',
            borderColor: '#f1f1f1',
            borderWidth: 1
          },
          lineStyle: {
            normal: {
              width: 2,
              color: borderColor[index] ?? randomRgbColor[1]
            }
          },
          data: item.value
        })
      })
      let dataZoom = [
        {
          type: 'slider', // 滑动条型数据区域缩放组件
          realtime: true, // 拖动时，是否实时更新系列的视图。如果设置为 false，则只在拖拽结束的时候更新。
          // start: 0, //数据窗口范围的起始百分比。范围是：0 ~ 100。表示 0% ~ 100%。
          // end: 10,  // 数据窗口范围的结束百分比。范围是：0 ~ 100
          height: 4,
          endValue: 5, // 数据窗口范围的结束数值。如果设置了 dataZoom-inside.end 则 endValue 失效
          fillerColor: 'rgba(17, 100, 210, 0.4)', // 滚动条颜色
          borderColor: 'rgba(17, 100, 210, 0.1)',
          handleSize: 0, // 两边手柄尺寸
          showDetail: false, // 拖拽时是否展示滚动条两侧的文字
          bottom: '8%', // 组件离容器上侧的距离
          zoomLock: true // 是否只平移不缩放
          // moveOnMouseMove:true, //开启鼠标移动窗口平移
          // zoomOnMouseWheel :true, //开启鼠标滚轮缩放
        },
        {
          type: 'inside', // 内置型数据区域缩放组件
          show: false,
          // start: 0,
          // end: 10,
          endValue: 11, // 最多12个
          zoomOnMouseWheel: false, // 关闭鼠标滚轮缩放
          moveOnMouseWheel: true, // 开启鼠标滚轮窗口平移
          moveOnMouseMove: true // 开启鼠标移动窗口平移
        }
      ]
      const option = {
        tooltip: {
          trigger: 'axis',
          backgroundColor: '#0D2169',
          borderColor: '#727382',
          textStyle: {
            color: '#fff'
          },
          axisPointer: {
            // 坐标轴指示器，坐标轴触发有效
            type: 'shadow' // 默认为直线，可选为：'line' | 'shadow'
          }
        },
        dataZoom: dataZoom,
        legend: {
          show: false,
          data: Array.from(echartsData, ({ name }) => name),
          type: 'scroll',
          orient: 'horizontal',
          left: 'right',
          top: '0',
          itemGap: 16,
          textStyle: {
            color: '#B3C2DD'
          }
        },
        grid: {
          left: '2%',
          right: '0%',
          bottom: '15%',
          top: '5%',
          width: '90%',
          containLabel: true
        },
        xAxis: {
          type: 'category',
          axisLine: {
            show: true,
            lineStyle: {
              color: '#878EA9',
              width: 1,
              type: 'solid'
            }
          },
          axisTick: {
            show: false
          },
          axisLabel: {
            interval: 0
          },
          boundaryGap: false,
          data: dateArr
        },
        yAxis: {
          type: 'value',
          axisLabel: {
            show: true,
            textStyle: {
              color: '#878EA9'
            }
          },
          axisLine: {
            lineStyle: {
              type: 'solid',
              color: '#3862B7',
              width: '1'
            }
          },
          splitLine: {
            lineStyle: {
              color: '#314A89',
              width: 1,
              type: 'solid'
            }
          },
          axisTick: {
            show: false
          }
        },
        series: seriesObj
      }
      getchart.clear()
      getchart.setOption(option)
      // 随着屏幕大小调节图表
      window.addEventListener('resize', () => {
        getchart.resize()
      })
    },
    // 工单状态筛选过滤
    checkBoxChanged() {
      this.checkTypeList = [this.checkTypeList[this.checkTypeList.length - 1]]
      this.showCheckGroup = false
      const codeList = []
      this.flowList.forEach((item) => {
        if (this.checkTypeList[0] == item.label) {
          codeList.push(item.value)
        }
      })
      this.checkedCodeTypeList = codeList
      this.pagination.pageNo = 1
      this.getWorkOrderListBySpaceRuiAn()
      this.gerReckonCount()
    },
    // 服务工单动态
    gerReckonCount() {
      let showTimeType = ''
      if (this.btnType == '1') {
        showTimeType = '1'
      }
      if (this.btnType == '2') {
        showTimeType = '6'
      }
      if (this.btnType == '3') {
        showTimeType = '2'
      }
      if (this.btnType == '4') {
        showTimeType = '3'
      }
      if (this.btnType == '5') {
        showTimeType = '4'
      }
      const codeParams = {
        ...this.iomsWorkOrderParams,
        deviceSpatialId: this.roomData?.ssmCodes?.split(',').at(-1),
        workTypeCode: 17,
        showTimeType,
        startTime: this.startTime,
        endTime: this.endTime,
        deviceId: this.roomData.deviceId,
        flowcode: this.checkedCodeTypeList.join(',')
      }
      GerReckonCount(codeParams).then((res) => {
        if (res.data.success && res.data.body.data) {
          const data = res.data.body.data
          this.statisticsData.forEach((item) => {
            item.value = data[item.field]
          })
        }
      })
    },
    // 获取工单
    getWorkOrderListBySpaceRuiAn() {
      this.tableLoading = true
      let showTimeType = ''
      if (this.btnType == '1') {
        showTimeType = '1'
      }
      if (this.btnType == '2') {
        showTimeType = '6'
      }
      if (this.btnType == '3') {
        showTimeType = '2'
      }
      if (this.btnType == '4') {
        showTimeType = '3'
      }
      if (this.btnType == '5') {
        showTimeType = '4'
      }
      const codeParams = {
        ...this.iomsWorkOrderParams,
        workTypeCode: 17,
        deviceSpatialId: this.roomData?.ssmCodes?.split(',').at(-1),
        pageNo: this.pagination.pageNo,
        pageSize: this.pagination.pageSize,
        showTimeType,
        startTime: this.startTime,
        endTime: this.endTime,
        deviceId: this.roomData.deviceId,
        flowcode: this.checkedCodeTypeList.join(',')
      }
      GetWorkOrderListBySpaceRuiAn(codeParams).then((res) => {
        if (res.data.rows) {
          if (this.pagination.pageNo === 1) {
            this.tableData = []
          }
          this.tableData = this.tableData.concat(res.data.rows)
          this.pagination.total = res.data.total
        }
        this.tableLoading = false
      })
    },
    // 标签显示 全选事件
    changeCheckTagAll(val) {
      this.checkTagList = val ? Array.from(this.sortTypeData, ({ code }) => code) : []
      this.isTagIndeterminate = val
      this.setCheckDataToWpf()
    },
    // 标签显示 单选事件
    changeTagCheckBox(value) {
      const checkedCount = value.length
      this.checkTagAll = checkedCount === this.sortTypeData.length
      this.isTagIndeterminate = checkedCount > 0 && checkedCount < this.sortTypeData.length
      this.setCheckDataToWpf()
    },
    setCheckDataToWpf() {
      // 根据this.checkTagList获取this.sortTypeData对应的deviceId集合
      const deviceIdList = []
      this.sortTypeData.forEach((item) => {
        this.checkTagList.forEach((e) => {
          if (item.code == e) {
            deviceIdList.push(item.deviceId)
          }
        })
      })
      this.sendDataToWPFEvent('params', {
        deviceIds: deviceIdList.toString()
      })
    },
    tableLoadEvent() {
      if (this.pagination.total > this.pagination.pageNo * this.pagination.pageSize) {
        this.pagination.pageNo++
        this.getWorkOrderListBySpaceRuiAn()
      }
    },
    allTableChange(type) {
      this.allTableComponentListShow = true
      Object.assign(this.tableCompenentData, {
        ...this.roomData,
        ...this.dialogTypeList.find((e) => e.type === type),
        height: 'calc(100% - 230px)'
      })
    },
    configCloseTableDialog() {
      this.allTableComponentListShow = false
    },
    // 双击 以及操作点击事件
    selectConfigRowData(row) {
      this.iomsDetailObj = row
      this.workOrderDetailCenterShow = true
      try {
        window.chrome.webview.hostObjects.sync.bridge.GetSwichFloorIsShow(true)
        window.chrome.webview.hostObjects.sync.bridge.ShowDialog(true)
      } catch (error) {}
    },
    workOrderDetailCloseDialog() {
      this.workOrderDetailCenterShow = false
      try {
        window.chrome.webview.hostObjects.sync.bridge.GetSwichFloorIsShow(false)
        window.chrome.webview.hostObjects.sync.bridge.ShowDialog(false)
      } catch (error) {}
    }
  }
}
</script>

<style lang="scss" scoped>
// 隐藏滚动条
::-webkit-scrollbar {
  display: none;
}
.repairComponent {
  width: 100%;
  height: 100%;
  overflow-y: auto;
  .work-order-stat {
    width: 100%;
    height: calc(54%);
    .work-order-type {
      width: 100%;
      height: calc(45%);
      padding-bottom: 0px;
      .search-data {
        display: flex;
        background: rgba(133, 145, 206, 0.15);
        padding: 0px 10px;
        ::v-deep .el-dropdown {
          padding: 7px 6px;
          .el-dropdown-link {
            font-size: 14px;
            font-weight: 500;
            color: #8bddf5;
            line-height: 16px;
            position: relative;
            cursor: pointer;
          }
          .el-dropdown-link::after {
            content: '';
            position: absolute;
            width: 1px;
            height: 12px;
            background: rgba(133, 145, 206, 0.5);
            top: 50%;
            right: -6px;
            transform: translateY(-50%);
          }
        }
        ::v-deep .datePickerInput {
          flex: 1;
          padding: 8px 10px;
          height: 16px;
          box-sizing: content-box;
          background: none;
          border: none;
          .el-input__icon,
          .el-range-separator {
            line-height: 16px;
            color: #b0e3fa;
          }
          .el-range-input {
            background: none;
            color: #a4afc1;
          }
        }
      }
      .box-content {
        height: calc(100% - 32px);
      }
    }
    .work-order-trend {
      width: 100%;
      height: calc(55%);
    }
  }
  .work-order-paiming {
    height: calc(30%);
  }
  .work-order-tag {
    height: calc(30%);
    .pipeline {
      box-sizing: border-box;
      padding: 16px 0 10px 10px;
      height: calc(100% - 44px);
      ::v-deep .middle-xz {
        display: flex;
        flex-direction: column;
        max-height: calc(100% - 16px);
        overflow-y: auto;
        .fiex-all {
          display: flex;
          align-items: center;
          margin-top: 8px;
          .fiex-div-font {
            padding-left: 10px;
            height: 20px;
            font-size: 14px;
            font-family: PingFang SC-Medium, PingFang SC;
            font-weight: 500;
            color: #ffffff;
            line-height: 20px;
            text-overflow: ellipsis;
            white-space: nowrap;
            overflow: hidden;
          }

          .checkbox {
            width: 14px;
            height: 14px;

            .el-checkbox__input {
              .el-checkbox__inner {
                box-shadow: 0px 0px 3px 0px #78fff8;
                opacity: 1;
                border: 1px solid #52fffc;
              }
            }
          }
          .el-checkbox__label {
            display: none;
          }
        }
      }
    }
  }
  .work-order-table {
    width: 100%;
    height: calc(40%);
    .statistics-top {
      height: 50px;
      width: 100%;
      display: flex;
      justify-content: space-around;
      > div {
        width: max-content;
        height: 100%;
        box-sizing: border-box;
        display: flex;
        justify-content: space-evenly;
        flex-direction: column;
        p {
          text-align: center;
          font-size: 0.75rem;
          font-family: HarmonyOS Sans SC-Regular, HarmonyOS Sans SC;
          color: #ffffff;
        }
        .green-font {
          font-size: 0.975rem;
          color: #ffca64;
          font-family: HarmonyOS Sans SC-Bold, HarmonyOS Sans SC;
          // font-weight: bold;
        }
      }
    }
  }
  .box-title {
    height: 30px;
    line-height: 30px;
    font-size: 14px;
    font-family: PingFangSC-Medium, PingFang SC;
    color: #ffffff;
    display: flex;
    justify-content: space-between;
    :last-child {
      font-size: 14px;
      font-family: PingFang-SC-Medium, PingFang-SC;
      color: #7eaef9;
      cursor: pointer;
    }
    > div {
      > span {
        display: none;
        width: 2px;
        height: 16px;
        background: #ffe3a6;
        margin-right: 6px;
        vertical-align: middle;
      }
    }
  }
  .box-content {
    width: 100%;
    height: calc(100% - 30px);
    position: relative;
    box-sizing: border-box;
    padding: 10px 10px 0px 10px;
    .pie_background {
      left: 26%;
    }
    #workOdertTypeEcharts,
    #workOdertTrendEcharts {
      position: relative;
      width: 100%;
      height: calc(100% - 20px);
      z-index: 2;
    }
    ::v-deep .el-table {
      border: none !important;
      .el-table__header-wrapper {
        .cell {
          padding-left: 0;
          padding-right: 0;
          text-align: center;
          white-space: nowrap;
        }
      }
      .el-table__body-wrapper {
        td.el-table__cell div {
          overflow: hidden;
          white-space: nowrap;
          text-overflow: ellipsis;
        }
      }
      .el-table__body {
        tr {
          background: center;
        }
        td.el-table__cell,
        th.el-table__cell.is-leaf {
          border-right: 2px solid #0a164e;
          border-bottom: 2px solid #0a164e;
          background: rgba(56, 103, 180, 0.2);
          color: #fff;
        }
      }
    }
  }
  .box-content2 {
    height: calc(100%);
  }
  ::v-deep .detailDialog {
    width: 60%;
    height: 80vh;
    margin-top: 7vh !important;
    background-color: transparent !important;
    background-image: url('@/assets/images/table-bg.png');
    background-repeat: no-repeat;
    background-size: 100% 100%;
    border: none;
    pointer-events: auto;
    box-shadow: none;
    .el-dialog__body {
      padding: 10px 50px;
      height: calc(100% - 60px);
      max-height: 80vh;
    }
    .dialog-title {
      display: inline-block;
      width: 100%;
      text-align: center;
      transform: translateY(-6px);
      color: #cbdeed;
    }
    .dialog-title::before {
      display: none;
    }
    .el-dialog__headerbtn {
      transform: translateX(-36px);
      width: 25px;
      height: 25px;
      background-image: url('@/assets/images/close.png');
      background-repeat: no-repeat;
      background-size: 100% 100%;
      .el-dialog__close::before {
        display: none;
      }
    }
  }
}
.today {
  display: flex;
  align-items: center;
  position: relative;
}
.u-item {
  margin-bottom: 10px;
}
.d-item {
  font-size: 12px;
}
.tj-item {
  width: 48%;
  display: flex;
  flex-direction: column;
  justify-content: center;
}
.red-color {
  color: #ff2d55;
}
.yello-color {
  color: #ffca64;
  font-weight: bold;
}
.dd {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
}
.d-item img {
  width: 12px;
  margin: 0 6px;
  transform: translateY(2px);
}
.green-color {
  color: #61e29d;
}
.el-checkbox-group {
  display: flex;
  justify-content: end;
}
.leixing {
  display: flex;
  align-items: center;
}
.leixing > div {
  width: 52px;
  height: 26px;
  color: #8bddf5 !important;
  line-height: 26px;
  text-align: center;
  font-size: 14px;
  font-family: HarmonyOS Sans SC-Regular, HarmonyOS Sans SC;
  background: rgba(255, 255, 255, 0.1) linear-gradient(90deg, rgba(10, 132, 255, 0) 0%, rgba(10, 132, 255, 0) 100%);
  box-sizing: border-box;
  cursor: pointer;
}
.leixing > div:nth-child(2) {
  margin: 0 6px;
}
.jindutiao {
  display: flex;
  height: 30px;
  align-items: center;
  & > span {
    font-size: 14px;
  }
  ::v-deep .el-progress {
    width: 60%;
    margin: 0 16px;
    display: flex;
    align-items: center;
    .el-progress-bar__inner:hover {
      cursor: pointer;
      background-color: #f6de88 !important;
    }
  }
}
.box-title {
  background-image: url('@/assets/images/<EMAIL>');
  background-repeat: no-repeat;
  background-size: 100% 100%;
  padding-left: 16px;
}
.box-title > div:nth-child(1) {
  font-weight: 500;
}
.active-type {
  background: rgba(255, 255, 255, 0.1) linear-gradient(90deg, rgba(10, 132, 255, 0.4) 0%, rgba(10, 132, 255, 0) 100%);
  border: 1px solid #abf0ff;
}
.progress-label {
  opacity: 0.8;
  width: 16%;
}
::v-deep .el-checkbox__label {
  color: #fff !important;
}
::v-deep .el-checkbox__input.is-checked span.el-checkbox__inner {
  background: url('@/assets/images/<EMAIL>') !important;
  background-size: 100% 100% !important;
}
::v-deep span.el-checkbox__inner {
  border-color: #c5fff9 !important;
}
::v-deep .el-checkbox__input.is-checked + .el-checkbox__label {
  color: #8bddf5;
}
::v-deep .el-checkbox__input {
  .el-checkbox__inner {
    box-shadow: 0px 0px 3px 0px #78fff8;
    opacity: 1;
    border: 1px solid #52fffc;
    background: transparent;
  }
}
::v-deep .el-checkbox__inner::before {
  display: none;
}
.order-more {
  width: 24px;
  height: 24px;
  margin-right: 0 !important;
  margin-left: 8px;
}
.filter {
  width: 24px;
  height: 24px;
}
.icon-box {
  flex: 1;
  display: flex;
  justify-content: flex-end;
  align-items: center;
  & > svg {
    font-size: 24px;
    cursor: pointer;
  }
}
::v-deep div.el-table th.el-table__cell.is-leaf {
  border-bottom: none;
}
::v-deep .el-table__body-wrapper .el-table__row:hover td {
  background-color: rgba(255, 255, 255, 0.3) !important;
}
::v-deep .el-table {
  .el-table__body tr {
    cursor: pointer;
  }
}
.today-label {
  color: #fff;
}
.scroll-box {
  overflow: auto;
}
.trend-box {
  width: 100%;
  height: 100%;
  position: relative;
  .checkbox-contain {
    display: flex;
    justify-content: flex-end;
    & > .el-checkbox-group .el-checkbox {
      margin-left: 5px;
    }
    // position: relative;
    img {
      width: 18px;
      height: 18px;
      margin-left: 12px;
      cursor: pointer;
    }
    .panel {
      position: absolute;
      right: 0;
      top: 20px;
      background-color: #374b79;
      padding: 0 8px 8px;
      z-index: 9;
      max-height: calc(100% - 30px);
      overflow: auto;
      .el-checkbox-group {
        display: flex;
        flex-direction: column;
        align-items: flex-end;
      }
      .el-checkbox {
        width: 100%;
        display: flex;
        // justify-content: space-between;
        align-items: center;
        margin-right: 0;
        margin-top: 5px;
      }
    }
  }
}
::v-deep .panel-s {
  position: absolute;
  right: 0;
  top: 28px;
  background-color: #374b79;
  z-index: 9;
  height: 200px;
  overflow: auto;
  .el-checkbox-group {
    display: flex;
    flex-direction: column;
    align-items: flex-end;
    padding: 8px;
    margin-right: 0 !important;
    .el-checkbox {
      width: 100%;
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-right: 0;
      & + .el-checkbox {
        margin-top: 5px;
      }
      .el-checkbox__label {
        color: #a3a9c0 !important;
      }
    }
  }
}
.table-icon {
  width: 16px;
  margin-right: 3px;
}
.status-box {
  display: flex;
  align-items: center;
}
</style>
