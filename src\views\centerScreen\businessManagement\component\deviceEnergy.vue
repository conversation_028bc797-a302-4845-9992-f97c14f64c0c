<template>
  <div class="deviceEnergy">
    <el-dialog v-dialogDrag :modal="false" :visible.sync="show" custom-class="bg-dialog main" :close-on-click-modal="false" :before-close="closeDialog">
      <template slot="title">
        <span class="bg-dialog-title">{{ requestParams.measurePointName }}</span>
      </template>
      <div class="deviceEnergy-content" style="height: 100%">
        <el-tabs v-model="tabsActiveName" class="module-tabs">
          <el-tab-pane label="实时抄表" name="1">
            <div v-loading="chartLoading" class="foldLineChart" id="foldLineChart1"></div>
          </el-tab-pane>
          <el-tab-pane label="历史抄表" name="2">
            <div class="search-data">
              <el-dropdown trigger="click" @command="dataTypeCommand">
                <span class="el-dropdown-link"> {{ dataTypeList.find((v) => v.value == statisticalType)?.name ?? '' }} <i class="el-icon-caret-bottom"></i> </span>
                <el-dropdown-menu slot="dropdown">
                  <el-dropdown-item v-for="item in dataTypeList" :key="item.value" :command="item.value" :class="{ isBjxl: statisticalType == item.value }">{{ item.name }}</el-dropdown-item>
                </el-dropdown-menu>
              </el-dropdown>
              <el-date-picker
                v-model="statisticalDate"
                class="datePickerInput"
                popper-class="date-style"
                type="daterange"
                value-format="yyyy-MM-dd"
                :disabled="statisticalType != 5"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                @change="getPointHistoryList()"
              >
              </el-date-picker>
            </div>
            <div v-loading="chartLoading" class="foldLineChart" id="foldLineChart2"></div>
          </el-tab-pane>
        </el-tabs>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { GetPointSignalList, GetRealTimeCvDataByIdList, GetPointHistoryList } from '@/utils/energyConsumption'
import moment from 'moment'
import * as echarts from 'echarts'
moment.locale('zh-cn')
export default {
  name: 'deviceEnergy',
  props: {
    show: {
      type: Boolean,
      default: false
    },
    requestParams: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  data() {
    return {
      chartLoading: true,
      tabsActiveName: '1',
      statisticalType: 3, // 选中日期类型
      statisticalDate: [moment().startOf('year').format('YYYY-MM-DD') + ' 00:00:00', moment().endOf('year').format('YYYY-MM-DD') + ' 23:59:59'], // 选中日期
      dataTypeList: [
        { value: 1, name: '今日' },
        { value: 2, name: '本周' },
        { value: 3, name: '本月' },
        { value: 4, name: '本年' },
        { value: 5, name: '自定义' }
      ],
      signalId: ''
    }
  },
  watch: {
    tabsActiveName(val) {
      if (val == 1) {
        this.getRealTimeCvDataByIdList()
      } else {
        this.getPointHistoryList()
      }
    }
  },
  mounted() {
    setTimeout(() => {
      this.getPointSignalList()
    }, 1000)
  },
  methods: {
    // 通过pointId获取表记信号列表
    getPointSignalList() {
      GetPointSignalList({ pointId: this.requestParams.measurePointId }, this.requestParams.kgceToken).then(res => {
        if (res.data.code == 200 && res.data.data.length) {
          this.signalId = res.data.data.map(v => v.signalId).join(',')
          this.getRealTimeCvDataByIdList()
        } else {
          this.electricityLineEchart({})
        }
      })
    },
    // 查询表计实时抄表示数
    getRealTimeCvDataByIdList() {
      this.chartLoading = true
      GetRealTimeCvDataByIdList({ signalId: this.signalId }, this.requestParams.kgceToken).then(res => {
        this.chartLoading = false
        if (res.data.code == 200 && res.data.data.length > 0) {
          const obj = {}
          res.data.data.forEach(item => {
            if (item.time) {
              if (obj[item.measurePointName]) {
                obj[item.measurePointName].push(item)
              } else {
                obj[item.measurePointName] = [item]
              }
            }
          })
          this.electricityLineEchart(obj)
        } else {
          this.electricityLineEchart({})
        }
      })
    },
    // 获取历史数据
    getPointHistoryList() {
      this.chartLoading = true
      const dataType = {
        1: 'hour',
        2: 'day',
        3: 'day',
        4: 'month',
        5: 'day'
      }
      const params = {
        dataType: dataType[this.statisticalType],
        startDate: this.statisticalDate[0],
        endDate: this.statisticalDate[1],
        measurePointId: this.requestParams.measurePointId, // 测点编号
        domainKeyword: '70020000', // 正向有功总电能
        sum: false // 是否计算合计值
      }
      GetPointHistoryList(params, this.requestParams.kgceToken).then((res) => {
        this.chartLoading = false
        if (res.data.code == 200 && res.data.data.length) {
          const obj = {}
          res.data.data.forEach(item => {
            if (item.time) {
              if (obj[item.measurePointName]) {
                obj[item.measurePointName].push(item)
              } else {
                obj[item.measurePointName] = [item]
              }
            }
          })
          this.electricityLineEchart(obj)
        } else {
          this.electricityLineEchart({})
        }
      })
    },
    // 折线图
    electricityLineEchart(data = {}) {
      const getchart = echarts.init(document.getElementById('foldLineChart' + this.tabsActiveName))
      let option
      if (Object.keys(data).length) {
        option = {
          dataZoom: [
            {
              type: 'inside',
              start: 0,
              end: 100
            }
          ],
          tooltip: {
            trigger: 'axis',
            confine: true,
            axisPointer: {
              lineStyle: {
                color: '#FFE3A6'
              }
            },
            backgroundColor: '#0C2269',
            borderColor: '#5B617C',
            textStyle: {
              color: '#fff'
            }
          },
          grid: {
            top: '25%',
            left: '15%',
            right: '4%',
            bottom: '10%'
          },
          xAxis: {
            // type: 'category',
            // data: nameList,
            type: 'time',
            boundaryGap: false,
            axisTick: {
              show: false
            },
            axisLine: {
              lineStyle: {
                color: 'rgba(133, 145, 206, 0.50)'
              }
            },
            axisLabel: {
              textStyle: {
                fontSize: 10,
                color: '#8C9EB5'
              }
            }
          },
          yAxis: {
            type: 'value',
            name: '单位：kwh',
            nameTextStyle: {
              color: '#A4AFC1',
              fontSize: 12,
              padding: [0, 0, 0, 5]
            },
            axisTick: {
              show: false
            },
            // axisLine: {
            //   lineStyle: {
            //     color: '#609ee9'
            //   }
            // },
            axisLabel: {
              margin: 10,
              textStyle: {
                fontSize: 10,
                color: '#8C9EB5'
              }
            },
            splitLine: {
              show: true,
              lineStyle: {
                color: ['rgba(230, 247, 255, 0.20)'],
                width: 1,
                type: 'dashed'
              }
            }
          },
          series: Object.keys(data).map(v => {
            return {
              name: v,
              type: 'line',
              // smooth: true,
              showSymbol: false,
              symbol: 'circle',
              symbolSize: 6,
              data: data[v].map((item) => [item.time, item.value]),
              itemStyle: {
                normal: {
                  color: '#FFE3A6'
                }
              },
              lineStyle: {
                normal: {
                  width: 2
                }
              }
            }
          })
        }
      } else {
        option = {
          title: {
            text: '暂无数据',
            x: 'center',
            y: 'center',
            textStyle: {
              fontSize: 14,
              fontWeight: 'normal',
              color: '#999'
            }
          }
        }
      }
      getchart.clear()
      getchart.setOption(option)
      // 随着屏幕大小调节图表
      window.addEventListener('resize', () => {
        getchart.resize()
      })
    },
    // 时间类型切换
    dataTypeCommand(val) {
      const date = {
        1: [moment().format('YYYY-MM-DD') + ' 00:00:00', moment().format('YYYY-MM-DD') + ' 23:59:59'],
        2: [moment().startOf('isoWeek').format('YYYY-MM-DD') + ' 00:00:00', moment().endOf('isoWeek').format('YYYY-MM-DD') + ' 23:59:59'],
        3: [moment().startOf('month').format('YYYY-MM-DD') + ' 00:00:00', moment().endOf('month').format('YYYY-MM-DD') + ' 23:59:59'],
        4: [moment().startOf('year').format('YYYY-MM-DD') + ' 00:00:00', moment().endOf('year').format('YYYY-MM-DD') + ' 23:59:59'],
        5: []
      }
      this.statisticalDate = date[val]
      this.statisticalType = val
      this.getPointHistoryList()
    },
    closeDialog() {
      this.$emit('closeDialog')
    }
  }
}
</script>

<style lang="scss" scoped>
.deviceEnergy {
  ::v-deep .bg-dialog {
    width: 500px;
    height: 350px;
    background: url('@/assets/images/qhdsys/detailDialogNew.png');
    background-repeat: no-repeat;
    background-size: 100% 100%;
    border: none;
    pointer-events: auto;
    box-shadow: none;
    .bg-dialog-title {
      display: inline-block;
      font-size: 16px;
      color: #fff;
      padding-top: 16px;
    }
    .el-dialog__header {
      height: 60px !important;
    }
    .el-dialog__body {
      height: calc(100% - 50px);
      padding: 0px 16px 16px 16px;
      overflow: auto;
    }
    .el-dialog__headerbtn {
      width: 16px;
      height: 16px;
      right: -20px;
      top: 30px;
      background-image: url('@/assets/images/qhdsys/detailDialogClose.png') !important;
    }
  }
  .deviceEnergy-content {
    .foldLineChart {
      flex: 1;
    }
    .search-data {
      display: flex;
      background: rgba(133, 145, 206, 0.15);
      padding: 0px 10px;
      margin-top: 10px;
      ::v-deep .el-dropdown {
        padding: 7px 6px;
        .el-dropdown-link {
          font-size: 14px;
          font-weight: 500;
          color: #8bddf5;
          line-height: 16px;
          position: relative;
          cursor: pointer;
        }
        .el-dropdown-link::after {
          content: '';
          position: absolute;
          width: 1px;
          height: 12px;
          background: rgba(133, 145, 206, 0.5);
          top: 50%;
          right: -6px;
          transform: translateY(-50%);
        }
      }
      ::v-deep .datePickerInput {
        flex: 1;
        padding: 8px 10px;
        height: 16px;
        box-sizing: content-box;
        background: none;
        border: none;
        .el-input__icon,
        .el-range-separator {
          line-height: 16px;
          color: #b0e3fa;
        }
        .el-range-input {
          background: none;
          color: #a4afc1;
        }
      }
    }
    ::v-deep .el-tabs {
      height: 100%;

      .el-tabs__nav {
        .el-tabs__item {
          font-size: 14px;
          color: #ffffff;
        }
        .is-active {
          color: #ffe3a6;
        }
      }
      .el-tabs__active-bar {
        background-color: #ffe3a6;
      }
      .el-tabs__nav-wrap::after {
        height: 1px;
        background-color: #7eaef9;
      }
      .el-tabs__content {
        height: calc(100% - 30px);
        .el-tab-pane {
          height: 100%;
          display: flex;
          flex-direction: column;
        }
      }
    }
    ::v-deep .module-tabs {
      .el-tabs__header {
        margin: 0px;
        .el-tabs__nav-wrap::after {
          display: none;
        }
        .el-tabs__item {
          height: 30px !important;
          line-height: 30px !important;
          color: rgba(255, 255, 255, 0.6) !important;
        }
        .is-active {
          color: #8BDDF5 !important;
        }
        .el-tabs__active-bar {
          height: 1px !important;
          background: url('@/assets/images/qhdsys/module-tabs-active-bar.png') no-repeat center;
        }
      }
    }
    ::v-deep .datePickerInput {
      flex: 1;
      padding: 8px 10px;
      height: 16px;
      box-sizing: content-box;
      background: none;
      border: none;
      .el-input__icon,
      .el-range-separator {
        line-height: 16px;
        color: #b0e3fa;
      }
      .el-range-input {
        background: none;
        color: #a4afc1;
      }
    }
  }
}
</style>
