<!--
 * @Author: hedd
 * @Date: 2023-02-27 14:35:19
 * @LastEditTime: 2024-04-02 09:07:02
 * @FilePath: \ihcrs_client_iframe\src\components\ModuleCard\index.vue
 * @Description:
-->
<template>
  <div class="box-card" ref="boxCard" :style="cstyle">
    <div v-if="showTitle" class="card-title">
      <span class="card-name" :class="{ floorName: floorName }" :style="{ width: $slots && $slots['title-right'] ? '50%' : '70%' }">
        <span v-if="floorName"> {{ floorName ? `${floorName} -` : '' }}</span>
        {{ title }}
      </span>
      <slot name="title-left" />
      <slot name="title-right" />
      <svg-icon v-if="hasExpand" @click="$emit('emit-expand')" class="right-expand" name="right-expand" />
    </div>
    <div v-if="scrollbarHover" ref="cardBody" v-scrollbarHover class="card-body">
      <slot name="content" />
    </div>
    <div v-if="!scrollbarHover" ref="cardBody" class="card-body">
      <slot name="content" />
    </div>
  </div>
</template>
<script>
export default {
  name: 'ModuleCard',
  props: {
    floorName: {
      type: String,
      default: ''
    },
    // 标题
    title: {
      type: String,
      default: ''
    },
    showTitle: {
      type: Boolean,
      default: true
    },
    // 是否显示展开按钮 与@emit-expand配合使用
    hasExpand: {
      type: Boolean,
      default: false
    },
    // 自定义样式
    cstyle: {
      type: Object,
      default: () => {}
    },
    // 是否直接显示滚动条
    scrollbarHover: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {}
  },
  mounted() {
    // console.log(this.$slots['title-right']);
  },
  methods: {}
}
</script>
<style lang="scss" scoped>
.box-card {
  .card-title {
    height: 44px;
    line-height: 44px;
    padding: 0px 10px 0px 38px;
    position: relative;
    display: flex;
    align-items: center;
    background: url('@/assets/images/qhdsys/bg-bt.png') no-repeat;
    background-size: 100% 100%;
    display: flex;
    justify-content: space-between;
    align-items: center;

    .card-name {
      // padding-left: 10px;
      flex: 1;
      font-family: HarmonyOS Sans SC-Bold;
      font-size: 16px;
      font-weight: bold;
      color: #fff;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
    .floorName {
      text-overflow: ellipsis;
      white-space: nowrap;
      overflow: hidden;
    }
    .right-expand {
      font-size: 24px;
      cursor: pointer;
    }
  }

  .card-body {
    margin-top: 6px;
    overflow-y: auto;
    height: calc(100% - 50px);
  }
  .title-left {
    position: absolute;
  }
}
</style>
