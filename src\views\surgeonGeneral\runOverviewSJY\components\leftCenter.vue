<template>
  <div class="left-content-item">
    <CardTitle title="门诊就诊信息" position="left" />
    <div v-loading="loading" class="card-content">
      <div class="card-content-itemList">
        <template v-for="(item, index) in dataStatistics">
          <div
            v-if="item.show"
            :key="index"
            class="card-content-itemList-item"
          >
            <div class="item-name">{{ item.name }}</div>
            <p>
              <span class="num">{{ item.num }}</span>
              <span class="percent">{{ item.unit }}</span>
            </p>
          </div>
        </template>
      </div>
    </div>
  </div>
</template>
<script>
import CardTitle from './title'
import { information } from '@/utils/runOverviewSJY'
import { formatThousand } from './utils'
export default {
  components: {
    CardTitle
  },
  props: {
    date: {
      type: Object,
      default: () => ({})
    },
    dateType: {
      type: Number,
      default: 0
    }
  },
  data() {
    return {
      loading: false,
      // 统计数据
      dataStatistics: [
        { key: 'allocationNum', name: '放号量', num: '0', unit: '人', show: true},
        { key: 'reservationNum', name: '预约量', num: '0', unit: '人', show: true},
        { key: 'registrationCount', name: '复诊量', num: '0', unit: '人', show: true},
        { key: 'patientvisits', name: '接诊量', num: '0', unit: '人', show: true},
        { key: 'averageconsultation', name: '平均接诊率', num: '0', unit: '%', show: true},
        { key: 'averageduration', name: '平均接诊时长', num: '0', unit: '分钟', show: true},
        { key: 'waitNum', name: '当前候诊人数', num: '0', unit: '人', show: true },
        { key: 'averagewaiting', name: '平均候诊时长', num: '0', unit: '分钟', show: true }
      ]
    }
  },
  watch: {
    date: {
      handler (val) {

        this.getData()
      },
      deep: true
    }
  },
  mounted () {

  },
  methods: {
    getData () {
      this.loading = true
      this.dataStatistics.forEach((item, index) => {
        if (item.key == 'averageduration' || item.key == 'averagewaiting') {
          if (this.dateType !== 0) {
            item.show = false // 日视图隐藏候诊人数和平均候诊时长
          } else {
            item.show = true // 月视图和年视图显示
          }
        }
      })
      information(this.date).then(res => {
        if (res.data.code == 200) {
          // 假设返回的数据格式与dataStatistics一致
          this.dataStatistics.forEach((item, index) => {
            let value = res.data.data[item.key] || '0'
            item.num = formatThousand(value)
          })
        }
      }).catch(() => {
        this.$message.error('获取门诊就诊信息失败')
      }).finally(() => {
        this.loading = false
      })
    }
  }
}
</script>
<style lang="scss" scoped>
.left-content-item {
  width: 100%;
  height: calc(100% / 3 - 7px);
  background: url("~@/assets/images/qhdsys/new-card-bg.png") no-repeat;
  background-size: 100% 100%;
  margin-bottom: 10px;
  .card-content {
    height: calc(100% - 36px);
    width: 100%;
    padding: 5px 10px 5px 10px;
    .card-content-itemList {
      display: flex;
      flex-wrap: wrap;
      color: #fff;
      width: 100%;
      height: 100%;
      .card-content-itemList-item {
        width: calc(50% - 5px); // 原有宽度计算不变
        background: rgba(255, 255, 255, 0.05);
        text-align: center;
        margin: 5px 0;
        display: flex;
        flex-direction: column;
        justify-content: center;
        &:nth-child(odd) {
          margin-right: 5px;
          margin-left: 0;
        }

        &:nth-child(even) {
          margin-left: 5px;
          margin-right: 0;
        }
        .item-name {
          margin-bottom: 6px;
        }
        .num {
          font-size: 18px;
          color: #ffdc83;
          margin-right: 10px;
          font-weight: bold;
        }
      }
    }
  }
}
</style>
