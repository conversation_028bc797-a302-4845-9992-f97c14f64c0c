<template>
  <div class="content" :class="screenSmall ? 'screen_small' : ''">
    <div class="title">
      <div @click="$router.go(-1)"><i class="el-icon-arrow-left"></i>返回</div>
    </div>
    <div class="content-bottom">
      <div class="left-content">
        <div class="left-top">
          <div class="bg-title">基本地址</div>
          <div class="bg-content content-padding" style="display: flex">
            <div class="left-box">
              <p>
                <span>报警来源</span><span>{{ alarmRecordEntity.alarmSource }}</span>
                <!-- <span>报警来源</span><span>{{ alarmSourceOption.filter((e) => e.dictValue == alarmRecordEntity.alarmSource) | dictFilter }}</span> -->
              </p>
              <p>
                <span>报警名称</span><span>{{ alarmRecordEntity.deviceName }}</span>
              </p>
              <p>
                <span>报警参数</span><span>{{ alarmRecordEntity.alarmCode }}</span>
                <!-- <span>报警参数</span><span>{{ alarmCodeOption.filter((e) => e.dictValue == alarmRecordEntity.alarmCode) | dictFilter }}</span> -->
              </p>
              <p>
                <span>报警时间</span><span>{{ alarmRecordEntity.alarmTime }}</span>
              </p>
            </div>
            <div class="right-box">
              <p>
                <span>报警类型</span><span>{{ alarmRecordEntity.alarmTypeName }}</span>
              </p>
              <p>
                <span>报警级别</span><span>{{ policeLevelOption.filter((e) => e.dictValue == alarmRecordEntity.alarmLevel) | dictFilter }}</span>
              </p>
              <p>
                <span>报警值</span><span>{{ alarmRecordEntity.alarmValue }}</span>
              </p>
              <p>
                <span>报警位置</span><span>{{ alarmRecordEntity.devicePosition }}</span>
              </p>
            </div>
          </div>
        </div>
        <div class="left-center">
          <div class="bg-title">关联信息</div>
          <div class="bg-content content-padding" style="overflow-y: scroll">
            <div class="message-title">关联监控</div>
            <div class="monitor-list" style="display: flex; width: 100%">
              <!-- <p><span>门诊口西向东球机</span><span>报警回放</span><span>实时视频</span></p> -->
              <div class="center-center">暂无关联监控</div>
            </div>
            <div class="message-title">
              关联工单
              <el-tooltip v-if="warnType === 'dispose'" popper-class="tooltip" effect="light" placement="right">
                <div slot="content" class="work-order-type">
                  <div v-for="item in workOrderList" :key="item.id" @click="selectWorkOrderType(item.id, item.workTypeName)">{{ item.workTypeName }}</div>
                </div>
                <div class="add-workorder"><i class="el-icon-plus"></i>创建工单</div>
              </el-tooltip>
            </div>
            <el-table
              v-loading="tableLoading"
              :data="tableData"
              :cell-style="$tools.setCell(3)"
              :header-cell-style="$tools.setHeaderCell(3)"
              style="width: 100%"
              element-loading-background="rgba(0, 0, 0, 0.2)"
            >
              <el-table-column fixed prop="workNum" show-overflow-tooltip label="工单号"></el-table-column>
              <el-table-column fixed prop="flowType" show-overflow-tooltip label="状态"></el-table-column>
              <el-table-column fixed prop="createDate" show-overflow-tooltip label="派单时间"></el-table-column>
              <el-table-column fixed prop="designateDeptName" show-overflow-tooltip label="服务部门"></el-table-column>
              <el-table-column fixed prop="designatePersonName" show-overflow-tooltip label="服务人员"></el-table-column>
              <el-table-column fixed prop="designatePersonPhone" show-overflow-tooltip label="联系电话"></el-table-column>
              <el-table-column fixed prop="disDesignateDate" show-overflow-tooltip label="接单时间"></el-table-column>
              <el-table-column fixed prop="disFinishTime" show-overflow-tooltip label="完工时间"></el-table-column>
            </el-table>
          </div>
        </div>
        <div class="left-bottom">
          <div class="bg-title">案件追踪</div>
          <div class="bg-content content-padding">
            <el-form ref="formInline" :model="formInline" :rules="rules" :inline="true" label-width="auto" size="medium">
              <div class="formRow" style="display: flex">
                <div style="flex: 1">
                  <div style="width: 100%">
                    <el-form-item label="处置人员" prop="executePersonName">
                      <el-input v-model.trim="formInline.executePersonName" :disabled="warnType === 'detail'" placeholder="请输入处置人员"></el-input>
                    </el-form-item>
                  </div>
                  <div style="width: 100%">
                    <el-form-item label="联系电话" prop="executePersonPhone">
                      <el-input v-model.trim="formInline.executePersonPhone" :disabled="warnType === 'detail'" placeholder="请输入联系电话"></el-input>
                    </el-form-item>
                  </div>
                  <div style="width: 180%">
                    <el-form-item label="现场记录文件：" prop="">
                      <el-upload
                        class="upload-demo"
                        action=""
                        :disabled="warnType === 'detail'"
                        :file-list="fileList"
                        multiple
                        :http-request="httpRequset"
                        :on-remove="handleRemove"
                        :before-upload="beforeUpload"
                        title="下载"
                        :on-preview="downloadfile"
                      >
                        <i class="el-icon-upload"></i>
                        <div class="el-upload__text"><em>点击上传</em></div>
                      </el-upload>
                    </el-form-item>
                  </div>
                </div>
                <div style="flex: 1">
                  <el-form-item label="处置结果" prop="disposaResult">
                    <el-input
                      v-model.trim="formInline.disposaResult"
                      :disabled="warnType === 'detail'"
                      maxlength="500"
                      placeholder="请输入处置结果"
                      type="textarea"
                      onkeyup="if(value.length>500)value=value.slice(0,500)"
                      show-word-limit
                    ></el-input>
                  </el-form-item>
                </div>
              </div>
            </el-form>
          </div>
          <el-button v-if="warnType === 'dispose'" class="sino-button-sure saveBtn" type="primary" @click="judgeSaveFn">确定</el-button>
        </div>
      </div>
      <div class="right-content">
        <div class="right-top">
          <div class="bg-title">
            执行预案&nbsp;&nbsp;<span v-if="planShow" class="title-span">{{ planData.prePlanName }}</span
            ><span v-if="planShow && startPlanShow" class="start-plan" @click="startPlan"><i class="el-icon-video-play" />启动预案</span>
          </div>
          <div class="bg-content content-padding">
            <div v-if="!planShow && !startPlanShow" class="no-plan center-center">暂未关联预案</div>
            <div v-if="planShow && startPlanShow" class="no-plan center-center">暂未启动预案</div>
            <!-- 预案未启动 -->
            <div v-if="planShow && !startPlanShow" class="message-title">关联预案</div>
            <div v-if="planShow && !startPlanShow" class="reserve-scorll">
              <div v-for="(reservePlan, index) in planData.intelligencePrePlaneEnactmentRecordList" :key="reservePlan.id" class="reserve-plan">
                <div class="plan-title">
                  <div class="color-box">{{ index + 1 }}</div>
                  <div class="linear-g">
                    <span class="linear-g-span1">{{ reservePlan.accomplish === '0' ? '已完成' : '未完成' }}</span>
                    <span>{{ reservePlan.nodeTypeName }}</span>
                  </div>
                </div>
                <div class="plan-content" :class="index === reservePlanList.length - 1 ? '' : 'plan-content-line'">
                  <p style="margin-bottom: 8px">
                    执行内容: {{ (reservePlan.postName ? reservePlan.postName + '-' : '') + (reservePlan.callPeopleName ? reservePlan.callPeopleName + '-' : '') + reservePlan.callPeoplePhone }}
                  </p>
                  <p>执行时间: {{ reservePlan.finishTime }}</p>
                </div>
              </div>
              <div v-if="finishData.state" class="reserve-plan">
                <div class="plan-title">
                  <div class="color-box">{{ index + 1 }}</div>
                  <div class="linear-g">
                    <img class="linear-g-span-bg" src="../../../../assets/images/peace/correct.png" />
                    <span>{{ finishData.nodeTypeName }}</span>
                  </div>
                </div>
                <div class="plan-content-noline">
                  <p>完成时间: {{ finishData.finishTime }}</p>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="right-bottom">
          <div class="bg-title">报警地图</div>
          <div class="bg-content" style="">
            <img v-if="this.warnMapFile.length" class="warn-map" :src="this.warnMapFile[0].fileUrl" alt="" />
            <div v-else class="warn-map center-center">暂无报警地图</div>
          </div>
        </div>
      </div>
    </div>
    <!-- <div v-show="workOrderSelectShow" class="work-order-type" :style="styl">
      <div v-for="item in workOrderList" :key="item.id" @click="selectWorkOrderType(item.id, item.workTypeName)">{{ item.workTypeName }}</div>
    </div> -->
    <!-- 创建工单 -->
    <template v-if="workOrderDealShow">
      <workOrderDeal
        ref="workOrderDeal"
        :workOrderDealShow="workOrderDealShow"
        :workTypeCode="olgTaskManagement.workTypeCode"
        :workTypeName="olgTaskManagement.workTypeName"
        dealType="add"
        @workOrderSure="workOrderSure"
        @closeDialog="workOrderDialog"
      ></workOrderDeal>
    </template>
  </div>
</template>
<script>
import $ from 'jquery'
import { getDictDataList } from '@/utils/api'
import { getAlarmRecordDetailById, getAssociatedWorkOrderList, updateAlarmRecordAndFiles, startUpPlan, getIntelligencePrePlanEnactmentValue, createWorkOrStartPlan } from '@/utils/peaceLeftScreenApi'
import { getWorkOrderConfigList } from '@/utils/peaceRightScreenApi'
import workOrderDeal from '../../rightScreen/workOrderDeal.vue'
export default {
  name: 'WarnDisposal',
  components: {
    workOrderDeal
  },
  filters: {
    dictFilter(val) {
      return val.length ? val[0].labelName : ''
    }
  },
  data() {
    return {
      tableData: [],
      tableLoading: false,
      rules: {},
      formInline: {
        executePersonName: '',
        executePersonPhone: '',
        disposaResult: ''
      },
      alarmRecordEntity: {},
      alarmSourceOption: [],
      alarmCodeOption: [],
      alarmTypeOption: [],
      policeLevelOption: [],
      warnMapFile: [],
      warnUpdataFile: [],
      fileList: [],
      reservePlanList: [
        {
          id: 1,
          index: 1,
          name: '拨打电话',
          state: '已完成',
          content1: '主管 张三 13027272727',
          content2: '2022-02-18 18:00:00',
          sort: 'a'
        },
        {
          id: 2,
          index: 2,
          name: '发送工单',
          state: '已完成',
          content1: '消防主- 张三 13027272727',
          content2: '2022-02-18 18:00:00',
          sort: 'b'
        },
        {
          id: 3,
          index: 3,
          name: '发送工单',
          state: '已完成',
          content1: '消防主- 张三 13027272727',
          content2: '2022-02-18 18:00:00',
          sort: 'b'
        },
        {
          id: 4,
          index: 4,
          name: '预案执行完毕',
          content1: '2022-02-18 18:00:00',
          sort: 'end'
        }
      ],
      finishData: {
        state: false
      },
      // workOrderSelectShow: false,
      styl: {
        top: 0,
        left: 0
      },
      planData: {}, // 获取预案数据
      planShow: false,
      startPlanShow: false,
      workOrderList: [],
      hasWorkOderList: false,
      workOrderDealShow: false,
      olgTaskManagement: {},
      warnType: 'detail',
      screenSmall: false
    }
  },
  created() {
    // 初始化 根据模块过滤数据
    if (Object.hasOwn(this.$route.query, 'screenSmall')) {
      this.screenSmall = this.$route.query.screenSmall === true
    }
  },
  mounted() {
    const dictList = JSON.parse(localStorage.getItem('dictList'))
    if (dictList === null || dictList === undefined) {
      this.setDictStorage()
    } else {
      this.setOptionList(dictList)
    }
    this.getDetail(this.$route.query.id)
    this.warnType = this.$route.query.type
    // console.log(this.$route.params)
    this.warnMapFile = this.$route.params?.file.filter((e) => e.free1 === '1')
    this.$bus.$off('workNum').$on('workNum', (num) => {
      this.saveWorkNumToWarn(num)
    })
  },
  methods: {
    getDetail(id, IsWorkOrder = false) {
      getAlarmRecordDetailById({ id: id }).then((res) => {
        // console.log(res)
        const data = res.data
        if (data.code === '200') {
          this.alarmRecordEntity = data.data.alarmRecordEntity
          // this.alarmRecordEntity.capturedPicture = 'http://pic.2265.com/upload/2017-3/2017361130314809.png'
          if (!IsWorkOrder) {
            // 显示 预案
            if (this.alarmRecordEntity.planId && this.alarmRecordEntity.planId !== null) {
              this.planShow = true
              this.getPlanProcessData()
            } else {
              this.planShow = false
            }
          }
          if (this.warnType === 'detail') {
            $('.upload-demo .el-upload').hide()
            Object.assign(this.formInline, {
              executePersonName: data.data.alarmRecordEntity.executePersonName,
              executePersonPhone: data.data.alarmRecordEntity.executePersonPhone,
              disposaResult: data.data.alarmRecordEntity.disposeResult
            })
            this.warnUpdataFile = this.$route.params.file.filter((e) => e.free1 !== '1')
            this.fileList = this.warnUpdataFile.map((e) => {
              return {
                name: e.fileName,
                url: e.fileUrl
              }
            })
          } else {
            this.getWorkOrderConfigList()
          }
          // 显示 工单
          if (this.alarmRecordEntity.workOrderNumber) {
            this.getAssociatedWorkOrderList(this.alarmRecordEntity.workOrderNumber)
          }
        } else {
          this.$message({
            message: data.msg,
            type: 'warning'
          })
        }
      })
    },
    beforeUpload(file) {
      // console.log(file)
      if (file.size > 1024 * 1024 * 200) {
        this.$message({
          message: '上传文件请不要超过200M！',
          type: 'warning'
        })
        return false
      }
    },
    downloadfile(file) {
      // console.log(file)
      // window.open(file.url)
      const filename = file.name
      fetch(file.url)
        .then((res) =>
          res.blob().then((blob) => {
            const a = document.createElement('a')
            const url = window.URL.createObjectURL(blob)
            a.href = url
            a.download = filename
            a.click()
            window.URL.revokeObjectURL(url)
          })
        )
        .catch((res) => {
          this.$message({
            message: '下载失败！',
            type: 'warning'
          })
        })
    },
    httpRequset(file) {
      // console.log(file)
      this.fileList.push(file.file)
    },
    handleRemove(file, fileList) {
      this.fileList = fileList
    },
    startPlan() {
      const params = {
        id: this.alarmRecordEntity.planId,
        alarmRecordId: this.$route.query.id,
        startPlan: '0'
      }
      startUpPlan(params).then((res) => {
        const data = res.data
        if (data.code === '200') {
          this.getPlanProcessData()
          this.$message({
            message: '启动预案成功！',
            type: 'success'
          })
        } else {
          this.$message({
            message: data.msg,
            type: 'warning'
          })
        }
      })
    },
    // 获取 预案初始化数据
    getPlanProcessData() {
      getIntelligencePrePlanEnactmentValue({ id: this.alarmRecordEntity.planId, pid: this.$route.query.id }).then((res) => {
        // getIntelligencePrePlanEnactmentValue({ id: this.alarmRecordEntity.planId }).then((res) => {
        const data = res.data.data
        if (res.data.code === '200') {
          this.planData = data
          if (data.intelligencePrePlaneEnactmentRecordList && data.intelligencePrePlaneEnactmentRecordList.length > 0) {
            this.startPlanShow = false
            const freeState = data.intelligencePrePlaneEnactmentRecordList
            // 最后一个流程 已完成后 在新增 完毕流程
            if (freeState[freeState.length - 1].accomplish === '0') {
              Object.assign(this.finishData, {
                state: true,
                nodeTypeName: '预案执行完毕',
                finishTime: this.planData.intelligencePrePlaneEnactmentRecordList[this.planData.intelligencePrePlaneEnactmentRecordList.length - 1].finishTime
              })
            }
          } else if (!data.intelligencePrePlaneEnactmentRecordList && this.warnType === 'dispose') {
            this.startPlanShow = true
            this.finishData.state = false
          }
        } else {
          this.$message({
            message: res.data.msg,
            type: 'warning'
          })
        }
        // console.log(res)
        // if ((!data || !data.length) && this.warnType === 'dispose') {
        //   this.planData.prePlanName = this.alarmRecordEntity.planName
        //   this.startPlanShow = true
        // } else {
        //   if (data.startPlan === '0') {
        //     this.startPlanShow = false
        //     this.planData.intelligencePrePlaneEnactmentRecordList.push({
        //       nodeTypeName: '预案执行完毕',
        //       finishTime: this.planData.intelligencePrePlaneEnactmentRecordList[this.planData.intelligencePrePlaneEnactmentRecordList.length - 1].finishTime
        //     })
        //   } else if (data.startPlan === '1' && this.warnType === 'dispose') {
        //     this.startPlanShow = true
        //   }
        // }

        // console.log(this.planData.intelligencePrePlaneEnactmentRecordList)
      })
    },
    // 提交表单
    judgeSaveFn() {
      const query = JSON.parse(JSON.stringify(this.formInline))
      Object.assign(query, {
        id: this.$route.query.id,
        disposeType: 2,
        multipartFiles: this.fileList
      })
      const params = new FormData()
      for (const key in query) {
        if (query[key] instanceof Array) {
          for (let i = 0; i < query[key].length; i++) {
            params.append(key, query[key][i])
          }
        } else {
          params.append(key, query[key])
        }
      }
      this.$confirm('是否确认处置？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        cancelButtonClass: 'sino-button-sure',
        confirmButtonClass: 'sino-button-sure',
        customClass: 'confirm-box-class',
        type: 'warning'
      })
        .then(() => {
          updateAlarmRecordAndFiles(params).then((res) => {
            // console.log(res)
            if (res.data.code === '200') {
              this.$message({
                message: '报警处置成功！',
                type: 'success'
              })
              this.$router.push({ path: '/EmergencyDisposal' })
            } else {
              this.$message({
                message: res.data.msg,
                type: 'warning'
              })
            }
          })
        })
        .catch(() => {})
    },
    // 获取工单列表并显示 div
    // getWorkOrderList(e) {
    //   this.workOrderSelectShow = !this.workOrderSelectShow
    //   if (this.workOrderSelectShow && !this.hasWorkOderList) {
    //     this.getWorkOrderConfigList(e)
    //   } else {
    //     this.setWorkOrderDivShow(e)
    //   }
    // },
    // 显示 工单列表 div
    // setWorkOrderDivShow(e) {
    //   this.$nextTick(() => {
    //     const height = $('.work-order-type')[0].offsetHeight
    //     this.styl = {
    //       top: e.clientY - height / 2 + 'px',
    //       left: e.clientX + 30 + 'px'
    //     }
    //   })
    // },
    // 获取工单列表
    getWorkOrderConfigList() {
      // this.workOrderSelectShow = true
      getWorkOrderConfigList({}).then((res) => {
        this.workOrderList = res.data.data.list
        // this.setWorkOrderDivShow(e)
      })
    },
    selectWorkOrderType(id, name) {
      // this.workOrderSelectShow = false
      this.olgTaskManagement = {
        workTypeCode: id,
        workTypeName: name
      }
      this.workOrderDealShow = true
    },
    // 获取 报警关联工单
    getAssociatedWorkOrderList(workNum) {
      getAssociatedWorkOrderList({ workNum: workNum }).then((res) => {
        const data = res.data
        if (data.code === '200') {
          this.tableData = data.data.list
        }
      })
    },
    // 字典信息重新赋值
    setDictStorage() {
      getDictDataList({}).then((res) => {
        const data = res.data
        if (data.code === '200') {
          localStorage.setItem('dictList', JSON.stringify(data.data))
          this.setOptionList(data.data)
        }
      })
    },
    setOptionList(dictList) {
      this.alarmSourceOption = this.getSelectDictOption(dictList, '报警来源')
      this.alarmCodeOption = this.getSelectDictOption(dictList, '报警参数')
      this.alarmTypeOption = this.getSelectDictOption(dictList, '报警类型')
      this.policeLevelOption = this.getSelectDictOption(dictList, '警情级别')
    },
    getSelectDictOption(list, type) {
      const option = list.length ? list.filter((e) => e.dictName === type) : []
      const optionList = option.length ? option[0].dictConfigList : []
      return optionList
    },
    // 保存 工单号到关联预警中
    saveWorkNumToWarn(num) {
      const params = {
        id: this.$route.query.id,
        workNum: num
      }
      createWorkOrStartPlan(params).then((res) => {
        // console.log(res)
        this.getDetail(this.$route.query.id, true)
      })
    },
    workOrderSure(item) {
      this.workOrderDealShow = false
      this.saveWorkNumToWarn(item)
    },
    workOrderDialog() {
      this.workOrderDealShow = false
    }
  }
}
</script>

<style lang="scss" scoped>
.screen_small {
  padding-left: 110px !important;
  padding-bottom: 80px !important;
}
.content {
  position: relative;
  // background-color: #031553;
  background: url('~@/assets/images/qhdsys/bj.png') no-repeat;
  box-sizing: border-box;
  width: 100%;
  height: 100%;
  padding: 0.25rem 0.625rem 0.125rem 0.625rem;
}
.title {
  height: 30px;
  position: relative;
  div {
    cursor: pointer;
    position: absolute;
    top: 5px;
    left: 0;
    width: 80px;
    height: 30px;
    line-height: 30px;
    text-align: center;
    color: #618ad3;
    font-size: 0.875rem;
    white-space: nowrap;
    background-image: url('~@/assets/images/peace/btn-back.png');
    background-size: 100% 100%;
    background-repeat: no-repeat;
  }
}
.content-bottom {
  height: calc(100% - 40px);
  margin-top: 10px;
  position: relative;
  box-sizing: border-box;
  width: 100%;
  display: flex;
  justify-content: space-between;
  .left-content {
    width: calc(63% - 7px);
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    .left-top {
      width: 100%;
      height: calc(24% - 5px);
      background: url('~@/assets/images/peace/bg-24.png') no-repeat;
      background-size: 100% 100%;
      .left-box,
      .right-box {
        display: flex;
        flex-direction: column;
        justify-content: space-around;
        color: #5175b9;
        font-size: 0.875rem;
        font-family: PingFangSC-Medium;
        flex: 1;
        span:first-child {
          display: inline-block;
          width: 100px;
        }
        span:last-child {
          color: #fff;
        }
      }
    }
    .left-center {
      width: 100%;
      height: calc(43% - 5px);
      background: url('~@/assets/images/peace/bg-43.png') no-repeat;
      background-size: 100% 100%;
      padding-right: 10px;
      padding-bottom: 10px;
      box-sizing: border-box;
      .monitor-list {
        width: 100%;
        // padding: 0 30px;
        box-sizing: border-box;
        display: flex;
        flex-wrap: wrap;
        p {
          width: 50%;
          font-size: 0.875rem;
          padding: 10px 0;
          span:first-child {
            display: inline-block;
            color: #a4aabe;
            width: 170px;
          }
          span:nth-child(2) {
            color: #c2b190;
            padding-left: 0.375rem;
            position: relative;
            cursor: pointer;
          }
          span:nth-child(2)::after {
            content: '';
            position: absolute;
            width: 0.9375rem;
            height: 0.9375rem;
            top: 0;
            left: -0.9375rem;
            background: url('~@/assets/images/peace/monitor.png') no-repeat;
            background-size: 100% 100%;
          }
          span:last-child {
            color: #c2b190;
            margin-left: 1.25rem;
            cursor: pointer;
          }
        }
      }
    }
    .left-bottom {
      width: 100%;
      height: calc(32% - 5px);
      background: url('~@/assets/images/peace/bg-32.png') no-repeat;
      background-size: 100% 100%;
      position: relative;
      .saveBtn {
        position: absolute;
        right: 150px;
        bottom: 30px;
      }
      .formRow {
        width: 100%;
        ::v-deep .el-form-item {
          width: 100%;
          .el-form-item__content {
            width: 60%;
          }
          .el-form-item__label {
            color: #648dd4;
          }
        }
        .upload-demo {
          display: flex;
          ::v-deep .el-upload {
            border: 1px solid #284a93;
            color: #d2bf95;
            width: 100px;
            height: 60px;
            display: flex;
            flex-direction: column;
            justify-content: space-around;
            .el-icon-upload {
              font-size: 25px;
              margin-top: 5px;
            }
          }
          ::v-deep .el-upload-list {
            max-height: 70px;
            overflow-y: auto;
          }
        }
      }
    }
  }
  .right-content {
    width: calc(37% - 7px);
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    .right-top {
      width: 100%;
      height: calc(50% - 5px);
      background: url('~@/assets/images/peace/warn-disposal-45.png') no-repeat;
      background-size: 100% 100%;
      .reserve-scorll {
        width: 100%;
        height: calc(100% - 50px);
        overflow-y: auto;
      }
      .reserve-plan {
        width: 100%;
        // height: inherit;
        .plan-title {
          display: flex;
          width: 100%;
          height: 1.25rem;
          .color-box {
            height: 1.25rem;
            width: 1.25rem;
            color: #1c284f;
            line-height: 1.25rem;
            text-align: center;
            background: #e6cf9d;
            border-radius: 3px;
            font-size: 12px;
          }
          .linear-g {
            margin-left: 2px;
            width: calc(100% - 22px);
            height: 100%;
            background: linear-gradient(90deg, rgba(255, 227, 166, 0.18) 0%, rgba(255, 227, 166, 0) 100%);
            font-size: 13px;
            line-height: 1.25rem;
            font-family: PingFangSC-Medium;
            .linear-g-span1 {
              margin: 0 15px;
              color: #5eb99e;
            }
            .linear-g-span-bg {
              display: inline-block;
              width: 12px;
              height: 12px;
              margin: 0 8px 0 10px;
              vertical-align: middle;
              // height: 20px;
              // background: ;
              // margin-top: 5px;
            }
            span:last-child {
              color: #d5d8e1;
            }
          }
        }
        .plan-content {
          width: calc(100% - 33px);
          // border-left: 1px solid #303758;
          margin-left: 11px;
          padding: 20px 0px 20px 20px;
          color: #b5bacb;
          font-size: 13px;
        }
        .plan-content-line {
          border-left: 1px solid #303758;
        }
        .plan-content-noline {
          width: calc(100% - 33px);
          margin-left: 11px;
          padding: 20px 0px 20px 20px;
          color: #b5bacb;
          font-size: 13px;
        }
      }
      .no-plan {
        width: 100%;
        height: 100%;
      }
    }
    .right-bottom {
      width: 100%;
      height: calc(50% - 5px);
      background: url('~@/assets/images/peace/warn-disposal-45.png') no-repeat;
      background-size: 100% 100%;
      .warn-map {
        margin: 15px;
        box-sizing: border-box;
        width: calc(100% - 30px);
        height: calc(100% - 30px);
      }
    }
  }
  .bg-title {
    margin-top: 5px;
    height: 2.5rem;
    line-height: 2.5rem;
    color: #d4e3f9;
    padding-left: 3rem;
    font-family: TRENDS;
    .title-span {
      color: #d8c59d;
      font-family: PingFangSC-Medium;
      font-size: 0.875rem;
    }
    .start-plan {
      color: #d8c59d;
      font-family: PingFangSC-Medium;
      font-size: 0.875rem;
      cursor: pointer;
      i {
        margin: 0 3px 0 30px;
      }
    }
  }
  .bg-content {
    position: relative;
    box-sizing: border-box;
    padding: 10px 10px 15px 10px;
    width: 100%;
    height: calc(100% - 3rem);
    .message-title {
      color: #6d99e0;
      position: relative;
      height: 1.875rem;
      line-height: 1.875rem;
      // padding-left: 3.125rem;
      margin: 0.625rem 0.625rem 0 0.625rem;
      box-sizing: border-box;
      font-size: 1rem;
      &::after {
        content: '';
        position: absolute;
        width: 1.2rem;
        height: 1.2rem;
        top: 5px;
        left: -1.3rem;
        background: url('~@/assets/images/war/relation.png') no-repeat;
        background-size: 100% 100%;
      }
      .add-workorder {
        display: inline-block;
        float: right;
        color: #ffe3a6;
        font-size: 14px;
        cursor: pointer;
        i {
          padding-right: 5px;
          font-weight: 600;
        }
      }
    }
  }
  .content-padding {
    padding: 20px 100px 15px 100px;
  }
  ::v-deep div.el-table__fixed-body-wrapper {
    background: center;
  }
  // ::v-deep .el-table {
  //   border: none !important;
  // }
  ::v-deep .el-table--enable-row-hover .el-table__body tr:hover {
    box-sizing: border-box;
    td.el-table__cell {
      background-color: #343c62 !important;
      border-top: 1px solid #ffe3a6;
      border-bottom: 1px solid #ffe3a6;
    }
  }
}
.work-order-type {
  // position: absolute;
  height: auto;
  width: auto;
  color: #fff;
  // top: 0;
  // left: 0;
  // z-index: 9;
  div {
    padding: 8px 15px;
    font-size: 14px;
    cursor: pointer;
    &:hover {
      color: #ffe3a6;
    }
  }
}
.center-center {
  display: flex;
  align-items: center;
  justify-content: center;
  color: #4d5880;
  font-size: 16px;
}
</style>
