<template>
  <div class="information-details">
    <div class="sino-lists-wrapper">
      <div class="title-bar">
        <div class="module-title">
          <span>施工信息</span>
        </div>
      </div>
      <div class="content-warp">
        <div class="warp-box" v-for="(item,index) in buildInfo" :key="index">
          <div class="warp-box-title">{{ `${item.label}:` }}</div>
          <div class="warp-box-text"><span></span><span>{{ basicInfoForm[item.prop] | basicFilters }}</span></div>
        </div>
      </div>
    </div>
    <div class="sino-lists-wrapper">
      <div class="title-bar">
        <div class="module-title">
          <span>巡检内容</span>
        </div>
      </div>
      <div class="content-warp">
        <div class="warp-box" v-for="(item, index) in 5" :key="index">
          <!-- <div class="warp-box-title"></div> -->
          <div class="warp-box-text"><span>1.这是巡检要点的描述</span></div>
        </div>
      </div>
    </div>
    <div class="sino-lists-wrapper">
      <div class="title-bar">
        <div class="module-title">
          <span>巡检执行</span>
        </div>
      </div>
      <div class="content-warp">
        <div class="warp-box" v-for="(item, index) in executeInfo" :key="index">
          <div class="warp-box-title" style="width: 7.5rem;">{{ `${item.label}:` }}</div>
          <div class="warp-box-text"><span>{{ basicInfoForm[item.prop] | basicFilters }}</span></div>
        </div>
      </div>
    </div>
    <div class="sino-lists-wrapper">
      <div class="title-bar">
        <div class="module-title">
          <span>巡检结果</span>
        </div>
      </div>
      <div class="content-warp">
        <div class="warp-box" v-for="(item, index) in inspectionResultInfo" :key="index">
          <div class="warp-box-title" style="width: 7.5rem;">{{ `${item.label}:` }}</div>
          <div class="warp-box-text"><span>{{ basicInfoForm[item.prop] | basicFilters }}</span></div>
        </div>
      </div>
    </div>
  </div>
</template>
  <script>
export default {
  name: "inspectionDetail",
  props: {
    deviceId: {
      type: String,
      default: "",
    },
  },
  components: {
  },
  filters: {
    basicFilters(val) {
      return val ? val : "---";
    },
  },
  data() {
    return {
      visible: false,
      breadcrumb: [{ label: "设备详情", name: "monitorDetails" }],
      activeComponent: "monitorDetails",
      title: "设备附属信息",
      basicInfoForm: {}, // 复制和编辑时详情信息
      departmentTurn: {},
      pageLoading: false,
      imageArr: [],
      departmentShare: [],
      //施工信息
      buildInfo: [
        {
          label: '施工名称',
          prop: 'assetsName',
          lgWidth: 8,
        },
        {
          label: '施工类型',
          prop: 'assetsCode',
          lgWidth: 8,
        },
        {
          label: '施工位置',
          prop: 'commonName',
          lgWidth: 8,
        },
        {
          label: '施工期限',
          prop: 'sixEightCode',
          lgWidth: 8,
        },
        {
          label: '施工内容',
          prop: 'deviceModel',
          lgWidth: 8,
        },
        {
          label: '现场负责人',
          prop: 'manuDate',
          lgWidth: 8,
        },
        {
          label: '联系电话',
          prop: 'mobie',
          lgWidth: 8,
        },
        {
          label: '巡检任务名称',
          prop: 'supplierName',
          lgWidth: 8,
        },
        {
          label: '周期类型',
          prop: 'remake',
          lgWidth: 24,
        },
        {
          label: '责任班组',
          prop: 'classAb',
          lgWidth: 8,
        },
        {
          label: '巡检点',
          prop: 'manufacturerBrand',
          lgWidth: 8,
        },
        {
          label: '巡查时间',
          prop: 'deviceUnitOfMeasurement',
          lgWidth: 8,
        },
      ],
      //执行信息
      executeInfo: [
        {
          label: '巡查情况',
          prop: 'budgetCode',
          lgWidth: 8,
        },
        {
          label: '执行人员',
          prop: 'purchaseCode',
          lgWidth: 8,
        },
        {
          label: '实际巡检时间',
          prop: 'purcCntrCode',
          lgWidth: 8,
        },
        {
          label: '定位状态',
          prop: 'mateCntrCode',
          lgWidth: 8,
        },
      ],
      //巡检结果
      inspectionResultInfo: [
        {
          label: '结果',
          prop: 'budgetCode',
          lgWidth: 8,
        },
        {
          label: '描述',
          prop: 'purchaseCode',
          lgWidth: 8,
        },
        {
          label: '语音',
          prop: 'purcCntrCode',
          lgWidth: 8,
        },
        {
          label: '图片',
          prop: 'mateCntrCode',
          lgWidth: 8,
        },
      ],
    };
  },
  created() { },
  methods: {
    /** 打开组件 */
    openDetailComponent(params) {
      this.activeComponent = params;
      let obj = {
        monitorDetails: { label: "设备详情", name: "monitorDetails" },
        monitorListIndex: { label: "监测设备列表", name: "monitorListIndex" },
      };
      this.breadcrumb.push(obj[params]);
      this.title = "";
    },
    /** 组件返回上一级 */
    back(name) {
      this.activeComponent = name;
      let arr = [];
      for (let i = 0; i < this.breadcrumb.length - 1; i++) {
        arr.push(this.breadcrumb[i]);
        if (this.breadcrumb[i].name == name) {
          break;
        }
      }
      this.breadcrumb = arr;
      if (this.breadcrumb.length) {
        this.title = this.breadcrumb.find((item) => item.name === name).label;
      }
    },
    /** 关闭弹窗 */
    closeDialogFrame() {
      this.visible = false;
      this.activeComponent = "monitorDetails"
      this.title = "设备附属信息"
      this.breadcrumb = [{ label: "设备附属信息", name: "monitorDetails" }]
    },
  }
};
</script>

  <style lang="scss" scoped>
.information-details {
  margin: 0 auto;
  width: 100%;
  height: 100%;
  overflow-y: auto;
  .sino-lists-wrapper {
    color: #fff;
    font-size: 15px;
    margin-bottom: 1rem;
    .title-bar {
      width: 100%;
      padding: 0px 10px;
      height: 2.625rem;
      background: rgba(133, 145, 206, 0.15);
      display: flex;
      align-items: center;
      color: #dadee1;
      justify-content: space-between;
    }
    .content-warp {
      padding-left: 0.5rem;
      background: rgba(133, 145, 206, 0.1);
      .warp-box {
        width: 100%;
        height: 2.5rem;
        display: flex;
        align-items: center;
        .warp-box-title {
          width: 5.5rem;
          font-size: 14px;
          flex-shrink: 0;
          color: #b0e3fa;
          margin-right: 0.5rem;
        }
        .warp-box-text {
          flex: 1;
          flex-shrink: 0;
        }
      }
    }
  }
}
</style>
