<template>
  <dialogFrame
    :visible="isDialog"
    :breadcrumb="breadcrumb"
    :title="title"
    @back="back"
    @update:visible="closeDialogFrame"
  >
    <component :is="activeComponent" v-if="isDialog" :roomData="roomData" :seleteItem="seleteItem" @change="change" @openDetailComponent="openDetailComponent"></component>
  </dialogFrame>
</template>

<script>
export default {
  name: 'keyAreasListDialog',
  components: {
    dialogFrame: () => import('@/components/common/DialogFrame'),
    keyAreasList: () => import('./keyAreasList.vue'),
    personnelFlowList: () => import('./personnelFlowList.vue')
  },
  props: {
    isDialog: {
      type: Boolean,
      default: false
    },
    roomData: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      breadcrumb: [{ label: '重点区域列表', name: 'keyAreasList' }],
      activeComponent: 'keyAreasList',
      title: '重点区域列表',
      seleteItem: {}
    }
  },
  computed: {
  },
  methods: {
    change(data) {
      this.$emit('change', data)
    },
    /** 打开组件 */
    openDetailComponent(params) {
      this.activeComponent = params.key
      this.seleteItem = params.data
      let obj = {
        personnelFlowList: { label: '人员流量', name: 'personnelFlowList' }
      }
      this.breadcrumb.push(obj[params.key])
      this.title = obj[params.key].label
    },
    /** 关闭弹窗 */
    closeDialogFrame() {
      this.$emit('close', false)
      this.activeComponent = 'keyAreasList'
      this.title = '重点区域列表'
      this.breadcrumb = [{ label: '重点区域列表', name: 'keyAreasList' }]
    },
    /** 组件返回上一级 */
    back(name) {
      this.activeComponent = name
      let arr = []
      for (let i = 0; i < this.breadcrumb.length - 1; i++) {
        arr.push(this.breadcrumb[i])
        if (this.breadcrumb[i].name == name) {
          break
        }
      }
      this.breadcrumb = arr
      if (this.breadcrumb.length) {
        this.title = this.breadcrumb.find((item) => item.name === name).label
      }
    }
  }
}

</script>

<style lang="scss" scoped>

</style>
