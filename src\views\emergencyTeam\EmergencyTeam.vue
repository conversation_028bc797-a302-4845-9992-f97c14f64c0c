<template>
  <div class="content">
    <div class="left-box">
      <div class="title">
        <span>应急队伍信息</span>
      </div>
      <div class="team-box">
        <carousel-box v-for="(item, index) in teamList" :key="index" :list="item" :id="index"></carousel-box>
      </div>
    </div>
    <div class="right-box">
      <div class="title">
        <span>人员职责信息</span>
      </div>
      <div class="person-info">
        <div class="team">
          <span>{{ personInfo.teamName || '' }}</span>
        </div>
        <div class="img-box">
          <img v-if="personInfo.picture || ''" :src="personInfo.picture || ''" style="width: 100%; height: 100%" />
          <img class="no-people" v-else src="../../assets/images/war/no-people.png" />
        </div>
        <div class="name">{{ personInfo.name || '' }}</div>
        <div class="detail">
          <div>
            <div style="color: #b0e3fa">岗 &nbsp;&nbsp;&nbsp;&nbsp; 位</div>
            <!-- <div>{{ personInfo.postName || '' }}</div> -->
            <div style="font-size: 14px">维修应急</div>
          </div>
          <div style="padding: 0 12px 0 0">
            <div style="color: #b0e3fa">联系电话&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</div>
            <div style="font-size: 14px">{{ personInfo.mobile || '' }}</div>
          </div>
        </div>
        <div class="split-line"></div>
        <div class="duty">
          <div>
            <span style="color: #b0e3fa">岗位职责：</span>
            <!-- <span class="duty-cont">{{ personInfo.postDuty }}</span> -->
            <span>
              1.负责公共区域设备设施的巡视检查和日常维护保养，及时发现、处理和解决公共设备设施存在的问题，排除故障、消除隐患。<br />
              2.负责室内共用设施进行巡视检查和维修保养，负责对室内装修的审批、监管。<br />
              3.负责电力/排水/暖通电梯等设备的硬件配置、软件系统、运行状况和设备保修期情况的检查登记，确保各设备的正常运转。<br />
              4.负责设备的维修与保养及设备维保单位和相关单位的对接工作。<br />
              5.参与设备报废、更新、改造项目，根据设备维护保养计划定期对相关设备进行维护保养。<br />
            </span>
          </div>
          <div>
            <span style="color: #b0e3fa">队伍职责：</span>
            <span class="duty-cont">{{ personInfo.teamDuty }}</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
// 滚动条组件
import carouselBox from './components/Carousel.vue'
import { getTeamAllDutyPersonnel } from '@/utils/peaceRightScreenApi'
export default {
  components: {
    carouselBox
  },
  data() {
    return {
      teamList: [], // 队伍列表
      personInfo: {}, // 右侧人员职责信息
      // showInfo: true, // 是否展示人员职责信息
      showCarousel: true
    }
  },
  created() {
    // window.addEventListener('blur', this.handleVisiable)
    // window.addEventListener('focus', this.handleHidden)
  },
  mounted() {
    this.getTeamList()
  },
  methods: {
    // 点击左侧人员触发
    personClick(val) {
      this.personInfo = val
      // this.showInfo = true
    },
    // 获取列表数据
    getTeamList() {
      const params = {
        page: 1,
        pageSize: 999,
        troopType: ''
      }
      getTeamAllDutyPersonnel(params).then((res) => {
        const data = res.data
        if (data.code === '200') {
          const valueArr = Object.values(data.data)
          const keyArr = Object.keys(data.data)

          if (valueArr.length) {
            for (let i = 0; i < valueArr.length; i++) {
              if (valueArr[i].length) {
                valueArr[i].map((e) => {
                  e.teamName = keyArr[i].split('_')[0]
                  e.teamDuty = keyArr[i].split('_')[1]
                  e.postName = '维修'
                })
              } else {
                valueArr[i].map((e) => {
                  e.teamName = ''
                  e.teamDuty = ''
                })
              }
            }
            console.log(valueArr);
            // 初始化 人员信息赋值
            this.personInfo = valueArr[0][0] || {}
          }
          this.teamList = valueArr
        } else {
          // this.$message({
          //   message: data.msg,
          //   type: 'warning'
          // })
        }
      })
    }
    // // 监听窗口切出 停止轮播
    // handleVisiable() {
    //   this.$refs.myChild.handleVisiable()
    //   this.$refs.myChild2.handleVisiable()
    //   this.$refs.myChild3.handleVisiable()
    //   this.$refs.myChild4.handleVisiable()
    // },
    // // 监听窗口切入 开始轮播
    // handleHidden() {
    //   this.$refs.myChild.autoPlayCase()
    //   this.$refs.myChild2.autoPlayCase()
    //   this.$refs.myChild3.autoPlayCase()
    //   this.$refs.myChild4.autoPlayCase()
    // }
  }
}
</script>

<style lang="scss" scoped>
@font-face {
  font-family: TRENDS;
  src: url(../../assets/font/TRENDS.ttf);
}
.content {
  width: 100%;
  height: 100%;
  background: url('~@/assets/images/qhdsys/bj.png') no-repeat;

  // box-sizing: border-box;
  color: #fff;
  padding-top: 24px;
  display: flex;
  justify-content: space-between;
}
.content .left-box {
  width: 68%;
  height: calc(100% - 1.5rem);
  // overflow: hidden;
  .team-box {
    // height: calc(100% - 30px);
    height: 100%;
    overflow-y: overlay;
  }
}
.content .right-box {
  width: 30%;
}
.content > div {
  text-align: center;
}
.person-info {
  height: 90%;
}
.person-info > div {
  // display: flex;
  // margin: 16px 0;
}
.person-info > div > span:nth-child(1) {
  width: 45%;
  text-align: right;
}
.person-info > div > span:nth-child(2) {
  width: 55%;
  text-align: left;
}
.title {
  height: 30px;
}
.title span {
  display: block;
  color: #dceaff;
  width: 200px;
  height: 100%;
  line-height: 30px;
  margin: 0 auto;
  background-image: url(../../assets/images/war/title-bg.png);
  background-size: 100% 100%;
  background-repeat: no-repeat;
  font-family: 'TRENDS';
}
.right-box .title {
  margin-bottom: 33px;
}
.person-info {
  position: relative;
  background-image: linear-gradient(360deg, rgba(19, 47, 105, 0) 0%, rgba(19, 47, 105, 0.42) 100%);
  color: rgba(255, 255, 255, 0.9);
}
.person-info::before {
  position: absolute;
  display: block;
  content: '';
  width: 100%;
  height: 120px;
  background-image: url(../../assets/images/qhdsys/personInfo-bg.png);
  background-size: 100% 100%;
  background-repeat: no-repeat;
  z-index: 0;
}
.person-info img {
  position: relative;
  z-index: 9;
}
.person-info .team {
  position: relative;
  z-index: 9;
  text-align: center;
  display: block;
}
.team {
  height: 80px;
  line-height: 80px;
  margin-bottom: -16px !important;
}
.img-box {
  display: flex;
  justify-content: center;
  width: 120px;
  height: 120px;
  margin: 0 auto !important;
  border-radius: 50%;
  overflow: hidden;
  border: 1px solid #3056a2;
  background-color: #132f69;
  z-index: 99999;
  position: relative;
}
.img-box img.no-people {
  width: 75%;
  height: 100%;
}
.person-info .name {
  display: inline-block;
  text-align: center;
  font-size: 20px;
  font-weight: 500;
  margin-top: 24px;
  letter-spacing: 3px;
}
.person-info .detail {
  display: flex;
  align-items: center;
  margin-top: 0;
  text-align: start;
  padding: 0 60px;
  margin-bottom: 20px;
}
.person-info .detail > div {
  // display: flex;
  width: 100%;
  // flex-direction: column;
  // align-items: flex-start;
  height: 50px;
  line-height: 30px;
  justify-content: space-between;
}
.person-info .detail > div:nth-child(2) {
  text-align: right;
}
.person-info .detail > div > span:nth-child(1) {
  color: #7eaef9;
}
.person-info .split-line {
  width: 83%;
  height: 1px;
  background: rgba(126, 174, 249, 0.35);
  margin: 0 auto;
}
.person-info .duty {
  padding: 0 60px;
  display: flex;
}
.person-info .duty > div {
  line-height: 20px;
  margin-top: 16px;
  width: 100%;
  display: flex;
  font-size: 14px;
  flex-direction: column;
  align-items: flex-start;
}
.person-info .duty .duty-cont {
  padding-right: 30px;
  white-space: pre-line;
}
.person-info .duty > div > span:nth-child(1) {
  color: #7eaef9;
  margin-bottom: 18px;
}
.person-info .duty > div > span:nth-child(2) {
  display: inline-block;
  text-align: left;
}
</style>
