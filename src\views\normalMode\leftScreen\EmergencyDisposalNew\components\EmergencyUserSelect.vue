<template>
  <div class="component EmergencyUserSelect">
    <div class="EmergencyUserSelect__users">
      <div v-if="userList.length" class="EmergencyUserSelect__users__tags" :title="userNameStr">
        <el-tag class="EmergencyUserSelect__users__tag" v-for="item of userList" :key="item.id" size="small"
          :closable="removable" @close="onRemoveUser(item)">
          {{ tagFormatter(item) }}
        </el-tag>
      </div>
      <span v-else class="EmergencyUserSelect__placeholder">{{ placeholder }}</span>
    </div>
    <div class="EmergencyUserSelect__action">
      <el-button type="primary" icon="el-icon-search" @click="showDialog = true">选择</el-button>
    </div>

    <DialogSelectUser :select-mode="multiple ? '1' : '2'" :visible="showDialog" @updateVisible="showDialog = false"
      @advancedSearchFn="onSelected" :defaultSelectedUser="userIds" />
  </div>
</template>


<script>

import DialogSelectUser from "../../../rightScreen/components/dialogConfig.vue";

export default {
  name: "EmergencyUserSelect",
  components: {
    DialogSelectUser
  },
  model: {
    prop: 'userList',
    event: 'update:userList'
  },
  props: {
    multiple: {
      type: Boolean,
      default: false
    },
    userList: {
      type: Array,
      default: () => ([])
    },
    placeholder: {
      type: String,
      default: '请选择人员'
    },
    tagFormatter: {
      type: Function,
      default: (item) => {
        return item.staffName || ''
      }
    },
    removable: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      showDialog: false,
    }
  },
  computed: {
    userIds: function () {
      return this.userList.map(x => x.id)
    },
    userNameStr: function () {
      return this.userList.map(this.tagFormatter).join(',')
    },
  },
  methods: {
    onSelected(data) {
      const list = this.multiple ? [...this.userList] : [];
      data.forEach(item => {
        if (this.userList.some(x => x.id === item.id)) return;
        list.push({
          staffNumber: item.staffNumber, // 工号
          id: item.id, // 用户ID
          staffName: item.staffName // 姓名
        })
      })
      this.$emit('update:userList', list)
    },
    onRemoveUser(data) {
      const index = this.userList.indexOf(data);
      if (index > -1) {
        this.userList.splice(index, 1)
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.EmergencyUserSelect {
  display: flex;
  flex-flow: row nowrap;
  color: #FFFFFF;

  &__placeholder {
    color: #C0C4CC;
  }

  &__users {
    border: 1px solid #3056A2;
    padding: 0 8px;

    &:hover {
      border-color: #FFE3A6;
    }

    flex: 1;
    overflow: hidden;

    &__tag {
      margin-right: 4px;
      background: rgba(133, 145, 206, 0.15);
      color: #fff;
      border: none;
    }
  }

  &__action {
    border: 1px solid #3056A2;
    border-left: none;

    .el-button {
      height: 100%;
    }
  }
}
</style>
<style lang="scss">
.component.EmergencyUserSelect {
  .component.dialogConfig {
    background: rgba(0, 0, 0, 0.6);

    .el-dialog {


      .el-dialog__header {
        .dialog-title {
          font-size: 18px;
          font-family: Source Han Sans CN-Regular, Source Han Sans CN;
          font-weight: 400;
          color: #cbdeed;
          line-height: 18px;
          vertical-align: super;
        }

        .el-dialog__headerbtn {
          transform: translateX(-36px);
          width: 36px;
          height: 36px;
          background: url('@/assets/images/qhdsys/bg-icon-gb.png') no-repeat;
          cursor: pointer;

          .el-dialog__close {
            display: none
          }
        }
      }

      .el-dialog__body {
        height: calc(100% - 140px);

        .outermost {
          .right {
            .el-input--suffix {
              height: 32px;
              line-height: 30px;
            }

            .el-input__icon {
              line-height: 32px;
            }

            .el-table {
              th.el-table__cell {
                line-height: 23px;
              }

              .el-table__cell {
                padding: 8px 0;

              }
            }

            .el-button.new-edition {
              height: 32px;
            }
          }
        }
      }

      .el-dialog__footer {
        text-align: right;
        padding-bottom: 0;

        .dialog-footer {
          margin-right: 33px;
        }
      }
    }
  }
}
</style>
