<template>
  <div class="emergencyPlan">
    <!-- <p style="width: 60%;position: absolute;right: 0;top: 50px;z-index: 10000;font-size: 20px; color: yellow;">{{ wpfData }}</p> -->
    <div class="emergencyPlan-main">
      <div class="bg-title">
        <!-- 头部nav -->
        <div v-scrollMove class="bg-tab">
          <div v-for="(item, index) in tabList" v-show="item.isShow()" :id="item.value" :key="index" class="tab-div" :class="{ 'is-activeTab': activeTab === item.value }" @click="activeTabEvent(item.value, index)">
            {{ item.name }}
          </div>
        </div>
        <el-tooltip popper-class="alarm-log-tooltip" placement="bottom-end">
          <div class="bg-record-btn">警情处理记录</div>
          <div slot="content">
            <logList :listData="alarmLogList" />
          </div>
        </el-tooltip>
      </div>
      <div class="main-content">
        <component :is="currentComponent" :ref="currentComponent" :alarmData="alarmData" :alarmType="alarmType"></component>
      </div>
    </div>
  </div>
</template>

<script>
import { GetAlarmOperationRecord } from '@/utils/wartimeMode'
import moment from 'moment'
import { GetPlanDetail, GetPreplanStateByAlarmId, SavePreplanOperationState } from '@/utils/wartimeMode'
export default {
  name: 'emergencyPlan',
  beforeRouteEnter(to, from, next) {
    if (from.name) {
      try {
        window.chrome.webview.hostObjects.sync.bridge.InitializeThePlan(to.query.alarmType)
      } catch (error) {}
    }
    next()
  },
  components: {
    planFlow: () => import('./components/planFlow'),
    lawFlow: () => import('./components/lawFlow'),
    planList: () => import('./components/planList'),
    logList: () => import('./components/logList')
  },
  data() {
    return {
      // wpfData: '',
      activeTab: '1',
      alarmData: {
        alarmId: '', // 报警id
        planId: ''
      },
      alarmType: 0, // 报警类型
      tabList: [
        {
          value: '1',
          name: '预案流程',
          component: 'planFlow',
          isShow: () => {
            // return true
            return !!this.alarmType && !!this.alarmData.planId
          }
        },
        {
          value: '2',
          name: '法规流程',
          component: 'lawFlow',
          isShow: () => {
            return !!this.alarmData.planId
          }
        },
        {
          value: '3',
          name: '预案参考',
          component: 'planList',
          isShow: () => {
            return true
          }
        }
      ],
      alarmLogList: [],
      timer: null,

      callAlarmType: 0, // 仅用来给打电话逻辑使用
      eventData: {
        1: {}, // 演习
        2: {}, // 演习-启动预案
        3: {}, // 演习-自定义
        4: {}, // 确警
        5: {}, // 确警-启动预案
        6: {} // 确警-自定义
      },
      stepTypes: {
        1: ['1', '2'], // 演习包含步骤
        2: ['4', '5'] // 确警包含步骤
      },
      activeStep: 0, // 当前预案执行到的步骤
      autoCallList: [] // 自动打电话列表
    }
  },
  computed: {
    currentComponent() {
      return this.tabList.find((e) => e.value === this.activeTab)?.component
    }
  },
  watch: {
    $route(to, from) {
      if (to.query.alarmId || to.query.planId) {
        this.alarmType = 0
        this.alarmData.alarmId = to.query.alarmId
        this.alarmData.planId = to.query.planId
        this.activeTab = this.alarmData.planId ? '2' : '3'

        this.callAlarmType = this.$route.query.alarmType
      }
    }
  },
  created() {
    if (this.$route.query.alarmId || this.$route.query.planId) {
      // this.wpfData = JSON.stringify(this.$route.query)
      // this.alarmType = 2
      this.alarmType = 0
      this.alarmData.alarmId = this.$route.query.alarmId
      this.alarmData.planId = this.$route.query.planId
      this.activeTab = this.alarmData.planId ? '2' : '3'

      this.callAlarmType = this.$route.query.alarmType
    }
    this.getPlanDetail('init')

    this.getAlarmOperationRecord()
    this.timer = setInterval(() => {
      setTimeout(() => {
        this.getAlarmOperationRecord()
      }, 0)
    }, 1000 * 15)
    // }, 1000 * timerNum)
    this.$once('hook:beforeDestroy', () => {
      clearInterval(this.timer)
      this.timer = null
    })
  },
  mounted() {
    // type：autoCall自动打电话完成消息 alarmInfo确警或者演戏
    try {
      window.chrome.webview.addEventListener('message', (event) => {
        // this.wpfData = event.data
        const data = JSON.parse(event.data)
        if (data.type == 'alarmInfo') {
          this.alarmType = data.alarmType
          this.alarmData = data.alarmData
          this.alarmData.planId = data.alarmData.preplanId
          this.activeTab = this.alarmType && this.alarmData.planId ? '1' : (this.alarmData.planId ? '2' : '3')
        } else if (data.type == 'autoCall') {
          if (this.activeTab == '1') {
            // 自动打完电话 检查状态进入下一步骤
            // this.$refs.planFlow.savePreplanOperationState()
          }
        } else if (data.type == 'startPlan') { // 点击启动预案
          this.getPreplanStateByAlarmId()
        }
      })
    } catch (errpr) {}
  },
  methods: {
    // 自动电话
    autoCall(list) {
      console.log('未启动自动打电话', list)
      const timeout = setTimeout(() => {
        this.savePreplanOperationState()
        clearTimeout(timeout)
      }, 1000)
      if (!list.length) return
      const param = list.map(v => {
        return {
          alarmId: this.alarmData.alarmId,
          name: v.personName,
          operation: '电话通知',
          // phone: v.textPhone,
          phone: v.noticePhone,
          planNoticePersonId: v.personId,
          noticeContent: v.noticeContent
        }
      })
      try {
        window.chrome.webview.hostObjects.sync.bridge.AutoCallJson(JSON.stringify(param))
      } catch (error) {}
    },
    // 提交当前预案执行到的步骤
    savePreplanOperationState(step) {
      const userInfo = this.$store.state.loginInfo.user
      const param = {
        alarmId: this.alarmData.alarmId,
        alarmAdress: this.alarmData.alarmSpaceName,
        alarmLevel: this.alarmData.alarmLevel,
        alarmTime: this.alarmData.alarmStartTime,
        // alarmType: this.alarmData.alarmEntityTypeName,
        alarmType: this.alarmData.incidentName,
        createName: userInfo.staffName,
        planBasicId: this.alarmData.planId,
        stepType: step || this.activeStep // 步骤类型（ 1演习 2演习-启动预案 3演习-自定义 4确警 5确警-启动预案 6确警-自定义 7误报 8解除警报）
      }
      if (!param.stepType) return
      SavePreplanOperationState(param).then(res => {
        if (res.data.code == 200) {
          if (!['1', '4'].includes(this.activeStep)) {
            this.getCallData()
          }
        }
      })
    },
    // 获取当前预案执行到的步骤
    getPreplanStateByAlarmId(type) {
      GetPreplanStateByAlarmId({ alarmId: this.alarmData.alarmId }).then(res => {
        if (res.data.code == 200) {
          const states = res.data.data.states
          const steps = this.stepTypes[this.callAlarmType]
          // 判断为空新增0状态，作为前端判断回显流程图勾选
          if (!states.length) {
            this.savePreplanOperationState('0')
            return
          }
          if (type == 'init' && (states[0] == '1' || states[0] == '4')) return
          this.activeStep = steps[steps.indexOf(states[0]) + 1]
          console.log('获取当前预案执行到的步骤', states[0], steps.indexOf(states[0]), this.activeStep)
          if (['1', '2', '4', '5'].includes(this.activeStep)) {
            // 获取当前步骤自动打电话列表
            const callList = this.autoCallList.filter(v => v.stepType == this.activeStep)
            // execuNum:已打电话数量 sumNum:应该打电话数量
            if ((res.data.data.execuNum < res.data.data.sumNum) || (res.data.data.execuNum == 0 && res.data.data.sumNum == 0)) {
              this.autoCall(callList)
            }
          }
          // 启动预案之后调用wpf方法进入预案流程页面
          if (['2', '5'].includes(states[0])) {
            try {
              window.chrome.webview.hostObjects.sync.bridge.startPlanNode()
            } catch (error) {}
          }
        }
      })
    },
    // 获取当前步骤打电话列表
    getCallData(type) {
      const allCallList = []
      // 取出所有拨打电话数据
      this.stepTypes[this.callAlarmType].forEach(step => {
        this.eventData[step].noticeEventList.forEach(item => {
          if (item.noticePersonList.length && (item.noticeWay == 0 || item.noticeWay == 1)) {
            allCallList.push(...item.noticePersonList.map(v => {
              return {
                ...v,
                stepType: item.stepType,
                checkFlag: item.checkFlag, // 是否通知
                noticeContent: item.noticeContent, // 电话沟通内容
                noticeWay: item.noticeWay, // 通知方式
                noticeType: item.noticeType, // 通知类型
                showFlag: item.showFlag, // 显示拨打按钮
                textPhone: item.noticePhone
              }
            }))
          }
        })
      })
      this.autoCallList = allCallList.filter(v => v.noticeWay == 1) // 自动打电话列表
      this.getPreplanStateByAlarmId(type)
    },
    // 获取预案详情
    getPlanDetail(type) {
      GetPlanDetail({ id: this.alarmData.planId }).then(res => {
        if (res.data.code == 200) {
          const { noticeEventList } = res.data.data
          noticeEventList.forEach(item => {
            item.noticeWay = Number(item.noticeWay)
            item.noticeType = Number(item.noticeType)
            if (Object.keys(this.eventData[item.stepType]).includes('noticeEventList')) {
              this.eventData[item.stepType].noticeEventList.push(item)
            } else {
              this.eventData[item.stepType].noticeEventList = [item]
            }
          })
          console.log('预案详情', res.data.data, this.eventData)
          this.getCallData(type)
        }
      })
    },
    // 获取报警处理记录
    getAlarmOperationRecord() {
      GetAlarmOperationRecord({ alarmId: this.alarmData.alarmId }).then((res) => {
        if (res.data.code == 200) {
          this.alarmLogList = res.data.data.map(item => {
            return {
              id: item.id,
              name: item.name,
              operationName: item.operation,
              startTime: item.operationStartTime ? moment(item.operationStartTime).format('YYYY-MM-DD HH:mm:ss') : '',
              endTime: item.operationEndTime ? moment(item.operationEndTime).format('YYYY-MM-DD HH:mm:ss') : '',
              status: item.operationState
            }
          })
        }
      })
    },
    // tab切换
    activeTabEvent(val, index) {
      this.activeTab = val
    }
  }
}

</script>

<style lang="scss" scoped>
.emergencyPlan {
  width: 100%;
  height: 100%;
  position: relative;
  .emergencyPlan-main {
    position: absolute;
    right: 0;
    top: 0;
    bottom: 0;
    width: 60%;
    background: url('@/assets/images/plan/planBgd.png') no-repeat center center / 100% 100%;
    display: flex;
    flex-direction: column;
  }
  .bg-title {
    padding: 0;
    border-bottom: 1px solid #5E6D97;
    overflow: hidden;
    color: #dceaff;
    font-family: HarmonyOS Sans SC-Regular, HarmonyOS Sans SC;
    display: flex;
    position: relative;
    .bg-tab {
      display: flex;
      overflow: hidden;
      box-sizing: border-box;
      .tab-div {
        width: 88px;
        height: 40px;
        flex-shrink: 0;
        line-height: 40px;
        text-align: center;
        font-size: 16px;
        color: #a4acb9;
        background: url('@/assets/images/qhdsys/bg-tab.png') no-repeat;
        cursor: pointer;
      }
      .is-activeTab {
        color: #b0e3fa;
        background: url('@/assets/images/qhdsys/bg-tab-xz.png') no-repeat;
      }
    }
    .bg-record-btn {
      font-weight: 500;
      font-size: 14px;
      color: #8BDDF5;
      position: absolute;
      top: 50%;
      right: 16px;
      transform: translateY(-50%);
      cursor: pointer;
    }
  }
  .main-content {
    padding: 16px;
    flex: 1;
    overflow: hidden;
  }
}
</style>
<style lang="scss">
.alarm-log-tooltip {
  width: 520px;
  padding: 16px;
  max-height: 80%;
  overflow: auto;
  background: radial-gradient( 100% 129% at 135% 17%, #152C4A 0%, #061431 100%), rgba(53,98,219,0.06) !important;
  box-shadow: 0px 0px 20px 0px #000510;
}
</style>
