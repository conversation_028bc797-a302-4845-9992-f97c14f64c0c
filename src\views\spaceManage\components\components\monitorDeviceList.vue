<template>
  <div class="monitorDeviceList">
    <div class="view-header-box">
      <div class="header-left">
        <el-form :model="searchForm" class="search-form" inline>
          <el-form-item>
            <el-input v-model="searchForm.assetsName" size="small" placeholder="设备名称" clearable></el-input>
          </el-form-item>
          <el-form-item>
            <el-select v-model="searchForm.sysOfCode" size="small" placeholder="所属系统" filterable clearable @change="systemChange">
              <el-option v-for="item in systemList" :key="item.dictionaryDetailsCode" :label="item.dictionaryDetailsName" :value="item.dictionaryDetailsCode"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-select v-model="searchForm.sysOf1Code" size="small" placeholder="设备类型" filterable clearable :disabled="!searchForm.sysOfCode">
              <el-option v-for="item in deviceTypeList" :key="item.dictionaryDetailsCode" :label="item.dictionaryDetailsName" :value="item.dictionaryDetailsCode"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-cascader
              v-model="searchForm.spaceId"
              placeholder="所在位置"
              :props="{ label: 'ssmName', value: 'id', checkStrictly: true }"
              clearable
              :show-all-levels="false"
              :filterable="true"
              :options="spacesList"
            >
            </el-cascader>
          </el-form-item>
          <el-form-item v-if="roomData.tabName == 'SpaceChair' && isSpecial == true">
            <el-select v-model="searchForm.useStatus" size="small" placeholder="监测状态" filterable clearable>
              <el-option v-for="item in useStatusList" :key="item.value" :label="item.label" :value="item.value"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item v-else>
            <el-select v-model="searchForm.assetsStatus" size="small" placeholder="监测状态" filterable clearable>
              <el-option v-for="item in deviceStatusList" :key="item.value" :label="item.label" :value="item.value"></el-option>
            </el-select>
          </el-form-item>
        </el-form>
      </div>
      <div class="header-right">
        <el-button class="sino-button-sure" @click="resetForm">重置</el-button>
        <el-button class="sino-button-sure" @click="handleSearchForm">查询</el-button>
      </div>
    </div>
    <div class="view-content">
      <el-table
        ref="table"
        v-loading="tableLoading"
        :data="tableData"
        :resizable="false"
        height="calc(100% - 46px)"
        :cell-style="$tools.setCell(3)"
        :header-cell-style="$tools.setHeaderCell(3)"
        stripe
        element-loading-background="rgba(0, 0, 0, 0.2)"
        @cell-click="viewDetail"
      >
        <el-table-column prop="assetsName" label="设备名称" show-overflow-tooltip/>
        <el-table-column prop="systemTypeName" label="所属系统" show-overflow-tooltip/>
        <el-table-column prop="deviceTypeName" label="设备类型" show-overflow-tooltip/>
        <el-table-column prop="spaceName" label="所在位置" show-overflow-tooltip/>
        <el-table-column prop="relevantDeptName" label="所在科室" show-overflow-tooltip/>
        <el-table-column prop="deviceTypeName" label="设备属性" show-overflow-tooltip/>
        <el-table-column prop="assetsStatus" label="监测状态" width="100" show-overflow-tooltip>
          <template slot-scope="scope">
            <span v-if="roomData.tabName == 'SpaceChair' && isSpecial == true" :style="{color: useStatusList.find(v => v.label == scope.row.chairStatus)?.color}">{{ scope.row.chairStatus }}</span>
            <span v-else :style="{color: deviceStatusList.find(v => v.value == scope.row.assetsStatus).color}">{{deviceStatusList.find(v => v.value == scope.row.assetsStatus).label}}</span>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="90">
          <template slot-scope="scope">
            <span class="operation-span" @click="viewDetails(scope.row)">查看</span>
          </template>
        </el-table-column>
      </el-table>
      <el-pagination
        :current-page="pagination.pageNo"
        :page-sizes="[15, 20, 30, 40]"
        :page-size="pagination.pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        :total="pagination.total"
        class="pagination"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      ></el-pagination>
    </div>
    <deviceDetails v-if="isDeviceDetails" :dialogShow="isDeviceDetails" :roomData="roomData" :deviceId="deviceId" @deviceDetailsClose="() => isDeviceDetails = false"/>
  </div>
</template>

<script>
import { GetMonitoringItemsList, GetClassifyDictList, GetDeviceType, GetSpaceTree } from '@/utils/spaceManage'
import deviceDetails from '../deviceDetailsNew.vue'
export default {
  name: 'monitorDeviceList',
  components: {
    deviceDetails
  },
  props: {
    roomData: {
      type: Object,
      default: () => {
        return {}
      }
    },
    // 重点区域空间id
    keyAreasSpaceId: {
      type: Array,
      default: () => []
    },
    // 设备id列表
    deviceIds: {
      type: Array,
      default: () => []
    },
    initParams: {
      type: Object,
      default: () => {
        return {}
      }
    },
    isSpecial: {
      type: Boolean,
      default: false
    },
    webSocketObj: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      isDeviceDetails: false,
      searchForm: {},
      systemList: [], // 所属系统
      deviceTypeList: [], // 设备类型
      spacesList: [], // 空间位置
      deviceStatusList: [
        {label: '正常', value: 1, color: '#61E29D'},
        {label: '离线', value: 2, color: '#86909C'},
        {label: '异常', value: 3, color: '#FF2D55'}
      ], // 设备状态
      useStatusList: [
        {label: '空闲', value: 0, color: ''},
        {label: '使用中', value: 1, color: ''},
        {label: '异常占用', value: 2, color: '#FF2D55'}
      ],
      pagination: {
        total: 0,
        pageSize: 15,
        pageNo: 1
      },
      tableData: [],
      tableLoading: false,
      deviceId: ''
    }
  },
  computed: {

  },
  watch: {
    isDeviceDetails(val) {
      try {
        window.chrome.webview.hostObjects.sync.bridge.ShowDialog(val)
      } catch (error) {}
    },
    webSocketObj: {
      handler (newObj) {
        // 监听，如果有变化说明有数据传过来，需要用id查询到某条数据更新监测状态
        if (newObj && Object.keys(newObj).length > 0 && newObj?.msgTxt?.id) {
          const { id, propertyList } = newObj.msgTxt

          // 查找需要更新的设备
          const foundIndex = this.tableData.findIndex(item => item.id === id)
          if (foundIndex > -1) {
            // 更新设备的iotPropertyList
            if (propertyList && propertyList.length > 0) {
              // 使用$set确保视图响应式更新
              this.$set(this.tableData[foundIndex], 'iotPropertyList', propertyList)

              // 如果是椅位监测，更新chairStatus
              if (this.roomData.tabName == 'SpaceChair' && this.isSpecial == true) {
                const statusProperty = propertyList.find(prop => prop.metadataTag === 'operatingStatus')
                if (statusProperty) {
                  this.$set(this.tableData[foundIndex], 'chairStatus', statusProperty.valueText || '-')
                  console.log(`已在弹窗中更新椅位 ${id} 的状态为: ${statusProperty.valueText || '-'}`)
                }
              }
            }
          }
        }
      },
      deep: true
    }
  },
  created() {
    this.getClassifyDictList()
    this.getTreelist()
    this.init()
  },
  methods: {
    init() {
      this.searchForm = {
        assetsName: '',
        sysOfCode: this.roomData.projectCode || '',
        sysOf1Code: '',
        spaceId: [],
        assetsStatus: '',
        useStatus: '' // 椅位状态
      }
      // 椅位监测带入二级类型
      if (this.roomData.tabName == 'SpaceChair' && this.isSpecial == true) {
        this.searchForm.sysOf1Code = this.roomData.categoryCode
      }
      if (this.searchForm.sysOfCode) {
        this.getDeviceType()
      }
      console.log(1111, this.initParams)
      if (Object.keys(this.initParams).length) {
        Object.assign(this.searchForm, this.initParams)
      }
      this.getMonitoringItemsList()
    },
    viewDetails(row) {
      this.deviceId = row.id
      this.isDeviceDetails = true
    },
    // 监测列表
    getMonitoringItemsList() {
      let {assetsName, sysOfCode, sysOf1Code, spaceId, assetsStatus, useStatus} = this.searchForm
      let params = {
        equipAttr: this.roomData.equipAttr || 2,
        page: this.pagination.pageNo,
        pageSize: this.pagination.pageSize,
        assetsName,
        sysOfCode,
        sysOf1Code,
        assetsStatus,
        useStatus
      }
      if (spaceId?.length) {
        params.spaceId = spaceId?.at(-1)
      } else {
        if (this.keyAreasSpaceId.length) {
          params.spaceIdList = this.keyAreasSpaceId
        } else if (this.deviceIds.length) {
          params.assetsInfoIdInList = this.deviceIds
        } {
          params.spaceId = this.roomData.ssmCodes?.split(',')?.at(-1)
        }
      }
      this.tableLoading = true
      GetMonitoringItemsList(params).then((res) => {
        if (res.data.code == 200) {
          if (this.pagination.pageNo == 1) this.tableData = []
          if (this.roomData.tabName == 'SpaceChair' && this.isSpecial == true) {
            res.data.data.records.forEach(item => {
              if (item.iotPropertyList.length) {
                item.chairStatus = item.iotPropertyList.find(v => v.metadataTag == 'operatingStatus')?.valueText ?? '-'
              }
            })
          }
          this.tableData = res.data.data.records
          this.pagination.total = res.data.data.total
        }
      }).finally(() => {
        this.tableLoading = false
      })
    },
    systemChange() {
      this.searchForm.sysOf1Code = ''
      this.getDeviceType()
    },
    // 获取设备类型
    getDeviceType() {
      let params = {
        dictionaryCode: this.searchForm.sysOfCode,
        equipAttr: this.roomData.equipAttr || 2
      }
      GetDeviceType(params).then(res => {
        if (res.data.code == 200) {
          this.deviceTypeList = res.data.data
        }
      })
    },
    // 获取所属系统
    getClassifyDictList() {
      let params = {
        dictionaryCategoryId: 'PRODUCT_CATEGORY',
        level: 1
      }
      GetClassifyDictList(params).then(res => {
        if (res.data.code == 200) {
          if (__PATH.VUE_APP_HOSPITAL_NODE == 'jldyyy') {
            this.systemList = res.data.data.filter(item => item.dictionaryDetailsCode == 'DTXT')
            return
          }
          this.systemList = res.data.data
        }
      })
    },
    // 获取服务空间树形结构
    getTreelist() {
      GetSpaceTree().then((res) => {
        if (res.data.code == 200) {
          this.spacesList = this.$tools.transData(res.data.data, 'id', 'pid', 'children')
        }
      })
    },
    /** 查询 */
    handleSearchForm() {
      this.getMonitoringItemsList()
    },
    /** 重置 */
    resetForm() {
      this.init()
    },
    viewDetail(row, column) {
      if (column.property == 'throughCount') {
        this.$emit('openDetailComponent', {key: 'personnelFlowList', data: row})
      }
    },
    /** 条数变化事件 */
    handleSizeChange(val) {
      this.pagination.pageSize = val
      this.handleSearchForm()
    },
    /** 页数变化事件 */
    handleCurrentChange(val) {
      this.pagination.pageNo = val
      this.handleSearchForm()
    }
  }
}

</script>

<style lang="scss" scoped>
.monitorDeviceList {
  position: relative;
  height: 100%;
  padding: 0px 40px;
}
.view-header-box {
  display: flex;
  justify-content: space-between;
  ::v-deep(.search-form) {
    .el-input {
      width: 200px;
      .el-input__inner {
        height: 35px;
        border: 1px solid rgba(133, 145, 206, 0.5);
        border-radius: 4px;
        line-height: 35px;
      }
      .el-input__icon, .el-input__suffix {
        height: 40px;
        line-height: 40px;
      }
    }
  }
  .header-right {
    line-height: 40px;
  }
}
.view-content {
  height: calc(100% - 4rem);
  .operation-span {
    cursor: pointer;
    color: #8BDDF5;
  }
}
::v-deep .el-table--striped .el-table__body tr.el-table__row--striped td.el-table__cell{
  background: transparent;
}
</style>
