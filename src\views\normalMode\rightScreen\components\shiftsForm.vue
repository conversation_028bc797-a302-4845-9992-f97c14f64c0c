<template>
  <div class="main" v-loading="loading">
    <el-dialog v-dialogDrag v-if="dialogVisible" class="sino-dialog-medium personDialog" :visible.sync="dialogVisible" :before-close="closeDialog">
      <template slot="title">
        <span class="dialog-title">交接班事项</span>
      </template>
      <div>
        <el-form ref="formInline" :model="formInline" :inline="true" class="form-inline shift-form" label-width="100px" :rules="rules">
          <el-form-item label="值班情况" prop="dutySituation">
            <el-input type="textarea" v-model="formInline.dutySituation" :autosize="{ minRows: 6, maxRows: 8 }"></el-input>
          </el-form-item>
          <br />
          <el-form-item label="遗留问题" prop="remainingProblems">
            <el-input type="textarea" v-model="formInline.remainingProblems" :autosize="{ minRows: 6, maxRows: 8 }"></el-input>
          </el-form-item>
        </el-form>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="closeDialog" class="new-edition">取 消</el-button>
        <el-button @click="editSubmit" class="new-edition">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>
<script>
import { signIn } from '@/utils/peaceRightScreenApi'
export default {
  name: 'shiftsForm',
  props: {
    dialogVisible: {
      type: Boolean,
      default: false
    },
    shiftsData: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  data() {
    return {
      loading: false,
      rules: {},
      formInline: {
        dutySituation: '',
        remainingProblems: ''
      }
    }
  },
  mounted() {
    Object.assign(this.formInline, {
      dutySituation: this.shiftsData?.dutySituation ?? '正常',
      remainingProblems: this.shiftsData?.remainingProblems ?? '无'
    })
  },
  methods: {
    closeDialog() {
      this.$emit('closeDialogShifts')
      this.reset()
      this.$refs.formInline.resetFields()
      this.$forceUpdate()
    },
    // 重置
    reset() {
      this.formInline.dutySituation = ''
      this.formInline.remainingProblems = ''
    },
    /**
     * 排班保存
     */
    editSubmit() {
      this.loading = true
      const data = {
        ...this.formInline,
        id: this.shiftsData.id
      }
      signIn(data).then((res) => {
        this.loading = false
        const item = res.data
        if (item.code === '200') {
          this.$message.success(item.message)
          this.reset()
          this.$emit('sureShifts')
        } else {
          this.$message.error(item.message)
        }
      })
    }
  }
}
</script>

<style lang="scss" type="text/css" scoped>
::v-deep .personDialog {
  .el-dialog {
    width: 48.25rem;
    height: 35.8125rem;
    margin-top: 18vh !important;
    background-color: transparent !important;
    background-image: url('@/assets/images/table-bg-small.png') !important;
    background-repeat: no-repeat;
    background-size: 100% 100%;
    border: none;
    pointer-events: auto;
    box-shadow: none;
  }
  .el-dialog__body {
    padding: 10px 50px 10px 50px;
    height: calc(100% - 150px);
  }
  .dialog-title {
    display: inline-block;
    width: 100%;
    text-align: center;
    transform: translateY(0px);
    color: #cbdeed;
  }
  .dialog-title::before {
    display: none;
  }
  .el-dialog__header {
    height: 3.125rem;
    line-height: 3.125rem;
    padding: 0;
  }
  .el-dialog__headerbtn {
    transform: translateX(-36px);
    width: 25px;
    height: 25px;
    background-image: url('@/assets/images/close.png');
    background-repeat: no-repeat;
    background-size: 100% 100%;
    .el-dialog__close::before {
      display: none;
    }
  }
}
</style>
<style lang="scss">
.shift-form {
  .el-form-item {
    width: 100%;
    .el-form-item__content {
      width: calc(100% - 110px) !important;
    }
  }
}
</style>
