<template>
  <el-dialog class="component DialogEmergencyShield" v-if="visible" center v-dialogDrag :close-on-click-modal="false"
    :modal="false" :show-close="false" title="屏蔽报警" width="768px" custom-class="DialogEmergencyShield__dialog"
    :visible="visible" :before-close="closeDialog" v-loading="loadingStatus">
    <div class="DialogEmergencyShield__head">
      <div class="DialogEmergencyShield__close" @click="closeDialog"></div>
    </div>
    <div class="DialogEmergencyShield__content">
      <el-form class="DialogEmergencyShield__form" ref="formRef" :model="formModel" :rules="rules" label-width="82px"
        label-suffix=":">
        <el-form-item label="屏蔽时长" prop="duration">
          <el-input class="DialogEmergencyShield__duration" v-model="formModel.duration"
            onkeyup="value=value.replace(/^0|[^\d]/g,'')" placeholder="请输入">
            <template #append>
              分钟
            </template>
          </el-input>
        </el-form-item>
        <el-form-item label="屏蔽原因" prop="status">
          <el-radio-group v-model="formModel.status" @change="onStatusChange">
            <el-radio v-for="item of shieldConfig" :key="item.status" :label="item.status">{{ item.label }}</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="说明" prop="reason" :required="isOtherStatus">
          <TextTagArea v-model="formModel.reason" :limit="100" :tag="tag">
          </TextTagArea>
        </el-form-item>
      </el-form>
      <div class="DialogEmergencyShield__action">
        <el-button class="sino-button-sure" @click="closeDialog">取消</el-button>
        <el-button class="sino-button-sure" @click="onSubmit">确定</el-button>
      </div>
    </div>
  </el-dialog>
</template>
<script>

import TextTagArea from "./TextTagArea.vue";
import { shield } from '@/utils/peaceLeftScreenApi'
import { operatePlatform, shieldConfig } from "../emergency-constant";


export default {
  name: 'DialogEmergencyShield',
  components: {
    TextTagArea
  },
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    types: {
      type: String,
      default: ''
    },
    ids: {
      type: String,
      default: ''
    },
    objectIds: {
      type: String,
      default: ''
    },
  },
  emits: ['success'],
  data() {
    var reasonValidator = (_, value, callback) => {
      if (this.isOtherStatus && !value) {
        callback((new Error('请输入屏蔽说明')))
      } else {
        callback()
      }
    }
    return {
      formModel: {
        duration: '15',
        reason: '',
        status: 2
      },
      shieldConfig,
      rules: {
        duration: [{ required: true, message: '请输入屏蔽时常' }],
        status: [{ required: true, message: '请选择屏蔽原因' }],
        reason: [{ validator: reasonValidator }],
      },
      loadingStatus: false,
      eventBound: false,

    }
  },
  watch: {
    visible() {
      this.loadingStatus = false;
    }
  },
  computed: {
    isBatch: function () {
      return this.ids.split(',').length > 1
    },
    tag: function () {
      return shieldConfig.find(x => x.status == this.formModel.status).label
    },
    isOtherStatus() {
      return this.formModel.status == 99
    }
  },
  methods: {
    onStatusChange() {
      this.$refs.formRef.validateField('reason')
    },
    // 关闭弹窗
    closeDialog() {
      this.$emit('update:visible', false)
      this.$refs.formRef.resetFields();
    },
    onSubmit() {
      this.$refs.formRef.validate()
        .then(() => {
          this.loadingStatus = true;
          return this.doUpdateStatus()
        })
        .then(() => {
          this.$emit('success')
          this.closeDialog()
        })
        .catch(() => { })
        .finally(() => this.loadingStatus = false)
    },
    // 更新状态
    doUpdateStatus() {
      const defaultError = this.isBatch ? '批量操作失败' : '操作失败';
      const { status } = this.formModel
      const params = {
        shield: 1,
        alarmAffirm: this.isOtherStatus ? '' : status,
        shieldTime: this.formModel.duration,
        alarmId: this.ids,
        projectCode: this.codes,
        remarks: this.tag + this.formModel.reason,
        alarmObjectId: this.objectIds,
        alarmType: this.types,
        operationSource: operatePlatform.client
      }
      return shield(params)
        .then(res => {
          if (res.data.code == 200) {
            this.$message.success('操作成功')
          } else {
            throw res.data.msg || defaultError
          }
        })
    }
  }
}
</script>
<style lang="scss" scoped>
::v-deep .DialogEmergencyShield__dialog.el-dialog {
  background: url('~@/assets/images/qhdsys/768×453.png') no-repeat center center / 100% 100%;
  height: 453px;
  overflow: hidden;

  .el-dialog__header {
    padding: 10px 20px 10px;
    text-align: center;

    .el-dialog__title {
      height: 20px;
      font-size: 18px;
      font-family: Source Han Sans CN-Regular, Source Han Sans CN;
      font-weight: 400;
      color: #cbdeed;
      line-height: 18px;
    }
  }

  .el-dialog__body {
    height: calc(100% - 40px);
  }

  .el-dialog__footer {
    padding-top: 0;
    padding-bottom: 40px;
    padding-right: 40px;
  }
}

.DialogEmergencyShield {
  background: rgba(0, 0, 0, 0.6);

  &__head {
    position: absolute;
    top: 18px;
    right: 52px;
    display: flex;
  }

  &__close {
    width: 36px;
    height: 36px;
    background: url('@/assets/images/qhdsys/bg-icon-gb.png') no-repeat;
    cursor: pointer;
  }

  &__content {
    height: 100%;
    display: flex;
    flex-flow: column nowrap;
  }

  &__form {
    flex: 1;
    padding: 30px 35px 0;

    ::v-deep .el-form-item__label {
      color: #B0E3FA;
    }
  }

  &__action {
    text-align: right;
    padding: 30px 35px;
  }

  &__duration.el-input::v-deep {
    .el-input__inner {
      padding-right: 46px;

      &::placeholder {
        color: #A6AFBF;
      }
    }

    .el-input__suffix {
      right: 0;
      padding-left: 9px;
      padding-right: 9px;
      background: rgba(133, 145, 206, 0.15);
    }

    ::-webkit-inner-spin-button {
      display: none;
    }
  }
}
</style>
