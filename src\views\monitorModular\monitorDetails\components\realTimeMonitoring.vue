<template>
  <div class="rtm-container">
    <div class="rtm-container-left">
      <div class="rtm-container-left-header" style="height: calc(28.5%)">
        <div class="left-img-box">
          <div class="img-box">
            <img
              src="@/assets/images/cs.png"
              style="width: 100%; height: 100%"
              alt=""
            />
          </div>
          <div class="img-titel">11L FMER</div>
        </div>
        <div class="left-info-box">
          <div class="info-box">
            <span class="info-box-title">品牌：</span>
            <span class="info-box-content">飞利浦</span>
          </div>
          <div class="info-box">
            <span class="info-box-title">型号：</span>
            <span class="info-box-content">2024JLSB0001</span>
          </div>
          <div class="info-box">
            <span class="info-box-title">所属科室：</span>
            <span class="info-box-content">MR</span>
          </div>
          <div class="info-box">
            <span class="info-box-title">使用科室：</span>
            <span class="info-box-content">MR</span>
          </div>
          <div class="info-box">
            <span class="info-box-title">当前位置：</span>
            <span class="info-box-content">11号楼1层</span>
          </div>
        </div>
      </div>
      <div class="rtm-container-left-content" style="height: calc(71.5%)">
        <div class="left-content-title">更多</div>
        <div class="more-container">
          <div class="more-box" v-for="(item, index) in moreInfo" :key="index">
            <div class="more-box-title">{{ item.title }}</div>
            <div class="more-box-content">
              <span>{{ item.value }}</span>
              <span>{{ item.unit }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="rtm-container-right">
        <monitorEcharts></monitorEcharts>
    </div>
  </div>
</template>
<script>
export default {
    components: {
        monitorEcharts: () => import("./monitorEcharts")
    },
  data() {
    return {
      moreInfo: [
        {
          title: "液氮液位",
          value: "19",
          unit: "m",
        },
        {
          title: "供电次数",
          value: "10",
          unit: "次",
        },
        {
          title: "电流",
          value: "24.0",
          unit: "A",
        },
        {
          title: "电压",
          value: "25",
          unit: "V",
        },
        {
          title: "室内温度",
          value: "25.0",
          unit: "℃",
        },
        {
          title: "辐射剂量",
          value: "23",
          unit: "mGy",
        },
        {
          title: "是否漏电",
          value: "否",
          unit: "",
        },
      ],
    };
  },
  mounted() {},
  methods: {},
};
</script>
<style lang="scss" scoped>
.rtm-container {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: space-between;
  .rtm-container-left,
  .rtm-container-right {
    width: 49.5%;
    flex-shrink: 0;
  }
}
.rtm-container-left {
  &-header {
    display: flex;
    .left-img-box {
      margin-right: 1rem;
      .img-box {
        width: 10rem;
        height: 8rem;
      }
      .img-titel {
        margin-top: 0.5rem;
        font-size: 1rem;
        color: #fff;
        line-height: 2;
      }
    }
    .left-info-box {
      .info-box {
        height: 2rem;
        display: flex;
        align-items: center;
      }
    }
  }
  &-content {
    .left-content-title {
      font-size: 16px;
      margin-bottom: 1rem;
    }
    .more-container {
      overflow-y: auto;
      display: flex;
      flex-wrap: wrap;
      align-items: flex-start;
      .more-box {
        width: 7.25rem;
        height: 6.06rem;
        background: #3562db0f;
        border-radius: 4px;
        margin: 0px 1rem 1rem 0rem;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        &-title {
          margin-bottom: 1rem;
          color: #fff;
        }
        &-content {
          span:nth-child(1) {
            font-size: 1.5rem;
            color: #ffca64;
            margin-right: 0.1rem;
          }
          span:nth-child(2) {
            font-size: 0.75rem;
            color: #c2c4c8;
          }
        }
      }
      .more-box:nth-child(5n){
        margin-right: 0px;
      }
    }
  }
}
</style>