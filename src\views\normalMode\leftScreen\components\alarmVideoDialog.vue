<!--
 * @Description:
-->
<!--
 * @Description:
-->
<template>
  <el-dialog
    v-if="visible"
    v-dialogDrag
    :modal="false"
    :close-on-click-modal="false"
    :show-close="false"
    title="录像"
    width="40%"
    custom-class="alarmVideoDialog"
    :visible.sync="visible"
    :before-close="closeDialog"
  >
    <div class="dialog-right">
      <div class="dialog-tc" @click="closeDialog"></div>
    </div>
    <div class="content" style="padding: 10px">
      <!-- <video controls height="100%" width="100%" :autoplay="true" src="/alarmVideo.mp4" /> -->
    </div>
  </el-dialog>
</template>
<script>
export default {
  name: 'alarmVideoDialog',
  props: {
    visible: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
    }
  },
  mounted() {},
  methods: {
    // 关闭弹窗
    closeDialog() {
      this.$emit('update:visible', !this.visible)
    }
  }
}
</script>
<style lang="scss" scoped>
::v-deep .el-dialog {
  background: url('@/assets/images/qhdsys/768×453.png') no-repeat center center / 100% 100%;
  .el-dialog__header {
    padding: 15px 20px 10px;
    text-align: center;
    .el-dialog__title {
      height: 20px;
      font-size: 18px;
      font-family: Source Han Sans CN-Regular, Source Han Sans CN;
      font-weight: 400;
      color: #cbdeed;
      line-height: 18px;
    }
  }
  .el-dialog__body {
    height: calc(100% - 0px);
  }
}
.alarmVideoDialog {
  .dialog-right {
    position: absolute;
    top: 17px;
    right: 30px;
    display: flex;
    .dialog-tc {
      width: 36px;
      height: 36px;
      background: url('@/assets/images/qhdsys/bg-icon-gb.png') no-repeat;
    }
    .dialog-tc:hover {
      cursor: pointer;
    }
  }
  .content {
    width: 100%;
    height: 400px;
  }
}
</style>
