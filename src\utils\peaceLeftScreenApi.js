/*
 * @Author: hedd
 * @Date: 2023-06-13 13:47:35
 * @LastEditTime: 2024-12-09 14:10:39
 * @FilePath: \ihcrs_client_iframe\src\utils\peaceLeftScreenApi.js
 * @Description:
 */
/*
 * @Author: <PERSON>
 * @Date: 2021-12-14 15:39:39
 * @Last Modified by: mikey.zhaopeng
 * @Last Modified time: 2022-03-15 10:42:07
 */
import qs from 'qs'
import http from './http'

const iemcElevatorApi = __PATH.VUE_APP_IEMC_ELEVATOR_API
const planApi = __PATH.VUE_APP_RESERVE_PLAN_API
const iomsApi = __PATH.VUE_APP_IOMS_API
const warnApi = __PATH.VUE_APP_WARN_API
const iemcApi = __PATH.VUE_APP_IEMC_API
const spaceApi = __PATH.VUE_APP_SPACE_API
const wsServer = __PATH.VUE_APP_SOCKET

export function getCountAlarmSource(params) {
  return http.post(
    `${warnApi}/alarmRecord/countAlarmSource?${qs.stringify({
      ...params
    })}`
  )
}
// 获取报警统计
export function GetPoliceInfoByApp(params) {
  return http.getRequest(`${warnApi}/alarm/record/getPoliceInfoByApp`, {
    ...params
  })
}
// 获取报警来源统计
export function GetAlarmSourceCount(params) {
  return http.getRequest(`${warnApi}/alarm/record/getAlarmSourceCount`, {
    ...params
  })
}
// 获取全部报警记录
export function GetAlarmRecordAll(params) {
  return http.requestPost(`${warnApi}/alarm/record/selectAlarmRecordAll`, {
    ...params
  })
}
// 根据消息来源查询事件类型
export function getIncidentGroupByProjectCode(params) {
  return http.getRequest(`${warnApi}/alarm/record/getIncidentGroup`, {
    ...params
  })
}
// 获取事件类型以及报警来源分组
export function getSourceByEmpty(params) {
  return http.getRequest(`${warnApi}/alarm/record/getSource`, {
    ...params
  })
}
// 查询空间管理列表
export function getSpaceInfoList(params) {
  return http.requestPost(`${spaceApi}/space/spaceInfo/list`, {
    ...params
  })
}
// 获得空间结构的列表树
export function getStructureTree(params) {
  return http.getQueryQS(`${spaceApi}/space/structure/structureTree`, {
    ...params
  })
}
// 获取全部报警记录
export function GetAllAlarmRecord(params) {
  return http.requestPost(`${warnApi}/alarm/record/queryAllAlarmData`, {
    ...params
  })
}
// 获取全部报警记录
export function GetAllAlarmRecordIemc(params) {
  return http.requestPost(`${iemcApi}/medicalGasAlarm/selectAlarmRecordAll`, params)
}
// 获取报警趋势
export function GetAlarmTrendPc(params) {
  return http.requestPost(`${warnApi}/alarm/record/getAlarmTrendPc`, {
    ...params
  })
}
// 关闭报警记录
export function CloseAlarmRecord(params) {
  return http.requestPost(`${warnApi}/alarm/record/closeAlarmRecordById`, {
    ...params
  })
}
// 屏蔽报警记录
export function shield(params) {
  return http.requestPost(`${warnApi}/alarm/record/shield`, {
    ...params
  })
}
// 报警记录确警
export function AlarmAffirm(params) {
  return http.requestPost(`${warnApi}/alarm/record/updateAlarmAffirmById`, {
    ...params
  })
}
// 报警一键派单
export function OneKeyDispatch(params) {
  return http.requestPost(`${warnApi}/alarm/record/oneKeyDispatch`, {
    ...params
  })
}
// 查询备注
export function selectAlarmOperationOrRemarkById(params) {
  return http.requestPost(`${warnApi}/alarm/operation/selectAlarmOperationOrRemarkById`, {
    ...params
  })
}
// 报警记录备注
export function insertRemarkById(params) {
  return http.requestPost(`${warnApi}/alarm/operation/insertRemarkById`, {
    ...params
  })
}
// 获取报警详情
export function GetAlarmDetails(params) {
  return http.requestPost(`${warnApi}/alarm/record/selectAlarmRecordById`, {
    ...params
  })
}
export function getWorkOrderToAdd(params) {
  return http.postParamsQS(`${iomsApi}/iHCRSOperOrderController/toAdd`, params)
}
export function getWorkOrderOper(params) {
  return http.postParamsQS(`${iomsApi}/iHCRSOperOrderController/oper`, params)
}
export function limWorkInfo(params) {
  return http.requestPost(`${warnApi}/alarm/record/limWorkInfo`, {
    ...params
  })
}
export function getItemTreeData(params) {
  return http.postParamsQS(`${iomsApi}/iHCRSStatisticsController/bootstrapTreeData`, params)
}
export function getAllOffice(params) {
  return http.postParamsQS(`${iomsApi}/iHCRSStatisticsController/getAllOffice`, params)
}
// 查看空间详情信息
export function lookUpDataById(params) {
  return http.getQueryQS(`${spaceApi}/space/spaceInfo/lookUpById`, {
    ...params
  })
}
export function getDataByTypeTeam(params) {
  return http.postParamsQS(`${iomsApi}/iHCRSStatisticsController/getOCTeamInfo`, params)
}
export function getGridTreeData(params) {
  return http.postParamsQS(`${iomsApi}/iHCRSStatisticsController/getNewAllSpaceInfo`, params)
}
export function getEmergencyBook(params) {
  return http.postParamsQS(`${iomsApi}/iHCRSOperOrderController/getEmergencyBook`, params)
}
export function placeAndCancelOrder(params) {
  return http.postParamsQS(`${iomsApi}/iHCRSOperOrderController/placeAndCancelOrder`, params)
}
export function placeAndCancelSave(params) {
  return http.postParamsQS(`${iomsApi}/iHCRSOperOrderController/save`, params)
}
export function getWorkOrderDetail(params) {
  return http.postParamsQS(`${iomsApi}/iHCRSStatisticsController/getWorkOrderDetail`, params)
}
export function getIomsDictList(params) {
  return http.postParamsQS(`${iomsApi}/iHCRSOperOrderController/getDictList`, params)
}
export function getServicePersonName(params) {
  return http.postParamsQS(`${iomsApi}/iHCRSStatisticsController/getOCTeamMemberInfo`, params)
}
export function workOrderOperOrder(params) {
  return http.postParamsQS(`${iomsApi}/iHCRSOperOrderController/operOrder`, params)
}
export function toTeamsChangeTask(params) {
  return http.postParamsQS(`${iomsApi}/iHCRSOperOrderController/toTeamsChangeTask`, params)
}
export function addFeedback(params) {
  return http.postParamsQS(`${iomsApi}/iHCRSOperOrderController/addFeedback`, params)
}
export function updateTask(params) {
  return http.postParamsQS(`${iomsApi}/iHCRSOperOrderController/updateTask`, params)
}
export function pdEvaluationOk(params) {
  return http.postParamsQS(`${iomsApi}/iHCRSOperOrderController/updEvaluationOk`, params)
}
// 上传文件
export function uploadFiles(params) {
  return http.postFile(`${iomsApi}/minio/upload`, params)
}
export function getfactMaterialTreeData(params) {
  return http.postParamsQS(`${iomsApi}/iHCRSOperOrderController/treeData`, params)
}
export function getConsumablesList(params) {
  return http.postParamsQS(`${iomsApi}/iHCRSOperOrderController/getConsumablesList`, params)
}

export function getCountAlarmNum(params) {
  return http.post(
    `${warnApi}/alarmRecord/countAlarmNum?${qs.stringify({
      ...params
    })}`
  )
}
export function getCountAlarmTrend(params) {
  return http.post(
    `${warnApi}/alarmRecord/countAlarmTrend?${qs.stringify({
      ...params
    })}`
  )
}
export function getAlarmList(params) {
  return http.post(
    `${warnApi}/alarmRecord/findPageAll?${qs.stringify({
      ...params
    })}`
  )
}
export function getAlarmRecordDetailById(params) {
  return http.post(`${warnApi}/alarmRecord/getAlarmRecordValue?id=${params.id}`)
}
export function getAssociatedWorkOrderList(params) {
  return http.postParamsQS(`${iomsApi}/iHCRSStatisticsController/getAssociatedWorkOrderList`, params)
}
// 处置报警
export function updateAlarmRecordAndFiles(params) {
  return http.post(`${warnApi}/alarmRecord/updateAlarmRecordAndFiles`, params)
}
export function createWorkOrStartPlan(query) {
  return http.post(
    `${warnApi}/alarmRecord/manualCreateWork?${qs.stringify({
      ...query
    })}`
  )
}
export function startUpPlan(params) {
  return http.requestPost(`${planApi}/intelligencePrePlanEnactment/insertIntelligencePrePlanEnactment`, params, {}, 'plan')
}
export function getIntelligencePrePlanEnactmentValue(params) {
  return http.requestPost(
    `${planApi}/intelligencePrePlanEnactment/getIntelligencePrePlanEnactmentValueispid?${qs.stringify(params)}`, {}, {}, 'plan'
  )
}
export function exportExcel(params) {
  return http.blobPost(
    `${warnApi}/alarmRecord/exportExcel?${qs.stringify({
      ...params
    })}`
  )
}
// 获取ups统计信息
export function GetSurveyStatusByImhCode(params) {
  return http.getRequest(`${iemcApi}/surveyAndParameter/getSurveyStatusByImhCode`, {
    ...params
  })
}
// 获取空调分组以及照明分组
export function GetMenuGroup(params) {
  return http.requestPost(`${iemcApi}/airCondition/getMenuGroup`, {
    ...params
  })
}
// 医用气体实时监测
export function getRealMonitoringList(params) {
  return http.requestPost(`${iemcApi}/realMonitoring/getRealMonitoringList`, {
    ...params
  })
}
export function getRealMonitoringListNew(params) {
  return http.requestPost(`${iemcApi}/realMonitoring/getRealMonitoringListNew`, {
    ...params
  })
}
// 获取氧气环境监测
export function GetGasEnvParamNew(params) {
  return http.requestPost(`${iemcApi}/iemcParamState/getEnvParamNew`, {
    ...params
  })
}
// 获取氧气环境监测
export function GetRealMonitoringListGasGroup(params) {
  return http.requestPost(`${iemcApi}/realMonitoring/getRealMonitoringListGasGroup`, {
    ...params
  })
}

// 医用气体实时监测
export function getDetailsBySurveyCodes(params) {
  return http.requestPost(`${iemcApi}/realMonitoring/getDetailsBySurveyCodes`, {
    ...params
  })
}
// 运行监测总览数据
export function getMonitorStateNum(params) {
  return http.requestPost(`${iemcApi}/iemcSurveyState/getMonitorStateNum`, {
    ...params
  })
}
// 运行监测 科室状态监测
export function getParamStateNum(params) {
  return http.requestPost(`${iemcApi}/iemcParamState/getParamStateNum`, {
    ...params
  })
}
// 运行监测 站房数据
export function getEnvParam(params) {
  return http.requestPost(`${iemcApi}/iemcParamState/getEnvParam`, {
    ...params
  })
}
// 获取 scada视图数据
export function getScadaList(params) {
  return http.requestPost(`${iemcApi}/entityMenu/getScalaEntityMenuList`, {
    ...params
  })
}
// 获取 根据模块获取对应系统
export function getEntityMenuList(params) {
  return http.requestPost(`${iemcApi}/entityMenu/getEntityMenuList`, {
    ...params
  })
}
// 获取报警数量
export function getPoliceCount(params) {
  return http.requestPost(`${iemcElevatorApi}/policeHistory/getPoliceCount`, {
    ...params
  })
}
// 近30日报警统计图
export function getTrendStatisticLine(params) {
  return http.requestPost(
    `${iemcElevatorApi}/policeHistory/getTrendStatisticLine
  `,
    {
      ...params
    }
  )
}
// 获取按类型统计饼图
export function getReasonStatisticPie(params) {
  return http.requestPost(
    `${iemcElevatorApi}/policeHistory/getReasonStatisticPie
  `,
    {
      ...params
    }
  )
}
// 报警中心列表分页查询
export function getPoliceList(params) {
  return http.requestPost(
    `${warnApi}/policeHistory/getPoliceList
  `,
    {
      ...params
    }
  )
}
// 报警批处理
export function disposePoliceBatch(params) {
  return http.requestPost(
    `${iemcElevatorApi}/policeHistory/disposePoliceBatch
  `,
    {
      ...params
    }
  )
}

/** 设置报警记录为经典案例-通过id */
export function setAlarmRecordToClassics(params) {
  return http.requestPost(`${warnApi}/alarm/record/setAlarmRecordToClassics`, params)
}

/** 处理报警-通过id */
export function handleAlarmAffirmById(params) {
  return http.requestPost(`${warnApi}/alarm/record/handleAlarmAffirmById`, params)
}

/** 分页查询巡检记录，主要用于报警详情任务分页 */
export function getInspectionTaskVoPage(params) {
  return http.requestPost(`${warnApi}/alarm/record/getInspectionTaskVoPage`, params)
}

/** 添加总结分析 */
export function alarmSummarySave(params) {
  return http.requestPost(`${warnApi}/alarm/alarmSummary/save`, params)
}

/** 查询报警录像列表 */
export function queryLimAlarmFile(params) {
  return http.requestPost(`${warnApi}/alarmFile/queryLimAlarmFile`, params)
}

/** 报警详情，生成图片二维码 */
export function queryAlarmAffirmQrCodeBase64(params) {
  return http.requestPost(`${warnApi}/wartime/queryAlarmAffirmQrCodeBase64`, params)
}

/** 根据报警系统 获取报警类型 */
export function getAlarmTypeByProjectCode(params) {
  return http.getRequest(`${warnApi}/alarm/record/getAlarmTypeByProjectCode`, params)
}

/** 根据报警系统 获取报警类型（新） fix: 43690 */
export function queryFieldsConfigList(params) {
  return http.postRequest(`${warnApi}/alarm/fieldsConfig/queryFieldsConfigList`, params)
}

/** 根据报警系统 获取监控实时画面 */
export function getCameraListByImsId(params) {
  return http.postFormData(`${iemcApi}/cameraManage/getCameraListByImsId`, params)
}

/** 报警系统 文件上传 */
export function emergencyUploadFiles(params) {
  return http.postFile(`${planApi}/preplanDrillTask/upload`, params)
}

/**
 * 报警详情，二维码扫码上传文件后消息
 * @param alarmId 报警ID
 */
export function fileUploadSocket(alarmId) {
  return http.websocketService(`${wsServer}/alarmServer/wartime/${alarmId}`)
}
