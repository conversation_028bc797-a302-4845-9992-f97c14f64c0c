<template>
  <div class='component EmergencyExecuteBaseInfo'>
    <div class="EmergencyExecuteBaseInfo__description">
      <div class="content-head">
        <div class="content-head__title">关联人员信息</div>
        <div class="content-head__extend">
        </div>
      </div>
      <div class="EmergencyExecuteBaseInfo__description__content">
        <ul class="description__content">
          <li style="margin-top: 0;">
            <div>区域责任部门</div>
            <div>{{ handleProcessInfo.liabilityDeptName | defaultVal }}</div>
          </li>
          <li style="margin-top: 0;">
            <div>区域负责人</div>
            <div>{{ handleProcessInfo.liabilityPersonName | defaultVal }}</div>
          </li>
          <li>
            <div>受理警情队伍</div>
            <div :title="handleProcessInfo.acceptAlarmTeamName | defaultVal">
              {{ handleProcessInfo.acceptAlarmTeamName | defaultVal }}
            </div>
          </li>
          <li>
            <div>值班人员</div>
            <div>{{ handleProcessInfo.operatorOnDuty | defaultVal }}</div>
          </li>
          <li class="full-row">
            <div>应急队员</div>
            <div :title="handleProcessInfo.emergencyTeamMembers | defaultVal">
              {{ handleProcessInfo.emergencyTeamMembers | defaultVal }}
            </div>
          </li>
        </ul>
        <el-table v-loading="loadStatus" ref="tableRef" :height="tableHeight"
          class="EmergencyExecuteBaseInfo__description__table" :data="taskList" stripe>
          <el-table-column label="任务类型" prop="taskType"></el-table-column>
          <el-table-column label="任务名称" prop="taskName" show-overflow-tooltip></el-table-column>
          <el-table-column label="执行人员" prop="executionPersonName"></el-table-column>
          <el-table-column label="执行时间" prop="executionTime" width="175px"></el-table-column>
          <el-table-column label="任务状态" prop="finish">
            <template #default="{ row }">
              <div v-if="row.taskStatus == 2"
                class="EmergencyExecuteBaseInfo__description__table__tag EmergencyExecuteBaseInfo__description__table__tag--finish">
                <img src="@/assets/images/icon-5.png">
                <span>已完成</span>
              </div>
              <div v-else
                class="EmergencyExecuteBaseInfo__description__table__tag EmergencyExecuteBaseInfo__description__table__tag--incomplete">
                <img src="@/assets/images/icon-2.png">
                <span>未完成</span>
              </div>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </div>
  </div>
</template>


<script>
import { getInspectionTaskVoPage } from '@/utils/peaceLeftScreenApi';

export default {
  name: 'EmergencyExecuteBaseInfo',
  props: {
    tableHeight: {
      type: String,
      default: '200px'
    }
  },
  data() {
    return {
      taskList: [],
      pageNo: 0,
      totalPages: 0,
      loadStatus: false, // 正在加载数据
    }
  },
  computed: {
    baseInfo: function () {
      return this.$parent?.detail?.record ?? {}
    },
    handleProcessInfo: function () {
      return this.$parent?.detail?.handleProcessInfo ?? {}
    },
    canFetch: function () {
      return !this.loadStatus && this.pageNo < this.totalPages
    }
  },
  filters: {
    defaultVal: function (val) {
      return val || "-"
    },
  },
  mounted() {
    this.getDataList();
    // 为table注册滚动事件
    this.$refs.tableRef.bodyWrapper.addEventListener("scroll", this.onTableScroll)
  },
  methods: {
    // 拉取新数据
    getDataList() {
      this.loadStatus = true;
      const params = {
        alarmDeviceId: this.baseInfo.alarmDeviceId,
        alarmStartTime: this.baseInfo.alarmStartTime,
        pageNo: this.pageNo + 1,
        pageSize: 10,
      }
      getInspectionTaskVoPage(params)
        .then(res => {
          if (res.data.code == 200) {
            const { pages, records, current } = res.data.data;
            this.taskList.push(...records)
            this.totalPages = pages
            this.pageNo = current
          } else {
            this.$message.error(res.data.msg || '获取任务数据失败')
          }
        })
        .finally(() => this.loadStatus = false)
    },
    onTableScroll(event) {
      if (!this.canFetch) return;
      const { scrollHeight, scrollTop, offsetHeight } = event.target;
      // buffer值设定100
      if (scrollHeight - offsetHeight - scrollTop < 100) {
        this.getDataList()
      }
    }

  }
}


</script>

<style lang='scss' scoped>
.component.EmergencyExecuteBaseInfo {
  display: flex;
  flex-flow: column nowrap;
  height: 100%;
  overflow: hidden;

  .content-head {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px;
    height: 52px;
  }


  .description {
    &__content {
      display: flex;
      flex-flow: row wrap;

      >li {
        flex-basis: 50%;
        display: flex;
        flex-flow: row nowrap;
        margin-top: 24px;

        &.full-row {
          flex-basis: 100%;
        }

        >div:first-of-type {
          color: #B0E3FA;
          width: 100px;
          text-align: right;

          &::after {
            content: '：';
          }

        }

        >div:last-child {
          width: 0;
          flex: 1;
          overflow: hidden;
          white-space: nowrap;
          text-overflow: ellipsis;
        }
      }
    }

    &__imgs {
      display: flex;
      flex-flow: row wrap;
    }

    &__img {
      height: 100px;
      width: 100px;
      overflow: hidden;
      margin: 0 10px 10px 0;

      >img {
        height: 100%;
        width: 100%;
        object-fit: cover;
        object-position: center;
      }
    }
  }

  .EmergencyExecuteBaseInfo {
    &__description {
      flex: 1;
      background: rgba(133, 145, 206, 0.15);
      overflow: hidden;
      display: flex;
      flex-flow: column nowrap;
      padding-bottom: 16px;

      &__content {
        padding: 0 16px;
        overflow: auto;
      }

      &__table {
        margin-top: 24px;

        &__tag {
          display: flex;
          align-items: center;

          >img {
            height: 16px;
            width: 16px;
            margin-right: 2px;
          }

          &--finish {
            >span {
              color: #61E29D;
            }
          }

          &--incomplete {
            >span {
              color: #FF2D55;
            }
          }
        }
      }
    }
  }
}
</style>
