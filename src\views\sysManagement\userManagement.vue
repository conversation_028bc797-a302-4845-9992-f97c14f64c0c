<template>
  <!-- 用户管理 -->
  <div class="content special_box">
    <div class="top_content" style="margin-bottom: 20px">
      <el-input
        style="width: 217px; margin-right: 20px"
        placeholder="请输入姓名"
        v-model="searchDataObj.staffName"
        maxlength="25"
        @keyup.enter.native="_searchByCondition"
        onkeyup="if(value.length>25)value=value.slice(0,25)"
      ></el-input>
      <el-input
        style="width: 217px; margin-right: 20px"
        placeholder="请输入登录名"
        v-model="searchDataObj.userName"
        maxlength="25"
        @keyup.enter.native="_searchByCondition"
        onkeyup="if(value.length>25)value=value.slice(0,25)"
      ></el-input>
      <el-input
        style="width: 217px; margin-right: 20px"
        placeholder="请输入手机号"
        v-model="searchDataObj.phone"
        maxlength="25"
        @keyup.enter.native="_searchByCondition"
        onkeyup="if(value.length>25)value=value.slice(0,25)"
      ></el-input>
      <el-button class="sino-button-sure" style="margin-left: 30px" @click="_searchByCondition">查询</el-button>
      <el-button class="sino-button-sure" @click="_resetCondition">重置</el-button>
      <el-button class="sino-button-sure" style="float: right" @click="selectRowData({}, 'add')"
        ><div class="img-add-icon"></div>
        新增</el-button
      >
    </div>
    <!-- </div> -->
    <div class="table_list">
      <el-table
        :data="tableData"
        height="calc(100% - 40px)"
        :cell-style="$tools.setCell(3)"
        :header-cell-style="$tools.setHeaderCell(3)"
        style="width: 100%"
        v-loading="tableLoading"
        element-loading-background="rgba(0, 0, 0, 0.2)"
      >
        <el-table-column fixed type="index" width="80" show-overflow-tooltip label="序号"></el-table-column>
        <el-table-column fixed prop="userName" show-overflow-tooltip label="登录名"></el-table-column>
        <el-table-column fixed prop="staffName" show-overflow-tooltip label="姓名"></el-table-column>
        <el-table-column fixed prop="phone" show-overflow-tooltip label="电话"></el-table-column>
        <el-table-column fixed prop="jobNumber" show-overflow-tooltip label="工号"></el-table-column>
        <el-table-column fixed prop="roleName" show-overflow-tooltip label="角色"></el-table-column>
        <el-table-column fixed prop="state" show-overflow-tooltip label="状态">
          <template slot-scope="scope">
            <el-switch :active-value="0" :inactive-value="1" active-color="#FFE3A6" inactive-color="#5996F9" @change="switchHandle(scope.row)" v-model="scope.row.state"></el-switch>
          </template>
        </el-table-column>
        <el-table-column fixed show-overflow-tooltip label="操作">
          <template slot-scope="scope">
            <div class="operationBtn">
              <span @click="selectRowData(scope.row, 'detail')">详情</span>
              <span @click="selectRowData(scope.row, 'edit')" style="margin: 0 10px">编辑</span>
              <span @click="selectRowData(scope.row, 'del')">删除</span>
            </div>
          </template>
        </el-table-column>
      </el-table>
      <el-pagination
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page="currentPage"
        :page-sizes="[15, 20, 30, 40]"
        :page-size="pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
        class="pagination"
      ></el-pagination>
    </div>
    <!-- 新增用户弹框 -->
    <template v-if="addDialogShow">
      <addUsers ref="addUsers" :dialogTitle="dialogTitle" :rowData="rowData" :type="type" :addDialogShow="addDialogShow" @userSure="userSure" @closeDialog="closeDialog"></addUsers>
    </template>
  </div>
  <!-- </sinoPanel> -->
</template>
<script type="text/ecmascript-6">
import addUsers from './dialogComponents/addUsers'
import { getUserPage, delUser, userState } from '@/utils/api'
export default {
  name: 'userManagement',
  components: {
    addUsers
  },
  data() {
    return {
      tableData: [],
      currentPage: 1,
      pageSize: 15,
      total: 0,
      addDialogShow: false,
      tableLoading: false,
      searchDataObj: {
        staffName: '',
        userName: '',
        phone: '',
        roleId: ''
      },
      dialogTitle: '',
      rowData: {},
      type: '',
      dialogType: {
        add: '新建',
        edit: '修改',
        detail: '查看'
      }
    }
  },
  mounted() {
    this._resetCondition()
  },
  methods: {
    // 选中列表行数据
    selectRowData(row, type) {
      if (type === 'del') {
        this.userDelFn(row.id)
      } else {
        this.addDialogShow = true
        this.rowData = row
        this.type = type
        this.dialogTitle = this.dialogType[type] + '用户'
      }
    },
    // 查询
    _searchByCondition() {
      this.currentPage = 1
      this.userTableList()
    },
    // 重置
    _resetCondition() {
      this.searchDataObj = {
        staffName: '',
        userName: '',
        phone: '',
        roleId: ''
      }
      this.currentPage = 1
      this.pageSize = 15
      this.total = 0
      this.userTableList()
    },
    // 获取用户列表
    userTableList() {
      const params = JSON.parse(JSON.stringify(this.searchDataObj))
      Object.assign(params, {
        page: this.currentPage,
        pageSize: this.pageSize
      })
      this.tableLoading = true
      getUserPage(params).then((res) => {
        // console.log(res)
        const data = res.data
        this.tableLoading = false
        if (data.code === '200') {
          this.tableData = data.data.records
          this.total = data.data.total
        } else {
          this.$message({
            message: data.msg,
            type: 'warning'
          })
        }
      })
    },
    // 状态切换
    switchHandle(row) {
      const params = {
        id: row.id,
        state: row.state
      }
      if (row.state === 1) {
        this.$confirm('关闭状态会导致该用户无法登录, 确认要关闭吗?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          cancelButtonClass: 'sino-button-sure',
          confirmButtonClass: 'sino-button-sure',
          customClass: 'confirm-box-class',
          type: 'warning'
        })
          .then(() => {
            this.changeUserState(params)
          })
          .catch(() => {
            this.userTableList()
          })
      } else {
        this.changeUserState(params)
      }
    },
    // 改变用户状态
    changeUserState(params) {
      userState(params).then((res) => {
        const data = res.data
        this.tableLoading = false
        if (data.code === '200' && data) {
          this.$message({
            type: 'success',
            message: '切换状态成功！'
          })
          this.currentPage = this.currentPage === 1 ? 1 : this.currentPage
        } else {
          this.$message({
            type: 'warning',
            message: data.msg
          })
        }
        this.userTableList()
      }).catch(() => {
        console.log('catch')
        this.userTableList()
      })
    },
    // 删除角色用户
    userDelFn(id) {
      this.$confirm('删除后将无法恢复, 确认要删除该记录吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        cancelButtonClass: 'sino-button-sure',
        confirmButtonClass: 'sino-button-sure',
        customClass: 'confirm-box-class',
        type: 'warning'
      })
        .then(() => {
          this.tableLoading = true
          delUser(id).then((res) => {
            const data = res.data
            this.tableLoading = false
            if (data.code === '200') {
              this.$message({
                type: 'success',
                message: '删除成功！'
              })
              this.currentPage = this.currentPage === 1 ? 1 : this.currentPage
              this.userTableList()
            } else {
              this.$message({
                type: 'warning',
                message: data.msg
              })
            }
          })
        })
        .catch(() => {})
    },
    handleSizeChange(val) {
      this.pageSize = val
      this.userTableList()
    },
    handleCurrentChange(val) {
      this.currentPage = val
      this.userTableList()
    },
    closeDialog() {
      this.addDialogShow = false
    },
    userSure(type) {
      this.addDialogShow = false
      if (type === 'detail') {
        return false
      } else if (type === 'add') {
        this._resetCondition()
      } else {
        this.userTableList()
      }
    }
  }
}
</script>
<style lang="scss" scoped>
.pagination {
  // bottom: 22px;
  // text-align: right;
}
.content {
  position: relative;
  width: 80%;
  height: 97%;
  background-color: #031553;
  box-sizing: border-box;
  padding: 20px 10px 10px 10px;
  margin: 10px 10px 10px 10%;

  .table_list {
    height: calc(100% - 60px);
    // overflow-y: scroll;
  }
  ::v-deep .el-switch__core:after {
    background-color: #1e3063;
  }
  ::v-deep .el-switch__core {
    width: 2.5rem!important;
  }
}
</style>
