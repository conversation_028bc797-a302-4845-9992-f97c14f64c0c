<template>
  <div class="main">
    <el-dialog v-dialogDrag :modal="false" :close-on-click-modal="false" :visible.sync="controlReportDialogShow" custom-class="mainDialog main" :before-close="closeDialog" class="personDialog">
      <template slot="title">
        <span class="dialog-title">控制报表</span>
      </template>
      <div class="timeline_content">
        <el-timeline>
          <el-timeline-item v-for="(item, index) in controlLineList" :key="index" placement="top">
            <div class="time_row" @click="collectEvent('item', index)">
              <span>{{ item.date }}</span>
              <span>执行反馈</span><span>总共控制：{{ item.total }}条</span>
              <span>执行成功：{{ item.isSuccess }}条</span>
              <span>执行失败：{{ item.isFail }}条</span>
              <i style="display: inline-block" :ref="'itemright' + index" class="el-icon-arrow-right title-icon"></i>
              <i v-show="false" :ref="'itemdown' + index" class="el-icon-arrow-down title-icon"></i>
            </div>
            <TransitionHeight :ref="'item' + index" :heigtRef="'heigtBox' + index">
              <div class="detail_ul">
                <div v-for="(loop, idx) in item.returnMap" :key="idx">
                  <span style="color: #7eaef9">{{ loop.outPutName }}</span>
                  <span style="color: #fff">{{ operationList[loop.forceSwitch ?? loop.outputStatus] }}</span>
                  <span><i :class="loop.code == 200 ? 'el-icon-circle-check' : 'el-icon-warning-outline'" :style="{ color: loop.code == 200 ? '#34b253' : '#ff4848' }"></i></span>
                  <span v-if="loop.code == 200" style="color: #ffe3a6; float: right; padding: 0 31px; font-weight: 600">成功</span>
                  <div v-else class="top_type_item" @click="resend(loop)">
                    <span>重新发送</span>
                  </div>
                  <!-- <el-button v-else slot="reference" style="float: right" class="sino-button-sure" @click="resend(loop)">重新发送</el-button> -->
                </div>
              </div>
            </TransitionHeight>
          </el-timeline-item>
        </el-timeline>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import TransitionHeight from '@/components/common/transitionHeight.vue'
import moment from 'moment'
export default {
  name: 'controlReportDialog',
  props: {
    controlReportDialogShow: {
      type: Boolean,
      default: false
    },
    dialogData: {
      type: Object,
      default: () => {}
    }
  },
  components: {
    TransitionHeight
  },
  data() {
    return {
      controlLineList: [
        {
          date: '11:42:12',
          total: 5,
          isSuccess: 3,
          isFail: 2,
          returnMap: [
            {
              outPutName: '1号灯',
              forceSwitch: 1,
              code: 200
            },
            {
              outPutName: '2号灯',
              forceSwitch: 0,
              code: 400
            },
            {
              outPutName: '3号灯',
              forceSwitch: 1,
              code: 200
            },
            {
              outPutName: '4号灯',
              forceSwitch: 0,
              code: 400
            },
            {
              outPutName: '5号灯',
              forceSwitch: 1,
              code: 200
            }
          ]
        },
        {
          time: '11:45:12',
          sumNum: 8,
          successNum: 5,
          errorNum: 3
        },
        {
          time: '12:22:12',
          sumNum: 4,
          successNum: 3,
          errorNum: 1
        }
      ],
      operationList: ['关闭', '开启', '', '强开', '强关']
    }
  },
  mounted() {
    // console.log(this.dialogData);
    this.dialogData.date = moment(this.dialogData.date).format('HH:mm:ss')
  },
  methods: {
    // 展开关闭事件
    collectEvent(box, i) {
      if (this.$refs[box + 'right' + i][0].style.display === 'inline-block') {
        this.$refs[box + 'right' + i][0].style.display = 'none'
        this.$refs[box + 'down' + i][0].style.display = 'inline-block'
        this.$refs[box + i][0].show = true
      } else {
        this.$refs[box + 'right' + i][0].style.display = 'inline-block'
        this.$refs[box + 'down' + i][0].style.display = 'none'
        this.$refs[box + i][0].show = false
      }
    },
    // 重新发送
    resend(item) {
      const type = item.forceSwitch ?? item.outputStatus
      const params = {
        type: 1,
        actuatorId: item.actuatorId,
        outputNum: item.outPutNum,
        outputStatus: type === 3 ? '1' : type === 4 ? '0' : type === 5 ? null : type,
        forceSwitch: type === 1 || type === 0 ? null : type
      }
      this.$http.lightOpenOrClose(params).then((res) => {
        if (res.code === 200) {
          if (res.data.code === 200) {
            this.$message.success(res.data.returnMap[0].outputStatus)
          } else {
            this.$message.warning(res.data.returnMap[0].outputStatus)
          }
          // this.searchFromChange()
        } else {
          this.searchFromChange()
        }
        this.dialogData.returnMap.map((e) => {
          if (e.actuatorId === params.actuatorId && e.outPutNum === params.outPutNum) {
            e.code = res.code
          }
        })
      })
    },
    closeDialog() {
      this.$emit('closeControlDialog')
    }
  }
}
</script>
<style lang="scss" scoped>
::v-deep .mainDialog {
  width: 50% !important;
  height: 70% !important;
  min-width: 562px !important;
  min-height: 376px !important;
  .el-dialog__body {
    padding: 1.875rem 2rem 0.625rem 2rem;
  }
  .timeline_content {
    // padding: 30px 50px;
    .el-timeline-item {
      padding-bottom: 10px;
      .el-timeline-item__tail {
        display: none;
      }
    }
    .el-timeline-item__node {
      display: none;
    }
    .el-timeline-item__timestamp {
      // font-size: 16px;
      display: none;
    }
    .time_row {
      color: #ffe3a6;
      height: 30px;
      line-height: 30px;
      cursor: pointer;
      background: #263057;
      border-radius: 0.375rem;
      font-size: 0.8125rem;
      span {
        margin: 0 15px;
      }
      span:nth-child(2) {
        font-weight: 600;
      }
      i {
        float: right;
        margin-right: 0.3125rem;
        margin-top: 9px;
        color: #ffe3a6;
      }
    }
    .detail_ul {
      margin-top: 15px;
      > div {
        height: 45px;
        line-height: 45px;
        padding: 0 10px;
        font-size: 15px;
        span {
          margin: 0 15px;
          // color: #7eaef9;
          i {
            font-size: 18px;
          }
        }
        .top_type_item {
          float: right;
          width: fit-content;
          margin: 3px 2%;
          height: 2.2rem;
          line-height: 2.2rem;
          text-align: center;
          font-size: 14px;
          font-family: PingFang-SC-Medium, PingFang-SC;
          color: #93bdff;
          background: url('~@/assets/images/center/block-bg.png') no-repeat;
          background-size: 100% 100%;
          cursor: pointer;
          &:hover {
            color: #fff;
            background: url('~@/assets/images/center/block-bg-active.png') no-repeat;
            background-size: 100% 100%;
          }
        }
      }
    }
  }
}
</style>
