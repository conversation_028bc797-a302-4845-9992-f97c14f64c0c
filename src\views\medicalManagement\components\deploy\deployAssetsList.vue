<template>
  <div class="list-index-view">
    <div class="view-header-box">
      <div class="header-left">
        <el-form :model="searchForm" class="search-form" inline ref="formRef">
          <el-form-item>
            <el-input v-model="searchForm.keyWord" placeholder="资产名称/编码/通用名" size="small"></el-input>
          </el-form-item>
          <el-form-item>
            <el-select v-model="searchForm.dept" placeholder="借用科室" size="small">
              <el-option v-for="item in deptOptions" :key="item.value" :label="item.label" :value="item.value">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-select v-model="searchForm.state" placeholder="调配状态" size="small">
              <el-option v-for="item in stateOptions" :key="item.value" :label="item.label" :value="item.value">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-select v-model="searchForm.assetssState" placeholder="资产状态" size="small">
              <el-option v-for="item in stateOptions" :key="item.value" :label="item.label" :value="item.value">
              </el-option>
            </el-select>
          </el-form-item>
        </el-form>
      </div>
      <div class="header-right">
        <el-button class="sino-button-sure" @click="resetForm">重置</el-button>
        <el-button class="sino-button-sure" @click="handleSearchForm">查询</el-button>
      </div>
    </div>
    <div class="view-content">
      <div class="dts-nav-box">
        <div class="nav-box" :class="{ active: index == navIdx }" v-for="(item, index) in navOptions" :key="index"
          @click="handleNavIdx(index)">
          <div class="nav-div">{{ item.label }}（{{item.number}}）</div>
        </div>
      </div>
      <div class="table-view">
        <el-table ref="table" v-loading="tableLoading" :data="tableData" :resizable="false" height="calc(100%)"
          :cell-style="$tools.setCell(3)" :header-cell-style="$tools.setHeaderCell(3)" stripe
          @selection-change="handleSelectionChange">
          <el-table-column type="selection" width="55" align="center" class-name="EmergencyDataTable__cell--selection">
          </el-table-column>
          <el-table-column prop="state" label="调配状态" show-overflow-tooltip>
            <template slot-scope="scope">
              <div class="status-box">
                <img class="table-icon" :src='icon_5' />
                <span style="color:#61E29D">{{scope.row.state}}</span>
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="dept" label="借用科室" show-overflow-tooltip></el-table-column>
          <el-table-column prop="assetsName" label="资产名称" show-overflow-tooltip></el-table-column>
          <el-table-column prop="assetsCode" label="资产编码" show-overflow-tooltip></el-table-column>
          <el-table-column prop="commonName" label="通用名" show-overflow-tooltip></el-table-column>
          <el-table-column prop="assetSn" label="SN码" show-overflow-tooltip></el-table-column>
          <el-table-column prop="acceptance" label="验收" show-overflow-tooltip>
            <template slot-scope="scope">
              <div class="status-box">
                <img class="table-icon" :src='icon_6' />
                <span style="color:#D4DEEC">{{scope.row.acceptance}}</span>
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="brandName" label="品牌" show-overflow-tooltip></el-table-column>
          <el-table-column prop="modelName" label="规格" show-overflow-tooltip></el-table-column>
          <el-table-column label="操作" width="260px">
            <template slot-scope="scope">
              <span class="operation-span">结算方式设置</span>
              <span class="operation-span">租赁记录</span>
              <span class="operation-span">运行情况</span>
            </template>
          </el-table-column>
        </el-table>
        <el-pagination :current-page="currentPage" :page-sizes="[15, 20, 30, 40]" :page-size="pageSize"
          layout="total, sizes, prev, pager, next, jumper" :total="total" class="pagination"
          @size-change="handleSizeChange" @current-change="handleCurrentChange"></el-pagination>
      </div>
    </div>
  </div>
</template>
  <script>
import icon_5 from '@/assets/images/icon-5.png'
import icon_6 from '@/assets/images/icon-6.png'
export default {
  name: 'leaseList',
  components: {
  },
  data() {
    return {
      icon_5,
      icon_6,
      tableLoading: false,
      searchForm: {
        keyWord: "",
        dept: "",
        state: "",
        assetssState: '',
      },
      deptOptions: [],
      stateOptions: [],
      navIdx: 0,
      currentPage: 1,
      pageSize: 15,
      total: 5,
      tableData: [],
      multipleSelection: [],
      navOptions: [
        { label: "全部", value: "#", number: 3 },
        { label: "呼吸机", value: "list", number: 3 },
        { label: "眼科手术器械", value: "list", number: 12 },
        { label: "神经外科手术器械", value: "list", number: 1 },
        { label: "基础外科手术器械", value: "list", number: 1 },
        { label: "测试", value: "list", number: 1 },
      ],
      navIdx: 0,
    };
  },
  mounted() {
    this.getDataList()
  },
  methods: {
    handleNavIdx(idx) {
      this.navIdx = idx;
    },
    getDataList() {
      this.tableData = [
        { state: '可借用', dept: '--', assetsName: "飞利浦CT", assetsCode: "flp-ct", commonName: '--', assetSn: '--', acceptance: '不需要', brandName: "飞利浦", modelName: "Achieva 3.0" },
        { state: '可借用', dept: '--', assetsName: "飞利浦CT", assetsCode: "flp-ct", commonName: '--', assetSn: '--', acceptance: '不需要', brandName: "飞利浦", modelName: "Achieva 3.0" },
        { state: '可借用', dept: '--', assetsName: "飞利浦CT", assetsCode: "flp-ct", commonName: '--', assetSn: '--', acceptance: '不需要', brandName: "飞利浦", modelName: "Achieva 3.0" },
      ]
    },
    /** 重置 */
    resetForm() {
      this.$refs.formRef.resetFields()
      this.getDataList()
    },
    /** 查询 */
    handleSearchForm() {
      this.$refs.formRef.resetFields()
      this.getDataList()
    },
    /** 条数变化事件 */
    handleSizeChange(val) {
      this.pageSize = val
      this.getDataList()
    },
    /** 页数变化事件 */
    handleCurrentChange(val) {
      this.currentPage = val
      this.getDataList()
    },
    /** 多选table */
    handleSelectionChange(val) {
      this.multipleSelection = val;
    },
  },
};
</script>
<style lang="scss" scoped>
.list-index-view {
  position: relative;
  height: 100%;
  width: 100%;
  padding: 0px 40px;
}
.view-header-box {
  display: flex;
  justify-content: space-between;
}
.view-content {
  height: calc(100% - 4rem);
  .dts-nav-box {
    margin-bottom: 1.2rem;
    display: flex;
    align-items: center;
    box-sizing: border-box;
    .nav-box {
      height: 2.5rem;
      line-height: 2.5rem;
      width: calc(100% / 6);
      font-size: 1rem;
      text-align: center;
      color: #a4afc1;
      cursor: pointer;
      box-sizing: border-box;
      border: 1px dashed #26314f;
      border-right: none;
      background: url(@/assets/images/nav-bg.png) no-repeat;
      background-size: cover;
    }
    .nav-box:last-child {
      border-right: 1px dashed #26314f;
    }
    .active {
      color: #b0e3fa;
      background: url(@/assets/images/nav-bg-xz.png) no-repeat;
      background-size: cover;
    }
  }
  .table-view {
    height: calc(100% - 130px) !important;
  }
}
.operation-span {
  cursor: pointer;
  color: #8bddf5;
}
.operation-span:nth-child(2) {
  margin: 0 10px;
  color: rgba(133, 145, 206, 0.5);
}

::v-deep
  .el-table--striped
  .el-table__body
  tr.el-table__row--striped
  td.el-table__cell {
  background: none;
}
.table-icon {
  width: 16px;
  margin-right: 3px;
}
.status-box {
  display: flex;
  align-items: center;
}
</style>
