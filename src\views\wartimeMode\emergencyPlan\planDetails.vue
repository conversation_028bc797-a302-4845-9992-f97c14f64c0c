<template>
  <div class="planDetails">
    <div class="planDetails-main">
      <div class="main-title"><i class="el-icon-arrow-left" @click="$router.go(-1)"></i>预案详情</div>
      <el-button class="usePlanBtnBgd" v-if="$route.query?.planType == 0" @click="startPlanSendWpf">使用预案</el-button>
      <div class="main-content">
        <el-tabs v-model="activeTabs">
          <el-tab-pane label="基础信息" name="1">
            <el-row :gutter="24" style="margin: 0">
              <el-col :md="22">
                <p class="info-item">
                  <span class="info-label">预案名称：</span>
                  <span class="info-value">{{ planData.planName }}</span>
                </p>
              </el-col>
            </el-row>
            <el-row :gutter="24" style="margin: 0">
              <el-col :md="22">
                <p class="info-item">
                  <span class="info-label">关联预案类型：</span>
                  <span class="info-value">{{ planData.planType ? planTypeList.find(v => v.imhId == planData.planType)?.imhMonitorName : '' }}</span>
                </p>
              </el-col>
            </el-row>
            <el-row :gutter="24" style="margin: 0">
              <el-col :md="22">
                <p class="info-item">
                  <span class="info-label">报警类型：</span>
                  <span class="info-value">{{ planData.alarmTypeName }}</span>
                </p>
              </el-col>
            </el-row>
            <el-row :gutter="24" style="margin: 0">
              <el-col :md="22">
                <p class="info-item">
                  <span class="info-label">空间类型：</span>
                  <span class="info-value">{{ planData.spaceTypeName }}</span>
                </p>
              </el-col>
            </el-row>
            <el-row :gutter="24" style="margin: 0">
              <el-col :md="22">
                <p class="info-item">
                  <span class="info-label">预案说明：</span>
                  <span class="info-value">{{planData.regulationsDesc}}</span>
                </p>
              </el-col>
            </el-row>
            <el-row :gutter="24" style="margin: 0">
              <el-col :md="22">
                <p class="info-item">
                  <span class="info-label">预案号：</span>
                  <span class="info-value">{{ planData.versionNo }}</span>
                </p>
              </el-col>
            </el-row>
            <el-row :gutter="24" style="margin: 0">
              <el-col :md="22">
                <p class="info-item">
                  <span class="info-label">法规流程图：</span>
                  <span class="info-value">
                    <img :src="$tools.imgUrlTranslation(planData.regulationsFlow.length ? planData.regulationsFlow[0].url : '')" @click="viewImage">
                    <el-image-viewer v-if="showViewer" :on-close="() => showViewer = false" :url-list="iconPathList" />
                  </span>
                </p>
              </el-col>
            </el-row>
            <el-row :gutter="24" style="margin: 0">
              <el-col :md="22">
                <p class="info-item">
                  <span class="info-label">法规文档：</span>
                  <span class="info-value">
                    <div class="fileValue" @click="viewMore(1)">
                      {{planData.regulationsDoc.length ? planData.regulationsDoc[0].name : ''}}
                      <i class="el-icon-view"></i>
                    </div>
                  </span>
                </p>
              </el-col>
            </el-row>
            <el-row :gutter="24" style="margin: 0">
              <el-col :md="22">
                <p class="info-item">
                  <span class="info-label">法规文案：</span>
                  <el-tooltip placement="top" content="点击查看法规文案">
                    <span class="info-value regulationsText" v-html="planData.regulationsText" @click="viewMore(2)"></span>
                  </el-tooltip>
                </p>
              </el-col>
            </el-row>
          </el-tab-pane>
          <el-tab-pane v-if="$route.query?.planType == 0" label="预案流程" name="2">
            <flowChart v-if="activeTabs == 2" type="view" :eventData="addEventData" />
          </el-tab-pane>
        </el-tabs>
      </div>
      <!-- <div class="active-tab"></div> -->
    </div>
  </div>
</template>

<script>
import ElImageViewer from 'element-ui/packages/image/src/image-viewer'
import flowChart from './components/flowChart'
import { GetAlarmSystem, GetPlanDetail } from '@/utils/wartimeMode'
export default {
  name: 'planDetails',
  components: {
    ElImageViewer,
    flowChart
  },
  data() {
    return {
      activeTabs: '1',
      planData: {
        regulationsFlow: [],
        regulationsDoc: []
      },
      planTypeList: [],
      showViewer: false,
      iconPathList: [], // 图片列表
      addEventData: {
        1: {}, // 演习
        2: {}, // 演习-启动预案
        3: {}, // 演习-自定义
        4: {}, // 确警
        5: {}, // 确警-启动预案
        6: {} // 确警-自定义
      }
    }
  },
  computed: {

  },
  created() {
    this.getAlarmSystem()
    this.getPlanDetail()
  },
  methods: {
    // 启动预案
    startPlanSendWpf() {
      const param = {
        alarmId: this.$route.query?.alarmId,
        planId: this.planData.id,
        alarmType: this.$route.query?.alarmType
      }
      try {
        window.chrome.webview.hostObjects.sync.bridge.AlarmStartUpPlan(JSON.stringify(param))
        this.$router.go(-1)
      } catch (error) {}
    },
    viewMore (type) {
      if (type == 1) {
        this.$router.push({
          path: '/regulationsDoc',
          query: {
            url: this.$tools.imgUrlTranslation(this.planData.regulationsDoc[0].url)
          }
        })
      } else {
        this.$router.push({
          path: '/regulationsText',
          query: {
            id: this.planData.id
          }
        })
      }
    },
    // 获取预案详情
    getPlanDetail() {
      GetPlanDetail({ id: this.$route.query?.id }).then(res => {
        if (res.data.code == 200) {
          const data = res.data.data
          const { regulationsFlow, regulationsDoc, warnEventList, noticeEventList, confirmEventList } = data
          data.regulationsFlow = JSON.parse(regulationsFlow)
          data.regulationsDoc = JSON.parse(regulationsDoc)
          this.planData = data
          if (this.$route.query?.planType != 0) return
          warnEventList.forEach(item => {
            if (Object.keys(this.addEventData[item.stepType]).includes('warnEventList')) {
              this.addEventData[item.stepType].warnEventList.push(item)
            } else {
              this.addEventData[item.stepType].warnEventList = [item]
            }
          })
          noticeEventList.forEach(item => {
            item.noticeWay = Number(item.noticeWay)
            item.noticeType = Number(item.noticeType)
            if (Object.keys(this.addEventData[item.stepType]).includes('noticeEventList')) {
              this.addEventData[item.stepType].noticeEventList.push(item)
            } else {
              this.addEventData[item.stepType].noticeEventList = [item]
            }
          })
          confirmEventList.forEach(item => {
            if (Object.keys(this.addEventData[item.stepType]).includes('confirmEventList')) {
              this.addEventData[item.stepType].confirmEventList.push(item)
            } else {
              this.addEventData[item.stepType].confirmEventList = [item]
            }
          })
        }
      })
    },
    // 获取预案类型
    getAlarmSystem() {
      GetAlarmSystem({ projectCodes: '' }).then((res) => {
        if (res.data.code == 200) {
          this.planTypeList = res.data.data
        }
      })
    },
    // 查看图片
    viewImage() {
      const img = this.planData.regulationsFlow
      if (!img.length) {
        this.$message.info('当前没有可预览的图片')
        return
      }
      this.iconPathList = [this.$tools.imgUrlTranslation(img[0].url)]
      this.showViewer = true
    }
  }
}

</script>

<style lang="scss" scoped>
.planDetails {
  width: 100%;
  height: 100%;
  position: relative;
  .planDetails-main {
    position: absolute;
    right: 0;
    top: 0;
    bottom: 0;
    width: 60%;
    background: url('@/assets/images/plan/planBgd.png') no-repeat center center / 100% 100%;
    display: flex;
    flex-direction: column;
    overflow: hidden;
  }
  .main-title {
    height: 40px;
    line-height: 40px;
    font-size: 15px;
    font-family: 'PingFang SC-Medium', 'PingFang SC';
    font-weight: 500;
    color: #B0E3FA;
    padding-left: 10px;
    i {
      color: #8BDDF5;
      width: 25px;
      cursor: pointer;
      font-weight: 600;
    }
  }
  .usePlanBtnBgd {
    width: 88px;
    height: 32px;
    font-size: 14px;
    color: #A4AFC1;
    line-height: 32px;
    position: absolute;
    right: 16px;
    top: 8px;
    padding: 0px;
    background: url('@/assets/images/plan/usePlanBtnBgd.png') no-repeat center center / 100% 100%;
  }
  .main-content {
    height: calc(100% - 40px);
    width: 100%;
    padding: 16px;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    ::v-deep(.el-tabs) {
      height: 100%;
      .el-tabs__header {
        margin: 0px;
      }
      .el-tabs__nav-wrap {
        &::after {
          height: 1px;
          background: #5E6D97;
        }
        .el-tabs__item {
          height: auto;
          line-height: inherit;
          padding: 6px 12px;
          color: #B0E3FA;
          background: linear-gradient( 90deg, rgba(10,132,255,0) 0%, rgba(10,132,255,0) 100%), rgba(255,255,255,0.1);
          border: 1px solid rgba(10,132,255,0);
          margin-bottom: 1px;
        }
        .is-active {
          background: linear-gradient( 90deg, rgba(10,132,255,0.4) 0%, rgba(10,132,255,0) 100%), rgba(255,255,255,0.1);
          border-radius: 0px 0px 0px 0px;
          border: 1px solid;
          border-image: radial-gradient(circle, rgba(171, 240, 255, 1), rgba(226, 254, 255, 1), rgba(132, 196, 203, 1), rgba(48, 151, 166, 0)) 10 10;
        }
        .el-tabs__active-bar {
          display: none;
        }
      }
      .el-tabs__content {
        height: calc(100% - 30px);
        .el-tab-pane {
          height: 100%;
          overflow: auto;
        }
      }
    }
    .info-item {
      margin-top: 24px;
      display: flex;
      span {
        display: inline-block;
        font-size: 14px;
        // line-height: 14px;
      }
      .info-label {
        text-align: right;
        min-width: 140px;
        display: inline-block;
        font-weight: 400;
        color: #B0E3FA;
      }
      .info-value {
        display: inline-block;
        font-weight: 500;
        color: #FFFFFF;
        flex: 1;
        word-wrap: break-word;
        word-break: break-all;
        img {
          width: 100px;
          height: 100px;
          cursor: pointer;
        }
        .fileValue{
          cursor: pointer;
          i {
            margin-left: 50px;
            color: #8BDDF5;
          }
        }
      }
      .regulationsText {
        text-overflow: -o-ellipsis-lastline;
        overflow: hidden;
        text-overflow: ellipsis;
        display: -webkit-box;
        -webkit-line-clamp: 7;
        -webkit-box-orient: vertical;
        cursor: pointer;
        line-height: initial;
      }
    }
  }
}
</style>
<style>
.el-image-viewer__wrapper {
  width: 60%;
  left: auto;
  overflow: hidden;
}
</style>
