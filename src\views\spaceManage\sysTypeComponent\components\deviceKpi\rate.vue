<template>
  <div class="rate">
    <div id="rate_chart"></div>
  </div>
</template>

<script>
import * as echarts from 'echarts'
import { mockRateData } from './mockData.js'
export default {
  name: 'rate',
  data() {
    return {
      rateData: {
        legendList: ['利润率', '投资收益率', '维修费用率'],
        nameList: ['2019', '2020', '2021', '2022', '2023'],
        profitRate: [0, 0, 0, 0, 0],
        investmentIncomeRate: [0, 0, 0, 0, 0],
        maintenanceCostRate: [0, 0, 0, 0, 0]
      }
    }
  },
  computed: {},
  created() {},
  mounted() {
    if (__PATH.VUE_APP_HOSPITAL_NODE === 'outernet') {
      this.rateData = mockRateData
    }
    this.$nextTick(() => {
      this.setChart()
    })
  },
  methods: {
    setChart() {
      const getchart = echarts.init(document.getElementById('rate_chart'))
      const option = {
        title: {
          text: '费率分布(%) ',
          textStyle: {
            fontSize: 16,
            fontWeight: 'normal',
            color: '#fff'
          },
          x: 20,
          top: 20
        },
        tooltip: {
          trigger: 'axis'
        },
        legend: {
          data: this.rateData.legendList,
          top: '8%',
          textStyle: {
            color: '#FFFFFF',
            fontSize: 12
          }
        },
        grid: {
          left: '7%',
          top: '20%',
          right: '7%',
          bottom: '10%'
        },
        xAxis: {
          type: 'category',
          boundaryGap: false, // 坐标轴两边留白
          data: this.rateData.nameList,
          axisTick: {
            show: false
          },
          axisLine: {
            show: false,
            lineStyle: {
              color: '#E6F7FF'
            }
          },
          splitLine: {
            show: false
          }
        },
        yAxis: [
          {
            type: 'value',
            splitNumber: 5,
            axisLabel: {
              textStyle: {
                color: '#E6F7FF',
                fontSize: 12
              }
            },
            axisLine: {
              show: false
            },
            axisTick: {
              show: false
            },
            splitLine: {
              show: true,
              lineStyle: {
                type: 'dashed',
                color: 'rgba(230, 247, 255, 0.20)'
              }
            }
          }
        ],
        series: [
          {
            name: '利润率',
            type: 'line',
            symbol: 'circle',
            symbolSize: 5,
            itemStyle: {
              normal: {
                color: '#FFCA64',
                lineStyle: {
                  color: '#FFCA64',
                  width: 1
                }
              }
            },
            data: this.rateData.profitRate
          },
          {
            name: '投资收益率',
            type: 'line',
            symbol: 'circle',
            symbolSize: 5,
            itemStyle: {
              normal: {
                color: '#61E29D',
                lineStyle: {
                  color: '#61E29D',
                  width: 1
                }
              }
            },
            data: this.rateData.investmentIncomeRate
          },
          {
            name: '维修费用率',
            type: 'line',
            symbol: 'circle',
            symbolSize: 5,
            itemStyle: {
              normal: {
                color: '#8BDDF5',
                lineStyle: {
                  color: '#8BDDF5',
                  width: 1
                }
              }
            },
            data: this.rateData.maintenanceCostRate
          }
        ]
      }
      getchart.clear()
      getchart.setOption(option)
      // 随着屏幕大小调节图表
      window.addEventListener('resize', () => {
        getchart.resize()
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.rate {
  width: 100%;
  height: 100%;
  background: rgba(53, 98, 219, 0.06);
  #rate_chart {
    width: 100%;
    height: 100%;
  }
}
</style>
