<template>
  <div class="constructionDetails">
    <div class="module-container" style="height: calc(35%); background: rgba(133,145,206,0.15);margin-bottom: 16px;">
      <div class="info-header clear">
        <p class="info-header-text fl">施工信息</p>
        <p class="info-header-status fl" :style="{color: `rgb(${statusObj[infoData.maintainStatus]?.color ?? ''}, 1)`, background: `rgb(${statusObj[infoData.maintainStatus]?.color ?? ''}, 0.2)`}">{{ statusObj[infoData.maintainStatus]?.name ?? '---' }}</p>
      </div>
      <div class="module-content info-list" style="height: calc(100% - 44px)">
        <div v-for="item in infoList" :key="item.label" class="info-list-item">
          <p class="item-label">{{ item.label }}：</p>
          <p class="item-value">{{ infoData[item.key] || '---' }}</p>
        </div>
      </div>
    </div>
    <div class="module-container" style="height: calc(65%)">
      <div class="module-header">
        <div class="title-left">
          <p class="title-left-text showFloorName">巡检</p>
        </div>
        <p style="font-size: 14px;">巡检进度：{{ infoData.maintainSchedule || '-' }}</p>
        <div class="title-right">
          <el-dropdown trigger="click" @command="(val) => (tagCurrent = val)">
            <span class="el-dropdown-link"> {{ tagList.find((v) => v.value == tagCurrent)?.text ?? '' }} <i class="el-icon-arrow-down"></i> </span>
            <el-dropdown-menu slot="dropdown">
              <el-dropdown-item v-for="item in tagList" :key="item.value" :command="item.value" :class="{ isBjxl: tagCurrent == item.value }">{{ item.text }}</el-dropdown-item>
            </el-dropdown-menu>
          </el-dropdown>
          <!-- <span class="viewMore"></span> -->
        </div>
      </div>
      <div class="module-content" style="height: calc(100% - 44px)">
        <el-table v-loading="tableLoading" class="table-center-transfer" :data="tableData" height="100%"
                  :cell-style="{ padding: ' 8px', backgroundColor: 'transparent', border: 'none', padding: '3px' }"
                  :header-cell-style="{ background: '#8591CE26!important', color: '#8BDDF5FF', padding: '4px 8px', fontWeight: 'bold' }"
                  style="width: 100%" element-loading-background="rgba(0, 0, 0, 0.2)">
          <el-table-column prop="workName" label="巡检名称" show-overflow-tooltip></el-table-column>
          <el-table-column prop="maintainDate" label="巡查时间" show-overflow-tooltip></el-table-column>
          <el-table-column prop="maintainDeptName" label="巡检部门" show-overflow-tooltip></el-table-column>
          <el-table-column prop="maintainUserName" label="巡检人员" show-overflow-tooltip></el-table-column>
        </el-table>
      </div>
    </div>
  </div>
</template>

<script>
import { GetDetailByLocalId } from '@/utils/spaceManage'
export default {
  name: 'constructionDetails',
  props: {
    roomData: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      tableLoading: false,
      tagCurrent: 1, // 日期类型
      tagList: [
        { text: '今日', value: 1 },
        { text: '本周', value: 2 },
        { text: '本月', value: 3 },
        { text: '本年', value: 4 }
      ],
      statusObj: {
        0: {name: '未开始', color: '255, 148, 53'},
        1: {name: '进行中', color: '139, 221, 245'},
        2: {name: '作业结束', color: '97, 226, 157'},
        3: {name: '超时', color: '255, 45, 85'}
      },
      infoData: {},
      infoList: [
        {label: '施工名称', key: 'maintainName'},
        {label: '施工类型', key: 'maintainType'},
        {label: '施工位置', key: 'maintainLocalName'},
        {label: '施工期限', key: 'maintainTime'},
        {label: '施工内容', key: 'maintainContent'},
        {label: '现场负责人', key: 'localManagePerson'},
        {label: '联系电话', key: 'maintainPhone'}
      ],
      tableData: []
    }
  },
  computed: {

  },
  watch: {
    tagCurrent(val) {
      this.getDetailByLocalId()
    }
  },
  created() {
    this.getDetailByLocalId()
  },
  methods: {
    getDetailByLocalId() {
      let params = {
        btnType: this.tagCurrent,
        // startDate: ,
        // endDate: ,
        projectCode: this.roomData.deviceId
      }
      GetDetailByLocalId(params).then(res => {
        if (res.data.code == 200) {
          this.infoData = res.data.data
          this.tableData = res.data.data.maintainRecordVoList
        }
      })
    }
  }
}

</script>

<style lang="scss" scoped>
@import '../style/module.scss';
.constructionDetails {
  padding: 5px 0px 0px 0px;
  .module-header {
    padding-left: 30px;
    padding-right: 10px;
    width: 100%;
    background: url("@/assets/images/qhdsys/bg-bt.png") no-repeat;
    background-size: contain;
  }
  .info-header {
    padding: 6px 8px;
    line-height: 22px;
    background: rgba(133,145,206,0.15);
    .info-header-text {
      font-weight: 500;
      font-size: 15px;
      color: #FFFFFF;
    }
    .info-header-status {
      margin-left: 10px;
      font-weight: 400;
      font-size: 14px;
      line-height: 16px;
      padding: 3px 8px;
      border-radius: 100px;
    }
  }
  .info-list {
    padding: 0px 16px;
    .info-list-item {
      display: flex;
      .item-label {
        font-weight: 400;
        font-size: 14px;
        color: #B0E3FA;
        line-height: 30px;
        width: 85px;
      }
      .item-value {
        font-weight: 400;
        font-size: 14px;
        color: #FFFFFF;
        line-height: 30px;
      }
    }
  }
  .title-right {
    font-size: 14px;
    align-items: center;
  }
  .el-dropdown-link {
    font-size: 14px;
    font-weight: 300;
    color: #ffffff;
    line-height: 16px;
    .el-icon-arrow-down {
      font-size: 12px;
    }
  }
  .viewMore {
    display: inline-block;
    width: 24px;
    height: 24px;
    margin-left: 10px;
    background: url('@/assets/images/qhdsys/bg-gd.png') no-repeat center / 100% 100%;
  }
}
</style>
