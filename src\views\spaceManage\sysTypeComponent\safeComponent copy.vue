<template>
  <div class="safeComponent module-container">
    <el-tabs v-model="tabsActiveName" class="module-tabs" stretch>
      <el-tab-pane label="报警管理" name="1">
        <div class="module-container" style="height: 60%">
          <div class="module-header">
            <div class="title-left">
              <p class="title-left-text">报警管理</p>
            </div>
            <div class="title-right">
              <el-dropdown trigger="click" @command="(val) => (tagCurrent = val)">
                <span class="el-dropdown-link"> {{ tagList.find((v) => v.value == tagCurrent)?.text ?? '' }} <i class="el-icon-arrow-down"></i> </span>
                <el-dropdown-menu slot="dropdown">
                  <el-dropdown-item v-for="item in tagList" :key="item.value" :command="item.value" :class="{ isBjxl: tagCurrent == item.value }">{{ item.text }}</el-dropdown-item>
                </el-dropdown-menu>
              </el-dropdown>
              <span class="viewMore" @click="goTo('/EmergencyDisposal', { projectCode: roomData.projectCode, pageType: 2 })"></span>
            </div>
          </div>
          <div class="module-content" style="height: calc(100% - 44px); padding-bottom: 16px; box-sizing: border-box">
            <!-- <div class="top_type">
              <div class="top_type_item" @click="changeType('')" :class="{ activeType: activeType === '' }">
                <span>全部（{{ tableData.allDispose || 0 }}）</span>
              </div>
              <div class="top_type_item" @click="changeType(1)" :class="{ activeType: activeType === 1 }">
                <span>已处理（{{ tableData.dispose || 0 }}）</span>
              </div>
              <div class="top_type_item" @click="changeType(0)" :class="{ activeType: activeType === 0 }">
                <span>未处理（{{ tableData.notDispose || 0 }}）</span>
              </div>
            </div> -->
            <div class="alarm-statistics">
              <div class="alarm-left">
                <p>未处理</p>
                <p>{{ tableData.notDispose || 0 }}</p>
              </div>
              <div class="alarm-center">
                <p>全部报警</p>
                <p>{{ tableData.allDispose || 0 }}</p>
              </div>
              <div class="alarm-right">
                <p>处理中</p>
                <p>{{ tableData.dispose || 0 }}</p>
              </div>
            </div>
            <el-table
              class="bottom-el-table"
              :data="tableData.policeList"
              height="calc(100% - 174px)"
              style="width: 100%"
              element-loading-background="rgba(0, 0, 0, 0.2)"
              v-loading="tableLoading"
              v-el-table-infinite-scroll="tableLoadEvent"
              @row-dblclick="tableDblclick"
            >
              <el-table-column prop="alarmStartTime" show-overflow-tooltip label="时间" sortable></el-table-column>
              <el-table-column prop="alarmDeviceName" show-overflow-tooltip label="设备/对象" sortable width="110">
                <span slot-scope="scope" style="color: #ffca64" @click="viewDevice(scope.row)">
                  {{ scope.row.alarmDeviceName }}
                </span>
              </el-table-column>
              <el-table-column prop="alarmLevel" show-overflow-tooltip label="等级" sortable width="75">
                <span slot-scope="scope" class="alarmLevel" :style="{ color: alarmLevelItem[scope.row.alarmLevel].color }">
                  {{ alarmLevelItem[scope.row.alarmLevel].text }}
                </span>
              </el-table-column>
              <!-- <el-table-column prop="incidentName" show-overflow-tooltip label="事件类型"></el-table-column> -->
              <el-table-column prop="alarmStatus" show-overflow-tooltip label="状态" sortable width="75">
                <div slot-scope="scope" class="alarmStatus" :style="{ color: scope.row.alarmStatus == 0 ? '#FA403C' : scope.row.alarmStatus == 1 ? '#fd9434' : '#999' }">
                  <span class="alarmStatusIcon" :style="{ background: scope.row.alarmStatus == 0 ? '#FA403C' : scope.row.alarmStatus == 1 ? '#ff9435' : '#999' }"></span>
                  {{ scope.row.alarmStatus == 0 ? '未处理' : scope.row.alarmStatus == 1 ? '处理中' : '已关闭' }}
                </div>
              </el-table-column>
            </el-table>
          </div>
        </div>
        <div class="module-container" style="height: 40%">
          <div class="module-header">
            <div class="title-left">
              <p class="title-left-text">设备报警分析</p>
            </div>
          </div>
          <div class="module-content" style="height: calc(100% - 44px)">
            <div id="deviceAlarmAnalysis" style="width: 100%; height: 100%"></div>
          </div>
        </div>
      </el-tab-pane>
      <el-tab-pane label="安全生产" name="2">
        <div style="height: 100%; overflow: auto">
          <div class="module-container" style="height: 30%">
            <div class="module-header">
              <div class="title-left">
                <p class="title-left-text">风险告知卡</p>
              </div>
            </div>
            <div class="module-content" style="height: calc(100% - 44px)">
              <div style="height: 100%; text-align: center; padding: 16px 0px; box-sizing: border-box">
                <el-image fit="contain" :src="getImage" :preview-src-list="[getImage]"></el-image>
              </div>
            </div>
          </div>

          <div class="module-container" style="height: 70%">
            <div class="module-header">
              <div class="title-left">
                <p class="title-left-text">风险分析</p>
              </div>
            </div>
            <div class="module-content" style="height: calc(100% - 44px)">
              <div class="analysis-list">
                <div class="list-item" v-for="item in analysisList" :key="item.name">
                  <img :src="item.imgSrc" :alt="item.name" />
                  <p class="item-value" :style="{ color: item.color }">{{ item.value }}</p>
                  <p class="item-name">{{ item.name }}</p>
                </div>
              </div>
              <el-table :data="tableDatas" style="width: 100%" height="200px" class="bottom-el-table">
                <el-table-column prop="name" label="风险点名称" min-width="129"></el-table-column>
                <el-table-column prop="grade" label="风险等级" min-width="129">
                  <span
                    slot-scope="scope"
                    style="display: inline-block; width: calc(100% - 5px); height: 100%; padding-left: 5px"
                    :style="{ background: 'rgba(' + scope.row.color + ',0.3)', color: 'rgba(' + scope.row.color + ',1)' }"
                  >
                    {{ scope.row.grade }}
                  </span>
                </el-table-column>
                <el-table-column prop="dept" label="责任部门" min-width="130"> </el-table-column>
              </el-table>
            </div>
          </div>
        </div>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script>
import { GetAlarmHandling, GetPoliceCountByDeviceId } from '@/utils/spaceManage'
import { refrigeratorParams } from '@/assets/common/dict.js'
import * as echarts from 'echarts'
import moment from 'moment'
moment.locale('zh-cn', {week: {dow: 1}})
export default {
  name: 'safeComponent',
  props: {
    roomData: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      oxygenRiskImage: require('../../../assets/images/center/oxygenRiskImage.png'),
      airConditioningRiskImage: require('../../../assets/images/center/airConditioningRiskImage.jpg'),
      tabsActiveName: '1',
      tagCurrent: 'year',
      activeType: '',
      tagList: [
        { text: '今日', value: 'day', timeType: 0, startTime: moment().format('YYYY-MM-DD'), endTime: moment().format('YYYY-MM-DD') },
        { text: '本周', value: 'week', timeType: 1, startTime: moment().startOf('week').format('YYYY-MM-DD'), endTime: moment().endOf('week').format('YYYY-MM-DD') },
        { text: '本月', value: 'month', timeType: 2, startTime: moment().startOf('month').format('YYYY-MM-DD'), endTime: moment().endOf('month').format('YYYY-MM-DD') },
        { text: '本年', value: 'year', timeType: 3, startTime: moment().startOf('year').format('YYYY-MM-DD'), endTime: moment().endOf('year').format('YYYY-MM-DD') }
      ],
      tableLoading: false,
      tableData: {
        policeList: []
      },
      refrigeratorParams: JSON.parse(JSON.stringify(refrigeratorParams)),
      alarmLevelItem: {
        0: { text: '通知', color: '#3562DB' },
        1: { text: '一般', color: '#3562DB' },
        2: { text: '紧急', color: '#FF9435' },
        3: { text: '重要', color: '#FA403C' }
      },
      pagination: {
        total: 0,
        pageSize: 20,
        pageNo: 1
      },
      analysisList: [
        { color: '#FA403C', name: '重大风险', value: 0, imgSrc: require('@/assets/images/qhdsys/risk-major.png') },
        { color: '#FF9435', name: '较大风险', value: 2, imgSrc: require('@/assets/images/qhdsys/risk-major(1).png') },
        { color: '#FFBE00', name: '一般风险', value: 1, imgSrc: require('@/assets/images/qhdsys/risk-commonly.png') },
        { color: '#3562DB', name: '低风险', value: 2, imgSrc: require('@/assets/images/qhdsys/risk-low.png') }
      ],
      tableDatas: [
        { name: '液氧储罐1', grade: '较大风险', dept: '总务处', color: '255, 143, 80' },
        { name: '液氧储罐2', grade: '较大风险', dept: '总务处', color: '255, 143, 80' },
        { name: '氧气汇流排', grade: '一般风险', dept: '总务处', color: '255, 202, 100' },
        { name: '氧气值班室', grade: '低风险', dept: '总务处', color: '60, 193, 255' },
        { name: '气瓶存放处', grade: '低风险', dept: '总务处', color: '60, 193, 255' }
      ]
    }
  },
  computed: {
    getImage() {
      if (this.roomData.projectCode === '73e7aab447b34971b9ae6d8dae034aa3') {
        // 空调
        return this.oxygenRiskImage
      } else {
        return this.airConditioningRiskImage
      }
    }
  },
  watch: {
    roomData: {
      handler(val, oldVal) {
        this.pagination.pageNo = 1
        this.getAlarmHandling()
        this.getPoliceCountByDeviceId()
      },
      deep: true
    },
    tabsActiveName(val) {
      this.tagCurrent = 'day'
      if (val === '1') {
        this.pagination.pageNo = 1
        this.getAlarmHandling()
        this.getPoliceCountByDeviceId()
      } else {
      }
    },
    tagCurrent(val) {
      if (this.tabsActiveName === '1') {
        this.pagination.pageNo = 1
        this.getAlarmHandling()
        this.getPoliceCountByDeviceId()
      } else {
      }
    }
  },
  mounted() {
    this.getAlarmHandling()
    this.getPoliceCountByDeviceId()

    const params = JSON.parse(JSON.stringify(this.refrigeratorParams))
    params.forEach((i) => {
      i.Visibility = 1
    })
    try {
      window.chrome.webview.hostObjects.sync.bridge.GetPipelineSelectData(JSON.stringify(params))
    } catch (error) {}
  },
  methods: {
    // 显示设备位置
    viewDevice(item) {
      try {
        window.chrome.webview.hostObjects.sync.bridge.viewDevice(
          JSON.stringify({
            deviceId: item.alarmDeviceId ? item.alarmDeviceId : item.deviceId,
            value: item.alarmCount ? item.alarmCount : ''
          })
        )
      } catch (error) {}
    },
    initData() {
      this.pagination.pageNo = 1
      this.getAlarmHandling()
    },
    changeType(type) {
      this.pagination.pageNo = 1
      this.activeType = type
      this.getAlarmHandling()
    },
    deviceAlarmAnalysisChart(data) {
      const myChart = echarts.init(document.getElementById('deviceAlarmAnalysis'))
      let option
      if (data.length) {
        option = {
          // backgroundColor: '#00265f',
          tooltip: {
            trigger: 'axis',
            axisPointer: {
              type: 'shadow'
            }
          },
          grid: {
            top: '10%',
            right: '3%',
            left: '10%',
            bottom: '10%'
          },
          xAxis: [
            {
              type: 'category',
              data: data.map(v => v.deviceName),
              axisLine: {
                lineStyle: {
                  color: '#E6F7FF'
                }
              },
              axisLabel: {
                margin: 8,
                color: 'rgba(230,247,255,0.5)',
                textStyle: {
                  fontSize: 14
                }
              },
              axisTick: {
                show: false
              }
            }
          ],
          yAxis: [
            {
              axisLabel: {
                formatter: '{value}',
                color: 'rgba(230,247,255,0.5)'
              },
              axisTick: {
                show: false
              },
              axisLine: {
                show: false
              },
              splitLine: {
                lineStyle: {
                  type: 'dashed',
                  color: 'rgba(255,255,255,0.12)'
                }
              }
            }
          ],
          series: [
            {
              type: 'bar',
              data: data.map(v => v.alarmCount),
              barWidth: '10px',
              itemStyle: {
                normal: {
                  color: '#0A84FF',
                },
                emphasis: {
                  color: '#FFCA64',
                }
              }
            }
          ]
        }
      } else {
        option = {
          title: {
            text: '暂无数据',
            x: 'center',
            y: 'center',
            textStyle: {
              fontSize: 14,
              fontWeight: 'normal',
              color: '#999'
            }
          }
        }
      }
      myChart.off('click')
      myChart.clear()
      myChart.setOption(option)
      // 随着屏幕大小调节图表
      window.addEventListener('resize', () => {
        myChart.resize()
      })
      myChart.on('click', (e) => {
        this.viewDevice(data[e.dataIndex])
      })
    },
    // 设备报警分析
    getPoliceCountByDeviceId() {
      const params = {
        projectCode: this.roomData.projectCode,
        startTime: this.tagList.find((item) => item.value === this.tagCurrent).startTime,
        endTime: this.tagList.find((item) => item.value === this.tagCurrent).endTime
      }
      GetPoliceCountByDeviceId(params).then((res) => {
        if (res.data.code === '200') {
          const data = res.data.data
          // const data = [
          //   { alarmCount: 100, deviceId: '13456789', deviceName: '设备1' },
          //   { alarmCount: 100, deviceId: '12456789', deviceName: '设备2' },
          //   { alarmCount: 100, deviceId: '12356789', deviceName: '设备3' },
          //   { alarmCount: 100, deviceId: '12346789', deviceName: '设备4' },
          //   { alarmCount: 100, deviceId: '12345789', deviceName: '设备5' },
          //   { alarmCount: 100, deviceId: '12345689', deviceName: '设备6' },
          //   { alarmCount: 100, deviceId: '12345679', deviceName: '设备7' }
          // ]
          this.deviceAlarmAnalysisChart(data)
        }
      })
    },
    // 获取报警列表
    getAlarmHandling() {
      this.tableLoading = true
      const params = {
        projectCode: this.roomData.projectCode,
        status: this.activeType,
        startTime: this.tagList.find((item) => item.value === this.tagCurrent).startTime,
        endTime: this.tagList.find((item) => item.value === this.tagCurrent).endTime,
        pageSize: this.pagination.pageSize,
        page: this.pagination.pageNo
      }
      GetAlarmHandling(params).then((res) => {
        if (res.data.code === '200') {
          const data = res.data.data
          if (this.pagination.pageNo === 1) {
            this.tableData.policeList = []
          }
          this.tableData.policeList = this.tableData.policeList.concat(data.policeList?.list || [])
          this.pagination.total = data.policeList?.count || 0
          this.tableData.allDispose = data?.airRunPolice?.total ?? 0
          this.tableData.notDispose = data?.airRunPolice?.noDealCount ?? 0
          this.tableData.dispose = data?.airRunPolice?.isDealCount ?? 0
        } else {
          this.tableData.policeList = []
          this.tableData.allDispose = 0
          this.tableData.notDispose = 0
          this.tableData.dispose = 0
        }
        this.tableLoading = false
      })
    },
    tableLoadEvent() {
      if (this.pagination.total > this.pagination.pageNo * this.pagination.pageSize) {
        this.pagination.pageNo++
        this.getAlarmHandling()
      }
    },
    tableDblclick(row) {
      this.goTo('/EmergencyDisposal', { projectCode: this.roomData.projectCode, pageType: 2 })
    },
    goTo(path, query) {
      this.$router.push({
        path,
        query
      })
    }
  }
}
</script>

<style lang="scss" scoped>
@import '../style/module.scss';
.safeComponent {
  width: 100%;
  height: 100%;
  ::v-deep .el-image {
    height: 100%;
    .el-image__error {
      background: transparent !important;
    }
  }
  .top_type {
    width: 100%;
    margin: 0 auto;
    padding: 0px 0px 15px 0;
    /* background-color: rgba(1, 11, 59, 0.5); */
    display: flex;
    justify-content: space-evenly;
    flex-wrap: wrap;
    .top_type_item {
      cursor: pointer;
      margin-left: 8px;
      margin-top: 8px;
      padding: 6px 8px;
      background: rgba(255, 255, 255, 0.1) linear-gradient(90deg, rgba(10, 132, 255, 0) 0%, rgba(10, 132, 255, 0) 100%);
      font-size: 14px;
      font-weight: 400;
      color: #b0e3fa;
      line-height: 14px;
      border: 1px solid transparent;
      // min-width: 25%;
      // margin: 3px 2%;
      // padding: 0 5px;
      // height: 2.5rem;
      // line-height: 2.5rem;
      // text-align: center;
      // font-size: 14px;
      // font-family: PingFang-SC-Medium, PingFang-SC;
      // color: #93bdff;
      // background: url('~@/assets/images/center/block-bg.png') no-repeat;
      // background-size: 100% 100%;
      // cursor: pointer;
    }
    .activeType {
      background: rgba(255, 255, 255, 0.1) linear-gradient(90deg, rgba(10, 132, 255, 0.4) 0%, rgba(10, 132, 255, 0) 100%);
      border: 1px solid;
      border-image: radial-gradient(circle, rgba(171, 240, 255, 1), rgba(226, 254, 255, 1), rgba(132, 196, 203, 1), rgba(48, 151, 166, 0)) 1 1;
      // background: url('~@/assets/images/center/block-bg-active.png') no-repeat;
      // background-size: 100% 100%;
      // color: #ffe3a6;
    }
  }
  .alarm-statistics {
    width: 100%;
    height: 173px;
    background: url('~@/assets/images/qhdsys/alarm_bg.png') no-repeat center / 100% 100%;
    position: relative;
    margin-bottom: 16px;
    .alarm-left {
      cursor: pointer;
      width: 120px;
      height: 94px;
      background: url('~@/assets/images/qhdsys/alarm_weichuli.png') no-repeat center / 100% 100%;
      position: absolute;
      top: 61px;
      left: -8px;
      text-align: center;
      padding: 27px 12px 0px 0px;
      box-sizing: border-box;
      p:first-child {
        font-size: 10px;
        font-weight: bold;
        color: #ffffff;
        line-height: 12px;
        text-shadow: 0px 2px 3px rgba(0, 28, 29, 0.26);
      }
      p:last-child {
        font-size: 18px;
        font-weight: 500;
        color: #ff5454;
        line-height: 21px;
        margin-top: 15px;
      }
    }
    .alarm-center {
      cursor: pointer;
      width: 146px;
      height: 168px;
      background: url('~@/assets/images/qhdsys/alarm_quanbu.png') no-repeat center / 100% 100%;
      position: absolute;
      bottom: 0;
      left: 50%;
      transform: translateX(-50%);
      text-align: center;
      padding-top: 35px;
      box-sizing: border-box;
      p:first-child {
        font-size: 16px;
        font-weight: 500;
        color: #b0e3fa;
        line-height: 19px;
      }
      p:last-child {
        margin-top: 11px;
        font-size: 38px;
        font-weight: 500;
        color: #ffffff;
        line-height: 37px;
      }
    }
    .alarm-right {
      cursor: pointer;
      width: 120px;
      height: 94px;
      background: url('~@/assets/images/qhdsys/alarm_chulizhong.png') no-repeat center / 100% 100%;
      position: absolute;
      top: 61px;
      right: -8px;
      text-align: center;
      padding: 27px 0 0px 8px;
      box-sizing: border-box;
      p:first-child {
        font-size: 10px;
        font-weight: bold;
        color: #ffffff;
        line-height: 12px;
        text-shadow: 0px 2px 3px rgba(0, 28, 29, 0.26);
      }
      p:last-child {
        font-size: 18px;
        font-weight: 500;
        color: #ffca64;
        line-height: 21px;
        margin-top: 15px;
      }
    }
  }
}
// 滚动条的宽度
::v-deep .el-table__body-wrapper::-webkit-scrollbar {
  width: 6px; // 横向滚动条
  height: 6px; // 纵向滚动条 必写
}

// 注意:hover
::v-deep .el-table__body-wrapper:hover::-webkit-scrollbar {
  width: 6px; // 横向滚动条
  height: 6px; // 纵向滚动条 必写
}
::v-deep .el-table__body-wrapper::-webkit-scrollbar-corner,
::v-deep .el-table__body-wrapper::-webkit-scrollbar-track {
  background-color: transparent;
}
// 滚动条的滑块
// ::v-deep .el-table__body-wrapper::-webkit-scrollbar-thumb {
//   background-color: transparent;
// }
// ::v-deep .el-table__body-wrapper:hover::-webkit-scrollbar {
//   background-color: transparent;
// }
.module-header {
  width: 100%;
  // padding-left: 50px;
  background: url('~@/assets/images/qhdsys/bg-bt.png') no-repeat center / 100% 100%;
  .title-right {
    align-items: center;
  }
  .el-dropdown-link {
    font-size: 14px;
    font-weight: 300;
    color: #ffffff;
    line-height: 16px;
    .el-icon-arrow-down {
      font-size: 12px;
    }
  }
  .viewMore {
    width: 23px;
    height: 23.5px;
    margin-left: 10px;
    background: url('@/assets/images/qhdsys/bg-gd.png') no-repeat center / 100% 100%;
  }
}
.title-left {
  padding-left: 20px;
}
.middle-right {
  display: flex;
  margin-right: 7px;
}
.is-activeTab {
  background: url('@/assets/images/qhdsys/bg-kj.png') no-repeat !important;
}
.middle-right div:hover {
  cursor: pointer;
}
.middle-right div {
  width: 55px;
  height: 26px;
  line-height: 26px;
  text-align: center;
  background: rgba(255, 255, 255, 0.1) linear-gradient(90deg, rgba(10, 132, 255, 0) 0%, rgba(10, 132, 255, 0) 100%);
  border-radius: 0px 0px 0px 0px;
  opacity: 1;
  font-size: 14px;
  font-family: HarmonyOS Sans SC-Regular, HarmonyOS Sans SC;
  font-weight: 400;
  color: #8bddf5;
}

.middle-right div:nth-child(2) {
  margin: 0 3px;
}
.middle-right div:nth-child(3) {
  margin-right: 3px;
}
::v-deep .bottom-el-table {
  border: 0 !important;

  .el-table__header-wrapper {
    .el-table__header {
      .has-gutter {
        tr {
          background: transparent;
        }

        tr th {
          padding: 0;
          height: 32px;
          background: rgba(133, 145, 206, 0.15) !important;
          border-bottom: 1px solid rgba(255, 255, 255, 0.5);

          .cell {
            font-size: 14px;
            font-family: HarmonyOS Sans SC-Bold, HarmonyOS Sans SC;
            font-weight: bold;
            color: #8bddf5;
          }
        }
      }
    }
  }
}
::v-deep .el-table__body-wrapper {
  background: transparent;
  // height: 222px !important;
  overflow: hidden;
  overflow-y: auto;
  td.el-table__cell {
    border: 0 !important;
  }
  .el-table__body {
    background: transparent;

    tbody {
      background: transparent;

      .el-table__row {
        // background-color: rgba(255, 255, 255, 0.2);
        background: transparent;
        border: 0;

        td {
          border: 0;
          padding: 0;
          height: 30px;

          div {
            line-height: 30px;
            font-size: 14px;
            font-family: HarmonyOS Sans SC-Regular, HarmonyOS Sans SC;
            font-weight: 400;
            color: #ffffff;
          }
        }
      }

      .el-table__row:nth-child(2n - 1) {
        background: rgba(168, 172, 171, 0.08);
      }

      .el-table__row:hover {
        border: 0;
        opacity: 1;
        cursor: pointer;

        td div {
          color: rgba(255, 202, 100, 1);
        }
      }
    }
  }
}
::v-deep .module-tabs {
  .el-tabs__header {
    .el-tabs__nav-wrap::after {
      display: none;
    }
    .el-tabs__item {
      color: rgba(255, 255, 255, 0.6) !important;
    }
    .is-active {
      color: #8bddf5 !important;
    }
    .el-tabs__active-bar {
      height: 1px !important;
      background: #6ee1f9;
    }
  }
}
.analysis-list {
  display: flex;
  flex-wrap: wrap;
  padding-top: 8px;
  .list-item {
    width: 50%;
    text-align: center;
    padding: 6px 0px;
    margin-top: 8px;
    img {
      width: 74px;
      height: 58.5px;
    }
    .item-value {
      font-size: 18px;
      font-weight: 500;
      line-height: 24px;
      margin: 4px 0px;
    }
    .item-name {
      font-size: 12px;
      font-weight: 400;
      color: #ffffff;
      line-height: 14px;
    }
  }
}
</style>
