<template>
  <div class="component FileListCard">
    <div v-for="(file, index) of localFileList" :key="`file-${index}`" class="FileListCard__item">
      <div v-if="file.isImage" class="FileListCard__item__image">
        <img class="FileListCard__item__image__img" :src="file.url">
        <div class="FileListCard__item__image__filename">
          {{ file.name }}
        </div>
      </div>
      <div v-else class="FileListCard__item__file">
        <img class="FileListCard__item__file__img" src="@/assets/images/common/icon-file.png">
        <div class="FileListCard__item__file__filename">
          {{ file.name }}
        </div>
      </div>
      <div class="FileListCard__item__action">
        <i v-if="file.isImage" class="el-icon-view" @click="handlePreview(file)"></i>
        <i v-else class="el-icon-download" @click="handleDownLoad(file)"></i>
      </div>
    </div>
    <el-image v-if="!!previewUrl" class="FileListCard__preview-image" :src="previewUrl" :preview-src-list="imageUrls"
              @load="showImageViewer"></el-image>
  </div>
</template>

<script>
export default {
  name: 'FileListCard',
  props: {
    fileList: {
      type: Array,
      default: () => ([])
    },
    // 是否开启图片前缀兼容
    isUrlTranslation: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      previewUrl: ''
    }
  },
  computed: {
    // 预览图片的url列表
    imageUrls: function () {
      return this.localFileList
        .filter(x => x.isImage)
        .map(x => x.url)
    },
    localFileList: function () {
      return this.fileList.map(file => {
        const extension = file.url.substring(file.url.lastIndexOf('.'))
        // 如果优先判断file自带isImage
        return {
          name: file.name || file.url,
          url: this.isUrlTranslation ? this.$tools.imgUrlTranslation(file.url) : file.url,
          isImage: file.isImage || '.png,.jpg'.includes(extension)
        }
      })
    }
  },
  methods: {
    // 预览文件
    handlePreview(file) {
      this.previewUrl = file.url
      this.showImageViewer()
    },
    handleDownLoad(file) {
      fetch(file.url)
        .then((res) =>
          res.blob().then((blob) => {
            const a = document.createElement('a')
            const url = window.URL.createObjectURL(blob)
            a.href = url
            a.download = file.name
            a.click()
            window.URL.revokeObjectURL(url)
          })
        )
        .catch((res) => {
          this.$message({
            message: '下载失败！',
            type: 'warning'
          })
        })
    },
    showImageViewer() {
      const imgEl = document.querySelector('.FileListCard__preview-image>img')
      if (imgEl) {
        imgEl.click()
      }
    }
  }
}

</script>

<style lang='scss' scoped>
.FileListCard {
  overflow: auto;
  display: flex;
  flex-flow: row wrap;

  &__item {
    height: 120px;
    width: 120px;
    padding: 0;
    overflow: hidden;
    margin: 0 10px 10px 0;
    background: rgba(133, 145, 206, 0.15);
    border-radius: 4px 4px 4px 4px;
    border: 1px solid rgba(133, 145, 206, 0.5);
    position: relative;

    &__image {
      height: 100%;

      &__filename {
        position: absolute;
        left: 0;
        bottom: 0;
        right: 0;
        background: rgba(0, 0, 0, 0.5);
        border-radius: 0px 0px 2px 2px;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
        padding: 4px 8px;
        color: #fff;
        font-size: 12px;
      }

      &__img {
        height: 100%;
        width: auto;
      }
    }

    &__file {
      text-align: center;
      height: 100%;

      &__img {
        margin-top: 26px;
      }

      &__filename {
        line-height: 1;
        margin: 8px;
        color: #fff;
        height: calc(100% - 68px);
        overflow: hidden;
        white-space: initial;
      }
    }

    &:hover {
      .FileListCard__item__action {
        display: flex;
      }
    }

    &__action {
      display: none;
      position: absolute;
      top: 0;
      right: 0;
      bottom: 0;
      left: 0;
      align-items: center;
      justify-content: space-around;
      padding: 24px;
      background: rgba(0, 0, 0, 0.3);

      > i {
        cursor: pointer;
        font-size: 20px;

        &.el-icon-view {
          color: #ffca64;
        }

        &.el-icon-delete {
          color: #8bddf5;
        }
      }
    }

  }

  &__preview-image {
    height: 1px;
    width: 0;
  }

}
</style>
