<template>
  <div class="footer-btn">
    <div v-if="data.type === '1' || data.type === ''">
      <!-- 未处理 -->
      <div v-if="data.olgTaskManagement.flowcode === '1'">
        <el-button class="sino-button-sure" @click="taskDetailPlaceOrder()">处理</el-button>
        <el-button class="sino-button-sure" @click="dealBtnEvent('taskDetailCancelFlag')">取消</el-button>
      </div>
      <!-- 已受理 -->
      <div v-if="data.olgTaskManagement.flowcode === '2'">
        <el-button class="sino-button-sure" v-if="data.olgTaskManagement.workTypeCode != '16'" @click="dealBtnEvent('taskDetailReturnVisitFlag', '督办')">督办</el-button>
        <el-button class="sino-button-sure" @click="dealBtnEvent('taskDetailTransferFlag')">转派</el-button>
        <el-button class="sino-button-sure" @click="dealBtnEvent('taskDetailToPersonFlag')">指派人员</el-button>
        <el-button class="sino-button-sure" v-if="data.olgTaskManagement.workTypeCode != '16'" @click="dealBtnEvent('taskDetailEntryOrderFlag')">挂单</el-button>
        <el-button class="sino-button-sure" v-if="data.olgTaskManagement.workTypeCode != '16'" @click="dealBtnEvent('taskDetailCancelFlag')">取消</el-button>
        <el-button class="sino-button-sure" v-if="data.olgTaskManagement.workTypeCode != '16'" @click="dealBtnEvent('taskDetailUpdateOrderFlag')">修改</el-button>
      </div>
      <!-- 已派工 -->
      <div v-if="data.olgTaskManagement.flowcode === '3'">
        <el-button class="sino-button-sure" v-if="data.olgTaskManagement.workTypeCode != '16'" @click="dealBtnEvent('taskDetailReturnVisitFlag', '督办')">督办</el-button>
        <el-button class="sino-button-sure" @click="dealBtnEvent('taskDetailTransferFlag')">转派</el-button>
        <el-button class="sino-button-sure" @click="dealBtnEvent('taskDetailToPersonFlag')">指派人员</el-button>
        <el-button class="sino-button-sure" v-if="data.olgTaskManagement.workTypeCode != '16'" @click="dealBtnEvent('taskDetailEntryOrderFlag')">挂单</el-button>
        <el-button class="sino-button-sure" @click="dealBtnEvent('taskDetailFinishOrderFlag')">完工</el-button>
        <el-button class="sino-button-sure" @click="taskDetailDoPrint()">打印</el-button>
        <el-button class="sino-button-sure" v-if="data.olgTaskManagement.workTypeCode != '16'" @click="dealBtnEvent('taskDetailCancelFlag')">取消</el-button>
        <el-button class="sino-button-sure" v-if="data.olgTaskManagement.workTypeCode != '16'" @click="dealBtnEvent('taskDetailUpdateOrderFlag')">修改</el-button>
      </div>
      <!-- 已挂单 -->
      <div v-if="data.olgTaskManagement.flowcode === '4'">
        <el-button class="sino-button-sure" v-if="data.olgTaskManagement.workTypeCode != '16'" @click="dealBtnEvent('taskDetailReturnVisitFlag', '督办')">督办</el-button>
        <el-button class="sino-button-sure" @click="dealBtnEvent('taskDetailTransferFlag')">转派</el-button>
        <el-button class="sino-button-sure" @click="dealBtnEvent('taskDetailToPersonFlag')">指派人员</el-button>
        <el-button class="sino-button-sure" @click="dealBtnEvent('taskDetailFinishOrderFlag')">完工</el-button>
        <el-button class="sino-button-sure" @click="taskDetailDoPrint()">打印</el-button>
        <el-button class="sino-button-sure" v-if="data.olgTaskManagement.workTypeCode != '16'" @click="dealBtnEvent('taskDetailCancelFlag')">取消</el-button>
        <el-button class="sino-button-sure" v-if="data.olgTaskManagement.workTypeCode != '16'" @click="dealBtnEvent('taskDetailUpdateOrderFlag')">修改</el-button>
      </div>
      <!-- 已完工 -->
      <div v-if="data.olgTaskManagement.flowcode === '5'">
        <el-button class="sino-button-sure" v-if="data.olgTaskManagement.workTypeCode != '16'" @click="dealBtnEvent('taskDetailReturnVisitFlag', '回访')">回访</el-button>
        <el-button class="sino-button-sure" v-if="data.olgTaskManagement.workTypeCode != '16'" @click="dealBtnEvent('taskDetailDegreeFlag')">评价</el-button>
        <el-button class="sino-button-sure" @click="taskDetailDoPrint()">打印</el-button>
        <el-button class="sino-button-sure" v-if="data.olgTaskManagement.workTypeCode != '16'" @click="dealBtnEvent('taskDetailUpdateOrderFlag')">修改</el-button>
      </div>
    </div>
    <div v-if="data.type === '2'">
      <el-button class="sino-button-sure" v-if="data.olgTaskManagement.workTypeCode != '16'" @click="dealBtnEvent('taskDetailReturnVisitFlag', '回复')">回复</el-button>
    </div>
    <DealFormDialog
      v-if="dealFormShow"
      :visible="dealFormShow"
      :showBtnType="showBtnType"
      :dialogDetail="data"
      :returnVisitName="returnVisitName"
      :projectCode="alarmDetail.projectCode"
      @submit="btnTypeSubmit"
      @closeDialog="btnTypeClose"
    />
    <!-- 处理工单 -->
    <template v-if="workOrderDealShow">
      <CreatedWorkOrder
        :workOrderDealShow.sync="workOrderDealShow"
        :workTypeCode="data.olgTaskManagement.workTypeCode"
        :workTypeName="data.olgTaskManagement.workTypeName"
        :workTypeId="data.olgTaskManagement.id"
        :projectCode="alarmDetail.projectCode"
        :spaceId="alarmDetail.alarmSpaceId"
        :alarmId="alarmDetail.alarmId"
        dealType="deal"
        @workOrderSure="workOrderSure"
      ></CreatedWorkOrder>
    </template>
    <iframe id="printTemplate" ref="iframeDom" hidden name="printTemplate" src="./printTemplate.html" frameborder="0"></iframe>
    <!-- 打印工单区域 start -->
    <div v-show="false">
      打印内容
      <div id="pagec1">
        <table id="barcode_area" cellpadding="0" cellspacing="0" style="page-break-after: always; padding-bottom: 50px;">
          <tr>
            <td colspan="2">
              <div id="" align="right" style="margin: 5px 5px;">正本</div>
            </td>
          </tr>
          <tr>
            <td colspan="2" align="center">
              <div id="barcode" style="width: 255px; height: 100px;"></div>
            </td>
          </tr>
          <tr>
            <td colspan="2" align="center">{{ data.olgTaskManagement.workNum }}</td>
          </tr>
          <tr>
            <td colspan="2" align="center" style="font-size: 15px; padding: 3px 0;">{{ data.olgTaskManagement.workTypeName }}&nbsp;&nbsp;&nbsp;&nbsp;{{ data.kd_time }}</td>
          </tr>
          <tr>
            <td colspan="2" align="center" style="font-size: 14px; height: 24px;">======= 工单信息 =======</td>
          </tr>
          <tr>
            <td align="right" style="width: 30%; font-size: 15px; height: 22px;">联系人：</td>
            <td align="left" class="callName" style="width: 70%; font-size: 15px; height: 22px;">{{ data.callerName }}</td>
          </tr>
          <tr>
            <td align="right" style="width: 30%; font-size: 15px; height: 22px;">工号：</td>
            <td align="left" style="width: 70%; font-size: 15px; height: 22px;">{{ data.olgTaskManagement.callerJobNum }}</td>
          </tr>
          <tr>
            <td align="right" style="width: 30%; font-size: 15px; height: 22px;">电话：</td>
            <td align="left" style="width: 70%; font-size: 15px; height: 22px;">{{ data.olgTaskManagement.sourcesPhone }}</td>
          </tr>
          <tr>
            <td align="right" style="width: 30%; font-size: 12px; height: 22px;">紧急程度：</td>
            <td align="left" style="width: 70%; font-size: 12px; height: 22px;">{{ data.urgencyDegree }}</td>
          </tr>
          <tr>
            <td align="right" style="width: 30%; font-size: 15px; height: 22px;">服务地点：</td>
            <td align="left" style="width: 70%; font-size: 15px; height: 22px;">
              <span v-for="(wx, wxi) in data.wxDetail" :key="wxi">
                {{ wx[1] }}
              </span>
            </td>
          </tr>
          <tr>
            <td align="right" style="width: 30%; font-size: 15px; height: 22px;">所属科室：</td>
            <td align="left" style="width: 70%; font-size: 15px; height: 22px;">{{ data.olgTaskManagement.sourcesDeptName }}</td>
          </tr>
          <tr v-if="data.olgTaskManagement.workTypeCode != '5'">
            <td align="right" style="width: 30%; font-size: 15px; height: 22px;">服务事项：</td>
            <td align="left" style="width: 70%; font-size: 15px; height: 22px;">
              <span v-for="(wx, wxi) in data.wxDetail" :key="wxi">
                <span v-if="wx[2] !== undefined && wx[2] !== '' && wx[3] !== undefined && wx[3] !== '' && wx[5] != undefined && wx[5] !== ''">{{
                  wx[2] + '-' + wx[3] + '-' + wx[5]
                }}</span>
              </span>
            </td>
          </tr>
          <!-- <%-- 去掉了指派备注说明 --%> -->
          <tr valign="top">
            <td align="right" style="width: 30%; font-size: 15px; height: 22px;">申报描述：</td>
            <td id="remarkZb" align="left" style="width: 70%; font-size: 15px; height: 22px; word-wrap: break-word; word-break: break-all;">
              {{ data.olgTaskManagement.questionDescription }}
            </td>
          </tr>
          <tr>
            <td align="right" style="width: 30%; font-size: 12px; height: 22px;">服务部门：</td>
            <td align="left" style="width: 70%; font-size: 12px; height: 22px;">{{ data.olgTaskManagement.designateDeptName }}</td>
          </tr>
          <tr>
            <td align="right" style="width: 30%; font-size: 12px; height: 22px;">服务人员：</td>
            <td id="designatePersonPrint" align="left" style="width: 70%; font-size: 12px; height: 22px;">{{ data.olgTaskManagement.designatePersonName }}</td>
          </tr>
          <tr>
            <td align="right" style="width: 30%; font-size: 12px; height: 22px;">服务时间：</td>
            <td align="left" style="width: 70%; font-size: 12px; height: 22px;">
              <span>
                {{ data.olgTaskManagement.appointmentDate ? dateToString(data.olgTaskManagement.appointmentDate) : '立刻' }}
              </span>
            </td>
          </tr>
          <!--  增加字段实际耗材及空白区域  -->
          <tr>
            <td valign="top" align="right" style="width: 30%; font-size: 12px; height: 22px;">实际耗材：</td>
            <td>
              <canvas id="actualConsumable" height="100px" width="100px"></canvas>
            </td>
          </tr>
          <tr>
            <td colspan="2" align="center" style="font-size: 12px; height: 22px; padding: 8px 0;">
              <b style="display: inline-block; border: 1px solid #676a6c; width: 10px; height: 10px;"></b>&nbsp;&nbsp;非常满意
              <b style="display: inline-block; border: 1px solid #676a6c; width: 10px; height: 10px;"></b>&nbsp;&nbsp;满意
              <b style="display: inline-block; border: 1px solid #676a6c; width: 10px; height: 10px;"></b>&nbsp;&nbsp;一般
              <b style="display: inline-block; border: 1px solid #676a6c; width: 10px; height: 10px;"></b>&nbsp;&nbsp;差
              <b style="display: inline-block; border: 1px solid #676a6c; width: 10px; height: 10px;"></b>&nbsp;&nbsp;非常差
            </td>
          </tr>
          <tr>
            <td colspan="3" align="center" style="font-size: 12px; height: 22px;">
              <p align="left" style="margin-left: 20px;">注:请联系人/接收人在空白处签字并确认实际耗材与数量</p>
            </td>
          </tr>
          <br />
          <br />
          <br />
        </table>
      </div>
      <div id="pagec2">
        <table id="barcode_area2" cellpadding="0" cellspacing="0" style="page-break-after: always; padding-bottom: 50px;">
          <tr>
            <td colspan="2">
              <div id="" align="right" style="margin: 5px 5px;">副本</div>
            </td>
          </tr>
          <tr>
            <td colspan="2" align="center">
              <div id="barcode2" style="width: 255px; height: 100px;"></div>
            </td>
          </tr>
          <tr>
            <td colspan="2" align="center">{{ data.olgTaskManagement.workNum }}</td>
          </tr>
          <tr>
            <td colspan="2" align="center" style="font-size: 15px; padding: 3px 0;">{{ data.olgTaskManagement.workTypeName }}&nbsp;&nbsp;&nbsp;&nbsp;{{ data.kd_time }}</td>
          </tr>
          <tr>
            <td colspan="2" align="center" style="font-size: 14px; height: 24px;">======= 工单信息 =======</td>
          </tr>
          <tr>
            <td align="right" style="width: 30%; font-size: 15px; height: 22px;">联系人：</td>
            <td align="left" class="callName" style="width: 70%; font-size: 15px; height: 22px;">{{ data.callerName }}</td>
          </tr>
          <tr>
            <td align="right" style="width: 30%; font-size: 15px; height: 22px;">工号：</td>
            <td align="left" style="width: 70%; font-size: 15px; height: 22px;">{{ data.olgTaskManagement.callerJobNum }}</td>
          </tr>
          <tr>
            <td align="right" style="width: 30%; font-size: 15px; height: 22px;">电话：</td>
            <td align="left" style="width: 70%; font-size: 15px; height: 22px;">{{ data.olgTaskManagement.sourcesPhone }}</td>
          </tr>
          <tr>
            <td align="right" style="width: 30%; font-size: 12px; height: 22px;">紧急程度：</td>
            <td align="left" style="width: 70%; font-size: 12px; height: 22px;">{{ data.urgencyDegree }}</td>
          </tr>
          <tr>
            <td align="right" style="width: 30%; font-size: 15px; height: 22px;">服务地点：</td>
            <td align="left" style="width: 70%; font-size: 15px; height: 22px;">
              <span v-for="(wx, wxi) in data.wxDetail" :key="wxi">
                {{ wx[1] }}
              </span>
            </td>
          </tr>
          <tr>
            <td align="right" style="width: 30%; font-size: 15px; height: 22px;">所属科室：</td>
            <td align="left" style="width: 70%; font-size: 15px; height: 22px;">{{ data.olgTaskManagement.sourcesDeptName }}</td>
          </tr>
          <tr v-if="data.olgTaskManagement.workTypeCode != '5'">
            <td align="right" style="width: 30%; font-size: 15px; height: 22px;">服务事项：</td>
            <td align="left" style="width: 70%; font-size: 15px; height: 22px;">
              <span v-for="(wx, wxi) in data.wxDetail" :key="wxi">
                <span v-if="wx[2] !== undefined && wx[2] !== '' && wx[3] !== undefined && wx[3] !== '' && wx[5] != undefined && wx[5] !== ''">{{
                  wx[2] + '-' + wx[3] + '-' + wx[5]
                }}</span>
              </span>
            </td>
          </tr>
          <tr valign="top">
            <td align="right" style="width: 30%; font-size: 15px; height: 22px;">申报描述：</td>
            <td id="remarkFb" align="left" style="width: 70%; font-size: 15px; height: 22px; word-wrap: break-word; word-break: break-all;">
              {{ data.olgTaskManagement.questionDescription }}
            </td>
          </tr>
          <tr>
            <td align="right" style="width: 30%; font-size: 12px; height: 22px;">服务部门：</td>
            <td align="left" style="width: 70%; font-size: 12px; height: 22px;">{{ data.olgTaskManagement.designateDeptName }}</td>
          </tr>
          <tr>
            <td align="right" style="width: 30%; font-size: 12px; height: 22px;">服务人员：</td>
            <td id="designatePersonPrint" align="left" style="width: 70%; font-size: 12px; height: 22px;">{{ data.olgTaskManagement.designatePersonName }}</td>
          </tr>
          <tr>
            <td align="right" style="width: 30%; font-size: 12px; height: 22px;">服务时间：</td>
            <td align="left" style="width: 70%; font-size: 12px; height: 22px;">
              <span>
                {{ data.olgTaskManagement.appointmentDate ? dateToString(data.olgTaskManagement.appointmentDate) : '立刻' }}
              </span>
            </td>
          </tr>
          <!--  增加字段实际耗材及空白区域  -->
          <tr>
            <td valign="top" align="right" style="width: 30%; font-size: 12px; height: 22px;">实际耗材：</td>
            <td>
              <canvas id="actualConsumable" height="100px" width="100px"></canvas>
            </td>
          </tr>
          <tr>
            <td colspan="2" align="center" style="font-size: 12px; height: 22px; padding: 8px 0;">
              <b style="display: inline-block; border: 1px solid #676a6c; width: 10px; height: 10px;"></b>&nbsp;&nbsp;非常满意
              <b style="display: inline-block; border: 1px solid #676a6c; width: 10px; height: 10px;"></b>&nbsp;&nbsp;满意
              <b style="display: inline-block; border: 1px solid #676a6c; width: 10px; height: 10px;"></b>&nbsp;&nbsp;一般
              <b style="display: inline-block; border: 1px solid #676a6c; width: 10px; height: 10px;"></b>&nbsp;&nbsp;差
              <b style="display: inline-block; border: 1px solid #676a6c; width: 10px; height: 10px;"></b>&nbsp;&nbsp;非常差
            </td>
          </tr>
          <tr>
            <td colspan="3" align="center" style="font-size: 12px; height: 22px;">
              <p align="left" style="margin-left: 20px;">注:请联系人/接收人在空白处签字并确认实际耗材与数量</p>
            </td>
          </tr>
          <br />
          <br />
          <br />
        </table>
      </div>
    </div>
  </div>
</template>
<script>
// import $ from 'jquery'
import moment from 'moment'
export default {
  name: 'DealBtn',
  components: {
    CreatedWorkOrder: () => import('../../CreatedWorkOrder'),
    DealFormDialog: () => import('./dealFormDialog')
  },
  props: {
    data: {
      type: Object,
      default: function () {
        return {}
      }
    },
    alarmDetail: {
      type: Object,
      default: function () {
        return {}
      }
    }
  },
  data() {
    return {
      workOrderDealShow: false, // 工单处理弹窗
      dealFormShow: false, // 操作按钮弹窗
      showBtnType: '', // 操作按钮类型
      returnVisitName: '' // 督办回访回复判断
    }
  },
  mounted() {},
  methods: {
    // 处理 打开处理工单弹窗
    taskDetailPlaceOrder() {
      this.workOrderDealShow = true
    },
    dealBtnEvent(type, returnVisitName) {
      this.showBtnType = type
      // 督办 回访 回复
      if (type === 'taskDetailReturnVisitFlag') {
        this.returnVisitName = returnVisitName
      }
      this.dealFormShow = true
    },
    // 处理工单保存返回
    workOrderSure(item) {
      this.workOrderDealShow = false
    },
    // 打印
    taskDetailDoPrint() {
      this.doPrintOperator()
    },
    // 打印
    doPrintOperator() {
      var content = ''
      var str = document.getElementById('pagec1').innerHTML // 获取需要打印的页面元素 ，page1元素设置样式page-break-after:always，意思是从下一行开始分割。
      content = content + str
      str = document.getElementById('pagec2').innerHTML // 获取需要打印的页面元素
      content = content + str
      document.getElementById('printTemplate').contentDocument.body.innerHTML = content
      window.frames['printTemplate'].print() // eslint-disable-line dot-notation
    },
    dateToString(date) {
      return moment(date).format('YYYY-MM-DD HH:mm:ss')
    },
    // 操作按钮提交返回
    btnTypeSubmit(type) {
      this.btnTypeClose()
      this.$emit('submit')
    },
    // 操作按钮关闭返回
    btnTypeClose() {
      this.dealFormShow = false
    }
  }
}
</script>
