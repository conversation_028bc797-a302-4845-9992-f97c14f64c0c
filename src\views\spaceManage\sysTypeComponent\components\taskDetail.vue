<template>
  <div class="main">
    <el-dialog
      v-dialogDrag
      :modal="false"
      :visible="visible"
      custom-class="mainDialog main"
      append-to-body
      :close-on-click-modal="false"
      :before-close="closeDialog"
      class="all-table-componentList"
    >
      <template slot="title">
        <span class="dialog-title">任务详情</span>
      </template>
      <div class="dialog-content">
        <div class="timeline_content">
          <el-form
            ref="detail"
            :model="detail"
            :inline="true"
            class="form-inline"
            label-width="120px"
          >
            <el-row :gutter="20">
              <el-col :span="8">
                <el-form-item label="机器人名称：">
                  <span>{{ detail.robotName }}</span>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="机器人类型：">
                  <span>{{ detail.robotModel }}</span>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="开始时间：">
                  <span
                  >{{ detail.startTime.split(" ")[0] }}
                    {{ detail.startTime.split(" ")[1] }}</span
                  >
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="发起人：">
                  <span>{{ detail.userName }}</span>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="出发地：">
                  <span>{{ detail.endPosition }}</span>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="任务类型：">
                  <span> {{ getJobType(detail.jobType) }}</span>
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
          <div class="timeline_content">
            <el-timeline>
              <el-timeline-item
                v-for="(activity, index) in detail.jobs"
                :key="index"
                :icon="
                  activity.jobState == 5 ? 'el-icon-success' : 'el-icon-error'
                "
                :size="index == 0 ? 'large' : 'large'"
                type="primary"
                :color="activity.color"
              >
                <div class="timestamp-row">
                  <p class="time">
                    {{ activity.startTime.split(" ")[0] }}
                    {{ activity.startTime.split(" ")[1] }}
                  </p>
                  <div class="continer">
                    <span class="item"> {{ activity.endPosition }}</span
                    >&nbsp;&nbsp;&nbsp;
                    <span class="item"
                    >操作人 : {{ activity.endUserName }}</span
                    >
                    <div style="margin-top: 12px">
                      {{ getJobState(activity.jobState) }}
                    </div>
                  </div>
                </div>
              </el-timeline-item>
            </el-timeline>
          </div>
        </div>
      </div>
    </el-dialog>
  </div>
</template>
<script>
export default {
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    detail: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {}
  },
  methods: {
    getJobState(state) {
      switch (state) {
        case 0:
          return '全部'
        case 1:
          return '任务队列中'
        case 2:
          return '任务下发中'
        case 3:
          return '任务执行中'
        case 4:
          return '任务到达'
        case 5:
          return '任务完成'
        case 6:
          return '取消任务'
        case 7:
          return '电量低终止'
        case 8:
          return '无消毒液终止'
        case 9:
          return '新任务终止'
        case 10:
          return '系统设置结束'
        case 11:
          return '资源不存在导致取消任务'
        case 12:
          return '任务过期取消任务'
        case 13:
          return '任务创建失败'
        case 14:
          return 'APP异常'
        case 15:
          return 'App放餐失败'
        case 16:
          return 'APP上报的任务执行超时'
        case 17:
          return '抽水传感器异常终止'
        default:
          return ''
      }
    },
    getJobType(state) {
      switch (state) {
        case 0:
          return '全部'
        case 10:
          return '配送任务'
        case 20:
          return '呼叫任务'
        case 30:
          return '返程任务'
        case 40:
          return '等待任务'
        case 50:
          return '充电任务'
        case 60:
          return '电梯等待区域任务'
        case 70:
          return '管制等待区域任务'
        case 80:
          return '定时消毒任务'
        case 90:
          return '到跨楼宇等待位置任务'
        case 100:
          return '即时消毒任务'
        case 110:
          return '查房任务'
        case 120:
          return '退药任务'
        case 130:
          return '补位任务'
        case 140:
          return '补货任务'
        case 150:
          return '送餐任务'
        case 160:
          return '回收任务'
        case 170:
          return '宣教任务'
        default:
          return ''
      }
    },
    closeDialog() {
      this.$emit('close')
    }
  }
}
</script>
<style lang="scss" scoped>
:deep(.mainDialog) {
  width: 70%;
  height: 84vh;
  margin-top: 6vh !important;
  // border: 1px solid #5996f9;
  background-color: transparent !important;
  pointer-events: auto;
  background-image: url("@/assets/images/table-bg.png");
  background-repeat: no-repeat;
  background-size: 100% 100%;
  box-shadow: none;

  .el-dialog__headerbtn {
    transform: translateX(-36px);
    width: 25px;
    height: 25px;
    background-image: url("@/assets/images/close.png");
    background-repeat: no-repeat;
    background-size: 100% 100%;
    .el-dialog__close::before {
      display: none;
    }
  }
  .dialog-title {
    display: inline-block;
    width: 100%;
    text-align: center;
    transform: translateY(-6px);
    color: #cbdeed;
  }
  .dialog-title::before {
    display: none;
  }

  .el-dialog__header {
    background-color: transparent;
  }
  .el-dialog__body {
    padding: 0;
    height: calc(100% - 60px);
    max-height: fit-content;
    overflow-y: hidden;
    background-color: transparent;
    padding: 0px 80px;
    .dialog-content {
      width: 100%;
      height: 100%;
      background-color: transparent;

      .timeline_content {
        padding: 20px 10px 0 10px;
        box-sizing: border-box;
        overflow-y: scroll;
        ::v-deep .el-timeline-item {
          padding-bottom: 0.5rem;
        }
        ::v-deep .el-timeline-item__tail {
          border-left: 0.0625rem solid #3d425a;
        }
        ::v-deep .el-timeline-item__node--normal {
          left: -0.015rem;
          width: 0.5rem;
          height: 0.5rem;
        }
        ::v-deep .el-timeline-item__icon {
          color: #e6cf9d;
          font-size: 1rem;
        }
        ::v-deep .el-timeline-item__node {
          background: #e6cf9d;
        }
        .timestamp-row {
          padding: 2px;
          font-family: PingFang SC;
          .time {
            color: #d0bd95;
          }
          .continer {
            margin-top: 16px;
            color: #a3a9c0;
          }
        }
      }
    }
  }
}
.el-form-item {
  :deep(.el-form-item__content) {
    width: calc(100% - 120px) !important;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
}
</style>
