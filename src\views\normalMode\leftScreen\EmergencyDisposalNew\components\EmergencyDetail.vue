<template>
  <div class='component EmergencyDetail'>
    <!-- 顶部tab -->
    <ul v-if="isView" class="EmergencyDetail__nav">
      <li :class="{ active: currentInfoType === tabConfig.base }" @click="switchInfo(tabConfig.base)">警情内容</li>
      <li v-if="tabShow.execute"
          :class="{ active: ['EmergencyExecuteReview', tabConfig.execute].includes(currentInfoType) }"
          @click="switchInfo(tabConfig.execute)">警情处理</li>
      <li v-if="tabShow.order" :class="{ active: currentInfoType === tabConfig.order }"
          @click="switchInfo(tabConfig.order)">关联工单
      </li>
      <li v-if="tabShow.summary"
          :class="{ active: ['EmergencySummaryReview', tabConfig.summary].includes(currentInfoType) }"
          @click="switchInfo(tabConfig.summary)">总结分析
      </li>
    </ul>

    <!-- 内容区域 -->
    <div v-loading="loadingStatus" class="EmergencyDetail__content" :style="{height: isView ? 'calc(100% - 140px)' : 'calc(100% - 40px)'}"
         :class="{ hideMonitor: currentInfoType === 'EmergencyWorkOrder' }">
      <div class="EmergencyDetail__content__left">
        <!-- 内容组件 -->
        <component :is="currentInfoType" ref="subPageRef" :isView="isView" :alarmId="alarmId" :extraData="extraData"
                   @refreshData="refreshData"></component>
      </div>
      <div class="EmergencyDetail__content__right">
        <!-- 视频画面 -->
        <EmergencyMonitor :alarmId="alarmId" :detail="detail" :objectId="baseInfo.alarmObjectId" />
      </div>
    </div>
    <!-- 底部action -->
    <div v-if="isView" class="EmergencyDetail__action">
      <!-- 在未获取到内容的时候，不展示操作按钮 -->
      <!-- 非战时状态 客户端不可操作的时候, 不展示操作按钮 -->
      <template v-if="ready && canExecute">
        <!-- 基础信息tab时 -->
        <template v-if="currentInfoType === tabConfig.base">
          <!-- 案件未完成，并且没有确警状态的可以处理 -->
          <template v-if="baseInfo.alarmStatus !== 2 && !baseInfo.alarmAffirm">
            <el-button class="sino-button-sure" @click="onConfirmClick('确警')">确警</el-button>
            <el-button class="sino-button-sure" @click="onConfirmClick('演习')">演习</el-button>
            <el-button class="sino-button-sure" @click="onConfirmClick('误报')">误报</el-button>
          </template>
          <!-- 只要没有派单，何时都能派单 -->
          <el-button v-if="!baseInfo.workNum" class="sino-button-sure" @click="onDispatchClick">派单</el-button>
          <el-button v-if="baseInfo.classic === 1" class="sino-button-sure" @click="onSetClassicClick">
            取消经典案例
          </el-button>
          <!-- 不是处理中(战时)的案件，就可以屏蔽 -->
          <template v-if="baseInfo.alarmStatus !== 1">
            <el-button v-if="baseInfo.shield === 1" class="sino-button-sure"
                       @click="onCancelShieldClick">取消屏蔽</el-button>
            <el-button v-else class="sino-button-sure" @click="showDialogShield = true">屏蔽</el-button>
          </template>
        </template>
        <!-- 警情处理 -->
        <template v-if="currentInfoType === tabConfig.execute">
          <el-button class="sino-button-sure" @click="onReleaseSubmit(0)">解除报警</el-button>
          <el-button class="sino-button-sure" @click="onReleaseSubmit(1)">解除并存经典</el-button>
        </template>
        <!-- 警情处理-回顾 -->
        <template v-if="currentInfoType === 'EmergencyExecuteReview'">
          <el-button v-if="baseInfo.classic === 0" class="sino-button-sure" @click="onSetClassicClick">
            存为经典案例
          </el-button>
        </template>
        <template v-if="currentInfoType === tabConfig.summary">
          <el-button class="sino-button-sure" @click="onSummarySubmit">提交</el-button>
        </template>
      </template>
      <el-button class="sino-button-sure" @click="$emit('cancel')">取消</el-button>
    </div>

    <!-- 屏蔽报警 -->
    <DialogEmergencyShield :visible.sync="showDialogShield" :ids="alarmId" :objectIds="baseInfo.alarmObjectId"
                           :types="baseInfo.alarmFleldsConfigId" @success="refreshData" />

  </div>
</template>

<script>
import { toDetailTab, alarmAffirmConfig, operatePlatform } from '../emergency-constant'
import { GetAlarmDetails, handleAlarmAffirmById, OneKeyDispatch, shield, setAlarmRecordToClassics, alarmSummarySave } from '@/utils/peaceLeftScreenApi'

export default {
  name: 'EmergencyDetail',
  components: {
    EmergencyBaseInfo: () => import('./EmergencyBaseInfo'),
    EmergencyExecute: () => import('./EmergencyExecute'),
    EmergencyExecuteReview: () => import('./EmergencyExecuteReview'),
    EmergencyWorkOrder: () => import('./EmergencyWorkOrder'),
    DialogEmergencyShield: () => import('./DialogEmergencyShield'),
    EmergencySummary: () => import('./EmergencySummary'),
    EmergencySummaryReview: () => import('./EmergencySummaryReview'),
    EmergencyMonitor: () => import('./EmergencyMonitor')
  },
  props: {
    isView: {
      type: Boolean,
      default: true
    },
    alarmId: {
      type: String,
      required: true
    },
    tab: String
  },
  data() {
    return {
      currentInfoType: 'EmergencyBaseInfo',
      showDialogShield: false, // 屏蔽报警弹窗
      tabConfig: toDetailTab,
      /** 案件详情，子组件依赖这个字段 */
      detail: {},
      loadingStatus: true, // 加载中
      // 顶部tab展示
      tabShow: {
        execute: false, // 显示处置tab
        order: false, // 显示工单tab
        summary: false // 显示总结分析
      },
      extraData: {} // 子组件额外数据
    }
  },
  computed: {
    /** 基本信息 */
    baseInfo: function () {
      const record = this.detail.record || {}
      if (record.alarmStatus > 0) {
        this.tabShow.execute = true
      }
      if (record.workNum) {
        this.tabShow.order = true
      }

      if (record.alarmStatus == 2 && record.alarmAffirm != 2) {
        this.tabShow.summary = true
      }

      return record
    },
    // 状态正常，可以操作案件
    ready() {
      return !!this.baseInfo.alarmId
    },
    // 是否存在总结内容
    hasSummary() {
      return !!this.detail?.summaryInfo?.id
    },
    /** 案件在当前平台可否进行操作 */
    canExecute() {
      return this.baseInfo.alarmStatus == 1 || /0/.test(this.baseInfo.disposalTerminal ?? '0')
    }
  },
  watch: {
    alarmId: {
      handler: function (newId) {
        if (!newId) return
        this.refreshData()
          .then(() => {
            this.switchInfo(this.tab || toDetailTab.base)
          })
      },
      immediate: true
    }
  },
  mounted() {
  },
  methods: {
    /** 刷新详情数据 */
    refreshData() {
      this.loadingStatus = true
      return GetAlarmDetails({ alarmId: this.alarmId })
        .then(res => {
          if (res.data.code == 200) {
            this.detail = res.data.data
          } else {
            throw res.data.msg || '获取报警详情失败'
          }
        })
        .catch(msg => {
          this.$message.error(msg)
        })
        .finally(() => this.loadingStatus = false)
    },
    /** 切换tab页面 */
    switchInfo(type) {
      if (type === toDetailTab.execute && !!this.baseInfo.alarmAffirm) {
        type = 'EmergencyExecuteReview'
      } else if (type === toDetailTab.summary && this.hasSummary) {
        type = 'EmergencySummaryReview'
      }
      this.currentInfoType = type
    },
    /** 设置，取消经典案例 */
    onSetClassicClick() {
      this.doConfirm(this.baseInfo.classic === 0 ? '确认存为经典案例?' : '确认取消经典案例?')
        .then(() => {
          const param = {
            classic: this.baseInfo.classic ^ 1,
            alarmId: this.alarmId,
            operationSource: operatePlatform.client
          }
          return setAlarmRecordToClassics(param)
        })
        .then((res) => {
          if (res.data.code === '200') {
            const message = this.baseInfo.classic === 0 ? '已设置' : '已取消'
            this.$message.success(message)
            this.refreshData()
          } else {
            this.$message.error(res.data.msg || '操作失败')
          }
        })
        .catch((err) => { console.error(err) })
    },
    // 一键派单
    onDispatchClick() {
      this.doConfirm('确认派发确警工单?')
        .then(() => {
          const userInfo = this.$store.state.loginInfo.user
          const param = {
            alarmId: this.alarmId,
            alarmLevel: this.baseInfo.alarmLevel,
            alarmSourceName: this.baseInfo.alarmSource,
            incidentName: this.baseInfo.incidentName,
            spaceId: this.baseInfo.alarmSpaceId,
            userName: userInfo.staffName,
            userId: userInfo.staffId,
            projectCode: this.baseInfo.projectCode,
            operationSource: operatePlatform.client
          }
          return OneKeyDispatch(param)
        })
        .then((res) => {
          if (res.data.code === '200') {
            this.$message.success('已派单')
            this.refreshData()
          } else {
            this.$message.error(res.data.msg)
          }
        })
        .catch(() => { })
    },
    // 点击取消屏蔽按钮
    onCancelShieldClick() {
      this.doConfirm('确定取消屏蔽当前报警?')
        .then(() => {
          this.loadingStatus = true
          return shield({
            shield: 0,
            alarmId: this.alarmId,
            alarmObjectId: this.baseInfo.alarmObjectId,
            alarmType: this.baseInfo.alarmFleldsConfigId,
            operationSource: operatePlatform.client
          })
        })
        .then((res) => {
          if (res.data.code === '200') {
            this.$message.success('已取消屏蔽')
            this.refreshData()
          } else {
            this.$message.error(res.data.msg || '取消屏蔽失败')
          }
        })
        .catch(() => { })
        .finally(() => this.loadingStatus = false)
    },
    // 去确警
    onConfirmClick(tag) {
      this.tabShow.execute = true
      this.switchInfo(toDetailTab.execute)
      this.extraData = {
        tag
      }
    },
    /**  解除报警（确警） */
    onReleaseSubmit(classicFlag) {
      this.$refs.subPageRef
        .validate() // 调用子组件的校验方法
        .catch(() => { throw null })
        .then((data) => { // 校验成功后会返回表单数据
          const affirm = alarmAffirmConfig.find(x => x.label === this.extraData.tag)
          if (!affirm) return
          // 再次组装表单
          const param = Object.assign({
            alarmAffirm: affirm.value,
            alarmId: this.alarmId,
            classic: classicFlag,
            operationSource: operatePlatform.client
          }, data)
          this.loadingStatus = true
          // 调用确认警情的方法
          return handleAlarmAffirmById(param)
        })
        .then(res => {
          if (res.data.code == 200) {
            this.$message.success('已解除')
            return this.refreshData()
          } else {
            throw res.data.msg || '解除失败'
          }
        })
        .then(() => this.switchInfo(toDetailTab.execute))
        .catch(msg => {
          if (!msg) return
          this.$message.error(msg)
        })
        .finally(() => this.loadingStatus = false)
    },
    /** 总结提交 */
    onSummarySubmit() {
      this.$refs.subPageRef
        .validate() // 调用子组件的校验方法
        .catch(() => { throw null })
        .then((data) => { // 校验成功后会返回表单数据
          // 再次组装表单
          const param = Object.assign({
            limAlarmId: this.alarmId,
            operationSource: operatePlatform.client
          }, data)
          this.loadingStatus = true
          // 调用确认警情的方法
          return alarmSummarySave(param)
        })
        .then(res => {
          if (res.data.code == 200) {
            this.$message.success('已添加')
            return this.refreshData()
          } else {
            throw res.data.msg || '添加失败'
          }
        })
        .then(() => this.switchInfo(toDetailTab.summary))
        .catch(msg => {
          if (!msg) return
          this.$message.error(msg)
        })
        .finally(() => this.loadingStatus = false)
    },
    /**
     * 页面通用询问框
     * @param msg 询问内容
     */
    doConfirm(msg) {
      return this
        .$confirm(msg, '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          cancelButtonClass: 'new-edition',
          confirmButtonClass: 'new-edition',
          customClass: 'confirm-box-class',
          type: 'warning'
        })
    }
  }
}

</script>

<style lang='scss' scoped>
.component.EmergencyDetail {
  color: #fff;
  height: 100%;

  .EmergencyDetail__nav {
    display: flex;
    justify-content: center;
    margin-bottom: 24px;

    >li {
      width: 88px;
      height: 40px;
      flex-shrink: 0;
      line-height: 40px;
      text-align: center;
      font-size: 16px;
      color: #a4acb9;
      background: url('~@/assets/images/qhdsys/bg-tab.png') no-repeat;
      cursor: pointer;

      &.active {
        color: #b0e3fa;
        background: url('~@/assets/images/qhdsys/bg-tab-xz.png') no-repeat;
      }
    }
  }

  .EmergencyDetail__content {
    height: calc(100% - 140px);
    padding: 0 35px;
    display: flex;
    flex-flow: row nowrap;
    overflow: hidden;
    align-items: stretch;

    &__left {
      flex: 1;
      overflow: hidden;
    }

    &__right {
      margin-left: 16px;
      flex: 1;
    }

    &.hideMonitor {

      .EmergencyDetail__content__right {
        display: none;
      }
    }
  }

  .EmergencyDetail__action {
    padding: 24px 35px 0;
    text-align: right;
  }
}
</style>
