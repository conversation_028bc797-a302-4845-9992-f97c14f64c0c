<script>
export default {
  name: '<PERSON><PERSON><PERSON><PERSON><PERSON>',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    title: { type: String, default: '标题' },
    /**
     * 数组格式，
     * {
     *   label:'ComponentName',
     *   name:'组件名称'
     * }
     */
    breadcrumb: Array
  },
  provide() {
    return { sizeChange: this.provideSizeChange }
  },
  data: () => ({
    // 全屏模式
    fullScreen: false,
    $callbackList: []
  }),
  computed: {
    showNav() {
      return Array.isArray(this.breadcrumb) && this.breadcrumb.length > 1
    }
  },
  watch: {
    // 监听弹窗开关，与客户端交互
    visible(val) {
      if (!val) {
        this.$store.commit('dialogFullScreen', false)
      }
      try {
        this.$tools.showDialog()
      } catch (error) {}
    }
  },
  emits: ['update:visible', 'back'],
  methods: {
    provideSizeChange(callback, isRegister = true) {
      if (!this.$callbackList) {
        this.$callbackList = []
      }
      if (isRegister) {
        this.$callbackList.push(callback)
      } else {
        const index = this.$callbackList.lastIndexOf(callback)
        if (index > -1) {
          this.$callbackList.splice(index, 1)
        }
      }
    },
    // 点击关闭按钮
    closeDialogFrame() {
      // 向父页面发送关闭事件
      this.$emit('update:visible', false)
      this.fullScreen = false
    },
    // 点击全屏按钮
    onFullScreenClick() {
      this.fullScreen = !this.fullScreen
      this.$store.commit('dialogFullScreen', this.fullScreen)
      if (this.$callbackList) {
        this.$callbackList.forEach(cb => cb(this.fullScreen))
      }
    },
    // 点击返回时触发
    onBackClick() {
      const beforeItem = this.breadcrumb.slice(-2)[0]
      if (beforeItem) {
        this.$emit('back', beforeItem.name)
      }
    }
  }
}
</script>
<template>
  <el-dialog v-dialogDrag :fullscreen="fullScreen" :class="{'is-nav': showNav}" class="component dialog-frame"
             :visible="visible" width="80%" :title="title" :close-on-click-modal="false" :show-close="false"
             custom-class="dialog-frame__dialog" :modal="false">
    <div class="dialog-frame__head">
      <div class="dialog-frame__head__close" @click="closeDialogFrame"></div>
      <div v-if="showNav" class="dialog-frame__head__nav">
        <div class="dialog-frame__head__back" @click="onBackClick">
        </div>
        <div class="dialog-frame__head__breadcrumb">
          <div v-for="item of breadcrumb" :key="item.name" class="dialog-frame__head__breadcrumb__item"
               @click="$emit('back',item.name)">{{ item.label }}
          </div>
        </div>
      </div>
    </div>
    <div class="dialog-frame__content" :style="{'padding-top': showNav ? '30px' : '0px'}">
      <slot></slot>
    </div>
    <div class="dialog-frame__btn-fullscreen" @click="onFullScreenClick"></div>
  </el-dialog>
</template>

<style lang="scss" scoped>
/* 全部数据弹窗，做为业务主窗体 */
.dialog-frame {
  // background: rgba(0, 0, 0, 0.8);
  font-family: Source Han Sans CN-Regular, Source Han Sans CN;

  &__head {
    position: absolute;
    left: 52px;
    top: 16px;
    right: 52px;
    display: flex;
    flex-flow: row-reverse nowrap;
    justify-content: space-between;

    &__close {
      width: 36px;
      height: 36px;
      background: url("@/assets/images/qhdsys/bg-icon-gb.png") no-repeat;
      cursor: pointer;
    }

    &__nav {
      display: flex;
      align-items: center;
    }

    &__back {
      width: 36px;
      height: 36px;
      background: url("@/assets/images/qhdsys/bg-icon-fx.png") no-repeat;
      cursor: pointer;
      transform: rotateY(180deg);
    }

    &__breadcrumb {
      margin-left: 8px;
      display: inline-flex;

      &__item {
        color: #a6afbf;
        cursor: pointer;

        &:not(:first-child) {
          &::before {
            content: "/";
            margin: 0 6px;
            color: #a6afbf;
          }
        }

        &:last-child {
          color: #b0e3fa;
          pointer-events: none;
        }
      }
    }
  }

  &__content {
    height: 100%;
    overflow: hidden;
  }

  &__btn-fullscreen {
    position: absolute;
    height: 64px;
    width: 64px;
    border-radius: 64px;
    cursor: pointer;
  }

  &.is-nav {
    ::v-deep(.dialog-frame__dialog.is-fullscreen) {
      > .el-dialog__header {
        display: none;
      }
    }
  }

  ::v-deep(.dialog-frame__dialog) {
    background: url("@/assets/images/qhdsys/1536x951.png") no-repeat center
      center / 100% 100%;
    transition: all 200ms;

    &:not(.is-fullscreen) {
      margin-top: 6vh !important;
      height: 88vh;
      overflow: hidden;

      .el-dialog__header {
        height: 38px;
        padding: 10px 20px 10px;
        text-align: center;
      }

      .el-dialog__body {
        padding: 28px 60px 28px;
        height: calc(100% - 56px);
        overflow: hidden;
      }

      .dialog-frame__head {
        position: absolute;
        left: 52px;
        top: 16px;
        right: 52px;
      }

      .dialog-frame__btn-fullscreen {
        background: url("@/assets/images/icon-fullscreen.png");
        bottom: 32px;
        right: 32px;

        &:hover {
          backdrop-filter: brightness(2);
        }
      }
    }

    &.is-fullscreen {
      background: radial-gradient(60% 60%, #152c4a, #061431);
      left: 0 !important;
      top: 0 !important;

      .el-dialog__body {
        padding: 24px;
        height: calc(100% - 48px);
        box-sizing: border-box;
        overflow: hidden;
      }

      .dialog-frame__head {
        position: absolute;
        left: 24px;
        top: 16px;
        right: 24px;
      }
      .dialog-frame__content{
        box-sizing: border-box;
      }

      .dialog-frame__btn-fullscreen {
        background: url("@/assets/images/icon-fullscreen2.png");
        bottom: 20px;
        right: 20px;
      }
    }

    .el-dialog__title {
      height: 20px;
      font-size: 18px;
      font-weight: 400;
      color: #cbdeed;
      line-height: 18px;
    }

    .el-dialog__body {
      color: #fff;

      .el-button.sino-button-sure {
        height: 33px;
        line-height: 33px;
        padding: 0 10px;
        background: url("~@/assets/images/qhdsys/button-bg.png") no-repeat
          center/100% 33px transparent;

        &:not(.is-disabled):hover {
          background: url("~@/assets/images/qhdsys/button-bg-hover.png")
            no-repeat center/100% 33px transparent;
        }
      }
    }
  }
}
</style>
