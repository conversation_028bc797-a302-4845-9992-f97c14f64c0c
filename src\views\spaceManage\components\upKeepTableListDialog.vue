<template>
  <div class="dialog-content">
    <el-dialog title="待保养任务" :visible.sync="dialogShow" width="60.1042%" class="dialog-url" :show-close="false">
      <div class="dialog-right">
        <div class="dialog-tc" @click="handleClose"></div>
      </div>
      <div class="dialog-div">
        <el-table v-loading="tableLoading" :data="tableData" style="width: 100%">
          <el-table-column v-for="(column, index) in tableColumn" :key="index" :prop="column.prop" :label="column.label" :min-width="column.minWidth" show-overflow-tooltip>
            <template slot-scope="scope">
              <table-render v-if="column.render" :sc="scope" :row="scope.row" :render="column.render"></table-render>
              <div v-else-if="!column.formatter">
                {{ scope.row[column.prop] }}
              </div>
              <div v-else>
                {{ column.formatter(scope.row) }}
              </div>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <div class="dialog-foot">
        <div class="foot-zs">共{{ total }}条</div>
        <div>
          <el-pagination layout="prev, pager, next" :total="total" :page-size="pageSize" @current-change="handleCurrentChange"> </el-pagination>
        </div>
      </div>
    </el-dialog>
    <inspectionDetail v-if="inspectionDetailShow" ref="inspectionDetail" systemType="byrw" :dataInfo="taskkDetailObj" @closeDialog="closeInspDetailDialog" @destroy="destroyInspDetailDialog"></inspectionDetail>
  </div>
</template>

<script lang="jsx">
import { getInspectionData } from '@/utils/centerScreenApi'
import tableRender from './tableRender.vue'
import inspectionDetail from '../sysTypeComponent/components/inspectionDetail.vue'
export default {
  name: '',
  components: {
    'table-render': tableRender,
    inspectionDetail
  },
  props: {
    dialogShow: {
      type: Boolean,
      default: false
    },
    upkeepTime: {
      type: String,
      default: ''
    },
    deviceId: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      tableData: [],
      inspectionDetailShow: false,
      tableColumn: [
        {
          prop: 'taskName',
          label: '任务名称'
        },
        {
          prop: 'planTypeName',
          label: '计划类型'
        },
        {
          prop: 'planName',
          label: '计划名称'
        },
        {
          prop: 'taskName',
          label: '周期类型',
          render: (h, row) => {
            return <span>{this.cycleTypeFn(row.row)}</span>
          }
        },
        {
          prop: 'taskStartTime',
          label: '保养日期'
        },
        {
          prop: 'planPersonName',
          label: '保养小组/人员',
          render: (h, row) => {
            return <span>{row.row.planPersonName || row.row.distributionTeamName}</span>
          }
        },
        {
          prop: '',
          label: '操作',
          render: (h, row) => {
            return (
              <div class="operationBtn">
                <span onClick={() => this.selectConfigRowData(row.row)}>详情</span>
              </div>
            )
          }
        }
      ],
      //   分页
      currentPage: 1,
      pageSize: 10,
      total: 0,
      tableLoading: false,
      cycleTypeArr: [
        {
          cycleType: 8,
          label: '单次'
        },
        {
          cycleType: 6,
          label: '每日'
        },
        {
          cycleType: 0,
          label: '每周'
        },
        {
          cycleType: 2,
          label: '每月'
        },
        {
          cycleType: 3,
          label: '季度'
        },
        {
          cycleType: 5,
          label: '全年'
        }
      ],
      taskkDetailObj: {}
    }
  },
  computed: {},
  watch: {},
  created() {},
  mounted() {
    this.getListTableData()
  },
  methods: {
    // 退出
    handleClose() {
      try {
        window.chrome.webview.hostObjects.sync.bridge.GetSwichFloorIsShow(false)
        window.chrome.webview.hostObjects.sync.bridge.ShowDialog(false)
      } catch (error) {}
      this.$emit('configCloseDialog')
    },
    closeInspDetailDialog() {
      this.inspectionDetailShow = false
    },
    destroyInspDetailDialog() {
      try {
        window.chrome.webview.hostObjects.sync.bridge.ShowDialog(true)
      } catch (error) {}
    },
    // 获取列表
    getListTableData() {
      this.tableLoading = true
      let params = {
        pageNo: this.currentPage, // 当前页
        pageSize: this.pageSize, // 每页条数
        taskStatus: 6,
        taskStartTime: this.upkeepTime,
        systemCode: 2,
        deviceId: this.deviceId
      }
      getInspectionData(params).then((res) => {
        const { data } = res
        if (data.code === '200') {
          this.tableLoading = false
          this.tableData = data.data.list
          this.total = data.data.sum.allCount
        } else {
          this.tableLoading = false
        }
      })
    },
    selectConfigRowData(row) {
      this.inspectionDetailShow = true
      this.taskkDetailObj = row
      this.$nextTick(() => {
        this.$refs.inspectionDetail.hiddenDangerDetailsListShow = true
        this.$refs.inspectionDetail.GetTaskPointReleaseList(row.id, 'insp')
      })
      try {
        window.chrome.webview.hostObjects.sync.bridge.GetSwichFloorIsShow(true)
        window.chrome.webview.hostObjects.sync.bridge.ShowDialog(true)
      } catch (error) {}
    },
    // 周期类型
    cycleTypeFn(row) {
      const item = this.cycleTypeArr.filter((i) => i.cycleType == row.cycleType)
      return item[0].label
    },
    handleCurrentChange(val) {
      this.currentPage = val
      this.getListTableData()
    }
  }
}
</script>
<style lang="scss" scoped>
.dialog-content {
  position: relative;
  .dialog-url {
    ::v-deep .el-dialog {
      height: 603px;
      background: url('@/assets/images/qhdsys/bg-tc.png') no-repeat;
      background-size: 100% 100%;
      .el-dialog__header {
        padding: 11px 20px 10px;
        text-align: center;
        .el-dialog__title {
          height: 20px;
          font-size: 18px;
          font-family: Source Han Sans CN-Regular, Source Han Sans CN;
          font-weight: 400;
          color: #cbdeed;
          line-height: 20px;
        }
      }
    }
    .dialog-div {
      padding: 0 65px;
      ::v-deep .el-table {
        border: 1px solid #203254 !important;
        .el-table__header-wrapper {
          .el-table__header {
            .has-gutter {
              tr {
                background: rgba(133, 145, 206, 0.15);
                // border-bottom: 2px solid #ffffff;
                th {
                  padding: 0;
                  height: 44px;
                  .cell {
                    font-size: 14px;
                    font-family: HarmonyOS Sans SC-Medium, HarmonyOS Sans SC;
                    font-weight: 500;
                    color: #8bddf5;
                  }
                }
              }
            }
          }
        }
        .el-table__body-wrapper {
          background: transparent;
          height: 360px;
          overflow: hidden;
          overflow-y: auto;
          .el-table__body {
            tbody {
              .el-table__row {
                background: transparent;
                border: 0;
                td {
                  padding: 0;
                  height: 40px;
                  border: 0;
                  .cell {
                    font-size: 14px;
                    font-family: HarmonyOS Sans SC-Regular, HarmonyOS Sans SC;
                    font-weight: 400;
                    color: #ffffff;
                  }
                }
              }
              .el-table__row:nth-child(2n - 1) {
                background: rgba(168, 172, 171, 0.08);
              }
              .el-table__row:hover {
                border: 0;
                opacity: 1;
                cursor: pointer;
                td div {
                  color: rgba(255, 202, 100, 1);
                }
              }
            }
          }
        }
      }
    }
    .dialog-foot {
      padding: 0 65px;
      margin-top: 41px;
      display: flex;
      align-items: center;
      justify-content: space-between;
      .foot-zs {
        font-size: 14px;
        font-family: HarmonyOS Sans SC-Regular, HarmonyOS Sans SC;
        font-weight: 400;
        color: #ffffff;
        line-height: 14px;
      }
      ::v-deep .el-pagination {
        .btn-prev {
          background: transparent;
          .el-icon {
            color: #ffffff;
          }
        }
        .btn-next {
          background: transparent;
          .el-icon {
            color: #ffffff;
          }
        }
      }
    }
    .dialog-right {
      position: absolute;
      top: 17px;
      right: 60px;
      display: flex;
      .dialog-fx {
        width: 36px;
        height: 36px;
        margin-right: 8px;
        background: url('@/assets/images/qhdsys/bg-icon-fx.png') no-repeat;
      }
      .dialog-fx:hover {
        cursor: pointer;
      }
      .dialog-tc {
        width: 36px;
        height: 36px;
        background: url('@/assets/images/qhdsys/bg-icon-gb.png') no-repeat;
      }
      .dialog-tc:hover {
        cursor: pointer;
      }
    }
  }
}
::v-deep .el-loading-mask {
  background: rgb(13, 27, 54, 0.9) !important;
}
</style>
