<template>
  <dialogFrame
    :visible="isDialog"
    title="危化品列表"
    @update:visible="closeDialogFrame"
  >
    <div class="hazMatListDialog">
      <div v-scrollMove class="bg-tab">
        <div
          v-for="item in tabList"
          :id="item.value"
          :key="item.value"
          class="tab-div"
          :class="{ 'is-activeTab': activeTab === item.value }"
          @click="activeTabEvent(item.value)"
        >
          {{ item.name }}
        </div>
      </div>
      <div style="flex: 1; overflow: hidden">
        <div class="list-index-view">
          <div class="view-header-box">
            <div class="header-left">
              <el-form :model="searchForm" class="search-form" inline>
                <el-form-item>
                  <el-input v-model="searchForm.name" size="small" placeholder="危化品名称/编码" clearable></el-input>
                </el-form-item>
                <el-form-item v-if="activeTab == 1">
                  <el-select v-model="searchForm.warehouseId" size="small" placeholder="所属库房" clearable>
                    <el-option v-for="item in storehousetList" :key="item.id" :label="item.warehouseName" :value="item.id"></el-option>
                  </el-select>
                </el-form-item>
                <el-form-item v-if="activeTab == 3">
                  <el-select v-model="searchForm.officesId" size="small" placeholder="领用单位" clearable>
                    <el-option v-for="item in officesLsit" :key="item.umId" :label="item.unitComName" :value="item.umId"></el-option>
                  </el-select>
                </el-form-item>
                <el-form-item v-if="activeTab != 1">
                  <el-select v-model="searchForm.supplierId" size="small" placeholder="供应商" clearable>
                    <el-option v-for="item in unitsManageLsit" :key="item.id" :label="item.unitsName" :value="item.id"></el-option>
                  </el-select>
                </el-form-item>
                <el-form-item v-if="activeTab == 1">
                  <el-cascader
                    v-model="searchForm.materialTypeCode"
                    :props="{
                      label: 'name',
                      value: 'code',
                      checkStrictly: true
                    }"
                    clearable
                    size="small"
                    :show-all-levels="false"
                    :filterable="true"
                    placeholder="全部类型"
                    :options="materialTypeTree"
                  >
                  </el-cascader>
                </el-form-item>
                <el-form-item v-if="activeTab != 1">
                  <el-date-picker
                    v-model="searchForm.dateRange"
                    type="daterange"
                    unlink-panels
                    popper-class="date-style"
                    range-separator="至"
                    start-placeholder="开始日期"
                    end-placeholder="结束日期"
                    value-format="yyyy-MM-dd"
                  >
                  </el-date-picker>
                </el-form-item>
              </el-form>
            </div>
            <div class="header-right">
              <el-button class="sino-button-sure" @click="resetForm">重置</el-button>
              <el-button class="sino-button-sure" @click="handleSearchForm">查询</el-button>
            </div>
          </div>
          <div class="view-content">
            <el-table
              ref="table"
              v-loading="tableLoading"
              :data="tableData"
              :resizable="false"
              height="calc(100% - 46px)"
              :cell-style="$tools.setCell(3)"
              :header-cell-style="$tools.setHeaderCell(3)"
              stripe
              element-loading-background="rgba(0, 0, 0, 0.2)"
            >
              <el-table-column v-for="(item, index) in tableColumn" :key="item.prop + index" :prop="item.prop" :label="item.label" show-overflow-tooltip></el-table-column>
            </el-table>
            <el-pagination
              :current-page="currentPage"
              :page-sizes="[15, 20, 30, 40]"
              :page-size="pageSize"
              layout="total, sizes, prev, pager, next, jumper"
              :total="total"
              class="pagination"
              @size-change="handleSizeChange"
              @current-change="handleCurrentChange"
            ></el-pagination>
          </div>
        </div>
      </div>
    </div>
  </dialogFrame>
</template>

<script>
import { GetInventoryList, GetInventorySelectList, inwarehouseDetailList, outwarehouseDetailList, GetMaterialTypeTree, GetUnitsManageLsit, GetUnitManagerSelected } from '@/utils/spaceManage'
export default {
  name: 'hazMatListDialog',
  components: {
    dialogFrame: () => import('@/components/common/DialogFrame')
  },
  props: {
    isDialog: {
      type: Boolean,
      default: false
    },
    roomData: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      searchForm: {
        name: '', // 名称/编码
        warehouseId: '', // 库房id
        materialTypeCode: '', // 类型
        supplierId: '', // 供应商id
        officesId: '', // 单位
        dateRange: []
      },
      materialTypeTree: [],
      storehousetList: [],
      unitsManageLsit: [], // 供应商
      officesLsit: [], // 单位
      tableLoading: false,
      currentPage: 1,
      pageSize: 15,
      total: 0,
      tableData: [],

      activeTab: 1,
      tabList: [
        {
          value: 1,
          name: '危化品台账'
        },
        {
          value: 2,
          name: '入库记录'
        },
        {
          value: 3,
          name: '出库记录'
        }
      ]
    }
  },
  computed: {
    tableColumn() {
      if (this.activeTab == 1) {
        return [
          { label: '危化品名称', prop: 'materialName' },
          { label: '危化品编码', prop: 'materialCode' },
          { label: '基本单位', prop: 'basicUnitName' },
          { label: '规格型号', prop: 'model' },
          { label: '存放地名称', prop: 'warehouseName' },
          { label: '库存数量', prop: 'inventory' },
          { label: '最低库存', prop: 'minStock' },
          { label: '最高库存', prop: 'maxStock' },
          { label: '所属分类', prop: 'materialTypeName' },
          { label: '商品编码', prop: 'goodsCode' },
          { label: '品牌', prop: 'trademark' },
          { label: '生产厂家', prop: 'manufacturer' }
        ]
      } else if (this.activeTab == 2) {
        return [
          { label: '入库单号', prop: 'orderNumber' },
          { label: '危化品编码', prop: 'materialCode' },
          { label: '危化品名称', prop: 'materialName' },
          { label: '规格型号', prop: 'model' },
          { label: '基础单位', prop: 'basicUnitName' },
          { label: '入库仓库', prop: 'warehouseName' },
          { label: '入库时间', prop: 'createTime' },
          { label: '入库数量', prop: 'operateCount' },
          { label: '进货单价（元）', prop: 'unitPrice' },
          { label: '总金额（元）', prop: 'amount' },
          { label: '有效期', prop: 'serviceLife' },
          { label: '品牌', prop: 'trademark' },
          { label: '供应商', prop: 'supplierName' }
        ]
      } else {
        return [
          { label: '入库单号', prop: 'orderNumber' },
          { label: '危化品编码', prop: 'materialCode' },
          { label: '危化品名称', prop: 'materialName' },
          { label: '规格型号', prop: 'model' },
          { label: '基础单位', prop: 'basicUnitName' },
          { label: '出库仓库', prop: 'warehouseName' },
          { label: '出库时间', prop: 'createTime' },
          { label: '出库数量', prop: 'operateCount' },
          { label: '进货单价（元）', prop: 'inventory' },
          { label: '总金额（元）', prop: 'amount' },
          { label: '有效期', prop: 'serviceLife' },
          { label: '品牌', prop: 'trademark' },
          { label: '供应商', prop: 'supplierName' }
        ]
      }
    }
  },
  created() {
    this.getUnitManagerSelected()
    this.getUnitsManageLsit()
    this.getMaterialTypeTree()
    this.getInventorySelectList()
    this.getDataList()
  },
  methods: {
    // 列表
    getDataList() {
      let obj = {
        1: GetInventoryList,
        2: inwarehouseDetailList,
        3: outwarehouseDetailList
      }
      this.searchForm.dateRange = this.searchForm.dateRange || []
      let params = {}
      if (this.activeTab == 1) {
        params = {
          gridCode: this.roomData.ssmCodes?.split(',')?.at(-1),
          warehouseId: this.searchForm.warehouseId,
          materialName: this.searchForm.name,
          materialCode: this.searchForm.name,
          materialTypeCode: this.searchForm.materialTypeCode,
          currentPage: this.currentPage,
          pageSize: this.pageSize
        }
      } else if (this.activeTab == 2 || this.activeTab == 3) {
        params = {
          gridCode: this.roomData.ssmCodes?.split(',')?.at(-1),
          unionSel: this.searchForm.name,
          supplierId: this.searchForm.supplierId,
          userId: this.$store.state.loginInfo.user.staffId,
          userName: this.$store.state.loginInfo.user.staffName,
          beginDate: this.searchForm?.dateRange[0] ?? '',
          endDate: this.searchForm?.dateRange[1] ?? '',
          currentPage: this.currentPage,
          pageSize: this.pageSize
        }
      }
      if (this.activeTab == 3) params.officesId = this.searchForm.officesId
      this.tableLoading = true
      obj[this.activeTab](params).then(res => {
        this.tableLoading = false
        if (res.data.code == 200) {
          this.tableData = res.data.data.list
          this.total = res.data.data.sum
        }
      }).catch(() => {
        this.tableLoading = false
      })
    },
    // 单位
    getUnitManagerSelected() {
      GetUnitManagerSelected({}).then(res => {
        if (res.data.code == 200) {
          this.officesLsit = res.data.data
        }
      })
    },
    // 供应商
    getUnitsManageLsit() {
      let params = {
        category: 2,
        unitsTypeCode: 'GYS',
        userId: this.$store.state.loginInfo.user.staffId,
        userName: this.$store.state.loginInfo.user.staffName
      }
      GetUnitsManageLsit(params).then(res => {
        if (res.data.code == 200) {
          this.unitsManageLsit = res.data.data.list
        }
      })
    },
    // 类型树
    getMaterialTypeTree() {
      let params = {
        type: 'WZFL',
        userId: this.$store.state.loginInfo.user.staffId,
        userName: this.$store.state.loginInfo.user.staffName
      }
      GetMaterialTypeTree(params).then(res => {
        if (res.data.code == 200) {
          res.data.data.forEach(item => {
            item.children = null
          })
          this.materialTypeTree = this.$tools.transData(res.data.data, 'id', 'pid', 'children')
        }
      })
    },
    // 获取仓库下拉
    getInventorySelectList() {
      GetInventorySelectList({}).then(res => {
        if (res.data.code == 200) {
          this.storehousetList = res.data.data.list
        }
      })
    },
    /** tab 切换事件 */
    activeTabEvent(val) {
      this.activeTab = val
      this.tableData = []
      this.getDataList()
    },
    /** 查询 */
    handleSearchForm() {
      this.getDataList()
    },
    /** 重置 */
    resetForm() {
      Object.assign(this.$data.searchForm, this.$options.data().searchForm)
      this.handleSearchForm()
    },
    viewDetail() {
      this.$emit('openDetailComponent', 'constructionFlow')
    },
    /** 条数变化事件 */
    handleSizeChange(val) {
      this.pageSize = val
      this.handleSearchForm()
    },
    /** 页数变化事件 */
    handleCurrentChange(val) {
      this.currentPage = val
      this.handleSearchForm()
    },
    /** 关闭弹窗 */
    closeDialogFrame() {
      this.$emit('close', false)
    }
  }
}

</script>

<style lang="scss" scoped>
.hazMatListDialog {
  height: 100%;
  display: flex;
  flex-direction: column;
  .bg-tab {
    display: flex;
    justify-content: center;
    overflow: hidden;
    box-sizing: border-box;
    margin-bottom: 16px;
    .tab-div {
      width: 100px;
      height: 40px;
      flex-shrink: 0;
      line-height: 40px;
      text-align: center;
      font-size: 16px;
      color: #a4acb9;
      background: url("@/assets/images/bg-tab.png") no-repeat;
      background-size: 100% 100%;
      cursor: pointer;
    }
    .is-activeTab {
      color: #b0e3fa;
      background: url("@/assets/images/bg-tab-xz.png") no-repeat;
      background-size: 100% 100%;
    }
  }
  .list-index-view {
    position: relative;
    height: 100%;
    padding: 0px 40px;
    .view-header-box {
      display: flex;
      justify-content: space-between;
      ::v-deep(.el-date-editor) {
        margin-top: 4px;
        width: 300px;
        height: 32px;
        background-color: transparent;
        border-color: #3056A2;
        border-radius: 0;
        .el-input__icon {
          transform: translateY(-4px);
        }
        .el-range-input {
          height: 30px;
          background-color: transparent;
          color: rgba(255, 255, 255, 0.8);
        }
        .el-range-separator {
          color: rgba(255, 255, 255, 0.8);
          transform: translateY(-4px);
        }
      }
      .header-right {
        line-height: 40px;
      }
    }
    .view-nav-box {
      margin-bottom: 1.5rem;
      display: flex;
      align-items: center;
      box-sizing: border-box;
      .nav-box {
        height: 2.5rem;
        line-height: 2.5rem;
        width: 12.38rem;
        font-size: 1rem;
        text-align: center;
        color: #a4afc1;
        cursor: pointer;
        box-sizing: border-box;
        border: 1px dashed #26314f;
        border-right: none;
        background: url(@/assets/images/nav-bg.png) no-repeat;
        background-size: cover;
      }
      .nav-box:last-child {
        border-right: 1px dashed #26314f;
      }
      .active {
        color: #b0e3fa;
        background: url(@/assets/images/nav-bg-xz.png) no-repeat;
        background-size: cover;
      }
    }
    .view-content {
      height: calc(100% - 4rem);
      .table-view {
        height: 100%;
      }
      .axis-big-box {
        width: 100%;
        height: 5.62rem;
        box-sizing: border-box;
        padding: 0px 20px;
        overflow-x: auto;
        display: flex;
      }
      .axis-box {
        height: 5rem;
        position: relative;
        display: flex;
        align-items: center;
        .axis {
          width: 100%;
          height: 1px;
          background: #ffca64;
          position: absolute;
          left: 0px;
          top: 45px;
        }
        .axis-text-box {
          margin-right: 1.25rem;
          font-size: 14px;
          div {
            width: 5.44rem;
            height: 1.25rem;
            line-height: 1.25rem;
            text-align: center;
          }
          &-top {
            font-size: 14px;
            color: #b0e3fa;
            margin-top: 0.44rem;
          }
          &-bot {
            margin-top: 1.44rem;
            border-radius: 2px;
            font-size: 12px;
            color: #ffca64;
            background: rgba(255, 202, 100, 0.2);
            position: relative;
          }
          &-bot::after {
            content: "";
            width: 1px;
            height: 0.75rem;
            background: #ffca64;
            position: absolute;
            left: 50%;
            top: -0.75rem;
          }
          &-bot::before {
            content: "";
            width: 5px;
            height: 5px;
            border-radius: 5px;
            background: #ffca64;
            position: absolute;
            left: 2.6rem;
            top: -0.75rem;
          }
        }
      }
    }
    .operation-span {
      cursor: pointer;
      color: #8BDDF5;
    }
    ::v-deep .el-table--striped .el-table__body tr.el-table__row--striped td.el-table__cell{
      background: transparent;
    }
  }
}
</style>
