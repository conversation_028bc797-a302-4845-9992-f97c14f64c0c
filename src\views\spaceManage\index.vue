<template>
  <div class="content">
    <div v-if="NODE_ENV_FLAG">
      <!-- <div v-if="process.env.NODE_ENV === 'developmen'"> -->
      <!-- <div style="color: #ffe3a6">{{ WPFmessage }}</div> -->
      <!-- <div style="color: #ffe3a6">{{ toWPFdata }}</div> -->
      <!-- <el-button class="sino-button-sure" @click="allTableChange('ioms')">showIomsDialog</el-button> -->
      <el-button class="sino-button-sure" @click="showCenterTableDialog('ioms')"
      >showCenterTableDialog</el-button
      >
      <el-button class="sino-button-sure" @click="szzlyyChange('build')"
      >SZZLYYBuild</el-button
      >
      <el-button class="sino-button-sure" @click="szzlyyChange('floor')"
      >SZZLYYFloor</el-button
      >
      <el-button class="sino-button-sure" @click="szzlyyChange('room')"
      >SZZLYYRoom</el-button
      >
      <el-button class="sino-button-sure" @click="szzlyyChange('Airend')"
      >SZZLYY12楼</el-button
      >
      <el-button class="sino-button-sure" @click="szzlyyChange('Electricity')"
      >SZZLYY重点设备</el-button
      >
      <el-button class="sino-button-sure" @click="szzlyyChange('parkingLot')"
      >停车场管理</el-button
      >
      <el-button class="sino-button-sure" @click="bjsjtyyChange('build')"
      >BJSJTYYBuild</el-button
      >
      <el-button class="sino-button-sure" @click="bjsjtyyChange('floor')"
      >BJSJTYYFloor</el-button
      >
      <!-- <el-button class="sino-button-sure" @click="bjsjtyyChange('room')">BJSJTYYRoom</el-button> -->
    </div>
    <div class="sham-content"></div>
    <div class="roomInfoManagement">
      <!-- <div @click="collapseHeight" :class="collapseFlag ? 'circle-btn-top' : 'circle-btn-bottom'" class="circle-btn circle-btn-top"></div> -->
      <div id="collapseWidth" ref="collapseWidth" class="right-content">
        <div v-show="collapseFlag" class="bg-title">
          <!-- 表头 -->
          <div v-scrollMove class="bg-tab">
            <div
              v-for="(item, index) in roomTypeList"
              :id="item.Dom"
              :key="index"
              class="tab-div"
              :class="{ 'is-activeTab': activeTabIndex === item.Dom }"
              @click="activeTypeEvent(item.Dom, index)"
            >
              {{ item.name }}
            </div>
          </div>
          <div class="center-empty"></div>
          <div class="icon-collapse">
            <el-dropdown trigger="click" @command="tabItemCommand">
              <img src="@/assets/images/qhdsys/bg-gd.png" alt="" />
              <el-dropdown-menu slot="dropdown" class="dropdown">
                <el-dropdown-item
                  v-for="(item, index) in roomTypeList"
                  :key="index"
                  :command="item.Dom"
                  :class="{ isBjxl: activeTabIndex === item.Dom }"
                >{{ item.name }}</el-dropdown-item
                >
              </el-dropdown-menu>
            </el-dropdown>
          </div>
        </div>
        <div style="width: 100%; height: 100%; overflow: hidden">
          <div v-show="collapseFlag" class="bg-content room-info-box">
            <div class="sys-box">
              <component
                :is="currentComponent"
                :ref="currentComponent"
                :roomData="roomInfo"
                :assetsList="assetsList"
                class="sys-box-content"
                @roomEvent="roomEvent"
                @sendWpfData="setWPFParamsData"
              ></component>
            </div>
          </div>
        </div>
      </div>
    </div>
    <template v-if="allTableComponentListShow">
      <allTableComponentList
        ref="allTableComponentList"
        :dialogData="tableCompenentData"
        :dialogShow="allTableComponentListShow"
        @configCloseDialog="configCloseTableDialog"
      ></allTableComponentList>
    </template>
    <template v-if="centerTableDialogShow">
      <component
        :is="centerTableComponent"
        :ref="centerTableComponent"
        :ssmCodes="tagSsmCodes"
        :location="localtion"
        :ssmType="ssmType"
        :type="hazardType"
        :dialogData="centerTableData"
        :dialogShow="centerTableDialogShow"
        @configCloseDialog="configCloseCenterTableDialog"
      ></component>
    </template>
    <template v-if="ipasPointDetailShow">
      <ipasPointDetail
        ref="retrospect"
        :dialogShow="ipasPointDetailShow"
        :detailId="detailId"
        @retrospectCloseDialog="retrospectCloseDialog"
      >
      </ipasPointDetail>
    </template>
    <template v-if="deviceInfoManagementShow">
      <deviceInfoManagement
        ref="deviceInfoManagement"
        :dialogData="deviceInfo"
        :dialogShow="deviceInfoManagementShow"
        @configCloseDialog="configCloseDeviceRightDialog"
      ></deviceInfoManagement>
    </template>
    <template v-if="deviceDetailsListShow">
      <deviceDialog
        ref="deviceDetailDialog"
        :dialogData="deviceDetailInfo"
        :dialogShow="deviceDetailsListShow"
        @deviceCloseDialog="() => changeDeviceDetailDialogShow(false)"
      ></deviceDialog>
    </template>
    <template v-if="parkingLotVisible">
      <parkingLot
        ref="parkingLot"
        :dialogData="parkingLotData"
        :dialogShow="parkingLotVisible"
        @configCloseDialog="configCloseDeviceDialog"
      ></parkingLot>
    </template>
    <paramDetails
      v-if="isParamDetails"
      :dialogShow="isParamDetails"
      :paramData="paramData"
      @paramDetailsClose="paramDetailsClose"
    />
    <template v-if="inspectionDetailsShow">
      <inspectionDetails
        ref="inspectionDetails"
        :dialogData="deviceInfo"
        :dialogShow="inspectionDetailsShow"
        @configCloseDialog="inspectionDetailsClose"
      />
    </template>
  </div>
</template>

<script>
import $ from 'jquery'
import allTableComponentList from './components/allTableComponentList.vue'
import roomInfoManagement from './roomInfoManagement.vue'
import { getSurveyAssetByProjectCode } from '@/utils/spaceManage'
import { monitorTypeList } from '@/assets/common/dict.js'
export default {
  inject: ['reload'],
  name: 'spaceManage',
  components: {
    allTableComponentList,
    roomInfoManagement,
    workOrderCenterList: () =>
      import(
        '@/views/centerScreen/businessManagement/component/workOrderCenterList.vue'
      ),
    ipasPointRecord: () =>
      import(
        '@/views/centerScreen/businessManagement/component/ipasPointRecord.vue'
      ),
    collectRecord: () =>
      import(
        '@/views/centerScreen/businessManagement/component/collectRecord.vue'
      ),
    ipasPointDetail: () =>
      import(
        '@/views/centerScreen/businessManagement/component/ipasPointDetail.vue'
      ),
    hazardList: () =>
      import('@/views/centerScreen/safetyOverview/components/hazardList.vue'),

    floorSpaceComponent: () =>
      import('./sysTypeComponent/floorSpaceComponent.vue'),
    // pipelineComponent: () => import('./sysTypeComponent/pipelineComponent.vue'),
    // baseInfoComponent: () => import('./sysTypeComponent/baseInfoComponent'),
    iomsComponent: () => import('./sysTypeComponent/iomsComponent'),
    energyManage: () => import('./sysTypeComponent/energyManage'),
    imesComponent: () => import('./sysTypeComponent/imesComponent'),
    // imwsComponent: () => import('./sysTypeComponent/imwsComponent'),
    // icisComponent: () => import('./sysTypeComponent/icisComponent'),
    // riskComponent: () => import('./sysTypeComponent/riskComponent'),
    // dangerComponent: () => import('./sysTypeComponent/dangerComponent'),
    // energyComponent: () => import('./sysTypeComponent/energyComponent'),
    riskDangerComponent: () => import('./sysTypeComponent/riskDangerComponent'),
    airComponent: () => import('./sysTypeComponent/airComponent'),
    powerComponent: () => import('./sysTypeComponent/powerComponent'),
    deviceDialog: () =>
      import('./sysTypeComponent/components/deviceDetailDialog'),
    parkingLot: () => import('./parkingLot'),
    deviceInfoManagement: () => import('./deviceInfoManagement'),
    paramDetails: () => import('./components/components/paramDetails'),
    taskManage: () => import('./sysTypeComponent/taskManage'),
    chemicalsManage: () => import('./sysTypeComponent/chemicalsManage'),
    inspectionDetails: () => import('@/views/taskManageModule/components/inspectionDetails.vue')
  },
  data() {
    const NODE_ENV_FLAG = import.meta.env.DEV
    return {
      NODE_ENV_FLAG,
      WPFmessage: '',
      toWPFdata: '',
      parkingLotVisible: false,
      parkingLotData: {},
      spaceDataList: [], // 空间数据(科室，功能)
      spaceName: null, // 所在空间名称
      modelCode: '', // 楼层空间模型编码
      localtion: '', // 空间编码
      ssmType: null, // 空间等级
      activeTab: '1',
      btnType: 1,
      allTableComponentListShow: false,
      roomInfoManagementShow: false,
      assetsList: [],
      deviceDetailsListShow: false,
      deviceDetailInfo: {},
      deviceInfoManagementShow: false,
      deviceInfo: {},
      // table组件参数
      tableCompenentData: {
        title: '',
        type: ''
      },
      centerTableData: {}, // 中间表格组件参数
      // 房间组件参数
      roomInfo: {
        localtion: '',
        ssmType: '',
        ssmCodes: '',
        modelCode: '',
        title: '',
        areaData: ''
      },
      dialogTypeList: [
        {
          title: '服务工单台账',
          type: 'ioms',
          height: 'calc(100% - 230px)'
        },
        {
          title: '科室医废收集记录',
          type: 'imws',
          height: 'calc(100% - 200px)'
        },
        {
          title: '隐患清单',
          type: 'danger',
          height: 'calc(100% - 200px)'
        },
        {
          title: '空间清单',
          type: 'space',
          height: 'calc(100% - 120px)'
        }
      ],

      getchart: null,
      analysisBarShow: false,
      spaceData: {
        idleCount: 0,
        publicArea: 0,
        totalArea: 0,
        totalCount: 0,
        useArea: 0
      }, // 空间管理数据
      ipasPointDetailShow: false,
      detailId: '',
      hazardType: '',
      centerTableDialogTypeList: [
        {
          label: '工单',
          type: 'ioms',
          component: 'workOrderCenterList'
        },
        {
          label: '巡检',
          type: 'icis',
          component: 'ipasPointRecord'
        },
        {
          label: '医废',
          type: 'imws',
          component: 'collectRecord'
        },
        {
          label: '隐患',
          type: 'danger',
          component: 'hazardList'
        },
        {
          label: '风险',
          type: 'risk',
          component: 'hazardList'
        },
        {
          Dom: 'imes',
          name: '资产',
          component: 'imesComponent'
        }
      ],
      centerTableDialogShow: false, // 中间表格弹窗
      tagSsmCodes: '', // tag标签对应table的ssmCodes 与roomInfo中的ssmCodes一致 只是为了区分
      collapseFlag: true,
      sysTitle: '',
      allTypeList: [
        {
          Dom: 'space',
          name: '空间管理',
          component: 'floorSpaceComponent'
        },
        {
          Dom: 'ioms',
          name: '工单管理',
          component: 'iomsComponent'
        },
        {
          Dom: 'Airend',
          name: '空调末端',
          component: 'airComponent',
          EN_HOSPITAL_ENV: ['syzxyy', 'szdeyy', 'gzzyyy']
        },
        {
          Dom: 'Electricity',
          name: '医疗设备',
          component: 'powerComponent',
          EN_HOSPITAL_ENV: ['szdeyy', 'fjslyy']
        },
        {
          Dom: 'danger',
          name: '风险隐患',
          component: 'riskDangerComponent',
          EN_HOSPITAL_ENV: ['szdeyy', 'syzxyy', 'sinomis', 'fjslyy', 'szzlyy', 'gzzyyy']
        },
        {
          Dom: 'imes',
          name: '资产台账',
          component: 'imesComponent',
          EN_HOSPITAL_ENV: ['szdeyy', 'fjslyy', 'gzzyyy']
        },
        {
          Dom: 'energy',
          name: '能耗管理',
          component: 'energyManage',
          EN_HOSPITAL_ENV: ['syzxyy', 'szdeyy', 'bjsjtyy', 'gzzyyy']
        },
        {
          Dom: 'task',
          name: '作业管理',
          component: 'taskManage',
          EN_HOSPITAL_ENV: ['gzzyyy', 'fjslyy']
        },
        {
          Dom: 'chemicals',
          name: '危化品',
          component: 'chemicalsManage',
          EN_HOSPITAL_ENV: ['gzzyyy', 'fjslyy']
        }
        // {
        //   Dom: 'imws',
        //   name: '医废',
        //   component: 'imwsComponent'
        // },
        // {
        //   Dom: 'icis',
        //   name: '巡检',
        //   component: 'icisComponent'
        // },

        // {
        //   Dom: 'risk',
        //   name: '风险',
        //   component: 'riskComponent'
        // },
        // {
        //   Dom: 'danger',
        //   name: '隐患',
        //   component: 'dangerComponent'
        // }
        // {
        //   Dom: 'energy',
        //   name: '能耗',
        //   component: 'energyComponent'
        // },
        // {
        //   Dom: 'pipe',
        //   name: '管线',
        //   component: 'pipelineComponent'
        // }
      ],
      roomTypeList: [],
      activeTabIndex: '',
      isParamDetails: false,
      paramData: {},
      inspectionDetailsShow: false
    }
  },
  computed: {
    currentComponent() {
      return this.roomTypeList.find((e) => e.Dom === this.activeTabIndex)
        ?.component
    }
  },
  created() {
    this.roomTypeList = this.allTypeList.filter(
      (e) => !e.EN_HOSPITAL_ENV?.includes(__PATH.VUE_APP_HOSPITAL_NODE)
    )
  },
  mounted() {
    // 初始化获取modelCode
    if (Object.hasOwn(this.$route.query, 'modelCode')) {
      // spaceManage?modelCode=BJSJTYY01&ssmCodes=1574997196057620481,1574997196330250241&localtion=01&ssmType=2
      // this.getSurveyAssetByProjectCode({ssmCodes: this.$route.query.ssmCodes}).then(() => {
      let ssmCodeList = this.$route.query.ssmCodes.split(',')
      if (ssmCodeList.length && ssmCodeList[0] != '#') {
        ssmCodeList = ['#', ...ssmCodeList].toString()
      }
      Object.assign(this.roomInfo, {
        ...this.$route.query,
        ssmCodes: ssmCodeList,
        title: '院区'
      })
      this.activeTabIndex = this.roomInfo.tabName
      // 直接调用这个方法
      this.initRoomInfo()
      // })
    }
    try {
      window.chrome.webview.addEventListener('message', (event) => {
        this.WPFmessage = event.data
        const data = JSON.parse(event.data)
        console.log(data)
        if (data.type === 'init') {
          // 页面重载
          this.reload()
        } else if (data.type === 'area') {
          // 区域
          data.ssmType = data.ssmType ? Number(data.ssmType) : 3
          this.modelCode = data.modelCode || ''
          this.localtion = data.localtion || ''
          this.ssmType = data.ssmType || ''
          this.getSurveyAssetByProjectCode({ ssmCodes: data.ssmCodes }).then(
            () => {
              let ssmCodeList = data.ssmCodes.split(',')
              if (ssmCodeList.length && ssmCodeList[0] != '#') {
                ssmCodeList = ['#', ...ssmCodeList].toString()
              }
              this.roomInfo = {
                modelCode: data.modelCode,
                localtion: data.localtion,
                ssmType: data.ssmType,
                ssmCodes: ssmCodeList,
                title: data.title,
                areaData: data.areaData
              }
              if (!this.collapseFlag) {
                if (this.centerTableDialogShow) {
                  this.configCloseCenterTableDialog()
                }
                if (this.deviceInfoManagementShow) {
                  this.configCloseDeviceRightDialog(false)
                }
                if (this.parkingLotVisible) {
                  this.configCloseDeviceDialog()
                }
                // setTimeout(() => {
                //   this.collapseFlag = !this.collapseFlag
                // }, 200)
                // this.$nextTick(() => {
                //   this.$refs.collapseWidth.style.width = '24.579%'
                //   this.$refs.collapseWidth.style.padding = '0 25px 10px 35px'
                // })
              }
            }
          )
        } else if (data.type === 'tag') {
          // 标签
          this.localtion = data.localtion
          this.modelCode = data.modelCode
          this.ssmType = data.ssmType
          this.tagSsmCodes = data.ssmCodes
          this.centerTableData = {} // 重置中间表格组件参数
          // 判断是否是空间管理页面
          if (data.typeName === 'space') {
            const param = data.spaceState === '0' ? { spaceState: 0 } : {}
            this.allTableChange('space', param)
          } else {
            // 获取当前选中的标签对应列表
            this.showCenterTableDialog(data.typeName)
          }
        } else if (data.type === 'deviceInfo') {
          const deviceInfo = this.assetsList.find(
            (e) => e.modelCode === data.deviceId
          )
          this.openDevideDialog(deviceInfo, data.deviceId)
        } else if (data.type === 'deviceInfoNew') {
          this.openDevideDialog({
            assetId: data.deviceId,
            assetName: data.deviceName,
            modelCode: data.deviceId
          }, data.deviceId)
        } else if (data.type === 'parkingLot') {
          this.parkingLotData = {
            parkingLotName: data.parkingLotName // 停车场出入库类型 in 入场 out 出场
          }
          this.openParkingLotDialog()
        } else if (data.type === 'deviceHistory') {
          this.paramData = data.data
          this.isParamDetails = true
          try {
            window.chrome.webview.hostObjects.sync.bridge.ShowDialog(true)
          } catch (error) {}
        }
      })
    } catch (errpr) {}
    // 测试用调用方法
  },
  methods: {
    allTableChange(type, params = {}) {
      this.tableCompenentData = {
        title: '',
        type: ''
      }
      Object.assign(this.tableCompenentData, {
        ...this.dialogTypeList.find((e) => e.type === type),
        localtion: this.localtion,
        modelCode: this.modelCode,
        deptId: '',
        functionDictId: '',
        ...params
      })
      this.allTableComponentListShow = true
    },
    inspectionDetailsClose() {
      this.inspectionDetailsShow = false
      this.openRightDialog()
    },
    configCloseTableDialog() {
      this.allTableComponentListShow = false
    },
    // 获取所有设备列表
    getSurveyAssetByProjectCode(initParams = {}) {
      // 空调末端需要获取空间对应设备列表 并根据modelCode获取到对应的device信息 如果后续需要增加component类型，可根据projectName进行传参判断
      const paramList = {
        Airend: '空调监测',
        Electricity: '配电监测'
      }
      if (paramList[this.activeTabIndex]) {
        const params = {
          projectCode: monitorTypeList.find(
            (e) => e.projectName === paramList[this.activeTabIndex]
          ).projectCode,
          spaceId:
            initParams.ssmCodes?.split(',')?.at(-1) ||
            this.roomInfo.ssmCodes?.split(',')?.at(-1)
        }
        if (paramList[this.activeTabIndex] === '配电监测') {
          params.keyDevice = '0'
        }
        return getSurveyAssetByProjectCode(params)
          .then((res) => {
            if (res.data.code === '200') {
              this.assetsList = res.data.data
              const deviceIdArr = Array.from(
                this.assetsList,
                ({ assetId }) => assetId
              )
              const deviceIds = deviceIdArr.length
                ? deviceIdArr.toString()
                : ''
              return deviceIds
            } else {
              this.assetsList = []
              return ''
            }
          })
          .catch(() => {
            this.assetsList = []
            return ''
          })
      } else {
        this.assetsList = []
        return Promise.resolve()
      }
    },
    szzlyyChange(type) {
      if (type === 'parkingLot') {
        this.parkingLotData = {
          parkingLotName: 'out' // 停车场出入库类型 in 入场 out 出场
        }
        this.openParkingLotDialog()
        return
      }
      let paramsData = {}
      if (type === 'build') {
        paramsData = {
          title: '第一住院楼',
          modelCode: 'SINOMIS01004',
          localtion: '01004',
          ssmType: 4,
          ssmCodes:
            '#,1724753192383705090,1724753192417259521,1724753193365172225'
        }
      } else if (type === 'floor') {
        paramsData = {
          title: '第一住院楼 > 8F',
          modelCode: 'SINOMIS0100408',
          localtion: '0100408',
          ssmType: 5,
          ssmCodes:
            '#,1724753192383705090,1724753192417259521,1724753193365172225,1724753193415503869'
        }
      } else if (type === 'room') {
        paramsData = {
          title: '第一住院楼 > 12F - 卫生间',
          modelCode: 'SINOMIS0100412001',
          localtion: '0100412001',
          ssmType: 6,
          ssmCodes:
            '#,1724753192383705090,1724753192417259521,1724753193365172225,1724753193415503873,1724753250428678146'
        }
        // } else if (type === 'room') {
        //   paramsData = {
        //     title: '第一住院楼 > 12F > 阳台',
        //     modelCode: 'SINOMIS0100412177',
        //     localtion: '0100412177',
        //     ssmType: 6,
        //     ssmCodes: '#,1724753192383705090,1724753192417259521,1724753193365172225,1724753193415503873,1724753265414926338'
        //   }
      } else if (type === 'Airend') {
        // 空调末端
        paramsData = {
          title: '第一住院楼 > 12F',
          modelCode: 'SINOMIS0100412',
          localtion: '0100412',
          ssmType: 5,
          ssmCodes:
            '#,1724753192383705090,1724753192417259521,1724753193365172225,1724753193415503873'
        }
      } else if (type === 'Electricity') {
        // 重点设备
        // paramsData = {
        //   title: '院区',
        //   modelCode: 'SINOMIS01',
        //   localtion: '01',
        //   ssmType: 3,
        //   ssmCodes: '#,1724753192383705090,1724753192417259521'
        // }
        // 设备详情测试
        const deviceInfo = this.assetsList.find(
          (e) => e.modelCode === '0100503_2673252'
        )
        this.openDevideDialog(deviceInfo)
        return
      }
      // this.getSurveyAssetByProjectCode({ssmCodes: paramsData.ssmCodes}).then(() => {
      Object.assign(this.roomInfo, {
        ...paramsData
      })
      this.initRoomInfo()
      // })
    },
    bjsjtyyChange(type) {
      let paramsData = {}
      if (type === 'build') {
        paramsData = {
          title: '综合急诊急救楼',
          modelCode: 'BJSJTYY01001',
          localtion: '01001',
          ssmType: 4,
          ssmCodes:
            '#,1574997196057620481,1574997196330250241,1574997196833566721'
        }
      } else if (type === 'floor') {
        paramsData = {
          title: '综合急诊急救楼 > 6F',
          modelCode: 'BJSJTYY0100109',
          localtion: '0100109',
          ssmType: 5,
          ssmCodes:
            '#,1574997196057620481,1574997196330250241,1574997196833566721,1574997197731147778'
        }
      } else if (type === 'room') {
        paramsData = {
          title: '第一住院楼 > 12F - 卫生间',
          modelCode: 'SINOMIS0100412001',
          localtion: '0100412001',
          ssmType: 6,
          ssmCodes:
            '#,1724753192383705090,1724753192417259521,1724753193365172225,1724753193415503873,1724753250428678146'
        }
      }
      Object.assign(this.roomInfo, {
        ...paramsData
      })
      this.initRoomInfo()
    },
    // 打开停车场弹框
    openParkingLotDialog() {
      if (this.collapseFlag) {
        this.collapseFlag = !this.collapseFlag
        this.$nextTick(() => {
          this.$refs.collapseWidth.style.width = '0'
          this.$refs.collapseWidth.style.padding = '0'
        })
      }
      this.parkingLotVisible = true
    },
    // 关闭停车场弹框
    configCloseDeviceDialog() {
      this.parkingLotVisible = false
      this.openRightDialog()
    },
    // 关闭设备详情弹窗
    configCloseDeviceRightDialog(isnotice = true) {
      this.deviceInfoManagementShow = false
      this.openRightDialog()
      if (isnotice) {
        try {
          window.chrome.webview.hostObjects.sync.bridge.FloorRestoration(
            'close'
          )
        } catch (error) {}
      }
    },
    // 打开右侧弹窗
    openRightDialog() {
      this.collapseFlag = true
      this.$nextTick(() => {
        this.$refs.collapseWidth.style.width = '24.579%'
        this.$refs.collapseWidth.style.padding = '0 25px 10px 35px'
      })
    },
    // 打开中屏table弹窗
    showCenterTableDialog(type) {
      // this.localtion = '0100413010'
      // this.ssmType = '6'
      // this.tagSsmCodes = '#,1724753192417259521,1724753193365172225,1724753193415503874,1759505194158665730'
      if (this.collapseFlag) {
        this.collapseFlag = !this.collapseFlag
        this.$nextTick(() => {
          this.$refs.collapseWidth.style.width = '0'
          this.$refs.collapseWidth.style.padding = '0'
        })
      }
      const filterComponent = this.centerTableDialogTypeList.find(
        (e) => e.type === type
      )
      this.centerTableComponent = filterComponent.component
      this.centerTableDialogShow = true
      this.hazardType = filterComponent.type || ''
      this.$nextTick(() => {
        setTimeout(() => {
          // 巡检单独处理
          if (this.centerTableComponent === 'ipasPointRecord') {
            this.$refs[this.centerTableComponent].getPointDetailTableList()
          } else if (this.centerTableComponent === 'collectRecord') {
            this.$refs[
              this.centerTableComponent
            ].getDepartMedicalWasteTableList()
          } else if (this.centerTableComponent === 'hazardList') {
            this.$refs[this.centerTableComponent].getWorkOrderTableData({
              placeIds: this.localtion
            })
          } else if (this.centerTableComponent === 'workOrderCenterList') {
            this.$refs[this.centerTableComponent].getWorkOrderTableData()
          }
        }, 250)
      })
      try {
        window.chrome.webview.hostObjects.sync.bridge.ShowTable(
          this.centerTableDialogShow
        )
      } catch (error) {}
    },
    configCloseCenterTableDialog() {
      this.centerTableDialogShow = false
      try {
        window.chrome.webview.hostObjects.sync.bridge.ShowTable(
          this.centerTableDialogShow
        )
      } catch (error) {}
      this.openRightDialog()
    },
    retrospectCloseDialog() {
      this.ipasPointDetailShow = false
      // this.ipasPointRecordShow = false
      try {
        // window.chrome.webview.hostObjects.sync.bridge.ShowTable(false)
        window.chrome.webview.hostObjects.sync.bridge.ShowDialog(false)
      } catch (error) {}
    },
    goTo(path, query) {
      this.$router.push({
        path,
        query
      })
    },
    // 楼层 渲染 空间信息
    initRoomInfo() {
      let index = this.roomTypeList.findIndex(
        (e) => e.Dom === this.activeTabIndex
      )
      index = index === -1 ? 0 : index
      // this.sysTitle = this.roomInfo.title
      this.activeTypeEvent(this.roomTypeList[index].Dom, index)
    },
    activeTypeEvent(dom, index) {
      this.activeTabIndex = dom
      this.getSurveyAssetByProjectCode({ ssmCodes: this.roomInfo.ssmCodes })
      this.$nextTick(() => {
        var element = document.querySelector('.is-activeTab')
        element.scrollIntoView({ behavior: 'smooth' })
      })
      // 风险默认穿risk 进入具体页面后切换后重新调用接口
      try {
        window.chrome.webview.hostObjects.sync.bridge.ZHMenuBarSwitch(dom)
      } catch (error) {}
      // 是否进入管线组件给wpf传参
      // try {
      //   window.chrome.webview.hostObjects.sync.bridge.SetPipeLineVis(this.activeType === 'pipe')
      // } catch (error) {}
      // if (this.activeType !== 'space') {
      //   const JSONdata = JSON.stringify({
      //     type: 'function',
      //     selectIds: '',
      //     modelCode: this.roomInfo.modelCode
      //   })
      //   try {
      //     window.chrome.webview.hostObjects.sync.bridge.GetRoomCodeType(JSONdata)
      //   } catch (error) {}
      // }
      // 点击中间六个 动态让wpf切换标签
      // const name = this.roomTypeList.find((e) => e.Dom === this.activeType)?.name
      // const tagJsonData = JSON.stringify({
      //   tabName: name,
      //   modelCode: this.roomInfo.modelCode
      // })
      // try {
      //   window.chrome.webview.hostObjects.sync.bridge.SpaceTabSwitch(tagJsonData)
      // } catch (error) {}
      this.roomTypeList.forEach((item) => {
        if (item.Dom === dom) {
          $(`#${item.Dom}`).addClass('type_active')
        } else {
          $(`#${item.Dom}`).removeClass('type_active')
        }
      })
    },
    // 子组件传递事件
    roomEvent(data) {
      if (data.type === 'move') {
        const deviceInfo = {
          type: data.type,
          assetId: data.assetId,
          assetName: data.assetName,
          modelCode: data.modelCode,
          entityTypeId: data.entityTypeId
        }
        this.openDevideDialog(deviceInfo)
      } else if (data.type === 'insp') {
        console.log(11111111, data)
        const inspInfo = {
          type: data.type,
          assetId: data.assetId,
          assetName: data.assetName,
          modelCode: data.modelCode
        }
        this.openDevideDialog(inspInfo)
      }
      // if (data.type === 'base') {
      //   this.sysTitle = this.roomInfo.title + ' > ' + (data.roomName ?? '')
      // } else if (data.type === 'space') {
      //   this.$parent.toWPFdata = data.data
      // }
    },
    // 传输给wpf的事件
    setWPFParamsData(data) {
      if (['ioms', 'energy'].includes(data.type)) {
        this.centerTableData = {
          ...data.params
        }
        console.log(this.centerTableData)
        try {
          window.chrome.webview.hostObjects.sync.bridge.WorkOrderScreening(
            JSON.stringify(this.centerTableData)
          )
        } catch (err) {}
      }
    },
    //
    tabItemCommand(val) {
      this.activeTabIndex = val
      this.$nextTick(() => {
        var element = document.querySelector('.is-activeTab')
        element.scrollIntoView({ behavior: 'smooth' })
      })
      try {
        window.chrome.webview.hostObjects.sync.bridge.ZHMenuBarSwitch(val)
      } catch (error) {}
      this.roomTypeList.forEach((item) => {
        if (item.Dom === val) {
          $(`#${item.Dom}`).addClass('type_active')
        } else {
          $(`#${item.Dom}`).removeClass('type_active')
        }
      })
    },
    paramDetailsClose() {
      this.isParamDetails = false
      try {
        window.chrome.webview.hostObjects.sync.bridge.ShowDialog(false)
      } catch (error) {}
    },
    changeDeviceDetailDialogShow(flag) {
      this.deviceDetailsListShow = flag
      try {
        window.chrome.webview.hostObjects.sync.bridge.ShowDialog(flag)
        window.chrome.webview.hostObjects.sync.bridge.GetSwichFloorIsShow(flag)
      } catch (error) {}
    },
    openDevideDialog(deviceInfo) {
      // 关闭变配电监测设备详情弹窗
      // if (this.deviceDetailsListShow) {
      //   this.changeDeviceDetailDialogShow(false)
      // }
      // modelCode字段并且有值
      if (!deviceInfo || !Object.keys(deviceInfo).length || !deviceInfo.modelCode) {
        return
        // return this.$message({
        //   message: '未找到该设备信息',
        //   type: 'warning'
        // })
      }
      // 打开变配电设备详情弹窗
      if (this.activeTabIndex === 'Electricity') {
        Object.assign(this.deviceDetailInfo, {
          projectCode: monitorTypeList.find((e) => e.projectName === '配电监测')
            .projectCode,
          ...this.roomInfo,
          modelCode: deviceInfo.modelCode,
          surveyCode: deviceInfo.surveyCode
        })
        this.changeDeviceDetailDialogShow(true)
      } else {
        Object.assign(this.deviceInfo, {
          ...this.roomInfo,
          tabName: this.activeTabIndex,
          deviceId: deviceInfo.assetId,
          deviceName: deviceInfo.assetName
        })
        if (this.collapseFlag) {
          this.collapseFlag = !this.collapseFlag
          this.$nextTick(() => {
            this.$refs.collapseWidth.style.width = '0'
            this.$refs.collapseWidth.style.padding = '0'
          })
        }
        if (deviceInfo.type == 'insp') {
          this.inspectionDetailsShow = true
        } else {
          this.deviceInfoManagementShow = true
        }
        this.$nextTick(() => {
          // this.$refs.deviceInfoManagement.mergeData("basicAccount");
        })
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.content {
  width: 100%;
  height: 100%;
  position: relative;
  .center-center {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #4d5880;
    font-size: 16px;
  }
  .sino-button-sure {
    z-index: 1;
  }
  .sham-content {
    pointer-events: none;
  }
  .roomInfoManagement {
    width: 100%;
    position: absolute;
    top: 0;
    right: 0;
    height: 100%;
    .circle-btn {
      position: absolute;
      top: calc(2%);
      right: 0;
      width: 26px;
      height: 26px;
      cursor: pointer;
      margin: auto 0;
      margin-right: 10px;
      z-index: 2;
    }
    .circle-btn-top {
      background: url("~@/assets/images/center/btn-fold.png") no-repeat;
      background-size: 100% 100%;
    }
    .circle-btn-bottom {
      width: 44px;
      height: 44px;
      background: url("~@/assets/images/center/btn-unfold.png") no-repeat;
      background-size: 100% 100%;
    }
    .right-content {
      width: 24.573%;
      height: 100%;
      margin: 0 0 0 auto;
      background: url("~@/assets/images/qhdsys/bg-mask.png") no-repeat;
      background-size: 100% 100%;
      box-sizing: border-box;
      padding: 0 25px 10px 35px;
      position: relative;
      transition: width 0.3s linear;
      overflow: hidden;
      .bg-title {
        padding: 0;
        background: rgba(133, 145, 206, 0.15);
        overflow: hidden;
        color: #dceaff;
        font-family: HarmonyOS Sans SC-Regular, HarmonyOS Sans SC;
        // 不可被选中
        -webkit-user-select: none;
        user-select: none;
        display: flex;
        .bg-tab {
          display: flex;
          overflow: hidden;
          box-sizing: border-box;
          .tab-div {
            width: 88px;
            height: 40px;
            flex-shrink: 0;
            line-height: 40px;
            text-align: center;
            font-size: 16px;
            color: #a4acb9;
            background: url("@/assets/images/qhdsys/bg-tab.png") no-repeat;
          }
          .tab-div:hover {
            cursor: pointer;
          }
          .is-activeTab {
            color: #b0e3fa;
            background: url("@/assets/images/qhdsys/bg-tab-xz.png") no-repeat;
          }
        }
        ::v-deep .el-popover {
          width: fit-content;
          min-width: 0;
          background: #374b79;
        }
        .center-empty {
          flex: 1;
          background: url("@/assets/images/qhdsys/bg-tab.png") no-repeat;
          background-size: 100% 100%;
        }
        .icon-collapse {
          width: 48px;
          height: 40px;
          // flex-shrink: 0;
          display: flex;
          justify-content: center;
          align-items: center;
          background: url("@/assets/images/qhdsys/bg-tab.png") no-repeat;
          &:hover {
            cursor: pointer;
          }
          img {
            margin: auto;
          }
        }
      }
      .bg-content {
        position: relative;
        box-sizing: border-box;
        //   padding: 10px 2px 2px 2px;
        //   width: 100%;
        //   height: calc(100% - 40px);
        width: calc(100% + 0px);
        height: calc(100% - 40px);
        overflow-y: auto;
        overflow-x: hidden;
        /* scroll-view 不显示滚动条 */
      }
      .bg-content::-webkit-scrollbar {
        height: 0;
        width: 0;
      }
      .room-info-box {
        color: #fff;
        display: flex;
        flex-direction: column;
        .box-type {
          width: 100%;
          margin: auto;
          display: flex;
          flex-wrap: wrap;
          justify-content: center;
          span {
            display: inline-block;
            width: fit-content;
            height: 24px;
            padding: 0 5px;
            background-color: #24396d;
            text-align: center;
            line-height: 24px;
            color: #dceaff;
            font-size: 14px;
            font-family: HarmonyOS Sans SC-Regular, HarmonyOS Sans SC;
            cursor: pointer;
            margin: 5px 2px 0 2px;
          }
          .type_active {
            color: #ffe3a6;
            background: url("~@/assets/images/center/border-bg-select.png")
              no-repeat;
            background-size: 100% 100%;
          }
        }
        .sys-box {
          width: 100%;
          // height: calc(100% - 30px);
          height: 100%;
          // flex: 1;
          .sys-box-content {
            width: 100%;
            height: 100%;
            //   padding: 10px;
            box-sizing: border-box;
          }
          // height: calc(100% - 24px);
        }
      }
    }
  }
}
.el-popper {
  padding: 0;
  padding: 8px 8px 0;
  background: #374b79 !important;
  border: 0;
  .el-dropdown-menu__item {
    width: 78px;
    height: 24px;
    line-height: 24px;
    text-align: center;
    padding: 0;
    background: rgba(255, 255, 255, 0.1)
      linear-gradient(
        90deg,
        rgba(10, 132, 255, 0) 0%,
        rgba(10, 132, 255, 0) 100%
      );
    margin-bottom: 8px;
    font-size: 14px;
    font-family: HarmonyOS Sans SC-Regular, HarmonyOS Sans SC;
    font-weight: 400;
    color: #b0e3fa !important;
  }
  .isBjxl {
    background: url("@/assets/images/qhdsys/bj-xl.png") no-repeat;
  }
  .el-dropdown-menu__item:hover {
    color: #b0e3fa !important;
    background: rgba(255, 255, 255, 0.1)
      linear-gradient(
        90deg,
        rgba(10, 132, 255, 0) 0%,
        rgba(10, 132, 255, 0) 100%
      ) !important;
  }
}
.el-popper.dropdown {
  .el-dropdown-menu__item {
    font-size: 16px;
  }
}
</style>
