<!--
 * @Description:
-->
<template>
  <!-- 危险点详情 -->
  <div class="riskPointDetails content">
    <div class="title" @click="$router.go(-1)"><i class="el-icon-arrow-left"></i>返回</div>
    <div class="form-content">
      <div class="form-title">巡检详情</div>
      <patrol-inspection-details-list :dataInfo="$route.query" />
    </div>
  </div>
</template>

<script>
import patrolInspectionDetailsList from './components/patrolInspectionDetailsList'
export default {
  name: 'patrolInspectionDetails',
  components: {
    patrolInspectionDetailsList
  },
  data() {
    return {

    }
  },
  methods: {

  }
}
</script>

<style lang="scss" scoped>
.content {
  position: relative;
  // background-color: #031553;
  background: url('~@/assets/images/qhdsys/bj.png') no-repeat;
  box-sizing: border-box;
  width: 100%;
  height: 100%;
  padding: 1.25rem 0.625rem 0.625rem 0.625rem;
}
.title {
  cursor: pointer;
  position: absolute;
  top: 1.6rem;
  left: 0;
  width: 80px;
  height: 30px;
  line-height: 30px;
  text-align: center;
  color: #618ad3;
  font-size: 0.875rem;
  white-space: nowrap;
  background-image: url('../../assets/images/peace/btn-back.png');
  background-size: 100% 100%;
  background-repeat: no-repeat;
}
.form-content {
  width: 60%;
  height: 100%;
  margin-left: 20%;
  .form-title {
    color: #dceaff;
    text-align: center;
    width: 200px;
    height: 30px;
    line-height: 30px;
    margin: 0 auto;
    background-image: url('../../assets/images/war/title-bg.png');
    background-size: 100% 100%;
    background-repeat: no-repeat;
    font-family: 'TRENDS';
  }
}
</style>
