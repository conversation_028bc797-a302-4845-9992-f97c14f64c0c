<template>
  <div class="assets-details-view">
    <div class="details-header">
      <div class="header-left-box">
        <div class="img">
          <img src="@/assets/images/cs.png" alt="" />
        </div>
        <div class="img-info">
          <div class="title-box">
            <div>{{ assetsInfo.assetsName }}</div>
            <div class="div">
              <img
                v-if="assetsInfo.isInsurance == '是'"
                src="@/assets/images/underInsurance.png"
                alt=""
              />
              <img
                v-if="assetsInfo.isInsurance == '否'"
                src="@/assets/images/uninsured.png"
                alt=""
              />
              <span>{{
                assetsInfo.isInsurance == "否" ? "未保" : "在保"
              }}</span>
            </div>
            <div class="time">{{ assetsInfo.guaranteedTime || "---" }}</div>
          </div>
          <div class="title-code">
            <span>资产编码：</span>
            <span>{{ assetsInfo.assetsCode }}</span>
          </div>
        </div>
      </div>
      <div class="header-right-box">
        <div class="img-div">
          <img src="@/assets/images/cs.png" alt="" />
        </div>
      </div>
    </div>
    <div class="details-content">
      <div class="dts-nav-box">
        <div
          class="nav-box"
          :class="{ active: index == navIdx }"
          v-for="(item, index) in navOptions"
          :key="index"
          @click="handleNavIdx(index)"
        >
          <div class="nav-div">{{ item.label }}</div>
        </div>
      </div>
      <div class="content">
        <el-form
          :model="basicInfoForm"
          class="basic-info-form"
          inline
          label-width="150px"
          ref="basicInfoForm"
        >
          <el-collapse v-model="activeNames" @change="handleChange">
            <el-collapse-item title="基础信息" name="1">
              <div class="item-div">
                <el-row>
                  <el-col
                    :span="item.lgWidth"
                    v-for="(item, index) in basiceInfo"
                    :key="index"
                  >
                    <el-form-item :label="`${item.label}：`">
                      <span>{{ basicInfoForm[item.prop] | basicFilters }}</span>
                    </el-form-item>
                  </el-col>
                </el-row>
              </div>
            </el-collapse-item>
            <el-collapse-item title="使用信息" name="2">
              <div class="item-div">
                <el-row>
                  <el-col
                    :span="item.lgWidth"
                    v-for="(item, index) in useInfo"
                    :key="index"
                  >
                    <el-form-item :label="`${item.label}：`">
                      <span>{{ basicInfoForm[item.prop] | basicFilters }}</span>
                    </el-form-item>
                  </el-col>
                </el-row>
              </div>
            </el-collapse-item>
            <el-collapse-item title="类别信息" name="3">
              <div class="item-div">
                <el-row>
                  <el-col
                    :span="item.lgWidth"
                    v-for="(item, index) in categoryInformationFormItemList"
                    :key="index"
                  >
                    <el-form-item :label="`${item.label}：`">
                      <span>{{ basicInfoForm[item.prop] | basicFilters }}</span>
                    </el-form-item>
                  </el-col>
                </el-row>
              </div>
            </el-collapse-item>
            <el-collapse-item title="折旧信息" name="4">
              <div class="item-div">
                <el-row>
                  <el-col
                    :span="item.lgWidth"
                    v-for="(item, index) in depreciationInformationFormItemList"
                    :key="index"
                  >
                    <el-form-item :label="`${item.label}：`">
                      <span>{{ basicInfoForm[item.prop] | basicFilters }}</span>
                    </el-form-item>
                  </el-col>
                </el-row>
              </div>
            </el-collapse-item>
          </el-collapse>
        </el-form>
      </div>
    </div>
  </div>
</template>
<script>
import {
  basiceInfo,
  useInfo,
  categoryInformationFormItemList,
  depreciationInformationFormItemList,
} from "../json/details";
import {assetDetailData} from "../json/mockJson"
export default {
  data() {
    return {
      basiceInfo,
      useInfo,
      categoryInformationFormItemList,
      depreciationInformationFormItemList,
      activeNames: ["1", "2", "3", "4"],
      assetsInfo: assetDetailData,
      navOptions: [
        { label: "基础信息", value: "#" },
        { label: "附属设备", value: "list" },
        { label: "合同发票", value: "list" },
        { label: "验收记录", value: "list" },
        { label: "资料文件", value: "list" },
        { label: "质控记录", value: "list" },
        { label: "使用记录", value: "list" },
        { label: "效益分析", value: "list" },
      ],
      navIdx: 0,
      basicInfoForm: assetDetailData,
    };
  },
  filters: {
    basicFilters(val) {
      return val ? val : "---";
    },
  },
  methods: {
    handleNavIdx(idx) {
      this.navIdx = idx;
    },
    handleChange(){}
  },
};
</script>
<style lang="scss" scoped>
.assets-details-view {
  height: 100%;
  padding: 0px 40px;
  .details-header {
    height: 9.375rem;
    background-color: rgba(53, 98, 219, 0.06);
    display: flex;
    align-items: center;
    justify-content: space-between;

    .header-left-box {
      padding: 1rem;
      box-sizing: border-box;
      height: 100%;
      display: flex;
      align-items: center;
      .img {
        width: 9.75rem;
        height: 100%;
        margin-right: 1rem;
        img {
          width: 100%;
          height: 100%;
        }
      }
      .title-box {
        display: flex;
        align-items: center;
        font-size: 1rem;
        margin-bottom: 1.3rem;
        .div {
          margin: 0px 0.3rem;
          padding: 0.3rem;
          display: flex;
          align-items: center;
          background: rgba(97, 226, 157, 0.2);
          color: #61e29d;
          border-radius: 4px;
          img {
            margin-right: 0.3rem;
          }
        }
      }
    }
    .header-right-box {
      height: 100%;
      padding: 1rem 5rem 1rem 1rem;
      background: url(@/assets/images/inUse.png) no-repeat right top;
      display: flex;
      align-items: center;
      .img-div {
        width: 6.5rem;
        height: 6.5rem;
        img {
          width: 100%;
          height: 100%;
        }
      }
    }
  }
  .details-content {
    height: calc(100% - 9.375rem);
    box-sizing: border-box;
    padding: 1.5rem 0rem 1.5rem;
    display: flex;
    flex-direction: column;
    .dts-nav-box {
      margin-bottom: 1.5rem;
      display: flex;
      align-items: center;
      box-sizing: border-box;
      .nav-box {
        height: 2.5rem;
        line-height: 2.5rem;
        width: 10.81rem;
        font-size: 1rem;
        text-align: center;
        color: #a4afc1;
        cursor: pointer;
        box-sizing: border-box;
        border: 1px dashed #26314f;
        border-right: none;
        background: url(@/assets/images/nav-bg.png) no-repeat;
        background-size: cover;
      }
      .nav-box:last-child {
        border-right: 1px dashed #26314f;
      }
      .active {
        color: #b0e3fa;
        background: url(@/assets/images/nav-bg-xz.png) no-repeat;
        background-size: cover;
      }
    }
    .content {
      flex: 1;
      overflow-y: auto;
    }
  }
}
::v-deep .el-collapse,
::v-deep .el-collapse-item__wrap,
::v-deep .el-collapse-item__header {
  background: transparent;
  border: none;
}
::v-deep .el-collapse-item__header {
  color: #fff;
  font-size: 1rem;
}
::v-deep .el-form-item__label {
  color: #b0e3fa;
}
::v-deep .el-form-item__content {
  color: #ffffff;
  font-size: 0.875rem;
}
</style>