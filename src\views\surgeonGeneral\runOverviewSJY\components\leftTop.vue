<template>
  <div class="left-content-item">
    <CardTitle title="收入分析" :list="dateList" position="left" @active="dateClick"/>
    <div class="card-content">
      <div v-loading="topLoading" class="card-content-top">
        <div
          v-for="(item, index) in statisticsList"
          :key="index"
          class="card-content-top-item"
        >
          {{ item.title }}
          <span class="num">{{ item.value }}</span>
          {{ item.unit }}
        </div>
      </div>
      <div class="card-content-tab">
        <div
          v-for="(item, index) in tabList"
          :key="index"
          class="title-tab-item"
          :class="{ active: item.value == tab }"
          @click="tabClick(item.value)"
        >
          <div class="tabItem">
            {{ item.name }}
          </div>
        </div>
      </div>
      <div v-loading="listLoading" class="card-content-list">
        <ContentList
          :list="incomeList"
          :option="{name: 'deptAlias', value: 'costs', unit: '万元'}"
          :scroll-speed="0.2"
          @scroll-to-bottom="handleScrollComplete"
        />
      </div>
    </div>
  </div>
</template>
<script>
import CardTitle from './title'
import ContentList from './contentList'
import { revenueAnalysis, deptcost, incomeRanking } from '@/utils/runOverviewSJY'
import { formatThousand, getDefaultDateRange } from './utils'
export default {
  components: {
    CardTitle,
    ContentList
  },
  data() {
    return {
      topLoading: false,
      listLoading: false,
      params: {
        visitDateStart: '',
        visitDateEnd: ''
      },
      dateType: 0,
      tab: 1,
      statisticsList: [
        { title: '总收入', key: 'totalRevenue', value: '0', unit: '万元' },
        { title: '次均费用', key: 'averageCosts', value: '0', unit: '万元' },
        { title: '门急诊量', key: 'outpatient', value: '0', unit: '人' },
        { title: '椅位日均接诊人次', key: 'chairAverageNum', value: '0', unit: '人' }
      ],
      tabList: [
        { value: 1, name: '收入科室排行' },
        { value: 2, name: '科室次均费用排行' }
      ],
      dateList: [
        { value: 0, name: '日', date: [] },
        { value: 1, name: '月', date: [] },
        { value: 2, name: '年', date: [] }
      ],

      incomeList: [
        {deptAlias: '111', costs: 1},
        {deptAlias: '111', costs: 1},
        {deptAlias: '111', costs: 1},
        {deptAlias: '111', costs: 1},
        {deptAlias: '111', costs: 1},
        {deptAlias: '111', costs: 1},
        {deptAlias: '111', costs: 1},
        {deptAlias: '111', costs: 1},
        {deptAlias: '111', costs: 1},
        {deptAlias: '111', costs: 1},
        {deptAlias: '111', costs: 1},
        {deptAlias: '111', costs: 1},
        {deptAlias: '111', costs: 1},
        {deptAlias: '111', costs: 1},
        {deptAlias: '111', costs: 1},
        {deptAlias: '111', costs: 1},
        {deptAlias: '111', costs: 1},
        {deptAlias: '111', costs: 1},
        {deptAlias: '111', costs: 1}
      ]
    }
  },
  created () {
    this.dateList = this.dateList.map(item => ({
      ...item,
      date: getDefaultDateRange(item.value)
    }))
    this.params = {
      visitDateStart: this.dateList[this.dateType].date[0],
      visitDateEnd: this.dateList[this.dateType].date[1]
    }
    this.$emit('dateChange', this.params, this.dateType)
  },
  mounted () {

    this.getRevenueAnalysisData()
    this.listLoading = true
    this.getIncomeRankingList()
  },
  methods: {
    handleScrollComplete() {

    },
    dateClick (val) {
      this.dateType = val
      this.params = {
        visitDateStart: this.dateList[this.dateType].date[0],
        visitDateEnd: this.dateList[this.dateType].date[1]
      }
      this.$emit('dateChange', this.params, this.dateType)
      this.getRevenueAnalysisData()
      this.tabClick(this.tab)
    },
    tabClick (value) {
      if (this.listLoading) return
      this.tab = value
      this.listLoading = true
      if (this.tab === 1) {
        this.getIncomeRankingList()
      } else if (this.tab === 2) {
        this.getDeptcostList()
      }
    },
    getRevenueAnalysisData () {
      this.topLoading = true
      revenueAnalysis(this.params).then(res => {
        if (res.data.code == 200) {
          if (res.data.data) {
            this.statisticsList.forEach(item => {
              let value = res.data.data[item.key] || '0'
              item.value = formatThousand(value)
            })
          }
        }
      }).catch(() => {
        this.$message.error('获取收入分析数据失败')
      }).finally(() => {
        this.topLoading = false
      })
    },
    getIncomeRankingList() {
      incomeRanking(this.params).then(res => {
        if (res.data.code == 200) {
          this.incomeList = res.data.data
        }
      }).catch(() => {
        this.$message.error('获取收入科室排行失败')
      }).finally(() => {
        this.listLoading = false
      })

    },
    getDeptcostList () {
      deptcost(this.params).then(res => {
        if (res.data.code == 200) {
          this.incomeList = res.data.data.map(el => {
            return {
              deptAlias: el.deptAlias,
              costs: el.cost
            }
          })
        }
      }).catch(() => {
        this.$message.error('获取科室次均费用排行失败')
      }).finally(() => {
        this.listLoading = false
      })
    }
  }
}
</script>
<style lang="scss" scoped>
.left-content-item {
  width: 100%;
  height: calc(100% / 3 - 7px);
  background: url("~@/assets/images/qhdsys/new-card-bg.png") no-repeat;
  background-size: 100% 100%;
  margin-bottom: 10px;
  .card-content {
    height: calc(100% - 36px);
    width: 100%;
    padding: 16px;
    .card-content-top {
      display: flex;
      flex-wrap: wrap;
      width: 100%;
      height: 84px;
      &-item {
        width: calc(50% - 5px);
        height: calc(50% - 5px);
        background: rgba(255, 255, 255, 0.05);
        display: flex;
        align-items: center;
        justify-content: center;
        color: #fff;
        margin-left: 10px;
        margin-bottom: 10px;
        &:nth-child(odd) {
          margin-left: 0;
        }
        .num {
          color: aqua;
          font-size: 18px;
          font-weight: bold;
          margin: 0 5px;
        }
      }
    }
    .card-content-tab {
      display: flex;
      justify-content: center;
      width: 100%;
      height: 32px;
      font-size: 14px;
      margin-top: 5px;
      border-bottom: 1px solid;
      border-image: linear-gradient(
          270deg,
          rgba(0, 78, 194, 1),
          rgba(106, 236, 250, 1),
          rgba(4, 107, 228, 1)
        )
        1 1;
      .title-tab-item {
        position: relative;
        width: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        height: 100%;
        line-height: 28px;
        color: #fff;
        text-align: center;
        font-size: 15px;
        .tabItem {
          padding-bottom: 5px;
        }
        &.active {
          font-weight: bold;
          .tabItem {
            height: 100%;
            color: #ffdc83;
            border-bottom: 2px solid #ffdc83;
            padding-bottom: 5px;
            &::after {
              content: "";
              position: absolute;
              bottom: 0px; // 略低于 border，避免重叠
              left: 50%;
              transform: translateX(-50%);
              width: 0;
              height: 0;
              border-left: 4px solid transparent;
              border-right: 4px solid transparent;
              border-bottom: 4px solid #ffdc83; // 向上的三角
            }
          }
        }
      }
    }
    .card-content-list {
      width: 100%;
      margin-top: 10px;
      height: calc(100% - 28px - 84px - 20px);
    }
  }
}
</style>
