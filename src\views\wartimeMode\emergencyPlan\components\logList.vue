<template>
  <div class="logList">
    <div class="list-item" v-for="item in listData" :key="item.id">
      <p class="item-start">
        <span>操作时间：</span>
        <span>{{ item.startTime }}</span>
      </p>
      <p v-if="item.endTime" class="item-start">
        <span>结束时间：</span>
        <span>{{ item.endTime }}</span>
      </p>
      <p class="item-value">
        <span>{{ item.operationName}}：</span>
        <span v-if="['0', '1'].includes(item.status) && statusMap[item.operationName]" :style="{color: item.status == 1 ? '#61E29D' : '#FA8C2B'}" class="value-status">
          <i :class="item.status == 1 ? 'el-icon-success' : 'el-icon-warning'"></i>
          {{getStatus(item.status, item.operationName)}}
        </span>
        <span>{{ item.name }}</span>
      </p>
    </div>
  </div>
</template>

<script>
export default {
  name: 'logList',
  props: {
    listData: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      statusMap: {
        短信通知: { 0: '发送失败', 1: '已送达' },
        电话通知: { 0: '未接听', 1: '接听' }
      }
    }
  },
  computed: {
    getStatus() {
      return (status, type) => {
        return this.statusMap[type][status]
      }
    }
  },
  created() {

  },
  methods: {

  }
}

</script>

<style lang="scss" scoped>
.logList {
  width: 100%;
  .list-item {
    padding: 2px 12px 12px 12px;
    background: rgba(255, 255, 255, 0.05);
    margin-top: 16px;
    margin-left: 34px;
    position: relative;
    .item-start {
      margin-top: 10px;
      font-size: 12px;
      span:first-child {
        font-weight: 400;
        color: #B0E3FA;
      }
      span:last-child {
        color: #C2C4C8;
        font-weight: 500;
      }
    }
    .item-value {
      line-height: 32px;
      margin-top: 12px;
      font-size: 14px;
      span:first-child {
        font-weight: 400;
        color: #8BDDF5;
      }
      span:last-child {
        color: #FFFFFF;
        font-weight: 400;
      }
      .value-status {
        padding: 4px 8px;
        background: rgba(232,255,234,0.2);
        border-radius: 4px;
        font-weight: 400;
        font-size: 14px;
        margin-right: 16px;
        i {
          font-size: 13px;
        }
      }
    }
    &::before {
      content: '';
      display: inline-block;
      width: 12px;
      height: 12px;
      background: #0C1E45;
      border: 2px solid #8BDDF5;
      border-radius: 100%;
      position: absolute;
      left: -28px;
      top: 4px;
      z-index: 1;
    }
    &::after {
      content: '';
      display: inline-block;
      height: calc(100% - 16px);
      width: 2px;
      background: rgba(255, 255, 255, 0.1);
      position: absolute;
      left: -23px;
      bottom: 0;
    }
  }
  .list-item:first-child {
    margin-top: 0;
    &::before {
      background: #8BDDF5;
    }
  }
}
</style>
