<template>
  <div class='component EmergencySummaryReview'>

    <div class="EmergencySummaryReview__files">
      <ul class="description__content">
        <li style="margin-top: 0;" class="full-row">
          <div>总结文件</div>
          <div>
            <FileListCard :file-list="fileList" />
          </div>
        </li>
      </ul>
    </div>

    <div class="EmergencySummaryReview__content">
      <ul class="description__content">
        <li style="margin-top: 0;" class="full-row">
          <div>总结说明</div>
          <div :title="summaryInfo.summaryAnalysis | defaultVal">{{ summaryInfo.summaryAnalysis | defaultVal }}</div>
        </li>
        <li class="full-row">
          <div>院内参与人员</div>
          <div :title="summaryInfo.hospitalPerson | defaultVal">{{ summaryInfo.hospitalPerson | defaultVal }}</div>
        </li>
        <li class="full-row">
          <div>其他参与人员</div>
          <div :title="summaryInfo.otherPerson | defaultVal">{{ summaryInfo.otherPerson | defaultVal }}</div>
        </li>
        <li class="full-row">
          <div>研判处理人</div>
          <div>{{ summaryInfo.createName | defaultVal }}</div>
        </li>
      </ul>
    </div>
  </div>
</template>


<script>
import FileListCard from "./FileListCard.vue";

export default {
  name: 'EmergencySummaryReview',
  components: {
    FileListCard
  },
  data() {
    return {
      showQrCode: false,
    }
  },
  computed: {
    summaryInfo: function () {
      return this.$parent.detail?.summaryInfo ?? {}
    },
    fileList() {
      let result = []
      if (this.summaryInfo.fileUrl) {
        try {
          result = JSON.parse(this.summaryInfo.fileUrl);
        } catch (error) {

        }
      }
      return result;
    }
  },
  filters: {
    defaultVal: function (val) {
      return val || "-"
    },
  },
}


</script>

<style lang='scss' scoped>
.EmergencySummaryReview {
  display: flex;
  flex-flow: column nowrap;
  height: 100%;
  overflow: hidden;

  &__files {
    background: rgba(133, 145, 206, 0.15);
    padding: 16px 0 16px 16px;
    flex: 1;
    overflow: hidden;

    .description__content {
      height: 100%;
      overflow: hidden;

      >li.full-row {
        height: 100%;
        overflow: hidden;

        >div:last-child {
          overflow: auto;
        }
      }
    }
  }

  &__content {
    background: rgba(133, 145, 206, 0.15);
    margin-top: 16px;
    padding: 16px;
  }


  .description {
    &__content {
      display: flex;
      flex-flow: row wrap;

      >li {
        flex-basis: 50%;
        display: flex;
        flex-flow: row nowrap;
        margin-top: 24px;

        &.full-row {
          flex-basis: 100%;
        }

        >div:first-of-type {
          color: #B0E3FA;
          width: 100px;
          text-align: right;

          &::after {
            content: '：';
          }

        }

        >div:last-child {
          width: 0;
          flex: 1;
          overflow: hidden;
          white-space: nowrap;
          text-overflow: ellipsis;
        }
      }
    }

    &__imgs {
      display: flex;
      flex-flow: row wrap;
    }

    &__img {
      height: 100px;
      width: 100px;
      overflow: hidden;
      margin: 0 10px 10px 0;

      >img {
        height: 100%;
        width: 100%;
        object-fit: cover;
        object-position: center;
      }
    }
  }
}
</style>
