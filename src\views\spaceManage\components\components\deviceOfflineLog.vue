<template>
  <div class="deviceOfflineLog">
    <div class="search-box">
      <el-select v-model="queryParam.harvesterIds" placeholder="请选择传感器" filterable multiple clearable popper-class="new-select">
        <el-option v-for="item in harvesterList" :key="item.harvesterId" :label="item.harvesterName" :value="item.harvesterId"> </el-option>
      </el-select>
      <el-select v-model="queryParam.parameterExceptions" placeholder="请选择状态" filterable clearable popper-class="new-select">
        <el-option v-for="item in statusList" :key="item.value" :label="item.label" :value="item.value"> </el-option>
      </el-select>
      <el-date-picker
        v-model="dateRange"
        type="daterange"
        unlink-panels
        popper-class="date-style"
        range-separator="至"
        start-placeholder="开始日期"
        end-placeholder="结束日期"
        value-format="yyyy-MM-dd"
        @change="changeDataEvent"
      >
      </el-date-picker>
      <div class="date-type">
        <p v-for="item in dateTypeList" :key="item.value" class="type-item" :class="{'active-type': dateType == item.value}" @click="activeTabEvent(item.value)">{{ item.name }}</p>
      </div>
    </div>
    <div style="flex: 1;">
      <el-table
        v-loading="tableLoading"
        :data="tableData"
        height="calc(100%)"
        :cell-style="$tools.setCell(3)"
        :header-cell-style="$tools.setHeaderCell(3)"
        style="width: 100%"
        element-loading-background="rgba(0, 0, 0, 0.2)"
      >
        <el-table-column prop="harvesterName" label="传感器名称" show-overflow-tooltip></el-table-column>
        <el-table-column prop="time" label="时间"></el-table-column>
        <el-table-column prop="parameterException" label="在线状态">
          <div slot-scope="scope">
            {{ scope.row.parameterException == 2 ? '离线' : '在线' }}
          </div>
        </el-table-column>
      </el-table>
    </div>
  </div>
</template>

<script>
import { GetParamDataHistoryList, GetSelectBySurveyCode } from '@/utils/centerScreenApi'
import moment from 'moment'
moment.locale('zh-cn')
export default {
  name: 'deviceOfflineLog',
  props: {
    deviceData: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      tableLoading: false,
      tableData: [],
      dateRange: [],
      queryParam: {
        harvesterIds: [],
        parameterExceptions: ''
      },
      dateType: 1,
      dateTypeList: [
        { name: '今天', value: 1 },
        { name: '近7天', value: 2 },
        { name: '近30天', value: 3 }
      ],
      harvesterList: [], // 传感器；列表
      statusList: [
        {label: '在线', value: '0,1'},
        {label: '离线', value: '2'}
      ] // 状态列表
    }
  },
  watch: {
    'queryParam.harvesterIds'(val) {
      this.getParamDataHistoryList()
    },
    'queryParam.parameterExceptions'(val) {
      this.getParamDataHistoryList()
    }
  },
  created() {
    this.getSelectBySurveyCode()
    this.activeTabEvent(1)
  },
  methods: {
    getSelectBySurveyCode() {
      let params = {
        surveyCode: this.deviceData.surveyCode,
        harvesterId: ''
      }
      GetSelectBySurveyCode(params).then(res => {
        if (res.data.code == 200) {
          this.harvesterList = res.data.data.harvesterList
        }
      })
    },
    activeTabEvent(val) {
      const dateList = {
        1: [moment().startOf('day').format('YYYY-MM-DD'), moment().format('YYYY-MM-DD')],
        2: [moment().subtract(6, 'days').format('YYYY-MM-DD'), moment().format('YYYY-MM-DD')],
        3: [moment().subtract(29, 'days').format('YYYY-MM-DD'), moment().format('YYYY-MM-DD')]
      }
      this.dateType = val
      this.dateRange = dateList[val]
      this.getParamDataHistoryList()
    },
    changeDataEvent() {
      this.getParamDataHistoryList()
    },
    getParamDataHistoryList() {
      const param = {
        projectCode: this.deviceData.projectCode,
        surveyCode: this.deviceData.surveyCode,
        startTime: this.dateRange[0],
        endTime: this.dateRange[1],
        harvesterIds: this.queryParam.harvesterIds,
        parameterExceptions: this.queryParam.parameterExceptions ? this.queryParam.parameterExceptions.split(',') : []
      }
      this.tableLoading = true
      GetParamDataHistoryList(param).then(res => {
        this.tableLoading = false
        if (res.data.code == 200) {
          this.tableData = res.data.data
        }
      })
    }
  }
}

</script>

<style lang="scss" scoped>
.deviceOfflineLog {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  .search-box {
    padding: 8px 0px 24px 0px;
    display: flex;
    align-items: center;
    .date-type {
      margin-left: 15px;
      display: flex;
      .type-item {
        cursor: pointer;
        padding: 8px 12px;
        font-weight: 400;
        font-size: 14px;
        color: #B0E3FA;
        margin-right: 8px;
        border: 1px solid transparent;
        background: linear-gradient( 90deg, rgba(10,132,255,0) 0%, rgba(10,132,255,0) 100%), rgba(255,255,255,0.1);
      }
      .active-type {
        color: #8BDDF5;
        background: linear-gradient( 90deg, rgba(10,132,255,0.4) 0%, rgba(10,132,255,0) 100%), rgba(255,255,255,0.1);
        border: 1px solid;
        border-image: radial-gradient(circle, rgba(171, 240, 255, 1), rgba(226, 254, 255, 1), rgba(132, 196, 203, 1), rgba(48, 151, 166, 0)) 1 1;
      }
    }
    ::v-deep(.el-select) {
      margin-right: 16px;
      .el-input {
        .el-input__inner {
          height: 35px;
          border: 1px solid rgba(133, 145, 206, 0.5);
          border-radius: 4px;
        }
        .el-input__icon {
          line-height: 35px;
        }
      }
    }
  }
  ::v-deep(.el-date-editor) {
    width: 300px;
    height: 35px;
    background-color: #14233e;
    border-color: rgba(133, 145, 206, 0.5);
    .el-input__icon {
      transform: translateY(-2px);
    }
    .el-range-input {
      background-color: #14233e;
      color: rgba(255, 255, 255, 0.8);
    }
    .el-range-separator {
      color: rgba(255, 255, 255, 0.8);
      transform: translateY(-2px);
    }
  }

}
</style>
