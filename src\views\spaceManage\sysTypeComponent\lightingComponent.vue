<template>
  <div class="moveComponent">
    <div class="module-container" style="height: calc(32%)">
      <div class="module-header">
        <div class="title-left">
          <p class="title-left-text">回路台账</p>
        </div>
        <div class="title-right">
          <div class="icon-box">
            <img src="../../../assets/images/order_more.png" class="order-more" @click="allTableChange('light', '1')" />
          </div>
        </div>
      </div>
      <div class="module-content" style="height: calc(100% - 44px)">
        <div class="top_Statistics">
          <div id="loopStatisticsEcharts"></div>
          <div class="bottom_loop_statistics">
            <div v-for="(item, index) in statisticsData" :key="index" class="itemStatistics">
              <p>{{ item.label }}</p>
              <p :style="{ color: item.color }">{{ lightInfo[item.key] || 0 }}</p>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="module-container" style="height: calc(34%)">
      <div class="module-header">
        <div class="title-left">
          <p class="title-left-text">回路状态</p>
        </div>
        <div class="title-right">
          <div class="toggle">
            <div v-if="roomData.ssmType < 4 && roomData.isSpace == '0'" :class="sortType == 'jz' ? 'active-type' : ''"
              @click="changeSortType('jz')">建筑</div>
            <div v-if="roomData.ssmType == 4 && roomData.isSpace == '0'" :class="sortType == 'lc' ? 'active-type' : ''"
              @click="changeSortType('lc')">楼层</div>
            <div :class="sortType == 'mk' ? 'active-type' : ''" @click="changeSortType('mk')">模块</div>
            <div :class="sortType == 'fwkj' ? 'active-type' : ''" @click="changeSortType('fwkj')">服务空间</div>
          </div>
        </div>
      </div>
      <div class="module-content" style="height: calc(100% - 48px)">
        <div id="EChart" style="height: 100%"></div>
      </div>
    </div>
    <div class="module-container" style="height: calc(34%)">
      <div class="module-header">
        <div class="title-left">
          <!-- <p class="title-left-icon"></p> -->
          <p class="title-left-text">回路列表</p>
        </div>
        <div class="title-right">
          <div class="operation" v-if="roomData.ssmType == '5' && roomData.isSpace == '0'">
            <span @click="selectConfigRowData('', '1')">全开</span>
            <span @click="selectConfigRowData('', '0')">全关</span>
          </div>
          <div class="icon-box">
            <img src="../../../assets/images/order_more.png" class="order-more" @click="allTableChange('light', '2')" />
          </div>
        </div>
      </div>
      <div class="module-content" style="height: calc(100% - 20px)">
        <el-table v-el-table-infinite-scroll="tableLoadMore" class="table-center-transfer" :data="tableData"
          height="100%" :key="sortType"
          :cell-style="{ padding: ' 8px', backgroundColor: 'transparent', border: 'none', padding: '3px' }"
          :header-cell-style="{ background: '#8591CE26!important', color: '#8BDDF5FF', padding: '4px 8px', fontWeight: 'bold' }"
          style="width: 100%" element-loading-background="rgba(0, 0, 0, 0.2)" v-loading="tableLoading"
          @row-dblclick="goDetail">
          <el-table-column v-for="(column, index) in tableColumn" :key="index" :prop="column.prop" :label="column.label"
            :min-width="column.minWidth" show-overflow-tooltip>
            <template slot-scope="scope">
              <table-render v-if="column.render" :sc="scope" :row="scope.row" :render="column.render"></table-render>
              <div v-else-if="!column.formatter">
                {{ scope.row[column.prop] }}
              </div>
              <div v-else>
                {{ column.formatter(scope.row) }}
              </div>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </div>
    <template v-if="allTableComponentListShow">
      <allTableComponentList ref="allTableComponentList" :dialogData="tableCompenentData"
        :dialogShow="allTableComponentListShow" @configCloseDialog="configCloseTableDialog"></allTableComponentList>
    </template>
    <!-- 回路详情 -->
    <template v-if="loopDetailsShow">
      <loopDetails :show="loopDetailsShow" :requestParams="requestLoopParams" @closeDialog="hiddenLoopDetailsDialog"
        @changeDetailState="changeLoopDetailState" />
    </template>
  </div>
</template>

<script lang="jsx">
import { getLightCountOverview, getActuatorCountOverview, getGroupOperationMonitoring, getLightOpenOrClose } from '@/utils/spaceManage'
import loopImg from '@/assets/images/peace/loop.png'

import * as echarts from 'echarts'
import tableRender from '../components/tableRender.vue'
import allTableComponentList from '../components/allTableComponentList.vue'
import loopDetails from './components/loopDetailsDialog.vue'
export default {
  name: 'lightingComponent',
  props: {
    roomData: {
      type: Object,
      default: () => {}
    }
  },
  components: {
    allTableComponentList,
    tableRender,
    loopDetails
  },
  watch: {
    // 监听模型层级数据变化
    roomData: {
      handler(newVal, oldVal) {
        this.initData()
      },
      deep: true
    }
  },
  data() {
    return {
      loopImg,
      loopDetailsShow: false, // 回路详情
      tableLoading: false,
      deviceInfoManagementShow: false,
      tableCompenentData: {}, // 一站式弹窗数据
      currentMenuData: {}, // 当前选中的设备类型数据
      tableData: [], // 列表
      allTableComponentListShow: false,
      lightInfo: {
        outPutTotal: 0, // 回路总数
        actuatorTotal: 0, // 模块总数
        offlineTotal: '0', // 模块离线数
        moduleFailureTotal: '' // 模块故障数
      },
      requestLoopParams: {},
      tableColumn: [
        {
          prop: 'loopsName',
          label: '回路名称'
        },
        // {
        //   prop: 'entityTypeName',
        //   label: '归属系统',
        //   minWidth: '50',
        // },
        {
          prop: 'status',
          label: '状态',
          minWidth: '40',
          render: (h, row) => {
            return (
              <div>
                {row.row.outputStatus == '0' && (
                  <div class="table-icon">
                    <span style="color:#FF2D55">关闭</span>
                  </div>
                )}
                {row.row.outputStatus == '1' && (
                  <div class="table-icon">
                    <span style="color:#61E29D">开启</span>
                  </div>
                )}
              </div>
            )
          }
        },
        {
          prop: '',
          label: '操作',
          minWidth: '40',
          render: (h, row) => {
            return (
              <div class="operationBtn">
                {row.row.outputStatus == '0' && (
                  <div class="table-icon" onClick={() => this.selectConfigRowData(row.row, '1')}>
                    开启
                  </div>
                )}
                {row.row.outputStatus == '1' && (
                  <div class="table-icon" onClick={() => this.selectConfigRowData(row.row, '0')}>
                    关闭
                  </div>
                )}
              </div>
            )
          }
        }
      ],
      sortType: '',
      pagination: {
        total: 0,
        pageSize: 15,
        pageNo: 1
      },
      chartData: [],
      ssmCodes: [],
      objInfo: {},
      statisticsData: [
        {
          label: '模块总数',
          value: 0,
          key: 'actuatorTotal',
          color: '#FFCA64'
        },
        {
          label: '离线模块总数',
          key: 'offlineTotal',
          value: 0,
          color: '#FFCA64'
        },
        {
          label: '故障模块总数',
          value: 0,
          key: 'moduleFailureTotal',
          color: '#FF2D55 '
        }
      ]
    }
  },
  mounted() {
    this.sortType = this.roomData.ssmType < 4 ? 'jz' : this.roomData.ssmType == 4 ? 'lc' : 'mk'
    // 初始化调用
    this.initData()
  },
  methods: {
    initData() {
      this.sortType = this.roomData.ssmType < 4 ? 'jz' : this.roomData.ssmType == 4 ? 'lc' : 'mk'
      this.ssmCodes = this.roomData.ssmCodes?.split(',') || []
      this.getLightStatistics()
      this.getLoopStateData()
      this.getListGroupData()
    },
    // 回路统计
    getLightStatistics() {
      const parmas = {
        projectCode: this.roomData.projectCode,
        constructionId: this.roomData.ssmType < 4 ? '' : this.ssmCodes.at(-1)
      }
      getLightCountOverview(parmas).then((res) => {
        const data = res.data
        if (data.code === '200') {
          this.lightInfo = data.data
          const arr = [
            {
              name: '开启',
              value: 0,
              color: '#3562DB'
            },
            {
              name: '关闭',
              value: 0,
              color: '#FF9435'
            },
            {
              name: '其他',
              value: 0,
              color: '#00BC6D'
            }
          ]
          arr[0].value = data.data.openTotal == '-' ? 0 : data.data.openTotal
          arr[1].value = data.data.closeTotal == '-' ? 0 : data.data.closeTotal
          arr[2].value = data.data.unknownTotal == '-' ? 0 : data.data.unknownTotal
          this.setPieChart(arr)
        }
      })
    },
    // 统计图echarts
    setPieChart(arr) {
      const getchart = echarts.init(document.getElementById('loopStatisticsEcharts'))
      const nameList = Array.from(arr, (item) => item.name)
      const valueList = Array.from(arr, (item) => item.value)
      const data = []
      var colorList = ['#61E29D', '#3CC0FE', '#D3DDEB']
      for (var i = 0; i < nameList.length; i++) {
        data.push({
          value: valueList[i],
          name: nameList[i],
          itemStyle: {
            normal: {
              borderWidth: 1,
              shadowBlur: 200,
              color: colorList[i],
              label: {
                normal: { show: true }
              }
            }
          }
        })
      }
      var seriesObj = [
        {
          name: '',
          type: 'pie',
          clockWise: false,
          center: ['25%', '50%'],
          radius: ['78%', '95%'],
          hoverAnimation: true,
          label: {
            normal: {
              show: false,
              position: 'center',
              formatter: '{text|{b}}\n{c} ({d}%)',
              rich: {
                value: {
                  padding: 5,
                  align: 'center',
                  verticalAlign: 'middle',
                  fontSize: 20
                },
                label: {
                  align: 'center',
                  verticalAlign: 'middle',
                  fontSize: 12
                }
              }
            }
          },
          data: data
        },

        // 外边框
        {
          name: '外边框',
          type: 'pie',
          clockWise: false, // 顺时加载
          hoverAnimation: false, // 鼠标移入变大
          center: ['25%', '50%'],
          radius: ['91%', '93%'],
          label: {
            normal: {
              show: false
            }
          },
          data: [
            {
              value: 0,
              name: '',
              itemStyle: {
                normal: {
                  borderWidth: 1,
                  color: 'rgba(133,145,206,0.15)',
                  borderColor: 'rgba(133,145,206,0.15)'
                }
              }
            }
          ]
        },
        {
          name: '',
          type: 'pie',
          startAngle: 90,
          radius: '62%',
          animation: false,
          hoverAnimation: false,
          center: ['25%', '50%'],
          itemStyle: {
            normal: {
              labelLine: {
                show: false
              },
              color: new echarts.graphic.RadialGradient(0.5, 0.5, 1, [
                {
                  offset: 1,
                  color: 'rgba(133,145,206,0.15)'
                },
                {
                  offset: 0,
                  color: 'rgba(133,145,206,0.15)'
                }
              ]),
              shadowBlur: 60
            }
          },
          data: [
            {
              value: 100
            }
          ]
        }
      ]
      const option = {
        backgroundColor: 'rgba(128, 128, 128, 0)',
        title: {
          show: true,
          text: this.lightInfo.outPutTotal,
          subtext: '回路总数',
          x: '24%',
          y: '30%',
          itemGap: 6,
          textStyle: {
            color: '#fff',
            fontSize: 18,
            fontWeight: '400',
            lineHeight: 20
          },
          subtextStyle: {
            color: 'rgba(255,255,255,0.6)',
            fontSize: 16,
            fontWeight: '400',
            lineHeight: 20
          },
          textAlign: 'center'
        },
        legend: {
          orient: 'vertical',
          type: 'scroll',
          x: 'left',
          y: 'center',
          left: '50%',
          bottom: '0%',
          data: nameList,
          itemWidth: 8,
          itemHeight: 8,
          itemGap: 16,
          pageTextStyle: {
            color: '#fff'
          },
          textStyle: {
            //  fontSize: 18,//字体大小
            color: '#B3C2DD' //  字体颜色
          },
          formatter: function (name) {
            var oa = option.series[0].data
            var num = oa.reduce((sum, e) => sum + e.value, 0)
            for (var i = 0; i < option.series[0].data.length; i++) {
              if (name === oa[i].name) {
                if (num === 0) {
                  return ' ' + name + oa[i].value + '   ' + '0%'
                } else {
                  return ' ' + name + oa[i].value + '   ' + ((oa[i].value / num) * 100).toFixed(2) + '%'
                }
              }
            }
          }
        },
        series: seriesObj
      }
      getchart.setOption(option)
      // 随着屏幕大小调节图表
      window.addEventListener('resize', () => {
        getchart.resize()
      })
      getchart.on('legendselectchanged', (e) => {
        this.warnListRouter()
      })
    },
    // 回路状态
    getLoopStateData() {
      const type = this.sortType == 'jz' || this.sortType == 'lc' ? '1' : this.sortType == 'mk' ? '2' : '3'
      const parmas = {
        type: type,
        projectCode: this.roomData.projectCode,
        constructionId: this.roomData.ssmType < 4 ? '' : this.ssmCodes.at(-1)
      }
      getActuatorCountOverview(parmas).then((res) => {
        const data = res.data
        if (data.code === '200') {
          if (this.sortType == 'fwkj') {
            this.chartData = data.data.list
            this.getRenderer(data.data.list)
          } else {
            this.chartData = data.data
            this.getRenderer(data.data)
          }
          this.pagination.pageNo = 1
        }
      })
    },
    // 初始化统计表
    getRenderer(data) {
      // 基于准备好的dom，初始化echarts实例
      const EChart = echarts.init(document.getElementById('EChart'))
      const barWidth = 10 /* 进度条宽度 */
      const openData = [] /* 开启 */
      const closeData = [] /* 关闭 */
      const attaVal = [] /* 进度条数值 */
      const bgData = [] /* 进度条背景 */
      const topName = [] /* 进度条名称 */
      data.forEach((item, i) => {
        if (this.sortType == 'fwkj') {
          topName[i] = item.spaceName
        } else if (this.sortType == 'mk') {
          topName[i] = item.localName
        } else {
          topName[i] = item.name
        }
        attaVal[i] = item.openCount + item.closeCount
        openData[i] = item.openCount
        closeData[i] = item.closeCount
        bgData[i] = 300
      })
      // 配置参数
      const config = {
        background: '#ffff',
        tooltip: {
          show: false,
          textStyle: {
            fontSize: 16
          }
        },
        grid: {
          left: '2%',
          right: '10%',
          top: '15%',
          bottom: '0%',
          containLabel: true
        },
        legend: {
          show: true,
          top: '3%',
          right: '3%',
          itemHeight: 8,
          itemWidth: 8,
          itemGap: 20,
          textStyle: {
            fontSize: 12,
            color: '#fff'
          },
          data: [
            // 设为circle 默认  rect
            { name: '开启', icon: 'rect' },
            { name: '关闭', icon: 'rect' }
          ],
          selectedMode: false
        },
        xAxis: {
          show: false,
          type: 'value'
        },
        yAxis: [
          // 图标
          {
            type: 'category',
            data: topName,
            inverse: true,
            axisLabel: {
              fontSize: '12px',
              verticalAlign: 'center',
              color: '#FFFFFF',
              formatter: function (value, index) {
                return `{img|} `
              },
              rich: {
                img: {
                  width: 35,
                  height: 35,
                  backgroundColor: {
                    image: loopImg
                  }
                }
              }
            },
            axisLine: {
              show: false
            },
            axisTick: {
              show: false
            },
            splitLine: {
              show: false
            }
          }
        ],
        series: [
          {
            name: '开启',
            stack: 'total',
            type: 'bar',
            zlevel: 1,
            barWidth: barWidth,
            data: openData,
            itemStyle: {
              normal: {
                color: ' #61E29D',
                borderColor: '#2C344C',
                borderWidth: 1
              }
            },
            label: {
              normal: {
                color: '#FFFFFF',
                show: true,
                position: [0, '-18px'],
                textStyle: {
                  fontSize: 14
                },
                formatter: function (a, b) {
                  return a.name
                }
              }
            }
          },
          {
            name: '关闭',
            type: 'bar',
            zlevel: 1,
            barWidth: barWidth,
            stack: 'total',
            data: closeData,
            itemStyle: {
              normal: {
                color: '#FFFFFF',
                borderColor: '#2C344C',
                borderWidth: 1
              }
            }
          },
          // 背景进度条
          {
            name: '背景',
            type: 'bar',
            barWidth: 12,
            barGap: '-120%',
            data: bgData,
            tooltip: {
              show: false
            },
            label: {
              show: true,
              position: 'right',
              verticalAlign: 'bottom',
              offset: [-30, -10],
              textStyle: { color: '#8BDDF5', fontSize: 14 },
              formatter: (params) => {
                const { dataIndex } = params
                return attaVal[dataIndex]
              }
            },
            itemStyle: {
              normal: {
                color: 'rgba(255,255,255,0.1)'
              }
            }
          }
        ]
      }
      if (data.length > 4) {
        config.dataZoom = [
          {
            yAxisIndex: [0],
            show: true, // 为true 滚动条出现
            type: 'slider', // 有type这个属性，滚动条在最下面，也可以不行，写y：36，这表示距离顶端36px，一般就是在图上面。
            height: '90%', // 表示滚动条的高度，也就是粗细
            startValue: 0,
            endValue: 3,
            width: 6,
            handleSize: 0,
            left: '99%',
            borderColor: 'rgba(43,48,67,.1)',
            fillerColor: '#6580b8',
            zoomLock: true,
            showDataShadow: false,
            backgroundColor: 'rgba(43,48,67,.1)',
            showDetail: false,
            // realtime: true,
            filterMode: 'filter',
            handleIcon: 'M-9.35,34.56V42m0-40V9.5m-2,0h4a2,2,0,0,1,2,2v21a2,2,0,0,1-2,2h-4a2,2,0,0,1-2-2v-21A2,2,0,0,1-11.35,9.5Z',
            handleStyle: {
              color: '#6580b8',
              borderColor: '#6580b8'
            },
            brushSelect: false
          },
          {
            // 没有下面这块的话，只能拖动滚动条，鼠标滚轮在区域内不能控制外部滚动条
            type: 'inside',
            yAxisIndex: 0,
            zoomOnMouseWheel: false, // 滚轮是否触发缩放
            moveOnMouseMove: true, // 鼠标滚轮触发滚动
            moveOnMouseWheel: true
          }
        ]
      }
      EChart.clear()
      // 设置参数
      EChart.setOption(config)
      // 注册 mouseover 事件，类目轴名称切换为自定义颜色
      EChart.on('mouseover', (params) => {
        if (params.componentType === 'yAxis') {
          const yAxisName = params.value
          const yAxisItem = {
            value: yAxisName,
            textStyle: {
              color: '#FFCA64FF'
            }
          }
          const index = topName.findIndex((item) => {
            if (item) {
              return item === yAxisName || item.value === yAxisName
            }
          })
          topName.splice(index, 1, yAxisItem)
          config.yAxis.data = JSON.parse(JSON.stringify(topName))
          EChart.setOption(config)
        }
      })

      // 注册 mouseout 事件，类目轴名称恢复默认颜色
      EChart.on('mouseout', (params) => {
        if (params.componentType === 'yAxis') {
          const yAxisName = params.value
          const index = topName.findIndex((item) => {
            if (item) {
              return item === yAxisName || item.value === yAxisName
            }
          })
          topName.splice(index, 1, yAxisName)
          config.yAxis.data = JSON.parse(JSON.stringify(topName))
          EChart.setOption(config)
        }
      })

      // 先移除点击事件 解决点击事件重复绑定
      EChart.off('click')
      // 点击事件
      EChart.on('click', (params) => {
        const index = params.dataIndex
        this.pagination.pageNo = 1
        if (this.sortType === 'jz') {
          // 建筑
          const paramsId = this.chartData[index].id || ''
          this.getListGroupData({ constructId: paramsId })
          try {
            window.chrome.webview.hostObjects.sync.bridge.LightingControl(JSON.stringify({ spaceId: paramsId }))
          } catch (error) {}
        } else if (this.sortType === 'lc') {
          const paramsId = this.chartData[index].id || ''
          this.getListGroupData({ floorId: paramsId })
          try {
            window.chrome.webview.hostObjects.sync.bridge.LightingControl(JSON.stringify({ spaceId: paramsId }))
          } catch (error) {}
          // 楼层
        } else if (this.sortType === 'mk') {
          // 模块
          const spaceId = this.chartData[index].actuatorId || ''
          const paramsId = this.chartData[index].child.map((item) => item.spaceId).join(';')
          this.getListGroupData({ actuatorId: spaceId })
        } else if (this.sortType === 'fwkj') {
          // 服务空间
          const paramsId = this.chartData[index].spaceId || ''
          this.getListGroupData({ spaceTypeId: paramsId })
          try {
            window.chrome.webview.hostObjects.sync.bridge.LightingControl(JSON.stringify({ spaceId: paramsId }))
          } catch (error) {}
        }
      })
    },
    // 根据状态分组查询列表
    getListGroupData(obj) {
      this.objInfo = obj || {}
      this.tableLoading = true
      const params = {
        projectCode: this.roomData.projectCode,
        type: 3,
        page: this.pagination.pageNo,
        pageSize: this.pagination.pageSize
      }
      if (obj) {
        if (this.sortType == 'jz') {
          params.constructionId = obj.constructId ? obj.constructId : this.roomData.ssmType < 4 ? '' : this.ssmCodes.at(-1)
        } else if (this.sortType == 'lc') {
          params.constructionId = obj.floorId ? obj.floorId : this.roomData.ssmType < 4 ? '' : this.ssmCodes.at(-1)
        } else if (this.sortType == 'mk') {
          params.actuatorId = obj.actuatorId
          params.constructionId = this.roomData.ssmType < 4 ? '' : this.ssmCodes.at(-1)
        } else if (this.sortType == 'fwkj') {
          params.constructionId = this.roomData.ssmType < 4 ? '' : this.ssmCodes.at(-1)
          params.spaceTypeId = obj.spaceTypeId
        }
      } else {
        params.constructionId = this.roomData.ssmType < 4 ? '' : this.ssmCodes.at(-1)
      }
      getGroupOperationMonitoring(params).then((res) => {
        this.tableLoading = false
        if (res.data.code === '200' && res.data.data.list && res.data.data.list.length > 0) {
          if (this.pagination.pageNo === 1) {
            this.tableData = []
          }
          this.tableData = this.tableData.concat(res.data.data.list)
          this.pagination.total = res.data.data.count
        } else {
          this.tableLoading = false
          this.tableData = []
        }
      })
    },
    tableLoadMore() {
      if (this.pagination.total > this.pagination.pageNo * this.pagination.pageSize) {
        this.pagination.pageNo++
        this.getListGroupData(this.objInfo)
      }
    },
    changeSortType(sortType) {
      this.pagination.pageNo = 1
      this.sortType = sortType
      this.getLoopStateData()
      this.getListGroupData()
    },
    selectConfigRowData(el, type) {
      if (el !== '') {
        const parmas = {
          type: 3,
          outputStatus: type,
          actuatorId: el.actuatorId,
          forceSwitch: type == '1' ? '3' : '4',
          outputNum: el.outPutId
        }
        getLightOpenOrClose(parmas).then((res) => {
          const data = res.data
          if (data.code === '200') {
            try {
              window.chrome.webview.hostObjects.sync.bridge.LightingControl(JSON.stringify({ spaceId: el.spaceId, outputStatus: type }))
            } catch (error) {}
            this.getListGroupData(this.objInfo)
          }
        })
      } else {
        if (!this.tableData.length) return this.$message.warning('暂无数据无法进行批量操作！')
        const groupControl = this.tableData.map((item) => {
          return item.actuatorId + '_' + item.outPutId
        })
        const parmas = {
          type: 3,
          outputStatus: type,
          groupControl: groupControl.flat().toString(),
          forceSwitch: el.forceSwitch
        }
        getLightOpenOrClose(parmas).then((res) => {
          const data = res.data
          if (data.code === '200') {
            try {
              window.chrome.webview.hostObjects.sync.bridge.LightingControl(JSON.stringify({ outputStatus: type }))
            } catch (error) {}
            this.getListGroupData(this.objInfo)
          }
        })
      }
    },
    changeLoopDetailState(val) {
      const parmas = {
        type: 3,
        outputStatus: val.outputStatus == '1' ? '0' : '1',
        actuatorId: val.actuatorId,
        forceSwitch: val.outputStatus == '1' ? '3' : '4',
        outputNum: val.outPutId
      }
      getLightOpenOrClose(parmas).then((res) => {
        const data = res.data
        if (data.code === '200') {
          try {
            window.chrome.webview.hostObjects.sync.bridge.LightingControl(JSON.stringify({ spaceId: val.spaceId, outputStatus: val.outputStatus == '1' ? '0' : '1' }))
          } catch (error) {}
          this.getListGroupData(this.objInfo)
          if (val.outputStatus == '1') {
            this.requestLoopParams.state = '关'
            this.requestLoopParams.outputStatus = '0'
          } else {
            this.requestLoopParams.state = '开'
            this.requestLoopParams.outputStatus = '1'
          }
        }
      })
    },
    allTableChange(type, el) {
      this.allTableComponentListShow = true
      Object.assign(this.tableCompenentData, {
        title: '回路台账列表',
        projectCode: this.roomData.projectCode,
        ssmType: this.roomData.ssmType,
        ssmCodes: this.roomData.ssmCodes,
        type: type,
        sortType: this.sortType,
        objInfo: el == '2' ? this.objInfo : {},
        height: 'calc(100% - 120px)'
      })
    },
    // 关闭弹窗
    configCloseTableDialog() {
      this.allTableComponentListShow = false
      try {
        window.chrome.webview.hostObjects.sync.bridge.ShowDialog(false)
      } catch (error) {}
    },
    // 详情
    goDetail(row) {
      try {
        window.chrome.webview.hostObjects.sync.bridge.LoopListDetails(JSON.stringify({ spaceId: row.spaceId }))
      } catch (error) {}
      this.loopDetailsShow = true
      try {
        window.chrome.webview.hostObjects.sync.bridge.ShowDialog(true)
      } catch (error) {}
      this.requestLoopParams = row
    },
    hiddenLoopDetailsDialog() {
      this.loopDetailsShow = false
      try {
        window.chrome.webview.hostObjects.sync.bridge.ShowDialog(false)
      } catch (error) {}
    }
  }
}
</script>

<style lang="scss" scoped>
@import '../style/module.scss';
.top_Statistics {
  height: 130px;
  display: flex;
  flex-wrap: wrap;
  #loopStatisticsEcharts {
    width: 100%;
    height: 100%;
    z-index: 2;
  }
  .bottom_loop_statistics {
    background: rgba(255, 255, 255, 0.03);
    width: 100%;
    padding: 17px 0 16px 0;

    display: flex;
    .itemStatistics {
      text-align: center;
      flex: 1;
      p:nth-child(1) {
        font-size: 14px;
        color: rgba(255, 255, 255, 0.6);
        margin-bottom: 6px;
      }
      p:nth-child(2) {
        font-size: 20px;
      }
    }
  }
}
.moveComponent {
  width: 100%;
  height: 100%;
  .title-left {
    padding-left: 20px;
  }
  .title-detail {
    cursor: pointer;
    font-size: 14px;
    font-family: HarmonyOS Sans SC-Regular, HarmonyOS Sans SC;
    font-weight: 400;
    color: #8bddf5 !important;
    margin-right: 13px;
  }
  .toggle {
    display: flex;
    cursor: pointer;
    font-size: 14px;
  }
  .toggle > div {
    padding: 4px 10px;
    color: #8bddf5 !important;
    text-align: center;
    background-color: #213251;
    box-sizing: border-box;
    border: 1px solid #213251;
    opacity: 1;
  }
  .active-type {
    background: rgba(255, 255, 255, 0.1) linear-gradient(90deg, rgba(10, 132, 255, 0.4) 0%, rgba(10, 132, 255, 0) 100%);
    border: 1px solid #abf0ff !important;
  }
  .operation {
    cursor: pointer;
    color: #b0e3fa;
    margin-right: 20px;
    font-size: 14px;
    span:nth-child(1) {
      margin-right: 16px;
    }
  }
  .icon-box {
    margin-right: 0;
    display: flex;
    align-items: center;
    position: relative;
  }
  ::v-deep .el-table {
    .el-table__body tr {
      cursor: pointer;
    }
  }
}
// 滚动条的宽度
::v-deep .el-table__body-wrapper::-webkit-scrollbar {
  width: 6px; // 横向滚动条
  height: 6px; // 纵向滚动条 必写
}

// 注意:hover
::v-deep .el-table__body-wrapper:hover::-webkit-scrollbar {
  width: 6px; // 横向滚动条
  height: 6px; // 纵向滚动条 必写
}
::v-deep .el-table__body-wrapper::-webkit-scrollbar-corner,
::v-deep .el-table__body-wrapper::-webkit-scrollbar-track {
  background-color: transparent;
}
.module-header {
  width: 100%;
  // padding-left: 50px;
  background: url('~@/assets/images/qhdsys/bg-bt.png') no-repeat;
}
.order-more {
  width: 24px;
  height: 24px;
  margin-right: 0 !important;
  margin-left: 8px;
}
</style>
