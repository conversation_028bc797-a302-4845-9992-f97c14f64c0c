<template>
  <el-dialog
    v-dialogDrag
    :modal="false"
    :visible="visible"
    custom-class="mainDialog main"
    append-to-body
    :close-on-click-modal="false"
    :before-close="closeDia"
    class="all-table-componentList"
  >
    <template slot="title">
      <span class="dialog-title">开门记录</span>
    </template>
    <div class="dialog-content">
      <div class="search-box">
        <div style="margin-right: 16px"  class="taskInput">
          <el-date-picker
            v-model="taskDate"
            type="datetimerange"
            range-separator="至"
            start-placeholder="开始时间"
            end-placeholder="结束时间"
            popper-class="date-style"
            value-format="yyyy-MM-dd HH:mm:ss"
          >
          </el-date-picker>
        </div>

        <div style="margin-right: 16px" class="taskInput">
          <el-select
            v-model="searchParams.taskStatus"
            placeholder="楼宇名称"
            filterable
            popper-class="new-select"
          >
            <el-option
              v-for="item in alarmTypeList"
              :key="item.id"
              :label="item.label"
              :value="item.id"
            >
            </el-option>
          </el-select>
        </div>

        <div style="margin-right: 16px" class="taskInput">
          <el-select
            v-model="searchParams.taskStatus"
            placeholder="电梯名称"
            filterable
            popper-class="new-select"
          >
            <el-option
              v-for="item in alarmLevelList"
              :key="item.id"
              :label="item.label"
              :value="item.id"
            >
            </el-option>
          </el-select>
        </div>
        <div style="margin-right: 16px" class="taskInput">
          <el-select
            v-model="searchParams.taskStatus"
            placeholder="停靠楼层"
            filterable
            popper-class="new-select"
          >
            <el-option
              v-for="item in alarmLevelList"
              :key="item.id"
              :label="item.label"
              :value="item.id"
            >
            </el-option>
          </el-select>
        </div>

        <div class="search-btn">
          <el-button @click="resetTaskSearch">重置</el-button>
          <el-button @click="handleTaskSearch">查询</el-button>
        </div>
      </div>
      <div class="tableContent">
        <el-table
          v-loading="tableLoading"
          :data="tableData"
          height="100%"
          :cell-style="$tools.setCell(3)"
          :header-cell-style="$tools.setHeaderCell(3)"
          style="width: 100%"
          element-loading-background="rgba(0, 0, 0, 0.2)"
        >
          <el-table-column prop="abc" label="电梯名称"> </el-table-column>
          <el-table-column prop="abc" label="开门时间"> </el-table-column>
          <el-table-column prop="abc" label="进人数量"> </el-table-column>
          <el-table-column prop="abc" label="出人数量"> </el-table-column>
          <el-table-column prop="abc" label="停靠楼层"> </el-table-column>
        </el-table>
        <el-pagination
          :current-page="currentPage"
          :page-sizes="[15, 20, 30, 40]"
          :page-size="pageSize"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
          class="pagination"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        ></el-pagination>
      </div>
    </div>
  </el-dialog>
</template>
<script>
export default {
  props: {
    visible: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      searchParams: {},
      alarmTypeList: [],
      spaceAreaList: [],
      alarmLevelList: [],
      taskDate: [],
      tableLoading: false,
      tableData: [],
      currentPage: 1,
      pageSize: 15,
      total: 0
    }
  },
  methods: {
    closeDia() {
      this.$emit('close')
    },
    getList() {},
    resetTaskSearch() {
      this.getList()
    },
    handleTaskSearch() {
      this.getList()
    },
    handleSizeChange(val) {
      this.currentPage = 1
      this.pageSize = val
      this.getList()
    },
    handleCurrentChange(val) {
      this.currentPage = val
      this.getList()
    }
  }
}
</script>
<style lang="scss" scoped>
::v-deep .mainDialog {
  width: 70%;
  height: 84vh;
  margin-top: 6vh !important;
  // border: 1px solid #5996f9;
  background-color: transparent !important;
  pointer-events: auto;
  background-image: url("@/assets/images/table-bg.png");
  background-repeat: no-repeat;
  background-size: 100% 100%;
  box-shadow: none;
  .el-dialog__headerbtn {
    transform: translateX(-36px);
    width: 25px;
    height: 25px;
    background-image: url("@/assets/images/close.png");
    background-repeat: no-repeat;
    background-size: 100% 100%;
    .el-dialog__close::before {
      display: none;
    }
  }
  .dialog-title {
    display: inline-block;
    width: 100%;
    text-align: center;
    transform: translateY(-6px);
    color: #cbdeed;
  }
  .dialog-title::before {
    display: none;
  }

  .el-dialog__header {
    background-color: transparent;
  }
  .el-dialog__body {
    padding: 0;
    height: calc(100% - 60px);
    max-height: fit-content;
    overflow-y: hidden;
    background-color: transparent;
    padding: 0px 80px;
    .dialog-content {
      width: 100%;
      height: 100%;
      background-color: transparent;

      .el-table {
        background-color: transparent;
        border: 1px solid #202d4c;
        .el-table__header-wrapper {
          background-color: transparent !important;
        }
        .el-table__body-wrapper {
          background-color: transparent !important;
        }
        tr {
          background-color: transparent !important;
        }
        td {
          background-color: transparent !important;
        }
      }
    }
  }
}
:deep(.search-box) {
  display: flex;
  flex-wrap: wrap;

  .taskInput {
    .el-input {
      width: 140px;
    }
    .el-cascader {
      line-height: 35px;
      .el-input__inner {
        height: 35px !important;
      }
    }
  }

  .el-button {
    background-image: url("@/assets/images/btn.png");
    background-repeat: no-repeat;
    background-size: 100% 100%;
    height: 35px;
  }
  .search-btn {
    // margin: 10px 0;
    margin-left: auto;
  }
  .el-select {
    .el-input .el-input__inner {
      border-color: rgba(133, 145, 206, 0.5);
      width: 150px;
      height: 35px;
    }
  }

  .el-date-editor {
    width: 250px;
    height: 35px;
    background-color: #14233e;
    border-color: rgba(133, 145, 206, 0.5);
    .el-input__icon {
      transform: translateY(-2px);
    }
    .el-range-input {
      background-color: #14233e;
      color: rgba(255, 255, 255, 0.8);
    }
    .el-range-separator {
      color: rgba(255, 255, 255, 0.8);
      transform: translateY(-2px);
    }
  }
  .el-input .el-input__inner {
    border-color: rgba(133, 145, 206, 0.5);
    border-radius: 4px;
    color: rgba(255, 255, 255, 0.8);
  }
}
.search-box {
  margin: 5px 0 15px;
}

.search-box .el-input {
  width: 120px;
  height: 35px;
}
.tableContent {
  width: 100%;
  height: calc(100% - 56px - 60px);
}
</style>
