<template>
  <div>
    <div class="riskHiddenDanger" v-dialogDrag ref="collapseHeight">
      <!-- <el-button class="sino-button-sure" @click="showDialog('dangerComponent')">showDanger</el-button> -->
      <!-- <el-button class="sino-button-sure" @click="showDialog('riskComponent')">showRisk</el-button> -->
      <!-- <el-button class="sino-button-sure" @click="showTableDialog()">showTableDialog</el-button> -->
      <div class="el-dialog right-content">
        <div @click="collapseHeight" :class="collapseFlag ? 'circle-btn-top' : 'circle-btn-bottom'" class="circle-btn circle-btn-top"></div>
        <div class="bg-title el-dialog__header" v-show="collapseFlag">
          <span>{{ sysTitle }}</span>
        </div>
        <div class="bg-content room-info-box" v-show="collapseFlag">
          <div class="sys-box">
            <component v-if="isShow" ref="comPonent" :is="activeType" :type="'2'" :roomData="dialogData" class="sys-box-content"></component>
          </div>
        </div>
      </div>
      <template>
        <hazardList ref="hazardList" :type="activeListType" :dialogShow="hazardListShow" @configCloseDialog="hazardListChange"></hazardList>
      </template>
    </div>
  </div>
</template>

<script>
export default {
  name: 'riskHiddenDanger',
  components: {
    riskComponent: () => import('./sysTypeComponent/riskComponent'),
    dangerComponent: () => import('./sysTypeComponent/dangerComponent'),
    hazardList: () => import('../centerScreen/safetyOverview/components/hazardList')
  },
  data() {
    return {
      sysTitle: '',
      activeType: 'riskComponent',
      WPFmessage: '',
      dialogData: {},
      isShow: false,
      collapseFlag: true,
      hazardListShow: false
    }
  },
  computed: {
    activeListType() {
      return this.activeType === 'riskComponent' ? 'risk' : 'danger'
    }
  },
  mounted() {
    this.setQueryData()
    try {
      window.chrome.webview.addEventListener('message', (event) => {
        const data = JSON.parse(event.data)
        if (data.type === 'tag') {
          const params = {
            placeIds: data.localtion
          }
          this.hazardListChange(params)
        }
      })
    } catch (error) {}
  },
  methods: {
    // 模拟数据
    showDialog(type) {
      let query = {}
      if (type === 'dangerComponent') {
        query = {
          types: 'dangerComponent',
          title: '综合急诊急救楼 > 1f',
          modelCode: 'BJSJTYY0100106',
          localtion: '0100104',
          ssmType: '5',
          ssmCodes: '1574997196330250241,1574997196833566721,1574997197265580033',
          type: 'area'
        }
      } else {
        query = {
          types: 'riskComponent',
          title: '综合急诊急救楼 > 1f',
          modelCode: 'BJSJTYY0100106',
          localtion: '0100104',
          ssmType: '5',
          ssmCodes: '1574997196330250241,1574997196833566721,1574997197265580033',
          type: 'area'
        }
      }
      this.$router.push({
        path: '/riskHiddenDanger',
        query
      })
      this.$nextTick(() => {
        this.setQueryData()
        this.hazardListShow = false
      })
    },
    showTableDialog() {
      const params = {
        placeIds: '1574997372902060033'
      }
      this.hazardListChange(params)
    },
    // 风险隐患清单
    hazardListChange(params) {
      this.hazardListShow = !this.hazardListShow
      this.$nextTick(() => {
        if (this.hazardListShow) {
          this.$refs.hazardList.getWorkOrderTableData(params)
        }
        try {
          window.chrome.webview.hostObjects.sync.bridge.ShowTable(this.hazardListShow)
        } catch (error) {}
      })
    },
    setQueryData() {
      try {
        this.activeType = this.$route.query.types
        this.dialogData = {
          title: decodeURI(this.$route.query.title),
          modelCode: this.$route.query.modelCode,
          localtion: this.$route.query.localtion,
          ssmType: this.$route.query.ssmType,
          ssmCodes: this.$route.query.ssmCodes
        }
        this.sysTitle = this.$route.query.title
        this.isShow = true
        this.$refs.comPonent.activeMethods()
      } catch (errpr) {}
    },
    closeRoomDialog() {
      this.$emit('configCloseDialog')
    },
    collapseHeight() {
      this.$nextTick(() => {
        if (this.collapseFlag) {
          this.collapseFlag = !this.collapseFlag
          this.$refs.collapseHeight.style.width = '0'
          // this.$refs.collapseHeight.style.padding = '0'
        } else {
          setTimeout(() => {
            this.collapseFlag = !this.collapseFlag
          }, 200)
          this.$refs.collapseHeight.style.width = '100%'
          // this.$refs.collapseHeight.style.padding = '10px 10px 20px 10px'
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.riskHiddenDanger {
  position: absolute;
  top: 3%;
  right: 1%;
  // width: 22%;
  width: 100%;
  height: 100%;
  max-height: 95%;
  .right-content {
    width: 22%;
    // width: 100%;
    height: 100%;
    margin: 0 0 3.125rem auto;
    background: url('~@/assets/images/center/bg-80.png') no-repeat;
    background-size: 100% 100%;
    box-sizing: border-box;
    position: relative;
    .bg-title {
      height: 3.8rem;
      line-height: 3.8rem;
      padding: 0 10px 0 2.2rem;
      color: #dceaff;
      font-family: TRENDS;
      // 不可被选中
      -webkit-user-select: none;
      display: flex;
      span {
        width: calc(100% - 2.2rem);
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
      i {
        float: right;
        line-height: 2.8rem;
        font-size: 1.5rem;
        cursor: pointer;
      }
    }
    .bg-content {
      position: relative;
      box-sizing: border-box;
      padding: 10px 2px 2px 2px;
      width: 100%;
      height: calc(100% - 3rem);
    }
    .room-info-box {
      color: #fff;
      display: flex;
      flex-direction: column;
      .box-type {
        width: 100%;
        margin: auto;
        display: flex;
        flex-wrap: wrap;
        justify-content: center;
        span {
          display: inline-block;
          width: fit-content;
          height: 24px;
          padding: 0 6px;
          background-color: #24396d;
          text-align: center;
          line-height: 24px;
          color: #dceaff;
          font-size: 14px;
          font-family: PingFang-SC-Medium, PingFang-SC;
          cursor: pointer;
          margin: 5px 3px 0 3px;
        }
        .type_active {
          color: #ffe3a6;
          background: url('~@/assets/images/center/border-bg-select.png') no-repeat;
          background-size: 100% 100%;
        }
      }
      .sys-box {
        width: 100%;
        height: calc(100% - 30px);
        // flex: 1;
        .sys-box-content {
          width: 100%;
          height: 100%;
          padding: 0 10px;
          box-sizing: border-box;
        }
        // height: calc(100% - 24px);
      }
    }
  }
}
.circle-btn {
  position: absolute;
  top: 20px;
  right: 8px;
  width: 26px;
  height: 26px;
  cursor: pointer;
  margin: auto 0;
  margin-right: 10px;
  z-index: 2;
}
.circle-btn-top {
  background: url('~@/assets/images/center/btn-fold.png') no-repeat;
  background-size: 100% 100%;
}
.circle-btn-top {
  background: url('~@/assets/images/center/btn-fold.png') no-repeat;
  background-size: 100% 100%;
}
.circle-btn-bottom {
  width: 44px;
  height: 44px;
  background: url('~@/assets/images/center/btn-unfold.png') no-repeat;
  background-size: 100% 100%;
}
</style>
