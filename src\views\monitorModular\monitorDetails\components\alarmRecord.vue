<template>
  <div class='content'>
    <el-row>
      <el-col :span="12">
        <div class="searchItem">
          <div class="itemTiele">指标项名称</div>
          <el-input placeholder="指标项名称"></el-input>
        </div>
      </el-col>
      <el-col :span="12">
        <div class="searchItem">
          <div class="itemTiele">类型</div>
          <el-input placeholder="类型"></el-input>
        </div>
      </el-col>
      <el-col :span="12">
        <div class="searchItem">
          <div class="itemTiele">报警时间</div>
          <el-date-picker
            v-model="value1"
            type="datetimerange"
            class="datePickerInput"
            popper-class="date-style"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期">
          </el-date-picker>
        </div>
      </el-col>
      <el-col :span="24" class="btnWrap">
        <el-button>重置</el-button>
        <el-button>查询</el-button>
        <el-button>导出</el-button>
        <el-link :underline="false">
          展开
          <i class="el-icon-arrow-down"></i>
        </el-link>
      </el-col>
    </el-row>
    <div class="tableWrap" style="height: calc(100% - 162px);">
      <el-table
        :data="tableData1"
        height="calc(100% - 32px)"
        style="width: 100%">
        <el-table-column
          prop="name"
          label="分类">
        </el-table-column>
        <el-table-column
          prop="code"
          label="指标项名称">
        </el-table-column>
        <el-table-column
          prop="assetType"
          label="单位">
        </el-table-column>
        <el-table-column
          prop="auditType"
          label="报警时间">
        </el-table-column>
        <el-table-column
          prop="dept"
          label="上限">
        </el-table-column>
        <el-table-column
          prop="medical"
          label="下限"
          width="80">
        </el-table-column>
        <el-table-column
          prop="brand"
          label="值">
        </el-table-column>
      </el-table>
      <div class="sic-content-table-pagination">
        <el-pagination :current-page="currentPage" :page-sizes="[15, 20, 30, 40]"
                       :page-size="pageSize" layout="total, sizes, prev, pager, next, jumper" :total="total"
                       class="pagination" @size-change="handleSizeChange" @current-change="handleCurrentChange"></el-pagination>
      </div>
    </div>
  </div>
</template>
<script>
export default {
  components: {},
  data() {
    return {
      tableData1: [],
      currentPage: 1,
      pageSize: 15,
      total: 0
    }
  },
  created() {},
  methods: {
    handleSizeChange() {},
    handleCurrentChange() {}
  }
}
</script>
<style lang="scss" scoped>
.content {
  padding: 20px;
  height: 100%;
  .searchItem {
    display: flex;
    align-items: center;
    margin-bottom: 20px;
    .itemTiele {
      width: 100px;
    }
    .el-input {
      max-width: calc(90% - 100px)
    }
  }
  ::v-deep .el-tabs__nav {
    .el-tabs__item {
      color: #fff !important;
    }
    .is-active {
      color: #409EFF !important;
    }
  }
  .btnWrap {
    display: flex;
    justify-content: flex-end;
    padding-right: calc(10% - 75px);
    .el-link {
      margin-left: 10px;
      color: #fff;
    }
  }
  ::v-deep .datePickerInput {
    flex: 1;
    padding: 8px 10px;
    height: 22px;
    box-sizing: content-box;
    border-radius: unset;
    background: none;
    max-width: calc(90% - 120px);
    border: 1px solid #3056A2;
    .el-input__icon,
    .el-range-separator {
      line-height: 16px;
      color: #b0e3fa;
    }
    .el-range-input {
      background: none;
      color: #a4afc1;
    }
  }
  .tableWrap {
    margin-top: 10px;
    .pie {
        display: inline-block;
        width: 8px;
        height: 8px;
        border-radius: 50%;
        margin-right: 4px;
      }
    .degree0 {
      color: #FA8B2C;
      .pie {
        background-color: #FA8B2C;
      }
    }
    .degree1 {
      color: #FF4D4F;
      .pie {
        background-color: #FF4D4F;
      }
    }
    .type0 {
      color: #52C41A;
      .pie {
        background-color: #52C41A;
      }
    }
    .type1 {
      color: #8C8C8C;
      .pie {
        background-color: #8C8C8C;
      }
    }
  }
}
</style>