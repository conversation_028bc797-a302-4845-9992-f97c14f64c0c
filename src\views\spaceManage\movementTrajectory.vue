<template>
  <div class="content">
    <div class="echarts-center">
      <div class="center-title">
        <div @click="backToWPFhome()"><i class="el-icon-arrow-left"></i>返回</div>
        <span>移动轨迹</span>
      </div>
    </div>
    <div class="screen_and_list">
      <div class="search-form">
        <el-input
          style="width: 217px; margin-right: 20px"
          placeholder="设备名称"
          v-model="searchForm.assetsName"
          clearable
          maxlength="25"
          @input="handleInput"
          @keyup.enter.native="_searchByCondition"
        ></el-input>
        <el-date-picker
          value-format="yyyy-MM-dd"
          style="margin-right: 20px;"
          :popper-class="{ 'timePicker': true, 'kxy-popper-class': true }"
          v-model="searchForm.timeSlot"
          type="daterange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          @change="_searchByCondition"
        >
        </el-date-picker>
        <!-- <el-button class="sino-button-sure" style="margin-left: 30px" @click="_searchByCondition">查询</el-button> -->
        <!-- <el-button class="sino-button-sure" @click="_resetCondition">重置</el-button> -->
      </div>
      <div class="equipment">
        <div class="equipment_list">
          <div class="equipment_item" v-for="item in equipmentList" :key="item.assetsId" @click="onEquipment(item)">
            <img class="equipment_img" :src="item.icon" alt="">
            <div class="equipment_data">
              <el-tooltip class="item" effect="dark" :content="item.assetsName" placement="top">
                <p class="ellipsis">{{ item.assetsName }}</p>
              </el-tooltip>
              <div class="equipment_des">
                <span>{{ item.useDepartmentName }}</span>
                <span>{{ item.assetsNumber }}</span>
              </div>
            </div>
            <img v-if="assetsId == item.assetsId" class="equipment_selected" src="@/assets/images/sys/selected.png" alt="">
          </div>
          <div v-if="!equipmentList.length" class="noData">暂无数据</div>
        </div>
        <div class="equipment_timeline">
          <div
            v-show="stepList.length"
            class="step_item"
            v-for="(item, index) in stepList"
            :key="index + 'id'"
          >
            <div class="time_left">
              <p class="p_date">{{ item.date }}</p>
              <p class="p_time">{{ item.time }}</p>
            </div>
            <div class="icon_left">
              <h3 v-if="index == 0" class="centered rise">起</h3>
              <h3 v-else-if="index == stepList.length-1" class="centered end">终</h3>
              <h3 v-else></h3>
              <p :class="index != stepList.length-1 ? 'vertical' : ''"></p>
            </div>
            <!-- <div class="step_right">
              <el-tooltip class="item" effect="dark" :content="item.gridName" placement="top">
                <p style="width: 15rem;" class="ellipsis">{{ item.gridName }}</p>
              </el-tooltip>
            </div> -->
          </div>
          <div v-if="!stepList.length" class="noData">暂无数据</div>
        </div>
        <!-- <div class="equipment_3D">

        </div> -->
      </div>
    </div>
  </div>
</template>

<script>
import moment from 'moment'
import { trackedAssets, getAssetsMovePath, getAssetsPosition } from '@/utils/equipmentApi'
export default {
  data() {
    return {
      moment,
      stepList: [],
      searchForm: {
        assetsName: null,
        timeSlot: null
      },
      assetsId: this.$route.query.id,
      equipmentList: [],
      inputTimer: null
    }
  },
  created() {
    this.searchForm.timeSlot = [
      moment().format('YYYY-MM-DD'),
      moment().format('YYYY-MM-DD')
    ]
  },
  mounted() {
    this.getList()
  },
  methods: {
    // 输入框节流
    handleInput() {
      if (this.inputTimer) {
        clearTimeout(this.inputTimer)
      }
      this.inputTimer = setTimeout(() => {
        this.inputTimer = null
        this._searchByCondition()
      }, 500)
    },
    getList() {
      trackedAssets({ assetsName: this.searchForm.assetsName }).then(res => {
        // this.equipmentList = [...res.data.data, ...res.data.data, ...res.data.data, ...res.data.data, ...res.data.data, ...res.data.data, ...res.data.data, ...res.data.data, ...res.data.data]
        this.equipmentList = res.data.data
        this.getAssetsMove()
      })
    },
    getAssetsMove() {
      const iotCode = this.equipmentList.find(ele => ele.assetsId === this.assetsId)?.iotCode
      const data = {
        iotCode,
        startTime: this.searchForm.timeSlot ? `${this.searchForm.timeSlot[0]} 00:00:00` : '',
        endTime: this.searchForm.timeSlot ? `${this.searchForm.timeSlot[1]} 23:59:59` : ''
      }
      getAssetsMovePath(data).then(res => {
        this.stepList = res.data.data.map(ele => {
          return {
            ...ele,
            date: ele.recordTimeStr ? moment(ele.recordTimeStr).format('hh:mm') : '08:00',
            time: ele.recordTimeStr ? moment(ele.recordTimeStr).format('MM.DD') : '01.01'
          }
        })
        try {
          getAssetsPosition({ assetsId: this.assetsId }).then(posRes => {
            const data = {
              id: this.assetsId,
              locationId: posRes.data.data.gridId || '',
              locationCode: posRes.data.data.gridCode || '',
              isClickType: 2,
              Points: res.data.data.map(ele => {
                return {
                  X: ele.locX,
                  Y: ele.locY,
                  Z: ele.locZ
                }
              })
            }
            console.log('data======', data)
            window.chrome.webview.hostObjects.sync.bridge.AntiTheftClick(JSON.stringify(data))
          })
        } catch (errpr) {}
      })
    },
    onEquipment(item) {
      this.assetsId = item.assetsId
      this.getAssetsMove()
    },
    // 列表查询
    _searchByCondition() {
      // if (this.searchForm.assetsName) {
      //   this.getList()
      // } else if (this.searchForm.timeSlot) {
      //   this.getAssetsMove()
      // }
      this.getList()
    },
    // 重置
    _resetCondition() {
      this.searchForm = {
        assetsName: null,
        timeSlot: null
      }
      this.getList()
    },
    backToWPFhome() {
      window.chrome.webview.hostObjects.sync.bridge.Back('equipmentAntiTheft')
    }
  }
}
</script>

<style lang="scss" scoped>
@import '~@/assets/sino-ui/common/var.scss';
.noData{
  width: 100%;
  display: flex;
  justify-content: center;
  color: rgb(204, 204, 204);
}
.equipment_list::-webkit-scrollbar,
.equipment_timeline::-webkit-scrollbar {
  width: .2rem;
}
.ellipsis {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.content {
  position: relative;
  // background-color: #031553;
  box-sizing: border-box;
  width: 100%;
  height: 100%;
  .screen_and_list{
    height: 87%;
    width: 33%;
    padding: 1%;
    background-color: rgba(3, 21, 83, .8);
    border: 1px solid #3866A9;
    .equipment{
      width: 100%;
      height: calc(100% - 3rem);
      display: flex;
      &_3D{
        flex: 1;
        // background: rgb(61, 60, 61);
      }
      &_timeline{
        width: 35%;
        margin-left: 5rem;
        height: 100%;
        overflow: auto;
        // background-color: #031553;
        .step_item{
          display: flex;
          height: 6rem;
          .time_left{
            display: flex;
            flex-direction: column;
            align-items: flex-end;
            margin-right: .5rem;
            .p_date{
              font-size: .6rem;
              color: #FFFFFF;
            }
            .p_time{
              font-size: 1.1rem;
              margin-top: .5rem;
              font-weight: bold;
              color: #FFFFFF;
            }
          }
          .icon_left{
            display: flex;
            flex-direction: column;
            align-items: center;
            width: 2.5rem;
            h3{
              width: .8rem;
              height: .8rem;
              border-radius: 50%;
              background: #BBC3CE;
            }
            .rise{
              background: #00C249;
            }
            .end{
              background: #FF6161;
            }
            .centered{
              display: flex;
              justify-content: center;
              align-items: center;
              width: 2.1rem;
              height: 2.1rem;
              border-radius: 50%;
              color: #FFFFFF;
            }
            .vertical{
              width: 2px;
              flex: 1;
              background: #D8DEE7;
            }
          }
          .step_right{
            margin-left: 12px;
            padding-bottom: 20px;
            p{
              font-size: 14px;
              color: #FFFFFF;
              margin-top: .5rem;
            }
          }
        }
      }
      &_list{
        width: 65%;
        // padding-left: 5%;
        height: 100%;
        // background-color: #031553;
        overflow: auto;
        padding-right: .8rem;
        .equipment_item{
          cursor: pointer;
          width: calc(100% - 4rem);
          background: rgba(179, 234, 235, .1);
          margin-bottom: 1rem;
          display: flex;
          padding: 2rem;
          position: relative;
          border-radius: 1rem;
          .equipment_img{
            width: 5.5rem;
            height: 5.5rem;
            border-radius: 50%;
          }
          .equipment_selected{
            height: 80%;
            position: absolute;
            right: -.7rem;
            top: 10%;
          }
          .equipment_data{
            display: flex;
            width: 13rem;
            flex-direction: column;
            margin-left: 1.5rem;
            justify-content: space-between;
            p{
              color: #FFFFFF;
              font-size: 16px;
              font-weight: 400;
            }
            .equipment_des{
              display: flex;
              flex-direction: column;
              span{
                font-size: 14px;
                font-weight: normal;
                color: #FFFFFF;
                opacity: .5;
                line-height: 21px;
              }
            }
          }
        }
      }
    }
    .search-form {
      height: 2.5rem;
      width: 100%;
      // padding-left: 5%;
      // background-color: #031553;
      padding-bottom: 1.5rem;
      .exportBtn {
        position: absolute;
        right: 0;
        transition: right 0.8s;
      }
      ::v-deep .el-input__inner {
        background: center;
        border: 1px solid $input-border-color;
        border-radius: 0;
        color: $color;
        height: inherit;
        font-family: PingFangSC-Medium, PingFang SC;
      }
      ::v-deep .el-range-input {
        background: center;
        color: $color;
      }
      ::v-deep .el-range-separator,
      ::v-deep .el-range__icon {
        color: #3769be;
      }
    }
  }
  .echarts-center {
    width: 100%;
    height: 3rem;
    // background-color: #031553;
    .center-title {
      height: 30px;
      z-index: 10;
      div {
        cursor: pointer;
        position: absolute;
        top: 5px;
        left: 0;
        width: 80px;
        height: 30px;
        line-height: 30px;
        text-align: center;
        color: #618ad3;
        font-size: 0.875rem;
        white-space: nowrap;
        background-image: url(../../assets/images/peace/btn-back.png);
        background-size: 100% 100%;
        background-repeat: no-repeat;
      }
      span {
        z-index: 1;
        left: 50%;
        transform: translate(-50%);
        position: absolute;
        display: block;
        color: #dceaff;
        width: 200px;
        height: 30px;
        line-height: 30px;
        margin: 0 auto;
        text-align: center;
        // background: linear-gradient(270deg, rgba(104, 168, 255, 0) 0%, rgba(89, 135, 239, 0.2) 50%, rgba(104, 168, 255, 0) 100%);
        background-image: url('~@/assets/images/war/title-bg.png');
        background-size: 100% 100%;
        background-repeat: no-repeat;
        font-family: 'TRENDS';
      }
    }
  }
}

</style>
