<!-- 重点区域详情 -->
<template>
  <div class="keyAreasDetails">
    <div class="module-container" style="height: calc(100%); background: rgba(133,145,206,0.15);margin-bottom: 16px;">
      <div class="info-header clear">
        <p class="info-header-text fl">基础信息</p>
      </div>
      <div class="module-content info-list" style="height: calc(100% - 44px)">
        <div v-for="item in infoList" :key="item.label" class="info-list-item">
          <p class="item-label">{{ item.label }}：</p>
          <p class="item-value">{{ detailsInfo[item.key] || '---' }}</p>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { GetGridAreaDetail } from '@/utils/spaceManage'
export default {
  name: 'keyAreasDetails',
  props: {
    roomData: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      infoList: [
        {label: '重点区域名称', key: 'areaName'},
        {label: '责任部门', key: 'officeName'},
        {label: '责任人', key: 'principalName'},
        {label: '责任人电话', key: 'phoneNo'},
        {label: '安全责任人', key: 'securityPrincipalName'},
        {label: '安全责任人电话', key: 'securityPhoneNo'},
        {label: '包含空间', key: 'spaceNameList'}
      ],
      detailsInfo: {}
    }
  },
  computed: {

  },
  created() {
    this.getGridAreaDetail()
  },
  methods: {
    getGridAreaDetail() {
      GetGridAreaDetail({id: this.roomData.deviceId}).then(res => {
        if (res.data.code == 200) {
          res.data.data.spaceNameList = res.data.data.spaceList?.map(v => v.regionName).join('、')
          this.detailsInfo = res.data.data
        }
      })
    }
  }
}

</script>

<style lang="scss" scoped>
@import '../style/module.scss';
.keyAreasDetails {
  padding: 5px 0px 0px 0px;
  .module-header {
    padding-left: 30px;
    padding-right: 10px;
    width: 100%;
    background: url("@/assets/images/qhdsys/bg-bt.png") no-repeat;
    background-size: contain;
  }
  .info-header {
    padding: 6px 8px;
    line-height: 22px;
    background: rgba(133,145,206,0.15);
    .info-header-text {
      font-weight: 500;
      font-size: 15px;
      color: #FFFFFF;
    }
  }
  .info-list {
    padding: 0px 16px;
    overflow: auto;
    .info-list-item {
      display: flex;
      .item-label {
        font-weight: 400;
        font-size: 14px;
        color: #B0E3FA;
        line-height: 30px;
        width: 115px;
      }
      .item-value {
        flex: 1;
        font-weight: 400;
        font-size: 14px;
        color: #FFFFFF;
        line-height: 30px;
      }
    }
  }
  .title-right {
    font-size: 14px;
    align-items: center;
  }
}
</style>
