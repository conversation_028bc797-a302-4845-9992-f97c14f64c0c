/*
 * @Author: ycw
 * @Date: 2020-05-26 14:56:38
 * @LastEditors: Please set LastEditors
 * @LastEditTime: 2024-09-13 16:48:43
 * @Description:
 */
import store from '@/store/index'
import moment from 'moment'
import { Message } from 'element-ui'
import { monitorTypeList, hasModelCodeFilterProjectName } from '@/assets/common/dict.js'
moment.locale('zh-cn', { week: { dow: 1 } })
function ListTree (list, rootid) {
  var arr = []
  list.forEach((item) => {
    if (item.id === rootid) {
      arr.push(item.id)
      const newArr = ListTree(list, item.pid)
      if (newArr) {
        arr.unshift(...newArr)
      }
    }
  })
  return arr
}
let dialogTimer = null
export default {
  /**
   * 将json串转换成树形结构
   * @param a 树dataList
   * @param idStr 树节点id string 'id'
   * @param pidStr 树parentId string '树parentId'
   * @param chindrenStr children string 'children'
   * @returns {Array}
   */
  transData(a, idStr, pidStr, chindrenStr, extraParameter) {
    const r = []
    const hash = {}
    const id = idStr
    const pid = pidStr
    const children = chindrenStr
    const len = a.length
    let i = 0
    let j = 0
    for (; i < len; i++) {
      hash[a[i][id]] = a[i]
    }
    for (; j < len; j++) {
      const aVal = a[j]
      const hashVP = hash[aVal[pid]]
      if (hashVP) {
        !hashVP[children] && (hashVP[children] = [])
        hashVP[children].push(aVal)
      } else {
        r.push(aVal)
      }
      // 查找已部署节点id集
      if (extraParameter && aVal.state === '1') extraParameter.push(aVal.id)
    }
    return r
  },
  setHeightNew: (cell, count, extra) => {
    return cell * count + extra
  },
  // setHeight: (type) => {
  //   let screenHeight = window.screen.height
  //   console.log(screenHeight, 'screenHeight')
  //   if (type == 1) {
  //     if (900 < screenHeight <= 1080) {
  //       return 10 * 40 + 42
  //     }
  //   } else if (type == 2) {
  //     if (screenHeight <= 900 && 768 < screenHeight) {
  //       return 3 * 40 + 52
  //     }
  //     // if (901 < screenHeight <= 1080) {
  //     if (screenHeight <= 1080 && 901 < screenHeight) {
  //       return 5 * 40 + 52
  //     }
  //   }
  //   if (type == 3) {
  //     if (screenHeight <= 768) {
  //       return 160
  //     }
  //     if (screenHeight <= 900 && 768 <= screenHeight) {
  //       return 2 * 40 + 52
  //     }
  //     if (900 < screenHeight && screenHeight <= 1080) {
  //       return 5 * 40 + 52
  //     }
  //   }
  //   if (type == 4) {
  //     if (screenHeight <= 768) {
  //       return 215
  //     }
  //     if (screenHeight <= 900 && 768 < screenHeight) {
  //       return 3 * 57 + 52
  //     }
  //     if (screenHeight <= 1080 && 901 < screenHeight) {
  //       return 6 * 57 + 52
  //     }
  //   }
  //   if (type == 5) {
  //     if (screenHeight <= 768) {
  //       return 5 * 40 - 12
  //     }
  //     if (screenHeight <= 900 && 768 < screenHeight) {
  //       return 5 * 40 + 49
  //     }
  //     if (screenHeight <= 1080 && 901 < screenHeight) {
  //       return 6 * 40
  //     }
  //   }
  //   if (type == 6) {
  //     if (screenHeight <= 768) {
  //       return 10 * 30 + 45
  //     }
  //     if (screenHeight <= 900 && 768 < screenHeight) {
  //       return 10 * 30 + 25
  //     }
  //     if (900 < screenHeight <= 1080) {
  //       return 10 * 40 + 49
  //     }
  //   }
  //   if (type == 7) {
  //     if (screenHeight <= 768) {
  //       return 10 * 30 + 25
  //     }
  //     if (screenHeight <= 900 && 768 < screenHeight) {
  //       return 8 * 40 + 52
  //     }
  //     if (screenHeight <= 1080 && 901 < screenHeight) {
  //       return 13 * 40 + 52
  //     }
  //   }
  //   if (type == 8) {
  //     if (900 < screenHeight <= 1080) {
  //       return 7 * 40 + 52
  //     }
  //     if (768 <= screenHeight < 900) {
  //       return 7 * 30 + 52
  //     }
  //   }
  //   if (type == 9) {
  //     if (screenHeight <= 768) {
  //       return 12 * 30
  //     }
  //     if (screenHeight <= 900 && 768 < screenHeight) {
  //       return 9 * 30 + 48
  //     }
  //     if (screenHeight <= 1080 && 901 < screenHeight) {
  //       return 14 * 40 + 48
  //     }
  //   }
  //   if (type == 10) {
  //     if (screenHeight <= 768) {
  //       return 6 * 30 + 10
  //     }
  //     if (screenHeight <= 900 && 768 < screenHeight) {
  //       return 4 * 50 + 20
  //     }
  //     if (screenHeight <= 1080 && 901 < screenHeight) {
  //       return 7 * 50 + 20
  //     }
  //   }
  //   if (type == 11) {
  //     if (screenHeight <= 768) {
  //       return 14 * 30 + 10
  //     }
  //     if (screenHeight <= 900 && 768 < screenHeight) {
  //       return 10 * 30 + 25
  //     }
  //     if (screenHeight <= 1080 && 901 < screenHeight) {
  //       return 15 * 40 + 25
  //     }
  //   }
  //   if (type == 12) {
  //     if (screenHeight <= 768) {
  //       return 440
  //     }
  //     if (screenHeight <= 900 && 768 < screenHeight) {
  //       540
  //     }
  //     if (screenHeight <= 1080 && 901 < screenHeight) {
  //       return 15 * 40 + 35
  //     }
  //   }
  //   if (type == 13) {
  //     if (screenHeight <= 768) {
  //       return 15 * 30 + 25
  //     }
  //     if (screenHeight <= 900 && 768 < screenHeight) {
  //       return 11 * 30 + 35
  //     }
  //     if (screenHeight <= 1080 && 901 < screenHeight) {
  //       return 16 * 40 + 35
  //     }
  //   }
  //   if (type == 14) {
  //     if (screenHeight <= 768) {
  //       return 7 * 31 - 30
  //     }
  //     if (screenHeight <= 900 && 768 <= screenHeight) {
  //       return 2 * 40 + 52
  //     }
  //     if (900 < screenHeight && screenHeight <= 1080) {
  //       return 6 * 40 + 46
  //     }
  //   }
  //   if (type == 15) {
  //     if (screenHeight <= 768) {
  //       return 3 * 40 + 50
  //     }
  //     if (screenHeight <= 900 && 768 < screenHeight) {
  //       return 5 * 40 + 20
  //     }
  //     if (900 < screenHeight && screenHeight <= 1080) {
  //       return 5 * 40 + 10
  //     }
  //   }
  //   if (type == 16) {
  //     if (screenHeight <= 768) {
  //       return 510
  //     }
  //     if (screenHeight <= 900 && 768 < screenHeight) {
  //       return 540
  //     }
  //     if (screenHeight <= 1080 && 901 < screenHeight) {
  //       return 15 * 40 + 35
  //     }
  //   }
  //   if (type == 17) {
  //     if (screenHeight <= 768) {
  //       return 5 * 31
  //     }
  //     if (screenHeight <= 900 && 768 <= screenHeight) {
  //       return 2 * 40 + 52
  //     }
  //     if (900 < screenHeight && screenHeight <= 1080) {
  //       return 6 * 40 + 46
  //     }
  //   }
  //   if (type == 18) {
  //     if (screenHeight <= 768) {
  //       return 300
  //     }
  //     if (screenHeight <= 900 && 768 < screenHeight) {
  //       return 5 * 40 + 49
  //     }
  //     if (screenHeight <= 1080 && 901 < screenHeight) {
  //       return 10 * 40 + 49
  //     }
  //   }
  //   if (type == 19) {
  //     if (screenHeight <= 768) {
  //       return 300
  //     }
  //     if (screenHeight <= 900 && 768 < screenHeight) {
  //       return 5 * 40 + 49
  //     }
  //     if (screenHeight <= 1080 && 901 < screenHeight) {
  //       return 9 * 40 + 25
  //     }
  //   }
  //   if (type == 20) {
  //     if (screenHeight <= 768) {
  //       return 7 * 30 + 10
  //     }
  //     if (screenHeight <= 900 && 768 < screenHeight) {
  //       return 8 * 30 + 25
  //     }
  //     if (screenHeight <= 1080 && 901 < screenHeight) {
  //       return 13 * 40
  //     }
  //   }
  // },
  tableLabel: (type) => {
    const labelObj = {
      init: '暂无数据',
      search: '未查到任何记录，请放大查询条件试试！',
      offLine: '查询失败，请检查您的网络…'
    }
    return labelObj[type] || '暂无数据'
  },
  // 用户名
  validateUserName: (val) => {
    if (!val) {
      return true
    } else {
      const reg = /[a-zA-Z0-9_]{1,18}$/
      return reg.test(val)
    }
  },
  // 名称/项目
  checkedProject: (data) => {
    var regu = /^[\u4e00-\u9fa5_a-zA-Z0-9]{2,50}$/
    var re = new RegExp(regu)
    if (data) {
      if (re.test(data)) {
        return true
      } else {
        return false
      }
    } else {
      return false
    }
  },
  // 中文字母数字下划线正则
  checkedName: (data) => {
    var regu = /^[\u4e00-\u9fa5_a-zA-Z0-9]{1,20}$/
    var re = new RegExp(regu)
    if (data) {
      if (re.test(data)) {
        return true
      } else {
        return false
      }
    } else {
      return false
    }
  },
  // 密码校验
  checkedpassWord: (data) => {
    var regu = /^[a-zA-Z0-9!@#$%^&*]{6,16}$/
    var re = new RegExp(regu)
    if (data) {
      if (re.test(data)) {
        return true
      } else {
        return false
      }
    } else {
      return false
    }
  },
  // 手机号验证
  checkedPhone: (data) => {
    var regu = /^1[34578]\d{9}$/
    var re = new RegExp(regu)
    if (data) {
      if (re.test(data)) {
        return true
      } else {
        return false
      }
    } else {
      return false
    }
  },
  // 真实姓名
  checkedCompellation: (data) => {
    var regu = /^([\u4e00-\u9fa5]{2,20})$/
    var re = new RegExp(regu)
    if (data) {
      if (re.test(data)) {
        return true
      } else {
        return false
      }
    } else {
      return false
    }
  },
  // 身份证号
  checkedidentity: (data) => {
    var regu = /^[1-9]\d{5}(18|19|([23]\d))\d{2}((0[1-9])|(10|11|12))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$/ || /^[1-9]\d{5}\d{2}((0[1-9])|(10|11|12))(([0-2][1-9])|10|20|30|31)\d{2}[0-9Xx]$/
    var re = new RegExp(regu)
    if (data) {
      if (re.test(data)) {
        return true
      } else {
        return false
      }
    } else {
      return false
    }
  },
  // 开户行
  checkedBank: (data) => {
    var regu = /^[\u4e00-\u9fa5]{1,20}$/
    var re = new RegExp(regu)
    if (data) {
      if (re.test(data)) {
        return true
      } else {
        return false
      }
    } else {
      return false
    }
  },
  // 银行卡
  checkedbackCard: (data) => {
    var regu = /^[0-9]{16,20}$/
    var re = new RegExp(regu)
    if (data) {
      if (re.test(data)) {
        return true
      } else {
        return false
      }
    } else {
      return false
    }
  },
  // 字母数字正则
  checkedCode: (data) => {
    var regu = /^[a-zA-Z0-9]{1,10}$/
    var re = new RegExp(regu)
    if (data) {
      if (re.test(data)) {
        return true
      } else {
        return false
      }
    } else {
      return false
    }
  },
  positiveNumberRule: (data) => {
    var regu = /^[0-9]{1,15}$/
    var re = new RegExp(regu)
    if (data !== '') {
      if (re.test(data)) {
        return true
      } else {
        return false
      }
    } else {
      return false
    }
  },
  // 排序正则 1-20个数字
  checkedSort: (data) => {
    var regu = /^[0-9]{1,20}$/
    var re = new RegExp(regu)
    if (data) {
      if (re.test(data)) {
        return true
      } else {
        return false
      }
    } else {
      return false
    }
  },
  // 设定宽度
  setWidth: (type) => {
    const screenWidth = window.screen.width
    if (type === 1) {
      if (screenWidth >= 1366 && screenWidth < 1600) {
        return 3 * 57 + 52
      }
    }
  },
  // 设定高度
  setHeight: (type) => {
    const screenHeight = window.screen.height
    if (type === 1) {
      if (screenHeight > 900 && screenHeight <= 1080) {
        return 10 * 40 + 42
      }
    }
    if (type === 2) {
      if (screenHeight > 900 && screenHeight <= 1080) {
        return 14 * 40 + 42
      } else if (screenHeight < 900) {
        return 340
      }
    }
    if (type === 3) {
      if (screenHeight > 900 && screenHeight <= 1080) {
        return 620
      } else if (screenHeight < 900) {
        return 372
      }
    }
    if (type === 4) {
      if (screenHeight > 900 && screenHeight <= 1080) {
        return 693
      } else if (screenHeight < 900) {
        return 436
      }
    }
    if (type === 5) {
      if (screenHeight > 900 && screenHeight <= 1080) {
        return 680
      } else if (screenHeight < 900) {
        return 410
      }
    }
    if (type === 6) {
      if (screenHeight > 900 && screenHeight <= 1080) {
        return 353
      } else if (screenHeight < 900) {
        return 340
      }
    }
    if (type === 7) {
      if (screenHeight > 900 && screenHeight <= 1080) {
        return 280
      } else if (screenHeight < 900) {
        return 200
      }
    }
    if (type === 11) {
      if (screenHeight <= 900 && screenHeight >= 768) {
        return 10 * 30 + 25
      }
      if (screenHeight <= 1080 && screenHeight > 901) {
        return 15 * 40 + 36
      } else {
        return 347
      }
    }
    if (type === 16) {
      if (screenHeight <= 900 && screenHeight >= 768) {
        return 13 * 30 + 15
      }
      if (screenHeight > 900 && screenHeight <= 1080) {
        return 18 * 40 + 24
      } else {
        return 20 * 20 + 84
      }
    }
  },
  setCell: (type) => {
    const screenWidth = window.screen.width
    if (type === 3) {
      if (screenWidth > 1600 && screenWidth <= 1920) {
        return { padding: ' 8px' }
      }
      if (screenWidth <= 1600) {
        return { padding: '3px 8px' }
      }
    }
  },
  setHeaderCell: (type) => {
    const screenWidth = window.screen.width
    if (type === 3) {
      if (screenWidth > 1600 && screenWidth <= 1920) {
        return { background: '#f2f4fbd1', padding: ' 8px' }
      }
      if (screenWidth <= 1600) {
        return { background: '#f2f4fbd1', padding: '3px 8px' }
      }
    }
  },
  // getDateByStr: (str) => {
  //   return moment(str).toDate()
  // },
  // getDate: (m) => {
  //   return moment(m)
  // },
  // 时间转字符串，转成yyyy-MM-dd HH:mm:SS格式
  dateToStr: (stamp = new Date) => {
    var dateTime = new Date(stamp)
    var year = dateTime.getFullYear()
    var month = dateTime.getMonth() + 1
    var date = dateTime.getDate()
    var hour = dateTime.getHours()
    var minutes = dateTime.getMinutes()
    var second = dateTime.getSeconds()
    if (month < 10) {
      month = '0' + month
    }
    if (date < 10) {
      date = '0' + date
    }
    if (hour < 10) {
      hour = '0' + hour
    }
    if (minutes < 10) {
      minutes = '0' + minutes
    }
    if (second < 10) {
      second = '0' + second
    }
    return year + '-' + month + '-' + date + ' ' + hour + ':' + minutes + ':' + second
  },
  getDate: (type = 'day') => {
    if (type === 'day') {
      return moment().format('YYYY-MM-DD')
    } else if (type === 'week') {
      return moment().startOf('week').format('YYYY-MM-DD')
    } else if (type === 'month') {
      return moment().startOf('month').format('YYYY-MM-DD')
    }
  },
  getEditDate: (type = 'day', arrow, date) => {
    let x = Number(moment().format('x'))
    if (arrow === 'left') {
      if (type === 'day') {
        return moment(date).subtract(1, 'd').format('YYYY-MM-DD')
      } else if (type === 'week') {
        return moment(date).subtract(1, 'w').format('YYYY-MM-DD')
        // return [moment(date[0]).subtract(1, 'w').format('YYYY-MM-DD'), moment(date[1]).subtract(7, 'd').format('YYYY-MM-DD')]
      } else if (type === 'month') {
        return moment(date).subtract(1, 'M').format('YYYY-MM-DD')
      }
    } else if (arrow === 'right') {
      if (type === 'day' && x >= Number(moment(date).add(1, 'd').format('x'))) {
        return moment(date).add(1, 'd').format('YYYY-MM-DD')
      } else if (type === 'week' && x >= Number(moment(date).add(1, 'w').format('x'))) {
        return moment(date).add(1, 'w').format('YYYY-MM-DD')
        // return [moment(date[0]).add(1, 'w').format('YYYY-MM-DD'), moment(date[1]).add(1, 'w').format('YYYY-MM-DD')]
      } else if (type === 'month' && x >= Number(moment(date).add(1, 'M').format('x'))) {
        return moment(date).add(1, 'M').format('YYYY-MM-DD')
      } else {
        return date
      }
    }
  },
  getCurrentDate: () => {
    // 获取当前时间
    return moment().format('YYYY-MM-DD HH:mm:ss')
  },
  getUserInfo: (key) => {
    return localStorage.getItem(key) ? JSON.parse(localStorage.getItem('LOGINDATA')) : {}
  },
  // 二次提醒
  confirm: (self, message, title, type, confirmButtonText, cancelButtonText, callback) => {
    self
      .$confirm(message, title || '提示', {
        type: type,
        confirmButtonText: confirmButtonText || '确定',
        cancelButtonText: cancelButtonText || '取消'
      })
      .then(() => {
        callback(new Error('confirm'))
      })
      .catch(() => {
        callback(new Error('cancel'))
      })
  },
  formatMenus: (menus) => {
    const list = []
    menus.forEach((item) => {
      if (item.parentId === '0') {
        const children = []

        menus.forEach((child) => {
          if (child.parentId === item.id) {
            child.children = []
            children.push(child)
          }
        })

        item.children = children
        list.push(item)
      }
    })

    return list
  },
  listToTree: (list, id, pid) => {
    const temp = {}
    const tree = []
    list.forEach((item) => {
      temp[item[id]] = item
    })
    for (const i in temp) {
      if (temp[i][pid] === '' || temp[i][pid] === null || temp[i][pid] === undefined) {
        temp[i][pid] = 0
      }
      if (temp[i][pid] !== 0 && temp[temp[i][pid]] !== undefined) {
        if (!temp[temp[i][pid]].children) {
          temp[temp[i][pid]].children = []
        }
        temp[temp[i][pid]].children.push(temp[i])
      } else {
        tree.push(temp[i])
      }
    }
    return tree
  },
  randomRgbColor: (type) => {
    //随机生成RGB颜色
    var r = Math.floor(Math.random() * 256) //随机生成256以内r值
    var g = Math.floor(Math.random() * 256) //随机生成256以内g值
    var b = Math.floor(Math.random() * 256) //随机生成256以内b值
    if (type == 'array') {
      return [`rgba(${r},${g},${b},0.3)`, `rgba(${r},${g},${b},1)`]
    }
    return `rgb(${r},${g},${b}})` //返回rgb(r,g,b)格式颜色
  },
  ListTree,
  transData(a, idStr, pidStr, chindrenStr, extraParameter) {
    let r = [],
      hash = {},
      id = idStr,
      pid = pidStr,
      children = chindrenStr,
      i = 0,
      j = 0,
      len = a.length
    for (; i < len; i++) {
      hash[a[i][id]] = a[i]
    }
    for (; j < len; j++) {
      let aVal = a[j],
        hashVP = hash[aVal[pid]]
      if (hashVP) {
        !hashVP[children] && (hashVP[children] = [])
        hashVP[children].push(aVal)
      } else {
        r.push(aVal)
      }
      //            查找已部署节点id集
      if (extraParameter && aVal.state == '1') extraParameter.push(aVal.id)
    }
    return r
  },
  /**
   * minio 图片前缀改为访问地址前缀
   * @params url === 全拼或半拼图片地址
   * @return 地址全拼
   * */
  imgUrlTranslation(url) {
    // const baseAddress = __PATH.VUE_MINIO_API
    const baseAddress = store.state.picPrefix
    // 用户信息未返回可替换的基础地址，直接返回原地址
    if (!baseAddress || !url) return url
    // 定义一个正则表达式来匹配以http或https开头的ip或域名地址
    const fullAddressRegex = /^((https?|ftp):\/\/(?:\d+\.\d+\.\d+\.\d+(?::\d+)?|\w+\.\w+\.\w+(?::\d+)?))/
    // 使用正则表达式检查输入地址是否匹配完整地址格式
    const match = url.match(fullAddressRegex)
    if (match) {
      // 如果匹配到完整地址，提取出半地址部分
      let halfAddress = url.replace(match[0], '')
      if (baseAddress.at(-1) === '/' && halfAddress[0] === '/') {
        // 如果半地址部分以/开头，则去掉/,否则会重复
        halfAddress = halfAddress.slice(1)
      }
      if (baseAddress.at(-1) !== '/' && halfAddress[0] !== '/') {
        halfAddress = '/' + halfAddress
      }
      // 替换完整地址部分为新地址，同时保留半地址
      const fullAddress = baseAddress + halfAddress
      return fullAddress
    } else {
      // 如果没有匹配到完整地址，则拼接地址
      if (url[0] !== '/') {
        url = '/' + url
      }
      const fullAddress = baseAddress + url
      return fullAddress
    }
  },
  /**
  * @param: fileName - 文件名称
  * @param: 数据返回 1) 无后缀匹配 - false
  * @param: 数据返回 2) 匹配图片 - image
  * @param: 数据返回 3) 匹配 txt - txt
  * @param: 数据返回 4) 匹配 excel - excel
  * @param: 数据返回 5) 匹配 word - word
  * @param: 数据返回 6) 匹配 pdf - pdf
  * @param: 数据返回 7) 匹配 ppt - ppt
  * @param: 数据返回 8) 匹配 视频 - video
  * @param: 数据返回 9) 匹配 音频 - radio
  * @param: 数据返回 10) 其他匹配项 - other
  **/
  matchFileSuffixType (fileName) {
    // 后缀获取
    var suffix = ''
    // 获取类型结果
    var result = ''
    try {
      var flieArr = fileName.split('.')
      suffix = flieArr[flieArr.length - 1]
    } catch (err) {
      suffix = ''
    }
    // fileName无后缀返回 false
    if (!suffix) {
      result = false
      return result
    }
    // 图片格式
    var imglist = ['png', 'jpg', 'jpeg', 'bmp', 'gif']
    // 进行图片匹配
    result = imglist.some(function (item) {
      return item == suffix
    })
    if (result) {
      result = 'image'
      return result
    }
    // 匹配txt
    var txtlist = ['txt']
    result = txtlist.some(function (item) {
      return item == suffix
    })
    if (result) {
      result = 'txt'
      return result
    }
    // 匹配 excel
    var excelist = ['xls', 'xlsx']
    result = excelist.some(function (item) {
      return item == suffix
    })
    if (result) {
      result = 'excel'
      return result
    }
    // 匹配 word
    var wordlist = ['doc', 'docx']
    result = wordlist.some(function (item) {
      return item == suffix
    })
    if (result) {
      result = 'word'
      return result
    }
    // 匹配 pdf
    var pdflist = ['pdf']
    result = pdflist.some(function (item) {
      return item == suffix
    })
    if (result) {
      result = 'pdf'
      return result
    }
    // 匹配 ppt
    var pptlist = ['ppt']
    result = pptlist.some(function (item) {
      return item == suffix
    })
    if (result) {
      result = 'ppt'
      return result
    }
    // 匹配 视频
    var videolist = ['mp4', 'm2v', 'mkv']
    result = videolist.some(function (item) {
      return item == suffix
    })
    if (result) {
      result = 'video'
      return result
    }
    // 匹配 音频
    var radiolist = ['mp3', 'wav', 'wmv']
    result = radiolist.some(function (item) {
      return item == suffix
    })
    if (result) {
      result = 'radio'
      return result
    }
    // 其他 文件类型
    result = 'other'
    return result
  },
  /**
    根据报警列表关联的设备进入设备对应的模型实景，对象中必带参数
  * @param: projectCode 根据监测系统找到对应监测与客户端的wpfkey
  * @param: DeviceCode 模型编码modelCode
  * @param: assetsId 设备id
  * @param: assetsName 设备名称
  * @param: spaceCode  设备对应空间id
  **/
  alarmDataRowRealView(row) {
    const viewMonitorData = monitorTypeList.find(e => e.projectCode == row.projectCode)
      // 照明不做跳转
      const noJupmFilterName = ['照明监测']
      if (noJupmFilterName.some(e => e == viewMonitorData?.projectName)) {
        return
      }
      // 受filterName包含的为使用DeviceCode/modelCode跳转（关联模型），不是的为使用assetsid跳转（关联表计）
      const filterName = hasModelCodeFilterProjectName
      const filterMonitorFlag = filterName.some(e => e == viewMonitorData?.projectName)
      console.log('查看实景', row, viewMonitorData)
      // 如果关联了设备即跳转设备详情页
      if (viewMonitorData.wpfKey) {
        if (filterMonitorFlag) {
          if (row.DeviceCode) {
            const params = {
              DeviceCode: row.DeviceCode,
              DeviceName: row?.assetsName,
              menuName: viewMonitorData.wpfKey,
              assetsId: row.assetsId,
              projectCode: viewMonitorData.projectCode,
              spaceCode: row?.spaceCode
            }
            console.log(params, 'EquipmentRealView')
            try {
              window.chrome.webview.hostObjects.sync.bridge.EquipmentRealView(JSON.stringify(params))
            } catch (error) {}
          } else {
            Message.warning('当前设备暂未录入模型编码!')
          }
        } else {
          if (row.assetsId) {
            const params = {
              DeviceCode: row.DeviceCode,
              menuName: viewMonitorData.wpfKey,
              assetsId: row.assetsId,
              projectCode: viewMonitorData.projectCode,
              spaceCode: row?.spaceCode
            }
            console.log(params, 'EquipmentRealView')
            try {
              window.chrome.webview.hostObjects.sync.bridge.EquipmentRealView(JSON.stringify(params))
            } catch (error) {}
          } else {
            Message.warning('当前设备暂未关联资产!')
          }
        }
      } else {
        // Message.warning('当前设备暂不支持关联!')
      }
  },
  // 节流
  throttle(func, wait) {
    let inThrottle, lastFunc, timerId;
    return function() {
      const context = this;
      const args = arguments;
      if (!inThrottle) {
        func.apply(context, args);
        inThrottle = true;
        setTimeout(() => inThrottle = false, wait);
      } else {
        clearTimeout(timerId);
        timerId = setTimeout(() => {
          if (!inThrottle) {
            func.apply(context, args);
            inThrottle = false;
          }
        }, wait);
      }
    }
  },
  // 给客户端发消息关闭/开启弹窗
  showDialog() {
    if (dialogTimer) {
      clearTimeout(dialogTimer)
    }
    dialogTimer = setTimeout(() => {
      let dialogNodes = document.querySelectorAll('.el-dialog__wrapper')
      let arr = []
      for (let i = 0; i < dialogNodes.length; i++) {
        arr.push(dialogNodes[i].style.display)
      }
      console.log(1111, arr.filter(v => v != 'none').length)
      if (arr.filter(v => v != 'none').length) {
        try {
          console.log(true)
          window.chrome.webview.hostObjects.sync.bridge.showDialog(true)
        } catch (error) {}
      } else {
        try {
          console.log(false)
          window.chrome.webview.hostObjects.sync.bridge.showDialog(false)
        } catch (error) {}
      }
    }, 500)
  }

}
// function formatDate(date) {
//   const myyear = date.getFullYear()
//   let mymonth = date.getMonth() + 1
//   let myweekday = date.getDate()
//   if (mymonth < 10) {
//     mymonth = '0' + mymonth
//   }
//   if (myweekday < 10) {
//     myweekday = '0' + myweekday
//   }
//   return myyear + '-' + mymonth + '-' + myweekday
// }
