<template>
  <div class="tree-main">
    <vue2-org-tree :data="treeData" :horizontal="false" label-class-name="bg-none" :render-content="renderContent" :collapsable="false" />
  </div>
</template>

<script lang="jsx">
import alarmTypeIcon from '@/assets/images/plan/alarmTypeIcon.png'
import Vue2OrgTree from 'vue2-org-tree'
import { GetSelectedDept } from '@/utils/wartimeMode'
export default {
  name: 'flowChart',
  components: {
    Vue2OrgTree
  },
  props: {
    type: {
      type: String,
      default: 'edit' // view edit
    },
    isPreview: {
      type: Boolean,
      default: false
    },
    currentStepId: {
      type: String,
      default: ''
    },
    eventData: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  data() {
    return {
      depts: {}, // 部门列表
      treeData: {}
    }
  },
  computed: {},
  mounted() {
    this.getUseDeptList()
  },
  methods: {
    setTreeData() {
      this.treeData = {
        id: '0',
        name: '系统报警',
        step: 1,
        children: [
          {
            id: '1',
            name: '误报',
            step: 2,
            tip: '点击误报按钮',
            notify: true,
            action: false,
            children: [
              {
                id: '1-1',
                name: '解除报警',
                step: 3,
                tip: '点击解除报警按钮'
              }
            ]
          },
          {
            id: '2',
            name: '演习',
            step: 2,
            tip: '点击演习按钮',
            notify: true,
            action: false,
            stepType: 1,
            children: [
              {
                id: '2-1',
                name: '取消预案',
                step: 3,
                tip: '点击取消启用预案按钮',
                children: [
                  {
                    id: '2-1-1',
                    name: '解除报警',
                    step: 4,
                    tip: '点击解除报警按钮'
                  }
                ]
              },
              {
                id: '2-2',
                name: '启动预案',
                step: 3,
                tip: '点击确认按钮',
                notify: true,
                action: false,
                stepType: 2,
                children: [
                  {
                    id: '2-2-1',
                    name: '节点名称',
                    step: 4,
                    tip: '请添加节点动作',
                    notify: true,
                    action: true,
                    stepType: 3,
                    children: [
                      {
                        id: '2-2-1-1',
                        name: '解除报警',
                        step: 5,
                        tip: '点击解除报警按钮'
                      }
                    ]
                  }
                ]
              }
            ]
          },
          {
            id: '3',
            name: '确警',
            step: 2,
            tip: '点击确警按钮',
            notify: true,
            action: false,
            stepType: 4,
            children: [
              {
                id: '3-1',
                name: '取消预案',
                step: 3,
                tip: '点击取消启用预案按钮',
                children: [
                  {
                    id: '3-1-1',
                    name: '解除报警',
                    step: 4,
                    tip: '点击解除报警按钮'
                  }
                ]
              },
              {
                id: '3-2',
                name: '启动预案',
                step: 3,
                tip: '点击确认按钮',
                notify: true,
                action: false,
                stepType: 5,
                children: [
                  {
                    id: '3-2-1',
                    name: '节点名称',
                    step: 4,
                    tip: '请添加节点动作',
                    notify: true,
                    action: true,
                    stepType: 6,
                    children: [
                      {
                        id: '3-2-1-1',
                        name: '解除报警',
                        step: 5,
                        tip: '点击解除报警按钮'
                      }
                    ]
                  }
                ]
              }
            ]
          }
        ]
      }
    },
    // 获取科室
    getUseDeptList() {
      GetSelectedDept().then((res) => {
        if (res.data.code == '200') {
          this.$tools.transData(res.data.data, 'id', 'parentId', 'children').forEach(v => {
            this.depts[v.id] = v.deptName
          })
          this.setTreeData()
        }
      })
    },
    // 流程图渲染方法
    renderContent(h, data) {
      let currentNodeData, startContent, isNotice, isWarn, isConfirm, noticeContent, warnContent, confirmContent = ''
      if (data.name == '系统报警') {
        return (
          <div class={['tree-item', 'alarm-item', this.currentStepId == data.id && this.isPreview ? 'current-step' : '']}>
            <p class="item-title">{data.name}</p>
            <p class="item-step">步骤{data.step}</p>
            <p class="item-tips">报警面板样式<span>可设置提示内容</span></p>
            <div class="item-main alarm-main">
              <p class="alarm-tyle">
                <img src={alarmTypeIcon} />
                <span>报警类型</span>
              </p>
              <p class="alarm-info">
                <span class="alarm-label">报警位置：</span>
                <span class="alarm-value">门诊楼西侧花坛</span>
              </p>
              <p class="alarm-info">
                <span class="alarm-label">报警设备：</span>
                <span class="alarm-value">跌倒报警</span>
              </p>
              <p class="alarm-info">
                <span class="alarm-label">报警时间：</span>
                <span class="alarm-value">2024-02-28  12:09:13</span>
              </p>
              <p class="alarm-info">
                <span class="alarm-label">报警级别：</span>
                <span class="alarmLevel">紧急</span>
              </p>
              <div class="alarm-btns">
                <el-button >误报</el-button>
                <el-button type="primary">演习</el-button>
                <el-button type="primary">确警</el-button>
              </div>
            </div>
          </div>
        )
      } else if (data.name == '误报' || data.name == '解除报警' || data.name == '取消预案') {
        return (
          <div class={['tree-item', this.currentStepId == data.id && this.isPreview ? 'current-step' : '']}>
            <p class="item-title">{data.name}</p>
            <p class="item-step">步骤{data.step}</p>
            <div class="item-main end-main">
              <p class="end-label">触发条件</p>
              <p class="end-value">{data.tip}</p>
            </div>
          </div>
        )
      } else if (data.name == '演习' || data.name == '确警' || data.name == '启动预案' || (data.name == '节点名称' || data.step == 4)) {
        currentNodeData = this.eventData[data.stepType]
        isNotice = Object.keys(currentNodeData).includes('noticeEventList') && currentNodeData.noticeEventList.length
        isWarn = Object.keys(currentNodeData).includes('warnEventList') && currentNodeData.warnEventList.length
        isConfirm = Object.keys(currentNodeData).includes('confirmEventList') && currentNodeData.confirmEventList.length
        if (!Object.keys(currentNodeData).length) {
          startContent = (
            <div class="item-main start-main">
              <p class="start-label">触发条件</p>
              <p class="start-value">{data.tip}</p>
            </div>
          )
        } else {
          if (isNotice) {
            noticeContent = (
              <div class="start-main-item">
                <p class="start-main-item-title">{currentNodeData.noticeEventList[0].eventName}</p>
                {
                  currentNodeData.noticeEventList.map(item => {
                    return (
                      <p class="start-main-item-value">
                        <el-tooltip placement="left" content={item.noticeType == 0 ? '通知人员：' + item.personName : '通知部门：' + item.departCode.split(',').map(v => this.depts[v])}>
                          <span>{item.noticeType == 0 ? '通知人员：' + item.personName : '通知部门：' + item.departCode.split(',').map(v => this.depts[v])}</span>
                        </el-tooltip>
                        <i class="el-icon-arrow-right"></i>
                      </p>
                    )
                  })
                }
              </div>
            )
          }
          if (isWarn) {
            warnContent = (
              <div class="start-main-item">
                <p class="start-main-item-title">{currentNodeData.warnEventList[0].eventName}</p>
                {
                  currentNodeData.warnEventList.map(item => {
                    return (
                      <p class="start-main-item-value">
                        <el-tooltip placement="left" content={item.warnContent}>
                          <span>{item.warnContent}</span>
                        </el-tooltip>
                        <i class="el-icon-arrow-right"></i>
                      </p>
                    )
                  })
                }
              </div>
            )
          }
          if (isConfirm) {
            confirmContent = (
              <div class="start-main-item">
                <p class="start-main-item-title">{currentNodeData.confirmEventList[0].eventName}</p>
                {
                  currentNodeData.confirmEventList.map(item => {
                    return (
                      <p class="start-main-item-value">
                        <el-tooltip placement="left" content={item.confirmTitle}>
                          <span>{item.confirmTitle}</span>
                        </el-tooltip>
                        <i class="el-icon-arrow-right"></i>
                      </p>
                    )
                  })
                }
              </div>
            )
          }
          startContent = (
            <div>
              {
                isWarn ? <div class="item-main start-main">{warnContent}</div> : ''
              }
              {
                isNotice ? <div class="item-main start-main">{noticeContent}</div> : ''
              }
              {
                isConfirm ? <div class="item-main start-main">{confirmContent}</div> : ''
              }
            </div>
          )
        }
        return (
          <div class={['tree-item', 'start-item', !this.isPreview ? 'start-item-hover' : '', this.currentStepId == data.id && this.isPreview ? 'current-step' : '']}>
            <p class="item-title">{isNotice ? currentNodeData.noticeEventList[0].stepName : data.name}</p>
            {(data.stepType == 3 || data.stepType == 6) && (!isNotice && !isWarn && !isConfirm) ? <i class="el-icon-warning"></i> : ''}
            <p class="item-step">步骤{data.step}</p>
            {startContent}
          </div>
        )
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.tree-main {
  width: 100%;
  height: 100%;
  text-align: center;
  overflow: auto;
  padding-top: 16px;
}
</style>
<style lang="less">
@import './org-tree.less';
</style>
