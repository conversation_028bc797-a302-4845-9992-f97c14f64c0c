<template>
  <el-dialog
    v-if="visible"
    v-dialogDrag
    :modal="false"
    :close-on-click-modal="false"
    :show-close="false"
    title="屏蔽报警"
    width="40%"
    custom-class="screenDialog"
    :visible.sync="visible"
    :before-close="closeDialog"
  >
    <div class="dialog-right">
      <div class="dialog-tc" @click="closeDialog"></div>
    </div>
    <div class="content">
      <el-form ref="ruleForm" :model="form" :rules="rules">
        <el-form-item label="屏蔽时长" prop="minute">
          <el-input v-model.trim="form.minute" placeholder="请输入" style="width: 12.5rem;height: 2.25rem;" onkeyup="value=value.replace(/[^\d]/g,'')">
            <template slot="append">分钟</template>
          </el-input>
        </el-form-item>
        <el-form-item label="屏蔽原因" prop="remarks">
          <el-radio-group v-model="form.radio" size="mini" @input="radioInput">
            <el-radio-button label="2">误报</el-radio-button>
            <el-radio-button label="4">调试</el-radio-button>
          </el-radio-group>
          <el-input v-model.trim="form.remarks" type="textarea" :rows="4" resize="none" placeholder="请输入备注" maxlength="100" show-word-limit style="width: 70%;" class="ipt"> </el-input>
        </el-form-item>
      </el-form>
    </div>
    <span slot="footer" class="dialog-footer">
      <el-button class="sino-button-sure" @click="closeDialog">取 消</el-button>
      <el-button class="sino-button-sure" @click="confirm('ruleForm')">确 定</el-button>
    </span>
  </el-dialog>
</template>
<script>
import { shield } from '@/utils/peaceLeftScreenApi'
export default {
  name: 'screenDialog',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    selectItems: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      form: {
        minute: '15',
        radio: '2',
        remarks: '误报'
      },
      rules: {
        minute: [{ required: true, message: '请输入分钟', trigger: 'change' }],
        remarks: [{ required: true, message: '请输入备注', trigger: 'change' }]
      }
    }
  },
  mounted() {},
  methods: {
    // 关闭弹窗
    closeDialog() {
      this.form.minute = '15'
      this.form.radio = '2'
      this.form.remarks = ' '
      this.$emit('update:visible', !this.visible)
    },
    radioInput() {
      if (this.form.radio === '2') {
        this.form.remarks += '误报'
      } else {
        this.form.remarks += '调试'
      }
    },
    confirm(formName) {
      var alarmObjectId = []
      var incidentType = []
      var alarmId = []
      var params = {
        shield: true,
        alarmAffirm: this.form.radio,
        shieldTime: this.form.minute,
        remarks: this.form.remarks
      }
      this.selectItems.forEach((item) => {
        alarmObjectId.push(item.alarmObjectId)
        incidentType.push(item.incidentType)
        alarmId.push(item.alarmId)
      })
      params.alarmObjectId = alarmObjectId.toString()
      params.incidentType = incidentType.toString()
      params.alarmId = alarmId.toString()
      this.$refs[formName].validate((valid) => {
        if (valid) {
          shield(params).then((res) => {
            if (res.data.code === '200') {
              this.$message({
                message: '屏蔽成功',
                type: 'success'
              })
            } else {
              this.$message.error(res.data.msg)
            }
            this.form.minute = '15'
            this.form.radio = '2'
            this.form.remarks = ''
            this.$emit('update:visible', !this.visible)
          })
        } else {
        }
      })
    }
  }
}
</script>
<style lang="scss" scoped>
::v-deep .el-dialog{
  background: url('@/assets/images/qhdsys/768×453.png') no-repeat center center / 100% 100%;
  .el-dialog__header {
    padding: 10px 20px 10px;
    text-align: center;
    .el-dialog__title {
      height: 20px;
      font-size: 18px;
      font-family: Source Han Sans CN-Regular, Source Han Sans CN;
      font-weight: 400;
      color: #cbdeed;
      line-height: 18px;
    }
  }
  .el-dialog__body {
    height: calc(100% - 40px);
  }
  .el-dialog__footer {
    padding-top: 0;
    padding-bottom: 40px;
    padding-right: 40px;
  }
}
.screenDialog {
  .dialog-right {
    position: absolute;
    top: 17px;
    right: 30px;
    display: flex;
    .dialog-tc {
      width: 36px;
      height: 36px;
      background: url('@/assets/images/qhdsys/bg-icon-gb.png') no-repeat;
    }
    .dialog-tc:hover {
      cursor: pointer;
    }
  }
  .content {
    width: 100%;
    height: 100%;
    padding: 20px !important;
    // background-color: #fff !important;
    ::v-deep .el-radio-group {
      .el-radio-button__orig-radio:checked + .el-radio-button__inner {
        background: #3562db;
        border: none;
        color: #fff;
      }
      .el-radio-button {
        margin-right: 8px;
        border: none;
      }
      .el-radio-button__inner {
        background: rgba(53, 98, 219, 0.2);
        border: none;
        box-shadow: none;
        color: #fff;
      }
      .el-radio-button:last-child .el-radio-button__inner,
      .el-radio-button:first-child .el-radio-button__inner {
        border-radius: 0;
      }
      .el-radio-button:focus:not(.is-focus):not(:active):not(.is-disabled) {
        box-shadow: none;
        border: none;
      }
    }
  }
  .model-dialog {
    padding: 0 !important;
  }
  .ipt {
    margin-top: 15px;
    margin-left: 80px;
  }
  ::v-deep .el-form-item__error {
    height: 20px;
    width: 300px;
    color: #f56c6c;
    font-size: 12px;
    line-height: 20px;
    padding-top: 4px;
    position: absolute;
    left: 78px;
  }
  ::v-deep .el-textarea {
    .el-textarea__inner {
      padding-bottom: 20px;
    }
    .el-input__count {
      line-height: 16px;
    }
  }
}
</style>
