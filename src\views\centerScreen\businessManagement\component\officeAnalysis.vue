<template>
  <div class="main">
    <el-dialog :modal="false" :visible.sync="dialogShow" custom-class="mainDialog main" append-to-body :close-on-click-modal="false" :before-close="closeDialog" class="personDialog">
      <template slot="title">
        <span class="dialog-title">科室医废收集记录</span>
      </template>
      <div class="dialog-content">
        <el-table
          :data="tableData"
          height="calc(100% - 0px)"
          :cell-style="$tools.setCell(3)"
          :header-cell-style="$tools.setHeaderCell(3)"
          style="width: 100%"
          v-loading="tableLoading"
          element-loading-background="rgba(0, 0, 0, 0.2)"
        >
          <el-table-column fixed prop="officeName" align="center" show-overflow-tooltip label="所属科室"></el-table-column>
          <el-table-column fixed prop="count" align="center" show-overflow-tooltip label="医废数量"></el-table-column>
          <el-table-column fixed prop="barCode" align="center" show-overflow-tooltip label="医废编码"></el-table-column>
          <el-table-column fixed prop="wasteType" align="center" show-overflow-tooltip label="医废类型"></el-table-column>
          <el-table-column fixed prop="gatherTime" align="center" show-overflow-tooltip label="收集时间"></el-table-column>
          <el-table-column fixed prop="gatherWeigh" align="center" show-overflow-tooltip label="收集重量">
            <template slot-scope="scope">
              <span>{{ scope.row.gatherWeigh }}kg</span>
            </template>
          </el-table-column>
          <el-table-column fixed prop="receivedPersonName" align="center" show-overflow-tooltip label="收集人员"> </el-table-column>
          <el-table-column fixed prop="receivedSignature" align="center" show-overflow-tooltip label="收集人员签字">
            <template slot-scope="scope">
              <span v-if="!scope.row.receivedSignature || scope.row.receivedSignature === undefined"></span>
              <el-image v-else style="width: 70%; height: 90%" :src="scope.row.receivedSignature" :preview-src-list="scope.row.receivedSignature"> </el-image>
            </template>
          </el-table-column>
          <el-table-column fixed prop="officeSignature" align="center" show-overflow-tooltip label="科室人员签字">
            <template slot-scope="scope">
              <span v-if="!scope.row.officeSignature || scope.row.officeSignature === undefined"></span>
              <el-image v-else style="width: 70%; height: 90%" :src="scope.row.officeSignature" :preview-src-list="scope.row.officeSignature"> </el-image>
            </template>
          </el-table-column>
          <el-table-column fixed prop="inventoryStatus" align="center" show-overflow-tooltip label="状态">
            <template slot-scope="scope">
              <span v-if="scope.row.inventoryStatus === '1'">已收集</span>
              <span v-if="scope.row.inventoryStatus === '2'">已入站</span>
              <span v-if="scope.row.inventoryStatus === '3'">已出站</span>
              <span v-else></span>
            </template>
          </el-table-column>
          <el-table-column fixed show-overflow-tooltip align="center" label="医废追溯">
            <template slot-scope="scope">
              <div class="operationBtn">
                <span @click="selectConfigRowData(scope.row.id)">追溯</span>
              </div>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </el-dialog>
    <template v-if="retrospectShow">
      <retrospect ref="retrospect" :dialogShow="retrospectShow" :detailId="detailId" @retrospectCloseDialog="retrospectCloseDialog"></retrospect>
    </template>
  </div>
</template>
<script>
import { getMedicalWasteDataPageList } from '@/utils/centerScreenApi'
import retrospect from './retrospect.vue'
export default {
  name: 'officeAnalysis',
  components: {
    retrospect
  },
  props: {
    dialogShow: {
      type: Boolean,
      default: false
    },
    officeId: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      tableData: [],
      retrospectShow: false,
      detailId: '',
      tableLoading: false
    }
  },
  watch: {},
  created() {},
  mounted() {
    // this.getDepartMedicalWasteTableList()
  },
  methods: {
    // 获取列表
    getDepartMedicalWasteTableList() {
      const params = {
        currentPage: 1,
        pageSize: 50,
        officeId: this.officeId
      }
      this.tableLoading = true
      getMedicalWasteDataPageList(params).then((res) => {
        const data = res.data.data
        this.tableLoading = false
        if (data.rows.length) {
          this.tableData = data.rows
        } else {
          this.tableData = []
        }
      })
    },
    selectConfigRowData(id) {
      this.detailId = id
      this.retrospectShow = true
      window.chrome.webview.hostObjects.sync.bridge.ShowDialog(true)
    },
    retrospectCloseDialog() {
      this.retrospectShow = false
      window.chrome.webview.hostObjects.sync.bridge.ShowDialog(false)
    },
    // 取消按钮
    closeDialog() {
      this.$emit('configCloseDialog', 'officeAnalysisShow')
    }
  }
}
</script>

<style lang="scss" type="text/css" scoped>
.personDialog {
  pointer-events: none;
  ::v-deep .el-image__error {
    background: center;
  }
}
::v-deep .mainDialog {
  width: 98%;
  height: 40vh;
  margin-top: 54vh !important;
  // background: #031553;
  // border: 1px solid #5996f9;
  background: url('@/assets/images/qhdsys/bottom-table-bg.png') no-repeat;
  background-size: 100% 100%;
  padding: 0 35px;
  pointer-events: auto;
  .dialog-title {
    display: inline-block;
    width: 38%;
  }
  .el-dialog__body {
    padding: 0;
    height: calc(100% - 10px);
    .dialog-content {
      width: 100%;
      height: 100%;
      .el-table {
        border: none !important;
        .redBg {
          background: #341f51;
        }
      }
    }
  }
}
</style>
