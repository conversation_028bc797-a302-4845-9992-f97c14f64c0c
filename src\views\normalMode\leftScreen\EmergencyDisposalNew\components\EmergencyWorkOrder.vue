<template>
  <div class='component EmergencyWorkOrder'>
    <div class="EmergencyWorkOrder__description">
      <div class="content-head">
        <div class="content-head__title">工单信息</div>
        <div class="content-head__extend">
          <!-- <el-button class="new-edition" icon="el-icon-plus">新建工单</el-button> -->
          <el-tooltip slot="title-right" popper-class="tooltip" effect="light" placement="bottom">
            <div slot="content" class="work-order-type" style="width: 90px;">
              <div v-for="item in createWorkOrderTypes" :key="item.id"
                @click="selectWorkOrderType(item.id, item.workTypeName)">
                {{ item.workTypeName }}</div>
            </div>
            <el-button v-if="alarmStatus != 2" class="sino-button-sure" icon="el-icon-plus">新建工单</el-button>
          </el-tooltip>
        </div>
      </div>
      <div class="EmergencyWorkOrder__description__content">
        <el-collapse :value="currentPanel" accordion @change="onCollapseChange">
          <el-collapse-item class="EmergencyWorkOrder__description__order" v-for="item of orderList" :key="item.workNum"
            :name="item.workNum" accordion>
            <template v-slot:title>
              <div class="EmergencyWorkOrder__description__order__title">
                <div class="EmergencyWorkOrder__description__order__title__icon">
                  <img src="@/assets/images/common/icon-list.png">
                </div>
                <ul class="description__content EmergencyWorkOrder__description__order__title__content">
                  <li>
                    <div>工单号</div>
                    <div>
                      <span class="order_number">{{ item.workNum }}</span>
                      <span class="tag" :class="'tag--' + item.flowcode">{{ item.flowtype }}</span>
                    </div>
                  </li>
                  <li>
                    <div>工单类型</div>
                    <div>{{ item.workTypeName }}</div>
                  </li>
                </ul>
              </div>
            </template>
            <div class="EmergencyWorkOrder__description__order__detail" v-if="workNumLoadList.includes(item.workNum)">
              <WorkOrderTaskList :row-data="{ id: item.workNum }" />
            </div>
          </el-collapse-item>
        </el-collapse>
      </div>
    </div>
    <template v-if="workOrderDealShow">
      <CreatedWorkOrder :workOrderDealShow.sync="workOrderDealShow" :workTypeCode="olgTaskManagement.workTypeCode"
        :workTypeName="olgTaskManagement.workTypeName" :alarmId="alarmId" :projectCode="alarmDetail.projectCode"
        :spaceId="alarmDetail.alarmSpaceId" dealType="add" @workOrderSure="workOrderSure" />
    </template>
  </div>
</template>


<script>
export default {
  name: 'EmergencyWorkOrder',
  components: {
    CreatedWorkOrder: () => import('../../components/CreatedWorkOrder'),
    WorkOrderTaskList: () => import('../../../rightScreen/components/workOrderDetailList')
  },
  props: {
    alarmId: {
      type: String
    },
    alarmStatus: {
      type: Number,
      default: 0
    },
  },
  emits: ['refreshData'],
  data() {
    return {
      currentPanel: '',
      olgTaskManagement: {
        workTypeCode: '',
        workTypeName: ''
      },
      workOrderDealShow: false,
      createWorkOrderTypes: [
        {
          workTypeName: '综合维修',
          id: '1'
        }
      ],
      alarmDetail: {},
      workNumLoadList: []  // 工单加载记录
    }
  },
  computed: {
    detail: function () {
      return this.$parent.detail || {}
    },
    orderList: function () { // 工单列表
      return this.detail.workInfo
    }
  },
  mounted() {
    if (this.orderList.length) {
      const [first] = this.orderList;
      this.onCollapseChange(first.workNum);
    }
  },
  methods: {
    // 新建工单 打开弹窗
    selectWorkOrderType(id, name) {
      this.olgTaskManagement = {
        workTypeCode: id,
        workTypeName: name
      }
      this.workOrderDealShow = true
    },
    // 新建工单提交
    workOrderSure() {
      this.workOrderDealShow = false
      this.$emit('refreshData')
    },
    onCollapseChange(workNum) {
      this.currentPanel = workNum;
      if (!workNum) return;

      if (!this.workNumLoadList.includes(workNum)) {
        this.workNumLoadList.push(workNum)
      }
    },
  }
}


</script>

<style lang='scss' scoped>
.component.EmergencyWorkOrder {
  display: flex;
  flex-flow: column nowrap;
  height: 100%;
  overflow: hidden;

  .content-head {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px;
    height: 52px;

    &__extend {
      display: flex;
      position: relative;

      .el-button {
        position: absolute;
        right: 0;
        top: -18px;
      }
    }
  }
}


.description {
  &__content {
    display: flex;
    flex-flow: row wrap;

    >li {
      flex-basis: 50%;
      display: flex;
      flex-flow: row nowrap;
      margin-top: 24px;

      &.full-row {
        flex-basis: 100%;
      }

      >div:first-of-type {
        color: #B0E3FA;

        &::after {
          content: '：';
        }

      }

      >div:last-child {
        width: 0;
        flex: 1;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
      }
    }
  }

  &__imgs {
    display: flex;
    flex-flow: row wrap;
  }

  &__img {
    height: 100px;
    width: 100px;
    overflow: hidden;
    margin: 0 10px 10px 0;

    >img {
      height: 100%;
      width: 100%;
      object-fit: cover;
      object-position: center;
    }
  }
}

.EmergencyWorkOrder {
  display: flex;
  flex-flow: column nowrap;
  height: 100%;
  overflow: hidden;

  &__description {
    flex: 1;
    background: rgba(133, 145, 206, 0.15);
    overflow: hidden;
    display: flex;
    flex-flow: column nowrap;
    padding-bottom: 16px;

    &__content {
      padding: 0 16px;
      overflow: auto;
      flex: 1;

      ::v-deep .el-collapse {
        border: none;

        .el-collapse-item:not(:first-of-type) {
          margin-top: 8px;
        }

        .el-collapse-item__header {
          background: rgba(133, 145, 206, 0.15);
          color: #fff;
          line-height: unset;
          height: 80px;
          padding: 16px 16px 16px 24px;
          border: none;
          border-radius: 4px 4px 4px 4px;
          font-size: 14px;

          &.is-active {
            background: rgba(255, 202, 100, 0.12);

          }
        }

        .el-collapse-item__wrap {
          background-color: transparent;
          border: none;
        }

        .el-collapse-item__content {
          color: #fff;
          padding: 16px 0 16px 60px;
          font-size: 14px;
        }

        .el-collapse-item__arrow {
          color: #8bddf5;

          &.is-active {
            color: #ffca64;
          }
        }
      }
    }

    &__order {

      &__title {
        display: flex;
        align-items: center;
        width: 50%;

        &__icon {
          width: 40px;
          height: 40px;
          display: flex;
          justify-content: center;
          align-items: center;

          >img {
            height: 32px;
            width: 32px;
          }
        }

        &__content {

          margin-left: 26px;
          flex: 1;
          display: block;

          >li:first-of-type {
            margin-top: 0;
            display: flex;
            align-items: center;
          }

          >li:last-of-type {
            margin-top: 10px;
          }
        }

        .tag {
          display: inline-block;
          margin-left: 10px;
          border-radius: 4px 4px 4px 4px;
          padding: 4px 8px;

          &--2 {
            color: #FF2D55;
            background: rgba(255, 45, 85, 0.2);
          }

          &--100 {
            color: #61E29D;
            background: rgba(97, 226, 157, 0.2);
          }
        }

        .order_number {
          color: #3CC1FF;
        }
      }

      &__remark.content-head {
        padding: 16px 16px 16px 0 !important;
      }
    }
  }
}
</style>

<style lang='scss'>
.component.EmergencyWorkOrder {
  .EmergencyWorkOrder__description__order__detail {
    .form-detail {
      padding-right: 0px;

      .reserve-scorll {
        padding-right: 0;
        overflow: hidden;

        .plan-title {
          height: 32px;
          pointer-events: none;

          .el-icon-time {
            color: #8BDDF5;
          }

          .linear-g {
            margin-left: 10px;
            padding: 1px 0 0 12px;
            position: relative;
            color: #fff;
            background: rgba(133, 145, 206, 0.15);
            border-radius: 4px;

            .linear-g-span1 {
              position: absolute;
              left: 144px;
              top: 5px;
              background: rgba(255, 202, 100, 0.2);
              border-radius: 4px 4px 4px 4px;
              color: #FFCA64;
              display: block;
              line-height: 22px;
              padding: 0 8px;
            }

            i {
              display: none !important;
            }
          }
        }

        .plan-content {
          padding-left: 42px;
          width: calc(100% - 9px);
          margin-left: 9px;
          font-size: 14px;

          >ul.item-row {
            padding: 16px 0 0 0;
          }

          .container {
            height: auto !important;

            .item-row {
              padding: 0;
              margin: 16px 0;
              width: 100%;
              color: #b5bacb;
              font-size: 14px;
            }
          }

          ul>li {

            &.width30,
            &.width45 {
              width: 33%;
            }

            span:first-child {
              width: 80px;
              text-align: right;
              margin-right: 4px;
              color: #B0E3FA;

              &::after {
                content: ':';
                margin-left: 1px;
              }
            }
          }
        }

        >div:nth-last-child(-n + 2) {
          .plan-content-line {
            border-left: none;
          }
        }
      }
    }
  }
}
</style>
