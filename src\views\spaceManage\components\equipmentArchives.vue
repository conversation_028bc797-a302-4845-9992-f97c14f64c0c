<template>
  <div class="inner">
    <div class="content-left">
      <div v-for="(item, index) in typeList" :key="index" :class="['archive-bar', active === item.dictValue ? 'active' : '']" @click="changeType(item.dictValue)">
        <div class="conten-text">
          <span>{{ item.dictLabel }}</span>
          <span>({{ item.total }})</span>
        </div>
      </div>
    </div>
    <div class="content-right">
      <div class="archives-title">
        <span class="title-text">资产档案</span>
      </div>
      <el-table ref="singleTable" :data="tableData" height="500px" style="border: 1px solid #5996f9 !important" stripe highlight-current-row :header-cell-style="{ color: '#7EAEF9' }">
        <el-table-column align="center" type="index" label="序号" width="80"></el-table-column>
        <el-table-column align="center" property="fileName" label="档案名称"></el-table-column>
        <el-table-column align="center" property="fileType" label="档案类型"></el-table-column>
        <el-table-column align="center" property="fileSize" label="档案大小"></el-table-column>
      </el-table>
      <el-pagination
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page="pageParams.currentPage"
        :page-sizes="[10, 20, 30, 40]"
        :page-size="pageParams.pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        :total="pageParams.total"
        class="pagination"
      ></el-pagination>
    </div>
  </div>
</template>
<script>
import { getArchivesType, getArchivesList } from '@/utils/equipmentApi'
export default {
  name: 'equipmentArchives',
  components: {},
  props: {
    deviceId: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      typeList: [],
      active: '',
      tableData: [],
      pageParams: {
        pageSize: 10,
        currentPage: 1,
        total: 0
      }
    }
  },
  computed: {},
  watch: {},
  methods: {
    getArchivesType(id) {
      getArchivesType({ assetsId: id }).then((res) => {
        if (res.data.code === '200') {
          this.typeList = res.data.data
          this.active = res.data.data[0].dictValue
          this.getArchivesList(id, this.active)
        }
      })
    },
    getArchivesList(id, type) {
      getArchivesList({ assetsId: id, fileCatalogCode: type }).then((res) => {
        if (res.data.code === '200') {
          this.tableData = res.data.data
        }
      })
    },
    changeType(type) {
      this.active = type
      const id = this.deviceId
      this.getArchivesList(id, type)
    },
    handleSizeChange(val) {
      this.pageParams.pageSize = val
      // 在此处请求接口
    },
    handleCurrentChange(val) {
      this.pageParams.currentPage = val
      // 在此处请求接口
    }
  },
  created() {
    const id = this.deviceId
    this.getArchivesType(id)
  },
  mounted() {}
}
</script>
<style lang="scss" scoped>
.inner {
  width: 100%;
  padding: 0 120px;
  display: flex;
  .content-left {
    .archive-bar {
      cursor: pointer;
      margin-bottom: 16px;
      width: 158px;
      height: 32px;
      background-color: #112d8a;
      color: #fff;
      font-size: 14px;
      display: flex;
      justify-content: center;
      align-items: center;
      .conten-text {
        span {
          display: inline-block;
        }
        :last-child {
          margin-left: 6px;
        }
      }
    }
    .active {
      background-color: rgba(255, 176, 55, 0.2);
    }
  }
  .content-right {
    width: 100%;
    margin-left: 30px;
    .archives-title {
      margin-bottom: 16px;
      width: calc(100% - 12px);
      padding-left: 12px;
      height: 32px;
      line-height: 32px;
      background-color: #263057;
      border-radius: 5px;
      .title-text {
        font-size: 14px;
        color: #ffe3a6;
      }
    }
  }
}
</style>
