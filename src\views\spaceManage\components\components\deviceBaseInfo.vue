<template>
  <div class="deviceBaseInfo">
    <div class="device-info">
      <div class="info-box">
        <p>
          <span>设施名称：</span>
          <span>{{deviceData.surveyName || '-'}}</span>
        </p>
        <p>
          <span>设备品类：</span>
          <span>{{deviceData.assetCategoryName || '-'}}</span>
        </p>
        <p>
          <span>设备类型：</span>
          <span>{{deviceData.assetTypeName || '-'}}</span>
        </p>
        <p>
          <span>运行状态：</span>
          <span>{{runState[deviceData.status] || '-'}}</span>
        </p>
      </div>
      <div class="param-box">
        <div v-for="item in parameterList" :key="item.surveyEntityCode" class="param-item">
          <p>{{ item.dictAlias || item.parameterName }}</p>
          <p>
            <span>{{ item.parameterValue || '-' }}</span>
            <span>{{ item.parameterUnit || '' }}</span>
          </p>
        </div>
      </div>
      <p style="font-size: 14px;color: #A4AFC1;">更新时间：{{ moment().format('YYYY-MM-DD HH:mm:ss') }}</p>
    </div>
    <div class="asset-info">
      <div class="asset-module">
        <p class="module-title">基本信息</p>
        <div class="info-box">
          <p>
            <span>资产名称：</span>
            <span>{{assetData.assetName || '-'}}</span>
          </p>
          <p>
            <span>资产编码：</span>
            <span>{{assetData.assetCode || '-'}}</span>
          </p>
          <p>
            <span>品牌：</span>
            <span>{{assetData.assetBrand || '-'}}</span>
          </p>
          <p>
            <span>型号：</span>
            <span>{{assetData.assetModel || '-'}}</span>
          </p>
          <p>
            <span>生产日期：</span>
            <span>{{assetData.dateOfManufacture || '-'}}</span>
          </p>
          <p>
            <span>SN码：</span>
            <span>{{assetData.assetSn || '-'}}</span>
          </p>
          <p>
            <span>所在区域：</span>
            <span>{{assetData.regionName || '-'}}</span>
          </p>
          <p>
            <span>计量单位：</span>
            <span>{{assetData.unitOfMeasurement || '-'}}</span>
          </p>
          <p>
            <span>启用日期：</span>
            <span>{{assetData.startDate || '-'}}</span>
          </p>
          <p>
            <span>使用期限：</span>
            <span>{{assetData.serviceLife || '-'}}</span>
          </p>
          <p>
            <span>资产状态：</span>
            <span>{{assetData.assetStatusName || '-'}}</span>
          </p>
        </div>
      </div>
      <div class="asset-module">
        <p class="module-title">资产归口部门</p>
        <div class="info-box">
          <p>
            <span>归口部门：</span>
            <span>{{assetData.centralizedDepartmentName || '-'}}</span>
          </p>
        </div>
      </div>
      <div class="asset-module">
        <p class="module-title">资产国标分类</p>
        <div class="info-box">
          <p>
            <span>资产大类：</span>
            <span>{{assetData.assetCategoryName || '-'}}</span>
          </p>
          <p>
            <span>资产小类：</span>
            <span>{{assetData.assetSubcategoryName || '-'}}</span>
          </p>
        </div>
      </div>
      <div class="asset-module">
        <p class="module-title">类别信息</p>
        <div class="info-box">
          <p>
            <span>专业类别：</span>
            <span>{{assetData.professionalCategoryName || '-'}}</span>
          </p>
          <p>
            <span>系统类别：</span>
            <span>{{assetData.systemCategoryName || '-'}}</span>
          </p>
        </div>
      </div>
      <div class="asset-module">
        <p class="module-title">使用信息</p>
        <div class="info-box">
          <p>
            <span>使用部门：</span>
            <span>{{assetData.userDepartmentName || '-'}}</span>
          </p>
          <p style="width: 60%;">
            <span>备注：</span>
            <span>{{assetData.remarks || '-'}}</span>
          </p>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { getSecurityOtherSysList } from '@/utils/spaceManage'
import { getAssetDetailsByAssetIds } from '@/utils/elevatorApi'
import moment from 'moment'
moment.locale('zh-cn')
export default {
  name: 'deviceBaseInfo',
  props: {
    deviceData: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      moment,
      parameterList: [], // 监测参数列表
      assetData: {}, // 资产详情
      runState: {
        0: '正常',
        6: '离线',
        10: '异常'
      }
    }
  },
  computed: {

  },
  created() {
    this.getSecurityOtherSysList()
    this.getAssetDetailsById()
  },
  methods: {
    // 获取资产详情
    getAssetDetailsById() {
      getAssetDetailsByAssetIds({ assetsId: this.deviceData.assetId }).then(res => {
        console.log(res)
        if (res.data.code == 200) {
          this.assetData = res.data.data
        }
      })
    },
    // 获取检测参数列表
    getSecurityOtherSysList() {
      const param = {
        projectCode: this.deviceData.projectCode,
        surveyCode: this.deviceData.surveyCode,
        page: 1,
        pageSize: 15
      }
      getSecurityOtherSysList(param).then(res => {
        if (res.data.code == 200 && res.data.data.list.length) {
          this.parameterList = res.data.data.list[0].parameterList
        }
      })
    }
  }
}

</script>

<style lang="scss" scoped>
.deviceBaseInfo {
  height: 100%;
  width: 100%;
  overflow: auto;
  .device-info {
    background: rgba(53,98,219,0.06);
    padding: 16px 24px;
  }
  .asset-info {
    margin-top: 16px;
    background: rgba(53,98,219,0.06);
    .asset-module {
      padding: 16px 24px 8px 24px;
      .module-title {
        margin-bottom: 28px;
        font-weight: 500;
        font-size: 15px;
        color: #FFFFFF;
      }
      .info-box {
        padding: 0px 0px 0px 24px;
      }
    }
  }
  .info-box {
    display: flex;
    flex-wrap: wrap;
    p {
      width: calc(100% / 3);
      font-size: 14px;
      margin-bottom: 24px;
      display: flex;
      span {
        display: inline-block;
      }
      span:first-child {
        width: 100px;
        text-align: right;
        font-weight: 400;
        color: #B0E3FA;
      }
      span:last-child {
        flex: 1;
        font-weight: 500;
        color: #FFFFFF;
      }
    }
  }
  .param-box {
    // display: flex;
    // flex-wrap: wrap;
    // justify-content: space-between;
    display: grid;
    grid-template-columns: 20% 20% 20% 20% 20%;
    .param-item {
      width: calc(100% - 10px);
      // width: calc(100% / 5 - 10px);
      padding: 11px 10px;
      background: rgba(53,98,219,0.06);
      margin-bottom: 10px;
      text-align: center;
      p:first-child {
        font-weight: 500;
        font-size: 15px;
        color: #FFFFFF;
        line-height: 18px;
        margin-bottom: 8px;
      }
      p:last-child {
        span:first-child  {
          font-weight: 500;
          font-size: 24px;
          color: #FFCA64;
          line-height: 36px;
        }
        span:last-child  {
          margin-left: 4px;
          font-weight: 500;
          font-size: 15px;
          color: #CCCED3;
          line-height: 18px;
        }
      }
    }
    .param-item:nth-child(5n) {
      margin-right: 0px;
    }
  }
}
</style>
