<template>
  <div class="enefitAnalysisComponent">
    <div class="module-container" style="height: calc(33%)">
      <div class="module-header">
        <div class="title-left title-left-operation">
          <p class="title-left-text">收入分析</p>
        </div>
        <div class="title-right">
          <el-dropdown trigger="click" @command="(e) => handleDeployCommand(e)">
            <span class="el-dropdown-link"> {{ dateTypeName }} <i class="el-icon-arrow-down"></i> </span>
            <el-dropdown-menu slot="dropdown">
              <el-dropdown-item v-for="(item, index) in dateList" :key="index" :command="item.val"
                :class="{ isBjxl: dateType == item.val }">{{ item.name }}</el-dropdown-item>
            </el-dropdown-menu>
          </el-dropdown>
          <div class="icon-box">
            <img src="../../../assets/images/order_more.png" class="order-more" @click="allTableChange" />
          </div>
        </div>
      </div>
      <div class="module-content statistics-box">
        <div class="box-item">
          <div>
            <p class="item-label">
              <span>总收入</span><span class="item-value">34123</span> <span class="item-unit">万元</span>
            </p>
            <p>
              <span class="item-unit">环比上月</span>
              <img src="@/assets/images/qhdsys/up.png" alt="">
              <span class="item-ratio">12.3%</span>
            </p>
          </div>
        </div>
        <div class="box-item">
          <div>
            <p class="item-label">
              <span>支出</span><span class="item-value">90550</span> <span class="item-unit">万元</span>
            </p>
            <p>
              <span class="item-unit">环比上月</span>
              <img src="@/assets/images/qhdsys/up.png" alt="">
              <span class="item-ratio">12.3%</span>
            </p>
          </div>
        </div>
        <div class="box-item">
         <div>
            <p class="item-label">
              <span>总利润</span><span class="item-value">8509</span> <span class="item-unit">万元</span>
            </p>
            <p>
              <span class="item-unit">环比上月</span>
              <img src="@/assets/images/qhdsys/down.png" alt="">
              <span class="item-ratio down-color">12.3%</span>
            </p>
          </div>
        </div>
        <div class="box-item">
          <div>
            <p class="item-label">投资回收期</p>
            <p>
              <span class="item-value">0.66</span>
              <span class="item-unit">年</span>
            </p>
          </div>
        </div>
      </div>
    </div>
    <div class="module-container" style="height: calc(35%)">
      <div class="module-header">
        <div class="title-left">
          <p class="title-left-text">收入明细</p>
        </div>
        <div class="title-right">
          <div class="icon-box">
            <img src="../../../assets/images/order_more.png" class="order-more" @click="allTableChange" />
          </div>
        </div>
      </div>
      <div class="module-content" style="height: calc(100% - 48px)">
        <div v-if="incomeTrendShow" class="center-center">暂无数据</div>
        <div v-else style="width: 100%; height: 100%">
          <div class="checkbox-contain">
            <!-- <el-checkbox v-model="checkAll" style="margin-right: 30px;" :indeterminate="isIndeterminate"
              @change="handleCheckAllChange"></el-checkbox> -->
            <el-checkbox-group v-model="checkList" fill="#8BDDF5">
              <el-checkbox label="检查收入"></el-checkbox>
              <el-checkbox label="药品收入"></el-checkbox>
              <el-checkbox label="耗材收入"></el-checkbox>
            </el-checkbox-group>
            <img src="@/assets/images/checkbox_add.png" @click="switchPanel('income')" />
            <div v-show="showPanel" class="panel">
              <el-checkbox-group v-model="checkList" fill="#8BDDF5">
                <el-checkbox v-for="item in incomeTypeList" :key="item.code" :label="item.name"></el-checkbox>
              </el-checkbox-group>
            </div>
          </div>
          <div id="incomeDetailEcharts" style="width: 100%; height: 100%"></div>
        </div>
      </div>
    </div>
    <div class="module-container" style="height: calc(32%)">
      <div class="module-header">
        <div class="title-left">
          <p class="title-left-text">支出明细</p>
        </div>
        <div class="title-right">
          <div class="icon-box">
            <img src="../../../assets/images/order_more.png" class="order-more" @click="allTableChange" />
          </div>
        </div>
      </div>
      <div class="module-content" style="height: calc(100% - 48px)">
        <div v-if="disburseTrendShow" class="center-center">暂无数据</div>
        <div v-else style="width: 100%; height: 100%">
          <div class="checkbox-contain">
            <!-- <el-checkbox v-model="checkDisburseAll" style="margin-right: 30px;" :indeterminate="isDisburseIndeterminate"
              @change="handleCheckDisburseAllChange"></el-checkbox> -->
            <el-checkbox-group v-model="checkDisburseList" fill="#8BDDF5">
              <el-checkbox label="支出类型1"></el-checkbox>
              <el-checkbox label="支出类型2"></el-checkbox>
              <el-checkbox label="支出类型3"></el-checkbox>
            </el-checkbox-group>
            <img src="@/assets/images/checkbox_add.png" @click="switchPanel('disburse')" />
            <div v-show="showDisbursPanel" class="panel">
              <el-checkbox-group v-model="checkDisburseList" fill="#8BDDF5">
                <el-checkbox v-for="item in disburseTypeList" :key="item.code" :label="item.name"></el-checkbox>
              </el-checkbox-group>
            </div>
          </div>
          <div id="disburseDetailEcharts" style="width: 100%; height: 100%"></div>
        </div>
      </div>
    </div>
    <!-- 更多 -->
    <dialogFrame :visible="visible" :breadcrumb="breadcrumb" :title="title" @back="back"
      @update:visible="visible = $event">
      <component :is="activeComponent" @openDetailComponent="openDetailComponent"></component>
    </dialogFrame>
  </div>
</template>

<script >
import * as echarts from 'echarts'
import dayjs from 'dayjs'
export default {
  name: 'enefitAnalysisComponent',
  props: {
    roomData: {
      type: Object,
      default: () => { }
    }
  },
  components: {
    dialogFrame: () => import("@/components/common/DialogFrame"),
    benefitAnalysis: () => import("../components/benefitAnalysis/detail.vue"),
  },
  watch: {
    // 监听模型层级数据变化
    roomData: {
      handler(newVal, oldVal) {
        this.initData()
      },
      deep: true
    }
  },
  data() {
    return {
      sortType: '',
      ssmCodes: [],
      incomeTrendShow: false,
      disburseTrendShow: false,
      checkAll: true,
      checkDisburseAll: false,
      isIndeterminate: false,
      isDisburseIndeterminate: false,
      showPanel: false,
      showDisbursPanel: false,
      incomeTypeList: [
        {
          name: '检查收入',
          code: '1'
        },
        {
          name: '药品收入',
          code: '2'
        },
        {
          name: '耗材收入',
          code: '3'
        },
      ],
      disburseTypeList: [
        {
          name: '支出类型1',
          code: '1'
        },
        {
          name: '支出类型2',
          code: '2'
        },
        {
          name: '支出类型3',
          code: '3'
        },
      ],
      checkList: ['检查收入', '药品收入', '耗材收入'],
      checkDisburseList: ['支出类型1', '支出类型2', '支出类型3'],
      checkedCodeList: ['1', '2', '3'],
      dateType: 'month',
      dateTypeName: '2024',
      dateList: [
        { name: '2024', val: 'day', startTime: dayjs().format('YYYY-MM-DD'), endTime: dayjs().format('YYYY-MM-DD') },
        // { name: '本周', val: 'week', startTime: dayjs().startOf('week').format('YYYY-MM-DD'), endTime: dayjs().endOf('week').format('YYYY-MM-DD') },
        // { name: '本月', val: 'month', startTime: dayjs().startOf('month').format('YYYY-MM-DD'), endTime: dayjs().endOf('month').format('YYYY-MM-DD') },
        // { name: '本年', val: 'year', startTime: dayjs().startOf('year').format('YYYY-MM-DD'), endTime: dayjs().endOf('year').format('YYYY-MM-DD') },
        // { name: '自定义', val: 'custom', startTime: '', endTime: '' }
      ],
      visible: false,
      breadcrumb: [{ label: "效益分析", name: "benefitAnalysis" }],
      activeComponent: "benefitAnalysis",
      title: "效益分析",
    }
  },
  mounted() {
    this.sortType = this.roomData.ssmType < 4 ? 'jz' : this.roomData.ssmType == 4 ? 'lc' : 'mk'
    // 初始化调用
    this.initData()
  },
  methods: {
    //收入选中变化
    handleCheckAllChange(val) {
      this.checkList = val ? this.incomeTypeList.map((item) => item.name) : []
      this.isIndeterminate = false
    },
    //支出选中变化
    handleCheckDisburseAllChange(val) {
      this.checkDisburseList = val ? this.disburseTypeList.map((item) => item.name) : []
      this.isDisburseIndeterminate = false
    },
    // 下拉选择
    switchPanel(type) {
      if (type === 'disburse') {
        this.showDisbursPanel = !this.showDisbursPanel
      } else {
        this.showPanel = !this.showPanel
      }
    },
    //时间切换
    handleDeployCommand(val) {
      const obj = this.dateList.find((el) => el.val === val)
      this.deployDateType = val
      this.dateTypeName = obj.name
    },
    openDetailComponent(params) {
      this.activeComponent = params;
      switch (params) {
        case 'deployAssetsList':
          this.breadcrumb.push({ label: "调配资产", name: "deployAssetsList " });
          this.title = '调配资产'
          break;
        case 'leaseList':
          this.breadcrumb.push({ label: "租赁列表", name: "leaseList" });
          this.title = '租赁列表'
          break;
        case 'leaseDetail':
          this.breadcrumb.push({ label: "租赁信息", name: "leaseDetail" });
          this.title = '租赁信息'
          break;
        default:
          break;
      }

    },
    back(name) {
      this.activeComponent = name;
      if (this.breadcrumb.length > 1) {
        let index = this.breadcrumb.findIndex((item) => item.name === name);
        this.breadcrumb.splice(index);
      }
    },
    initData() {
      this.sortType = this.roomData.ssmType < 4 ? 'jz' : this.roomData.ssmType == 4 ? 'lc' : 'mk'
      this.ssmCodes = this.roomData.ssmCodes?.split(',') || []
      this.getIncomeDetailData()
      this.getDisburseDetailData()
    },
    //收入明细
    getIncomeDetailData() {
      const getchart = echarts.init(document.getElementById('incomeDetailEcharts'))
      var borderColor = ['#3CC1FF', '#FFCA64', '#41E4BB', 'rgba(0, 248, 114, 1)', 'rgba(46, 119, 251, 1)']
      let dateArr = ['0点', '01点', '02点', '03点', '04点', '05点', '06点', '07点', '08点', '09点', '11点']
      let echartsData = [
        {
          name: '检查收入',
          value: [1000.3000, 3300, 6000, 5500, 1600, 3677, 4677, 2666, 3333, 5666]
        }, {
          name: '药品收入',
          value: [4000.3060, 2300, 4000, 4500, 5600, 3677, 2677, 1666, 3833, 4666]
        }, {
          name: '耗材收入',
          value: [1900.3900, 4300, 5000, 1500, 4600, 3677, 5677, 5666, 4533, 2666]
        }
      ]
      const seriesObj = []
      echartsData.forEach((item, index) => {
        const randomRgbColor = this.$tools.randomRgbColor('array')
        seriesObj.push({
          name: item.name,
          type: 'line',
          smooth: false,
          showAllSymbol: true,
          symbol: 'circle',
          symbolSize: 0,
          itemStyle: {
            color: '#3eb5dd',
            borderColor: '#f1f1f1',
            borderWidth: 1
          },
          lineStyle: {
            width: 2,
            color: borderColor[index] ?? randomRgbColor[1]
          },
          data: item.value
        })
      })
      const dataZoom = [
        // {
        //   type: 'slider', // 滑动条型数据区域缩放组件
        //   realtime: false, // 拖动时，是否实时更新系列的视图。如果设置为 false，则只在拖拽结束的时候更新。
        //   // start: 0, //数据窗口范围的起始百分比。范围是：0 ~ 100。表示 0% ~ 100%。
        //   // end: 10,  // 数据窗口范围的结束百分比。范围是：0 ~ 100
        //   height: 4,
        //   endValue: 5, // 数据窗口范围的结束数值。如果设置了 dataZoom-inside.end 则 endValue 失效
        //   fillerColor: 'rgba(17, 100, 210, 0.4)', // 滚动条颜色
        //   borderColor: 'rgba(17, 100, 210, 0.1)',
        //   handleSize: 0, // 两边手柄尺寸
        //   showDetail: false, // 拖拽时是否展示滚动条两侧的文字
        //   bottom: '14%', // 组件离容器上侧的距离
        //   zoomLock: true // 是否只平移不缩放
        //   // moveOnMouseMove:true, //开启鼠标移动窗口平移
        //   // zoomOnMouseWheel :true, //开启鼠标滚轮缩放
        // },
        // {
        //   type: 'inside', // 内置型数据区域缩放组件
        //   // start: 0,
        //   // end: 10,
        //   endValue: 11, // 最多12个
        //   zoomOnMouseWheel: false, // 关闭鼠标滚轮缩放
        //   moveOnMouseWheel: true, // 开启鼠标滚轮窗口平移
        //   moveOnMouseMove: true // 开启鼠标移动窗口平移
        // }
      ]
      const option = {
        tooltip: {
          trigger: 'axis',
          backgroundColor: '#0D2169',
          borderColor: '#727382',
          textStyle: {
            color: '#fff'
          },
          axisPointer: {
            // 坐标轴指示器，坐标轴触发有效
            type: 'shadow' // 默认为直线，可选为：'line' | 'shadow'
          }
        },
        dataZoom: dataZoom,
        legend: {
          show: false,
          data: Array.from(echartsData, ({ name }) => name),
          type: 'scroll',
          orient: 'horizontal',
          left: 'right',
          top: '0',
          itemGap: 16,
          textStyle: {
            color: '#B3C2DD'
          }
        },
        grid: {
          left: '2%',
          right: '5%',
          bottom: '15%',
          top: '10%',
          containLabel: true
        },
        xAxis: {
          type: 'category',
          axisLabel: {
            show: true,
            textStyle: {
              color: 'rgba(255,255,255,0.5)'
            }
          },
          axisLine: {
            lineStyle: {
              type: 'solid',
              color: 'rgba(255,255,255,0.5)',
              width: '1'
            }
          },
          splitLine: {
            lineStyle: {
              color: 'rgba(255,255,255,0.2)',
              width: 1,
              type: 'solid',
              type: 'dashed'
            }
          },
          axisTick: {
            show: false
          },
          axisLabel: {
            interval: 0
          },
          boundaryGap: false,
          data: dateArr
        },
        yAxis: {
          type: 'value',
          axisLabel: {
            show: true,
            textStyle: {
              color: 'rgba(255,255,255,0.5)'
            }
          },
          axisLine: {
            lineStyle: {
              type: 'solid',
              color: 'rgba(255,255,255,0.2)',
              width: '1'
            }
          },
          splitLine: {
            lineStyle: {
              color: 'rgba(255,255,255,0.2)',
              width: 1,
              type: 'solid',
              type: 'dashed'
            }
          },
          axisTick: {
            show: false
          }
        },
        series: seriesObj
      }
      getchart.clear()
      getchart.setOption(option)
      // 随着屏幕大小调节图表
      window.addEventListener('resize', () => {
        getchart.resize()
      })
    },
    //支出明细
    getDisburseDetailData() {
      const getchart = echarts.init(document.getElementById('disburseDetailEcharts'))
      var borderColor = ['#3CC1FF', '#FFCA64', '#41E4BB', 'rgba(0, 248, 114, 1)', 'rgba(46, 119, 251, 1)']
      let dateArr = ['0点', '01点', '02点', '03点', '04点', '05点', '06点', '07点', '08点', '09点', '11点']
      let echartsData = [
        {
          name: '检查收入',
          value: [1000.3000, 3300, 6000, 5500, 1600, 3677, 4677, 2666, 3333, 5666]
        }, {
          name: '药品收入',
          value: [4000.3060, 2300, 4000, 4500, 5600, 3677, 2677, 1666, 3833, 4666]
        }, {
          name: '耗材收入',
          value: [1900.3900, 4300, 5000, 1500, 4600, 3677, 5677, 5666, 4533, 2666]
        }
      ]
      const seriesObj = []
      echartsData.forEach((item, index) => {
        const randomRgbColor = this.$tools.randomRgbColor('array')
        seriesObj.push({
          name: item.name,
          type: 'line',
          smooth: false,
          showAllSymbol: true,
          symbol: 'circle',
          symbolSize: 0,
          itemStyle: {
            color: '#3eb5dd',
            borderColor: '#f1f1f1',
            borderWidth: 1
          },
          lineStyle: {
            width: 2,
            color: borderColor[index] ?? randomRgbColor[1]
          },
          data: item.value
        })
      })
      const dataZoom = [
        // {
        //   type: 'slider', // 滑动条型数据区域缩放组件
        //   realtime: false, // 拖动时，是否实时更新系列的视图。如果设置为 false，则只在拖拽结束的时候更新。
        //   // start: 0, //数据窗口范围的起始百分比。范围是：0 ~ 100。表示 0% ~ 100%。
        //   // end: 10,  // 数据窗口范围的结束百分比。范围是：0 ~ 100
        //   height: 4,
        //   endValue: 5, // 数据窗口范围的结束数值。如果设置了 dataZoom-inside.end 则 endValue 失效
        //   fillerColor: 'rgba(17, 100, 210, 0.4)', // 滚动条颜色
        //   borderColor: 'rgba(17, 100, 210, 0.1)',
        //   handleSize: 0, // 两边手柄尺寸
        //   showDetail: false, // 拖拽时是否展示滚动条两侧的文字
        //   bottom: '14%', // 组件离容器上侧的距离
        //   zoomLock: true // 是否只平移不缩放
        //   // moveOnMouseMove:true, //开启鼠标移动窗口平移
        //   // zoomOnMouseWheel :true, //开启鼠标滚轮缩放
        // },
        // {
        //   type: 'inside', // 内置型数据区域缩放组件
        //   // start: 0,
        //   // end: 10,
        //   endValue: 11, // 最多12个
        //   zoomOnMouseWheel: false, // 关闭鼠标滚轮缩放
        //   moveOnMouseWheel: true, // 开启鼠标滚轮窗口平移
        //   moveOnMouseMove: true // 开启鼠标移动窗口平移
        // }
      ]
      const option = {
        tooltip: {
          trigger: 'axis',
          backgroundColor: '#0D2169',
          borderColor: '#727382',
          textStyle: {
            color: '#fff'
          },
          axisPointer: {
            // 坐标轴指示器，坐标轴触发有效
            type: 'shadow' // 默认为直线，可选为：'line' | 'shadow'
          }
        },
        dataZoom: dataZoom,
        legend: {
          show: false,
          data: Array.from(echartsData, ({ name }) => name),
          type: 'scroll',
          orient: 'horizontal',
          left: 'right',
          top: '0',
          itemGap: 16,
          textStyle: {
            color: '#B3C2DD'
          }
        },
        grid: {
          left: '2%',
          right: '5%',
          bottom: '15%',
          top: '10%',
          containLabel: true
        },
        xAxis: {
          type: 'category',
          axisLabel: {
            show: true,
            textStyle: {
              color: 'rgba(255,255,255,0.5)'
            }
          },
          axisLine: {
            lineStyle: {
              type: 'solid',
              color: 'rgba(255,255,255,0.5)',
              width: '1'
            }
          },
          splitLine: {
            lineStyle: {
              color: 'rgba(255,255,255,0.2)',
              width: 1,
              type: 'solid',
              type: 'dashed'
            }
          },
          axisTick: {
            show: false
          },
          axisLabel: {
            interval: 0
          },
          boundaryGap: false,
          data: dateArr
        },
        yAxis: {
          type: 'value',
          axisLabel: {
            show: true,
            textStyle: {
              color: 'rgba(255,255,255,0.5)'
            }
          },
          axisLine: {
            lineStyle: {
              type: 'solid',
              color: 'rgba(255,255,255,0.2)',
              width: '1'
            }
          },
          splitLine: {
            lineStyle: {
              color: 'rgba(255,255,255,0.2)',
              width: 1,
              type: 'solid',
              type: 'dashed'
            }
          },
          axisTick: {
            show: false
          }
        },
        series: seriesObj
      }
      getchart.clear()
      getchart.setOption(option)
      // 随着屏幕大小调节图表
      window.addEventListener('resize', () => {
        getchart.resize()
      })
    },
    allTableChange() {
      this.visible = true
      this.title = '效益分析'
    },
  }
}
</script>

<style lang="scss" scoped>
@import "../../spaceManage/style/module";
.enefitAnalysisComponent {
  width: 100%;
  height: 100%;
  .el-dropdown-link {
    color: #fff !important;
    margin-left: 10px;
  }
  .title-left {
    padding-left: 20px;
  }
}
.module-header {
  width: 100%;
  background: url("~@/assets/images/qhdsys/bg-bt.png") no-repeat;
}
.order-more {
  width: 24px;
  height: 24px;
  margin-right: 0 !important;
  margin-left: 8px;
}
.checkbox-contain {
  margin-top: 4px;
  display: flex;
  justify-content: flex-end;
  position: relative;
  img {
    width: 18px;
    height: 18px;
    margin-left: 12px;
    cursor: pointer;
  }
  .panel {
    position: absolute;
    right: 0;
    top: 20px;
    background-color: #374b79;
    padding: 8px;
    z-index: 9;
    height: 100px;
    overflow: auto;
    .el-checkbox-group {
      display: flex;
      flex-direction: column;
      align-items: flex-end;
      .el-checkbox {
        width: 100%;
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-right: 0;
        margin-bottom: 5px;
        .el-checkbox__label {
          font-weight: 300;
          font-size: 14px;
          color: #ffffff;
        }
      }
    }
  }
}
.statistics-box {
  display: flex;
  flex-wrap: wrap;
  width: 100%;
  height: calc(100% - 48px);
  padding: 16px 0;
  .box-item {
    width: calc(50% - 0.5rem);
    height: calc(50% - 0.5rem);
    flex-shrink: 0;
    box-sizing: border-box;
    background: rgba(53, 98, 219, 0.06);
    display: flex;
    align-items: center;
    justify-content: center;
    .item-value {
      font-weight: bold;
      font-size: 20px;
      color: #ffca64;
      margin-left: 6px;
    }
    .item-unit {
      font-weight: 400;
      font-size: 14px;
      color: rgba(255, 255, 255, 0.5);
      margin-left: 6px;
    }
    .item-label {
      font-weight: 400;
      font-size: 14px;
      color: #ffffff;
      margin-bottom: 10px;
    }
    .item-ratio {
      font-weight: bold;
      font-size: 12px;
      color: #ff2d55;
      margin-left: 5px;
    }
    .down-color{
      color: #61E29D !important;
    }
    img {
      margin-left: 2px;
      width: 19px;
      height: 19px;
      vertical-align: middle;
    }
  }
  .box-item:nth-child(2n) {
    margin-left: 1rem !important;
  }
  .box-item:nth-child(3),
  .box-item:nth-child(4) {
    margin-top: 1rem;
  }
}
::v-deep .el-checkbox__input.is-checked + .el-checkbox__label {
  color: #8bddf5 !important;
}
</style>
