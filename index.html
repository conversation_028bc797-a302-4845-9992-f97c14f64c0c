<!--
 * @Author: hedd
 * @Date: 2023-08-16 16:12:01
 * @LastEditTime: 2025-08-04 10:14:30
 * @FilePath: \ihcrs_client\index.html
 * @Description:
-->
<!DOCTYPE html>
<html lang="">
  <head>
    <meta charset="utf-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta name="viewport" content="width=device-width,initial-scale=1.0" />
    <link rel="icon" href="/favicon.ico" />
    <title>iHCRS智慧医院后勤综合管理系统</title>
    <style>
      #openDebuger {
        position: fixed;
        width: 50px;
        height: 50px;
        bottom: 100px;
        right: 0;
        z-index: 9999;
      }
    </style>
  </head>
  <body style="background-color: transparent">
    <div onclick="openDebuggerEvent()" id="openDebuger"></div>
    <div id="app"></div>
    <noscript>
      <strong></strong>
    </noscript>
    <script src="/eruda.js"></script>
    <script data-custom-attribute="value">
      eruda.init()
    </script>
    <script type="module" src="/src/main.js"></script>
    <script>
      // 定时器
      var timerNum = 30 //秒
      // 能耗账号
      var KECG_ACCOUNT = {
        username: 'gy_function',
        password: 'Xm12345678!'
      }
      // 登录账号
      var IHCRS_ACCOUNT = {
        username: 'sinomisihcrs',
        password: 'Sinomis@123'
      }
      // 肿瘤 外网演示环境
      if ('<%= VUE_APP_HOSPITAL_NODE_ENV %>' === 'szzlyy' || '<%= VUE_APP_HOSPITAL_NODE_ENV %>' === 'sinomis' || '<%= VUE_APP_HOSPITAL_NODE_ENV %>' === 'outernet') {
        KECG_ACCOUNT = {
          username: 'gy_function',
          password: 'Xm12345678!'
        }
        if ('<%= VUE_APP_HOSPITAL_NODE_ENV %>' === 'szzlyy') {
          IHCRS_ACCOUNT = {
            username: 'admin',
            password: 'sinomis123'
          }
        }
      } else if ('<%= VUE_APP_HOSPITAL_NODE_ENV %>' === 'ljxyy') {
        KECG_ACCOUNT = {
          username: 'admin',
          password: 'Es12345678!'
        }
        IHCRS_ACCOUNT = {
          username: '***********',
          password: 'sinomis123'
        }
      } else if ('<%= VUE_APP_HOSPITAL_NODE_ENV %>' === 'fjslyy') {
        IHCRS_ACCOUNT = {
          username: 'sinomisihcrs',
          password: 'sinomis123'
        }
        KECG_ACCOUNT = {
          username: 'gy_function',
          password: 'Xm12345678!'
        }
      } else if ('<%= VUE_APP_HOSPITAL_NODE_ENV %>' === 'bjsjtyy') {
        KECG_ACCOUNT = {
          username: 'technician',
          password: 'Gy12345678!'
        }
        IHCRS_ACCOUNT = {
          username: '***********',
          password: 'sinomis123'
        }
      } else if ('<%= VUE_APP_HOSPITAL_NODE_ENV %>' === 'jldyyy') {
        IHCRS_ACCOUNT = {
          username: 'sinomisihcrs',
          password: 'sinomis123'
        }
      } else if('<%= VUE_APP_HOSPITAL_NODE_ENV %>' === 'sjydkqyy') {
        IHCRS_ACCOUNT = {
          username: 'admin',
          password: 'sinomis123'
        }
      }
      
      var __PATH = {
        VUE_APP_BASE_API: '<%= VUE_APP_UNIFIED_SERVER %>' + 'sinomis-authweb', // 系统管理
        // VUE_APP_BASE_API: '**************:9219', // 系统管理
        VUE_APP_IDPS_API: '<%= VUE_APP_UNIFIED_SERVER %>' + 'ipsm', // 双预防
        VUE_APP_RESERVE_PLAN_API: '<%= VUE_APP_UNIFIED_SERVER %>' + 'preplan', // 预案
        VUE_APP_IOMS_API: '<%= VUE_APP_UNIFIED_SERVER %>' + 'oneLogistics', // 一站式
        VUE_APP_IMWS_API: '<%= VUE_APP_UNIFIED_SERVER %>' + 'medicalWaste', // 医废
        VUE_APP_WARN_API: '<%= VUE_APP_UNIFIED_SERVER %>' + 'sinomis-alarm', // 预警
        VUE_APP_IEMC_API: '<%= VUE_APP_UNIFIED_SERVER %>' + 'sinomis-aircondition', // 空调
        // VUE_APP_IEMC_API: 'http://**************:8191', // 空调
        VUE_APP_INSP_API: '<%= VUE_APP_UNIFIED_SERVER %>' + 'inspection', // 巡检
        VUE_APP_SPACE_API: '<%= VUE_APP_UNIFIED_SERVER %>' + 'base_info', // 基础信息
        VUE_APP_IEMC_ELEVATOR_API: '<%= VUE_APP_UNIFIED_SERVER %>' + 'iemc_medical_elevator', // 电梯
        VUE_APP_ICIS_API: '<%= VUE_APP_UNIFIED_SERVER %>' + 'sinomis-service-external', // 网关基础服务
        VUE_APP_SOCKET: '<%= VUE_APP_WS_SERVER %>' + 'websocket', // 基础SOCKET地址
        VUE_APP_IEMC_WS_API: '<%= VUE_APP_WS_SERVER %>' + 'iemcWebsocket',
        VUE_APP_IEMC_ELEVATOR_WS: '<%= VUE_APP_WS_SERVER %>' + 'iemcEleWebsocket', // 电梯socket
        VUE_APP_KGCE_API: '<%= VUE_APP_ENERGY_API %>' + 'api', // 能耗
        VUE_APP_RTSP_LIVE_WS: '<%= VUE_APP_RTSP_LIVE_WS_SERVER %>', // rtsp视频socket
        VUE_APP_IEMS_API: '<%= VUE_APP_IEMS_UNIFIED_SERVER %>' + 'iaastServer', // 资产
        VUE_APP_IEMS_IOMS_API: '<%= VUE_APP_IEMS_UNIFIED_SERVER %>' + 'oneLogisticsServer', // 资产一站式
        VUE_APP_MOVE_API: '<%= VUE_APP_IEMS_UNIFIED_SERVER %>' + 'iaasIot', //  移动轨迹
        VUE_APP_HOSPITAL_NODE: '<%= VUE_APP_HOSPITAL_NODE_ENV %>',
        BASE_URL: '<%= VUE_APP_BASE_URL_SUFFIX %>',
        VUE_APP_PLAN_API: '<%= VUE_APP_UNIFIED_SERVER %>' + 'preplan', // 演习预案
        VUE_SPACE_API: '<%= VUE_APP_UNIFIED_SERVER %>' + 'sinomis-service-manage', // 空间
        VUE_TRANSFER_API: '<%= VUE_APP_UNIFIED_SERVER %>' + 'ifem', // 新iot
        // VUE_TRANSFER_API: 'http://**************:9800', // 新iot
        VUE_ELEVATOR_API: '<%= VUE_APP_UNIFIED_SERVER %>' + 'data_house', // 新iot
        VUE_CHEMICAL_API:  '<%= VUE_APP_UNIFIED_SERVER %>' + 'sinomis-chemicalWeb', // 危化品
        VUE_APP_IFEM_WEBSOCKET: '<%= VUE_APP_WS_IFEMSERVER %>' + 'ifemServer' // 空间椅位 WebSocket
      }
    </script>
    <script data-custom-attribute="openDebuger">
      let clickCount = 0
      let clickOpenTimer = null
      // 默认设置eruda为隐藏
      document.getElementById('eruda').style.display = 'none'
      // 点击事件打开或关闭eruda
      function openDebuggerEvent() {
        clickCount++
        clearTimeout(clickOpenTimer)
        timer = setTimeout(() => {
          clickCount = 0
        }, 500)
        if (clickCount >= 3) {
          // id为eruda的dom显示或者隐藏
          try {
            if (document.getElementById('eruda').style.display === 'block') {
              document.getElementById('eruda').style.display = 'none'
              window.chrome.webview.hostObjects.sync.bridge.ShowDialog(false)
            } else {
              document.getElementById('eruda').style.display = 'block'
              window.chrome.webview.hostObjects.sync.bridge.ShowDialog(true)
            }
          } catch (error) {}
        }
      }
    </script>
  </body>
</html>
