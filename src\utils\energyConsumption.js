// 能耗
import http from './http'
const kgceApi = __PATH.VUE_APP_KGCE_API
const iemcApi = __PATH.VUE_APP_IEMC_API
// 电

// 能耗登录
export function KgceLogin(params) {
  return http.postRequest(`${kgceApi}/sys/login`, { ...params })
}

// 能源及分类的树结构
export function GetEnergyAndClassifyTree(params, token) {
  return http.getRequest(`${kgceApi}/gy-service-energy-core/entSysEnergyCategoryInfo/getEnergyCategoryTree`, { ...params }, { 'X-Access-Token': token })
}

// 获取模型能源数据列表
export function GetModelEnergyDataList(params, token) {
  return http.getRequest(`${kgceApi}/gy-service-energy-core/entEnergyData/getModelEnergyDataList`, { ...params }, { 'X-Access-Token': token })
}

// 查询表记列表
export function GetPointList(params, token) {
  return http.getRequest(`${kgceApi}/gy-service-energy-core/measurePoint/queryPointList`, { ...params }, { 'X-Access-Token': token })
}
// 根据分析树查询下属分析对象
export function GetTreeAndPathAndDetail(params, token) {
  return http.getRequest(`${kgceApi}/gy-service-energy-core/modelTree/queryTreeAndPathAndDetail`, { ...params }, { 'X-Access-Token': token })
}
// 获取能耗设备列表
export function GetEnergyMeteringList(params) {
  return http.requestPost(`${iemcApi}/client/energyMeteringList`, params)
}
// 根据设备监测code取point code列表
export function GetGyPointList(params) {
  return http.requestPost(`${iemcApi}/client/getGyPointList`, params)
}
// 查询测点历史用量
export function GetPointHistoryList(params, token) {
  return http.getRequest(`${kgceApi}/gy-service-energy-core/history/point/queryUsedHistoryDataValueList`, { ...params }, { 'X-Access-Token': token })
}
// 查询表计实时抄表示数
export function GetRealTimeCvDataByIdList(params, token) {
  return http.getRequest(`${kgceApi}/gy-service-energy-core/realTime/point/queryRealTimeCvDataByIdList`, { ...params }, { 'X-Access-Token': token })
}
// 查询表计信号列表
export function GetPointSignalList(params, token) {
  return http.getRequest(`${kgceApi}/gy-service-energy-core/measurePoint/queryPointSignalList`, { ...params }, { 'X-Access-Token': token })
}
// 根据modelId查询表计列表
export function GetModelFormulaPoint(params, token) {
  return http.getRequest(`${kgceApi}/gy-service-energy-core/model/modelFormulaPoint`, { ...params }, { 'X-Access-Token': token })
}

// 用电类型分析
// export function getTypeElectricity(params) {
//   return http.post(`${kgceApi}/alarmRecord/getTypeElectricity`, {
//     // ...postCommonInfo,
//     ...params
//   })
// }
// // 区域用电排行
// export function getAreaRanking(params) {
//   return http.post(`${kgceApi}/alarmRecord/getAreaRanking`, {
//     // ...postCommonInfo,
//     ...params
//   })
// }
// // 区域用电占比
// export function getAreaPercent(params) {
//   return http.post(`${kgceApi}/alarmRecord/getAreaPercent`, {
//     // ...postCommonInfo,
//     ...params
//   })
// }
// // 能耗趋势
// export function getTrendStatistical(params) {
//   return http.post(`${kgceApi}/alarmRecord/getTrendStatistical`, {
//     // ...postCommonInfo,
//     ...params
//   })
// }

// // 根据楼层获取电表类型统计
// export function getTypeAmmeter(params) {
//   return http.post(`${kgceApi}/alarmRecord/getTypeAmmeter`, {
//     // ...postCommonInfo,
//     ...params
//   })
// }
