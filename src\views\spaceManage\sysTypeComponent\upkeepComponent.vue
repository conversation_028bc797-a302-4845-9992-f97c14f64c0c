<template>
  <div class="icisComponent">
    <ModuleCard title="保养统计" class="module-container" :floorName="!['Kongtiao', 'Electricity'].includes(roomData.tabName) ? roomData.title : ''" style="height: 24%">
      <div class="title-right" slot="title-right">
        <el-dropdown trigger="click" @command="changeDateType">
          <span class="el-dropdown-link"> {{ tagList.find((v) => v.value == tagCurrent)?.text ?? '' }} <i class="el-icon-arrow-down"></i> </span>
          <el-dropdown-menu slot="dropdown">
            <el-dropdown-item v-for="item in tagList" :key="item.value" :command="item.value" :class="{ isBjxl: tagCurrent == item.value }">{{ item.text }}</el-dropdown-item>
          </el-dropdown-menu>
        </el-dropdown>
      </div>
      <div v-loading="statisticsEchartsLoading" class="module-content" slot="content" style="height: calc(100%)">
        <div v-if="workOrderStatisticsShow" style="width: 100%; height: 100%; overflow: hidden" class="chartsContent">
          <div id="workOrderStatisticsEcharts" ref="workOrderStatisticsEcharts"></div>
        </div>
        <div v-else class="center-center">暂无数据</div>
      </div>
    </ModuleCard>
    <ModuleCard :title="`保养提醒(${remindData.count})`" :hasExpand="true" @emit-expand="openUpkeepDialog()">
      <div slot="content" style="height: 100%"></div>
    </ModuleCard>
    <!-- v-if="roomData.projectCode === 'IEMC-SecuritySystem'" -->
    <ModuleCard title="保养任务类型" class="module-container" style="height: 24%">
      <div slot="content" style="height: 100%">
        <TaskTypeStatist :roomData="roomData" :dateType="tagCurrent" :systemCode="2" @filterateTask="filterateTask"></TaskTypeStatist>
      </div>
    </ModuleCard>
    <ModuleCard title="保养任务清单" ref="taskBox" :hasExpand="true" @emit-expand="allTableChange('upkeep')" class="task-list module-container">
      <div slot="content" class="task-list-box" style="height: 100%">
        <div class="statistics-box">
          <div class="statistics-box-left">
            <span style="color: #ff5454; cursor: pointer" @click="changeType(5)">{{ lsitCount.timeoutCount || 0 }}</span>
            <span></span>
            <span style="color: #78abfc; cursor: pointer" @click="changeType(3)">{{ lsitCount.notStarted || 0 }}</span>
          </div>
          <div class="statistics-box-center">
            <span>全部任务</span>
            <span class="font-din" style="cursor: pointer" @click="changeType('')">{{ lsitCount.allCount || 0 }}</span>
            <span></span>
          </div>
          <div class="statistics-box-right">
            <span style="color: #ffba65; cursor: pointer" @click="changeType(4)">{{ lsitCount.underway || 0 }}</span>
            <span></span>
            <span style="color: #00d1ff; cursor: pointer" @click="changeType(2)">{{ lsitCount.hasCount || 0 }}</span>
          </div>
        </div>
        <div class="module-content" style="flex: 1; height: calc(100% - 9.5rem)">
          <el-table
            class="table-center-transfer"
            ref="taskTable"
            :data="tableData"
            height="100%"
            :cell-style="{ padding: ' 8px', backgroundColor: 'transparent', border: 'none', padding: '3px' }"
            :header-cell-style="{ background: '#8591CE26!important', color: '#8BDDF5FF', padding: '4px 8px', fontWeight: 'bold' }"
            style="width: 100%"
            element-loading-background="rgba(0, 0, 0, 0.2)"
            v-loading="tableLoading"
            v-el-table-infinite-scroll="tableLoadEvent"
            highlight-current-row
            @row-click="openTaskPointList"
          >
            <el-table-column fixed prop="taskName" show-overflow-tooltip label="任务名称"></el-table-column>
            <el-table-column fixed prop="cycleType" width="50" show-overflow-tooltip label="周期">
              <template slot-scope="scope">
                {{ format(scope.row.cycleType) }}
              </template>
            </el-table-column>
            <el-table-column fixed prop="distributionTeamName" width="100" show-overflow-tooltip label="巡检部门"></el-table-column>
            <el-table-column fixed prop="taskStatus" width="95" show-overflow-tooltip label="完成状态">
              <template slot-scope="scope">
                <div v-if="scope.row.taskStatus === 1" class="table-icon">
                  <img src="@/assets/images/icon-2.png" />
                  <span>未完成</span>
                </div>
                <div v-else class="table-icon">
                  <img src="@/assets/images/icon-5.png" />
                  <span>已完成</span>
                </div>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>
    </ModuleCard>
    <ModuleCard v-if="taskPointListShow" title="保养点清单" class="task-point-list module-container">
      <div slot="content" style="height: 100%">
        <el-table
          class="table-center-transfer"
          :data="pointTableData"
          height="100%"
          :cell-style="{ padding: ' 8px', backgroundColor: 'transparent', border: 'none', padding: '3px' }"
          :header-cell-style="{ background: '#8591CE26!important', color: '#8BDDF5FF', padding: '4px 8px', fontWeight: 'bold' }"
          style="width: 100%"
          element-loading-background="rgba(0, 0, 0, 0.2)"
          v-loading="pointTableLoading"
          v-el-table-infinite-scroll="pointTableLoadEvent"
          @cell-dblclick="toView"
        >
          <el-table-column fixed prop="taskPointName" show-overflow-tooltip label="保养点名称"></el-table-column>
          <el-table-column fixed prop="excuteTime" show-overflow-tooltip label="保养时间"></el-table-column>
          <!-- <el-table-column fixed prop="implementPersonName" show-overflow-tooltip label="执行人员"></el-table-column> -->
          <el-table-column fixed prop="taskStatus" width="95" show-overflow-tooltip label="保养结果">
            <template slot-scope="scope">
              <div v-if="scope.row.state == '3' || scope.row.state == '4'" class="table-icon">
                <img src="@/assets/images/icon-2.png" />
                <span>{{ scope.row.state == '3' ? '不合格' : '异常报修' }}</span>
              </div>
              <div v-else-if="scope.row.state == '2'" class="table-icon">
                <img src="@/assets/images/icon-5.png" />
                <span>合格</span>
              </div>
              <div v-else>
                <span>未保养</span>
              </div>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </ModuleCard>
    <template v-if="allTableComponentListShow">
      <allTableComponentList ref="allTableComponentList" :dialogData="tableCompenentData" :dialogShow="allTableComponentListShow" @configCloseDialog="configCloseTableDialog"></allTableComponentList>
    </template>
    <template v-if="upKeepRemindDialogShow">
      <upkeepRemindDialog
        :upkeepList="upkeepList"
        :deviceId="roomData.deviceId"
        :redmineCount="remindData.count"
        :tagCurrent="tagCurrent"
        :dialogShow="upKeepRemindDialogShow"
        @configCloseDialog="upKeepRemindDialogShow = false"
      />
    </template>
    <template v-if="upKeepTableListShow">
      <upKeepTableListDialog
        ref="upKeepTableListDialog"
        :upkeepTime="upkeepTime"
        :deviceId="roomData.deviceId"
        :dialogShow="upKeepTableListShow"
        @configCloseDialog="upKeepTableListShow = false"
      ></upKeepTableListDialog>
    </template>
    <taskPointDetail ref="taskPointDetail" :taskMap="taskMap" taskType="insp" systemType="byrw" @closeDialog="closeTaskPointDialog"></taskPointDetail>
  </div>
</template>

<script>
import { getInspectionData, getTaskPointSpaceList } from '@/utils/centerScreenApi'
import { GetInspTaskPointReleaseList, GetInspTaskRemindNum } from '@/utils/spaceManage'
import { dialogTypeList } from '@/assets/common/dict.js'
import allTableComponentList from '../components/allTableComponentList.vue'
import upKeepTableListDialog from '../components/upKeepTableListDialog.vue'
import upkeepRemindDialog from './components/upkeepRemindDialog.vue'
import TaskTypeStatist from './components/taskTypeStatist.vue'
import taskPointDetail from './components/taskPointDetail.vue'
import * as echarts from 'echarts'
export default {
  components: {
    taskPointDetail,
    allTableComponentList,
    upKeepTableListDialog,
    upkeepRemindDialog,
    TaskTypeStatist
  },
  name: 'upkeepComponent',
  props: {
    roomData: {
      type: Object,
      default: () => {}
    },
    type: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      tableLoading: false,
      tableData: [],
      tagCurrent: 'month',
      workOrderStatisticsShow: true,
      tagList: [
        { text: '今日', value: 'day' },
        { text: '本周', value: 'week' },
        { text: '本月', value: 'month' },
        { text: '本年', value: 'year' }
      ],
      activeType: '',
      lsitCount: {},
      allTableComponentListShow: false,
      upKeepTableListShow: false,
      upkeepTime: '',
      upkeepList: [],
      upKeepRemindDialogShow: false,
      tableCompenentData: {},
      dialogTypeList: dialogTypeList, // 过滤选中弹窗类型
      pageParams: {
        pageSize: 20,
        pageNo: 1,
        total: 0
      },
      pagePointParams: {
        pageSize: 20,
        pageNo: 1,
        total: 0
      },
      remindData: {
        count: 0,
        dateList: []
      },
      pointTableData: [],
      pointTableLoading: false,
      taskMap: {},
      statisticsEchartsLoading: false,
      currentTaskTableRow: {}, // 当前选中的任务行
      taskPointListShow: false // 巡检点列表
    }
  },
  watch: {
    // 监听模型层级数据变化
    roomData: {
      handler(val) {
        this.changeDateType(this.tagCurrent)
      },
      deep: true
    }
  },
  mounted() {
    this.changeDateType(this.tagCurrent)
  },
  methods: {
    // 获取巡检数据
    getInspectionDatas(type, receiveParam = {}) {
      const params = {}
      // 巡检是空间和设备两个模块的公共页面，用type作区分，根据不同参数分别查询
      // 2023/11/8秦皇岛新版巡检和保养暂时为设备单模块，如果再使用公共页面需要，将insp的接口增加insp及ipsm的兼容处理
      if (this.type === 'device') {
        // const assetsData = JSON.parse(localStorage.getItem('imesAssetsData'))
        // const assetsNumber = assetsData.assetsNumber
        // params.engineerCode = assetsNumber
        params.deviceId = this.roomData.deviceId
      } else {
        params.spaceCode = this.roomData.ssmCodes?.substring(this.roomData.ssmCodes.lastIndexOf(',') + 1) ?? ''
        if (this.roomData.projectCode) {
          params.spaceLevel = 4
        } else {
          params.spaceLevel = this.roomData.ssmType ? this.roomData.ssmType - 1 : 5
        }
        this.getTskPoint()
      }
      if (this.roomData.deviceId) {
        this.tableLoading = true
        const codeData = {
          dateType: this.tagCurrent,
          taskStatus: this.activeType,
          ...this.pageParams,
          ...params,
          systemCode: 2,
          ...receiveParam
        }
        this.statisticsEchartsLoading = true
        getInspectionData(codeData).then((res) => {
          this.statisticsEchartsLoading = false
          if (res.data.code === '200') {
            this.tableLoading = false
            this.lsitCount = res.data.data.sum
            this.pageParams.total = this.lsitCount.allCount
            if (this.pageParams.pageNo === 1) {
              this.tableData = []
            }
            this.tableData = this.tableData.concat(res.data.data.list)
            const arr = []
            const nameArr = ['已完成', '进行中', '未开始', '超时']
            const countArr = [res.data.data.sum.hasCount, res.data.data.sum.underway, res.data.data.sum.notStarted, res.data.data.sum.timeoutCount]
            const percentageArr = [res.data.data.sum.hasCountPercentage, res.data.data.sum.underwayPercentage, res.data.data.sum.notStartedPercentage, res.data.data.sum.timeoutCountPercentage]
            for (let i = 0; i < 4; i++) {
              arr[i] = {
                name: nameArr[i],
                count: countArr[i],
                percentage: percentageArr[i]
              }
            }
            if (arr.some((i) => i.count)) {
              this.workOrderStatisticsShow = true
              this.$nextTick(() => {
                if (type) {
                  this.getAlarmSourceEchart(arr)
                }
              })
            } else {
              this.workOrderStatisticsShow = false
            }
          }
        })
      } else {
        this.lsitCount = {}
        this.tableData = []
        this.workOrderStatisticsShow = false
        this.taskPointListShow = false
        this.remindData = {
          count: 0,
          dateList: []
        }
      }
    },
    // 获取保养提醒
    GetInspTaskRemindNum() {
      const params = {
        deviceId: this.roomData.deviceId,
        dateType: this.tagCurrent,
        systemCode: 2
      }
      GetInspTaskRemindNum(params).then((res) => {
        if (res.data.code === '200') {
          this.remindData = res.data.data
        }
      })
    },
    // 根据空间获取巡检点数据根据设备id或者ids获取
    getTskPoint() {
      getTaskPointSpaceList({
        spaceCode: this.roomData.ssmCodes?.substring(this.roomData.ssmCodes.lastIndexOf(',') + 1) ?? '',
        spaceLevel: 4
      }).then((res) => {
        if (res.data.code === '200') {
          if (res.data.data.list.length > 0) {
            const ids = []
            res.data.data.list.forEach((i) => {
              if (i.interspaceCode) {
                ids.push(i.interspaceCode)
              }
            })
            try {
              window.chrome.webview.hostObjects.sync.bridge.GetInspection(ids.toString())
            } catch (error) {}
          }
        }
      })
    },
    getAlarmSourceEchart(arr) {
      const getchart = echarts.init(this.$refs.workOrderStatisticsEcharts)
      const data = []
      var color = ['#8fe7ea', '#74e2a0', '#f2d988', '#e38e6f', '#f2d988']
      const xdata = Array.from(arr, ({ name }) => name)
      for (var i = 0; i < arr.length; i++) {
        const randomRgbColor = this.$tools.randomRgbColor('array')
        data.push({
          name: arr[i].name,
          value: arr[i].count,
          percentage: arr[i].percentage,
          itemStyle: {
            normal: {
              borderWidth: 1,
              shadowBlur: 200,
              color: color[i] ?? randomRgbColor[0],
              label: {
                normal: { show: true }
              }
            }
          }
        })
      }
      const option = {
        backgroundColor: '',
        legend: {
          icon: 'circle',
          orient: 'vartical',
          x: 'left',
          top: 'center',
          left: '52%',
          bottom: '0%',
          data: xdata,
          itemWidth: 8,
          itemHeight: 8,
          itemGap: 16,
          textStyle: {
            color: '#B3C2DD' //  字体颜色
          },
          formatter: function (name) {
            var oa = option.series[0].data
            for (var i = 0; i < option.series[0].data.length; i++) {
              if (name === oa[i].name) {
                return ' ' + name + '     ' + oa[i].percentage + '%'
              }
            }
          }
        },
        series: [
          {
            name: '',
            type: 'pie',
            clockWise: false,
            center: ['30%', '50%'],
            radius: ['58%', '70%'],
            label: {
              normal: {
                show: false,
                position: 'center',
                formatter: '{value|{c}}\n{label|{b}}',
                rich: {
                  value: {
                    padding: 5,
                    align: 'center',
                    verticalAlign: 'middle',
                    fontSize: 20
                  },
                  label: {
                    align: 'center',
                    verticalAlign: 'middle',
                    fontSize: 14
                  }
                }
              },
              emphasis: {
                show: true,
                textStyle: {
                  fontSize: '12',
                  color: '#fff'
                }
              }
            },
            data: data
          }
        ]
      }
      getchart.clear()
      getchart.setOption(option)
      // 随着屏幕大小调节图表
      window.addEventListener('resize', () => {
        getchart.resize()
      })
      getchart.dispatchAction({
        type: 'highlight',
        seriesIndex: 0,
        dataIndex: 0
      })
      getchart.on('mouseover', (e) => {
        // 取消默认高亮
        getchart.dispatchAction({
          type: 'downplay',
          seriesIndex: 0,
          dataIndex: 0
        })
      })
      // 鼠标移出后默认高亮
      getchart.on('mouseout', (e) => {
        getchart.dispatchAction({
          type: 'highlight',
          seriesIndex: 0,
          dataIndex: 0
        })
      })
    },
    changeType(val) {
      this.pageParams.pageNo = 1
      this.$refs.taskTable.bodyWrapper.scrollTop = 0
      this.activeType = val
      this.getInspectionDatas()
      this.GetInspTaskRemindNum()
    },
    changeDateType(val) {
      this.pageParams.pageNo = 1
      this.$refs.taskTable.bodyWrapper.scrollTop = 0
      this.tagCurrent = val
      this.$emit('sendWpfData', { btnType: val })
      this.getInspectionDatas(true)
      this.GetInspTaskRemindNum()
    },
    format(scope) {
      if (scope === 8) return '单次'
      if (scope === 6) return '每日'
      if (scope === 0) return '每周'
      if (scope === 2) return '每月'
      if (scope === 3) return '季度'
      if (scope === 5) return '全年'
    },
    openTaskPointList(row) {
      if (this.currentTaskTableRow.id === row.id && this.taskPointListShow) {
        this.$refs.taskTable.setCurrentRow({})
        this.taskPointListShow = false
        this.taskMap = {}
        this.$nextTick(() => {
          this.$refs.taskBox.$refs.boxCard.style.height = 'calc(76% - 50px)'
        })
      } else {
        this.currentTaskTableRow = row
        this.$refs.taskTable.setCurrentRow(row)
        this.taskPointListShow = true
        this.getTaskPointTableData(row.id)
        this.$nextTick(() => {
          this.$refs.taskBox.$refs.boxCard.style.height = 'calc(45% - 50px)'
        })
      }
    },
    // 根据任务id获取巡检点清单
    getTaskPointTableData(id) {
      this.pointTableLoading = true
      const params = {
        taskId: id,
        pageNo: this.pagePointParams.pageNo,
        pageSize: this.pagePointParams.pageSize
      }
      GetInspTaskPointReleaseList(params).then((res) => {
        this.pointTableLoading = false
        if (res.data.code === '200') {
          this.pagePointParams.total = parseInt(res.data.data.sum)
          if (this.pagePointParams.pageNo === 1) {
            this.pointTableData = []
          }
          this.taskMap = res.data.data.taskMap
          this.pointTableData = this.pointTableData.concat(res.data.data.list)
        }
      })
    },
    toView(row) {
      this.$nextTick(() => {
        this.$refs.taskPointDetail.hiddenDangerDetailsListShow = true
        this.$refs.taskPointDetail.getDataDetail(row.id)
        try {
          window.chrome.webview.hostObjects.sync.bridge.GetSwichFloorIsShow(true)
          window.chrome.webview.hostObjects.sync.bridge.ShowDialog(true)
        } catch (error) {}
      })
    },
    closeTaskPointDialog() {
      try {
        window.chrome.webview.hostObjects.sync.bridge.GetSwichFloorIsShow(false)
        window.chrome.webview.hostObjects.sync.bridge.ShowDialog(false)
      } catch (error) {}
    },
    allTableChange(type) {
      this.allTableComponentListShow = true
      Object.assign(this.tableCompenentData, {
        ...this.roomData,
        ...this.dialogTypeList.find((e) => e.type === type),
        deviceId: this.roomData.deviceId,
        dateType: this.tagCurrent,
        taskStatus: this.activeType,
        systemCode: 2
      })
    },
    // 打开保养提醒弹窗
    openUpkeepDialog() {
      if (this.remindData?.dateList?.length) {
        if (this.tagCurrent === 'day') {
          this.upkeepTime = this.remindData.dateList[0].taskStartTime
          this.upKeepTableListShow = true
        } else {
          this.upkeepList = this.remindData.dateList
          this.upKeepRemindDialogShow = true
        }
        try {
          window.chrome.webview.hostObjects.sync.bridge.ShowDialog(true)
        } catch (error) {}
      }
    },
    configCloseTableDialog() {
      this.allTableComponentListShow = false
    },
    tableLoadEvent() {
      if (this.pageParams.total > this.pageParams.pageNo * this.pageParams.pageSize) {
        this.pageParams.pageNo++
        this.getInspectionDatas()
      }
    },
    pointTableLoadEvent() {
      if (this.pagePointParams.total > this.pagePointParams.pageNo * this.pagePointParams.pageSize) {
        this.pagePointParams.pageNo++
        this.getTaskPointTableData(this.currentTaskTableRow.id)
      }
    },
    // 安防根据任务类型筛选任务
    filterateTask(planTypeId) {
      this.getInspectionDatas(false, { planTypeId })
    }
  }
}
</script>

<style lang="scss" scoped>
// 隐藏滚动条
::-webkit-scrollbar {
  display: none;
}
@import '../style/module.scss';
.icisComponent {
  width: 100%;
  height: 100%;
  overflow-y: auto;
  #workOrderStatisticsEcharts {
    width: 100%;
    height: 100%;
    position: relative;
    z-index: 2;
  }
  .task-list {
    height: calc(76% - 50px);
    .task-list-box {
      display: flex;
      flex-direction: column;
    }
  }
  .task-point-list {
    height: 36%;
  }
  .top_type {
    width: 100%;
    margin: 0 auto;
    padding: 0px 0px 15px 0;
    /* background-color: rgba(1, 11, 59, 0.5); */
    display: flex;
    justify-content: space-evenly;
    flex-wrap: wrap;
    .top_type_item {
      width: 18%;
      // margin: 3px 2%;
      height: 2rem;
      line-height: 2rem;
      text-align: center;
      font-size: 12px;
      font-family: PingFang-SC-Medium, PingFang-SC;
      color: #93bdff;
      background: url('~@/assets/images/center/block-bg.png') no-repeat;
      background-size: 100% 100%;
      cursor: pointer;
    }
    .activeType {
      background: url('~@/assets/images/center/block-bg-active.png') no-repeat;
      background-size: 100% 100%;
      color: #ffe3a6;
    }
  }
  .chartsContent {
    position: relative;
    // border-bottom: 1px solid #2e4989;
    .pie_background {
      left: 30%;
    }
  }
  .new-header {
    // margin: 24px 0;
    background-image: url('@/assets/images/<EMAIL>');
    background-repeat: no-repeat;
    background-size: 100% 100%;
    .title-left {
      padding-left: 38px;
    }
  }
  .activeSelect {
    padding: 0px 10px;
    background-size: 100% 100%;
    border-bottom: 2px solid #ffca64;
    background: rgba(255, 227, 166, 0.1);
  }
  .statistics-box {
    flex-shrink: 0;
    display: flex;
    margin-bottom: 8px;
    width: 100%;
    aspect-ratio: 20 / 7;
    background: url('~@/assets/images/qhdsys/insp-more-type-text-bg.png') no-repeat;
    background-size: 100% 100%;
    span {
      font-size: 16px;
    }
    .font-din {
      font-family: DIN, DIN;
      font-weight: bold;
      font-size: 38px;
    }
    .statistics-box-left,
    .statistics-box-right {
      width: 24%;
      display: flex;
      justify-content: space-around;
      flex-direction: column;
      align-items: center;
    }
    .statistics-box-center {
      width: 52%;
      display: flex;
      justify-content: space-evenly;
      flex-direction: column;
      align-items: center;
    }
  }
  .table-icon {
    display: flex;
    align-items: center;
  }
  .table-icon img {
    width: 16px;
    margin-right: 3px;
  }
  .title-right {
    align-items: center;
    padding-right: 10px;
  }
  .el-dropdown-link {
    font-size: 14px;
    font-weight: 300;
    color: #ffffff;
    line-height: 16px;
    .el-icon-arrow-down {
      font-size: 12px;
    }
  }
  ::v-deep .el-table {
    .el-table__body tr {
      cursor: pointer;
    }
  }
}
</style>
