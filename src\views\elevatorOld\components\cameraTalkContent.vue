<!--
 * @Author: hedd
 * @Date: 2023-07-04 18:56:38
 * @LastEditTime: 2023-10-09 17:40:05
 * @FilePath: \ihcrs_client_iframe\src\views\elevator\components\cameraTalkContent.vue
 * @Description:
-->
<template>
  <div class="camera-talk-content">
    <div class="talk-header">
      <div class="left-title">{{ rowData.surveyEntityName }}</div>
      <div class="right-icon" @click="goback()"></div>
    </div>
    <div class="talk-content" :class="flexFormat">
      <div class="talk-aside">
        <!-- <video-flv ref="videoflvBox" class="videoflv" /> -->
        <div v-if="hasPhoneTalk" class="talk-box"></div>
      </div>
      <div v-if="hasPhoneTalk" class="stop-btn" @click="goback()"><span class="phone-dis-icon"></span> 关闭对讲</div>
      <!-- <el-button  type="primary" ><svg-icon name="phone-stop" /> </el-button> -->
    </div>
  </div>
</template>
<script>
// import videoFlv from './videoFlv.vue'
import { getVideoList, getHlvAddress } from '@/utils/elevatorApi'
export default {
  name: 'cameraTalkContent',
  components: {
    // 'video-flv': videoFlv
  },
  props: {
    rowData: {
      type: Object,
      default: () => {}
    },
    flexGrid: {
      type: String,
      default: 'column'
    },
    hasPhoneTalk: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      videoTabsList: []
    }
  },
  computed: {
    flexFormat() {
      let classStr = 'horizontal-grid'
      switch (this.flexGrid) {
        case 'column':
          classStr = 'vertical-grid'
          break
        case 'row':
          classStr = 'horizontal-grid'
          break
      }
      return classStr
    }
  },
  mounted() {
    this.getVideoList()
  },
  methods: {
    // 摄像机列表
    getVideoList() {
      getVideoList({
        surveyCode: this.rowData.surveyEntityCode
      }).then((resp) => {
        const res = resp.data
        if (res.code === '200') {
          this.videoTabsList = res.data
          if (res.data.length) {
            // this.videoTabsValue = res.data[0].vidiconId
            this.getHlvAddress(res.data[0].vidiconId)
          } else {
            // const videoUrl = 'https://sf1-hscdn-tos.pstatp.com/obj/media-fe/xgplayer_doc_video/flv/xgplayer-demo-360p.flv'
            // this.$refs.videoflvBox.initFlvjs(videoUrl)
          }
        }
      })
    },
    // 获取摄像机地址
    getHlvAddress(id) {
      const params = {
        cameraId: id
      }
      getHlvAddress(params).then((resp) => {
        const res = resp.data
        if (res.code === '200') {
          const videoUrl = res.data
          // const videoUrl = 'http://************:18080/flv?port=1935&app=live&stream=rtmpStream'
          // const videoUrl = 'https://sf1-hscdn-tos.pstatp.com/obj/media-fe/xgplayer_doc_video/flv/xgplayer-demo-360p.flv'
          this.$nextTick(() => {
            this.$refs.videoflvBox.initFlvjs(videoUrl)
          })
        }
      })
    },
    goback() {
      this.$emit('goback')
    }
  }
}
</script>
<style scoped lang="scss">
.camera-talk-content {
  width: 100%;
  height: 100%;
  // background-color: #fff;
  .talk-header {
    height: 30px;
    padding-left: 45px;
    padding-right: 10px;
    background: url('~@/assets/images/elevator/card-title-50-bg.png') no-repeat;
    background-size: 100% 100%;
    display: flex;
    justify-content: space-between;
    align-items: center;
    .left-title {
      font-size: 15px;
      font-family: Alibaba PuHuiTi-Medium, Alibaba PuHuiTi;
      font-weight: 500;
      color: #b7cfff;
    }
    .right-icon {
      width: 24px;
      height: 24px;
      background: url('~@/assets/images/elevator/dialog-close.png') no-repeat;
      background-size: 100% 100%;
      cursor: pointer;
    }
  }
  .talk-content {
    height: calc(100% - 30px);
    background: rgba(6, 18, 78, 0.5);
    border: 1px solid #3563a9;
    border-top: none;
    padding: 24px;
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    .talk-aside {
      height: calc(100% - 50px);
      width: 100%;
      display: flex;
      justify-content: space-between;
    }
    .talk-box {
      background: url('~@/assets/images/elevator/camera-null.png') no-repeat;
      background-size: 100% 100%;
    }
    .stop-btn {
      width: 116px;
      height: 36px;
      // margin: 0 auto;
      line-height: 36px;
      text-align: center;
      border-color: #fa403c;
      background-color: #fa403c;
      border-radius: 4px;
      font-size: 14px;
      font-family: PingFang HK-Regular, PingFang HK;
      font-weight: 400;
      color: #ffffff;
      cursor: pointer;
      .phone-dis-icon {
        width: 20px;
        height: 20px;
        // margin-top: 8px;
        vertical-align: middle;
        background: url('~@/assets/images/elevator/phone-disabled.png') no-repeat;
        background-size: 100% 100%;
        display: inline-block;
        // margin-right: 8px;
      }
    }
  }
  .horizontal-grid {
    .talk-aside {
      flex-direction: row;
    }
    .videoflv,
    .talk-box {
      width: calc(50% - 8px);
      height: 100%;
    }
    .stop-btn {
      margin: 0 0 0 auto;
    }
  }
  .vertical-grid {
    .talk-aside {
      flex-direction: column;
    }
    .videoflv,
    .talk-box {
      width: 100%;
      height: calc(50% - 8px);
    }
    .stop-btn {
      margin: 0 auto;
    }
  }
}
</style>
