<!--
 * @Author: hedd
 * @Date: 2023-07-04 14:35:15
 * @LastEditTime: 2025-07-17 02:21:35
 * @FilePath: \ihcrs_client_iframe\src\views\elevator\elevatorMonitor\index.vue
 * @Description:
-->
<template>
  <div class="content">
    <div class="roomInfoManagement">
      <div ref="collapseWidth" class="left-content">
        <LeftTop v-if="isRefresh" @dateChange="dateChange"/>
        <LeftCenter v-if="isRefresh" :date="date" :dateType="dateType"/>
        <LeftBottom v-if="isRefresh" :date="date"/>

      </div>
      <div ref="collapseWidth" class="right-content">

        <RightTop v-if="isRefresh"/>
        <RightCenter v-if="isRefresh"/>
        <RightBottom v-if="isRefresh"/>
      </div>
    </div>
  </div>
</template>
<script>

import LeftTop from './components/leftTop.vue'
import LeftCenter from './components/leftCenter.vue'
import LeftBottom from './components/leftBottom.vue'
import RightTop from './components/rightTop.vue'
import RightCenter from './components/rightCenter.vue'
import RightBottom from './components/rightBottom.vue'

export default {
  name: 'elevatorMonitor',
  components: {
    LeftTop,
    LeftCenter,
    LeftBottom,
    RightTop,
    RightCenter,
    RightBottom

  },
  data() {
    return {
      dateType: 0,
      date: {
        visitDateStart: '',
        visitDateEnd: ''
      },
      timer: null,
      isRefresh: true
    }
  },
  mounted () {
    this.timer = setInterval(() => {
      this.isRefresh = false
      this.$nextTick(() => {
        this.isRefresh = true
      })
    }, 1000 * 60 * 10)
  },
  destroyed () {
    // 清理定时器等资源
    clearInterval(this.timer)
  },
  methods: {
    dateChange (date, type) {
      this.dateType = type
      this.date = date
    }
  }

}
</script>
<style lang="scss" scoped>
.content {
  width: 100%;
  height: 100%;
  position: relative;
  box-sizing: border-box;
  font-size: 14px;
  background: transparent;
  .roomInfoManagement {
    display: flex;
    justify-content: space-between;
    width: 100%;
    height: 100%;
    background: transparent;
    .left-content,
    .right-content {
      width: 26.573%;
      height: 100%;
      background: transparent;
      background: url("~@/assets/images/qhdsys/bg-mask.png") no-repeat;
      background-size: 100% 100%;
      box-sizing: border-box;
      padding: 0 16px 10px 44px;
      position: relative;
      transition: width 0.3s linear;
      overflow: hidden;
    }
    .left-content {
      left: 0;
      padding: 0 44px 10px 16px;
      background: url("~@/assets/images/qhdsys/bg-left-mask.png") no-repeat;
    }
  }
}
</style>
