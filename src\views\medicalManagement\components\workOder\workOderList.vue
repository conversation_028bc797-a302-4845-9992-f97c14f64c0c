<template>
  <div class='content'>
    <el-row>
      <el-col :span="12">
        <div class="searchItem">
          <div class="itemTiele">维修信息</div>
          <el-input placeholder="维修单号/资产编码/资产名称"></el-input>
        </div>
      </el-col>
      <el-col :span="12">
        <div class="searchItem">
          <div class="itemTiele">紧急程度</div>
          <el-input placeholder="请选择紧急程度"></el-input>
        </div>
      </el-col>
      <el-col :span="12">
        <div class="searchItem">
          <div class="itemTiele">维修结果</div>
          <el-input placeholder="请选择维修结果"></el-input>
        </div>
      </el-col>
      <el-col :span="12" class="btnWrap">
        <el-button>重置</el-button>
        <el-button>查询</el-button>
        <el-link :underline="false">
          展开
          <i class="el-icon-arrow-down"></i>
        </el-link>
      </el-col>
    </el-row>
    <div class="tableWrap">
      <div class="operationBtn">
        <el-button>报修</el-button>
        <el-button>维修单打印</el-button>
        <el-button>导出</el-button>
      </div>
      <el-table
        :data="tableData"
        height="calc(100% - 120px)"
        style="width: 100%">
        <el-table-column
          type="selection"
          width="55">
        </el-table-column>
        <el-table-column
          prop="code"
          label="维修单编码"
          width="180"
          sortable>
        </el-table-column>
        <el-table-column
          prop="degree"
          label="紧急程度"
          sortable
          width="180">
          <template slot-scope="scope">
            <span :class="'degree' + scope.row.degreeCode">
              <span class="pie"></span>
              {{ scope.row.degree }}
            </span>
          </template>
        </el-table-column>
        <el-table-column
          prop="type"
          sortable
          label="状态">
          <template slot-scope="scope">
            <span :class="'type' + scope.row.typeCode">
              <span class="pie"></span>
              {{ scope.row.type }}
            </span>
          </template>
        </el-table-column>
        <el-table-column
          prop="date"
          sortable
          label="报修时间"
          width="180">
        </el-table-column>
        <el-table-column
          prop="dept"
          label="报修科室"
          width="180">
        </el-table-column>
        <el-table-column
          prop="staff"
          label="报修人">
        </el-table-column>
        <el-table-column
          prop="result"
          label="维修结果"
          sortable
          width="180">
        </el-table-column>
        <el-table-column
          prop="serviceDept"
          label="维修班组"
          width="180">
        </el-table-column>
        <el-table-column
          label="操作"
          width="180">
          <template slot-scope="scope">
            <el-link style="color: #409EFF;" @click="toDetailTab(scope.row)">详情</el-link>
            <el-link style="margin: 0 10px;" disabled>派单</el-link>
            <el-link style="margin-right: 10px;" disabled>接单</el-link>
            <el-link disabled>更多<i class="el-icon-arrow-down"></i></el-link>
          </template>
        </el-table-column>
      </el-table>
      <div class="sic-content-table-pagination">
        <el-pagination :current-page="currentPage" :page-sizes="[15, 20, 30, 40]"
                       :page-size="pageSize" layout="total, sizes, prev, pager, next, jumper" :total="total"
                       class="pagination" @size-change="handleSizeChange" @current-change="handleCurrentChange"></el-pagination>
      </div>
    </div>
  </div>
</template>
<script>
export default {
  data() {
    return {
      tableData: [
        {
          code: 'YL2410301249101',
          degreeCode: '0',
          degree: '紧急',
          typeCode: '0',
          type: '已完工',
          date: '2024-10-30 10:40:23',
          dept: '测试部门',
          staff: '赵新兵',
          result: '----',
          serviceDept: '维修班组'
        },
        {
          code: 'YL2410301249101',
          degreeCode: '1',
          degree: '非常紧急',
          typeCode: '0',
          type: '已完工',
          date: '2024-10-30 10:40:23',
          dept: '测试部门',
          staff: '赵新兵',
          result: '完全维修',
          serviceDept: '维修班组'
        },
        {
          code: 'YL2410301249101',
          degreeCode: '0',
          degree: '紧急',
          typeCode: '1',
          type: '待验收',
          date: '2024-10-30 10:40:23',
          dept: '测试部门',
          staff: '赵新兵',
          result: '----',
          serviceDept: '维修班组'
        },
        {
          code: 'YL2410301249101',
          degreeCode: '0',
          degree: '紧急',
          typeCode: '0',
          type: '已完工',
          date: '2024-10-30 10:40:23',
          dept: '测试部门',
          staff: '赵新兵',
          result: '完全维修',
          serviceDept: '维修班组'
        },
        {
          code: 'YL2410301249101',
          degreeCode: '0',
          degree: '紧急',
          typeCode: '2',
          type: '会诊中',
          date: '2024-10-30 10:40:23',
          dept: '测试部门',
          staff: '赵新兵',
          result: '----',
          serviceDept: '维修班组'
        },
        {
          code: 'YL2410301249101',
          degreeCode: '0',
          degree: '紧急',
          typeCode: '3',
          type: '待接单',
          date: '2024-10-30 10:40:23',
          dept: '测试部门',
          staff: '赵新兵',
          result: '----',
          serviceDept: '维修班组'
        },
        {
          code: 'YL2410301249101',
          degreeCode: '0',
          degree: '紧急',
          typeCode: '0',
          type: '已完工',
          date: '2024-10-30 10:40:23',
          dept: '测试部门',
          staff: '赵新兵',
          result: '----',
          serviceDept: '维修班组'
        },
        {
          code: 'YL2410301249101',
          degreeCode: '0',
          degree: '紧急',
          typeCode: '0',
          type: '已完工',
          date: '2024-10-30 10:40:23',
          dept: '测试部门',
          staff: '赵新兵',
          result: '----',
          serviceDept: '维修班组'
        }
      ],
      currentPage: 1,
      pageSize: 15,
      total: 5,
      type: 'list'
    }
  },
  created() {},
  methods: {
    handleSizeChange() {},
    handleCurrentChange() {},
    toDetailTab() {
      this.$emit('openDetailComponent', 'workOderDetail')
    }
  }
}
</script>
<style lang="scss" scoped>
.content {
  padding: 20px;
  height: 100%;
  .searchItem {
    display: flex;
    align-items: center;
    margin-bottom: 20px;
    .itemTiele {
      width: 100px;
    }
    .el-input {
      max-width: calc(90% - 100px)
    }
  }
  .btnWrap {
    display: flex;
    justify-content: flex-end;
    padding-right: calc(10% - 75px);
    .el-link {
      margin-left: 10px;
      color: #fff;
    }
  }
  .tableWrap {
    height: calc(100% - 124px);
    .operationBtn {
      margin-bottom: 10px;
    }
    .pie {
        display: inline-block;
        width: 8px;
        height: 8px;
        border-radius: 50%;
        margin-right: 4px;
      }
    .degree0 {
      color: #FA8B2C;
      .pie {
        background-color: #FA8B2C;
      }
    }
    .degree1 {
      color: #FF4D4F;
      .pie {
        background-color: #FF4D4F;
      }
    }
    .type0 {
      color: #52C41A;
      .pie {
        background-color: #52C41A;
      }
    }
    .type1, .type2 {
      color: #FA8B2C;
      .pie {
        background-color: #FA8B2C;
      }
    }
    .type3 {
      color: #FF4D4F;
      .pie {
        background-color: #FF4D4F;
      }
    }
  }
}
</style>