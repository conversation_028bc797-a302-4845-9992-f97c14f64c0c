<template>
  <div class="parkingManagement" v-if="dialogShow">
    <div class="right-content">
      <div class="content">
        <div class="bg-title el-dialog__header"><span>停车场管理</span><i class="el-icon-close" @click="closeDeviceDialog"></i></div>
        <div class="bg-content room-info-box">
          <div class="sys-box">
            <div class="parking-content">
              <ModuleCard title="运营总览" class="module-container" style="height: 24%">
                <div slot="title-right" class="title-right">
                  <el-dropdown trigger="click" @command="changeDateType">
                    <span class="el-dropdown-link"> {{ dateType.find((v) => v.value == tagCurrent)?.name ?? '' }} <i class="el-icon-arrow-down"></i> </span>
                    <el-dropdown-menu slot="dropdown">
                      <el-dropdown-item v-for="item in dateType" :key="item.value" :command="item.value" :class="{ isBjxl: tagCurrent == item.value }">{{ item.name }}</el-dropdown-item>
                    </el-dropdown-menu>
                  </el-dropdown>
                  <!-- <div class="titleTypeItem" :class="{ active: tagCurrent === item.value }" v-for="(item, index) in dateType" :key="index" @click="changeDateType(item.value)">{{ item.name }}</div> -->
                </div>
                <div slot="content" class="moduleContent operatOverview">
                  <div class="cardItem">
                    <img src="../../assets/images/parkingLot/car.png" alt="" />
                    <div class="statisc">
                      <div class="statiscItem">入场车次</div>
                      <div class="statiscData">
                        <span class="num">{{ operatInfo.parkingInTime || 0 }}</span
                        >辆
                      </div>
                    </div>
                  </div>
                  <div class="cardItem">
                    <img src="../../assets/images/parkingLot/car.png" alt="" />
                    <div class="statisc">
                      <div class="statiscItem">出场车次</div>
                      <div class="statiscData">
                        <span class="num">{{ operatInfo.parkingOutTime || 0 }}</span
                        >辆
                      </div>
                    </div>
                  </div>
                  <div class="cardItem">
                    <img src="../../assets/images/parkingLot/statisc.png" alt="" />
                    <div class="statisc">
                      <div class="statiscItem">实时收费总额</div>
                      <div class="statiscData">
                        <span class="num">{{ operatInfo.chargeTotal || 0 }}</span
                        >元
                      </div>
                    </div>
                  </div>
                  <div class="cardItem">
                    <img src="../../assets/images/parkingLot/statisc.png" alt="" />
                    <div class="statisc">
                      <div class="statiscItem">收费笔数</div>
                      <div class="statiscData">
                        <span class="num">{{ operatInfo.chargeTime || 0 }}</span
                        >笔
                      </div>
                    </div>
                  </div>
                </div>
              </ModuleCard>
              <ModuleCard title="停车场监督" class="module-container" style="height: 22%">
                <div slot="content" class="moduleContent supervise">
                  <div class="superviseItme">
                    <img src="../../assets/images/parkingLot/P.png" alt="" />
                    <div class="superviseTitle">总车位个数</div>
                    <div class="ststisc">
                      <span class="num">{{ superviseInfo.parkingSpaceTotal }}</span>
                      个
                    </div>
                  </div>
                  <div class="superviseItme">
                    <img src="../../assets/images/parkingLot/P.png" alt="" />
                    <div class="superviseTitle">可用车位个数</div>
                    <div class="ststisc">
                      <span class="num">{{ superviseInfo.remainingSpace }}</span>
                      个
                    </div>
                  </div>
                  <div class="superviseItme">
                    <img src="../../assets/images/parkingLot/car.png" alt="" />
                    <div class="superviseTitle">总放行数</div>
                    <div class="ststisc">
                      <span class="num">{{ superviseInfo.passedTotal }}</span>
                      次
                    </div>
                  </div>
                </div>
              </ModuleCard>
              <ModuleCard title="出入库流量压力分析" class="module-container" style="height: 26%">
                <div slot="content" class="moduleContent">
                  <div id="flowAnalysis"></div>
                </div>
              </ModuleCard>
              <ModuleCard title="" class="module-container" style="height: 28%">
                <div slot="title-left" class="title-left">
                  <span :class="{ 'is-activeTab': isType === 'in' }" @click="onKg('in')">入场纪录</span>
                  <span :class="{ 'is-activeTab': isType === 'out' }" @click="onKg('out')">出场纪录</span>
                </div>
                <div slot="title-right" class="title-right">
                  <div class="title-detail" @click="toMore()">更多</div>
                </div>
                <div slot="content" style="height: 100%">
                  <el-table
                    v-el-table-infinite-scroll="tableLoadMore"
                    class="table-center-transfer"
                    :data="tableData"
                    height="100%"
                    :cell-style="{ padding: ' 8px', backgroundColor: 'transparent', border: 'none', padding: '3px' }"
                    :header-cell-style="{ background: '#8591CE26!important', color: '#8BDDF5FF', padding: '4px 8px', fontWeight: 'bold' }"
                    style="width: 100%"
                    element-loading-background="rgba(0, 0, 0, 0.2)"
                    v-loading="tableLoading"
                  >
                    <el-table-column prop="proofNum" label="凭证号" show-overflow-tooltip></el-table-column>
                    <el-table-column prop="inParkingTime" label="入场时间" show-overflow-tooltip>
                      <template slot-scope="scope">
                        <el-tooltip placement="top">
                          <div slot="content">
                            <div>{{scope.row.inParkingTime}}</div>
                          </div>
                          <div>{{moment(scope.row.inParkingTime).format('HH:mm:ss')}}</div>
                        </el-tooltip>
                      </template>
                    </el-table-column>
                    <el-table-column prop="inParkingEq" label="入场设备" show-overflow-tooltip></el-table-column>
                    <el-table-column label="视频图片" align="center">
                      <template slot-scope="scope">
                        <div class="title-detail" @click="check(scope.row)">查看</div>
                      </template>
                    </el-table-column>
                  </el-table>
                </div>
              </ModuleCard>
            </div>
          </div>
        </div>
      </div>
    </div>
    <template v-if="parkingTableComponentListShow">
      <allTableComponentList
        ref="parkingTableComponentListShow"
        :dialogData="tableCompenentData"
        :dialogShow="parkingTableComponentListShow"
        @configCloseDialog="configParkingCloseTableDialog"
        @handleDetailImage="handleImage"
      ></allTableComponentList>
    </template>
    <template v-if="parkingDetailsShow">
      <parkingDetails :show="parkingDetailsShow" :requestParams="requestParkingParams" @closeDialog="hiddenParkingDetailsDialog" />
    </template>
  </div>
</template>

<script>
import moment from 'moment'
import { operateTotal, superviseTotal, flowAnalysis, getInParkingRecord, getOutParkingRecord } from '@/utils/parking'
import * as echarts from 'echarts'
import allTableComponentList from './components/allTableComponentList.vue'
import parkingDetails from './sysTypeComponent/components/parkingDetails.vue'
export default {
  name: 'parkingLot',
  components: {
    allTableComponentList,
    parkingDetails
  },
  props: {
    dialogData: {
      type: Object,
      default: () => {}
    },
    dialogShow: {
      type: Boolean,
      default: false
    }
  },
  watch: {
    dialogShow(val) {
      if (val) {
        if (this.isType === 'in') {
          this.getInTableData()
        } else {
          this.getOutTableData()
        }
      }
    }
  },
  data() {
    return {
      moment,
      tagCurrent: '1',
      dateType: [
        { name: '日', value: '1' },
        { name: '周', value: '2' },
        { name: '月', value: '3' }
      ],
      operatInfo: {
        chargeTime: 0,
        chargeTotal: 0,
        parkingInTime: 0,
        parkingOutTime: 0
      },
      superviseInfo: {
        parkingSpaceTotal: 0,
        remainingSpace: 0,
        passedTotal: 0
      },
      flowAnalysisList: {},
      parkingTableComponentListShow: false,
      tableCompenentData: {},
      tableLoading: false,
      parkingDetailsShow: false,
      isType: 'in',
      tableData: [],
      requestParkingParams: {},
      pagination: {
        total: 0,
        pageSize: 15,
        page: 1
      }
    }
  },

  mounted() {
    this.getParkingLotStatiscData()
    this.getSuperviseTotal()
    this.getFlowAnalysis()
    this.isType = this.dialogData?.parkingLotName ?? 'in'
    if (this.isType === 'in') {
      this.getInTableData()
    } else {
      this.getOutTableData()
    }
  },
  methods: {
    // 日期类型改变
    changeDateType(val) {
      this.tagCurrent = val
      this.getParkingLotStatiscData()
    },
    // 获取停车场统计数据
    getParkingLotStatiscData() {
      operateTotal({ querySection: this.tagCurrent }).then((res) => {
        console.log(res)
        if (res.data.code === '200') {
          this.operatInfo = res.data.data
        }
      })
    },
    // 获取车位监控
    getSuperviseTotal() {
      superviseTotal({}).then((res) => {
        if (res.data.code === '200') {
          this.superviseInfo = res.data.data
        }
      })
    },
    // 出入口流量压力分析
    getFlowAnalysis() {
      flowAnalysis({}).then((res) => {
        if (res.data.code === '200') {
          this.flowAnalysisList = res.data.data
        } else {
          this.flowAnalysisList = {}
          this.$message.error(res.message)
        }
        this.initCharts()
      })
    },
    initCharts() {
      let myChart = echarts.init(document.getElementById('flowAnalysis'))
      let option = {}
      if (JSON.stringify(this.flowAnalysisList) != '{}') {
        option = {
          color: ['#FFCA64', '#61E29D'],
          backgroundColor: '',
          tooltip: {
            trigger: 'axis',
            confine: true,
            axisPointer: {
              lineStyle: {
                color: '#FFE3A6'
              }
            },
            backgroundColor: '#0C2269',
            borderColor: '#5B617C',
            textStyle: {
              color: '##FFF'
            }
          },
          legend: {
            show: false,
            data: this.flowAnalysisList.flowRateData
          },
          grid: {
            left: '30',
            right: '30',
            bottom: '10',
            top: '30',
            containLabel: true
          },
          dataZoom: [
            // {
            //   fillerColor: '#BBC3CE',
            //   backgroundColor: '#fff',
            //   height: 10,
            //   type: 'slider',
            //   bottom: 10,
            //   textStyle: {
            //     color: '#000'
            //   },
            //   start: 0,
            //   end: 85
            // },
            // {
            //   type: 'inside', // 支持内部鼠标滚动平移
            //   start: 0,
            //   end: 85,
            //   zoomOnMouseWheel: false, // 关闭滚轮缩放
            //   moveOnMouseWheel: true, // 开启滚轮平移
            //   moveOnMouseMove: true // 鼠标移动能触发数据窗口平移
            // }
          ],
          xAxis: [
            {
              type: 'category',
              data: this.flowAnalysisList.time,
              axisLabel: {
                clickable: true
              },
              axisLine: {
                lineStyle: {
                  color: '#D8DEE7' // 更改坐标轴颜色
                }
              },
              // 去除x轴上的刻度线
              axisTick: {
                show: false
              }
            }
          ],
          yAxis: [
            {
              type: 'value',
              name: '辆',
              axisLabel: {
                formatter: '{value}',
                show: true,
                textStyle: {
                  color: '#909399' // 更改坐标轴文字颜色
                }
              },
              // 控制y轴线是否显示
              axisLine: {
                lineStyle: {
                  color: '#D8DEE7' // 更改坐标轴颜色
                }
              },
              // 网格样式
              splitLine: {
                show: true,
                lineStyle: {
                  color: ['#ccc'],
                  width: 1,
                  type: 'dashed'
                }
              },
              // 去除y轴上的刻度线
              axisTick: {
                show: false
              }
            }
          ],
          series: [
            {
              // symbol: 'none',
              name: this.flowAnalysisList.flowRateData[0].name,
              data: this.flowAnalysisList.flowRateData[0].data,
              type: 'line'
              // areaStyle: {
              //   color: {
              //     x: 0,
              //     y: 1,
              //     x2: 0,
              //     y2: 0,
              //     colorStops: [
              //       {
              //         offset: 0,
              //         color: '#fff'
              //       },
              //       {
              //         offset: 1,
              //         color: '#FA403C'
              //       }
              //     ]
              //   }
              // }
            },
            {
              name: this.flowAnalysisList.flowRateData[1].name,
              data: this.flowAnalysisList.flowRateData[1].data,
              type: 'line'
              // areaStyle: {
              //   color: {
              //     x: 0,
              //     y: 1,
              //     x2: 0,
              //     y2: 0,
              //     colorStops: [
              //       {
              //         offset: 0,
              //         color: '#fff'
              //       },
              //       {
              //         offset: 1,
              //         color: '#3F63D3'
              //       }
              //     ]
              //   }
              // }
            }
          ]
        }
      } else {
        option = {
          title: {
            text: '暂无数据',
            x: 'center',
            y: 'center',
            textStyle: {
              fontSize: 14,
              fontWeight: 'normal',
              color: '#999'
            }
          }
        }
      }
      myChart.clear()
      myChart.setOption(option) // 随着屏幕大小调节图表
      window.addEventListener('resize', () => {
        myChart.resize()
      })
    },
    // 关闭弹框
    closeDeviceDialog() {
      this.$emit('configCloseDialog')
    },
    // 点击入场 出场
    onKg(type) {
      this.isType = type
      this.pagination = {
        total: 0,
        pageSize: 15,
        page: 1
      }
      if (this.isType === 'in') {
        this.getInTableData()
      } else {
        this.getOutTableData()
      }
    },
    // 入场table
    getInTableData() {
      let params = {
        querySection: '',
        sectionBegin: '',
        sectionEnd: '',
        userName: '',
        proofNum: '',
        pageNum: this.pagination.page,
        pageSize: this.pagination.pageSize
      }
      getInParkingRecord(params).then((res) => {
        this.tableLoading = false
        if (res.data.code === '200' && res.data.data && res.data.data.length > 0) {
          if (this.pagination.page === 1) {
            this.tableData = []
          }
          this.tableData = this.tableData.concat(res.data.data)
          this.pagination.total = res.data.total
        } else {
          this.tableLoading = false
          this.tableData = []
        }
      })
    },
    tableLoadMore() {
      if (this.pagination.total > this.pagination.page * this.pagination.pageSize) {
        this.pagination.page++
        this.getInTableData()
      }
    },
    check(row) {
      this.parkingDetailsShow = true
      this.requestParkingParams = {...row, objType: this.isType}
      try {
        window.chrome.webview.hostObjects.sync.bridge.ShowDialog(true)
      } catch (error) {}
    },
    handleImage(row) {
      this.parkingDetailsShow = true
      this.requestParkingParams = row
      try {
        window.chrome.webview.hostObjects.sync.bridge.ShowDialog(true)
      } catch (error) {}
    },
    // 出场table
    getOutTableData() {
      let params = {
        timeType: '',
        sectionBegin: '',
        sectionEnd: '',
        userName: '',
        voucherNo: '',
        pageNum: this.pagination.page,
        pageSize: this.pagination.pageSize
      }
      getOutParkingRecord(params).then((res) => {
        this.tableLoading = false
        if (res.data.code === '200' && res.data.data && res.data.data.length > 0) {
          if (this.pagination.page === 1) {
            this.tableData = []
          }
          this.tableData = this.tableData.concat(res.data.data)
          this.pagination.total = res.data.total
        } else {
          this.tableLoading = false
          this.tableData = []
        }
      })
    },
    // 关闭弹框
    configParkingCloseTableDialog() {
      this.parkingTableComponentListShow = false
      // try {
      //   window.chrome.webview.hostObjects.sync.bridge.ShowDialog(false)
      // } catch (error) {}
    },
    toMore() {
      this.parkingTableComponentListShow = true
      // try {
      //   window.chrome.webview.hostObjects.sync.bridge.ShowDialog(true)
      // } catch (error) {}
      Object.assign(this.tableCompenentData, {
        title: this.isType === 'in' ? '入库纪录' : '出库记录',
        type: 'parking',
        objType: this.isType,
        height: 'calc(100% - 120px)'
      })
    },
    hiddenParkingDetailsDialog() {
      this.parkingDetailsShow = false
      try {
        window.chrome.webview.hostObjects.sync.bridge.ShowDialog(false)
      } catch (error) {}
    }
  }
}
</script>

<style lang="scss" scoped>
@import './style/module.scss';
.parkingManagement {
  position: absolute;
  top: 0;
  right: 0%;
  // width: 22%;
  width: 100%;
  height: 100%;
  // margin-top: 2%;
  height: 100%;
  .content {
    padding: 0px 25px 10px 35px;
    height: 100%;
  }
  .right-content {
    margin: 0 0 0 auto;
    width: 24.573%;
    background: url('~@/assets/images/qhdsys/bg-mask.png') no-repeat;
    height: 100%;
    background-color: transparent;
    // background: url('~@/assets/images/center/bg-80.png') no-repeat;
    background-size: 100% 100%;
    box-sizing: border-box;
    position: relative;
    .bg-title {
      background-color: rgba(255, 224, 152, 0.12) !important;
      height: 44px;
      line-height: 44px;
      padding: 0 10px 0 1rem;
      color: #ffca64;
      font-family: TRENDS;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      i {
        float: right;
        line-height: 44px;
        font-size: 1.5rem;
        cursor: pointer;
      }
    }
    .bg-content {
      position: relative;
      box-sizing: border-box;
      padding: 10px 2px 10px 2px;
      width: 100%;
      height: calc(100% - 40px);
    }
    .room-info-box {
      color: #fff;
      display: flex;
      flex-direction: column;
      .box-type {
        width: 100%;
        margin: auto;
        display: flex;
        flex-wrap: wrap;
        justify-content: center;
        .tabs-item:nth-child(1) {
          flex: 2;
        }
        span {
          // display: inline-block;
          // width: fit-content;
          // height: 24px;
          // padding: 0 10px;
          // background-color: #24396d;
          // text-align: center;
          // line-height: 24px;
          // color: #dceaff;
          // font-size: 14px;
          // font-family: PingFang-SC-Medium, PingFang-SC;
          // cursor: pointer;
          // margin: 5px 3px 0 3px;
          flex: 2;
          display: inline-block;
          width: fit-content;
          cursor: pointer;
          padding: 10px 0;
          text-align: center;
          font-size: 16px;
          font-weight: 400;
          color: #a4afc1;
          line-height: 19px;
          overflow: hidden;
          background: linear-gradient(360deg, #334572 0%, rgba(38, 49, 79, 0.14) 57%, rgba(36, 46, 73, 0) 100%);
          border-left: 1px solid;
          border-bottom: 1px solid;
          border-image: linear-gradient(180deg, rgba(113, 125, 168, 0), rgba(113, 128, 168, 1)) 1 1;
        }
        .tabs-item:last-child {
          border-right: 1px solid;
        }
        .type_active {
          // color: #ffe3a6;
          // background: url('~@/assets/images/center/border-bg-select.png') no-repeat;
          // background-size: 100% 100%;
          position: relative;
          color: #b0e3fa;
          background: linear-gradient(360deg, #3a668e 0%, rgba(36, 46, 73, 0) 100%);
        }
      }
      .sys-box {
        width: 100%;
        flex: 1;
        height: 100%;
        .parking-content {
          height: 100%;
          width: 100%;
          overflow-y: auto;
          .title-right {
            display: flex;
            align-items: center;
            padding-right: 10px;
            cursor: pointer;
            .el-dropdown-link {
              font-size: 14px;
              font-weight: 300;
              color: #ffffff;
              line-height: 16px;
              .el-icon-arrow-down {
                font-size: 12px;
              }
            }
            .titleTypeItem {
              width: 38px;
              height: 26px;
              background: rgba(255, 255, 255, 0.1) linear-gradient(90deg, rgba(10, 132, 255, 0.4) 0%, rgba(10, 132, 255, 0) 100%);
              border: 1px solid;
              border-image: radial-gradient(circle, rgba(171, 240, 255, 1), rgba(226, 254, 255, 1), rgba(132, 196, 203, 1), rgba(48, 151, 166, 0)) 1 1;
              font-size: 14px;
              text-align: center;
              line-height: 26px;
              margin-left: 5px;
              color: #0a84ff;
              cursor: pointer;
            }
            .active {
              color: #fff;
            }
          }
          .moduleContent {
            width: 100%;
            height: 100%;
            #flowAnalysis {
              width: 100%;
              height: 100%;
              overflow: hidden;
            }
          }
          .operatOverview {
            display: flex;
            flex-wrap: wrap;
            padding-top: 16px;
            padding-bottom: 6px;
            .cardItem {
              padding-bottom: 10px;
              width: 50%;
              height: 50%;
              display: flex;
              img {
                width: 50px;
                height: 100%;
              }
              .statisc {
                width: calc(100% - 50px);
                margin-left: 5px;
                height: 100%;
                text-align: center;
                font-size: 14px;
                .statiscItem {
                  color: #b0e3fa;
                  width: 100%;
                  text-align: center;
                  margin-bottom: 6px;
                }

                .statiscData {
                  width: calc(100% - 10px);
                  font-size: 12px;
                  .num {
                    color: #ffca64;
                    font-size: 18px;
                    margin-right: 5px;
                  }
                  color: #a4afc1;
                  height: 30px;
                  // text-align: center;
                  line-height: 30px;
                  background: url('~@/assets/images/parkingLot/group.png') no-repeat;
                  background-size: 100% 100%;
                }
              }
            }
          }
          .supervise {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0;
            .superviseItme {
              width: 33%;
              text-align: center;
              font-size: 16px;
              color: #b0e3fa;
              img {
                width: 50px;
                height: 50px;
              }
              .superviseTitle {
                font-size: 16px;
                margin: 10px 0;
              }
              .ststisc {
                font-size: 12px;
                color: #a4afc1;
                .num {
                  color: #ffca64;
                  font-size: 18px;
                }
              }
            }
          }
        }
        // height: calc(100% - 24px);
      }
    }
  }
}
.title-detail {
  cursor: pointer;
  font-size: 14px;
  font-weight: 400;
  color: #8bddf5;
}
.title-left span:nth-child(2) {
  margin-left: 16px;
}
.title-left {
  color: #a4afc1;
  font-family: HarmonyOS Sans SC-Bold;
  font-size: 16px;
  font-weight: bold;
}
.is-activeTab {
  color: #ffffff;
}
</style>
