<template>
  <div class="content">
    <div class="left-box">
      <div class="title">
        <span>报警信息</span>
      </div>
      <div class="info-box">
        <div>
          <span>报警来源</span>
          <span>氧气系统</span>
        </div>
        <div>
          <span>报警类型</span>
          <span>设备参数报警</span>
        </div>
        <div>
          <span>报警设备</span>
          <span>氧气站1#液氧罐</span>
        </div>
        <div>
          <span>报警位置</span>
          <span>门诊大楼B1层204</span>
        </div>
        <div>
          <span style="margin-right: 21px">监测项目名称</span>
          <span>1#液氧罐</span>
        </div>
        <div>
          <span>报警参数</span>
          <span>实时罐压</span>
        </div>
        <div>
          <span>报警数值</span>
          <span>氧气系统</span>
        </div>
        <div>
          <span>报警等级</span>
          <span>紧急</span>
        </div>
        <div>
          <span>报警时间</span>
          <span>2022-02-09 13:09:10</span>
        </div>
      </div>
      <div class="title">
        <span>报警处置</span>
      </div>
      <div class="alarm-handle">
        <img src="../../assets/images/war/relation.png" />
        <span>关联工单</span>
        <span>20220201</span>
        <span>已派工</span>
        <div class="add-item">
          <span>+ 创建工单</span>
        </div>
      </div>
      <div class="btns">
        <div class="btn">
          <img src="../../assets/images/sys/alarm-icon.png" />
          <span>确警</span>
        </div>
        <div class="btn">
          <img src="../../assets/images/sys/alarm-icon.png" />
          <span>演习</span>
        </div>
        <!-- <div class="btn sino-button-sure">
          <span>误报</span>
        </div> -->
        <el-button class="sino-button-sure" @click="_resetCondition">重置</el-button>
      </div>
    </div>
    <div class="right-box">
      <div class="title">
        <span>关联视频</span>
      </div>
      <div class="video-box">
        <div>
          <video-player ref="videoPlayer" class="video-player vjs-custom-skin" :playsinline="true" :options="playerOptions"></video-player>
        </div>
        <div>
          <video-player ref="videoPlayer" class="video-player vjs-custom-skin" :playsinline="true" :options="playerOptions"></video-player>
        </div>
        <div>
          <video-player ref="videoPlayer" class="video-player vjs-custom-skin" :playsinline="true" :options="playerOptions"></video-player>
        </div>
        <div>
          <video-player ref="videoPlayer" class="video-player vjs-custom-skin" :playsinline="true" :options="playerOptions"></video-player>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  data() {
    return {
      playerOptions: {
        // playbackRates: [0.7, 1.0, 1.5, 2.0], //播放速度
        autoplay: true, // 如果true,浏览器准备好时开始回放。
        muted: true, // 默认情况下将会消除任何音频。
        loop: false, // 导致视频一结束就重新开始。
        preload: 'auto', // 建议浏览器在<video>加载元素后是否应该开始下载视频数据。auto浏览器选择最佳行为,立即开始加载视频（如果浏览器支持）
        language: 'zh-CN',
        aspectRatio: '16:10', // 将播放器置于流畅模式，并在计算播放器的动态大小时使用该值。值应该代表一个比例 - 用冒号分隔的两个数字（例如"16:9"或"4:3"）
        fluid: true, // 当true时，Video.js player将拥有流体大小。换句话说，它将按比例缩放以适应其容器。
        sources: [
          {
            type: 'application/x-mpegURL', // 这里的种类支持很多种：基本视频格式、直播、流媒体等，具体可以参看git网址项目
            src: 'http://61.150.108.155:7086/live/cameraid/1000005%240/substream/1.m3u8' // url地址
          }
        ],
        hls: true,
        poster: '', // 你的封面地址
        width: document.documentElement.clientWidth, // 播放器宽度
        notSupportedMessage: '此视频暂无法播放，请稍后再试', // 允许覆盖Video.js无法播放媒体源时显示的默认信息。
        controlBar: {
          timeDivider: false,
          durationDisplay: false,
          remainingTimeDisplay: false,
          fullscreenToggle: false // 全屏按钮
        }
      },
    }
  },
  methods: {}
}
</script>

<style lang="scss" scoped>
.content {
  width: 100%;
  color: #fff;
  padding-top: 24px;
  display: flex;
  justify-content: space-between;
}
.content .left-box {
  width: 35%;
}
.content .right-box {
  width: 64%;
}
.content > div {
  text-align: center;
}
.title {
  height: 30px;
}
.title span {
  display: block;
  color: #dceaff;
  width: 200px;
  height: 100%;
  line-height: 30px;
  margin: 0 auto;
  background-image: url(../../assets/images/war/title-bg.png);
  background-size: 100% 100%;
  background-repeat: no-repeat;
  font-family: 'TRENDS';
}
.info-box {
  width: 587px;
  height: 320px;
  background-image: url(../../assets/images/war/alarm-box.png);
  background-size: 100% 100%;
  background-repeat: no-repeat;
  margin: 40px 0;
  padding-top: 18px;
  box-sizing: border-box;
}
.info-box > div {
  font-size: 14px;
  display: flex;
  height: 32px;
  line-height: 32px;
}
.info-box > div span:nth-child(1) {
  color: #ac5166;
  padding-left: 228px;
  margin-right: 48px;
}
.info-box > div span:nth-child(2) {
  color: rgba(255, 255, 255, 0.9);
}
.video-box {
  display: flex;
  justify-content: space-between;
  flex-wrap: wrap;
  margin-top: 40px;
}
.video-box > div {
  width: 588px;
  height: 400px;
  background-image: url(../../assets/images/war/video-border.png);
  background-size: 100% 100%;
  background-repeat: no-repeat;
  margin-bottom: 24px;
}
.alarm-handle {
  margin-top: 34px;
  display: flex;
  justify-content: center;
}
.alarm-handle img {
  width: 20px;
  height: 20px;
}
.alarm-handle > span {
  margin: 0 12px;
}
.alarm-handle > span:nth-child(2) {
  color: rgba(126, 174, 249, 1);
}
.alarm-handle > span:nth-child(3) {
  color: rgba(255, 255, 249255, 0.9);
}
.alarm-handle > span:nth-child(4) {
  color: rgba(126, 174, 249, 1);
}
.add-item {
  margin-left: 12px;
  color: #ffe3a6;
  cursor: pointer;
}
.btns {
  display: flex;
  justify-content: space-around;
  width: 50%;
  margin: 24px auto;
}
.btns .btn {
  width: 80px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  cursor: pointer;
}
.btns .btn:nth-child(1),
.btns .btn:nth-child(2) {
  background-image: url(../../assets/images/sys/btn-red.png);
  background-size: 100% 100%;
  background-repeat: no-repeat;
  color: #ff7070;
}
.btns .btn:nth-child(1):hover,
.btns .btn:nth-child(2):hover {
  background-image: url(../../assets/images/sys/btn-red-hover.png);
}
.btns img {
  width: 18px;
  height: 18px;
  margin-right: 5px;
  transform: translateY(-1px);
}
::v-deep .video-js .vjs-tech {
  object-fit: fill;
}
::v-deep .vjs-poster {
  background-size: cover;
}
::v-deep .vjs-custom-skin > .video-js {
  width: 98%;
  height: 390px;
  margin: 5px auto;
}
</style>
