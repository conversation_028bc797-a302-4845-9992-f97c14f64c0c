<template>
  <div class="main">
    <el-dialog v-dialogDrag :visible.sync="addDialogShow" :close-on-click-modal="false" :before-close="closeDialog" class="personDialog">
      <template slot="title">
        <span class="dialog-title">{{ dialogTitle }}</span>
      </template>
      <div class="dialog-content" style="width: 100%">
        <el-form ref="formInline" :model="formInline" :rules="rules" :inline="true" label-width="auto" size="medium">
          <div class="formRow">
            <el-form-item label="登录名：" prop="userName">
              <el-input v-if="type !== 'detail'" v-model.trim="formInline.userName" placeholder="请输入登录名"></el-input>
              <span class="form-detail-span" v-else>{{ formInline.userName }}</span>
            </el-form-item>
          </div>
          <div class="formRow">
            <el-form-item label="姓名：" prop="staffName">
              <el-input v-if="type !== 'detail'" v-model.trim="formInline.staffName" placeholder="请输入姓名"></el-input>
              <span class="form-detail-span" v-else>{{ formInline.staffName }}</span>
            </el-form-item>
          </div>
          <div class="formRow">
            <el-form-item label="电话：" prop="phone">
              <el-input v-if="type !== 'detail'" v-model.trim="formInline.phone" placeholder="请输入电话"></el-input>
              <span class="form-detail-span" v-else>{{ formInline.phone }}</span>
            </el-form-item>
          </div>
          <div class="formRow">
            <el-form-item label="工号：" prop="jobNumber">
              <el-input v-if="type !== 'detail'" v-model.trim="formInline.jobNumber" placeholder="请输入工号"></el-input>
              <span class="form-detail-span" v-else>{{ formInline.jobNumber }}</span>
            </el-form-item>
          </div>
          <div v-if="type !== 'detail'" class="formRow">
            <el-form-item label="密码：" prop="passWord">
              <el-input v-model.trim="formInline.passWord" type="password" show-password placeholder="请输入密码"></el-input>
            </el-form-item>
          </div>
          <div v-if="type !== 'detail'" class="formRow">
            <el-form-item label="确认密码：" prop="rePassword">
              <el-input v-model.trim="formInline.rePassword" type="password" show-password placeholder="请确认登录密码"></el-input>
            </el-form-item>
          </div>
          <div class="formRow" style="display: flex">
            <el-form-item label="用户角色：" prop="roleId">
              <el-select v-if="type !== 'detail'" v-model="formInline.roleId" multiple placeholder="请选择角色">
                <el-option v-for="item in roleOptions" :key="item.id" :label="item.roleName" :value="item.id"> </el-option>
              </el-select>
              <span class="form-detail-span" v-else>{{ formInline.roleName }}</span>
            </el-form-item>
          </div>
          <div class="formRow">
            <el-form-item label="状态：" prop="state">
              <el-radio v-if="type !== 'detail'" v-model="formInline.state" :label="0">启用</el-radio>
              <el-radio v-if="type !== 'detail'" v-model="formInline.state" :label="1">禁用</el-radio>
              <span class="form-detail-span" v-else>{{ formInline.state === 0 ? '启用' : formInline.state === 1 ? '禁用' : '' }}</span>
            </el-form-item>
          </div>
          <div class="formRow"></div>
        </el-form>
      </div>
      <span slot="footer" class="dialog-footer" v-if="type !== 'detail'">
        <el-button class="sino-button-sure" @click="closeDialog">取 消</el-button>
        <el-button class="sino-button-sure" type="primary" @click="userSaveFn">保 存</el-button>
      </span>
    </el-dialog>
  </div>
</template>
<script>
import { submitUser, getRoleList } from '@/utils/api'
export default {
  name: 'addUser',
  props: {
    addDialogShow: {
      type: Boolean,
      default: false
    },
    dialogTitle: {
      type: String,
      default: ''
    },
    rowData: {
      type: Object,
      default: function () {
        return {}
      }
    },
    type: {
      type: String,
      default: ''
    }
  },
  data() {
    // /^[A-Za-z0-9]+$/ 校验登录名 数字+字母
    // /^\S*(?=\S{6,})(?=\S*\d)(?=\S*[A-Z])(?=\S*[a-z])(?=\S*[!@#$%^&*? ])\S*$/ 密码校验 最少6位，包括至少1个大写字母，1个小写字母，1个数字，1个特殊字符
    var validatePass = (rule, value, callback) => {
      // 这里每一种情况都要callback，不然表单验证会失效
      if (value === '') {
        callback(new Error('请输入密码'))
      } else {
        if (this.formInline.rePassword !== '') {
          this.$refs.formInline.validateField('rePassword')
        }
        callback()
      }
    }
    var validatePass2 = (rule, value, callback) => {
      // 这里每一种情况都要callback，不然表单验证会失效
      if (value === '') {
        callback(new Error('请再次输入密码'))
      } else if (value !== this.formInline.passWord) {
        callback(new Error('两次输入密码不一致!'))
      } else {
        callback()
      }
    }
    var validateStaffName = (rule, value, callback) => {
      // 这里每一种情况都要callback，不然表单验证会失效
      const CellPoe = /^[a-zA-Z\u4E00-\u9FA5\uf900-\ufa2d·s]{2,20}$/
      if (value === '') {
        callback()
      } else if (!CellPoe.test(value)) {
        callback(new Error('姓名格式填写错误!'))
      } else {
        callback()
      }
    }
    var validatePhoneNumber = (rule, value, callback) => {
      // 这里每一种情况都要callback，不然表单验证会失效
      const Cellpho = /^[1][3,4,5,7,8][0-9]{9}$/
      if (value === '') {
        callback()
      } else if (!Cellpho.test(value)) {
        callback(new Error('电话格式填写错误!'))
      } else {
        callback()
      }
    }
    return {
      formInline: {
        userName: '',
        staffName: '',
        jobNumber: '',
        phone: '',
        passWord: '',
        rePassword: '',
        state: 0,
        roleId: []
      },
      roleOptions: [],
      rules: {
        // 登录名校验
        userName: [
          { required: true, message: '请输入登录名', trigger: 'blur' },
          { min: 2, max: 50, message: '长度在 2 到 50 个字符', trigger: 'blur' }
        ],
        // 姓名校验
        staffName: [{ required: false }, { validator: validateStaffName, trigger: 'blur' }],
        // 电话校验
        phone: [{ required: false }, { validator: validatePhoneNumber, trigger: 'blur' }],
        jobNumber: [{ required: false }, { min: 2, max: 50, message: '长度在 2 到 50 个字符', trigger: 'blur' }],
        // 密码校验
        passWord: [
          { required: true, message: '请输入密码', trigger: 'blur' },
          { min: 6, max: 16, message: '长度在 6 到 16 个字符', trigger: 'blur' },
          { validator: validatePass, trigger: 'blur' }
        ],
        // 确认密码校验
        rePassword: [
          { required: true, message: '请输入确认密码', trigger: 'blur' },
          { validator: validatePass2, trigger: 'blur' }
        ],
        // 角色校验
        roleId: [{ required: true, message: '请选择角色', trigger: 'blur' }]
      }
    }
  },
  watch: {},
  mounted() {
    const newData = JSON.parse(JSON.stringify(this.rowData))
    if (this.type === 'edit') {
      newData.rePassword = newData.passWord || ''
      newData.roleId = newData.roleId ? newData.roleId.split(',').map(Number) : ''
      delete newData.roleName
    }
    Object.assign(this.formInline, newData)
    if (this.type !== 'detail') {
      this.getRoleList()
    }
  },
  methods: {
    // 取消按钮
    closeDialog() {
      this.$emit('closeDialog')
    },
    // 确认按钮
    userSaveFn() {
      const formData = JSON.parse(JSON.stringify(this.formInline))
      this.$refs.formInline.validate((valid) => {
        if (valid) {
          formData.roleId = formData.roleId.toString()
          delete formData.rePassword
          submitUser(formData).then((res) => {
            const data = res.data
            if (data.code === '200') {
              this.$message({
                message: this.dialogTitle + '成功！',
                type: 'success'
              })
              this.$emit('userSure', this.type)
            } else {
              this.$message({
                message: data.msg,
                type: 'warning'
              })
            }
          })
        } else {
          console.log('error submit!!')
          // return false
        }
      })
    },
    getRoleList() {
      getRoleList().then((res) => {
        const data = res.data
        if (data.code === '200') {
          this.roleOptions = data.data
        } else {
          this.$message({
            message: data.msg,
            type: 'warning'
          })
        }
      })
    }
  }
}
</script>

<style lang="scss" type="text/css" scoped>
// .main {
//   width: 50%;
//   height: 40px;
//   display: inline-block;
//   line-height: 40px;
// }

.formRow {
  width: 100%;
  display: flex;
  ::v-deep .el-form-item {
    flex: 1;
    display: flex;
  }
  ::v-deep .el-form-item__content {
    flex: 1;
  }
  .el-select ::v-deep .el-tag {
    border-color: #ffe3a6;
    color: #ffe3a6;
    background: center;
    .el-icon-close {
      display: none;
    }
  }
}
</style>
