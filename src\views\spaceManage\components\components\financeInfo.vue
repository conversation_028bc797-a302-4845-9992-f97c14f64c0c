<template>
  <div>
    <el-row>
      <el-col :span="8">
        <div class="item">
          <span class="item-title">资产财务分类代码</span>
          <span class="item-content">{{ detailsInfo.financialClassificationCode || '--' }}</span>
        </div>
        <div class="item">
          <span class="item-title">项目名称</span>
          <span class="item-content">{{ detailsInfo.projectName || '--' }}</span>
        </div>
        <div class="item">
          <span class="item-title">部门经济分类编码</span>
          <span class="item-content">{{ detailsInfo.deptEcoClassifySubjectCode || '--' }}</span>
        </div>
      </el-col>
      <el-col :span="8">
        <div class="item">
          <span class="item-title">资产财务分类名称</span>
          <span class="item-content">{{ detailsInfo.financialClassificationName || '--' }}</span>
        </div>
        <div class="item">
          <span class="item-title">功能分类科目编码</span>
          <span class="item-content">{{ detailsInfo.statisticsCode || '--' }}</span>
        </div>
        <div class="item">
          <span class="item-title">部门经济分类名称</span>
          <span class="item-content">{{ detailsInfo.deptEcoClassifySubjectName || '--' }}</span>
        </div>
      </el-col>
      <el-col :span="8">
        <div class="item">
          <span class="item-title">项目编号</span>
          <span class="item-content">{{ detailsInfo.projectCode || '--' }}</span>
        </div>
        <div class="item">
          <span class="item-title">功能分类科目名称</span>
          <span class="item-content">{{ detailsInfo.statisticsName || '--' }}</span>
        </div>
      </el-col>
    </el-row>
  </div>
</template>
<script>
export default {
  name: 'financeInfo',
  props: ['detailsInfo'],
  data() {
    return {}
  },
  methods: {},
  mounted() {}
}
</script>

<style scoped lang="scss">
  .item {
    margin-bottom: 12px;
    font-size: 14px;
    display: flex;
    height: 20px;
    line-height: 20px;
    .item-title {
      color: #7EAEF9;
      width: 120px;
    }
    .item-content {
      color: #fff;
    }
  }
</style>
