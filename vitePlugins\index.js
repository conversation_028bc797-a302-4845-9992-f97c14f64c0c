/*
 * @Author: hedd
 * @Date: 2023-02-24 13:48:57
 * @LastEditTime: 2024-07-17 15:14:02
 * @FilePath: \ihcrs_client_iframe\vitePlugins\index.js
 * @Description:
 */
// import requireTransform from 'vite-plugin-require-transform'
import vueJsx from '@vitejs/plugin-vue2-jsx'
import vue from '@vitejs/plugin-vue2'
import OptimizationPersist from 'vite-plugin-optimize-persist'
import PkgConfig from 'vite-plugin-package-config'
import viteCompression from 'vite-plugin-compression'
// import topLevelAwait from 'vite-plugin-top-level-await'
import createHtml from './html'
import createSvgIcon from './svg-icon'
import fileList from './fileList'
// 让浏览器支持commonjs语法
import { viteCommonjs } from '@originjs/vite-plugin-commonjs'
// 兼容require
import vitePluginRequire from 'vite-plugin-require'

export default function createVitePlugins(viteEnv, isBuild = false) {
  const vitePlugins = [
    vueJsx(),
    vue(),
    PkgConfig(),
    viteCompression({
      verbose: true,
      disable: false,
      threshold: 10240,
      algorithm: 'gzip',
      ext: '.gz'
    }),
    OptimizationPersist(),
    fileList(),
    viteCommonjs(),
    vitePluginRequire() // 兼容require
  ]
  vitePlugins.push(createHtml(viteEnv, isBuild))
  vitePlugins.push(createSvgIcon(isBuild))
  return vitePlugins
}
