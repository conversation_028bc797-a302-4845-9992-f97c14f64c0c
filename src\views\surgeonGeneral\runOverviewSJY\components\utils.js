
export function formatThousand (num) {
  if (num == null || isNaN(num)) return num

  const number = Number(num)
  const isDecimal = number % 1 !== 0
  let formattedNum

  if (isDecimal) {
    // 保留两位小数，并去掉末尾多余的 0
    const str = number.toFixed(2)
    const [intPart, decPart] = str.split('.')
    const decimal = parseFloat(`0.${decPart}`)
    const intNum = Number(intPart)

    // 去掉无意义的小数点后 0
    if (decimal === 0) {
      formattedNum = intNum.toLocaleString()
    } else {
      formattedNum = `${intNum.toLocaleString()}.${decPart.replace(/\.?0+$/, '')}`
    }
  } else {
    formattedNum = number.toLocaleString()
  }

  return formattedNum
}
// 这个方法只适用于四军医的日期格式化，因为如果是日的话，结束日期需要加1，例如[2025-07-16，2025-07-17]
import dayjs from 'dayjs'

export function getDefaultDateRange(type) {
  const today = dayjs()
  let start, end

  switch (type) {
    case 0: // 日
      start = today.format('YYYY-MM-DD')
      end = today.add(1, 'day').format('YYYY-MM-DD')
      break
    case 1: // 月
      start = today.startOf('month').format('YYYY-MM-DD')
      end = today.endOf('month').format('YYYY-MM-DD')
      break
    case 2: // 年
      start = today.startOf('year').format('YYYY-MM-DD')
      end = today.endOf('year').format('YYYY-MM-DD')
      break
    default:
      start = end = ''
  }

  return [start, end]
}