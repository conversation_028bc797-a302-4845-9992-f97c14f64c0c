<!--
 * @Author: hedd
 * @Date: 2023-07-04 19:16:12
 * @LastEditTime: 2024-02-06 19:11:10
 * @FilePath: \ihcrs_client_iframe\src\views\elevator\components\rtspCavas.vue
 * @Description:
-->
<template>
  <div
    v-loading="cavasLoading"
    element-loading-text="视频加载中"
    element-loading-spinner="el-icon-loading"
    element-loading-background="rgba(0, 0, 0, 0.4)"
    class="jsmpeg-content"
    @mouseenter="handlePlayerMouseEnter"
    @mouseleave="handlePlayerMouseLeave"
  >
    <canvas v-if="hasCavas" id="cameraCanvas" ref="cameraCanvas" @mousemove.passive="handleCanvasMouseMove" @dblclick="toggleFullscreen"></canvas>
    <div v-else class="empty-cavas"></div>
    <div class="overlayers" v-show="playerHover">
      <el-button type="text" icon="el-icon-full-screen" @click="toggleFullscreen"></el-button>
      <span class="player-title">{{videoName}}</span>
    </div>
  </div>
</template>
<script>
import fullscreen from './fullscreen.js'
const defaultOptions = () => ({
  /** 是否循环播放视频(仅静态文件)。默认true */
  autoplay: true,
  /** 是否解码音频。默认true */
  audio: true,
  /** 是否解码视频。默认true */
  video: true,
  /** 预览图像的URL，用来在视频播放之前作为海报显示。 */
  poster: null,
  /** 是否禁用后台播放，当web页面处于非活动状态时是否暂停播放，默认true（注意，浏览器通常会在非活动标签中限制JS） */
  pauseWhenHidden: true,
  /** 是否禁用WebGL，始终使用Canvas2D渲染器。默认false */
  disableGl: false,
  /** 是否禁用WebAssembly并始终使用JavaScript解码器。默认false */
  disableWebAssembly: false,
  /** WebGL上下文是否创建必要的“截图”。默认false */
  preserveDrawingBuffer: true,
  /** 是否以块的形式加载数据(仅静态文件)。当启用时，回放可以在完整加载源之前开始，默认true */
  progressive: true,
  /** 当不需要回放时是否推迟加载块。默认=progressive */
  throttled: true,
  /** 使用时，以字节为单位加载的块大小。默认(1 mb)1024*1024 */
  chunkSize: 1024 * 1024,
  /** 是否解码并显示视频的第一帧，一般用于设置画布大小以及使用初始帧作为"poster"图像。当使用自动播放或流媒体资源时，此参数不受影响。默认true */
  decodeFirstFrame: true,
  /** 流媒体时，以秒为单位的最大排队音频长度。（可以理解为能接受的最大音画不同步时间） */
  maxAudioLag: 0.25,
  /** 流媒体时，视频解码缓冲区的字节大小。默认的512 * 1024 (512 kb)。对于非常高的比特率，您可能需要增加此值。 */
  videoBufferSize: 1024 * 1024,
  /** 流媒体时，音频解码缓冲区的字节大小。默认的128 * 1024 (128 kb)。对于非常高的比特率，您可能需要增加此值。 */
  audioBufferSize: 256 * 1024
})
export default {
  name: 'rtspCavas',
  props: {
    options: {
      type: Object,
      default: defaultOptions
    },
    rtspUrl: {
      type: String,
      default: ''
    },
    videoName: {
      type: String,
      default: ''
    },
    hasCavas: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      jsmpegPlayer: null, // 播放器实体
      cavasLoading: false, // cavas加载状态
      playerHover: false, // 播放器是否鼠标悬浮
      canvasMouseMoveTimers: null, // canvas鼠标移动定时器
      playerFullscreen: false // 播放器是否全屏
    }
  },
  watch: {
    // rtspUrl() {
    //   if (this.rtspUrl == null || this.rtspUrl === '') {
    //     this.destroyPlayer()
    //   } else {
    //     this.initPlayer()
    //   }
    // },
    options: {
      deep: true,
      handler() {
        this.initPlayer()
      }
    }
  },
  mounted() {
    this.initPlayer()
    this.$once('hook:beforeDestroy', () => {
      this.destroyPlayer()
    })
  },
  methods: {
    initPlayer() {
      if (!this.hasCavas) return
      this.cavasLoading = true
      this.destroyPlayer()
      // rtspUrl = 'rtsp://admin:Sinomis+123@192.168.0.65:554/h264/ch1/main/av_stream'
      /* eslint-disable */
      this.jsmpegPlayer = new JSMpeg.Player(__PATH.VUE_APP_RTSP_LIVE_WS + '/rtsp?url=' + btoa(this.rtspUrl), {
        canvas: this.$refs['cameraCanvas'],
        ...this.options,
        onPlay: () => {
          console.log('[JSMpegPlayer] 事件触发 -> 播放开始')
          setTimeout(() => {
            this.cavasLoading = false
          }, 250)
        },
        onPause: () => {
          console.log('pause')
        },
        onEnded: () => {
          console.log('end')
        },
        onStalled: () => {
          console.log('没有足够的数据用于播放')
          this.destroyPlayer()
        }
      })
    },
    destroyPlayer() {
      if (this.jsmpegPlayer) {
        this.jsmpegPlayer.destroy()
        this.jsmpegPlayer = null
      }
    },
    // 鼠标移入
    handlePlayerMouseEnter() {
      console.log('handlePlayerMouseEnter')
      this.playerHover = true
    },
    // 鼠标移出
    handlePlayerMouseLeave() {
      console.log('handlePlayerMouseLeave')
      clearTimeout(this.canvasMouseMoveTimers)
      this.playerHover = false
    },
    // 摄像机实体上鼠标移动
    handleCanvasMouseMove() {
      console.log('handleCanvasMouseMove')
      this.playerHover = true
      clearTimeout(this.canvasMouseMoveTimers)
      this.canvasMouseMoveTimers = setTimeout(() => {
        this.playerHover = false
      }, 3000)
    },
    // 切换全屏
    toggleFullscreen() {
      if (this.playerFullscreen) {
        fullscreen.exit(this.$el)
      } else {
        fullscreen.request(this.$el, () => {
          this.playerFullscreen = false
        })
      }
      this.playerFullscreen = !this.playerFullscreen
    }
  }
}
</script>
<style lang="scss" scoped>
.jsmpeg-content {
  width: 100%;
  height: 100%;
  position: relative;
  #cameraCanvas {
    width: 100%;
    height: 100%;
  }
  .empty-cavas {
    width: 100%;
    height: 100%;
    background: url('~@/assets/images/elevator/camera-null.png') no-repeat;
    background-size: 100% 100%;
  }
  .overlayers {
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 32px;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    justify-content: space-between;
    padding: 0 20px;
    box-sizing: border-box;
    .el-button {
      color: #fff;
      height: 32px;
      line-height: 32px;
      padding: 0;
      margin: auto 0;
      min-width: auto;
      background: none;
      &:hover {
        color: #fff;
      }
    }
    .player-title {
      font-size: 12px;
      font-family: HarmonyOS Sans-Regular, HarmonyOS Sans;
      font-weight: 400;
      color: #ffffff;
      margin: auto 0;
    }
    // .overlayers-content {
    //   position: absolute;
    //   top: 0;
    //   right: 0;
    //   padding: 10px;
    //   .el-button {
    //     color: #fff;
    //     &:hover {
    //       color: #fff;
    //     }
    //   }
    // }
  }
}
</style>
