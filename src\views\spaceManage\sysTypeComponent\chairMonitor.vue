<template>
  <div class="chairMonitor">
    <div class="module-container" style="height: calc(22%)">
      <div class="module-header">
        <div class="title-left" style="width: 60%">
          <p class="title-left-text">椅位统计</p>
        </div>
        <div class="title-right">
          <div class="icon-box" @click="viewMore">
            <img src="../../../assets/images/order_more.png" class="order-more" />
          </div>
        </div>
      </div>
      <div class="module-content" style="height: calc(100% - 44px)">
        <div class="statistics_top_new">
          <div v-for="(item, index) in statisticsData" :key="index" class="statistics_top_item">
            <img src="@/assets/images/center/yw-icon.png" alt="" />
            <div class="statistics_top_content">
              <p class="statistics_top_title">{{ item.name }}</p>
              <div class="font_bg">
                <p class="color_font" :style="{ color: item.key == 'abnormalDeviceTotal' ? '#FF2D55' : '' }">{{ chairStatistics[item.key] || 0 }}</p>
                <p class="color_unit">{{ item?.unit ?? '' }}</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="module-container" style="height: calc(28%)">
      <div class="module-header">
        <div class="title-left">
          <p class="title-left-text">椅位状态</p>
        </div>
        <div class="title-right">
          <el-dropdown trigger="click" @command="(val) => (tagCurrent = val)">
            <span class="el-dropdown-link"> {{ tagList.find((v) => v.value == tagCurrent)?.text ?? '' }} <i class="el-icon-arrow-down"></i> </span>
            <el-dropdown-menu slot="dropdown">
              <el-dropdown-item v-for="item in tagList" :key="item.value" :command="item.value" :class="{ isBjxl: tagCurrent == item.value }">{{ item.text }}</el-dropdown-item>
            </el-dropdown-menu>
          </el-dropdown>
        </div>
      </div>
      <div class="module-content" style="height: calc(100% - 48px)">
        <div id="EChart" style="height: 100%"></div>
      </div>
    </div>
    <div class="module-container" style="height: calc(50%)">
      <div class="module-header">
        <div class="title-left">
          <p class="title-left-text" style="cursor: pointer" :style="{ color: isChair == true ? '#a6afbf' : '#fff' }" @click="changeChairShow(1)">科室列表</p>
          <p class="title-left-text" style="cursor: pointer; padding-left: 16px" :style="{ color: isChair == true ? '#fff' : '#a6afbf' }" @click="changeChairShow(2)">
            椅位列表
          </p>
        </div>
        <div class="title-right">
          <div v-show="isChair" class="icon-box" @click="viewMore">
            <img src="../../../assets/images/order_more.png" class="order-more" />
          </div>
        </div>
      </div>
      <div class="module-content" style="height: calc(100% - 48px);">
        <div v-if="!isChair" v-scrollbarHover v-loading="deptLoading" style="height: 100%; overflow-y: auto;">
          <div v-if="!surgeryList.length" class="empty-div"><span>暂无数据</span></div>
          <div v-for="(item,index) in surgeryList" :key="index" class="surgery-list-item">
            <div class="item-title" @click="tableRowClick(1, item)">
              <span style="flex: 1; margin-right: 10px">{{ item.relevantDeptName || '-' }}</span>
              <span class="title-detail" @click.stop="() => surgeryList[index].isOpen = !surgeryList[index].isOpen">
                {{ item.isOpen ? '收起' : '展开' }} <i class="el-icon-arrow-down"></i></span>
            </div>
            <div class="item-info">
              <div class="item-info-box">
                <p class="item-info-box-name">椅位状态：</p>
                <p class="item-info-box-value">
                  {{ item.inUse + '/' + item.totalCount}}，<span style="color: #FF2D55;">异常占用{{item.abnormal }}</span>
                </p>
              </div>
              <div class="item-info-box">
                <p class="item-info-box-name">待就诊数：</p>
                <p class="item-info-box-value">{{ item.idle || 0 }}</p>
              </div>
              <div class="item-info-box" style="width: 100%;">
                <p class="item-info-box-name">所在楼层：</p>
                <p class="item-info-box-value">{{ item.spaceName || '-' }}</p>
              </div>
            </div>
            <div v-show="item.isOpen" class="item-table">
              <el-table
                class="table-center-transfer"
                :data="item.chairs"
                height="100%"
                :cell-style="{ padding: ' 8px', backgroundColor: 'transparent', border: 'none', padding: '3px', color: '#fff'}"
                :header-cell-style="{ background: '#8591CE26!important', color: '#8BDDF5FF', padding: '3px', fontWeight: 'bold',}"
                style="width: 100%"
              >
                <el-table-column prop="assetsName" label="椅位名称" show-overflow-tooltip></el-table-column>
                <el-table-column prop="assetsStatus" label="状态" width="100" show-overflow-tooltip>
                  <template slot-scope="scope">
                    {{ scope.row.assetsStatus == 0 ? '空闲' : scope.row.assetsStatus == 1 ? '使用中' : scope.row.assetsStatus == 2 ? '异常占用' : ''}}
                  </template>
                </el-table-column>
              </el-table>
            </div>
          </div>
        </div>
        <el-table
          v-else
          v-el-table-infinite-scroll="tableLoadMore"
          v-scrollHideTooltip
          v-loading="deviceTableLoading"
          class="table-center-transfer"
          :data="deviceList"
          height="100%"
          :cell-style="{ padding: ' 8px', backgroundColor: 'transparent', border: 'none', padding: '3px' }"
          :header-cell-style="{ background: '#8591CE26!important', color: '#8BDDF5FF', padding: '4px 8px', fontWeight: 'bold' }"
          style="width: 100%"
          element-loading-background="rgba(0, 0, 0, 0.2)"
          @row-click="(row) => tableRowClick(2, row)"
        >
          <el-table-column v-for="(column, index) in deviceTableColumn" :key="index" :prop="column.prop" :label="column.label" :min-width="column.minWidth" :width="column.width" show-overflow-tooltip>
            <template slot-scope="scope">
              <table-render v-if="column.render" :sc="scope" :row="scope.row" :render="column.render"></table-render>
              <div v-else-if="!column.formatter">
                {{ scope.row[column.prop] }}
              </div>
              <div v-else>
                {{ column.formatter(scope.row) }}
              </div>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </div>
    <monitorDeviceListDialog v-if="isMonitorDeviceList" :isDialog="isMonitorDeviceList" :roomData="roomData" :isSpecial="true" :webSocketObj="webSocketObj" @close="() => isMonitorDeviceList = false"/>
  </div>
</template>

<script lang="jsx">
import { GetChairStatistics, GetFloorChairStatus, GetMonitoringItemsList, GetDepartmentAndChairList, GetDepartmentAndChairListWebsocket } from '@/utils/spaceManage'
import * as echarts from 'echarts'
import tableRender from '../components/tableRender.vue'
export default {
  name: 'chairMonitor',
  components: {
    tableRender,
    monitorDeviceListDialog: () => import('../components/monitorDeviceListDialog.vue')
  },
  props: {
    roomData: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      isChair: false,
      isMonitorDeviceList: false,
      deptLoading: false,
      deviceTableLoading: false,
      deviceList: [], // 设备列表
      surgeryList: [], // 科室列表
      chairStatistics: {}, // 椅位统计
      statisticsData: [
        { name: '椅位总数', key: 'chairTotal', unit: '台' },
        { name: '空闲', key: 'idle', unit: '台' },
        { name: '使用中', key: 'inUse', unit: '台' },
        { name: '异常占用', key: 'abnormalOccupancy', unit: '台' }
      ], // 统计数据
      deviceTableColumn: [
        {
          prop: 'assetsName',
          label: '椅位名称'
        },
        {
          prop: 'relevantDeptName',
          label: '所在科室',
          width: 80
        },
        {
          prop: 'spaceName',
          label: '所属位置',
          render: (h, { row }) => {
            return (
              <div>
                {row?.spaceName?.split('>').reverse().join(' > ')}
              </div>
            )
          }
        },
        {
          prop: 'chairStatus',
          label: '状态'
        }
      ],
      pagination: {
        total: 0,
        pageSize: 15,
        pageNo: 1
      },
      chartData: [],
      ssmCodes: [],
      tagCurrent: 1, // 筛选类型
      tagList: [
        { text: '楼层', value: 1 },
        { text: '科室', value: 2 }
      ],
      initParams: {},
      webSocket: null, // WebSocket连接对象
      webSocketConnected: false, // WebSocket连接状态
      reconnectAttempts: 0, // 重连尝试次数
      maxReconnectAttempts: 3, // 最大重连次数
      webSocketObj: {},
      reconnectTimer: null, // 重连定时器
      createWebSocketTimer: null // 创建WebSocket定时器
    }
  },
  watch: {
    // 监听模型层级数据变化
    roomData: {
      handler(newVal, oldVal) {
        this.initData()
      },
      deep: true
    },
    tagCurrent(val) {
      this.getFloorChairStatus()
    }
  },
  mounted() {
    // 初始化调用
    this.initData()
    // 创建WebSocket连接
    this.createWebSocket()
  },
  beforeDestroy() {
    // 组件销毁前关闭WebSocket连接
    this.closeWebSocket()
  },
  methods: {
    initData() {
      this.ssmCodes = this.roomData.ssmCodes?.split(',') || []
      this.pagination.pageNo = 1
      this.getChairStatistics()
      this.getFloorChairStatus()
      this.getDepartmentAndChairList()
    },
    // 创建WebSocket连接
    createWebSocket() {
      // 关闭之前的连接
      this.closeWebSocket()
      
      try {
        // 创建新的WebSocket连接
        const socket = GetDepartmentAndChairListWebsocket(this.$store.state.loginInfo.user.staffId)
        socket.onopen = () => {
          console.log('椅位监测WebSocket连接已建立')
          this.webSocketConnected = true
          this.reconnectAttempts = 0 // 重置重连次数
        }
        socket.onmessage = this.onMessage
        socket.onerror = (error) => {
          console.error('椅位监测WebSocket连接错误:', error)
          this.webSocketConnected = false
          // 连接错误时尝试重连
          this.reconnectWebSocket()
        }
        socket.onclose = () => {
          console.log('椅位监测WebSocket连接已关闭')
          this.webSocketConnected = false
          // 如果不是主动关闭的连接，尝试重连
          if (this.webSocket) {
            this.reconnectWebSocket()
          }
        }
        this.webSocket = socket
      } catch (error) {
        console.error('创建WebSocket连接失败:', error)
        this.webSocketConnected = false
        // 创建连接失败时尝试重连
        // 清除之前的定时器
        if (this.createWebSocketTimer) {
          clearTimeout(this.createWebSocketTimer)
        }
        this.createWebSocketTimer = setTimeout(() => {
          this.reconnectWebSocket()
        }, 2000) // 延迟2秒后重连
      }
    },
    // 重新连接WebSocket
    reconnectWebSocket() {
      if (this.reconnectAttempts < this.maxReconnectAttempts) {
        console.log(`尝试重新连接WebSocket，第${this.reconnectAttempts + 1}次重连`)
        this.reconnectAttempts++
        // 清除之前的定时器
        if (this.reconnectTimer) {
          clearTimeout(this.reconnectTimer)
        }
        this.reconnectTimer = setTimeout(() => {
          this.createWebSocket()
        }, 2000) // 重连间隔
        return true
      } else {
        console.error(`WebSocket连接失败，已达到最大重连次数(${this.maxReconnectAttempts})`)
        return false
      }
    },
    // 接收WebSocket消息
    onMessage(event) {
      try {
        const data = JSON.parse(event.data)
        // 判断是不是椅位监测
        if (data?.msgTxt?.sysOfCode === 'YWJC') {
          console.log('收到WebSocket消息:', data)
          if (this.isMonitorDeviceList) {
            // 只有当弹窗打开如果有消息推送才保存通知子组件更新状态
            this.webSocketObj = data
          }

          if (this.isChair) {
            // 当前是在椅位列表才可以操作
            const { id, propertyList } = data?.msgTxt
            
            // 更新椅位列表中的状态
            const foundIndex = this.deviceList.findIndex(item => item.id === id)
            if (foundIndex > -1) {
              // 如果有propertyList，更新椅位状态
              if (propertyList && propertyList?.length) {
                // 使用this.$set更新iotPropertyList整个数组，确保视图响应式更新
                this.$set(this.deviceList[foundIndex], 'iotPropertyList', propertyList)
                
                // 更新椅位状态
                const statusProperty = propertyList.find(prop => prop.metadataTag === 'operatingStatus')
                if (statusProperty) {
                  this.$set(this.deviceList[foundIndex], 'chairStatus', statusProperty.valueText || '-')
                  console.log(`已更新椅位 ${id} 的状态为: ${statusProperty.valueText || '-'}`)
                }  
              }
            }
          } else if (!this.isChair) {
            // 不在椅位列表就只用刷新科室列表
            this.getDepartmentAndChairList()         
          }
          // 更新统计数据（只要推消息就需要刷新椅位统计和椅位状态）
          this.getChairStatistics()
          this.getFloorChairStatus()
        }
      } catch (error) {
        console.error('解析WebSocket消息出错:', error)
      }
    },
    // 关闭WebSocket连接
    closeWebSocket() {
      // 清除所有定时器
      if (this.reconnectTimer) {
        clearTimeout(this.reconnectTimer)
        this.reconnectTimer = null
      }
      if (this.createWebSocketTimer) {
        clearTimeout(this.createWebSocketTimer)
        this.createWebSocketTimer = null
      }
      // 清除WebSocket连接
      if (this.webSocket) {
        this.webSocket.close()
        this.webSocket = null
      }
    },
    viewMore() {
      this.isMonitorDeviceList = true
    },
    // 获取椅位状态
    getFloorChairStatus() {
      const parmas =  {
        spaceId: this.ssmCodes.at(-1),
        queryType: this.tagCurrent
      }
      GetFloorChairStatus(parmas).then((res) => {
        if (res.data.code == 200) {
          this.chartData = res.data.data
          this.$nextTick(() => {
            this.getRenderer(res.data.data ? res.data.data.reverse() : [])
          })
        }
      })
    },
    // 获取椅位统计
    getChairStatistics() {
      const params = {
        spaceId: this.ssmCodes.at(-1)
      }
      GetChairStatistics(params).then((res) => {
        if (res.data.code == 200) {
          this.chairStatistics = res.data.data
        }
      })
    },
    // 初始化统计表
    getRenderer(data) {
      // 基于准备好的dom，初始化echarts实例
      const EChart = echarts.init(document.getElementById('EChart'))
      const barWidth = 14 /* 进度条宽度 */
      const idleData = [] /* 空闲 */
      const inuseData = [] /* 使用中 */
      const abnormalData = [] /* 异常 */
      const attaVal = [] /* 进度条数值 */
      const topName = [] /* 进度条名称 */
      data.forEach((item, i) => {
        topName[i] = {
          value: this.tagCurrent == 1 ? item.spaceName : (item.relevantDeptName || '-'),
          textStyle: {
            color: '#FFF'
          }
        }
        attaVal[i] = item.totalCount
        idleData[i] = item.idle
        inuseData[i] = item.inUse
        abnormalData[i] = item.abnormal
      })
      // 配置参数
      let config
      if (data.length) {
        config = {
          background: '#ffff',
          tooltip: {
            show: false,
            textStyle: {
              fontSize: 16
            }
          },
          grid: {
            left: '2%',
            right: '10%',
            top: '13%',
            bottom: '0%',
            containLabel: true
          },
          dataZoom: [
            {
              yAxisIndex: [0, 1],
              orient: 'vertical',
              show: true,
              type: 'slider',
              start: 100,
              // end: 100,
              width: 8,
              left: '99%',
              borderColor: 'rgba(43,48,67,.1)',
              fillerColor: '#6580b8',
              zoomLock: true,
              showDataShadow: false,
              backgroundColor: 'rgba(43,48,67,.1)',
              showDetail: false,
              // realtime: true,
              filterMode: 'filter',
              handleIcon: 'M-9.35,34.56V42m0-40V9.5m-2,0h4a2,2,0,0,1,2,2v21a2,2,0,0,1-2,2h-4a2,2,0,0,1-2-2v-21A2,2,0,0,1-11.35,9.5Z',
              handleStyle: {
                color: '#6580b8',
                borderColor: '#6580b8'
              },
              maxValueSpan: 4,
              minValueSpan: 4,
              brushSelect: false
            },
            {
              type: 'inside',
              show: false,
              yAxisIndex: [0, 1],
              height: 8
            }
          ],
          legend: {
            show: true,
            top: '3%',
            itemHeight: 8,
            itemWidth: 8,
            itemGap: 20,
            textStyle: {
              fontSize: 12,
              color: '#fff'
            },
            selectedMode: false
          },
          xAxis: {
            show: false,
            type: 'value'
          },
          yAxis: [
            {
              type: 'category',
              // inverse: true,
              triggerEvent: true,
              axisTick: {
                show: false
              },
              axisLine: {
                show: false
              },
              axisLabel: {
                textStyle: {
                  color: '#fff',
                  fontSize: 14
                }
              },
              data: topName
            }
          ],
          series: [
            {
              name: '空闲',
              stack: 'total',
              type: 'bar',
              barWidth: barWidth,
              data: idleData,
              itemStyle: {
                normal: {
                  color: ' rgba(101,234,162,0.6)',
                  borderColor: '#2C344C',
                  borderWidth: 1
                }
              }
            },
            {
              name: '异常',
              type: 'bar',
              barWidth: barWidth,
              stack: 'total',
              data: abnormalData,
              itemStyle: {
                normal: {
                  color: 'rgba(255,45,85,0.6)',
                  borderColor: '#2C344C',
                  borderWidth: 1
                }
              }
            },
            {
              name: '使用中',
              type: 'bar',
              barWidth: barWidth,
              stack: 'total',
              data: inuseData,
              itemStyle: {
                normal: {
                  color: 'rgba(212,222,236,0.6)',
                  borderColor: '#2C344C',
                  borderWidth: 1
                }
              }
            },
            // total
            {
              type: 'bar',
              zlevel: 1,
              barWidth: barWidth,
              barGap: '-100%',
              label: {
                show: true,
                position: 'right',
                distance: 8,
                textStyle: { color: '#DADEE1', fontSize: 14 }
              },
              itemStyle: {
                color: 'transparent'
              },
              data: attaVal
            }
          ]
        }
      } else {
        config = {
          title: {
            text: '暂无数据',
            x: 'center',
            y: 'center',
            textStyle: {
              fontSize: 14,
              fontWeight: 'normal',
              color: '#999'
            }
          }
        }
      }
      if (data.length > 4) {
        data.dataZoom = [
          {
            yAxisIndex: [0],
            orient: 'vertical',
            show: true,
            type: 'slider',
            startValue: 100,
            // end: 100,
            width: 8,
            left: '99%',
            borderColor: 'rgba(43,48,67,.1)',
            fillerColor: '#6580b8',
            zoomLock: true,
            showDataShadow: false,
            backgroundColor: 'rgba(43,48,67,.1)',
            showDetail: false,
            // realtime: true,
            filterMode: 'filter',
            handleIcon: 'M-9.35,34.56V42m0-40V9.5m-2,0h4a2,2,0,0,1,2,2v21a2,2,0,0,1-2,2h-4a2,2,0,0,1-2-2v-21A2,2,0,0,1-11.35,9.5Z',
            handleStyle: {
              color: '#6580b8',
              borderColor: '#6580b8'
            },
            maxValueSpan: 4,
            minValueSpan: 4,
            brushSelect: false
          },
          {
            type: 'inside',
            show: false,
            yAxisIndex: [0],
            height: 8
          }
        ]
      }
      EChart.clear()
      // 设置参数
      EChart.setOption(config)
      // 先移除点击事件 解决点击事件重复绑定
      EChart.off('click')
      // 点击事件
      EChart.on('click', (params) => {
        const index = params.dataIndex
        // 新增变量 判断当前点击柱状图是选中还是取消选中
        let isSelected = false
        let name = ''
        topName.map((e, i) => {
          // 选中的设置选中色
          if (i == index && e.textStyle.color != '#FFCA64FF') {
            e.textStyle.color = '#FFCA64FF'
            name = e.value
          } else {
            // 选中已选中的则为取消选中
            if (i == index && e.textStyle.color == '#FFCA64FF') {
              isSelected = true
            }
            // 其他的设为默认色
            e.textStyle.color = '#FFF'
          }
        })
        config.yAxis.data = JSON.parse(JSON.stringify(topName))
        EChart.setOption(config)
        // 取消选中 则恢复过滤条件
        this.selectFilterParam = ''
        this.pagination.pageNo = 1
        // 取消选中 则恢复过滤条件
        if (!isSelected) {
          this.selectFilterParam = this.tagCurrent == 1 ? this.chartData[index].spaceId : this.chartData[index].relevantDept
        }
        this.isChair ? this.getMonitoringItemsList() : this.getDepartmentAndChairList()
      })
    },
    // 获取科室列表
    getDepartmentAndChairList() {
      let params = {}
      if (this.selectFilterParam) {
        this.tagCurrent == 1 ? params.spaceId = this.selectFilterParam : params.relevantDept = this.selectFilterParam
      } else {
        params.spaceId = this.ssmCodes.at(-1)
      }
      this.deptLoading = true
      GetDepartmentAndChairList(params).then((res) => {
        if (res.data.code == 200) {
          res.data.data.forEach((item) => {
            item.isOpen = false
          })
          this.surgeryList = res.data.data
        }
      }).finally(() => {
        this.deptLoading = false
      })
    },
    // 获取椅位列表
    getMonitoringItemsList() {
      let params = {
        equipAttr: 2,
        page: this.pagination.pageNo,
        pageSize: this.pagination.pageSize,
        sysOfCode: this.roomData.projectCode,
        sysOf1Code: this.roomData.categoryCode
      }
      if (this.selectFilterParam) {
        this.tagCurrent == 1 ? params.spaceId = this.selectFilterParam : params.relevantDept = this.selectFilterParam
      } else {
        params.spaceId = this.ssmCodes.at(-1)
      }
      this.deviceTableLoading = true
      GetMonitoringItemsList(params).then((res) => {
        if (res.data.code == 200) {
          if (this.pagination.pageNo == 1) this.deviceList = []
          res.data.data.records.forEach(item => {
            if (item.iotPropertyList.length) {
              item.chairStatus = item.iotPropertyList.find(v => v.metadataTag == 'operatingStatus')?.valueText ?? '-'
            }
          })
          this.deviceList = this.deviceList.concat(res.data.data.records)
          this.pagination.total = res.data.data.total
        }
      }).finally(() => {
        this.deviceTableLoading = false
      })
    },
    tableRowClick(type, row) {
      let newObj = {
        1: [{ name: '科室详情', component: 'deptDetails' }],
        2: [{ name: '椅位详情', component: 'basicAccountComponent' }]
      }
      let params = {}
      if (type == 1) {
        params = {
          assetsId: row.relevantDept,
          assetName: row.relevantDeptName,
          spaceId: row.spaceId
        }
      } else {
        params = {
          assetsId: row.id,
          assetName: row.assetsName,
          DeviceCode: row.modelCode
        }
      }
      this.$emit('roomEvent', { type: 'manyMove', detailsNav: newObj[type], ...params})
      try {
        window.chrome.webview.hostObjects.sync.bridge.EquipmentRealView(JSON.stringify(params))
      } catch (error) {}
    },
    tableLoadMore() {
      if (this.pagination.total > this.pagination.pageNo * this.pagination.pageSize) {
        this.pagination.pageNo++
        this.getMonitoringItemsList()
      }
    },
    changeChairShow(type) {
      this.isChair = type == 2
      if (type == 1) {
        this.getDepartmentAndChairList()
      } else {
        this.getMonitoringItemsList()
      }
    }
  }
}
</script>

<style lang="scss" scoped>
@import '../style/module.scss';
.statistics_top_new {
  height: 130px;
  display: flex;
  flex-wrap: wrap;
  .statistics_top_item {
    height: 50%;
    width: 50%;
    // padding: 16px 0;
    display: flex;
    justify-content: center;
    align-items: center;
    img {
      width: 50px;
      height: 50px;
    }
  }
  .statistics_top_content {
    .statistics_top_title {
      padding-left: 20px;
      font-size: 14px;
      font-family: HarmonyOS Sans SC-Regular, HarmonyOS Sans SC;
      font-weight: 400;
      color: #b0e3fa;
      margin-bottom: 6px;
    }
    .font_bg {
      width: 125px;
      height: 32px;
      background: url(./../../../assets/images/qhdsys/Group-new.png) no-repeat 100%;
      display: flex;
      align-items: center;
      justify-content: center;
      .color_font {
        font-size: 20px;
        font-family: HarmonyOS Sans SC-Bold, HarmonyOS Sans SC;
        font-weight: bold;
        color: #ffca64;
        margin-bottom: 3px;
      }
      .color_unit {
        margin-left: 3px;
        font-size: 14px;
        font-family: HarmonyOS Sans SC-Light, HarmonyOS Sans SC;
        font-weight: 300;
        color: rgba(255, 255, 255, 0.5);
      }
    }
  }
}
.chairMonitor {
  width: 100%;
  height: 100%;
  .title-left {
    padding-left: 20px;
  }
  .title-detail {
    cursor: pointer;
    font-size: 14px;
    font-family: HarmonyOS Sans SC-Regular, HarmonyOS Sans SC;
    font-weight: 400;
    color: #8bddf5 !important;
    margin-right: 13px;
  }
  .icon-box {
    cursor: pointer;
    margin-right: 0;
    display: flex;
    align-items: center;
    position: relative;
  }
  ::v-deep .el-table {
    .el-table__body tr {
      cursor: pointer;
    }
  }
  .surgery-list-item {
    border: 1px solid rgba(133,145,206,0.5);
    background: rgba(133, 145, 206, 0.15);
    margin-top: 10px;
    padding-top: 16px;
    .item-title {
      // background: rgba(133, 145, 206, 0.15);
      padding: 0px 16px;
      font-size: 15px;
      font-weight: 500;
      color: #ffffff;
      line-height: 18px;
      display: flex;
      align-items: center;
      cursor: pointer;
    }
    .item-info {
      padding: 0px 16px 16px 16px;
      display: flex;
      flex-wrap: wrap;
      line-height: 20px;
      .item-info-box {
        margin-top: 16px;
        width: calc(100% / 2);
        font-size: 14px;
        font-weight: 400;
        color: #ffffff;
      }
      .item-info-box-name {
        color: #C4F3FE;
      }
      .item-info-box-value {
        text-align: left;
        margin-top: 4px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
    }
    .item-table {
      border-top: 1px solid rgba(133,145,206,0.5);
    }
  }
  .empty-div {
    width: 100%;
    height: 100%;
    display: flex;
    > span {
      font-size: 14px;
      color: #999;
      margin: auto;
    }
  }
}
// 滚动条的宽度
::v-deep .el-table__body-wrapper::-webkit-scrollbar {
  width: 6px; // 横向滚动条
  height: 6px; // 纵向滚动条 必写
}

// 注意:hover
::v-deep .el-table__body-wrapper:hover::-webkit-scrollbar {
  width: 6px; // 横向滚动条
  height: 6px; // 纵向滚动条 必写
}
::v-deep .el-table__body-wrapper::-webkit-scrollbar-corner,
::v-deep .el-table__body-wrapper::-webkit-scrollbar-track {
  background-color: transparent;
}
.module-header {
  width: 100%;
  // padding-left: 50px;
  background: url('~@/assets/images/qhdsys/bg-bt.png') no-repeat;
}

.order-more {
  width: 24px;
  height: 24px;
  margin-right: 0 !important;
  margin-left: 8px;
}
</style>
