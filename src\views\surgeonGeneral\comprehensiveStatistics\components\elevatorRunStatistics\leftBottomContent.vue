<template>
  <div class="statistics_item">
    <div class="item_title">电梯运载排行</div>
    <div id="treeMap" ref="treemap"></div>
  </div>
</template>
<script>
import * as echarts from 'echarts'
import { adsLiftFloorCountList } from '@/utils/comprehensiveStatistics'
export default {

  data () {
    return {
      myChart: null
    }
  },
  mounted() {
    this.getData()
  },
  methods: {
    getData() {
      adsLiftFloorCountList().then((res) => {
        if (res.data.code == 200) {
          this.initChart(res.data.data)
        }
      })
    },
    initChart(data) {
      let chartDom = document.getElementById('treeMap')
      this.myChart = echarts.init(chartDom)
      let option = {}
      if (data && data.length) {
        option = {
          yAxis: [
            {
              type: 'category',
              position: 'left', // 左侧 Y 轴
              axisLabel: {
                color: '#fff'
              },
              data: data.map((item) => item.assetsName)
            }
          ],
          xAxis: {
            type: 'value',
            splitLine: {
              show: true,
              lineStyle: {
                color: 'rgba(230,247,255,0.2)'
              }
            }
          },
          grid: {
            left: '2%',
            right: '10%',
            top: '5',
            bottom: '10',
            containLabel: true
          },
          series: [
            {
              data: data.map((el) => {
                return {
                  value: el.inPeopleNum
                }
              }),
              type: 'bar',
              barWidth: 10,
              barGap: '10%',
              itemStyle: {
                color: '#8BDDF5'
              }
            }
          ],
          dataZoom: [
            {
              yAxisIndex: [0, 1],
              orient: 'vertical',
              show: true,
              type: 'slider',
              start: 0,
              end: 100,
              width: 8,
              left: '99%',
              borderColor: 'rgba(43,48,67,.1)',
              fillerColor: '#6580b8',
              zoomLock: false,
              showDataShadow: false,
              backgroundColor: 'rgba(43,48,67,.1)',
              showDetail: false,
              // realtime: true,
              filterMode: 'filter',
              handleIcon: 'path://M-9.35,34.56V42m0-40V9.5m-2,0h4a2,2,0,0,1,2,2v21a2,2,0,0,1-2,2h-4a2,2,0,0,1-2-2v-21A2,2,0,0,1-11.35,9.5Z',
              handleStyle: {
                color: '#6580b8',
                borderColor: '#6580b8'
              },
              maxValueSpan: 5,
              minValueSpan: 5,
              brushSelect: false
            },
            {
              type: 'inside', // 内置型数据区域缩放组件
              yAxisIndex: [0, 1], // 对应的 Y 轴索引
              start: 0, // 数据窗口范围的起始百分比
              end: 50 // 数据窗口范围的结束百分比
            }
          ]
        }
      } else {
        option = {
          title: {
            text: '暂无数据',
            x: 'center',
            y: 'center',
            textStyle: {
              fontSize: 14,
              fontWeight: 'normal',
              color: '#999'
            }
          }
        }
      }
      this.myChart.setOption(option)
      this.myChart.off('click')
      // 图例点击事件
      this.myChart.on('click', (params) => {
        this.$router.push('/DTXT/operationalMonitoring')
      })
      this.myChart.hideLoading()
    }
  }
}
</script>
<style lang="scss" scoped>
#treeMap {
  width: 100%;
  height: calc(100% - 26px);
}
.item_title {
  font-weight: 500;
  font-size: 16px;
  color: #b0e3fa;
  margin-bottom: 10px;
}
</style>
