<template>
  <div class="constructionDetails">
    <div class="module-container" style="height: calc(100%); background: rgba(133,145,206,0.15);">
      <div class="info-header clear">
        <p class="info-header-text fl">施工作业信息</p>
        <p class="info-header-status fl" :style="{color: `rgb(${statusObj[infoData.assignmentStatus]?.color ?? ''}, 1)`, background: `rgb(${statusObj[infoData.assignmentStatus]?.color ?? ''}, 0.2)`}">
          {{ statusObj[infoData.assignmentStatus]?.name ?? '---' }}
        </p>
        <!-- <p class="info-header-more fr">更多详情</p> -->
      </div>
      <div class="module-content info-list" style="height: calc(100% - 44px)">
        <div v-for="item in infoList" :key="item.label" class="info-list-item">
          <p class="item-label">{{ item.label }}：</p>
          <p v-if="item.key == 'assignmentType'" class="item-value">{{ infoData.assignmentType == 0 ? '作业' : infoData.assignmentType == 1 ? '作业证' : '---' }}</p>
          <p v-else-if="item.key == 'locationType'" class="item-value">{{ infoData.locationType == 0 ? '室内' : infoData.locationType == 1 ? '室外' : '---' }}</p>
          <p v-else class="item-value">{{ infoData[item.key] || '---' }}</p>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { getAssignmentInfo } from '@/utils/spaceManage'
export default {
  name: 'constructionDetails',
  props: {
    roomData: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      statusObj: {
        0: {name: '未开始', color: '255, 148, 53'},
        1: {name: '进行中', color: '139, 221, 245'},
        2: {name: '作业结束', color: '97, 226, 157'},
        3: {name: '超时', color: '255, 45, 85'}
      },
      infoList: [
        {label: '作业名称', key: 'projectName'},
        {label: '作业位置', key: 'projectLocationName'},
        {label: '创建人', key: 'createName'},
        {label: '创建时间', key: 'createTime'},
        {label: '上级单位编码', key: 'unitCode'},
        {label: '医院编码', key: 'hospitalCode'},
        {label: '作业类型', key: 'assignmentType'}, // 0:作业,1:作业证
        {label: '施工地点', key: 'locationType'}, // 0:室内 ,1:室外
        {label: '作业开始时间', key: 'projectStartTime'},
        {label: '作业结束时间', key: 'projectEndTime'},
        {label: '申请单位', key: 'applicantDepartmentName'},
        {label: '施工单位', key: 'constructionUnitName'},
        {label: '描述', key: 'description'}
      ],
      infoData: {}
    }
  },
  computed: {

  },
  created() {
    this.getAssignmentInfo()
  },
  methods: {
    getAssignmentInfo() {
      let params = {
        projectCode: this.roomData.deviceId
      }
      getAssignmentInfo(params).then(res => {
        if (res.data.code == 200) {
          this.infoData = res.data.data
        }
      })
    }
  }
}

</script>

<style lang="scss" scoped>
@import '../style/module.scss';
.constructionDetails {
  padding: 5px 0px 0px 0px;
  .module-header {
    padding-left: 30px;
    padding-right: 10px;
    width: 100%;
    background: url("@/assets/images/qhdsys/bg-bt.png") no-repeat;
    background-size: contain;
  }
  .info-header {
    padding: 6px 8px;
    line-height: 22px;
    background: rgba(133,145,206,0.15);
    .info-header-text {
      font-weight: 500;
      font-size: 15px;
      color: #FFFFFF;
    }
    .info-header-status {
      margin-left: 10px;
      font-weight: 400;
      font-size: 14px;
      line-height: 16px;
      padding: 3px 8px;
      border-radius: 100px;
    }
    .info-header-more {
      font-size: 14px;
      cursor: pointer;
    }
  }
  .info-list {
    padding: 0px 16px;
    .info-list-item {
      display: flex;
      .item-label {
        font-weight: 400;
        font-size: 14px;
        color: #B0E3FA;
        line-height: 38px;
        width: 110px;
      }
      .item-value {
        font-weight: 400;
        font-size: 14px;
        color: #FFFFFF;
        line-height: 38px;
        flex: 1;
      }
    }
  }
  .title-right {
    font-size: 14px;
    cursor: pointer;
  }
}
</style>
