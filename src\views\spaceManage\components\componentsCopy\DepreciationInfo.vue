<template>
  <div>
    <el-row>
      <el-col :span="8">
        <div class="item">
          <span class="item-title">使用年限</span>
          <span class="item-content">{{ detailsInfo.使用年限 || '--' }}</span>
        </div>
        <div class="item">
          <span class="item-title">折旧方式</span>
          <span class="item-content">{{ detailsInfo.折旧方式 || '--' }}</span>
        </div>
      </el-col>
      <el-col :span="8">
        <div class="item">
          <span class="item-title">资产净值</span>
          <span class="item-content">{{ detailsInfo.资产净值 || '--' }}</span>
        </div>
        <div class="item">
          <span class="item-title">资产原值</span>
          <span class="item-content">{{ detailsInfo.资产原值 || '--' }}</span>
        </div>
        <!-- <div class="item">
          <span class="item-title">经费来源</span>
          <span class="item-content">
            <el-popover
              placement="top"
              width="200"
              trigger="click">
              <div>
                <div style="display: flex">
                  <span style="width: 80px">财政拨款：</span>
                  <span>{{ detailsInfo.fiscalAppropriation }}元</span>
                </div>
                <div style="display: flex">
                  <span style="width: 80px">教学经费：</span>
                  <span>{{ detailsInfo.educationFunds }}元</span>
                </div>
                <div style="display: flex">
                  <span style="width: 80px">科研经费：</span>
                  <span>{{ detailsInfo.researchFunds }}元</span>
                </div>
                <div style="display: flex">
                  <span style="width: 80px">自筹金额：</span>
                  <span>{{ detailsInfo.selfRaisedFunds }}元</span>
                </div>
                <div style="display: flex">
                  <span style="width: 80px">专项经费：</span>
                  <span>{{ detailsInfo.specialFunds }}元</span>
                </div>
                <div style="display: flex">
                  <span style="width: 80px">其他：</span>
                  <span>{{ detailsInfo.otherFunds }}元</span>
                </div>
              </div>
              <el-button slot="reference" class="view-btn">查看</el-button>
            </el-popover>
          </span>
        </div> -->
      </el-col>
      <el-col :span="8">
        <div class="item">
          <span class="item-title">经费来源</span>
          <span class="item-content">{{ detailsInfo.经费来源 || '--' }}</span>
        </div>
        <div class="item">
          <span class="item-title">科室分摊</span>
          <span class="item-content">{{ detailsInfo.科室分摊 || '--' }}</span>
        </div>
        <!-- <div class="item">
          <span class="item-title">科室分摊</span>
          <span class="item-content">
            <el-popover
              placement="top"
              width="200"
              trigger="click">
              <div>
                <div v-for="(item, index) in departmentShare" :key="index">
                  <span style="width: 80px">{{ item.name }}:</span>
                  <span>{{ item.value || '--' }}%</span>
                </div>
              </div>
              <el-button slot="reference" class="view-btn">查看</el-button>
            </el-popover>
          </span>
        </div> -->
      </el-col>
    </el-row>
  </div>
</template>
<script>
export default {
  name: 'DepreciationInfo',
  props: ['detailsInfo', 'departmentShare'],
  data() {
    return {}
  },
  methods: {},
  mounted() {}
}
</script>

<style scoped lang="scss">
  .item {
    margin-bottom: 12px;
    font-size: 14px;
    display: flex;
    height: 20px;
    line-height: 20px;
    .item-title {
      color: #7EAEF9;
      width: 100px;
    }
    .item-content {
      color: #fff;
      .view-btn {
        font-size: 12px;
        height: 20px !important;
        min-width: 50px !important;
        width: 50px !important;
      }
    }
  }
</style>
