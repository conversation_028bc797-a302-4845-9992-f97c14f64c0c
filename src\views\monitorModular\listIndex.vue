<template>
  <div class="list-index-view">
    <div class="view-header-box">
      <div class="header-left">
        <el-form
          :model="searchForm"
          class="search-form"
          :class="{ collapsed }"
          inline
        >
          <el-form-item>
            <el-input
              v-model="searchForm.input"
              size="small"
              placeholder="资产编码/名称"
            ></el-input>
          </el-form-item>
          <el-form-item>
            <el-select
              size="small"
              v-model="searchForm.brandCode"
              placeholder="品牌"
            >
              <el-option
                v-for="item in brandOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              >
              </el-option>
            </el-select>
          </el-form-item>
        </el-form>
      </div>
      <div class="header-right">
        <el-button class="sino-button-sure" @click="resetForm">重置</el-button>
        <el-button class="sino-button-sure" @click="handleSearchForm"
          >查询</el-button
        >
      </div>
    </div>
    <div class="view-content" style="height: calc(100% - 4rem)">
      <div class="table-view">
        <el-table
          ref="table"
          v-loading="tableLoading"
          :data="tableData"
          :resizable="false"
          height="calc(100% - 50px)"
          :cell-style="$tools.setCell(3)"
          :header-cell-style="$tools.setHeaderCell(3)"
          stripe
        >
          <el-table-column
            prop="assetsName"
            label="资产名称"
            show-overflow-tooltip
          ></el-table-column>
          <el-table-column
            prop="assetsCode"
            label="资产编码"
            show-overflow-tooltip
          ></el-table-column>
          <el-table-column
            prop="belongDeptName"
            label="所属科室"
            show-overflow-tooltip
          ></el-table-column>
          <el-table-column
            prop="alarmStatus"
            label="预警状态"
            show-overflow-tooltip
          ></el-table-column>
          <el-table-column
            prop="workStatus"
            label="运行状态"
            show-overflow-tooltip
          ></el-table-column>
          <el-table-column
            prop="monitorStatus"
            label="监测状态"
            show-overflow-tooltip
          ></el-table-column>
          <el-table-column
            prop="brandName"
            label="品牌"
            show-overflow-tooltip
          ></el-table-column>
          <el-table-column
            prop="modelName"
            label="规格型号"
            show-overflow-tooltip
          ></el-table-column>
          <el-table-column
            prop="installAddress"
            label="当前位置"
            show-overflow-tooltip
          ></el-table-column>
          <el-table-column label="操作" width="100px">
            <template slot-scope="scope">
              <span
                class="operation-span"
                @click="operating('detail', scope.row)"
                >详情</span
              >
            </template>
          </el-table-column>
        </el-table>
        <el-pagination
          :current-page="currentPage"
          :page-sizes="[15, 20, 30, 40]"
          :page-size="pageSize"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
          class="pagination"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        ></el-pagination>
      </div>
    </div>
  </div>
</template>
  <script>
export default {
  components: {},
  data() {
    return {
      tableLoading: false,
      collapsed: false,
      searchForm: {
        input: "",
        brandCode: "",
      },
      navIdx: 0,
      currentPage: 1,
      pageSize: 15,
      total: 3,
      brandOptions: [],
      tableData: [
        {
          assetsName: "飞利浦CT",
          assetsCode: "flp-ct",
          belongDeptName: "临床科11",
          alarmStatus: "",
          workStatus: "",
          monitorStatus: "",
          brandName: "飞利浦",
          modelName: "Achieva 3.0",
          monitorHostAddr: "",
          installAddress: "",
        },
        {
          assetsName: "飞利浦CT",
          assetsCode: "flp-ct",
          belongDeptName: "医技部",
          alarmStatus: "",
          workStatus: "",
          monitorStatus: "",
          brandName: "飞利浦",
          modelName: "Achieva 3.0",
          monitorHostAddr: "",
          installAddress: "",
        },
        {
          assetsName: "飞利浦CT",
          assetsCode: "flp-ct",
          belongDeptName: "门诊部",
          alarmStatus: "",
          workStatus: "",
          monitorStatus: "",
          brandName: "飞利浦",
          modelName: "Achieva 3.0",
          monitorHostAddr: "",
          installAddress: "",
        },
      ],
    };
  },
  methods: {
    handleClose(done) {
      done();
    },
    /** 重置 */
    resetForm() {},
    /** 查询 */
    handleSearchForm() {},
    /** 条数变化事件 */
    handleSizeChange() {},
    /** 页数变化事件 */
    handleCurrentChange() {},
    /** nav 切换事件 */
    handleNavIdx(idx) {
      this.navIdx = idx;
    },
    /** 获取数据 */
    getData() {},
    /** 跳转详情 */
    operating(type, row) {
      switch (type) {
        case "detail":
          this.$emit("openDetailComponent", "monitorDetails");
          break;
        default:
          break;
      }
    },
  },
};
</script>
  <style lang="scss" scoped>
.list-index-view {
  position: relative;
  height: 100%;
  padding: 0px 40px;
}
.view-header-box {
  display: flex;
  justify-content: space-between;
}
.view-content {
  height: calc(100% - 4rem);
  .table-view {
    height: 100%;
  }
}
.operation-span {
  cursor: pointer;
}
::v-deep
  .el-table--striped
  .el-table__body
  tr.el-table__row--striped
  td.el-table__cell {
  background: none;
}
</style>