<template>
  <div class="main">
    <el-dialog v-dialogDrag :modal="false" :visible.sync="show" custom-class="mainDialog main" :close-on-click-modal="false" :before-close="closeDialog" class="searchComponentList">
      <template slot="title">
        <span class="dialog-title" style="text-align: center">图片查看</span>
      </template>
      <div class="dialogs">
        <div style="padding: 15px 20px; width: 49%; background: rgba(53, 98, 219, 0.06)">
          <div class="entrance" style="background: rgba(133, 145, 206, 0.15)">
            <div>
              <p style="font-size: 18px; font-family: PingFang SC-Semibold, PingFang SC">入场 <br /><img src="@/assets/images/parkingLot/entrance.png" alt="" /></p>
            </div>
            <div>
              <div class="entranceTxt">
                <p>入场凭证类型：</p>
                <p>车牌号码</p>
              </div>
              <div class="entranceTxt">
                <p>入场凭证：</p>
                <p>{{ requestParams.proofNum }}</p>
              </div>
              <div class="entranceTxt">
                <p>入场套餐：</p>
                <p>{{ requestParams.setMealType }}</p>
              </div>
              <div class="entranceTxt">
                <p>入场设备：</p>
                <p>{{ requestParams.inParkingEq }}</p>
              </div>
              <div class="entranceTxt">
                <p>入场时间：</p>
                <p>{{ requestParams.inParkingTime }}</p>
              </div>
            </div>
          </div>
          <p style="margin: 15px 0">入场图片</p>
          <div class="admissionPictures">
            <img v-if="requestParams.inParkingPhotos" :src="requestParams.inParkingPhotos[0]" alt="" />
            <img v-else :src="empty" alt="" />
          </div>
          <div class="admissionPictures">
            <img v-if="requestParams.inParkingPhotos" :src="requestParams.inParkingPhotos[1] || empty" alt="" />
            <img v-else :src="empty" alt="" />
          </div>
        </div>
        <div style="padding: 15px 20px; width: 49%; background: rgba(53, 98, 219, 0.06)">
          <div class="entrance" style="background: rgba(133, 145, 206, 0.15)">
            <div>
              <p style="font-size: 18px; font-family: PingFang SC-Semibold, PingFang SC">出场 <br /><img src="@/assets/images/parkingLot/enter.png" alt="" /></p>
            </div>
            <div>
              <div class="entranceTxt">
                <p>出场凭证类型：</p>
                <p>车牌号码</p>
              </div>
              <div class="entranceTxt">
                <p>出场凭证：</p>
                <p>{{ requestParams.objType == 'out' ? requestParams.proofNum : '' }}</p>
              </div>
              <div class="entranceTxt">
                <p>出场套餐：</p>
                <p>{{ requestParams.objType == 'out' ? requestParams.setMealType : '' }}</p>
              </div>
              <div class="entranceTxt">
                <p>出场设备：</p>
                <p>{{ requestParams.objType == 'out' ? requestParams.outParkingEq : '' }}</p>
              </div>
              <div class="entranceTxt">
                <p>出场时间：</p>
                <p>{{ requestParams.objType == 'out' ? requestParams.outParkingTime : '' }}</p>
              </div>
            </div>
          </div>
          <p style="margin: 15px 0">出场图片</p>
          <div class="admissionPictures">
            <img v-if="requestParams.outParkingPhotos && requestParams.objType == 'out'" :src="requestParams.outParkingPhotos[0] || empty" alt="" />
            <img v-else :src="empty" alt="" />
          </div>
          <div class="admissionPictures">
            <img v-if="requestParams.outParkingPhotos && requestParams.objType == 'out'" :src="requestParams.outParkingPhotos[1] || empty" alt="" />
            <img v-else :src="empty" alt="" />
          </div>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import empty from '@/assets/images/parkingLot/empty.png'
export default {
  props: {
    show: {
      type: Boolean,
      default: false
    },
    requestParams: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  data() {
    return {
      empty
    }
  },
  created() {},
  mounted() {
    console.log();
  },
  methods: {
    closeDialog() {
      this.$emit('closeDialog')
    },
    changeState() {
      this.$emit('changeDetailState', this.requestParams)
    }
  }
}
</script>

<style lang="scss" scoped>
// .spaceDetails {
.searchComponentList {
  pointer-events: none;
}

::v-deep .mainDialog {
  width: 60%;
  margin-top: 9vh !important;
  // border: 1px solid #5996f9;
  background-color: transparent !important;
  pointer-events: auto;
  background-image: url('@/assets/images/table-bg.png');
  background-repeat: no-repeat;
  background-size: 100% 100%;
  box-shadow: none;
  border: none !important;
  .dialog-title::before {
    display: none;
  }
  .el-dialog__header {
    background-color: transparent;
    padding-top: 15px;
    height: 50px !important;
    color: #ffffff;
    margin: auto 0;
  }

  .el-dialog__body {
    padding: 0;
    height: calc(100% - 60px);
    max-height: fit-content;
    overflow-y: hidden;
    background-color: transparent;
    padding: 0px 80px;
    .dialog-content {
      width: 100%;
      height: 100%;
      background-color: transparent;

      .el-table {
        background-color: transparent;
        border: 1px solid #202d4c;
        .el-table__header-wrapper {
          background-color: transparent !important;
        }
        .el-table__body-wrapper {
          background-color: transparent !important;
        }
        tr {
          background-color: transparent !important;
        }
        td {
          background-color: transparent !important;
        }
      }
    }
  }
}
.dialogs {
  width: 100%;
  padding: 10px;
  display: flex;
  height: 725px;
  color: #ffffff !important;
  justify-content: space-between;
  > div {
    width: 49%;
  }
}
.entrance {
  display: flex;
  padding: 15px;
  > div:nth-child(1) {
    width: 30%;
    > p {
      width: 40px;
      text-align: center;
      margin: 54px auto;
    }
  }
  > div:nth-child(2) {
    width: 70%;
  }
}
.entranceTxt {
  display: flex;
  height: 35px;
  line-height: 35px;
  > p:nth-child(1) {
    width: 100px;
    font-size: 14px;
  }
  > p:nth-child(2) {
    text-align: left;
  }
}
.admissionPictures {
  width: 100%;
  height: 200px;
  margin-bottom: 15px;
  img {
    width: 100%;
    height: 200px;
  }
}
</style>
