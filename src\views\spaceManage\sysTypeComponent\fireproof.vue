<template>
  <div class="moveComponent">
    <div class="module-container" style="height: calc(25%)">
      <div class="module-header">
        <div class="title-left">
          <!-- <p class="title-left-icon"></p> -->
          <p class="title-left-text">防火分区</p>
        </div>
      </div>
      <div class="module-content" style="height: calc(100% - 48px)">
        <el-table
          v-loading="fireproofLoading"
          class="table-center-transfer"
          :data="fireproofList"
          height="100%"
          :cell-style="{ padding: ' 8px', backgroundColor: 'transparent', border: 'none', padding: '3px' }"
          :header-cell-style="{ background: '#8591CE26!important', color: '#8BDDF5FF', padding: '4px 8px', fontWeight: 'bold' }"
          style="width: 100%"
          element-loading-background="rgba(0, 0, 0, 0.2)"
          @row-click="(row) => tableRowClick(row, 'fireproof')"
        >
          <el-table-column v-for="(column, index) in fireproofColumns" :key="index" :prop="column.prop" :label="column.label" :min-width="column.minWidth" show-overflow-tooltip>
            <template slot-scope="scope">
              <table-render v-if="column.render" :sc="scope" :row="scope.row" :render="column.render"></table-render>
              <div v-else-if="!column.formatter">
                {{ scope.row[column.prop] }}
              </div>
              <div v-else>
                {{ column.formatter(scope.row) }}
              </div>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </div>
    <div class="module-container" style="height: calc(50%)">
      <div class="module-header">
        <div class="title-left">
          <p class="title-left-text">设备台账</p>
        </div>
        <div class="title-right">
          <div class="title-detail" @click="allTableChange()">详情</div>
        </div>
      </div>
      <div class="module-content" style="height: calc(100% - 44px)">
        <div class="statistics_top_new">
          <div v-for="(item, index) in statisticsData" :key="index" class="statistics_top_item">
            <p class="statistics_top_title">{{ item.name }}</p>
            <div class="statistics_top_num" :style="{ color: item.color }">{{ item.value }}</div>
          </div>
        </div>
        <div style="height: calc(100% - 77px)">
          <el-table
            v-el-table-infinite-scroll="tableLoadMore"
            v-loading="deviceTableLoading"
            class="table-center-transfer"
            :data="deviceList"
            height="100%"
            :cell-style="{ padding: ' 8px', backgroundColor: 'transparent', border: 'none', padding: '3px' }"
            :header-cell-style="{ background: '#8591CE26!important', color: '#8BDDF5FF', padding: '4px 8px', fontWeight: 'bold' }"
            style="width: 100%"
            element-loading-background="rgba(0, 0, 0, 0.2)"
          >
            <el-table-column v-for="(column, index) in deviceColumns" :key="index" :prop="column.prop" :label="column.label" :min-width="column.minWidth" show-overflow-tooltip>
              <template slot-scope="scope">
                <table-render v-if="column.render" :sc="scope" :row="scope.row" :render="column.render"></table-render>
                <div v-else-if="!column.formatter">
                  {{ scope.row[column.prop] }}
                </div>
                <div v-else>
                  {{ column.formatter(scope.row) }}
                </div>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>
    </div>

    <div class="module-container" style="height: calc(100% - 75%)">
      <div class="module-header">
        <div class="title-left">
          <p class="title-left-text">设备显示</p>
        </div>
        <div class="title-right">
          <div class="toggle" style="margin-right: 10px">
            <!-- <div class="toggle" style="margin-right: 10px;"> -->
            <div :class="isPipeline ? 'active-type' : ''" @click="changePipelineShow">管线显示</div>
          </div>
        </div>
      </div>
      <div class="module-content" style="height: calc(100% - 48px)">
        <allCheckbox :visible="!isPipeline" :roomData="sendCheckData" />
        <pipelineSelect :visible="isPipeline" :roomData="pipelineData" />
      </div>
    </div>
    <template v-if="allTableComponentListShow">
      <fireproofEqu ref="fireproofEqu" :dialogData="tableCompenentData" :dialogShow="allTableComponentListShow" @configCloseDialog="configCloseTableDialog"></fireproofEqu>
    </template>
  </div>
</template>

<script lang="jsx">
import { refrigeratorData, refrigeratorParams } from '@/assets/common/dict.js'
import icon_5 from '@/assets/images/icon-5.png'
import icon_6 from '@/assets/images/icon-6.png'
import icon_2 from '@/assets/images/icon-2.png'
import abnormal from '@/assets/images//qhdsys/abnormal.png'
import normal from '@/assets/images//qhdsys/normal.png'
import allCheckbox from './components/allCheckbox.vue'
import pipelineSelect from './components/pipelineSelect.vue'
import fireproofEqu from './components/fireproofEqu.vue'
import tableRender from '../components/tableRender.vue'
import { fireproolList, deviceCount, deviceList } from '@/utils/fireproof.js'
export default {
  name: 'moveComponent',
  components: {
    allCheckbox,
    pipelineSelect,
    tableRender,
    fireproofEqu
  },
  props: {
    roomData: {
      type: Object,
      default: () => {}
    },
    assetsList: {
      type: Array,
      default: () => {}
    }
  },
  data() {
    return {
      icon_2,
      icon_5,
      icon_6,
      fireproofColumns: [
        {
          label: '防火分区名称',
          prop: 'gridName'
        },
        {
          label: '所属区域',
          prop: 'gridArea'
        }
      ], // 防火分区columns
      fireproofLoading: false,
      fireproofList: [],
      pipelineData: {}, // 给管线组件传递的数据

      deviceTableLoading: false,
      deviceList: [], // 设备列表

      statisticsData: [
        {
          name: '设备总数',
          key: 'deviceTotal',
          color: '#FFCA64',
          value: 0
        },
        // {
        //   name: '正常设备',
        //   key: 'deviceMonitorTotal',
        //   color: '#FFCA64',
        //   value: 0
        // },
        {
          name: '离线设备',
          key: 'offDeviceTotal',
          color: '#FFCA64',
          value: 0
        },
        {
          name: '故障设备',
          key: 'abnormalDeviceTotal',
          color: '#FF2D55',
          value: 0
        }
      ], // 统计数据
      deviceColumns: [
        {
          prop: 'surveyName',
          label: '设备名称',
          minWidth: 130
        },
        {
          prop: 'menuName',
          label: '归属系统'
        },
        {
          prop: 'status',
          label: '运行状态',
          render: (h, row) => {
            return (
              <div>
                {row.row.status == '0' && (
                  <div class="table-icon">
                    <img src={icon_5} />
                    <span style="color:#61E29D">正常</span>
                  </div>
                )}
                {row.row.status == '6' && (
                  <div class="table-icon">
                    <img src={icon_6} />
                    <span style="color:#86909C">离线</span>
                  </div>
                )}
                {row.row.status == '10' && (
                  <div class="table-icon">
                    <img src={icon_2} />
                    <span style="color:#FF2D55">异常</span>
                  </div>
                )}
              </div>
            )
          }
        }
      ],

      ssmCodes: [],
      checkedValue: '',
      isPipeline: false, // 管线显示
      sendCheckData: {}, // 给checkbox组件传递的数据
      allTableComponentListShow: false,
      deviceParams: {
        fireAreaId: '',
        projectCode: this.roomData.projectCode,
        spaceId: this.roomData.spaceId,
        deviceStatus: '',
        page: 1,
        pageSize: 10
      },
      tableCompenentData: {} // 一站式弹窗数据: {},
    }
  },
  watch: {
    // 监听模型层级数据变化
    roomData: {
      handler(newVal, oldVal) {
        this.ssmCodes = this.roomData.ssmCodes ? this.roomData.ssmCodes.split(',') : []

        this.initData()
      },
      deep: true,
      immediate: true
    }
  },

  methods: {

    initData() {
      this.sendCheckData = {
        ...this.roomData,
        entityTypeId: '',
        spaceId: this.ssmCodes.at(-1)
      }
      this.isPipeline = true
      this.$nextTick(() => {
        this.isPipeline = false
        this.isKeyDevice = false
      })
      this.getFireproofList()
      this.getDeviceCount()
      this.getDeviceList()
    },
    // 获取防火分区列表
    getFireproofList() {
      let params = {
        deviceStatus: '',
        fireAreaId: '',
        page: 0,
        pageSize: 0,
        projectCode: this.roomData.projectCode,
        queryFlag: '',
        spaceId: this.ssmCodes.at(-1)
      }
      fireproolList(params).then(res => {
        if (res.data.code === '200') {
          this.fireproofList = res.data.data
        }
      })
    },
    // 获取设备统计
    getDeviceCount(val) {
      let params = {
        deviceStatus: '',
        fireAreaId: val || '',
        page: 0,
        pageSize: 0,
        projectCode: this.roomData.projectCode,
        queryFlag: '',
        spaceId: this.ssmCodes.at(-1)
      }
      deviceCount(params).then(res => {
        if (res.data.code === '200') {
          for (const key in res.data.data) {
            this.statisticsData.forEach(el => {
              if (key === el.key) {
                el.value = res.data.data[key]
              }
            })
          }
        }
      })
    },
    // 获取设备列表
    getDeviceList(id) {
      this.deviceTableLoading = true
      this.deviceParams.spaceId = this.ssmCodes.at(-1),
      deviceList(this.deviceParams).then((res) => {
        if (res.data.code === '200') {
          if (this.deviceParams.page === 1) {
            this.deviceList = []
          }
          this.deviceList = this.deviceList.concat(res.data.data.list)
          this.deviceParams.total = res.data.data.total
        }
      }).finally(() => {
        this.deviceTableLoading = false
      })
    },
    tableRowClick(row, type) {
      try {
        window.chrome.webview.hostObjects.sync.bridge.FireProtectionZoneFireAreaId(row.id)
      } catch (error) {}
      this.getDeviceCount(row.id)

      this.deviceParams.fireAreaId = row.id
      this.getDeviceList()
    },

    tableLoadMore() {
      if (this.deviceParams.total > this.deviceParams.page * this.deviceParams.pageSize) {
        this.pagination.pageNo++
        this.getDeviceList()
      }
    },
    allTableChange() {
      this.allTableComponentListShow = true
      // 如果无数据则填充初始空间过滤数据，有数据则按照当前数据过滤
      Object.assign(this.tableCompenentData, {
        title: '设备台账列表',
        ...this.deviceParams,
        projectCode: this.roomData.projectCode,
        height: 'calc(100% - 120px)'
      })
      try {
        window.chrome.webview.hostObjects.sync.bridge.ShowDialog(true)
      } catch (error) {}
    },
    // 关闭弹窗
    configCloseTableDialog() {
      this.allTableComponentListShow = false
      try {
        window.chrome.webview.hostObjects.sync.bridge.ShowDialog(false)
      } catch (error) {}
    },

    // 管线显示
    changePipelineShow() {
      this.isPipeline = !this.isPipeline
      if (this.isPipeline) {
        this.pipelineData = {
          ...this.roomData
        }
      } else {
        this.sendCheckData = {
          ...this.roomData,
          entityTypeId: '',
          spaceId: this.ssmCodes.at(-1)
        }
      }
    }
  }
}
</script>

<style lang="scss" scoped>
@import '../style/module.scss';
.statistics_top_new {
  width: calc(100% - 34px);
  height: 77px;
  display: flex;
  flex: 1;
  align-items: center;
  justify-content: space-between;
  .statistics_top_item {
    height: 100%;
    // padding: 16px 0;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    .statistics_top_title {
      padding-left: 20px;
      font-size: 14px;
      font-family: HarmonyOS Sans SC-Regular, HarmonyOS Sans SC;
      font-weight: 400;
      color: #b0e3fa;
    }
    .statistics_top_num {
      font-weight: bold;
      font-size: 20px;
      margin-top: 10px;
    }
  }
}
.moveComponent {
  width: 100%;
  height: 100%;
  .device-list {
    overflow: auto;
    .device-list-item {
      padding-top: 16px;
      .item-title {
        background: rgba(133, 145, 206, 0.15);
        font-size: 15px;
        font-weight: 500;
        color: #ffffff;
        line-height: 18px;
        padding: 7px 8px;
        display: flex;
        align-items: center;
      }
      .item-status {
        display: flex;
        .status-item {
          width: calc(100% / 3);
          text-align: center;
          padding: 15px;
        }
        .status-item-name {
          font-size: 14px;
          font-weight: 400;
          color: #ffffff;
          line-height: 16px;
        }
        .status-item-value {
          font-size: 16px;
          font-weight: bold;
          color: #ffca64;
          line-height: 19px;
          margin-top: 6px;
        }
        .status-item:last-child {
          .status-item-value {
            color: #ff2d55;
          }
        }
      }
    }
  }
  .table-icon {
    display: flex;
    align-items: center;
  }
  .table-icon img {
    width: 16px;
    margin-right: 3px;
  }
  .title-left {
    padding-left: 20px;
  }
  .title-detail {
    cursor: pointer;
    font-size: 14px;
    font-family: HarmonyOS Sans SC-Regular, HarmonyOS Sans SC;
    font-weight: 400;
    color: #8bddf5 !important;
    margin-right: 13px;
  }
  .toggle {
    display: flex;
    cursor: pointer;
    font-size: 14px;
  }
  .toggle > div {
    padding: 4px 10px;
    color: #8bddf5 !important;
    text-align: center;
    background-color: #213251;
    box-sizing: border-box;
    border: 1px solid #213251;
    opacity: 1;
  }
  .active-type {
    background: rgba(255, 255, 255, 0.1) linear-gradient(90deg, rgba(10, 132, 255, 0.4) 0%, rgba(10, 132, 255, 0) 100%);
    border: 1px solid #abf0ff !important;
  }
  .icon-box {
    margin-right: 0;
    display: flex;
    align-items: center;
    position: relative;
  }
  ::v-deep .el-table {
    .el-table__body tr {
      cursor: pointer;
    }
  }
}
// 滚动条的宽度
::v-deep .el-table__body-wrapper::-webkit-scrollbar {
  width: 6px; // 横向滚动条
  height: 6px; // 纵向滚动条 必写
}

// 注意:hover
::v-deep .el-table__body-wrapper:hover::-webkit-scrollbar {
  width: 6px; // 横向滚动条
  height: 6px; // 纵向滚动条 必写
}
::v-deep .el-table__body-wrapper::-webkit-scrollbar-corner,
::v-deep .el-table__body-wrapper::-webkit-scrollbar-track {
  background-color: transparent;
}
.module-header {
  width: 100%;
  // padding-left: 50px;
  background: url('~@/assets/images/qhdsys/bg-bt.png') no-repeat;
}

::v-deep .el-checkbox__input.is-checked span.el-checkbox__inner {
  background: url('@/assets/images/<EMAIL>') !important;
  background-size: 100% 100% !important;
}
::v-deep span.el-checkbox__inner {
  border-color: #c5fff9 !important;
}
::v-deep .el-checkbox__input.is-checked + .el-checkbox__label {
  color: #8bddf5;
}
.order-more {
  width: 24px;
  height: 24px;
  margin-right: 0 !important;
  margin-left: 8px;
}
.filter {
  width: 24px;
  height: 24px;
}
::v-deep .panel-s {
  position: absolute;
  right: 0;
  top: 28px;
  background-color: #374b79;
  padding: 8px;
  z-index: 9;
  height: 120px;
  overflow: auto;
  .el-checkbox-group {
    display: flex;
    flex-direction: column;
    align-items: flex-end;
    margin-right: 0 !important;
    .el-checkbox {
      width: 100%;
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-right: 0;
      margin-bottom: 5px;
      .el-checkbox__label {
        color: #a3a9c0 !important;
      }
      .el-checkbox__input {
        // display: none;
      }
    }
  }
}
</style>
