<template>
  <div class="main">
    <el-dialog :modal="false" width="18%" :visible.sync="show" custom-class="bg-dialog main" :close-on-click-modal="false" :before-close="closeDialog">
      <template slot="title">
        <span class="bg-dialog-title">回路详情</span>
      </template>
      <div class="spaceDetails-content">
        <div v-for="(item, index) in loopStatisticsData" :key="index" class="info-item">
          <p class="item-label">{{ item.name }}</p>
          <p class="item-value">{{ requestParams[item.key] || '' }}</p>
        </div>
        <div class="info-item">
          <p class="item-label">回路操作：</p>
          <p class="item-state" @click="changeState">{{ requestParams.outputStatus == '1' ? '关闭' : '开启' }}</p>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script>
export default {
  props: {
    show: {
      type: Boolean,
      default: false
    },
    requestParams: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  data() {
    return {
      loopStatisticsData: [
        {
          name: '回路名称：',
          key: 'loopsName'
        },
        {
          name: '回路类型：',
          key: 'entityTypeName'
        },
        {
          name: '回路状态：',
          key: 'state'
        }
      ], // 统计数据
      loopInfo: {}
    }
  },
  created() {},
  methods: {
    closeDialog() {
      this.$emit('closeDialog')
    },
    changeState() {
      this.$emit('changeDetailState', this.requestParams)
    }
  }
}
</script>

<style lang="scss" scoped>
// .spaceDetails {
  ::v-deep .bg-dialog {
    height: 250px;
    margin: calc(50vh - 125px) auto 0!important;
    margin-left: calc(38vw - 9%)!important;
    background: url('@/assets/images/qhdsys/minDialogBgd.png') no-repeat;
    background-size: 100% 100%;
    border: none;
    pointer-events: auto;
    box-shadow: none;
    .bg-dialog-title {
      font-size: 16px;
    }
    .el-dialog__header {
      height: 50px !important;
    }
    .el-dialog__body {
      height: calc(100% - 50px);
      padding: 10px 20px;
      overflow: auto;
    }
    .el-dialog__headerbtn {
      width: 16px;
      height: 16px;
      right: -18px;
      top: 13px;
      background-image: url('@/assets/images/qhdsys/detailDialogClose.png') !important;
    }
    .spaceDetails-content {
      width: 100%;
      ::v-deep .el-table {
        .el-table__header thead tr {
          background: rgba(15, 34, 103, 0.5);
        }
        .el-table__row {
          background: rgba(4, 16, 54, 0.5);
          color: #fff;
        }
      }
      .light-opeartion {
        height: auto;
        width: auto;
        color: #ffca64 !important;
        div {
          padding: 8px 15px;
          font-size: 14px;
          cursor: pointer;
          &:hover {
            color: #ffe3a6;
          }
        }
      }
      .info-item {
        font-size: 14px;
        display: flex;
        // height: 40px;
        line-height: 16px;
        padding: 10px 0px;
        .item-label {
          color: #7eaef9;
        }
        .item-value {
          flex: 1;
          color: #ffffff;
        }
        .item-state {
          cursor: pointer;
          height: 25px;
          line-height: 25px;
          width: 70px;
          background-image: url('@/assets/images/btn.png') !important;
          background-repeat: no-repeat;
          background-size: 100% 100%;
          color: #ffffff;
          margin: auto 0;
          border-radius: 4px;
          text-align: center;
        }
      }
    }
  }
// }
</style>
