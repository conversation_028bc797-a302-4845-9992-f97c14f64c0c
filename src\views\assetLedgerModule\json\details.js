export const basiceInfo = [
	{
		label: '资产名称',
		prop: 'assetsName',
		lgWidth: 8,
	},
	{
		label: '资产编码',
		prop: 'assetsCode',
		lgWidth: 8,
	},
	{
		label: '通用名',
		prop: 'commonName',
		lgWidth: 8,
	},
	{
		label: '68编码',
		prop: 'sixEightCode',
		lgWidth: 8,
	},
	{
		label: '甲乙类',
		prop: 'classAb',
		lgWidth: 8,
	},
	{
		label: '品牌',
		prop: 'manufacturerBrand',
		lgWidth: 8,
	},
	{
		label: '规格型号',
		prop: 'deviceModel',
		lgWidth: 8,
	},
	{
		label: '计量单位',
		prop: 'deviceUnitOfMeasurement',
		lgWidth: 8,
	},
	{
		label: '资产金额',
		prop: 'inputAmount',
		lgWidth: 8,
	},
	{
		label: '出厂日期',
		prop: 'manuDate',
		lgWidth: 8,
	},
	{
		label: 'SN码',
		prop: 'snCode',
		lgWidth: 8,
	},
	{
		label: '寿命年限',
		prop: 'usePeriod',
		lgWidth: 8,
	},
	{
		label: '设备情况',
		prop: 'deviceState',
		lgWidth: 8,
	},
	{
		label: '健康分析设备',
		prop: 'healthFlag',
		lgWidth: 8,
	},
	{
		label: '产地',
		prop: 'deviceOrigin',
		lgWidth: 8,
	},
	{
		label: '供应商名称',
		prop: 'supplierName',
		lgWidth: 8,
	},
	{
		label: '生产厂商名称',
		prop: 'manufacturerName',
		lgWidth: 8,
	},
	{
		label: '备注',
		prop: 'remake',
		lgWidth: 24,
	},

]

export const useInfo = [
	{
		label: '预算编码',
		prop: 'budgetCode',
		lgWidth: 8,
	},
	{
		label: '采购单编码',
		prop: 'purchaseCode',
		lgWidth: 8,
	},
	{
		label: '采购合同编码',
		prop: 'purcCntrCode',
		lgWidth: 8,
	},
	{
		label: '维保合同编码',
		prop: 'mateCntrCode',
		lgWidth: 8,
	},
	{
		label: '财务编码',
		prop: 'finCode',
		lgWidth: 8,
	},
	{
		label: '管理科室',
		prop: 'mgtDeptId',
		lgWidth: 8,
	},
	{
		label: '所属科室',
		prop: 'belongDeptId',
		lgWidth: 8,
	},
	{
		label: '资产情况',
		prop: 'asetSitn',
		lgWidth: 8,
	},
	{
		label: '使用科室',
		prop: 'useDepartmentId',
		lgWidth: 8,
	},
	{
		label: '存放仓库',
		prop: 'ownedWarehouseId',
		lgWidth: 8,
	},
	{
		label: '负责人',
		prop: 'resperNo',
		lgWidth: 8,
	},
	{
		label: '负责人电话',
		prop: 'resperPhone',
		lgWidth: 8,
	},
	{
		label: '启用日期',
		prop: 'startDate',
		lgWidth: 8,
	},
	{
		label: '维保日期',
		prop: 'maintenanceDate',
		lgWidth: 8,
	},
	{
		label: '资产来源',
		prop: 'asetSouc',
		lgWidth: 8,
	},
	{
		label: '资产用途',
		prop: 'asetUsed',
		lgWidth: 8,
	},
	{
		label: '资产状态',
		prop: 'assetsStatus',
		lgWidth: 8,
	},
	{
		label: '所属区域',
		prop: 'regionId',
		lgWidth: 8,
	},
	{
		label: '存放位置',
		prop: 'instLoc',
		lgWidth: 8,
	},
	{
		label: '主副分类',
		prop: 'deviceClass',
		lgWidth: 8,
	},
]

// 类别信息
export const categoryInformationFormItemList = [
	{
		label: '医疗器械分类',
		prop: 'medicalInstrumentsCategory',
		lgWidth: 8,
	},
	{
		label: '使用分类',
		prop: 'useCategory',
		lgWidth: 8,
	},
	{
		label: '68分类',
		prop: 'sixEightCategory',
		lgWidth: 8,
	},
	{
		label: '国标分类',
		prop: 'gbCategory',
		lgWidth: 8,
	},
	{
		label: '资产分类标准',
		prop: 'assetsClassificationStandard',
		lgWidth: 8,
	},
	{
		label: '紧急生命支持设备',
		prop: 'emergencyLifeSupport',
		lgWidth: 8,
	},
	{
		label: '特种设备',
		prop: 'specialDeviceCategory',
		lgWidth: 8,
	},
	{
		label: '检验类设备',
		prop: 'inspectionClass',
		lgWidth: 8,
	},
	{
		label: '辐射设备',
		prop: 'radiationCategory',
		lgWidth: 8,
	},
	{
		label: '中医诊疗类设备',
		prop: 'diagnosisAndTreatmentCategory',
		lgWidth: 8,
	},
	{
		label: '灭菌类设备',
		prop: 'sterilizationCategory',
		lgWidth: 8,
	},
	{
		label: '评估模型',
		prop: 'assessModelId',
		lgWidth: 8,
	},
	{
		label: '风险等级',
		prop: 'riskLevel',
		lgWidth: 8,
	},
	{
		label: '分类属性',
		prop: 'categoryAttributes',
		lgWidth: 8,
	},
	{
		label: '是否计量',
		prop: 'isMetering',
		lgWidth: 8,
	},
	{
		type: 'radio',
		label: '资产类型',
		prop: 'assetBillType',
		lgWidth: 8,
	},
	{
		type: 'radio',
		label: '门/急诊类型',
		prop: 'diacrisisServiceType',
		lgWidth: 8,
	},
	{
		type: 'inputNumber',
		label: '日额定工作时长',
		prop: 'dayRatedWorkTime',
		lgWidth: 8,
	},
]

// 折旧信息
export const depreciationInformationFormItemList = [
	{
		type: 'slot',
		label: '折旧方式',
		prop: 'depreciationMethod',
		slotName: 'depreciationMethod',
		lgWidth: 8,
	},
	{
		type: 'inputNumber',
		precision: 1,
		label: '月残值率',
		prop: 'monthlyResidualValueRate',
		lgWidth: 8,
	},
	{
		type: 'inputNumber',
		label: '折旧年限',
		prop: 'deprePeriod',
		append: '月',
		lgWidth: 8,
	},
]