<template>
  <div class="statistics_item first" style="margin-bottom: 8px">
    <div class="statistics_current">
      <div class="item_title">{{ dateTypeName }}报警数</div>
      <div  class="currentAlarmPie" @click="viewAlarm">
        <div class="left">
          <div style="width: 100%; height: 100%; padding: 45px 0;">
            <span>{{alarmTotalStatistics.alarmCount || 0}}</span>
            <span>件</span>
          </div>
        </div>
        <div class="right">
          <div>同比</div>
          <div>
            <img :src="imgShowType(alarmTotalStatistics.yoy)" alt="">
            <span>{{alarmTotalStatistics.yoy || 0 }}%</span>
          </div>
          <div>环比</div>
          <div>
            <img :src="imgShowType(alarmTotalStatistics.yoy)" alt="">
            <span>{{alarmTotalStatistics.qoq || 0}}%</span>
          </div>
        </div>
      </div>
    </div>
    <div class="statistics_current">
      <div class="item_title">
        报警类型占比
      </div>
      <div id="alarmTypeProportion" class="item_content"></div>
    </div>
  </div>
</template>
<script>
import * as echarts from 'echarts'
import { alarmStatistics, alarmTypeStatistics } from '@/utils/comprehensiveStatistics'
import shang from '@/assets/images/shang.png'
import xia from '@/assets/images/xia.png'
import equal from '@/assets/images/flat.png'
export default {

  props: {
    dateType: {
      type: String,
      default: ''
    },
    date: {
      type: [String, Array],
      default: () => []
    }
  },
  data () {
    return {
      shang,
      xia,
      equal,
      highlightTimer: null,
      currentIndex: -1,
      alarmTotalStatistics: {},
      myChart: null
    }
  },
  computed: {
    dateTypeName () {
      let str = ''
      switch (this.dateType) {
        case 'day':
          str = '今日'
          break
        case 'week':
          str = '本周'
          break
        case 'month':
          str = '本月'
          break
        case 'year':
          str = '本年'
          break
        case 'custom':
          str =''
          break
      }
      return str
    }
  },
  watch: {
    dateType: {
      handler (val) {
        if   (val !== 'custom') {
          this.getAlarmStatistics()
          this.getRightCenter()
        }
      },
      immediate: true
    },
    date: {
      handler (val) {
        if (val.length) {
          this.getAlarmStatistics()
          this.getRightCenter()
        }
      },
      deep: true

    }
  },
  mounted () {
    this.getAlarmStatistics()
    this.getRightCenter()
  },
  methods: {
    imgShowType (val) {
      let img = ''
      if (val > 0) {
        img = this.shang
      } else if (val < 0) {
        img = this.xia
      } else {
        img = this.equal
      }
      return img
    },
    viewAlarm () {
      this.$emit('itemClick')
    },
    // 获取报警数同比环比
    getAlarmStatistics () {
      let params = {
        dateType: this.dateType,
        startTime: this.date[0],
        endTime: this.date[1]
      }
      alarmStatistics(params).then(res => {
        if (res.data.code == 200) {
          this.alarmTotalStatistics = res.data.data
        }
      })
    },
    // 获取报警类型占比
    getRightCenter() {
      const params = {
        dateType: this.dateType,
        startTime: this.date[0],
        endTime: this.date[1],
        searchType: 'all'
      }
      alarmTypeStatistics(params).then((res) => {
        const data = res.data
        if (data.code === '200' && data.data.policeList) {
          this.initPieChart(data.data.policeList)
        }
      })
    },
    initPieChart (valueList) {
      if (this.myChart) {
        this.myChart.dispose()
      }
      let dom = document.getElementById('alarmTypeProportion')
      this.myChart = echarts.init(dom)
      this.myChart.resize()
      let option = {}
      if (valueList && valueList.length !== 0) {
        var scaleData = valueList
        var xdata = valueList.map((item) => {
          return {
            // 这里主要是因为显示legend的时候，会出现name相同的情况，所以name和id互换代替，同样的下面data的name也需要和id互换
            name: item.alarmTypeId || '',
            id: item.alarmTypeName || ''
          }
        })
        var data = []
        var colorList = ['#E88D6B', '#61E29D', '#5E89EE', '#0A84FF', '#F4D982']
        for (var i = 0; i < scaleData.length; i++) {
          data.push({
            value: scaleData[i].count,
            id: scaleData[i].alarmTypeName,
            name: scaleData[i].alarmTypeId,
            policeRate: scaleData[i].policeRate,
            itemStyle: {
              borderWidth: 2,
              shadowBlur: 200,
              borderColor: '#0A1732',
              color: colorList[i],
              label: {
                show: true
              }
            }
          })
        }
        var seriesObj = [
          {
            name: '',
            type: 'pie',
            clockwise: false,
            center: ['20%', '50%'],
            radius: ['45%', '60%'], // 调整饼图的大小
            label: {
              show: false,
              position: 'center',
              formatter: function (params) {
                console.log(params)
                return `{c_style|${params.data.value}}\n{b_style|${params.data.id}}`
              },
              rich: {
                b_style: {
                  fontSize: 12,
                  fontWeight: 400,
                  color: '#fff'
                },
                c_style: {
                  padding: [0, 0, 10, 0],
                  fontSize: 16,
                  fontWeight: 'bold',
                  color: '#fff'
                }
              }
            },
            emphasis: {
              scale: true,
              label: {
                show: true,
                fontSize: '14',
                fontWeight: 'normal'
              }
            },
            labelLine: {
              show: false
            },
            data: data
          },
          {
            type: 'pie',
            radius: ['56%', '60%'],
            center: ['20%', '50%'],
            animation: false,
            emphasis: {
              scale: false
            },
            data: [
              {
                value: 100
              }
            ],
            label: {
              show: false
            },
            itemStyle: {
              color: 'rgba(133,145,206,0.15)'
            }
          },
          // 外边框
          {
            name: '外边框',
            type: 'pie',
            clockwise: false, // 顺时加载
            emphasis: {
              scale: false
            },
            center: ['20%', '50%'],
            radius: ['55%', '70%'],
            label: {
              show: false
            },
            data: [
              {
                value: 0,
                name: '',
                itemStyle: {
                  borderWidth: 1,
                  color: 'rgba(133,145,206,0.15)',
                  borderColor: 'rgba(133,145,206,0.15)'
                }
              }
            ]
          },
          {
            name: '',
            type: 'pie',
            startAngle: 90,
            radius: '50%',
            animation: false,
            emphasis: {
              scale: false
            },
            center: ['20%', '50%'],
            itemStyle: {
              labelLine: {
                show: false
              },
              color: new echarts.graphic.RadialGradient(0.5, 0.5, 1, [
                {
                  offset: 1,
                  color: 'rgba(133,145,206,0.15)'
                },
                {
                  offset: 0,
                  color: 'rgba(133,145,206,0.15)'
                }
              ]),
              shadowBlur: 60
            },
            data: [
              {
                value: 100
              }
            ]
          }
        ]
        option = {
          backgroundColor: 'rgba(128, 128, 128, 0)',
          legend: {
            selectedMode: false,
            orient: 'vertical',
            type: 'scroll',
            x: 'left',
            top: '6%',
            left: '45%',
            bottom: '0%',
            data: xdata,
            itemWidth: 8,
            itemHeight: 8,
            itemGap: 13,
            pageTextStyle: {
              color: '#fff'
            },
            pageIconColor: '#fff', // 设置翻页按钮的激活颜色
            pageIconInactiveColor: '#606266', // 设置翻页按钮的非激活颜色
            textStyle: {
              fontSize: 12, // 字体大小
              color: '#B3C2DD' //  字体颜色
            },
            formatter: function (name) {
              var oa = option.series[0].data
              var num = oa.reduce((sum, e) => sum + e.value, 0)
              for (var i = 0; i < option.series[0].data.length; i++) {
                if (name === oa[i].name) {
                  return ' ' + oa[i].id + ' ' + oa[i].policeRate
                }
              }
            },
            tooltip: {
              show: false
            }
          },

          series: seriesObj
        }
      } else {
        option = {
          title: {
            text: '暂无数据',
            x: 'center',
            y: 'center',
            textStyle: {
              fontSize: 14,
              fontWeight: 'normal',
              color: '#999'
            }
          }
        }
      }
      this.myChart.clear()
      this.myChart.setOption(option)
      // 随着屏幕大小调节图表
      window.addEventListener('resize', () => {
        this.myChart.resize()
      })
      this.highlightTimer = null
      this.currentIndex = -1
      this.highlightTimer = setInterval(() => {
        this.startTooltipLoop(this.myChart, valueList.length)
      }, 1000)
      // 鼠标移入暂停
      this.myChart.on('mouseover', (params) => {
        this.myChart.dispatchAction({
          type: 'downplay',
          seriesIndex: 0,
          dataIndex: this.currentIndex
        })
        clearInterval(this.highlightTimer)
        this.highlightTimer = null
      })
      // 鼠标移出继续
      this.myChart.on('mouseout', (params) => {
        clearInterval(this.highlightTimer)
        this.highlightTimer = setInterval(() => {
          this.startTooltipLoop(this.myChart, valueList.length)
        }, 1000)
      })
      // 先移除点击事件 解决点击事件重复绑定
      this.myChart.off('click')
      // 图例点击事件
      this.myChart.on('click', (params) => {
        this.myChart.setOption({
          legend: { selected: { [params.name]: true } } // 取消点击图例置灰
        })
        let obj = {
          projectCode: 'DTXT',
          id: valueList[params.dataIndex].alarmTypeId
        }
        this.$emit('itemClick', obj)
      })
    },
    startTooltipLoop(getchart, length) {
      // 取消之前高亮的图形
      getchart.dispatchAction({
        type: 'downplay',
        seriesIndex: 0,
        dataIndex: this.currentIndex
      })
      this.currentIndex = (this.currentIndex + 1) % length
      // 高亮当前图形
      getchart.dispatchAction({
        type: 'highlight',
        seriesIndex: 0,
        dataIndex: this.currentIndex
      })
      // 显示tooltip
      // getchart.dispatchAction({
      //   type: 'showTip',
      //   seriesIndex: 0,
      //   dataIndex: this.currentIndex
      // })
    },
    typeChange (val) {
      this.filterTyp = val
    }

  }
}
</script>
<style lang="scss" scoped>
.first {
  display: flex;
  .statistics_current {
    width: calc(50% - 4px);
    height: 100%;
    margin-right: 8px;
    &:last-child {
      margin-right: 0;
    }

    .item_content {
      width: 100%;
      height: calc(100% - 26px);
    }
    .currentAlarmPie{
      width: 100%;
      height: calc(100% - 26px);
      display: flex;
      .left{
        width: 144px;
        height:100%;
        background: url('~@/assets/images/statistics_icon.png') no-repeat;
        background-size: 100% 100%;
        text-align: center;
        margin-right: 16px;
        span{
          color: #fff;
          font-size: 14px;
          margin-top: 45px;
          &:first-child{
            font-size: 36px;
            color: #FFCA64;
            margin-right: 5px;
          }
        }
      }
      .right{
        color: #fff;
        width: calc(100% - 144px - 16px);
        height: 100%;
        padding: 16px 8px;
        &>div{
          margin-top: 8px;
          &:nth-child(odd) {
            margin-top: 16px;
          }
        }
      }
    }
  }
}
.item_title {
  font-weight: 500;
  font-size: 16px;
  color: #b0e3fa;
  margin-bottom: 10px;
}
</style>
