<template>
  <div class="main">
    <el-dialog v-dialogDrag :visible.sync="addDialogShow" :close-on-click-modal="false" :before-close="closeDialog" class="personDialog">
      <template slot="title">
        <span class="dialog-title">{{ dialogTitle }}</span>
      </template>
      <div class="dialog-content" style="width: 100%">
        <el-form ref="formInline" :model="formInline" :rules="rules" :inline="true" label-width="auto" size="medium">
          <div class="formRow" style="display: flex">
            <el-form-item label="角色名称：" prop="roleName">
              <el-input v-if="type !== 'detail'" v-model.trim="formInline.roleName" placeholder="请输入角色名称"></el-input>
              <span class="form-detail-span" v-else>{{ formInline.roleName }}</span>
            </el-form-item>
          </div>
          <div class="formRow" style="display: flex">
            <el-form-item label="角色编码：" prop="roleCode">
              <el-input v-if="type !== 'detail'" v-model.trim="formInline.roleCode" placeholder="请输入角色编码"></el-input>
              <span class="form-detail-span" v-else>{{ formInline.roleCode }}</span>
            </el-form-item>
          </div>
          <div class="formRow">
            <el-form-item label="状态：" prop="roleState">
              <el-radio v-if="type !== 'detail'" :disabled="type == 'detail'" v-model="formInline.roleState" label="0">启用</el-radio>
              <el-radio v-if="type !== 'detail'" :disabled="type == 'detail'" v-model="formInline.roleState" label="1">禁用</el-radio>
              <span class="form-detail-span" v-else>{{ formInline.roleState === '0' ? '启用' : formInline.roleState === '1' ? '禁用' : '' }}</span>
            </el-form-item>
          </div>
          <div class="formRow">
            <el-form-item label="权限配置：" prop="menuIds">
              <el-tree :class="{'tree-color':type!=='detail','menu-tree':true}" :data="treeData" show-checkbox default-expand-all node-key="id" ref="tree" highlight-current :props="defaultProps"> </el-tree>
            </el-form-item>
          </div>
        </el-form>
      </div>
      <span slot="footer" class="dialog-footer" v-if="type !== 'detail'">
        <el-button class="sino-button-sure" @click="closeDialog">取 消</el-button>
        <el-button class="sino-button-sure" type="primary" @click="userSaveFn">保 存</el-button>
      </span>
    </el-dialog>
  </div>
</template>
<script>
import { submitRole, getMenuList } from '@/utils/api'
export default {
  name: 'addRole',
  props: {
    addDialogShow: {
      type: Boolean,
      default: false
    },
    dialogTitle: {
      type: String,
      default: ''
    },
    rowData: {
      type: Object,
      default: function () {
        return {}
      }
    },
    type: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      formInline: {
        roleName: '',
        roleCode: '',
        roleState: '0',
        menuIds: ''
      },
      treeData: [],
      defaultProps: {
        children: 'children',
        label: 'menuName'
      },
      rules: {
        roleName: [
          { required: true, message: '请输入角色名称', trigger: 'blur' },
          { min: 2, max: 50, message: '长度在 2 到 50 个字符', trigger: 'blur' }
        ],
        roleCode: [
          { required: true, message: '请输入角色编码', trigger: 'blur' },
          { min: 2, max: 50, message: '长度在 2 到 50 个字符', trigger: 'blur' }
        ],
        roleState: [{ required: true, message: '请选择角色状态', trigger: 'blur' }],
        menuIds: [{ required: true, message: '请选择角色权限配置', trigger: 'blur' }]
      }
    }
  },
  watch: {},
  mounted() {
    Object.assign(this.formInline, this.rowData)
    this.getMenuList()
  },
  methods: {
    getMenuList() {
      getMenuList().then((res) => {
        const data = res.data
        if (data.code === '200' && data.data.length) {
          if (this.type === 'detail') {
            data.data.map((e) => {
              e.disabled = true
            })
          }
          this.treeData = this.$tools.listToTree(data.data, 'id', 'menuFid')
          if (this.formInline.menuIds) {
            this.$refs.tree.setCheckedKeys(this.formInline.menuIds.split(','))
          }
          // console.log(this.treeData)
        } else {
          this.$message({
            message: data.msg,
            type: 'warning'
          })
        }
      })
    },
    // 取消按钮
    closeDialog() {
      this.$emit('closeDialog')
    },
    // 确认按钮
    userSaveFn() {
      let formData = ''
      if (this.$refs.tree.getCheckedKeys().length) {
        this.formInline.menuIds = this.$refs.tree.getCheckedKeys().toString()
        formData = JSON.parse(JSON.stringify(this.formInline))
      } else {
        this.formInline.menuIds = ''
      }
      this.$refs.formInline.validate((valid) => {
        if (valid) {
          submitRole(formData).then((res) => {
            const data = res.data
            if (data.code === '200') {
              this.$message({
                message: this.dialogTitle + '角色成功！',
                type: 'success'
              })
              this.$emit('sure', this.type)
            } else {
              this.$message({
                message: data.msg,
                type: 'warning'
              })
            }
          })
        } else {
          console.log('error submit!!')
          return false
        }
      })
    }
  }
}
</script>

<style lang="scss" type="text/css" scoped>
// .main {
//   width: 50%;
//   height: 40px;
//   display: inline-block;
//   line-height: 40px;
// }
.formRow {
  width: 100%;
  display: flex;
  ::v-deep .el-form-item {
    flex: 1;
    display: flex;
  }
  ::v-deep .el-form-item__content {
    flex: 1;
  }
  .form-detail-span {
    font-size: 15px;
    padding-left: 20px;
  }
}
.tree-color {
  color: #fff;
}
.menu-tree {
  max-height: 33vh;
  overflow: scroll;
  background: center;
  // color: #fff;
  ::v-deep .is-current {
    color: #ffe3a6;
  }
  ::v-deep .el-tree-node__content:hover {
    background: center;
    color: #ffe3a6;
  }
}
</style>
<style lang="scss">
.menu-tree {
  .el-checkbox__input.is-disabled .el-checkbox__inner {
    background: center;
    border: 1px solid #2e529e;
  }
}
</style>
