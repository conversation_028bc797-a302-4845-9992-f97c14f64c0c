/*
 * @Author: error: error: git config user.name & please set dead value or install git && error: git config user.email & please set dead value or install git & please set dead value or install git
 * @Date: 2023-02-24 13:52:58
 * @LastEditors: Please set LastEditors
 * @LastEditTime: 2023-08-10 10:26:33
 * @FilePath: \ihcrs_pc\vitePlugins\html.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import { ViteEjsPlugin } from 'vite-plugin-ejs'

export default function createHtml(env, isBuild) {
  return ViteEjsPlugin((viteConfig) => {
    return {
      root: viteConfig.root,
      ...env
    }
  })
}
// vite-plugin-html导致public重定向 暂弃用
// import { createHtmlPlugin } from 'vite-plugin-html'
// export default function createHtml(env, isBuild) {
//   return createHtmlPlugin({
//     template: './index.html',
//     entry: '/src/main.js', // 这个会帮我们注入入口 js 文件
//     inject: {
//       data: {
//         ...env
//       }
//     },
//     minify: isBuild
//   })
// }
