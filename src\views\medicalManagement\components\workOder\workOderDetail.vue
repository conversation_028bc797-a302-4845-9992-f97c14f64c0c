<template>
  <div class="list-index-view">
    <div class="view-header-box">
      <div class="header-left-box">
        <div :class="[`state${currentStatus}`, 'cfx']">
          <div class="text">
            <em class="icon iconfont"></em>
            <!-- 配送单 block样式 -->
            <span>
              未完成
            </span>
          </div>
          <div class="triangle"></div>
        </div>
        <slot name="left"></slot>
      </div>
      <div class="flowBox">
        <div v-for="(item, index) in flowData" :key="index" class="flowItemWrap">
          <span v-if="index != 0" class="line">-----------></span>
          <div class="flowItem">
            <i style="font-size: 30px;" :class="item.icon"></i>
            <span style="margin: 10px 0;">{{ item.name }}</span>
            <span>{{ item.date }}</span>
          </div>
        </div>
      </div>
      <div style="padding-right: 20px;">
        <el-link type="primary" :underline="false">
          操作详情
          <i class="el-icon-arrow-down"></i>
        </el-link>
      </div>
    </div>
    <div class="view-content">
      <el-form ref="form" :model="detailInfo" label-width="120px">
        <div class="baseInfo">
          <div class="toptip">
            <span class="green_line"></span>
            报修工单
          </div>
          <el-row :gutter="24">
            <el-col v-for="(item, index) in assetsInfo" :key="index" :span="item.lgWidth">
              <el-form-item :label="`${item.label}：`">
                <span>{{ basicInfoForm.assetsName | basicFilters }}</span>
              </el-form-item>
            </el-col>
          </el-row>
        </div>
        <div class="assetsInfo">
          <div class="toptip">
            <span class="green_line"></span>
            维修情况
          </div>
          <el-row :gutter="24">
            <el-col v-for="(item, index) in completeInfo" :key="index" :span="item.lgWidth">
              <el-form-item :label="`${item.label}：`">
                <span>{{ item.assetsName | basicFilters }}</span>
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="现场照片">
                <img src="@/assets/images/war/no-people.png" alt="" class="image" />
              </el-form-item>
            </el-col>
          </el-row>
        </div>
      </el-form>
    </div>
  </div>
</template>
<script>
import icon_5 from '@/assets/images/icon-5.png'
export default {
  name: 'leaseDetail',
  components: {
  },
  filters: {
    basicFilters(val) {
      return val || '---'
    }
  },
  data() {
    return {
      icon_5,
      tableLoading: false,
      searchForm: {
        input: '',
        dept: ''
      },
      detailInfo: {},
      deptOptions: [],
      stateOptions: [],
      currentPage: 1,
      pageSize: 15,
      total: 5,
      tableData: [],
      assetsInfo: [
        {
          label: '联系人',
          prop: 'code',
          lgWidth: 8
        },
        {
          label: '电话',
          prop: 'dept',
          lgWidth: 8
        },
        {
          label: '工号',
          prop: 'name',
          lgWidth: 8
        },
        {
          label: '工单号',
          prop: 'phone',
          lgWidth: 8
        },
        {
          label: '工单类型',
          prop: 'time',
          lgWidth: 8
        },
        {
          label: '申报来源',
          prop: 'returnTime',
          lgWidth: 8
        },
        {
          label: '紧急程度',
          prop: 'way',
          lgWidth: 8
        },
        {
          label: '申报属性',
          prop: 'money',
          lgWidth: 8
        }
      ],
      completeInfo: [
        {
          label: '维修班组',
          assetsName: '维修班组',
          prop: 'code',
          lgWidth: 8
        },
        {
          label: '维修工程师',
          assetsName: '管理员',
          prop: 'dept',
          lgWidth: 8
        },
        {
          label: '责任班组',
          prop: 'name',
          lgWidth: 8
        },
        {
          label: '责任工程师',
          prop: 'phone',
          lgWidth: 8
        },
        {
          label: '协助班组',
          prop: 'time',
          lgWidth: 8
        },
        {
          label: '协助工程师',
          prop: 'returnTime',
          lgWidth: 8
        },
        {
          label: '完工时间',
          prop: 'way',
          assetsName: '2024-10-31 10:20:32',
          lgWidth: 8
        },
        {
          label: '外送维修',
          assetsName: '否',
          prop: 'money',
          lgWidth: 8
        }
      ],
      basicInfoForm: {},
      currentStatus: 13,
      flowData: [
        {
          name: '报修',
          date: '2024-10-30 10:40:19',
          icon: 'el-icon-document-copy'
        },
        {
          name: '派单',
          date: '2024-10-30 11:20:53',
          icon: 'el-icon-document-add'
        },
        {
          name: '接单',
          date: '2024-10-31 08:42:20',
          icon: 'el-icon-document'
        },
        {
          name: '维修完工',
          date: '2024-10-31 15:18:19',
          icon: 'el-icon-document-remove'
        },
        {
          name: '验收完成',
          date: '2024-10-31 18:20:42',
          icon: 'el-icon-document-checked'
        }
      ]
    }
  },
  mounted() {
    this.getDataList()
  },
  methods: {
    getDataList() {
      this.tableData = [
        {
          assetsName: '呼吸机',
          assetsCode: '30SD45EDT123',
          commonName: '--',
          model: 'Primus',
          statusName: '闲置中',
          sort: '呼吸机',
          money: '--',
          settlementWay: '按小时结算',
          rate: '1.00元/小时',
          money: '--',
          settlementWay: '按小时结算',
          rate: '1.00元/小时'
        },
        {
          assetsName: '眼科手术器械',
          assetsCode: '30SD45EDT123',
          commonName: '--',
          model: 'Primus',
          statusName: '闲置中',
          sort: '呼吸机',
          money: '--',
          settlementWay: '按小时结算',
          rate: '1.00元/小时'
        }
      ]
    },
    handleClose(done) {
      done()
    }
  }
}
</script>
  <style lang="scss" scoped>
.list-index-view {
  position: relative;
  height: 100%;
  padding: 0px 40px;
}
.view-header-box {
  background-color: rgba(53, 98, 219, 0.06);
  display: flex;
  align-items: center;
  height: 100px;
  justify-content: space-between;
  .header-left-box {
    .state0,
    .state12 {
      background-color: #8c8c8c;
    }
    .state4,
    .state1 {
      background-color: #fa8c2b;
    }
    .state5,
    .state2 {
      background-color: #52c41a;
    }
    .state6,
    .state3,
    .state13 {
      background-color: #ff4d4f;
    }
    .state7 {
      background-color: #1474a4;
    }
    .cfx {
      display: flex;
      height: 36px;
      line-height: 36px;
      position: relative;
      .text {
        display: inline-flex;
        align-items: center;
        height: 36px;
        line-height: 36px;
        font-size: 14px;
        color: #fff;
        margin-right: 26px;
        .icon {
          color: #fff;
          margin: 0 5px;
        }
        span {
          font-weight: bold;
        }
      }
      .triangle {
        position: absolute;
        top: 2px;
        right: 0;
        width: 0;
        height: 0;
        border-width: 16px;
        border-style: solid;
        border-color: transparent #061430 transparent transparent;
      }
    }
  }
  .flowBox {
    width: calc(100% - 180px);
    display: flex;
    justify-content: space-evenly;
    align-items: center;
    .flowItemWrap {
      position: relative;
    }
    .line {
      position: absolute;
      top: 20px;
      left: -90px;
    }
    .flowItem {
      display: flex;
      flex-direction: column;
      align-items: center;
    }
  }
}
.view-content {
  height: calc(100% - 4rem);
  .image {
    width: 100px;
    height: 120px;
    margin-top: 8px;
  }
  .toptip {
    box-sizing: border-box;
    height: 50px;
    width: 100%;
    line-height: 50px;
    display: flex;
    font-size: 16px;
    align-items: center;
  }
  .green_line {
    display: inline-block;
    width: 6px;
    height: 16px;
    border-radius: 2px;
    background: #1574a4;
    margin-right: 10px;
    vertical-align: middle;
  }
  .table-icon {
    width: 16px;
    margin-right: 3px;
  }
  .status-box {
    display: flex;
    align-items: center;
  }
  .table-view {
    height: 100%;
  }
}
::v-deep .el-form-item__content {
  font-size: 0.875rem;
}
::v-deep .el-form-item {
  margin-bottom: 8px !important;
}
::v-deep .el-form-item__label {
  color: #ffffff !important;
}
</style>

