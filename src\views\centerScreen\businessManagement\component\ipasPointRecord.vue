<template>
  <div class="main">
    <el-dialog :modal="false" :visible.sync="dialogShow" custom-class="mainDialog main" append-to-body :close-on-click-modal="false" :before-close="closeDialog" class="personDialog">
      <template slot="title">
        <span class="dialog-title">巡检点详情</span>
      </template>
      <div class="dialog-content">
        <el-table
          :data="tableData"
          height="calc(100% - 40px)"
          :cell-style="$tools.setCell(3)"
          :header-cell-style="$tools.setHeaderCell(3)"
          style="width: 100%"
          v-loading="tableLoading"
          element-loading-background="rgba(0, 0, 0, 0.2)"
        >
          <el-table-column fixed prop="taskPointName" align="center" show-overflow-tooltip label="巡检名称"></el-table-column>
          <el-table-column fixed prop="taskPointTypeName" align="center" show-overflow-tooltip label="巡检点类型"></el-table-column>
          <el-table-column fixed prop="state" align="center" show-overflow-tooltip label="巡检结果">
            <template slot-scope="scope">
              <span v-if="scope.row.state === '2'">合格</span>
              <span v-if="scope.row.state === '3'">不合格</span>
              <span v-if="scope.row.state === '4'">报修</span>
              <span v-else></span>
            </template>
          </el-table-column>
          <el-table-column fixed prop="hiddenDangerId" align="center" show-overflow-tooltip label="隐患编号"></el-table-column>
          <el-table-column fixed prop="excuteTime" align="center" show-overflow-tooltip label="实际巡检时间"></el-table-column>
          <el-table-column fixed prop="implementPersonName" align="center" show-overflow-tooltip label="巡检人员"> </el-table-column>
          <el-table-column fixed prop="spyScan" align="center" show-overflow-tooltip label="定位状态"> </el-table-column>
          <el-table-column fixed show-overflow-tooltip align="center" label="操作">
            <template slot-scope="scope">
              <div class="operationBtn">
                <span @click="selectConfigRowData(scope.row.id)">详情</span>
              </div>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import { getSpaceListData } from '@/utils/centerScreenApi'
export default {
  name: 'ipasPointRecord',
  props: {
    dialogShow: {
      type: Boolean,
      default: false
    },
    location: {
      type: String,
      default: ''
    },
    ssmType: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      tableData: [],
      ipasPointDetailShow: false,
      tableLoading: false
    }
  },
  watch: {},
  created() {},
  mounted() {
  },
  methods: {
    // 获取列表
    getPointDetailTableList(from, row) {
      const params = {
        pageNo: 1,
        pageSize: 25,
        spaceCode: this.location,
        spaceLevel: this.ssmType
      }
      if (from === 'inspection') {
        params.taskPointId = row.taskPointId
        params.dateType = row.dateType
      } else if (from === 'taskList') {
        params.taskId = row
      }
      this.tableLoading = true
      getSpaceListData(params).then((res) => {
        const data = res.data
        this.tableLoading = false
        if (data.data.list.length) {
          this.tableData = data.data.list
        } else {
          this.tableData = []
        }
      })
    },
    selectConfigRowData(id) {
      Object.assign(this.$parent, {
        detailId: id,
        ipasPointDetailShow: true
      })
      // document.getElementsByClassName('transparentBgColor')[0].style.backgroundColor = '#031553'
      window.chrome.webview.hostObjects.sync.bridge.ShowDialog(true)
    },
    // 取消按钮
    closeDialog() {
      this.$emit('configCloseDialog')
    }
  }
}
</script>

<style lang="scss" type="text/css" scoped>
.personDialog {
  pointer-events: none;
  ::v-deep .el-image__error {
    background: center;
  }
}
::v-deep .mainDialog {
  width: 98%;
  height: 40vh;
  margin-top: 54vh !important;
  // background: #031553;
  // border: 1px solid #5996f9;
  background: url('@/assets/images/qhdsys/bottom-table-bg.png') no-repeat;
  background-size: 100% 100%;
  padding: 0 35px;
  pointer-events: auto;
  .dialog-title {
    display: inline-block;
    width: 100%;
    text-align: center;
    transform: translateY(-6px);
    color: #cbdeed;
    &::before {
      display: none;
    }
  }
  .el-dialog__body {
    padding: 0;
    height: calc(100% - 50px);
    .dialog-content {
      width: 100%;
      height: 100%;
      .el-table {
        border: none !important;
        .redBg {
          background: #341f51;
        }
      }
    }
  }
}
</style>
