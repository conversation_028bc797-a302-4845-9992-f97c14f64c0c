<template>
  <div class="content" :class="screenSmall ? 'screen_small' : screenBack ? 'screen_back' : ''">
    <div class="title" v-if="pageType === 2">
      <div @click="gobackToDevice"><i class="el-icon-arrow-left"></i>返回</div>
    </div>
    <div class="search-form">
      <el-date-picker
        value-format="yyyy-MM-dd"
        style="margin-right: 20px"
        popper-class="timePicker"
        v-model="searchForm.daterange"
        type="daterange"
        range-separator="至"
        start-placeholder="开始日期"
        end-placeholder="结束日期"
        :picker-options="{
          firstDayOfWeek: 1
        }"
      >
      </el-date-picker>
      <el-input style="margin-right: 20px" v-model="searchForm.surveyName" placeholder="设备名称"></el-input>
      <el-select v-model="searchForm.disposeResult" placeholder="请选择处置状态">
        <el-option v-for="item in alarmStateOption" :key="item.id" :label="item.labelName" :value="item.id"> </el-option>
      </el-select>
      <el-button class="sino-button-sure" style="margin-left: 30px" @click="_searchByCondition">查询</el-button>
      <el-button class="sino-button-sure" @click="_resetCondition">重置</el-button>
      <el-button class="sino-button-sure" :disabled="multipleSelection.length == 0" @click="batching">批处理</el-button>
      <div class="right-control">
        <div class="voice-box" @click="changeVoiceFlag">
          <div :class="openVoice ? 'voice-on-icon' : 'voice-off-icon'"></div>
        </div>
      </div>
    </div>
    <div class="eahart-list">
      <div class="echarts-left">
        <div class="left-top">
          <div class="bg-content alarm-analysis">
            <div class="case-img">
              <img src="@/assets/images/elevator/icon-warn-count.png" />
              <div class="case-num">
                <span>今日报警</span>
                <p>{{ CountPolice.day }}</p>
              </div>
            </div>
            <div class="case-img">
              <img src="@/assets/images/elevator/icon-warn-count.png" />
              <div class="case-num">
                <span>本月报警</span>
                <p>{{ CountPolice.month }}</p>
              </div>
            </div>
            <div class="case-img">
              <img src="@/assets/images/elevator/icon-warn-count.png" />
              <div class="case-num">
                <span>本年报警</span>
                <p>{{ CountPolice.year }}</p>
              </div>
            </div>
          </div>
        </div>
        <div class="left-bottom">
          <div class="card_box_title">报警来源分析</div>
          <div class="bg-content">
            <div v-if="warnAnalysisShow" class="center-center">暂无数据</div>
            <div v-else style="width: 100%; height: 100%">
              <div id="alarmSourceEchart"></div>
              <div class="warnTypeCenter">
                <img src="@/assets/images/elevator/icon-warn-type-pie.png" alt="" />
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="echarts-right">
        <div class="card_box_title">近30日报警走势图</div>
        <div class="bg-content">
          <div id="trendEchart"></div>
        </div>
      </div>
    </div>
    <div class="table-list">
      <el-table
        :data="tableData"
        height="calc(100% - 40px)"
        :cell-style="$tools.setCell(3)"
        :header-cell-style="$tools.setHeaderCell(3)"
        style="width: 100%"
        v-loading="tableLoading"
        @selection-change="handleSelectionChange"
        element-loading-background="rgba(0, 0, 0, 0.2)"
      >
        <el-table-column type="selection" width="70" align="center" fixed></el-table-column>
        <el-table-column fixed type="index" width="80" show-overflow-tooltip label="序号"></el-table-column>
        <el-table-column fixed prop="surveyName" show-overflow-tooltip label="监测项名称"></el-table-column>
        <el-table-column fixed prop="parameterName" show-overflow-tooltip label="报警参数名称"></el-table-column>
        <el-table-column fixed prop="policeReason" show-overflow-tooltip label="报警原因"></el-table-column>
        <el-table-column fixed prop="gridName" show-overflow-tooltip label="地理位置"></el-table-column>
        <el-table-column fixed prop="policeTime" show-overflow-tooltip label="最新报警时间"></el-table-column>
        <el-table-column fixed prop="disposeResultName" width="120" show-overflow-tooltip label="状态">
          <template slot-scope="scope">
            <div class="state-color" :style="scope.row.disposeResult == '0' ? '--color:#FF605E' : '--color:#86FF8B'">
              {{ scope.row.disposeResultName }}
            </div>
          </template>
        </el-table-column>
        <el-table-column fixed show-overflow-tooltip width="150" label="操作">
          <template slot-scope="scope">
            <div class="operationBtn">
              <span @click="selectRowData(scope.row, 'check')" style="margin-right: 10px">详情</span>
              <span v-if="scope.row.disposeResultName !== 2" @click="selectRowData(scope.row, 'add')" style="margin-right: 10px">处置</span>
            </div>
          </template>
        </el-table-column>
      </el-table>
      <el-pagination
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page="currentPage"
        :page-sizes="[15, 20, 30, 40]"
        :page-size="pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
        class="pagination"
      ></el-pagination>
    </div>
  </div>
</template>
<script>
import { mapGetters } from 'vuex'
import { getDictDataList } from '@/utils/api'
import { getPoliceCount, getTrendStatisticLine, getReasonStatisticPie, getPoliceList, disposePoliceBatch } from '@/utils/peaceLeftScreenApi'
import * as echarts from 'echarts'
export default {
  data() {
    return {
      openVoice: true,
      tableData: [],
      currentPage: 1,
      pageSize: 15,
      total: 0,
      tableLoading: false,
      warnAnalysisShow: true,
      searchForm: {
        daterange: [],
        surveyName: '',
        disposeResult: ''
      },
      CountPolice: {
        day: '',
        month: '',
        year: ''
      }, // 报警统计-日-月-年
      policeLevelOption: [],
      alarmStateOption: [
        {
          id: 0,
          labelName: '未处理'
        },
        {
          id: 1,
          labelName: '已确认'
        },
        {
          id: 2,
          labelName: '误报'
        }
      ],
      multipleSelection: [],
      timer: null,
      projectCode: '',
      screenSmall: false, // 是否是小屏
      screenBack: false, // 是否是返回
      pageType: 1 // 1：默认 2：返回
      // projectCode: '713e24b03094410499db0b08a2eccbcc'
    }
  },
  created() {
    // 初始化 根据模块过滤数据
    if (Object.hasOwn(this.$route.query, 'projectCode')) {
      this.projectCode = this.$route.query.projectCode || ''
      this.screenSmall = true
    } else {
      this.projectCode = ''
      this.screenSmall = false
    }
    if (Object.hasOwn(this.$route.query, 'pageType')) {
      this.pageType = 2
      this.screenBack = true
      this.screenSmall = false
      try {
        window.chrome.webview.hostObjects.sync.bridge.SetIsShowShiBei(false)
      } catch (error) {}
    }
  },
  mounted() {
    this.search()
    this.timer = setInterval(() => {
      setTimeout(() => {
        this.search()
      }, 0)
    }, 1000 * timerNum)
    this.$once('hook:beforeDestroy', () => {
      clearInterval(this.timer)
      this.timer = null
    })
    const dictList = JSON.parse(localStorage.getItem('dictList'))
    if (dictList === null || dictList === undefined) {
      this.setDictStorage()
    } else {
      this.setOptionList(dictList)
    }
  },
  computed: {
    ...mapGetters({
      socketelevatorMsgs: 'socketelevatorMsgs'
    })
  },
  watch: {
    socketelevatorMsgs() {
      this.search()
    }
  },
  methods: {
    search() {
      this.getCountAlarmNum()
      this.getCountAlarmTrend()
      this.getAlarmSourceData()
      this.getTableData()
    },
    gobackToDevice() {
      try {
        // 来源是 运行监控
        if (this.$route.query.sourcePath === '/operationMonitoring') {
          window.chrome.webview.hostObjects.sync.bridge.LeftMenuSwitch('IsCheckedYX')
        } else {
          window.chrome.webview.hostObjects.sync.bridge.SetIsShowShiBei(true)
        }
      } catch (error) {}
      this.$router.go(-1)
    },
    // 获取 报警统计数据
    getCountAlarmNum() {
      getPoliceCount({
        projectCode: this.projectCode
      }).then((res) => {
        const data = res.data
        if (data.code === '200') {
          Object.assign(this.CountPolice, data.data)
        } else {
          this.$message({
            message: data.msg,
            type: 'warning'
          })
        }
      })
    },
    // 获取 近30日报警走势图
    getCountAlarmTrend() {
      getTrendStatisticLine({ projectCode: this.projectCode }).then((res) => {
        const data = res.data
        if (data.code === '200') {
          // 获取近30日报警走势图
          this.getTrendEchart(data.data)
        } else {
          this.$message({
            message: data.msg,
            type: 'warning'
          })
        }
      })
    },
    // 获取 报警来源分析数据
    getAlarmSourceData() {
      getReasonStatisticPie({ projectCode: this.projectCode }).then((res) => {
        const data = res.data
        if (data.code === '200') {
          // const toData = JSON.parse(data.data)
          if (data.data.length) {
            this.warnAnalysisShow = false
            this.$nextTick(() => {
              // 报警来源分析
              this.getAlarmSourceEchart(data.data)
            })
          } else {
            this.warnAnalysisShow = true
          }
        } else {
          this.$message({
            message: data.msg,
            type: 'warning'
          })
        }
      })
    },
    // 获取列表数据
    getTableData() {
      const params = JSON.parse(JSON.stringify(this.searchForm))
      Object.assign(params, {
        projectCode: this.projectCode,
        pageNo: this.currentPage,
        pageSize: this.pageSize,
        startTime: params.daterange.length ? params.daterange[0] : '',
        endTime: params.daterange.length ? params.daterange[1] : ''
      })
      delete params.daterange
      this.tableLoading = true
      getPoliceList(params).then((res) => {
        const data = res.data
        this.tableLoading = false
        if (data.code === '200') {
          this.tableData = data.data.list
          this.total = data.data.count
        } else {
          this.$message({
            message: data.msg,
            type: 'warning'
          })
        }
      })
    },
    selectRowData(row, type) {
      // if (type === 'dispose' || type === 'detail') {
      this.$router.push({
        name: 'elevatorWarnDisposal',
        query: {
          type: type,
          id: row.id,
          projectCode: this.projectCode
        }
      })
      // }
    },
    handleSizeChange(val) {
      this.pageSize = val
      this.getTableData()
    },
    handleCurrentChange(val) {
      this.currentPage = val
      this.getTableData()
    },
    getTrendEchart(list) {
      const getchart = echarts.init(document.getElementById('trendEchart'))
      const xArr = list.xAxisData
      const value = list.seriesData
      var color = 'rgba(246, 180, 58'
      var lineY = []
      var data = {
        type: 'line',
        color: color + ')',
        smooth: true,
        areaStyle: {
          normal: {
            color: new echarts.graphic.LinearGradient(
              0,
              0,
              0,
              1,
              [
                {
                  offset: 0,
                  color: color + ', 0.3)'
                },
                {
                  offset: 1,
                  color: color + ', 0)'
                }
              ],
              false
            )
          }
        },
        symbolSize: 5,
        data: value
      }
      lineY.push(data)
      // }
      var option = {
        backgroundColor: 'rgba(128, 128, 128, 0)',
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            lineStyle: {
              color: '#FFF'
            }
          },
          backgroundColor: '#0C2269',
          borderColor: '#5B617C',
          textStyle: {
            color: '#719DE7'
          },
          formatter: '时间：{b0}</br>数量：{c0}'
        },
        grid: {
          top: '14%',
          left: '4%',
          right: '4%',
          bottom: '12%'
        },
        xAxis: {
          type: 'category',
          boundaryGap: false,
          data: xArr,
          axisLabel: {
            textStyle: {
              color: '#FFF'
            },
            formatter: function (params) {
              return params.substring(3).replace('-', '.')
            }
          },
          axisLine: {
            lineStyle: {
              color: '#50608B',
              width: 1
            }
          }
        },
        yAxis: {
          type: 'value',
          axisLabel: {
            formatter: '{value}',
            textStyle: {
              color: '#FFF'
            }
          },
          splitLine: {
            lineStyle: {
              color: '#2A4382'
            }
          }
        },
        series: lineY
      }
      getchart.setOption(option)
      // })
      // 随着屏幕大小调节图表
      window.addEventListener('resize', () => {
        getchart.resize()
      })
    },
    getAlarmSourceEchart(chartdata) {
      const getchart = echarts.init(document.getElementById('alarmSourceEchart'))
      getchart.resize()
      const nameList = Array.from(chartdata, (item) => item.name)
      let sum = 0
      const warnData = []
      const colorIn = ['rgba(246, 180, 58, 1)', 'rgba(183, 187, 202, 1)', 'rgba(81, 136, 252, 1)', 'rgba(0, 221, 197, 1)', 'rgba(255, 86, 84, 1)']
      const colorOut = ['rgba(246, 180, 58, .4)', 'rgba(183, 187, 202, .4)', 'rgba(81, 136, 252, .4)', 'rgba(0, 221, 197, .4)', 'rgba(255, 86, 84, .4)']
      chartdata.forEach((item, index) => {
        sum += Number(item.value)
        const randomRgbColor = this.$tools.randomRgbColor('array')
        if (!colorIn[index] || !colorOut[index]) {
          colorIn.push(randomRgbColor[1])
          colorOut.push(randomRgbColor[0])
        }
        warnData.push(item, {
          value: sum / 100,
          labelLine: {
            show: false,
            lineStyle: {
              color: 'transparent'
            }
          },
          itemStyle: {
            color: 'transparent'
          }
        })
      })
      // colorIn数组每个元素后插入空字符
      colorIn.forEach((item, index) => {
        colorIn.splice(2 * index + 1, 0, '')
        colorOut.splice(2 * index + 1, 0, '')
      })
      const legend = this.group(nameList, 3)
      console.log(legend)
      const legendData = legend.map((item, index) => {
        const data = {
          type: index === legend.length - 1 ? 'scroll' : 'plain',
          orient: 'horizontal',
          // top: 'center',
          x: 'center',
          left: '22%',
          top: 0 + index * 18 + '%',
          data: item,
          itemWidth: 12,
          itemHeight: 12,
          itemGap: 22,
          textStyle: {
            fontSize: 12, // 字体大小
            color: '#FFF' //  字体颜色
          },
          tooltip: {
            show: true,
            confine: true // 限制tootip在容器内
          },
          formatter: function (name) {
            var oa = option.series[0].data
            var num = oa.reduce((sum, e) => sum + e.value, 0)
            for (var i = 0; i < option.series[0].data.length; i++) {
              if (name === oa[i].name) {
                return ' ' + (name.length > 5 ? name.substr(0, 5) + '...' : name) + ' (' + oa[i].value + ')   ' + ((oa[i].value / num) * 100).toFixed(2) + '%'
              }
            }
          }
        }
        return data
      })
      const option = {
        backgroundColor: 'rgba(128, 128, 128, 0)',
        legend: legendData,
        tooltip: {
          trigger: 'item',
          formatter: '{b} : {c} ({d}%)',
          confine: true // 限制tootip在容器内
        },
        series: [
          {
            type: 'pie',
            radius: ['50%', '75%'],
            center: ['12%', '50%'],
            hoverAnimation: false,
            startAngle: 180,
            selectedMode: 'single',
            label: {
              show: false
            },
            itemStyle: {
              normal: {
                color: function (params) {
                  return colorOut[params.dataIndex]
                }
              }
            },
            data: warnData,
            z: 666
          },
          {
            type: 'pie',
            radius: ['75%', '80%'],
            center: ['12%', '50%'],
            hoverAnimation: false,
            startAngle: 180,
            selectedMode: 'single',
            label: {
              show: false
            },
            itemStyle: {
              normal: {
                color: function (params) {
                  return colorIn[params.dataIndex]
                }
              }
            },
            data: warnData,
            z: 1
          }
        ]
      }
      getchart.clear()
      getchart.setOption(option)
      // 随着屏幕大小调节图表
      window.addEventListener('resize', () => {
        getchart.resize()
      })
    },
    // 字典信息重新赋值
    setDictStorage() {
      getDictDataList({}).then((res) => {
        const data = res.data
        if (data.code === '200') {
          localStorage.setItem('dictList', JSON.stringify(data.data))
          this.setOptionList(data.data)
        }
      })
    },
    setOptionList(dictList) {
      this.policeLevelOption = this.getSelectDictOption(dictList, '警情级别')
    },
    getSelectDictOption(list, type) {
      const option = list.length ? list.filter((e) => e.dictName === type) : []
      const optionList = option.length ? option[0].dictConfigList : []
      return optionList
    },
    group(array, subGroupLength) {
      let index = 0
      const newArray = []
      while (index < array.length) {
        if (newArray.length > 3) {
          newArray.push(array.slice(index, array.length))
          index = array.length
        } else {
          newArray.push(array.slice(index, (index += subGroupLength)))
        }
      }
      return newArray
    },
    // 改变电梯消音
    changeVoiceFlag() {
      this.openVoice = !this.openVoice
      localStorage.setItem('elevatorOpenAlarmVoice', this.openVoice)
    },
    // 列表查询
    _searchByCondition() {
      this.currentPage = 1
      this.getTableData()
    },
    // 重置
    _resetCondition() {
      this.searchForm = {
        daterange: [],
        surveyName: '',
        disposeResult: ''
      }
      this.currentPage = 1
      this.pageSize = 15
      this.total = 0
      this.getTableData()
    },
    // 导出
    // exportEvent() {
    //   const params = JSON.parse(JSON.stringify(this.searchForm))
    //   Object.assign(params, {
    //     startTime: params.daterange.length ? params.daterange[0] : '',
    //     endTime: params.daterange.length ? params.daterange[1] : ''
    //   })
    //   delete params.daterange
    //   exportExcel(params).then((res) => {
    //     const data = res.data
    //     const blob = new Blob([data], { type: 'application/vnd.ms-excel' })
    //     const fileName = '警情信息表.xls'
    //     const elink = document.createElement('a')
    //     elink.download = fileName
    //     elink.style.display = 'none'
    //     elink.href = URL.createObjectURL(blob)
    //     document.body.appendChild(elink)
    //     elink.click()
    //     URL.revokeObjectURL(elink.href) // 释放URL 对象
    //     document.body.removeChild(elink)
    //   })
    // },
    // 勾选
    handleSelectionChange(val) {
      this.multipleSelection = val
    },
    /**
     * 批处理
     */
    batching() {
      this.$confirm('该操作将会对所有选中的信息进行确认，是否继续？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        cancelButtonClass: 'sino-button-sure',
        confirmButtonClass: 'sino-button-sure',
        customClass: 'confirm-box-class',
        type: 'warning'
      }).then(() => {
        const ids = []
        this.multipleSelection.map((item) => {
          ids.push(item.id)
        })
        disposePoliceBatch({ ids: ids.join(','), disposeResult: 1 }, this.requestHttp).then((resp) => {
          const res = resp.data
          if (res.code === '200') {
            this.$confirm('报警消息已确认！', '批处理', {
              confirmButtonText: '确定',
              cancelButtonText: '关闭',
              cancelButtonClass: 'sino-button-sure',
              confirmButtonClass: 'sino-button-sure',
              customClass: 'confirm-box-class',
              type: 'warning'
            }).then(() => {})
            this._searchByCondition()
          }
        })
      })
    }
  }
}
</script>
<style lang="scss" scoped>
@import '@/assets/sino-ui/common/var.scss';
.screen_small {
  padding-left: 20px !important;
  padding-bottom: 80px !important;
}
.screen_back {
  padding-bottom: 40px !important;
}
html,
.content {
  position: relative;
  box-sizing: border-box;
  width: 100%;
  height: 100%;
  padding: 1.25rem 0.625rem 0.625rem 0.625rem;
  background: url('~@/assets/images/elevator/page-bg.png') no-repeat;
  background-size: 100% 100%;
  .title {
    height: 30px;
    // position: relative;
    position: absolute;
    left: 16px;
    top: 10px;
    div {
      cursor: pointer;
      position: absolute;
      top: 5px;
      left: 0;
      width: 80px;
      height: 30px;
      line-height: 30px;
      text-align: center;
      color: #618ad3;
      font-size: 0.875rem;
      white-space: nowrap;
      background-image: url('~@/assets/images/peace/btn-back.png');
      background-size: 100% 100%;
      background-repeat: no-repeat;
    }
  }
  .screen_back {
    .title {
      position: initial;
      div {
        left: 10px;
        top: 10px;
      }
    }
  }
  .search-form {
    height: 2.5rem;
    width: 100%;
    display: flex;
    align-items: center;
    .exportBtn {
      position: absolute;
      right: 16px;
      transition: right 0.8s;
    }
    ::v-deep .el-input {
      width: 200px;
    }
    ::v-deep .el-input__inner {
      background: center;
      border: 1px solid $input-border-color;
      border-radius: 0;
      color: $color;
      height: 32px;
      font-family: PingFangSC-Medium, PingFang SC;
    }
    ::v-deep .el-range-input {
      background: center;
      color: $color;
    }
    ::v-deep .el-range-separator,
    ::v-deep .el-range__icon {
      color: #3769be;
    }
    .right-control {
      flex: 1;
      // text-align: right;
    }
    .voice-box {
      margin: auto 0;
      width: 36px;
      height: 24px;
      background: #e6effc;
      border-radius: 100px 100px 100px 100px;
      display: flex;
      float: right;
      cursor: pointer;
      > div {
        width: 16px;
        height: 16px;
        margin: auto;
      }
    }
    .voice-on-icon {
      background: url('~@/assets/images/elevator/voice-on.png') no-repeat;
      background-size: 100% 100%;
    }
    .voice-off-icon {
      background: url('~@/assets/images/elevator/voice-off.png') no-repeat;
      background-size: 100% 100%;
    }
  }
  .eahart-list {
    width: 100%;
    height: 46%;
    margin-top: 0.7rem;
    display: flex;
    justify-content: space-between;
    .echarts-left {
      width: calc(55% - 16px);
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      .left-top {
        height: 41%;
      }
      .left-bottom {
        height: calc(59% - 16px);
      }
      .alarm-analysis {
        display: flex;
        justify-content: space-around;
        box-sizing: border-box;
        width: 100%;
        height: 100%;
        .case-img {
          width: 30%;
          height: 100%;
          // margin-top: 5%;
          // margin: auto;
          // position: relative;
          display: flex;
          img {
            width: 130px;
            height: 133px;
            // aspect-ratio: 1.05/1;
          }
          .case-num {
            flex: 1;
            text-align: left;
            padding-left: 10px;
            display: flex;
            justify-content: space-evenly;
            flex-direction: column;
            // top: 16%;
            color: #fff;
            font-size: 1.25rem;
            font-weight: bold;
            span {
              font-size: 16px;
              font-family: OPPOSans-Regular, OPPOSans;
              font-weight: 400;
              color: #FFFFFF;
              display: block;
              height: 27px;
              // line-height: 10px;
              padding-left: 20px;
              background: url('~@/assets/images/elevator/warn-text-bg.png') no-repeat;
              background-size: 100% 100%;
            }
            p {
              padding-left: 20px;
            }
          }
          .case-anim-icon {
            position: absolute;
            // bottom: 10%;
            top: 60%;
            left: calc(50% - 1rem);
            width: 2rem;
            height: 2rem;
            background: url('~@/assets/images/peace/icon-warn.png') no-repeat;
            background-size: 100% 100%;
            animation: jump 1s ease-out infinite alternate-reverse;
          }
        }
      }
    }
    .echarts-right {
      width: 45%;
      padding: 3px;
      box-sizing: border-box;
    }
    .card_box_title {
      height: 31px;
      width: 100%;
      line-height: 31px;
      padding-left: 45px;
      box-sizing: border-box;
      font-size: 15px;
      font-family: Alibaba PuHuiTi-Medium, Alibaba PuHuiTi;
      font-weight: 500;
      color: #b7cfff;
      background: url('~@/assets/images/elevator/card-title-50-bg.png') no-repeat;
      background-size: 100% 100%;
    }
    .bg-title {
      margin-top: 5px;
      height: 2.5rem;
      line-height: 2.5rem;
      color: #d4e3f9;
      padding-left: 3rem;
      font-family: TRENDS;
    }
    .center-center {
      width: 100%;
      height: 100%;
      display: flex;
      align-items: center;
      justify-content: center;
      color: #4d5880;
      font-size: 16px;
    }
    .bg-content {
      background: rgba(6, 18, 78, 0.5);
      position: relative;
      box-sizing: border-box;
      padding: 10px 10px 15px 10px;
      width: 100%;
      height: calc(100% - 30px);
      display: flex;
      #trendEchart,
      #alarmSourceEchart {
        width: 100%;
        height: 100%;
        position: relative;
        z-index: 2;
      }
      .warnTypeCenter {
        position: absolute;
        height: calc(100% - 25px);
        aspect-ratio: 1/1;
        left: calc(12% + 6px);
        top: calc(50% - 5px);
        transform: translateX(-50%) translateY(-50%);
        display: flex;
        img {
          margin: auto;
        }
      }
    }
  }
  .table-list {
    width: 100%;
    height: calc(54% - 4rem);
    margin-top: 0.8rem;
    .state-color {
      color: var(--color);
      &::before {
        content: '';
        display: inline-block;
        width: 8px;
        height: 8px;
        border-radius: 50%;
        background: var(--color);
        margin-right: 5px;
      }
    }
    ::v-deep .el-table th.el-table__cell>.cell {
      white-space: nowrap;
    }
  }
}
</style>
