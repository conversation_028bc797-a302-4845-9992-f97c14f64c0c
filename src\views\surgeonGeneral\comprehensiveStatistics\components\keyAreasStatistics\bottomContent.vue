<template>
  <div class="statistics_item">
    <div class="item_title">区域人流趋势</div>
    <div class="checkbox-contain">
      <el-checkbox-group v-model="checkList" fill="#52FFFC" class="checkedBoxGroup" @change="checkedChange">
        <div v-for="(item, index) in areaList" :key="index">
          <el-tooltip  effect="dark" :content="item.area" placement="top">
            <el-checkbox  :label="item.id" :disabled="index == 0" >
              {{ item.area }}
            </el-checkbox>
          </el-tooltip>
        </div>
      </el-checkbox-group>
      <img src="@/assets/images/checkbox_add.png" @click="switchPanel" />
      <div v-show="showPanel" class="panel">
        <el-checkbox-group v-model="originCheckList" fill="#52FFFC" @change="originChange">
          <el-checkbox v-for="item in originList" :key="item.id" :label="item.id" :disabled="areaList.length > 5">{{ item.area }}</el-checkbox>
        </el-checkbox-group>
      </div>
    </div>
    <div id="areaCrowdTrend" ref="areaCrowdTrend"></div>
  </div>
</template>
<script>
import * as echarts from 'echarts'
import {  findAreaThroughTrend } from '@/utils/comprehensiveStatistics.js'
export default {
  props: {
    dateType: {
      type: String,
      default: 'day'
    },
    date: {
      type: [String, Array],
      default: () => []
    }
  },
  data () {
    return {

      areaList: [],
      checkList: [],
      originList: [],
      originCheckList: [],
      showPanel: false,
      xValue: [],
      myChart: null
    }
  },
  watch: {
    dateType: {
      handler (val) {
        if (val !== 'custom') {

          this.getAreaThroughTrend(val)
        }
      },
      immediate: true
    },
    date: {
      handler(val) {
        if (val && val.length) {
          this.getAreaAlarmRank()
          this.getAreaThroughRank()
        }
      },
      deep: true

    }
  },

  methods: {
    switchPanel () {
      this.showPanel = !this.showPanel
    },
    getAreaThroughTrend (val) {
      let params = {
        dateType: this.dateType,
        beginDate: this.date[0],
        endDate: this.date[1]
      }
      findAreaThroughTrend(params).then(res => {
        if (res.data.code == 200) {
          this.originList = res.data.data.data
          this.areaList = this.originList.splice(0, 6)
          this.checkList = this.areaList.map(item => item.id)
          this.xValue = res.data.data.xaxisList
          this.initLine()
        }
      })
    },

    checkedChange (val) {
      // 这个方法只会出现取消勾选某一项
      let item = this.areaList.filter(item => {
        return !val.includes(item.id)
      })
      // 如果找到了，那么就从area里面删除这一项
      this.areaList = this.areaList.filter(el => {
        return el.id !== item[0].id
      })
      // 然后需要给originList里面添加到头部
      this.originList.unshift(item[0])
      this.initLine()
      this.$forceUpdate()
    },
    originChange (val) {
      let item = this.originList.filter(item => {
        return val.includes(item.id)
      })
      this.areaList = this.areaList.concat(item)
      this.checkList.push(item[0].id)
      this.originCheckList = []
      this.originList = this.originList.filter(el => {
        return el.id !== item[0].id
      })
      this.initLine()
      this.$forceUpdate()
    },
    initLine () {
      let chartDom = document.getElementById('areaCrowdTrend')
      if (this.myChart) {
        this.myChart.dispose()
      }
      this.myChart = echarts.init(chartDom)

      let option = {}
      this.myChart.showLoading()
      if (this.xValue && this.xValue.length) {
        option = {
          tooltip: {
            show: true,
            trigger: 'axis',
            formatter: (params) => {
              console.log(params)
              let tooltip = params[0].name + '<br/>'
              params.forEach(item => {
                let color = item.color
                let colorCircle = `<span style="display:inline-block;margin-right:5px;border-radius:50%;width:10px;height:10px;background-color:${color};"></span>`
                tooltip += `${colorCircle}${item.seriesName}: ${item.value}<br/>`
              })
              return tooltip
            }
          },

          grid: {
            left: '0',  // 调整左侧边距
            right: '5%', // 调整右侧边距
            top: '10px',  // 调整顶部边距，为图例留出空间
            bottom: '0', // 调整底部边距
            containLabel: true
          },
          xAxis: {
            type: 'category',
            data: this.xValue
          },
          yAxis: {
            type: 'value',
            splitLine: {
              show: true,
              lineStyle: {
                color: 'rgba(230,247,255,0.2)'
              }
            }
          },
          series: [

          ]
        }
      } else {
        option = {
          title: {
            text: '暂无数据',
            x: 'center',
            y: 'center',
            textStyle: {
              fontSize: 14,
              fontWeight: 'normal',
              color: '#999'
            }
          }
        }
      }
      this.areaList.forEach(el => {
        let obj = {
          name: el.area,
          type: 'line',
          data: el.data
        }
        option.series.push(obj)
      })
      this.myChart.setOption(option)
      this.myChart.off('click')
      // 图例点击事件
      this.myChart.on('click', (params) => {
        console.log(params.data)
        this.$emit('itemClick', params.data)
      })
      this.myChart.hideLoading()
    }
  }
}
</script>
<style lang="scss" scoped>
.statistics_item{
  background: rgba(133,145,206,0.05);
  padding: 16px 8px 0;
}
#areaCrowdTrend {
  width: 100%;
  height: calc(100% - 50px);
}
.item_title {
  font-weight: 500;
  font-size: 16px;
  color: #b0e3fa;
  margin-bottom: 10px;
}
.checkbox-contain {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  position: relative;
  img {
    width: 14px;
    height: 14px;
    // margin-left: 12px;
    cursor: pointer;
  }
  .panel {
    position: absolute;
    right: 0;
    top: 20px;
    background-color: #374b79;
    padding: 8px;
    z-index: 9;
    height: 100px;
    overflow: auto;
    .el-checkbox-group {
      display: flex;
      flex-direction: column;
      align-items: flex-end;
      .el-checkbox {
        width: 100%;
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-right: 0;
        margin-bottom: 5px;
      }
    }
  }
}
.checkedBoxGroup{
  display: flex;
  align-items: center;
  .el-checkbox{
    margin-right: 10px;
    display: flex;
    align-items: center;
    :deep(.el-checkbox__label){
      color: #8BDDF5;
      padding-left: 5px;
      display: inline-block;
      width: 70px;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
    }
  }
}
</style>
