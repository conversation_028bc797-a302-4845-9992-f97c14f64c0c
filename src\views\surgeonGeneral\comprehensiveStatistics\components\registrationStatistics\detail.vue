<template>
  <el-dialog
    v-dialogDrag
    :modal="false"
    :visible="visible"
    custom-class="mainDialog main"
    append-to-body
    :close-on-click-modal="false"
    :before-close="closeDia"
    class="all-table-componentList"
  >
    <template slot="title">
      <span class="dialog-title">号源管理</span>
    </template>
    <div class="dialog-content">
      <div class="search-box">
        <div style="margin-right: 16px" class="taskInput">
          <el-input
            v-model="searchParams.doctorName"
            placeholder="医生名称/编码"
          ></el-input>
        </div>
        <div style="margin-right: 16px" class="taskInput">
          <el-input
            v-model="searchParams.chairName"
            placeholder="椅位名称"
          ></el-input>
        </div>

        <div style="margin-right: 16px" class="taskInput">
          <el-date-picker
            v-model="searchParams.time"
            type="date"
            placeholder="出诊日期"
            popper-class="date-style"
            value-format="yyyy-MM-dd"
            format="yyyy-MM-dd"
          >
          </el-date-picker>
        </div>
        <div style="margin-right: 16px" class="taskInput">
          <el-select
            v-model="searchParams.departmentId"
            placeholder="所在科室"
            filterable
            multiple
            collapse-tags
            popper-class="new-select"
          >
            <el-option
              v-for="item in deptList"
              :key="item.id"
              :label="item.deptName"
              :value="item.deptId"
            >
            </el-option>
          </el-select>
        </div>

        <div class="search-btn">
          <el-button @click="resetSearch">重置</el-button>
          <el-button @click="handleSearch">查询</el-button>
        </div>
      </div>
      <div class="tableContent">
        <el-table
          v-loading="tableLoading"
          :data="tableData"
          height="100%"
          :cell-style="$tools.setCell(3)"
          :header-cell-style="$tools.setHeaderCell(3)"
          style="height: 100%"
          element-loading-background="rgba(0, 0, 0, 0.2)"
        >
          <el-table-column prop="doctorName" label="医生名称" width="100" show-overflow-tooltip> </el-table-column>
          <el-table-column prop="title" label="职称"  width="100" show-overflow-tooltip> </el-table-column>
          <el-table-column prop="departmentName" label="所在科室"   show-overflow-tooltip> </el-table-column>
          <el-table-column prop="chairName" label="出诊椅位"  width="100" show-overflow-tooltip> </el-table-column>
          <el-table-column prop="visitDate" label="出诊日期" width="240">
            <template slot-scope="scope">
              <span>{{ scope.row.visitDate }} {{ scope.row.visitBeginTime }}-{{ scope.row.visitEndTime }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="numberTotal" width="100" label="号源总数"> </el-table-column>
          <el-table-column prop="registerNum" width="100" label="挂号数"> </el-table-column>
          <el-table-column prop="surplusNum" width="100" label="剩余号量"> </el-table-column>
          <el-table-column prop="waitNum" width="100" label="候诊数"> </el-table-column>
          <el-table-column prop="alreadyNum" width="100" label="已诊数"> </el-table-column>
          <el-table-column prop="abc" width="100" label="操作">
            <template slot-scope="scope">
              <span
                class="operation-span"
                @click="viewRecord(scope.row)"
              >详情</span
              >
            </template>
          </el-table-column>
        </el-table>
        <el-pagination
          :current-page="currentPage"
          :page-sizes="[15, 20, 30, 40]"
          :page-size="pageSize"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
          class="pagination"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        ></el-pagination>
      </div>
      <RegistrationRecord v-if="recordVisible" :visible="recordVisible" :params="currentItem" @close="recordVisible = false"/>
    </div>
  </el-dialog>
</template>
<script>
import dayjs from 'dayjs'
import { sourceManagement } from '@/utils/comprehensiveStatistics'
import RegistrationRecord from './record'
import { GetSpaceTree } from '@/utils/spaceManage'
import { allDeptList } from '@/utils/newIot'
export default {
  components: {
    RegistrationRecord
  },
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    params: {
      type: Object,
      default: () => {
        return {}
      }

    }
  },
  data() {
    return {
      searchParams: {
        doctorName: '',
        time: '',
        departmentId: [],
        position: '',
        registType: ''
      },
      deptList: [],

      spaceAreaList: [],
      alarmLevelList: [],
      tableLoading: false,
      tableData: [],
      currentPage: 1,
      pageSize: 15,
      total: 0,
      recordVisible: false,
      currentItem: {}
    }
  },
  mounted () {
    this.searchParams.time = dayjs().format('YYYY-MM-DD')
    this.getTreelist()
    this.getUseDeptList()
    this.getList()
  },
  methods: {
    // 获取科室
    getUseDeptList() {
      allDeptList().then((res) => {
        if (res.data.code == '200') {
          this.deptList = res.data.data
        }
      })
    },
    // 获取服务空间树形结构
    getTreelist() {
      GetSpaceTree().then((res) => {
        if (res.data.code == 200) {
          this.spaceAreaList = this.$tools.transData(res.data.data, 'id', 'pid', 'children')
        }
      })
    },
    closeDia() {
      this.$emit('close')
    },
    getList () {
      this.tableLoading = true
      let params = {
        ...this.searchParams,
        page: this.currentPage,
        pageSize: this.pageSize
      }
      sourceManagement(params).then(res => {
        if (res.data.code == 200) {
          this.tableData = res.data.data.records
          this.total = res.data.data.total
        }
      }).finally(() => {
        this.tableLoading = false
      })
    },
    resetSearch () {
      this.searchParams = {
        doctorName: '',
        time: '',
        departmentId: [],
        position: '',
        registType: ''
      }
      this.currentPage = 1
      this.getList()
    },
    handleSearch () {
      this.currentPage = 1
      this.getList()
    },
    handleSizeChange(val) {
      this.currentPage = 1
      this.pageSize = val
      this.getList()
    },
    handleCurrentChange(val) {
      this.currentPage = val
      this.getList()
    },
    viewRecord (row) {
      this.recordVisible = true
      this.currentItem = row
    }
  }
}
</script>
<style lang="scss" scoped>
:deep(.mainDialog) {
  width: 80%;
  height: 84vh;
  margin-top: 6vh !important;
  // border: 1px solid #5996f9;
  background-color: transparent !important;
  pointer-events: auto;
  background-image: url("@/assets/images/table-bg.png");
  background-repeat: no-repeat;
  background-size: 100% 100%;
  box-shadow: none;
  .el-dialog__headerbtn {
    transform: translateX(-36px);
    width: 25px;
    height: 25px;
    background-image: url("@/assets/images/close.png");
    background-repeat: no-repeat;
    background-size: 100% 100%;
    .el-dialog__close::before {
      display: none;
    }
  }
  .dialog-title {
    display: inline-block;
    width: 100%;
    text-align: center;
    transform: translateY(-6px);
    color: #cbdeed;
  }
  .dialog-title::before {
    display: none;
  }

  .el-dialog__header {
    background-color: transparent;
  }
  .el-dialog__body {
    padding: 0;
    height: calc(100% - 60px);
    max-height: fit-content;
    overflow-y: hidden;
    background-color: transparent;
    padding: 0px 80px;
    .dialog-content {
      width: 100%;
      height: 100%;
      background-color: transparent;

      .el-table {
        background-color: transparent;
        border: 1px solid #202d4c;
        .el-table__header-wrapper {
          background-color: transparent !important;
        }
        .el-table__body-wrapper {
          background-color: transparent !important;
        }
        tr {
          background-color: transparent !important;
        }
        td {
          background-color: transparent !important;
        }
      }
    }
  }
}
:deep(.search-box) {
  display: flex;
  flex-wrap: wrap;

  .taskInput {
    .el-input {
      width: 200px;
    }
    .el-cascader {
      line-height: 35px;
      .el-input__inner {
        height: 35px !important;
      }
    }
  }

  .el-button {
    background-image: url("@/assets/images/btn.png");
    background-repeat: no-repeat;
    background-size: 100% 100%;
    height: 35px;
  }
  .search-btn {
    // margin: 10px 0;
    margin-left: auto;
  }
  .el-select {
    .el-input .el-input__inner {
      border-color: rgba(133, 145, 206, 0.5);
      width: 200px;
      height: 35px;
    }
  }

  .el-date-editor {
    width: 300px;
    height: 35px;
    background-color: #14233e;
    border-color: rgba(133, 145, 206, 0.5);
    .el-input__icon {
      transform: translateY(-2px);
    }
    .el-range-input {
      background-color: #14233e;
      color: rgba(255, 255, 255, 0.8);
    }
    .el-range-separator {
      color: rgba(255, 255, 255, 0.8);
      transform: translateY(-2px);
    }
  }
  .el-input .el-input__inner {
    border-color: rgba(133, 145, 206, 0.5);
    border-radius: 4px;
    color: rgba(255, 255, 255, 0.8);
  }
}
.search-box {
  margin: 5px 0 15px;
}

.search-box .el-input {
  // width: 120px;
  height: 35px;
}
.tableContent {
  width: 100%;
  height: calc(100% - 56px - 60px);
}
:deep(.el-select__tags) {
  span {
    .el-tag {
      &:first-child {
        .el-select__tags-text {
          display: inline-block;
          width: 50px;
          text-overflow: ellipsis;
          overflow: hidden;
          white-space: nowrap;
        }
      }
    }
  }
}
</style>
