$--aside-background : #121F3E;



//字体相关

//颜色
$font-base-color:#ffffff;
$font-color-theme1 : #A1A9BC;
$font-color-theme1-menu-hover:#5188FC;
$font-color-theme2 : #fff;
$font-color-theme3 : #fff;

//尺寸
$font_little_s : 10px;
$font_little : 12px;
$font_medium_s : 14px;
$font_medium : 16px;
$font_large_s : 18px;
$font_large : 20px;

//背景

// 整体背景
$background-color-theme1 :#F5F5FA;
$background-color-theme2 :red;
$background-color-theme3 :blue;
// aside背景
$aside-background1 : #121F3E;
$aside-background1-item-hover:rgba(105,141,255,1);
$aside-background2 : red;
$aside-background3 : blue;
// header背景
$background-color-header1 :#fff;
$background-color-header2 :red;
$background-color-header3 :blue;
// body背景

//按钮
$button-color: #5188FC;
$button-color-white: #fff;
$button-color-border-plain: #DCDFE6;
$button-color-text: #909399;
$button-color-text-reset: #606266;

//表单
$input-color: #031553;
$input-border-color: #3056A2;

//表格
$table-color-hover:#ECF5FF;
$table-thead-color:#F2F4FB;


//内容主题颜色
$content-theme-color: #5188FC;

$btnColor: #A1C5FF;
$color: #FFFFFF;
$bcgColor: #031553;
$btnBgColor: #0E287E;
$activeColor: #FFE3A6;

$normal-state-color: #FFCA64;
$abnormal-state-color: #FF2D55;