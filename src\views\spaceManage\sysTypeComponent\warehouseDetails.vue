<template>
  <div class="warehouseDetails">
    <div class="module-container" style="height: calc(35%); background: rgba(133,145,206,0.15);margin-bottom: 16px;">
      <div class="info-header clear">
        <p class="info-header-text fl">库房信息</p>
      </div>
      <div class="module-content info-list" style="height: calc(100% - 44px)">
        <div class="info-list-item">
          <p class="item-label">库房数量：</p>
          <p class="item-value">{{ count || '---' }}</p>
        </div>
        <div v-for="item in infoList" :key="item.label" class="info-list-item">
          <p class="item-label">{{ item.label }}：</p>
          <p class="item-value">{{ detailsInfo[item.key] || '---' }}</p>
        </div>
      </div>
    </div>
    <div class="module-container" style="height: calc(65%)">
      <div class="module-header">
        <div class="title-left">
          <p class="title-left-text showFloorName">危化品列表</p>
        </div>
      </div>
      <div class="module-content" style="height: calc(100% - 44px)">
        <el-table class="table-center-transfer" :data="tableList" height="100%"
                  :cell-style="{ padding: ' 8px', backgroundColor: 'transparent', border: 'none', padding: '3px' }"
                  :header-cell-style="{ background: '#8591CE26!important', color: '#8BDDF5FF', padding: '4px 8px', fontWeight: 'bold' }"
                  style="width: 100%" element-loading-background="rgba(0, 0, 0, 0.2)">
          <el-table-column prop="materialName" label="危化品名称" width="100" show-overflow-tooltip></el-table-column>
          <el-table-column prop="materialTypeName" label="所属类型" show-overflow-tooltip></el-table-column>
          <el-table-column prop="inventory" label="库存" width="60" show-overflow-tooltip></el-table-column>
          <el-table-column prop="model" label="规格" width="80" show-overflow-tooltip></el-table-column>
          <el-table-column prop="maxStock" label="存储标准" show-overflow-tooltip>
            <template slot-scope="scope">
              {{ (scope.row?.minStock ?? '') + '-' + (scope.row?.maxStock ?? '')}}
            </template>
          </el-table-column>
        </el-table>
      </div>
    </div>
  </div>
</template>

<script>
import { GetWarehouseDetails } from '@/utils/spaceManage'
export default {
  name: 'warehouseDetails',
  props: {
    roomData: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      infoList: [
        {label: '库房位置', key: 'warehouseAddress'},
        {label: '管理员', key: 'manageName'},
        {label: '联系电话', key: 'managePhone'}
      ],
      count: '',
      detailsInfo: {},
      tableList: []

    }
  },
  computed: {

  },
  created() {
    console.log(1111, this.roomData)
    this.getWarehouseDetails()
  },
  methods: {
    getWarehouseDetails() {
      GetWarehouseDetails({warehouseId: this.roomData.deviceId}).then(res => {
        if (res.data.code == 200) {
          this.count = res.data.data.count
          this.detailsInfo = res.data.data.wareHouse
          this.tableList = res.data.data.detail
        }
      })
    }
  }
}

</script>

<style lang="scss" scoped>
@import '../style/module.scss';
.warehouseDetails {
  padding: 5px 0px 0px 0px;
  .module-header {
    padding-left: 30px;
    padding-right: 10px;
    width: 100%;
    background: url("@/assets/images/qhdsys/bg-bt.png") no-repeat;
    background-size: contain;
  }
  .info-header {
    padding: 6px 8px;
    line-height: 22px;
    background: rgba(133,145,206,0.15);
    .info-header-text {
      font-weight: 500;
      font-size: 15px;
      color: #FFFFFF;
    }
  }
  .info-list {
    padding: 0px 16px;
    .info-list-item {
      display: flex;
      .item-label {
        font-weight: 400;
        font-size: 14px;
        color: #B0E3FA;
        line-height: 30px;
        width: 85px;
      }
      .item-value {
        font-weight: 400;
        font-size: 14px;
        color: #FFFFFF;
        line-height: 30px;
      }
    }
  }
  .title-right {
    font-size: 14px;
    align-items: center;
  }
  .el-dropdown-link {
    font-size: 14px;
    font-weight: 300;
    color: #ffffff;
    line-height: 16px;
    .el-icon-arrow-down {
      font-size: 12px;
    }
  }
  .viewMore {
    display: inline-block;
    width: 24px;
    height: 24px;
    margin-left: 10px;
    background: url('@/assets/images/qhdsys/bg-gd.png') no-repeat center / 100% 100%;
  }
}
</style>
