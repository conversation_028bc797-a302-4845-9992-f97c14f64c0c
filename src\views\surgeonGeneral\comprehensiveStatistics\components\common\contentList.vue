<template>
  <div class="card">
    <div class="contentTitle">
      <span>{{ chartName }}</span>
      <span>
        <slot name="filter"></slot>
      </span>
    </div>
    <div v-if="list.length" class="contentList">
      <div
        v-for="(item, index) in list"
        :key="index"
        class="card-content-item"
        :class="{ active: index == curActive }"
        @click="itemClick(item, index)"
      >
        <div class="name" :style="{ width: option.nameWidth || '155px' }">
          {{ item[option.name || "name"] }}
        </div>
        <div class="progress" :style="`width: ${progressWidth}`">
          <div
            class="checkd"
            :style="{
              width: getProgressWidth(item[option.value || 'value']) + '%',
            }"
          >
            <div class="point"></div>
          </div>
        </div>
        <div class="value" :style="{ width: option.valueWidth || '120px' }">
          {{ item[option.value || "value"] }}{{ option.unit }}
        </div>
      </div>
    </div>
    <div v-else class="empty">暂无数据</div>
  </div>
</template>
<script>
export default {
  props: {
    chartName: {
      type: String,
      default: ''
    },
    list: {
      type: Array,
      default: () => []
    },
    option: {
      type: Object,
      default: () => ({})
    }
  },

  data() {
    return {
      curActive: ''
    }
  },
  computed: {
    maxValue() {
      if (!this.list.length) return 0
      return Math.max(
        ...this.list.map((item) => item[this.option.value || 'value'])
      )
    },
    progressWidth() {
      let nameWidth = this.option.nameWidth || '155px'
      let valueWidth = this.option.valueWidth || '120px'
      return `calc(100% - ${nameWidth} - ${valueWidth});`
    }
  },
  created() {
    this.list.forEach((el) => {
      this.$set(el, 'active', false)
    })
  },

  methods: {
    getProgressWidth(value) {
      if (this.maxValue === 0) return 0
      return ((value / this.maxValue) * 100).toFixed(2)
    },
    itemClick(item, index) {
      item.active = !item.active
      this.curActive = index
    }
  }
}
</script>
<style lang="scss" scoped>
.card {
  width: 100%;
  height: 100%;
  .contentTitle {
    padding: 5px;
    color: #88ddf5;
    display: flex;
    justify-content: space-between;
    margin-bottom: 8px;
  }
  .contentList {
    height: calc(100% - 34px);
    overflow-y: auto;
    /* Webkit 浏览器滚动条样式 */
    &::-webkit-scrollbar {
      width: 4px; /* 设置滚动条宽度为 3.2px */
    }

    &::-webkit-scrollbar-track {
      background: transparent; /* 轨道背景透明 */
    }

    &::-webkit-scrollbar-thumb {
      background-color: #88ddf5; /* 设置滚动块颜色 */
      border-radius: 2px; /* 简单圆角，匹配细滚动条 */
    }

    &::-webkit-scrollbar-thumb:hover {
      background-color: #88ddf5; /* 悬停时保持颜色不变 */
    }

    /* 隐藏滚动条两端的箭头按钮 */
    &::-webkit-scrollbar-button {
      display: none;
    }

    /* Firefox 浏览器滚动条样式 */
    // scrollbar-width: thin;
    // scrollbar-color: #88ddf5 transparent;
    .card-content-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      color: #fff;
      padding: 5px;
      cursor: pointer;
      .name {
        width: 155px;
        text-align: right;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
      .progress {
        width: calc(100% - 100px - 120px);
        height: 6px;
        background: rgba($color: #fff, $alpha: 0.2);
        margin: 0 10px;
        border-radius: 2px;
        .checkd {
          background: #8BDDF5;
          height: 100%;
          position: relative;

        }
      }
      .value {
        width: 120px;
        color: #fff;
      }
      &.active {
        color: #ffdc83;
        .progress {
          .checkd {
            background: #ffdc83;
            .point {
              background: url("~@/assets/images/qhdsys/list-checked-point-bg.png")
                no-repeat;
              background-size: 100% 100%;
            }
          }
        }
        .value {
          color: #ffdc83;
        }
      }
    }
  }
  .empty {
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    color: #999;
  }
}
</style>
