<!--
 * @Author: ycw
 * @Date: 2020-05-29 14:31:47
 * @LastEditors: ycw
 * @LastEditTime: 2020-06-03 12:01:44
 * @Description:
-->
<template>
  <div class="sino-header">
    <!-- <div class="sino-header-box header-icon">
      <span
        :class="[
          'icon',
          'iconfont',
          $store.state.isCollapse ? 'icon-switch' : ''
        ]"
        @click="switchMenu($store.state.isCollapse)"
        >&#xe62e;</span
      >
    </div> -->
    <div class="sino-header-box header-title">
      <span>欢迎使用iHCRS智慧医院后勤综合监管系统</span>
    </div>
    <div class="sino-time-box">
      <span class="month">{{ month }}<span style="font-size: 12px; color: #a1c5ff">月</span></span>
      <span class="month" style="margin: 0 5px">{{ date }}<span style="font-size: 12px; color: #a1c5ff">日</span></span>
      <span class="time">{{ hours + ':' + minutes }}</span>
    </div>
    <div class="sino-header-box header-person">
      <el-dropdown @command="handleCommand">
        <span class="el-dropdown-link">
          <span class="user">管理员</span>
          <span class="icon iconfont">&#xe626;</span>
        </span>
        <el-dropdown-menu slot="dropdown">
          <el-dropdown-item command="exit">退出登录</el-dropdown-item>
          <el-dropdown-item command="theme-black">主题1</el-dropdown-item>
          <el-dropdown-item command="theme-blue">主题2</el-dropdown-item>
          <el-dropdown-item command="theme-green">主题3</el-dropdown-item>
        </el-dropdown-menu>
      </el-dropdown>
    </div>
  </div>
</template>

<script>
export default {
  data() {
    return {
      month: '',
      date: '',
      hours: '',
      minutes: '',
      interval: ''
    }
  },
  methods: {
    handleCommand(command) {
      if (command === 'exit') {
        this.$confirm('确定退出登录?', '提示')
          .then((res) => {
            this.$store.commit('outLogin')
            this.$router.push('/login')
          })
          .catch((res) => {})
      } else {
        window.document.documentElement.setAttribute('data-theme', command)
      }
    },
    switchMenu(state) {
      this.$store.commit('setCollapse', !state)
    },
    getNowTime() {
      const myDate = new Date()
      const months = myDate.getMonth() + 1
      this.month = months >= 10 ? months : '0' + months // 获取当前月份(0-11,0代表1月)
      this.date = myDate.getDate() >= 10 ? myDate.getDate() : '0' + myDate.getDate() // 获取当前日(1-31)
      this.hours = myDate.getHours() >= 10 ? myDate.getHours() : '0' + myDate.getHours() // 获取当前小时数(0-23)
      this.minutes = myDate.getMinutes() >= 10 ? myDate.getMinutes() : '0' + myDate.getMinutes() // 获取当前分钟数(0-59)
    }
  },
  mounted() {
    this.getNowTime()
    this.interval = setInterval(() => {
      this.getNowTime()
    }, 3000)
  },
  beforeDestroy() {
    clearInterval(this.interval)
  }
}
</script>

<style lang="scss" scoped>
@import '@assets/sino-ui/common/mixin';
::v-deep .el-dropdown {
  color: #a1c5ff;
}
.sino-header {
  height: 100%;
  display: flex;
  align-items: center;
  .sino-header-box {
    height: 100%;
    display: inline-block;
    vertical-align: top;
    line-height: 64px;
  }
  .header-icon {
    width: 6%;
    color: rgba(132, 141, 161, 1);
    .icon {
      font-size: 22px;
      cursor: pointer;
    }
  }
  .header-title {
    width: 64%;
    // color: rgba(15, 24, 45, 1);
    color: #a1c5ff;
    font-size: 18px;
    font-weight: 600;
    span::before {
      content: ' ';
      display: inline-block;
      width: 24px;
      height: 24px;
      background-image: url('../../assets/images/header/hi.png');
      background-repeat: no-repeat;
      background-size: 100% 100%;
      margin-right: 12px;
      vertical-align: middle;
    }
  }
  .sino-time-box {
    width: 10%;
    display: flex;
    align-items: center;
    margin-left: 50%;
    .month {
      width: 58px;
      height: 36px;
      background-image: url('../../assets/images/demo/month.png');
      background-repeat: no-repeat;
      background-size: 100% 100%;
      text-align: center;
      line-height: 36px;
      color: #fff;
    }
    .time {
      width: 75px;
      height: 36px;
      background-image: url('../../assets/images/demo/time.png');
      background-repeat: no-repeat;
      background-size: 100% 100%;
      text-align: center;
      line-height: 36px;
      color: #fff;
    }
  }
  .header-person {
    width: 30%;
    font-size: 14px;
    font-weight: bold;
    text-align: right;
    // color: rgba(89, 89, 89, 1);
    color: #a1c5ff;
    cursor: pointer;
    .user::before {
      content: ' ';
      display: inline-block;
      width: 24px;
      height: 24px;
      background-image: url('../../assets/images/header/user.png');
      background-repeat: no-repeat;
      background-size: 100% 100%;
      margin-right: 8px;
      vertical-align: sub;
      color: #a1c5ff;
    }
    .icon {
      font-size: 12px;
      margin-left: 3px;
    }
  }
}

.icon-switch {
  display: inline-block;
  @include rotate(180deg);
}
</style>
