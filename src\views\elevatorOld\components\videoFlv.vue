<!--
 * @Author: hedd
 * @Date: 2023-07-04 19:16:12
 * @LastEditTime: 2023-07-27 10:45:10
 * @FilePath: \ihcrs_client_iframe_elevator\src\views\elevator\components\videoFlv.vue
 * @Description:
-->
<template>
  <video id="videoElement" ref="videoElement" muted controls autoplay width="100%" height="100%"></video>
</template>
<script>
import flvjs from 'flv.js'
export default {
  name: 'videoFlv',
  props: {
    url: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      flvPlayer: null,
      vloading: false
    }
  },
  mounted() {
    // this.initFlvjs(this.url);
  },
  destroyed() {
    this.destroyFlvjs()
  },
  methods: {
    initFlvjs(url) {
      this.destroyFlvjs()
      if (flvjs.isSupported()) {
        var videoElement = document.getElementById('videoElement')
        this.flvPlayer = flvjs.createPlayer(
          {
            type: 'flv',
            isLive: true,
            url: url,
            hasAudio: false // 禁止音频
          }
        )
        this.flvPlayer.attachMediaElement(videoElement)
        this.flvPlayer.load()
        this.flvPlayer.play()
      }
    },
    destroyFlvjs() {
      if (this.flvPlayer) {
        this.flvPlayer.pause()
        this.flvPlayer.unload()
        this.flvPlayer.detachMediaElement()
        this.flvPlayer.destroy()
        this.flvPlayer = null
      }
    }
  }
}
</script>
