<template>
  <el-dialog

    v-dialogDrag
    :modal="false"
    :visible="dialogShow"
    custom-class="mainDialog main"
    append-to-body
    :close-on-click-modal="false"
    :before-close="closeDialog"
    class="all-table-componentList"
  >
    <EmergencyDetail
      v-if="dialogShow"
      :alarmId="alarmId"
      :isView="false"
    >
    </EmergencyDetail>
  </el-dialog>
</template>

<script>
import EmergencyDetail from '@/views/normalMode/leftScreen/EmergencyDisposalNew/components/EmergencyDetail.vue'
export default {
  name: 'alarmDetailNew',
  components: {
    EmergencyDetail
  },
  props: {
    dialogShow: {
      type: Boolean,
      default: false
    },
    alarmId: {
      type: String,
      default: ''
    }
  },
  data() {
    return {

    }
  },
  computed: {

  },
  created() {

  },
  methods: {
    // 取消按钮
    closeDialog() {
      this.$emit('close')
    }
  }
}

</script>

<style lang="scss" scoped>
:deep(.mainDialog) {
  width: 80%;
  height: 84vh;
  margin-top: 6vh !important;
  // border: 1px solid #5996f9;
  background-color: transparent !important;
  pointer-events: auto;
  background-image: url("@/assets/images/table-bg.png");
  background-repeat: no-repeat;
  background-size: 100% 100%;
  box-shadow: none;
  .el-dialog__headerbtn {
    transform: translateX(-36px);
    width: 25px;
    height: 25px;
    background-image: url("@/assets/images/close.png");
    background-repeat: no-repeat;
    background-size: 100% 100%;
    .el-dialog__close::before {
      display: none;
    }
  }
  .dialog-title {
    display: inline-block;
    width: 100%;
    text-align: center;
    transform: translateY(-6px);
    color: #cbdeed;
  }
  .dialog-title::before {
    display: none;
  }

  .el-dialog__header {
    background-color: transparent;
  }
  .el-dialog__body {
    padding: 0;
    height: calc(100% - 60px);
    max-height: fit-content;
    overflow-y: hidden;
    background-color: transparent;
    padding: 0px 80px;
    .dialog-content {
      width: 100%;
      height: 100%;
      background-color: transparent;

      .el-table {
        background-color: transparent;
        border: 1px solid #202d4c;
        .el-table__header-wrapper {
          background-color: transparent !important;
        }
        .el-table__body-wrapper {
          background-color: transparent !important;
        }
        tr {
          background-color: transparent !important;
        }
        td {
          background-color: transparent !important;
        }
      }
    }
  }
}
</style>
