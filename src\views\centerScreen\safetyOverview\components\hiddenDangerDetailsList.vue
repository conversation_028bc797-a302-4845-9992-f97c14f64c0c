<template>
  <div class="form-detail">
    <div class="reserve-scorll">
      <div class="reserve-plan" v-for="(item, index) in detailsInfo" :key="index">
        <div class="plan-title">
          <div class="color-box"><i class="el-icon-time"></i></div>
          <div class="linear-g" @click="collectEvent('item', index)">
            <span class="linear-g-span1">{{item.title}}</span>
            <span>{{item.createRecordTime}}</span>
            <i style="display: inline-block" :ref="'itemright' + index" class="el-icon-arrow-right title-icon"></i>
            <i v-show="false" :ref="'itemdown' + index" class="el-icon-arrow-down title-icon"></i>
          </div>
        </div>
        <!-- 基本信息 -->
        <div class="plan-content plan-content-line" v-if="item.optType == '0'">
          <ul class="item-row">
            <li class="width30">
              <span class="li-first-span">检查类型</span><span class="li-last-span">{{item.checkName}}</span>
            </li>
            <li class="width60">
              <span class="li-first-span">隐患区域</span><span class="li-last-span">{{item.questionAddress}}</span>
            </li>
          </ul>
          <TransitionHeight :ref="'item' + index" :heigtRef="'heigtBox' + index">
            <ul class="item-row">
              <li class="width30">
                <span class="li-first-span">隐患分类</span><span class="li-last-span">{{item.questionDetailType}}</span>
              </li>
              <li class="width30">
                <span class="li-first-span">隐患等级</span><span class="li-last-span">{{item.riskName}}</span>
              </li>
              <li class="width30">
                <span class="li-first-span">反馈部门</span><span class="li-last-span">{{item.createByDeptName}}</span>
              </li>
            </ul>
            <ul class="item-row">
              <li class="width30">
                <span class="li-first-span">反馈人</span><span class="li-last-span">{{item.createPersonName}}</span>
              </li>
              <li class="width30">
                <span class="li-first-span">反馈人电话</span><span class="li-last-span">{{item.createPersonPhone}}</span>
              </li>
              <li class="width30">
                <span class="li-first-span">要求整改完成时限</span><span class="li-last-span">{{item.rectificationPlanTime}}</span>
              </li>
            </ul>
            <ul class="item-row">
              <li class="width30">
                <span class="li-first-span">反馈时间</span><span class="li-last-span">{{item.createTime}}</span>
              </li>
            </ul>
            <ul class="item-row">
              <li class="width90">
                <span class="li-first-span">语音</span>
                <div id="audio-box">
                  <audio v-if="item.questionVoiceUrl" controls>
                    <source :src="item.questionVoiceUrl" />
                  </audio>
                  <span v-else>当前任务未上传录音</span>
                </div>
                <span></span>
              </li>
            </ul>
            <ul class="item-row">
              <li class="width90">
                <span class="li-first-span">隐患描述</span><span class="li-last-span">{{item.questionContent}}</span>
              </li>
            </ul>
            <ul class="item-row">
              <li class="width90">
                <span class="li-first-span">附件</span>
                <p class="li-last-span">
                  <span v-for="(img, index) in item.questionAttachmentList" :key="index">
                    <el-image style="width: 100px; height: 100px; margin-right: 15px" :src="img" :preview-src-list="[img]"> </el-image>
                  </span>
                </p>
                <span></span>
              </li>
            </ul>
          </TransitionHeight>
        </div>
        <!-- 指派信息 -->
        <div class="plan-content plan-content-line" v-if="item.optType == '1'">
          <ul class="item-row">
            <li class="width30">
              <span class="li-first-span">部门</span><span class="li-last-span">{{item.dutyDeptName}}</span>
            </li>
            <li class="width30">
              <span class="li-first-span">人员</span><span class="li-last-span">{{item.claimPersonName}}</span>
            </li>
            <li class="width30">
              <span class="li-first-span">电话</span><span class="li-last-span">{{item.claimPersonPhone}}</span>
            </li>
          </ul>
          <TransitionHeight :ref="'item' + index" :heigtRef="'heigtBox' + index">
            <ul class="item-row">
              <li class="width30">
                <span class="li-first-span">时间</span><span class="li-last-span">{{item.claimTime}}</span>
              </li>
              <li class="width30">
                <span class="li-first-span">超时状态</span><span class="li-last-span">{{item.overTimeState}}</span>
              </li>
              <li class="width30">
                <span class="li-first-span">超时时长</span><span class="li-last-span">{{item.overTimeOften}}</span>
              </li>
            </ul>
          </TransitionHeight>
        </div>
        <!-- 整改信息 -->
        <div class="plan-content plan-content-line" v-if="item.optType == '2'">
          <ul class="item-row">
            <li class="width30">
              <span class="li-first-span">部门</span><span class="li-last-span">{{item.handleDeptName}}</span>
            </li>
            <li class="width30">
              <span class="li-first-span">人员</span><span class="li-last-span">{{item.handlePersonName}}</span>
            </li>
            <li class="width30">
              <span class="li-first-span">电话</span><span class="li-last-span">{{item.handlePersonPhone}}</span>
            </li>
          </ul>
          <TransitionHeight :ref="'item' + index" :heigtRef="'heigtBox' + index">
            <ul class="item-row">
              <li class="width30">
                <span class="li-first-span">时间</span><span class="li-last-span">{{item.handleTime}}</span>
              </li>
            </ul>
            <ul class="item-row">
              <li class="width90">
                <span class="li-first-span">隐患描述</span><span class="li-last-span">{{item.finishContent}}</span>
              </li>
            </ul>
            <ul class="item-row">
              <li class="width90">
                <span class="li-first-span">语音</span>
                <div id="audio-box">
                  <audio v-if="item.finishVoiceUrl" controls>
                    <source :src="item.questionVoiceUrl" />
                  </audio>
                  <span v-else>当前任务未上传录音</span>
                </div>
                <span></span>
              </li>
            </ul>
            <ul class="item-row">
              <li class="width90">
                <span class="li-first-span">附件</span>
                <p class="li-last-span">
                  <span v-for="(img, index) in item.finishPicUrlList" :key="index">
                    <el-image style="width: 100px; height: 100px; margin-right: 15px" :src="img" :preview-src-list="[img]"> </el-image>
                  </span>
                </p>
                <span></span>
              </li>
            </ul>
          </TransitionHeight>
        </div>
        <!-- 挂账信息 -->
        <div class="plan-content plan-content-line" v-if="item.optType == '3'">
          <ul class="item-row">
            <li class="width30">
              <span class="li-first-span">挂帐时间</span><span class="li-last-span">{{item.paymentTime}}</span>
            </li>
            <li class="width30">
              <span class="li-first-span">预计解决时间</span><span class="li-last-span">{{item.estimateSolutionTime}}</span>
            </li>
            <li class="width30">
              <span class="li-first-span">管控部门</span><span class="li-last-span">{{item.recordDeptName}}</span>
            </li>
          </ul>
          <TransitionHeight :ref="'item' + index" :heigtRef="'heigtBox' + index">
            <ul class="item-row">
              <li class="width90">
                <span class="li-first-span">挂帐原因描述</span><span class="li-last-span">{{item.paymentReason}}</span>
              </li>
            </ul>
            <ul class="item-row">
              <li class="width90">
                <span class="li-first-span">现阶段防控措施</span><span class="li-last-span">{{item.riskControl}}</span>
              </li>
            </ul>
            <ul class="item-row">
              <li class="width90">
                <span class="li-first-span">整改计划</span><span class="li-last-span">{{item.rectificationPlan}}</span>
              </li>
            </ul>
          </TransitionHeight>
        </div>
        <!-- 审核信息 -->
        <div class="plan-content plan-content-line" v-if="item.optType == '14'">
          <ul class="item-row">
            <li class="width30">
              <span class="li-first-span">部门</span><span class="li-last-span">{{item.recordDeptName}}</span>
            </li>
            <li class="width30">
              <span class="li-first-span">人员</span><span class="li-last-span">{{item.currentAuditPersonName}}</span>
            </li>
            <li class="width30">
              <span class="li-first-span">电话</span><span class="li-last-span">{{item.currentAuditPersonPhone}}</span>
            </li>
          </ul>
          <TransitionHeight :ref="'item' + index" :heigtRef="'heigtBox' + index">
            <ul class="item-row">
              <li class="width30">
                <span class="li-first-span">时间</span><span class="li-last-span">{{item.checkTime}}</span>
              </li>
              <li class="width30">
                <span class="li-first-span">审核结论</span><span class="li-last-span">{{item.auditResult}}</span>
              </li>
              <li class="width30">
                <span class="li-first-span">满意度</span><span class="li-last-span">
                  <el-rate v-model="item.starLevel" disabled text-color="#fff"> </el-rate>
                </span>
              </li>
            </ul>
            <ul class="item-row">
              <li class="width90">
                <span class="li-first-span">审核评价</span><span class="li-last-span">{{item.currentAuditEvaluate}}</span>
              </li>
            </ul>
          </TransitionHeight>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import TransitionHeight from '@/components/common/transitionHeight.vue'
import { GetHistoryFollowInfo } from '@/utils/centerScreenApi'
export default {
  name: 'hiddenDangerDetailsList',
  components: {
    TransitionHeight
  },
  props: {
    rowData: {
      type: Object,
      default: function () {
        return {}
      }
    }
  },
  data() {
    return {
      detailsInfo: []
    }
  },
  created() {
    // if (this.$route.query.id) {
    //   this.getHistoryFollowInfo(this.$route.query.id)
    // }
  },
  mounted() {
    // console.log(this.rowData)
    this.getHistoryFollowInfo(this.rowData.id)
    // try {
    //   window.chrome.webview.hostObjects.sync.bridge.GetSwichFloorIsShow(true)
    //   window.chrome.webview.hostObjects.sync.bridge.ShowDialog(true)
    // } catch (error) {}
  },
  beforeDestroy() {
    // try {
    //   window.chrome.webview.hostObjects.sync.bridge.GetSwichFloorIsShow(false)
    //   window.chrome.webview.hostObjects.sync.bridge.ShowDialog(false)
    // } catch (error) {}
    this.detailsInfo = []
  },
  methods: {
    getHistoryFollowInfo(id) {
      GetHistoryFollowInfo({ id }).then(res => {
        if (res.data.code === '200') {
          this.detailsInfo = res.data.data
        }
      })
    },
    // 展开关闭事件
    collectEvent(box, i) {
      if (this.$refs[box + 'right' + i][0].style.display === 'inline-block') {
        this.$refs[box + 'right' + i][0].style.display = 'none'
        this.$refs[box + 'down' + i][0].style.display = 'inline-block'
        this.$refs[box + i][0].show = true
      } else {
        this.$refs[box + 'right' + i][0].style.display = 'inline-block'
        this.$refs[box + 'down' + i][0].style.display = 'none'
        this.$refs[box + i][0].show = false
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.form-detail {
  height: calc(100% - 30px);
  width: 100%;
  padding: 10px 25px;
  overflow-y: scroll;
  box-sizing: border-box;
  .reserve-scorll {
    width: 100%;
    height: calc(100% - 50px);
    overflow-y: auto;
    box-sizing: border-box;
    padding-right: 15px;
  }
  .reserve-plan {
    width: 100%;
    position: relative;
    // height: inherit;
    .plan-title {
      display: flex;
      width: 100%;
      height: 30px;
      .color-box {
        height: 1.25rem;
        width: 1.25rem;
        color: #e6cf9d;
        line-height: 1.25rem;
        text-align: center;
        border-radius: 3px;
        font-size: 16px;
        margin: auto;
      }
      .linear-g {
        margin-left: 2px;
        width: calc(100% - 22px);
        height: 100%;
        background: #263057;
        font-size: 13px;
        line-height: 30px;
        font-family: PingFangSC-Medium;
        color: #ffe3a6;
        border-radius: 6px;
        cursor: pointer;
        .linear-g-span1 {
          margin: 0 15px;
          font-weight: 600;
        }
        .title-icon {
          float: right;
          line-height: 30px;
          margin-right: 5px;
        }
      }
    }
    .plan-content {
      width: calc(100% - 33px);
      // border-left: 1px solid #303758;
      margin-left: 11px;
      // padding: 20px 0px 20px 20px;
      color: #b5bacb;
      font-size: 13px;
      .item-row {
        width: 100%;
        display: flex;
        padding: 20px 0px 20px 30px;
        box-sizing: border-box;
        .width30 {
          width: 30%;
        }
        .width45 {
          width: 45%;
        }
        .width60 {
          width: 60%;
          display: flex;
        }
        .width90 {
          width: 90%;
          display: flex;
        }
        ::v-deep .el-image__error,
        ::v-deep .el-image__placeholder {
          background: center;
        }
        .li-first-span {
          display: inline-block;
          width: 120px;
          // margin-right: 20px;
          font-size: 14px;
          font-family: PingFang-SC-Medium, PingFang-SC;
          font-weight: 500;
          color: #7eaef9;
        }
        .li-last-span {
          display: inline-block;
          width: calc(100% - 120px);
          font-size: 14px;
          font-family: PingFang-SC-Medium, PingFang-SC;
          font-weight: 500;
          color: #ffffff;
          // align-items: center;
        }
        #audio-box {
          display: flex;
        }
        #audio-box > audio {
          width: 260px;
          height: 30px;
        }
        #audio-box > a {
          width: 40px;
          text-align: center;
          background-color: #2cc7c5;
          height: 35px;
          line-height: 35px;
          color: #fff;
          border-radius: 5px;
          margin-left: 10px;
        }
      }
      .show-content {
        width: 100%;
      }
    }
    .plan-content-line {
      border-left: 1px solid #303758;
    }
  }
}
</style>
