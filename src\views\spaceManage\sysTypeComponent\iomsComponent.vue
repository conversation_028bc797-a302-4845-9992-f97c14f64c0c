<template>
  <div class="iomsComponent">
    <div class="work-order-type">
      <div class=" box-title2">
        <div><span></span>{{ roomData.title }} - 工单统计分析</div>
      </div>
      <div class="search-data">
        <el-dropdown trigger="click" @command="dataTypeCommand">
          <span class="el-dropdown-link"> {{ dataTypeList.find((v) => v.value == btnType)?.name ?? '' }} <i class="el-icon-caret-bottom"></i> </span>
          <el-dropdown-menu slot="dropdown">
            <el-dropdown-item v-for="item in dataTypeList" :key="item.value" :command="item.value" :class="{ isBjxl: btnType == item.value }">{{ item.name }}</el-dropdown-item>
          </el-dropdown-menu>
        </el-dropdown>
        <el-date-picker
          v-model="value2"
          class="datePickerInput"
          popper-class="date-style"
          unlink-panels
          type="daterange"
          value-format="yyyy-MM-dd HH:mm:ss"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          :default-time="['00:00:00', '23:59:59']"
          :picker-options="{
            firstDayOfWeek: 1
          }"
          @change="dataPickerValChange()"
          @focus="setWPFBgShow()"
          @blur="setWPFBgHide()"
        >
        </el-date-picker>
      </div>
      <div class="box-content">
        <div v-if="workOdertTypeShow" class="center-center">暂无数据</div>
        <div v-else style="width: 100%; height: 100%" class="dd">
          <div class="tj-item">
            <div class="u-item">
              <span>{{ btnType == 5 ? '' : dataTypeList[btnType - 1].name }}工单数&ensp;</span>
              <span class="yello-color">{{ orderStatistics?.allOrderCount }}&ensp;</span>
              <span>件</span>
            </div>
            <div class="d-item">
              <span>同比</span>
              <img v-if="orderStatistics?.allOrderTB?.startsWith('-')" src="@/assets/images/xia.png" />
              <img v-else-if="orderStatistics?.allOrderTB?.startsWith('0%')" src="@/assets/images/flat.png" />
              <img v-else src="@/assets/images/shang.png" />
              <span :class="orderStatistics?.allOrderTB?.startsWith('-') ? 'green-color' : orderStatistics?.allOrderTB?.startsWith('0%') ? '' : 'red-color'">{{ orderStatistics?.allOrderTB }}</span>
            </div>
          </div>
          <div class="tj-item">
            <div class="u-item">
              <span>未完成数&ensp;</span>
              <span class="yello-color">{{ orderStatistics?.unfinishCount }}&ensp;</span>
              <span>件</span>
            </div>
            <div class="d-item">
              <span>同比</span>
              <img v-if="orderStatistics?.unfinishOrderTB?.startsWith('-')" src="@/assets/images/xia.png" />
              <img v-else-if="orderStatistics?.unfinishOrderTB?.startsWith('0%')" src="@/assets/images/flat.png" />
              <img v-else src="@/assets/images/shang.png" />
              <span :class="orderStatistics?.unfinishOrderTB?.startsWith('-') ? 'green-color' : orderStatistics?.unfinishOrderTB?.startsWith('0%') ? '' : 'red-color'">{{
                orderStatistics?.unfinishOrderTB
              }}</span>
            </div>
          </div>
          <div class="tj-item">
            <div class="u-item">
              <span>挂单数&ensp;</span>
              <span class="yello-color">{{ orderStatistics?.placeCount }}&ensp;</span>
              <span>件</span>
            </div>
            <div class="d-item">
              <span>同比</span>
              <img v-if="orderStatistics?.placeOrderTB?.startsWith('-')" src="@/assets/images/xia.png" />
              <img v-else-if="orderStatistics?.placeOrderTB?.startsWith('0%')" src="@/assets/images/flat.png" />
              <img v-else src="@/assets/images/shang.png" />
              <span :class="orderStatistics?.placeOrderTB?.startsWith('-') ? 'green-color' : orderStatistics?.placeOrderTB?.startsWith('0%') ? '' : 'red-color'">{{
                orderStatistics?.placeOrderTB
              }}</span>
            </div>
          </div>
          <div class="tj-item">
            <div class="u-item">
              <span>超时工单数&ensp;</span>
              <span class="yello-color">{{ orderStatistics?.overTimeCount }}&ensp;</span>
              <span>件</span>
            </div>
            <div class="d-item">
              <span>同比</span>
              <img v-if="orderStatistics?.overTimeTB?.startsWith('-')" src="@/assets/images/xia.png" />
              <img v-else-if="orderStatistics?.overTimeTB?.startsWith('0%')" src="@/assets/images/flat.png" />
              <img v-else src="@/assets/images/shang.png" />
              <span :class="orderStatistics?.overTimeTB?.startsWith('-') ? 'green-color' : orderStatistics?.overTimeTB?.startsWith('0%') ? '' : 'red-color'">{{ orderStatistics?.overTimeTB }}</span>
            </div>
          </div>
          <!-- <div id="workOdertTypeEcharts"></div>
          <div class="pie_background"></div> -->
        </div>
      </div>
    </div>
    <div class="work-order-trend">
      <div class="box-title">
        <div><span></span>工单趋势（件）</div>
        <span></span>
      </div>
      <div class="box-content">
        <div v-if="workOderTrendShow" class="center-center">暂无数据</div>
        <div v-else style="width: 100%; height: 100%">
          <div class="checkbox-contain">
            <el-checkbox v-model="checkAll" style="margin-right: 30px;" :indeterminate="isIndeterminate" @change="handleCheckAllChange">全部工单</el-checkbox>
            <el-checkbox-group v-model="checkList" fill="#52FFFC">
              <!-- <el-checkbox label="全部工单"></el-checkbox> -->
              <el-checkbox label="综合维修"></el-checkbox>
              <el-checkbox label="应急保洁"></el-checkbox>
            </el-checkbox-group>
            <img src="@/assets/images/checkbox_add.png" @click="switchPanel" />
            <div v-show="showPanel" class="panel">
              <el-checkbox-group v-model="checkList" fill="#52FFFC">
                <el-checkbox v-for="item in workOrderTypeList" :key="item.workTypeCode" :label="item.workTypeName"></el-checkbox>
              </el-checkbox-group>
            </div>
          </div>
          <div id="workOdertTrendEcharts"></div>
        </div>
      </div>
    </div>
    <div class="work-order-paiming">
      <div class="box-title box-title2">
        <div><span></span>工单排名</div>
        <div class="leixing">
          <div :class="sortType == '1' ? 'active-type' : ''" @click="changeSortType('1')">类型</div>
          <div :class="sortType == '2' ? 'active-type' : ''" @click="changeSortType('2')">部门</div>
          <div v-if="roomData.ssmType < 5" :class="sortType == '3' ? 'active-type' : ''" @click="changeSortType('3')">建筑</div>
        </div>
      </div>
      <div v-if="sortType == '1'" class="box-content box-content2 scroll-box">
        <div v-for="(item, index) in sortTypeData" :key="index" class="jindutiao" @click="filterWorkOrderStatus(item.workTypeCode)">
          <el-tooltip slot="title-right" popper-class="tooltip" effect="dark" placement="bottom" :content="item.workTypeName" :disabled="item.workTypeName.length <= 4">
            <span class="progress-label">{{ item.workTypeName }}</span>
          </el-tooltip>
          <el-progress stroke-width="8" :percentage="item.workNum" :color="[rankingSelect == item.workTypeCode ? '#f6de88' : '#0A84FF']" :show-text="false" define-back-color="#394257"></el-progress>
          <span class="progress-label">{{ item.workNum }}个</span>
        </div>
      </div>
      <div v-if="sortType == '2'" class="box-content box-content2 scroll-box">
        <div v-for="(item, index) in sortDeptData" :key="index" class="jindutiao" @click="filterWorkOrderStatus(item.deptCode)">
          <el-tooltip slot="title-right" popper-class="tooltip" effect="dark" placement="bottom" :content="item.deptName" :disabled="item.deptName.length <= 4">
            <span class="progress-label">{{ item.deptName }}</span>
          </el-tooltip>
          <el-progress stroke-width="8" :percentage="item.workCount" :color="[rankingSelect == item.deptCode ? '#f6de88' : '#0A84FF']" :show-text="false" define-back-color="#394257"></el-progress>
          <span class="progress-label">{{ item.workCount }}个</span>
        </div>
      </div>
      <div v-if="sortType == '3'" class="box-content box-content2 scroll-box">
        <div v-for="(item, index) in sortBuildData" :key="index" class="jindutiao" @click="filterWorkOrderStatus(item.spaceId)">
          <el-tooltip slot="title-right" popper-class="tooltip" effect="dark" placement="bottom" :content="item.gridName" :disabled="item.gridName.length <= 4">
            <span class="progress-label">{{ item.gridName }}</span>
          </el-tooltip>
          <el-progress stroke-width="8" :percentage="item.allCount" :color="[rankingSelect == item.spaceId ? '#f6de88' : '#0A84FF']" :show-text="false" define-back-color="#394257"></el-progress>
          <span class="progress-label">{{ item.allCount }}个</span>
        </div>
      </div>
    </div>
    <div class="work-order-table">
      <div class="box-title box-title2">
        <div><span></span>工单状态</div>
        <div class="icon-box">
          <!-- <span class="filter"></span>
          <span @click="allTableChange('ioms')" class="order-more"></span> -->
          <img src="../../../assets/images/filter.png" class="filter" @click="showCheckGroup = !showCheckGroup" />
          <div v-show="showCheckGroup" v-scrollbarHover class="panel-s">
            <el-checkbox-group v-model="checkList2" fill="#52FFFC" @change="checkBoxChanged">
              <el-checkbox v-for="item in flowList" :key="item.value" :label="item.label"></el-checkbox>
            </el-checkbox-group>
          </div>
          <img src="../../../assets/images/order_more.png" class="order-more" @click="allTableChange('ioms')" />
        </div>
      </div>
      <div class="box-content box-content2" style="padding: 5px 0px">
        <div class="statistics-top">
          <div v-for="(item, index) in statisticsData" :key="index">
            <p>{{ item.name }}</p>
            <p class="green-font">{{ item.value + (item?.unit ?? '') }}</p>
          </div>
        </div>
        <el-table
          v-loading="tableLoading"
          class="table-center-transfer"
          :data="tableData"
          height="calc(100% - 50px)"
          :cell-style="{ padding: ' 8px', backgroundColor: 'transparent', border: 'none', padding: '3px' }"
          :header-cell-style="{ background: '#8591CE26!important', color: '#8BDDF5FF', padding: '4px 8px', fontWeight: 'bold' }"
          style="width: 100%"
          element-loading-background="rgba(0, 0, 0, 0.2)"
          @row-dblclick="selectConfigRowData"
        >
          <el-table-column v-for="(column, index) in tableColumn" :key="index" :prop="column.prop" align="center" :label="column.label" :min-width="column.minWidth" show-overflow-tooltip>
            <template slot-scope="scope">
              <table-render v-if="column.render" :sc="scope" :row="scope.row" :render="column.render"></table-render>
              <div v-else-if="!column.formatter">
                {{ scope.row[column.prop] }}
              </div>
              <div v-else>
                {{ column.formatter(scope.row) }}
              </div>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </div>
    <template v-if="allTableComponentListShow">
      <allTableComponentList ref="allTableComponentList" :dialogData="tableCompenentData" :dialogShow="allTableComponentListShow" @configCloseDialog="configCloseTableDialog"></allTableComponentList>
    </template>
    <template v-if="workOrderDetailCenterShow">
      <el-dialog v-dialogDrag :modal="false" :visible.sync="workOrderDetailCenterShow" custom-class="detailDialog main" :close-on-click-modal="false" :before-close="workOrderDetailCloseDialog">
        <template slot="title">
          <span class="dialog-title">综合维修（{{ iomsDetailObj.flowtype }}）</span>
        </template>
        <workOrderDetailList :rowData="iomsDetailObj" sourceScreen="center" />
      </el-dialog>
    </template>
  </div>
</template>

<script lang="jsx">
import moment from 'moment'
import allTableComponentList from '../components/allTableComponentList.vue'
import workOrderDetailList from '@views/normalMode/rightScreen/components/workOrderDetailList.vue'
import { dialogTypeList, iomsWorkOrderParams } from '@/assets/common/dict.js'
import tableRender from '../components/tableRender.vue'
import icon_5 from '@/assets/images/icon-5.png'
import icon_3 from '@/assets/images/icon-3.png'
import icon_7 from '@/assets/images/icon-7.png'
import icon_6 from '@/assets/images/icon-6.png'
import icon_2 from '@/assets/images/icon-2.png'
import {
  GetWorkOrderListBySpaceRuiAn,
  GerReckonCount,
  getWorkOrderTypeStatisticsBySpaceRuiAn,
  getWorkOrderCountStatisticsBySpaceRuiAn,
  getWorkOrderCount,
  getWorkTypeCountInfo,
  getWorkDeptCountInfo,
  getWorkOrderNumInfo,
  getWorkOrkerChange
} from '@/utils/spaceManage'
import * as echarts from 'echarts'
moment.locale('zh-cn', { week: { dow: 1 } })
export default {
  name: 'iomsComponent',
  components: {
    'table-render': tableRender,
    allTableComponentList,
    workOrderDetailList
  },
  props: {
    roomData: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      icon_5,
      icon_3,
      icon_7,
      icon_6,
      icon_2,
      workOdertTypeShow: false, // 工单类型统计echarts是否显示
      workOderTrendShow: false, // 近12个月工单走势echarts是否显示
      allTableComponentListShow: false, // 一站式弹窗显示
      tableCompenentData: {}, // 一站式弹窗数据
      workOrderDetailCenterShow: false, // 工单详情弹窗显示
      iomsDetailObj: {}, // 工单详情弹窗数据
      dialogTypeList: dialogTypeList, // 过滤选中弹窗类型
      statisticsData: [
        {
          name: '工单总数',
          value: 0,
          field: 'all'
        },
        {
          name: '完成率',
          value: 0,
          field: 'completionRate'
        },
        {
          name: '平均响应',
          value: 0,
          // unit: '%',
          field: 'response'
        },
        {
          name: '平均完工',
          value: 0,
          // unit: '分钟',
          field: 'finishTime'
        }
        // {
        //   name: '平均完工',
        //   value: 0,
        //   // unit: '秒',
        //   field: 'finishTime'
        // }
      ], // 统计数据
      tableData: [], // 表格数据
      tableColumn: [
        {
          prop: 'workTypeName',
          label: '工单类型'
        },
        {
          prop: 'itemServiceName',
          label: '服务事项'
        },
        {
          prop: 'designatePersonName',
          label: '服务人员'
        },
        {
          prop: 'flowtype',
          label: '工单状态',
          render: (h, row) => {
            return (
              <div style="display: flex;justify-content: center;">
                {row.row.flowcode == '5' && (
                  <div class="status-box">
                    <img class="table-icon" src={icon_5} />
                    <span style="color:#61E29D">{row.row.flowtype}</span>
                  </div>
                )}
                {row.row.flowcode == '3' && (
                  <div class="status-box">
                    <img class="table-icon" src={icon_3} />
                    <span style="color:#3CC1FF">{row.row.flowtype}</span>
                  </div>
                )}
                {(row.row.flowcode == '7' || row.row.flowcode == '1' || row.row.flowcode == '4') && (
                  <div class="status-box">
                    <img class="table-icon" src={icon_7} />
                    <span style="color:#D25F00">{row.row.flowtype}</span>
                  </div>
                )}
                {row.row.flowcode == '6' && (
                  <div class="status-box">
                    <img class="table-icon" src={icon_6} />
                    <span style="color:#86909C">{row.row.flowtype}</span>
                  </div>
                )}
                {row.row.flowcode == '2' && (
                  <div class="status-box">
                    <img class="table-icon" src={icon_2} />
                    <span style="color:#FF2D55">{row.row.flowtype}</span>
                  </div>
                )}
                {row.row.flowcode != '1' &&
                  row.row.flowcode != '2' &&
                  row.row.flowcode != '3' &&
                  row.row.flowcode != '4' &&
                  row.row.flowcode != '5' &&
                  row.row.flowcode != '6' &&
                  row.row.flowcode != '7' && (
                  <div class="status-box">
                    <span>{row.row.flowtype}</span>
                  </div>
                )}
              </div>
            )
          }
        }
      ], // 表格列
      tableLoading: false, // 表格加载
      iomsWorkOrderParams: iomsWorkOrderParams, // 查询列表参数
      checkList: ['巡检报修', '后勤设备', '随手拍', '综合维修', '应急保洁', '运送服务', '订餐服务', '综合服务', '后勤投诉', '医疗设备', '巡检自修', '巡检整改'],
      dataTypeList: [
        { value: 1, name: '今日' },
        { value: 2, name: '本周' },
        { value: 3, name: '本月' },
        { value: 4, name: '本年' },
        { value: 5, name: '自定义' }
      ],
      value2: [moment().startOf('week').format('YYYY-MM-DD') + ' 00:00:00', moment().endOf('week').format('YYYY-MM-DD') + ' 23:59:59'],
      regex: /^(\d{8})\s(.*)/,
      btnType: '2',
      orderStatistics: {},
      sortTypeData: '',
      sortType: '1',
      sortDeptData: '',
      sortBuildData: '',
      startTime: '',
      endTime: '',
      showPanel: false,
      workOrderTypeList: [
        // {
        //   workTypeName: '全部工单',
        //   workTypeCode: ''
        // },
        {
          workTypeName: '巡检报修',
          workTypeCode: '10'
        },
        {
          workTypeName: '后勤设备',
          workTypeCode: '17'
        },
        {
          workTypeName: '随手拍',
          workTypeCode: '11'
        },
        {
          workTypeName: '综合维修',
          workTypeCode: '1'
        },
        {
          workTypeName: '应急保洁',
          workTypeCode: '2'
        },
        {
          workTypeName: '运送服务',
          workTypeCode: '3'
        },
        {
          workTypeName: '订餐服务',
          workTypeCode: '4'
        },
        {
          workTypeName: '综合服务',
          workTypeCode: '6'
        },
        {
          workTypeName: '后勤投诉',
          workTypeCode: '5'
        },
        {
          workTypeName: '医疗设备',
          workTypeCode: '7'
        },
        {
          workTypeName: '巡检自修',
          workTypeCode: '8'
        },
        {
          workTypeName: '巡检整改',
          workTypeCode: '9'
        }
      ],
      checkedCodeList: ['10', '17', '11', '1', '2', '3', '4', '6', '5', '7', '8', '9'],
      checkAll: true,
      isIndeterminate: false,
      checkedCodeList2: [],
      showCheckGroup: false,
      checkList2: ['全部'],
      flowList: [
        {
          label: '全部',
          value: ''
        },
        {
          label: '未受理',
          value: '1'
        },
        {
          label: '暂存',
          value: '7'
        },
        {
          label: '未派工',
          value: '2'
        },
        {
          label: '已派工',
          value: '3'
        },
        {
          label: '已挂单',
          value: '4'
        },
        {
          label: '已完工',
          value: '5'
        },
        {
          label: '已取消',
          value: '6'
        }
      ],
      rankingSelect: ''
    }
  },
  computed: {
    btnTypeText() {
      switch (this.btnType) {
        case '1':
          return '今天'
        case '2':
          return '本周'
        case '3':
          return '本月'
        case '4':
          return '本年'
        case '5':
          return '自定义'
        default:
          return '今天'
      }
    }
  },
  watch: {
    btnType: {
      handler(val) {
        if (val == 5) {
          return
        }
        this.getWorkOrderCount()
        this.getWorkTypeCountInfo()
        this.getWorkDeptCountInfo()
        this.getWorkOrderNumInfo()
        this.getWorkOderTrend()
        const ssmCodeList = this.roomData.ssmCodes?.split(',') ?? []
        let params = {}
        if (ssmCodeList.length) {
          params = {
            region: ssmCodeList[2] ?? '',
            buliding: ssmCodeList[3] ?? '',
            storey: ssmCodeList[4] ?? '',
            room: ssmCodeList[5] ?? ''
          }
        }
        this.getWorkOrderListBySpaceRuiAn(params)
        this.gerReckonCount(params)
      },
      immediate: true
    },
    roomData: {
      handler(val) {
        // 不根据房间过滤数据
        // if (val.ssmType >= 6) return
        this.getWorkOrderCount()
        this.getWorkTypeCountInfo()
        this.getWorkDeptCountInfo()
        this.getWorkOrderNumInfo()
        this.getWorkOderTrend()
        const ssmCodeList = this.roomData.ssmCodes.split(',')
        let params = {}
        if (ssmCodeList.length) {
          params = {
            region: ssmCodeList[2] ?? '',
            buliding: ssmCodeList[3] ?? '',
            storey: ssmCodeList[4] ?? '',
            room: ssmCodeList[5] ?? ''
          }
        }
        this.getWorkOrderListBySpaceRuiAn(params)
        this.gerReckonCount(params)
        this.giveWpf()
      },
      deep: true
    },
    checkList: {
      handler(val) {
        // 遍历workOrderTypeList 根据checkList的name,获取选中的code
        const codeList = []
        this.workOrderTypeList.forEach((item) => {
          this.checkList.forEach((e) => {
            if (item.workTypeName == e) {
              codeList.push(item.workTypeCode)
            }
          })
        })
        this.checkAll = codeList.length === this.workOrderTypeList.length
        this.isIndeterminate = codeList.length > 0 && codeList.length < this.workOrderTypeList.length
        this.checkedCodeList = codeList
        this.getWorkOderTrend()
      },
      deep: true
    }
  },
  mounted() {
    // 服务工单动态
    const ssmCodeList = this.roomData.ssmCodes.split(',')
    let params = {}
    if (ssmCodeList.length) {
      params = {
        region: ssmCodeList[2] ?? '',
        buliding: ssmCodeList[3] ?? '',
        storey: ssmCodeList[4] ?? '',
        room: ssmCodeList[5] ?? ''
      }
    }
    this.getWorkOderType(params)
    this.getWorkOderTrend(params)
    // this.getWorkOrderListBySpaceRuiAn(params)
    // this.gerReckonCount(params)
    this.giveWpf()
    // this.getWorkOrderCount()
  },
  methods: {
    // 点击工单排名筛选工单状态
    filterWorkOrderStatus(val) {
      console.log(val)
      this.rankingSelect = this.rankingSelect == val ? '' : val
      const ssmCodeList = this.roomData.ssmCodes.split(',')
      let params = {}
      if (ssmCodeList.length) {
        params = {
          region: ssmCodeList[2] ?? '',
          buliding: ssmCodeList[3] ?? '',
          storey: ssmCodeList[4] ?? '',
          room: ssmCodeList[5] ?? ''
        }
      }
      this.getWorkOrderListBySpaceRuiAn(params)
      this.gerReckonCount(params)
      // 建筑点击 并且在院区模式的时候 告诉wpf切换视角
      if (this.sortType == '3' && this.roomData.ssmType < 4) {
        try {
          window.chrome.webview.hostObjects.sync.bridge.WebSwitchingPerspective(this.rankingSelect)
        } catch (error) {}
      }
    },
    handleCheckAllChange(val) {
      this.checkList = val ? this.workOrderTypeList.map((item) => item.workTypeName) : []
      this.isIndeterminate = false
    },
    setWPFBgShow() {
      window.chrome.webview.hostObjects.sync.bridge.DateMask(true)
    },
    setWPFBgHide() {
      window.chrome.webview.hostObjects.sync.bridge.DateMask(false)
    },
    // 时间类型切换
    dataTypeCommand(val) {
      const date = {
        1: [moment().format('YYYY-MM-DD') + ' 00:00:00', moment().format('YYYY-MM-DD') + ' 23:59:59'],
        2: [moment().startOf('week').format('YYYY-MM-DD') + ' 00:00:00', moment().endOf('week').format('YYYY-MM-DD') + ' 23:59:59'],
        3: [moment().startOf('month').format('YYYY-MM-DD') + ' 00:00:00', moment().endOf('month').format('YYYY-MM-DD') + ' 23:59:59'],
        4: [moment().startOf('year').format('YYYY-MM-DD') + ' 00:00:00', moment().endOf('year').format('YYYY-MM-DD') + ' 23:59:59'],
        5: []
      }
      this.value2 = date[val]
      this.startTime = this.value2[0]
      this.endTime = this.value2[1]
      this.btnType = val
      if (val != 5) {
        this.giveWpf()
      }
    },
    switchPanel() {
      this.showPanel = !this.showPanel
    },
    dataPickerValChange() {
      this.btnType = '5'
      this.startTime = this.value2[0]
      this.endTime = this.value2[1]
      this.getWorkOrderCount()
      this.getWorkTypeCountInfo()
      this.getWorkDeptCountInfo()
      this.getWorkOrderNumInfo()
      this.getWorkOderTrend()
      const ssmCodeList = this.roomData.ssmCodes.split(',')
      let params = {}
      if (ssmCodeList.length) {
        params = {
          region: ssmCodeList[2] ?? '',
          buliding: ssmCodeList[3] ?? '',
          storey: ssmCodeList[4] ?? '',
          room: ssmCodeList[5] ?? ''
        }
      }
      this.getWorkOrderListBySpaceRuiAn(params)
      this.gerReckonCount(params)
      this.giveWpf()
    },
    giveWpf() {
      this.$emit('sendWpfData', {
        type: 'ioms',
        params: {
          btnType: this.btnType,
          startTime: this.startTime,
          endTime: this.endTime
        }
      })
    },
    changeSortType(sortType) {
      this.sortType = sortType
      if (sortType == '1') {
        this.getWorkTypeCountInfo()
      }
      if (sortType == '2') {
        this.getWorkDeptCountInfo()
      }
      if (sortType == '3') {
        this.getWorkOrderNumInfo()
      }
    },
    getWorkOrderCount() {
      getWorkOrderCount({ btnType: this.btnType, startTime: this.startTime, endTime: this.endTime, spatialId: this.roomData.ssmCodes.split(',').at(-1) }).then((res) => {
        if (res.data.code == '200') {
          this.orderStatistics = res.data.data
        }
      })
    },
    getWorkTypeCountInfo() {
      getWorkTypeCountInfo({
        btnType: this.btnType,
        startTime: this.startTime,
        endTime: this.endTime,
        spatialId: this.roomData.ssmCodes.split(',').at(-1)
      }).then((res) => {
        if (res.data.code == '200') {
          this.sortTypeData = res.data.data
          // 按照工单数量排序
          this.sortTypeData.sort((a, b) => {
            return b.workNum - a.workNum
          })
          // this.filterWorkOrderStatus(this.sortTypeData.length ? this.sortTypeData[0].workTypeCode : '')
        }
      })
    },
    getWorkDeptCountInfo() {
      getWorkDeptCountInfo({
        btnType: this.btnType,
        startTime: this.startTime,
        endTime: this.endTime,
        spatialId: this.roomData.ssmCodes.split(',')[this.roomData.ssmCodes.split(',').length - 1]
      }).then((res) => {
        if (res.data.code == '200') {
          if (res.data.data) {
            const data = res.data.data
            this.sortDeptData = data.map((e) => {
              e.deptName = e.deptName || '未命名'
              const match = e.deptName.match(this.regex)
              e.deptName = match ? match[2].trimLeft() : e.deptName
              return e
            })
            // 按照工单数量排序
            this.sortDeptData.sort((a, b) => {
              return b.workCount - a.workCount
            })
            // this.filterWorkOrderStatus(this.sortDeptData.length ? this.sortDeptData[0].deptCode : '')
          }
        }
      })
    },
    getWorkOrderNumInfo() {
      getWorkOrderNumInfo({ btnType: this.btnType, spatialId: this.roomData.ssmCodes.split(',')[this.roomData.ssmCodes.split(',').length - 1], startTime: this.startTime, endTime: this.endTime }).then(
        (res) => {
          if (res.data.code == '200') {
            this.sortBuildData = res.data.data.list
            // this.filterWorkOrderStatus(this.sortBuildData.length ? this.sortBuildData[0].spaceId : '')
          }
        }
      )
    },
    // 工单类型统计
    getWorkOderType(params) {
      getWorkOrderTypeStatisticsBySpaceRuiAn(params).then((res) => {
        const data = res.data
        if (data.code === '200') {
          if (data.data.list.length) {
            this.workOdertTypeShow = false
            const arr = data.data.list
            const total = data.data.totalPrice ? data.data.totalPrice : 0
            this.$nextTick(() => {
              this.workOderTypeEchart(arr, total)
            })
          } else {
            this.workOdertTypeShow = true
          }
        }
      })
    },
    // 工单类型统计echarts
    workOderTypeEchart(arr, total) {
      const getchart = echarts.init(document.getElementById('workOdertTypeEcharts'))
      const data = []
      var color = ['rgba(31, 250, 255, 0.3)', 'rgba(255, 227, 166, 0.28)', 'rgba(147, 130, 255, 0.26)', 'rgba(0, 248, 114, 0.24)', 'rgba(46, 119, 251, 0.3)']
      var borderColor = ['rgba(31, 250, 255, 1)', 'rgba(255, 227, 166, 1)', 'rgba(147, 130, 255, 1)', 'rgba(0, 248, 114, 1)', 'rgba(46, 119, 251, 1)']
      const xdata = Array.from(arr, ({ workTypeName }) => workTypeName)
      for (var i = 0; i < arr.length; i++) {
        const randomRgbColor = this.$tools.randomRgbColor('array')
        data.push({
          name: arr[i].workTypeName,
          value: arr[i].workNum,
          itemStyle: {
            normal: {
              borderWidth: 1,
              shadowBlur: 200,
              borderColor: borderColor[i] ?? randomRgbColor[1],
              color: color[i] ?? randomRgbColor[0],
              label: {
                normal: { show: true }
              }
            }
          }
        })
      }
      var seriesObj = [
        {
          name: '',
          type: 'pie',
          clockWise: false,
          center: ['25%', '50%'],
          radius: ['47%', '70%'],
          // hoverAnimation: false,
          label: {
            normal: {
              show: false,
              formatter: '{b}\n {c}\n {d}%',
              textStyle: {
                fontSize: '12',
                color: '#fff'
              }
            },
            emphasis: {
              show: false,
              // position: 'outside',
              // alignTo: 'edge',
              // margin: 10,
              textStyle: {
                fontSize: '12',
                color: '#fff'
              }
            }
          },
          data: data
        }
      ]
      const option = {
        backgroundColor: '',
        tooltip: {
          trigger: 'item',
          axisPointer: {
            lineStyle: {
              color: '#FFE3A6'
            }
          },
          backgroundColor: '#0C2269',
          borderColor: '#5B617C',
          textStyle: {
            color: '##FFF'
          }
        },
        title: {
          text: '维修总成本',
          subtext: '  ' + total + '元',
          textStyle: {
            color: '#CFE1F7',
            fontSize: 12
          },
          subtextStyle: {
            fontSize: 16,
            color: '#FFFFFF'
          },
          x: '15%',
          y: '38%'
        },
        legend: {
          orient: 'vertical',
          // x: 'right',
          top: 'center',
          right: '0%',
          bottom: '0%',
          data: xdata,
          type: 'scroll',
          itemWidth: 8,
          itemHeight: 8,
          itemGap: 16,
          textStyle: {
            //  fontSize: 18,//字体大小
            color: '#B3C2DD' //  字体颜色
          },
          formatter: function (name) {
            var oa = option.series[0].data
            for (var i = 0; i < option.series[0].data.length; i++) {
              if (name === oa[i].name) {
                return ' ' + name + '  ' + oa[i].value + ' 个     '
              }
            }
          }
        },
        series: seriesObj
      }
      getchart.clear()
      getchart.setOption(option)
      // 随着屏幕大小调节图表
      window.addEventListener('resize', () => {
        getchart.resize()
      })
    },
    // 工单走势
    getWorkOderTrend() {
      const params = {
        btnType: this.btnType,
        spatialId: this.roomData.ssmCodes.split(',')[this.roomData.ssmCodes.split(',').length - 1],
        startTime: this.startTime,
        endTime: this.endTime,
        workTypeCode: this.checkedCodeList.join(',')
      }
      getWorkOrkerChange(params).then((res) => {
        if (res.data.code == '200') {
          if (this.btnType == '2' || this.btnType == '3' || this.btnType == '5') {
            res.data.data.xAxisData.forEach((item, index) => {
              res.data.data.xAxisData[index] = item.substring(5)
            })
          }
          this.workOderTrendEchart(res.data.data.xAxisData, res.data.data.seriesData)
        }
      })
      // getWorkOrderCountStatisticsBySpaceRuiAn(params).then((res) => {
      //   const data = res.data
      //   if (data.code === '200') {
      //     const arr = data.data.list
      //     if (arr.length) {
      //       this.workOderTrendShow = false
      //       const dateArr = []
      //       const echartsData = []
      //       // 获取日期 以及 获取工单类型每日对应数据
      //       for (let i = 0; i < arr.length; i++) {
      //         if (dateArr.indexOf(arr[i].createDate) === -1) {
      //           dateArr.push(arr[i].createDate)
      //         }
      //         if (!echartsData.length || !echartsData.some((e) => e.name === arr[i].workTypeName)) {
      //           echartsData.push({
      //             name: arr[i].workTypeName,
      //             value: [arr[i].workOrderNum]
      //           })
      //         } else {
      //           for (let j = 0; j < echartsData.length; j++) {
      //             if (echartsData[j].name === arr[i].workTypeName) {
      //               echartsData[j].value.push(arr[i].workOrderNum)
      //               break
      //             }
      //           }
      //         }
      //       }
      //       this.$nextTick(() => {
      //         console.log(dateArr, echartsData)
      //         this.workOderTrendEchart(dateArr, echartsData)
      //       })
      //     } else {
      //       this.workOderTrendShow = true
      //     }
      //   }
      // })
      // const nameList = ['2022-01', '2022-02', '2022-03', '2022-04', '2022-05', '2022-06', '2022-07', '2022-08', '2022-09', '2022-10']
      // // 遍历nameList 生成30-80随机数
      // const chartdata = []
      // nameList.forEach((item, index) => {
      //   chartdata.push({
      //     value: Math.floor(Math.random() * (80 - 30 + 1) + 30),
      //     value2: Math.floor(Math.random() * (80 - 30 + 1) + 30)
      //   })
      // })
      // this.$nextTick(() => {
      //   this.workOderTrendEchart(nameList, chartdata)
      // })
    },
    // 工单走势echarts
    workOderTrendEchart(dateArr, echartsData) {
      console.log(dateArr, echartsData, 'echartsDataechartsDataechartsDataechartsDataechartsDataechartsDataechartsDataechartsDataechartsData')
      const getchart = echarts.init(document.getElementById('workOdertTrendEcharts'))
      var color = ['rgba(31, 250, 255, 0.3)', 'rgba(255, 227, 166, 0.28)', 'rgba(147, 130, 255, 0.26)', 'rgba(0, 248, 114, 0.24)', 'rgba(46, 119, 251, 0.3)']
      var borderColor = ['rgba(31, 250, 255, 1)', 'rgba(255, 227, 166, 1)', 'rgba(147, 130, 255, 1)', 'rgba(0, 248, 114, 1)', 'rgba(46, 119, 251, 1)']
      const seriesObj = []
      echartsData.forEach((item, index) => {
        const randomRgbColor = this.$tools.randomRgbColor('array')
        seriesObj.push({
          name: item.name,
          type: 'line',
          smooth: false,
          showAllSymbol: true,
          symbol: 'circle',
          symbolSize: 0,
          itemStyle: {
            color: '#3eb5dd',
            borderColor: '#f1f1f1',
            borderWidth: 1
          },
          lineStyle: {
            normal: {
              width: 2,
              color: borderColor[index] ?? randomRgbColor[1]
            }
          },
          // areaStyle: {
          //   // 区域填充样式
          //   normal: {
          //     color: new echarts.graphic.LinearGradient(
          //       0,
          //       0,
          //       0,
          //       1,
          //       [
          //         {
          //           offset: 0,
          //           color: borderColor[index] ?? randomRgbColor[1]
          //         },
          //         {
          //           offset: 1,
          //           color: color[index] ?? randomRgbColor[0]
          //         }
          //       ],
          //       false
          //     )
          //   }
          // },
          data: item.value
        })
      })
      const dataZoom = [
        {
          type: 'slider', // 滑动条型数据区域缩放组件
          realtime: true, // 拖动时，是否实时更新系列的视图。如果设置为 false，则只在拖拽结束的时候更新。
          // start: 0, //数据窗口范围的起始百分比。范围是：0 ~ 100。表示 0% ~ 100%。
          // end: 10,  // 数据窗口范围的结束百分比。范围是：0 ~ 100
          height: 4,
          endValue: 5, // 数据窗口范围的结束数值。如果设置了 dataZoom-inside.end 则 endValue 失效
          fillerColor: 'rgba(17, 100, 210, 0.4)', // 滚动条颜色
          borderColor: 'rgba(17, 100, 210, 0.1)',
          handleSize: 0, // 两边手柄尺寸
          showDetail: false, // 拖拽时是否展示滚动条两侧的文字
          bottom: '8%', // 组件离容器上侧的距离
          zoomLock: true // 是否只平移不缩放
          // moveOnMouseMove:true, //开启鼠标移动窗口平移
          // zoomOnMouseWheel :true, //开启鼠标滚轮缩放
        },
        {
          type: 'inside', // 内置型数据区域缩放组件
          // start: 0,
          // end: 10,
          endValue: 11, // 最多12个
          zoomOnMouseWheel: false, // 关闭鼠标滚轮缩放
          moveOnMouseWheel: true, // 开启鼠标滚轮窗口平移
          moveOnMouseMove: true // 开启鼠标移动窗口平移
        }
      ]
      const option = {
        tooltip: {
          trigger: 'axis',
          backgroundColor: '#0D2169',
          borderColor: '#727382',
          textStyle: {
            color: '#fff'
          },
          axisPointer: {
            // 坐标轴指示器，坐标轴触发有效
            type: 'shadow' // 默认为直线，可选为：'line' | 'shadow'
          }
        },
        dataZoom: dataZoom,
        legend: {
          show: false,
          data: Array.from(echartsData, ({ name }) => name),
          type: 'scroll',
          orient: 'horizontal',
          left: 'right',
          top: '0',
          itemGap: 16,
          textStyle: {
            color: '#B3C2DD'
          }
        },
        grid: {
          left: '2%',
          right: '0%',
          bottom: '10%',
          top: '18%',
          width: '90%',
          containLabel: true
        },
        xAxis: {
          type: 'category',
          axisLine: {
            show: true,
            lineStyle: {
              color: '#878EA9',
              width: 1,
              type: 'solid'
            }
          },
          axisTick: {
            show: false
          },
          axisLabel: {
            interval: 0
          },
          boundaryGap: false,
          data: dateArr
        },
        yAxis: {
          type: 'value',
          axisLabel: {
            show: true,
            textStyle: {
              color: '#878EA9'
            }
          },
          axisLine: {
            lineStyle: {
              type: 'solid',
              color: '#3862B7',
              width: '1'
            }
          },
          splitLine: {
            lineStyle: {
              color: '#314A89',
              width: 1,
              type: 'solid'
            }
          },
          axisTick: {
            show: false
          }
        },
        series: seriesObj
      }
      getchart.clear()
      getchart.setOption(option)
      // 随着屏幕大小调节图表
      window.addEventListener('resize', () => {
        getchart.resize()
      })
    },
    // 服务工单动态
    gerReckonCount(params) {
      let showTimeType = ''
      if (this.btnType == '1') {
        showTimeType = '1'
      }
      if (this.btnType == '2') {
        showTimeType = '6'
      }
      if (this.btnType == '3') {
        showTimeType = '2'
      }
      if (this.btnType == '4') {
        showTimeType = '3'
      }
      if (this.btnType == '5') {
        showTimeType = '4'
      }
      const param = this.sortType == 1 ? { workTypeCode: this.rankingSelect } : this.sortType == 2 ? { sourcesDept: this.rankingSelect } : { buliding: this.rankingSelect }
      GerReckonCount({ ...this.iomsWorkOrderParams, ...params, showTimeType, startTime: this.startTime, endTime: this.endTime, flowcode: this.checkedCodeList2.join(','), ...param }).then((res) => {
        if (res.data.success && res.data.body.data) {
          const data = res.data.body.data
          this.statisticsData.forEach((item) => {
            item.value = data[item.field]
          })
        }
      })
    },
    // 获取维修工单
    getWorkOrderListBySpaceRuiAn(params) {
      this.tableLoading = true
      let showTimeType = ''
      if (this.btnType == '1') {
        showTimeType = '1'
      }
      if (this.btnType == '2') {
        showTimeType = '6'
      }
      if (this.btnType == '3') {
        showTimeType = '2'
      }
      if (this.btnType == '4') {
        showTimeType = '3'
      }
      if (this.btnType == '5') {
        showTimeType = '4'
      }
      const param = this.sortType == 1 ? { workTypeCode: this.rankingSelect } : this.sortType == 2 ? { sourcesDept: this.rankingSelect } : { buliding: this.rankingSelect }
      GetWorkOrderListBySpaceRuiAn({
        ...this.iomsWorkOrderParams,
        pageNo: 1,
        pageSize: 20,
        ...params,
        showTimeType,
        // startTime: this.startTime,
        // endTime: this.endTime,
        sectionStartDate: this.startTime || this.value2[0],
        sectionEndDate: this.endTime || this.value2[1],
        flowcode: this.checkedCodeList2.join(','),
        ...param
      }).then((res) => {
        if (res.data.rows) {
          this.tableData = res.data.rows
        }
        this.tableLoading = false
      })
    },
    allTableChange(type) {
      this.allTableComponentListShow = true
      Object.assign(this.tableCompenentData, {
        ...this.roomData,
        ...this.dialogTypeList.find((e) => e.type === type),
        height: 'calc(100% - 230px)'
      })
    },
    configCloseTableDialog() {
      this.allTableComponentListShow = false
    },
    // 双击 以及操作点击事件
    selectConfigRowData(row) {
      this.iomsDetailObj = row
      this.workOrderDetailCenterShow = true
      try {
        window.chrome.webview.hostObjects.sync.bridge.GetSwichFloorIsShow(true)
        window.chrome.webview.hostObjects.sync.bridge.ShowDialog(true)
      } catch (error) {}
    },
    workOrderDetailCloseDialog() {
      this.workOrderDetailCenterShow = false
      try {
        window.chrome.webview.hostObjects.sync.bridge.GetSwichFloorIsShow(false)
        window.chrome.webview.hostObjects.sync.bridge.ShowDialog(false)
      } catch (error) {}
    },
    checkBoxChanged() {
      this.checkList2 = [this.checkList2[this.checkList2.length - 1]]
      this.showCheckGroup = false
      const codeList = []
      this.flowList.forEach((item) => {
        if (this.checkList2[0] == item.label) {
          codeList.push(item.value)
        }
      })
      this.checkedCodeList2 = codeList
      const ssmCodeList = this.roomData.ssmCodes.split(',')
      let params = {}
      if (ssmCodeList.length) {
        params = {
          region: ssmCodeList[2] ?? '',
          buliding: ssmCodeList[3] ?? '',
          storey: ssmCodeList[4] ?? '',
          room: ssmCodeList[5] ?? ''
        }
      }
      this.getWorkOrderListBySpaceRuiAn(params)
      this.gerReckonCount(params)
    }
  }
}
</script>

<style lang="scss" scoped>
.iomsComponent {
  width: 100%;
  height: 100%;
  .work-order-type {
    width: 100%;
    height: calc(25%);
    padding-bottom: 0px;
    .search-data {
      display: flex;
      background: rgba(133, 145, 206, 0.15);
      padding: 0px 10px;
      ::v-deep .el-dropdown {
        padding: 7px 6px;
        .el-dropdown-link {
          font-size: 14px;
          font-weight: 500;
          color: #8bddf5;
          line-height: 16px;
          position: relative;
          cursor: pointer;
        }
        .el-dropdown-link::after {
          content: '';
          position: absolute;
          width: 1px;
          height: 12px;
          background: rgba(133, 145, 206, 0.5);
          top: 50%;
          right: -6px;
          transform: translateY(-50%);
        }
      }
      ::v-deep .datePickerInput {
        flex: 1;
        padding: 8px 10px;
        height: 16px;
        box-sizing: content-box;
        background: none;
        border: none;
        .el-input__icon,
        .el-range-separator {
          line-height: 16px;
          color: #b0e3fa;
        }
        .el-range-input {
          background: none;
          color: #a4afc1;
        }
      }
    }
    .box-content {
      height: calc(100% - 62px);
    }
  }
  .work-order-trend {
    width: 100%;
    height: calc(25%);
    // border-top: 1px solid #2e4989;
    // border-bottom: 1px solid #2e4989;
    // padding: 16px 0;
    // padding-bottom: 16px;
  }
  .work-order-paiming {
    height: calc(20%);
    // margin-top: 10px;
  }
  .work-order-table {
    width: 100%;
    height: calc(30%);
    // padding-top: 10px;
    .statistics-top {
      height: 50px;
      width: 100%;
      display: flex;
      justify-content: space-around;
      // margin-bottom: 12px;
      > div {
        width: max-content;
        // width: 20%;
        height: 100%;
        // padding: 10px 0;
        box-sizing: border-box;
        display: flex;
        justify-content: space-evenly;
        flex-direction: column;
        p {
          text-align: center;
          font-size: 0.75rem;
          font-family: DIN-Bold, DIN;
          color: #ffffff;
        }
        .green-font {
          font-size: 0.975rem;
          color: #ffca64;
          font-weight: bold;
        }
      }
    }
  }
  .box-title {
    height: 30px;
    line-height: 30px;
    font-size: 14px;
    font-family: PingFangSC-Medium, PingFang SC;
    color: #ffffff;
    display: flex;
    justify-content: space-between;
    :last-child {
      // margin-right: 10px;
      font-size: 14px;
      font-family: PingFang-SC-Medium, PingFang-SC;
      color: #7eaef9;
      cursor: pointer;
    }
    > div {
      > span {
        display: none;
        // width: 15px;
        // height: 15px;
        // background: url('~@/assets/images/peace/arrow-bottom.png') no-repeat;
        // background-size: 100% 100%;
        width: 2px;
        height: 16px;
        background: #ffe3a6;
        margin-right: 6px;
        vertical-align: middle;
      }
    }
  }
  .box-content {
    width: 100%;
    height: calc(100% - 30px);
    position: relative;
    box-sizing: border-box;
    padding: 10px 10px 0px 10px;
    .pie_background {
      left: 26%;
    }
    #workOdertTypeEcharts,
    #workOdertTrendEcharts {
      position: relative;
      width: 100%;
      height: calc(100% - 20px);
      z-index: 2;
    }
    ::v-deep .el-table {
      border: none !important;
      .el-table__header-wrapper {
        .cell {
          padding-left: 0;
          padding-right: 0;
          text-align: center;
          white-space: nowrap;
        }
      }
      .el-table__body-wrapper {
        td.el-table__cell div {
          overflow: hidden;
          white-space: nowrap;
          text-overflow: ellipsis;
        }
      }
      .el-table__body {
        tr {
          background: center;
        }
        td.el-table__cell,
        th.el-table__cell.is-leaf {
          border-right: 2px solid #0a164e;
          border-bottom: 2px solid #0a164e;
          background: rgba(56, 103, 180, 0.2);
          color: #fff;
        }
      }
    }
  }
  .box-content2 {
    height: calc(100% - 44px);
  }
  ::v-deep .detailDialog {
    width: 60%;
    height: 80vh;
    margin-top: 7vh !important;
    background-color: transparent !important;
    background-image: url('@/assets/images/table-bg.png');
    background-repeat: no-repeat;
    background-size: 100% 100%;
    border: none;
    pointer-events: auto;
    box-shadow: none;
    .el-dialog__body {
      padding: 10px 50px;
      height: calc(100% - 60px);
      max-height: 80vh;
    }
    .dialog-title {
      display: inline-block;
      width: 100%;
      text-align: center;
      transform: translateY(-6px);
      color: #cbdeed;
    }
    .dialog-title::before {
      display: none;
    }
    .el-dialog__headerbtn {
      transform: translateX(-36px);
      width: 25px;
      height: 25px;
      background-image: url('@/assets/images/close.png');
      background-repeat: no-repeat;
      background-size: 100% 100%;
      .el-dialog__close::before {
        display: none;
      }
    }
  }
  ::v-deep .el-table {
    .el-table__body tr {
      cursor: pointer;
    }
  }
}
.today {
  display: flex;
  align-items: center;
  position: relative;
}
.u-item {
  margin-bottom: 10px;
}
.d-item {
  font-size: 12px;
}
.tj-item {
  width: 48%;
  display: flex;
  flex-direction: column;
  justify-content: center;
}
.red-color {
  color: #ff2d55;
}
.yello-color {
  color: #ffca64;
  font-weight: bold;
}
.dd {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
}
.d-item img {
  width: 12px;
  margin: 0 6px;
  transform: translateY(2px);
}
.green-color {
  color: #61e29d;
}
.el-checkbox-group {
  display: flex;
  justify-content: end;
}
.leixing {
  display: flex;
  align-items: center;
}
.leixing > div {
  width: 52px;
  height: 22px;
  // border: 1px solid #91D2FF;
  color: #8bddf5 !important;
  line-height: 22px;
  text-align: center;
  background-color: #213251;
  box-sizing: border-box;
}
.leixing > div:nth-child(2) {
  margin: 0 5px;
}
.jindutiao {
  cursor: pointer;
  display: flex;
  height: 30px;
  align-items: center;
}
.el-progress {
  width: 60%;
  margin: 0 16px;
  display: flex;
  align-items: center;
}
.jindutiao > span {
  font-size: 14px;
}
.box-title {
  background-image: url('@/assets/images/<EMAIL>');
  background-repeat: no-repeat;
  background-size: 100% 100%;
  padding-left: 16px;
}
.box-title2 {
  height: 44px !important;
  display: flex;
  justify-content: space-between;
  background-image: url('@/assets/images/<EMAIL>') !important;
  background-repeat: no-repeat;
  background-size: 100% 100%;
  padding: 3px 12px;
  padding-left: 40px;
  font-family: HarmonyOS Sans SC-Bold !important;
  font-size: 16px !important;
  // font-weight: bold !important;
  > div:nth-child(1) {
    display: flex;
    align-items: center;
  }
}
.box-title > div:nth-child(1) {
  font-weight: 500;
}
.box-title2 > div:nth-child(1) {
  font-weight: bold;
}
// .el-date-editor {
//   position: absolute;
//   left: 0;
//   top: 0;
//   opacity: 0;
//   width: 50px;
// }
.active-type {
  background: rgba(255, 255, 255, 0.1) linear-gradient(90deg, rgba(10, 132, 255, 0.4) 0%, rgba(10, 132, 255, 0) 100%);
  border: 1px solid #abf0ff;
}
.progress-label {
  opacity: 0.8;
  width: 15%;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
::v-deep .el-checkbox__input.is-checked span.el-checkbox__inner {
  background: url('@/assets/images/<EMAIL>') !important;
  background-size: 100% 100% !important;
}
::v-deep span.el-checkbox__inner {
  border-color: #c5fff9 !important;
}
::v-deep .el-checkbox__input.is-checked + .el-checkbox__label {
  color: #8bddf5;
}
.order-more {
  width: 24px;
  height: 24px;
  margin-right: 0 !important;
  margin-left: 8px;
}
.filter {
  width: 24px;
  height: 24px;
}
.icon-box {
  margin-right: 0;
  display: flex;
  align-items: center;
  position: relative;
}
::v-deep div.el-table th.el-table__cell.is-leaf {
  border-bottom: none;
}
::v-deep .el-table__body-wrapper .el-table__row:hover td {
  background-color: rgba(255, 255, 255, 0.3) !important;
}
.today-label {
  color: #fff;
}
.scroll-box {
  overflow: auto;
  // height: 15vh !important;
}
.checkbox-contain {
  display: flex;
  justify-content: flex-end;
  position: relative;
  img {
    width: 18px;
    height: 18px;
    margin-left: 12px;
    cursor: pointer;
  }
  .panel {
    position: absolute;
    right: 0;
    top: 20px;
    background-color: #374b79;
    padding: 8px;
    z-index: 9;
    height: 100px;
    overflow: auto;
    .el-checkbox-group {
      display: flex;
      flex-direction: column;
      align-items: flex-end;
      .el-checkbox {
        width: 100%;
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-right: 0;
        margin-bottom: 5px;
      }
    }
  }
}
::v-deep .panel-s {
  position: absolute;
  right: 0;
  top: 28px;
  background-color: #374b79;
  padding: 8px;
  z-index: 9;
  // height: 220px;
  overflow-y: auto;
  overflow-x: hidden;
  .el-checkbox-group {
    display: flex;
    flex-direction: column;
    align-items: flex-end;
    margin-right: 0 !important;
    .el-checkbox {
      width: 100%;
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-right: 0;
      margin-bottom: 5px;
      .el-checkbox__label {
        color: #a3a9c0 !important;
      }
      .el-checkbox__input {
        // display: none;
      }
    }
  }
}
.table-icon {
  width: 16px;
  margin-right: 3px;
}
.status-box {
  display: flex;
  align-items: center;
}
</style>
