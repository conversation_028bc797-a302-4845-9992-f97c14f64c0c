<!--
 * @Author: hedd
 * @Date: 2023-03-23 10:11:49
 * @LastEditTime: 2025-02-11 18:04:54
 * @FilePath: \ihcrs_client\src\App.vue
 * @Description:
-->
<template>
  <div id="app">
    <router-view v-if="isRouterAlive" />
    <audio ref="audio" controls="controls" muted="muted" hidden
           src="@/assets/images/elevator/elevatorAlarmAudio.mp3"></audio>
  </div>
</template>
<script>
import { mapGetters } from 'vuex'
import { IemcWebsocket } from '@/utils/centerScreenApi.js'
import { elevatorWebsocket } from '@/utils/elevatorApi'
let iemcLockReconnects = false
let iemcWebsocket = null
let elevatorLockReconnects = false
let elevatorIemcWebsocket = null
export default {
  name: 'App',
  provide() {
    return {
      reload: this.reload
    }
  },
  data() {
    return {
      isRouterAlive: true,
      elevatorPlayCount: 0
    }
  },
  computed: {
    ...mapGetters({
      isLogin: 'isLogin'
    })
  },
  watch: {
    '$store.state.keepAlive.list'(val) {
      process.env.NODE_ENV === 'development' && console.log(`[ keepAliveList ] ${val}`)
    },
    isLogin(flag) {
      if (flag) {
        this.getIemcWebsocket(1)
        this.getElevatorWebsocket(1)
        this.getRealLoginUser()
      }
    }
  },
  created() {
    if (!this.$store.getters.isLogin) {
      this.$store.dispatch('login')
    } else {
      this.getIemcWebsocket(1)
      this.getElevatorWebsocket(1)
    }
    // 禁止滑轮
    window.addEventListener(
      'mousewheel',
      function (event) {
        if (event.ctrlKey === true || event.metaKey) {
          event.preventDefault()
        }
      },
      { passive: false }
    )

    // firefox
    window.addEventListener(
      'DOMMouseScroll',
      function (event) {
        if (event.ctrlKey === true || event.metaKey) {
          event.preventDefault()
        }
      },
      { passive: false }
    )
    // 禁止+、- ：
    window.onload = function () {
      document.addEventListener(
        'keydown',
        function (event) {
          if (
            (event.ctrlKey === true || event.metaKey === true) &&
            (event.which === 61 || event.which === 107 || event.which === 173 || event.which === 109 || event.which === 187 || event.which === 189)
          ) {
            event.preventDefault()
          }
        },
        false
      )
    }
  },
  mounted() {
    // this.$message.warning(Math.random())
  },
  methods: {
    reload() {
      this.isRouterAlive = false
      this.$nextTick(() => {
        this.isRouterAlive = true
      })
    },
    // 获取iemcWebsocket
    getIemcWebsocket(staffId = this.$store.state.user.userInfo.user) {
      const vm = this
      const createAlarmWebSocket = function (data) {
        iemcWebsocket = IemcWebsocket(data)
        if (iemcWebsocket) {
          iemcWebsocket.onopen = function () {
            console.log('启动iemcWebsocket链接')
          }
          iemcWebsocket.onmessage = function (message) {
            const parseData = JSON.parse(message.data)
            // console.log('iemcWebsocket接收到消息：', parseData)
            if (parseData.code === 2) {
              heartCheck.reset().start()
              try {
                // 收到信息后存储到vuex中
                vm.$store.commit('setSocketIemcMsgs', JSON.stringify(parseData))
              } catch (e) { }
            }
          }
          iemcWebsocket.onclose = function (e) {
            heartCheck.reset()
            // console.log(e, 'iemcWebsocket关闭')
            //   // 监听链接关闭 关闭时重新连接
            //   reconnect(staffId)
          }
          iemcWebsocket.onerror = function () {
            // console.log('iemcWebsocket异常')
            reconnect(staffId)
          }
        }
      }
      createAlarmWebSocket(staffId)
      function reconnect(data) {
        if (iemcLockReconnects) return
        iemcLockReconnects = true
        setTimeout(function () {
          // 没连接上会一直重连，设置延迟避免请求过多
          createAlarmWebSocket(data)
          iemcLockReconnects = false
        }, 5000)
      }
      var heartCheck = {
        timeout: 540000, // 9分钟发一次心跳
        timeoutObj: null,
        serverTimeoutObj: null,
        reset: function () {
          clearTimeout(this.timeoutObj)
          clearTimeout(this.serverTimeoutObj)
          return this
        },
        start: function () {
          var self = this
          this.timeoutObj = setTimeout(function () {
            // 这里发送一个心跳，后端收到后，返回一个心跳消息，
            iemcWebsocket.send(`{code:4,toUserId: ${staffId}}`)
            self.serverTimeoutObj = setTimeout(function () {
              // 如果超过一定时间还没重置，说明后端主动断开了
              iemcWebsocket.close() // 如果onclose会执行reconnect，执行ws.close()就行了.如果直接执行reconnect 会触发onclose导致重连两次
            }, self.timeout)
          }, this.timeout)
        }
      }
    },
    // 获取电梯Websocket
    getElevatorWebsocket(staffId = this.$store.state.user.userInfo.user) {
      const vm = this
      const createAlarmWebSocket = function (data) {
        elevatorIemcWebsocket = elevatorWebsocket(data)
        if (elevatorIemcWebsocket) {
          elevatorIemcWebsocket.onopen = function () {
            console.log('启动elevatorWebsocket链接')
          }
          elevatorIemcWebsocket.onmessage = function (message) {
            // console.log('elevatorWebsocket接收到消息：', JSON.parse(message.data))
            if (JSON.parse(message.data).surveyEntityCode) {
              heartCheck.reset().start()
              try {
                const itemData = JSON.parse(message.data)
                // 电梯执行报警相关逻辑
                vm.executeElevatorEvent(itemData)
                // 收到信息后存储到vuex中
                vm.$store.commit('setSocketElevatorMsgs', JSON.stringify(itemData))
              } catch (e) { }
            }
          }
          elevatorIemcWebsocket.onclose = function (e) {
            heartCheck.reset()
            // console.log(e, 'elevatorIemcWebsocket关闭')
            //   // 监听链接关闭 关闭时重新连接
            //   reconnect(staffId)
          }
          elevatorIemcWebsocket.onerror = function () {
            // console.log('elevatorWebsocket异常')
            reconnect(staffId)
          }
        }
      }
      createAlarmWebSocket(staffId)
      function reconnect(data) {
        if (elevatorLockReconnects) return
        elevatorLockReconnects = true
        setTimeout(function () {
          // 没连接上会一直重连，设置延迟避免请求过多
          createAlarmWebSocket(data)
          elevatorLockReconnects = false
        }, 5000)
      }
      var heartCheck = {
        timeout: 540000, // 9分钟发一次心跳
        timeoutObj: null,
        serverTimeoutObj: null,
        reset: function () {
          clearTimeout(this.timeoutObj)
          clearTimeout(this.serverTimeoutObj)
          return this
        },
        start: function () {
          var self = this
          this.timeoutObj = setTimeout(function () {
            // 这里发送一个心跳，后端收到后，返回一个心跳消息，
            elevatorIemcWebsocket.send(`{code:4,toUserId: ${staffId}}`)
            self.serverTimeoutObj = setTimeout(function () {
              // 如果超过一定时间还没重置，说明后端主动断开了
              elevatorIemcWebsocket.close() // 如果onclose会执行reconnect，执行ws.close()就行了.如果直接执行reconnect 会触发onclose导致重连两次
            }, self.timeout)
          }, this.timeout)
        }
      }
    },
    // 电梯执行报警相关逻辑
    executeElevatorEvent(data) {
      const openVioce = localStorage.getItem('elevatorOpenAlarmVoice') // 初始化为null 只有关闭'false'才执行业务
      // const allowPlayRoutes = ['/elevatorMenu/elevatorMonitor', '/elevatorMenu/elevatorOverview', '/elevatorMenu/elevatorAlarmCenter', '/elevatorMenu/elevatorAnalysis']
      // 判断当前消息是否为报警消息
      if (data.isAlarm === 1 && openVioce !== 'false') {
        // if (data.isAlarm === 1 && allowPlayRoutes.includes(this.$route.path) && openVioce !== 'false') {
        this.elevatorPlayCount++
        this.playElevatorAlarmAudio()
      }
    },
    playElevatorAlarmAudio() {
      this.$refs.audio.play()
      this.$refs.audio.onended = () => {
        this.elevatorPlayCount-- // 点击次数递减
        if (this.elevatorPlayCount > 0) {
          this.playElevatorAlarmAudio() // 继续播放声音
        }
      }
    },
    /** 获取真实登录账号 */
    getRealLoginUser() {
      Promise.resolve()
        // 发起获取请求
        .then(() => window.chrome.webview.hostObjects.sync.bridge.GettingLoginInformation())
        .then(() => {
          // 发起成功后添加消息订阅
          window.chrome.webview.addEventListener('message', (event) => {
            const data = JSON.parse(event.data)
            // 仅过滤type是登录人信息的消息类型
            if (data.type === 'realLoginInfo') {
              console.log('realLoginInfo: ====> ', data)
              // 如果两个登录人一致，不用再次登录
              if (window.IHCRS_ACCOUNT.username === data.username) {
                return
              }
              // 更换新的登录账号
              window.IHCRS_ACCOUNT = {
                username: data.userName,
                password: data.passWord
              }
              // 派发登录方法，以更新store数据
              this.$store.dispatch('login')
            }
          })
        })
        .catch((err) => console.error('获取真实登录人信息失败：', err))
    }
  }
}
</script>
<style lang="scss">
#app {
  height: 100vh;
}

.el-popover {
  background-color: rgba(2, 19, 82, 0.9) !important;
  color: #fff !important;
  border: 1px solid #5996f9 !important;
}

.new-select.el-select-dropdown {
  background-color: #374b79;
}

.date-style.el-picker-panel {
  .el-date-range-picker__time-header {
    border-color: #5b699f;

    .el-icon-arrow-right {
      color: rgba(255, 255, 255, 0.8);
    }
    .el-date-range-picker__editors-wrap {
      .el-date-range-picker__time-picker-wrap {
        .el-time-panel {
          .el-time-panel__content {
            .el-time-spinner {
              .el-time-spinner__wrapper {
                .el-scrollbar__wrap {
                  .el-scrollbar__view {
                    .active {
                      color: #fff;
                    }
                  }
                }
              }
            }
          }
        }
      }
    }

  }
  .el-scrollbar {
    .el-scrollbar__view {
      .time-select-item {
        color: #fff;
        &:hover {
          background-color: transparent;
        }
      }
      .selected {
        color: #409EFF;
      }
      .disabled {
        color: #606266;
        cursor: no-drop;
      }
    }
  }
  .el-picker-panel__footer {
    background-color: #374b79;
    border-top-color: #5b699f;

    .el-button {
      background-image: url('@/assets/images/btn.png') !important;
      background-repeat: no-repeat;
      background-size: 100% 100%;
    }
  }

  .el-picker-panel__icon-btn {
    color: #fff;
  }

  .el-date-range-picker__header {
    color: #fff;
  }

  .el-date-table th {
    color: #fff;
  }

  .el-date-table td.in-range div {
    background-color: rgba(133, 145, 206, 0.5);
  }

  .available.start-date span {
    color: #fff !important;
  }

  .available.end-date span {
    color: #fff !important;
  }

  .el-date-table td.start-date span {
    background-color: #ffca64;
  }

  .el-date-table td.end-date span {
    background-color: #ffca64;
  }

  border-color: #5b699f;

  .el-date-table th {
    border-bottom-color: #5b699f;
  }

  .el-date-table__row .available span {
    color: #c0c4cc;
  }

  .el-date-table__row .next-month span {
    color: rgba(255, 255, 255, 0.3);
  }

  .el-picker-panel__body-wrapper {
    background-color: #2f426e;

    .el-picker-panel__sidebar {
      background-color: #2f426e;
      border-right-color: #5b699f;

      .el-picker-panel__shortcut {
        color: #fff;
      }
    }

    .el-picker-panel__content {
      border-color: #5b699f;
    }
  }
}
</style>
