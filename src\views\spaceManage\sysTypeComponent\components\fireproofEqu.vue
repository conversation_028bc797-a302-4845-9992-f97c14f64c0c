<template>
  <div class="main">
    <el-dialog
      v-dialogDrag
      :modal="false"
      :visible.sync="dialogShow"
      custom-class="mainDialog main"
      append-to-body
      :close-on-click-modal="false"
      :before-close="closeDialog"
      class="all-table-componentList"
    >
      <template slot="title">
        <span class="dialog-title">{{ dialogData.title }}</span>
      </template>
      <div class="dialog-content">
        <div class="search-box">
          <div style="margin-right: 16px" class="assetsInput">
            <span>设备名称：</span>
            <el-input v-model="deviceParams.surveyName" placeholder="请输入设备名称"></el-input>
          </div>
          <div style="margin-right: 16px" class="taskInput">
            <span>设备类型：</span>
            <el-select v-model="deviceParams.entityTypeId" placeholder="请选择类型" filterable clearable popper-class="new-select">
              <el-option v-for="item in typeList" :key="item.entityTypeId" :label="item.entityTypeName" :value="item.entityTypeId"> </el-option>
            </el-select>
          </div>
          <div class="search-btn">
            <el-button @click="resetFireproofEqu">重置</el-button>
            <el-button @click="searchFireproofEqu">查询</el-button>
          </div>
        </div>

        <el-table
          v-loading="deviceTableLoading"
          :data="tableList"
          :height="dialogData.height"
          :cell-style="$tools.setCell(3)"
          :header-cell-style="$tools.setHeaderCell(3)"
          style="width: 100%"
          element-loading-background="rgba(0, 0, 0, 0.2)"
        >
          <el-table-column v-for="(column, index) in tableColumn" :key="index" :prop="column.prop" :label="column.label" :min-width="column.minWidth" show-overflow-tooltip>
            <template slot-scope="scope">
              <table-render v-if="column.render" :sc="scope" :row="scope.row" :render="column.render"></table-render>
              <div v-else-if="!column.formatter">
                {{ scope.row[column.prop] }}
              </div>
              <div v-else>
                {{ column.formatter(scope.row) }}
              </div>
            </template>
          </el-table-column>
        </el-table>
        <el-pagination
          :current-page="page"
          :page-sizes="[15, 20, 30, 40]"
          :page-size="pageSize"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
          class="pagination"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        ></el-pagination>
      </div>
    </el-dialog>
  </div>
</template>
<script lang="jsx">
import tableRender from '../../components/tableRender'
import icon_5 from '@/assets/images/icon-5.png'
import icon_6 from '@/assets/images/icon-6.png'
import icon_2 from '@/assets/images/icon-2.png'

import { deviceList, entityType } from '@/utils/fireproof.js'
export default {
  name: 'fireproofEqu',
  components: {
    'table-render': tableRender
  },
  props: {
    dialogShow: {
      type: Boolean,
      default: false
    },
    dialogData: {
      type: Object,
      default: () => {
        return {
          title: '服务工单台账',
          height: 'calc(100% - 40px)'
        }
      }
    }
  },
  data() {
    return {
      deviceParams: {
        surveyName: '',
        entityTypeId: ''
      },
      icon_5,
      icon_6,
      icon_2,

      // 列表
      tableList: [],
      deviceTableLoading: false,
      page: 1,
      pageSize: 15,
      total: 0,
      // 实体table数据
      tableColumn: [
        {
          prop: 'surveyName',
          label: '设备名称'
        },
        {
          prop: 'menuName',
          label: '归属系统'
        },

        {
          prop: 'status',
          label: '状态',
          render: (h, row) => {
            return (
              <div>
                {row.row.status == '0' && (
                  <div class="status-box">
                    <img class="table-icon" src={icon_5} />
                    <span style="color:#61E29D">正常</span>
                  </div>
                )}
                {row.row.status == '6' && (
                  <div class="status-box">
                    <img class="table-icon" src={icon_6} />
                    <span style="color:#86909C">离线</span>
                  </div>
                )}
                {row.row.status == '10' && (
                  <div class="status-box">
                    <img class="table-icon" src={icon_2} />
                    <span style="color:#FF2D55">异常</span>
                  </div>
                )}
              </div>
            )
          }
        }
      ],

      typeList: []

    }
  },
  mounted() {
    this.getEquTypeList()
    this.getList()
  },
  methods: {
    // 设备类型
    getEquTypeList() {
      let params = {
        projectCode: this.dialogData.projectCode
      }
      entityType(params).then(res => {
        if (res.data.code === '200') {
          this.typeList = res.data.data
        }
      })
    },
    // 获取列表
    getList() {
      this.deviceTableLoading = true
      let params = {
        ...this.deviceParams,
        ...this.dialogData
      }
      deviceList(params).then((res) => {
        if (res.data.code === '200') {
          this.tableList = res.data.data.list
          this.total = res.data.data.total
        } else {
        }
      }).finally(() => {
        this.deviceTableLoading = false
      })
    },
    handleSizeChange(val) {
      this.pageSize = val
      this.getList()
    },
    handleCurrentChange(val) {
      this.page = val
      this.getList()
    },

    // 防火分区设备台账
    resetFireproofEqu() {
      this.deviceParams.surveyName = ''
      this.deviceParams.entityTypeId = ''
      this.getList()
    },

    // 防火分区设备台账
    searchFireproofEqu() {
      this.getList()
    },

    // 关闭弹框
    closeDialog() {
      this.$emit('configCloseDialog')
    }

  }
}
</script>

<style lang="scss" type="text/css" scoped>
.all-table-componentList {
  pointer-events: none;
  ::v-deep .el-image__error {
    background: center;
  }
}
.preview-image {
  z-index: 9999 !important;
  ::v-deep .el-image-viewer__canvas {
    color: #fff;
  }
}
::v-deep .mainDialog {
  width: 70%;
  height: 84vh;
  margin-top: 6vh !important;
  // border: 1px solid #5996f9;
  background-color: transparent !important;
  pointer-events: auto;
  background-image: url('@/assets/images/table-bg.png');
  background-repeat: no-repeat;
  background-size: 100% 100%;
  box-shadow: none;
  .el-dialog__headerbtn {
    transform: translateX(-36px);
    width: 25px;
    height: 25px;
    background-image: url('@/assets/images/close.png');
    background-repeat: no-repeat;
    background-size: 100% 100%;
    .el-dialog__close::before {
      display: none;
    }
  }
  .dialog-title {
    display: inline-block;
    width: 100%;
    text-align: center;
    transform: translateY(-6px);
    color: #cbdeed;
  }
  .dialog-title::before {
    display: none;
  }
  .el-tabs__nav-scroll {
    display: flex;
    .el-tabs__nav {
      margin: auto;
    }
  }
  .title-tabs {
    margin-bottom: 10px;
    .el-tabs__header {
      margin: 0;
      .el-tabs__active-bar {
        background: #ffe3a6;
      }
      .el-tabs__item.is-active {
        color: #ffe3a6;
      }
      .el-tabs__item {
        font-size: 14px;
        font-family: PingFang-SC-Medium, PingFang-SC;
        font-weight: 500;
        color: #ffffff;
      }
    }
    .el-tabs__nav-wrap::after {
      display: none;
    }
  }
  .el-dialog__header {
    background-color: transparent;
  }
  .el-dialog__body {
    padding: 0;
    height: calc(100% - 60px);
    max-height: fit-content;
    overflow-y: hidden;
    background-color: transparent;
    padding: 0px 80px;
    .dialog-content {
      width: 100%;
      height: 100%;
      background-color: transparent;
      .statistics-top {
        height: 70px;
        width: 100%;
        display: flex;
        justify-content: space-around;
        > div {
          width: max-content;
          // width: 10%;
          height: 100%;
          padding: 10px 0;
          box-sizing: border-box;
          display: flex;
          justify-content: space-evenly;
          flex-direction: column;
          p {
            text-align: center;
            font-size: 1rem;
            font-family: DIN-Bold, DIN;
            font-weight: bold;
            color: #ffffff;
          }
          .green-font {
            font-size: 1.25rem;
            color: #ffca64;
          }
        }
      }
      .el-table {
        background-color: transparent;
        border: 1px solid #202d4c;
        .el-table__header-wrapper {
          background-color: transparent !important;
        }
        .el-table__body-wrapper {
          background-color: transparent !important;
        }
        tr {
          background-color: transparent !important;
        }
        td {
          background-color: transparent !important;
        }
      }
    }
  }
}
::v-deep .detailDialog {
  width: 60%;
  height: 80vh;
  margin-top: 7vh !important;
  background-color: transparent !important;
  background-image: url('@/assets/images/table-bg.png');
  background-repeat: no-repeat;
  background-size: 100% 100%;
  pointer-events: auto;
  .el-dialog__body {
    padding: 10px 50px;
    height: calc(100% - 60px);
    max-height: 80vh;
    .el-table th.el-table__cell > .cell {
      width: max-content;
    }
    .el-table td.el-table__cell .cell div {
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
  }
}
::v-deep .search-box {
  display: flex;
  margin-bottom: 12px;
  .riskInput {
    .el-input {
      width: 110px;
    }
    .el-select {
      .el-input__inner {
        width: 110px !important;
      }
    }
  }
  .taskInput {
    .el-input {
      width: 140px;
    }
  }
  .assetsInput {
    .el-input {
      width: 140px;
    }
  }
  .el-button {
    background-image: url('@/assets/images/btn.png');
    background-repeat: no-repeat;
    background-size: 100% 100%;
    height: 35px;
  }
  .search-btn {
    margin-left: 24px;
  }
  .el-select {
    .el-input .el-input__inner {
      border-color: rgba(133, 145, 206, 0.5);
      width: 150px;
      height: 35px;
    }
  }
  .el-cascader {
    .el-input__inner {
      height: 35px !important;
    }
  }
  .el-date-editor {
    width: 250px;
    height: 35px;
    background-color: #14233e;
    border-color: rgba(133, 145, 206, 0.5);
    .el-input__icon {
      transform: translateY(-2px);
    }
    .el-range-input {
      background-color: #14233e;
      color: rgba(255, 255, 255, 0.8);
    }
    .el-range-separator {
      color: rgba(255, 255, 255, 0.8);
      transform: translateY(-2px);
    }
  }
  .el-input .el-input__inner {
    border-color: rgba(133, 145, 206, 0.5);
    border-radius: 4px;
    color: rgba(255, 255, 255, 0.8);
  }
}
.search-box .el-input {
  width: 120px;
  height: 35px;
}
.transY {
  display: inline-block;
  transform: translateY(-2px);
}
.table-icon {
  width: 16px;
  margin-right: 3px;
}
.status-box {
  display: flex;
  align-items: center;
}
</style>
