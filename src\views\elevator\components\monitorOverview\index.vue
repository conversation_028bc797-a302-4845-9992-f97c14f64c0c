<template>
  <div class="viewContent">
    <ElevatorStatistics :projectCode="roomData.projectCode" :roomData="roomData"/>
    <ElevatorList :roomData="roomData"/>
  </div>
</template>
<script>
export default {
  components: {
    ElevatorStatistics: () => import('./components/elevatorStatistics'),
    ElevatorList: () => import('./components/elevatorList')
  },
  props: {
    roomData: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
    }
  },
  mounted() {
    console.log('===', this.roomData)
  }

}
</script>
<style lang="scss" scoped>
.viewContent{
  width: 100%;
  height: 100%;

}
</style>
