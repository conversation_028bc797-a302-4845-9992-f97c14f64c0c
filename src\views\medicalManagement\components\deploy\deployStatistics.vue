<template>
  <div class="statistics-view">
    <div class="sic-content-left">
      <div class="sic-content-leftSearch">
        <el-form :model="searchForm" class="search-form" inline ref="formRef">
          <el-row>
            <el-col :span="5">
              <el-form-item>
                <el-select v-model="searchForm.dimension" size="small" placeholder="统计维度">
                  <el-option v-for="item in dimensionOptions" :key="item.value" :label="item.label" :value="item.value">
                  </el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="9" v-if="searchForm.dimension == 1||searchForm.dimension == 2||!searchForm.dimension">
              <el-form-item>
                <el-date-picker v-model="searchForm.data" format="yyyy-MM-dd" value-format="yyyy-MM-dd"
                  style="width:100%" popper-class="date-style" type="daterange" range-separator="至"
                  start-placeholder="开始时间" end-placeholder="结束时间">
                </el-date-picker>
              </el-form-item>
            </el-col>
            <el-col :span="5">
              <el-form-item>
                <el-select v-model="searchForm.deptCode" size="small" placeholder="租赁科室">
                  <el-option v-for="item in deptOptions" :key="item.value" :label="item.label" :value="item.value">
                  </el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="5">
              <el-form-item>
                <el-select v-model="searchForm.assetsType" size="small" placeholder="设备类型">
                  <el-option v-for="item in assetsTypeOptions" :key="item.value" :label="item.label"
                    :value="item.value">
                  </el-option>
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </div>
      <div class="sic-content-leftTop">
        <div class="box-top">
          <div>调配汇总分析</div>
        </div>
        <div class="box-content  sic-content-columnBox">
          <div class="sic-content-3column" v-for="(item, index) in statisticsData" :key="index"
            @click="toMore('lease')">
            <img :src="item.img" alt="" />
            <div>
              <p class="statistics_top_title">{{ item.name }}</p>
              <p class="statistics_top_value"> {{ leaseAccountStatistics[item.key] || 0 }} <span
                  class="unit">{{item.unit}}</span>
              </p>
            </div>
          </div>
        </div>
      </div>
      <div class="sic-content-leftCenter">
        <div class="box-content box-barEcharts">
          <div id="bar_frequency" style="width: 100%; height: 100%"></div>
          <div id="bar_amount" style="width: 100%; height: 100%"></div>
        </div>
      </div>
      <div class="sic-content-leftBottom">
        <div class="box-top">
          <div>租用统计</div>
          <div @click="toMore('lease')">更多 <i class="el-icon-arrow-right"></i> </div>
        </div>
        <div class="box-content">
          <el-table :data="rentStatisticsTable" height="100%" :cell-style="$tools.setCell(3)"
            :header-cell-style="$tools.setHeaderCell(3)" style="width: 100%"
            element-loading-background="rgba(0, 0, 0, 0.2)" v-loading="rentStatisticsLoading">
            <el-table-column prop="classificationName" show-overflow-tooltip label="分类名称"></el-table-column>
            <el-table-column prop="rentTotal" show-overflow-tooltip label="租赁总数(元)"></el-table-column>
            <el-table-column prop="rentTotalTime" show-overflow-tooltip label="租赁总时长(时)" width="120"></el-table-column>
            <el-table-column prop="rentTotalMoney" show-overflow-tooltip label="租赁总额(元)"></el-table-column>
            <el-table-column prop="deptMoney" show-overflow-tooltip label="科室结算总额(元)" width="120"></el-table-column>
            <el-table-column prop="averageTime" show-overflow-tooltip label="平均租赁时长(时)" width="120"></el-table-column>
            <el-table-column prop="averageMoney" show-overflow-tooltip label="平均租赁总额(元)" width="120"></el-table-column>
          </el-table>
        </div>
      </div>
    </div>
    <div class="sic-content-right">
      <div class="sic-content-rightTop">
        <div class="box-top">
          <div>调配设备统计</div>
          <div @click="toMore('assets')">更多 <i class="el-icon-arrow-right"></i> </div>
        </div>
        <div class="box-content box-echarts">
          <div id="pie_borrow" style="width: 100%; height: 100%"></div>
          <div id="pie_canBorrow" style="width: 100%; height: 100%"></div>
        </div>
      </div>
      <div class="sic-content-rightCenter">
        <div class="box-top">
          <div>可借用设备</div>
          <div @click="toMore('assets')">更多 <i class="el-icon-arrow-right"></i> </div>
        </div>
        <div class="box-content">
          <el-table :data="loanableTableData" height="100%" :cell-style="$tools.setCell(3)"
            :header-cell-style="$tools.setHeaderCell(3)" style="width: 100%"
            element-loading-background="rgba(0, 0, 0, 0.2)" v-loading="loanableTableLoading">
            <el-table-column prop="assetsName" show-overflow-tooltip label="资产名称"></el-table-column>
            <el-table-column prop="assetsCode" show-overflow-tooltip label="资产编码"></el-table-column>
            <el-table-column prop="commonName" show-overflow-tooltip label="通用名"></el-table-column>
            <el-table-column prop="model" show-overflow-tooltip label="规格型号"></el-table-column>
            <el-table-column prop="statusName" show-overflow-tooltip label="资产状态">
              <template slot-scope="scope">
                <div class="status-box">
                  <img class="table-icon" :src='icon_5' />
                  <span style="color:#61E29D">{{scope.row.statusName}}</span>
                </div>
              </template>
            </el-table-column>
            <el-table-column prop="sort" show-overflow-tooltip label="调配分类"></el-table-column>
          </el-table>
        </div>
      </div>
      <div class="sic-content-rightBottom">
        <div class="box-top">
          <div>借用中设备</div>
          <div @click="toMore('assets')">更多 <i class="el-icon-arrow-right"></i> </div>
        </div>
        <div class="box-content">
          <el-table :data="borrowTableData" height="100%" :cell-style="$tools.setCell(3)"
            :header-cell-style="$tools.setHeaderCell(3)" style="width: 100%"
            element-loading-background="rgba(0, 0, 0, 0.2)" v-loading="borrowTableLoading">
            <el-table-column prop="assetsName" show-overflow-tooltip label="资产名称"></el-table-column>
            <el-table-column prop="assetsCode" show-overflow-tooltip label="资产编码"></el-table-column>
            <el-table-column prop="commonName" show-overflow-tooltip label="通用名"></el-table-column>
            <el-table-column prop="model" show-overflow-tooltip label="规格型号"></el-table-column>
            <el-table-column prop="statusName" show-overflow-tooltip label="资产状态">
              <template slot-scope="scope">
                <div class="status-box">
                  <img class="table-icon" :src='icon_5' />
                  <span style="color:#61E29D">{{scope.row.statusName}}</span>
                </div>
              </template>
            </el-table-column>
            <el-table-column prop="sort" show-overflow-tooltip label="调配分类"></el-table-column>
            <el-table-column prop="lendDept" show-overflow-tooltip label="借用科室"></el-table-column>
            <el-table-column prop="storageLocation" show-overflow-tooltip label="当期位置"></el-table-column>
          </el-table>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import * as echarts from 'echarts'
import rentMoneyImg from '@/assets/images/medicalManagement/rentMoney.png'
import rentTotalImg from '@/assets/images/medicalManagement/rentTotal.png'
import rentTimeImg from '@/assets/images/medicalManagement/rentTime.png'
import icon_5 from '@/assets/images/icon-5.png'

export default {
  data() {
    return {
      icon_5,
      rentMoneyImg,
      rentTotalImg,
      rentTimeImg,
      searchForm: {
        data: [],
        dimension: '',
        deptCode: '',
        assetsType: '',
      },
      pieIdData: [
        "pie_borrow",
        "pie_canBorrow",
      ],
      barIdData: [
        "bar_frequency",
        "bar_amount",
      ],
      statisticsData: [
        {
          name: '租赁总数',
          key: 'total',
          unit: '次',
          img: rentTotalImg,
        },
        {
          name: '租赁总时长',
          key: 'totalTime',
          unit: '小时',
          img: rentTimeImg,
        },
        {
          name: '平均租赁时长',
          key: 'averageTime',
          unit: '小时',
          img: rentTimeImg,
        },
        {
          name: '租赁总额',
          key: 'totalMoney',
          unit: '万元',
          img: rentMoneyImg,
        },
        {
          name: '科室结算总金额',
          key: 'deptMoney',
          unit: '万元',
          img: rentMoneyImg,
        },
        {
          name: '平均租赁金额',
          key: 'averageMoney',
          unit: '元',
          img: rentMoneyImg,
        }
      ], // 统计数据
      leaseAccountStatistics: {
        total: '18,643',
        totalTime: '1,458',
        totalMoney: '186',
        deptMoney: '960.75',
        averageTime: '186',
        averageMoney: '9,430.90',
      },//租赁数据
      dimensionOptions: [
        {
          label: '综合统计',
          value: 1,
        },
        {
          label: '科室统计',
          value: 2,
        },
        {
          label: '月度统计',
          value: 3,
        },
        {
          label: '季度统计',
          value: 4,
        },
        {
          label: '年度统计',
          value: 5,
        },
      ],
      assetsTypeOptions: [],
      deptOptions: [],
      isType: 'month',
      rentStatisticsTable: [],//租用统计table
      rentStatisticsLoading: false,//租用统计loading
      borrowTableData: [], //可借用table
      loanableTableData: [],
      loanableTableLoading: false,
      borrowTableLoading: false,
    };
  },
  // 监听全屏缩放 动态改变echarts图表大小
  watch: {
    '$store.state.dialogFullScreenState': {
      handler(val) {
        this.$nextTick(() => {
          const echartsDom = ['bar_frequency', 'bar_amount', 'pie_borrow', 'pie_canBorrow']
          setTimeout(() => {
            echartsDom.forEach((item) => {
              if (document.getElementById(item)) {
                this.initData()
              }
            })
          }, 200)
        })
      },
      deep: true
    }
  },
  mounted() {
    this.initData()
  },
  methods: {
    //更多
    toMore(type) {
      switch (type) {
        case 'assets':
          this.$emit('openDetailComponent', 'deployAssetsList')
          break;
        case 'lease':
          this.$emit('openDetailComponent', 'leaseList')
          break;
        default:
          break;
      }
    },
    /** 重置 */
    resetForm() {
      this.searchForm = {}
      this.initData()
    },
    /** 查询 */
    handleSearchForm() {
      this.initData()
    },
    //图表
    initData() {
      this.getLoanableAssetsList()
      this.getBorrowAssetsList()
      this.getRentStatisticsList()
      this.barIdData.forEach((item) => {
        this.initBarEchart(item);
      });
      this.pieIdData.forEach((item) => {
        this.initPie(item);
      });
    },
    //获取可借用设备
    getLoanableAssetsList() {
      this.loanableTableData = [
        {
          assetsName: '呼吸机',
          assetsCode: '30SD45EDT123',
          commonName: '--',
          model: 'Primus',
          statusName: '闲置中',
          sort: '呼吸机'
        },
        {
          assetsName: '眼科手术器械',
          assetsCode: '30SD45EDT123',
          commonName: '--',
          model: 'Primus',
          statusName: '闲置中',
          sort: '呼吸机'
        }, {
          assetsName: '测试',
          assetsCode: '30SD45EDT123',
          commonName: '--',
          model: 'Primus',
          statusName: '闲置中',
          sort: '呼吸机'
        }, {
          assetsName: '神经外科手术器械',
          assetsCode: '30SD45EDT123',
          commonName: '--',
          model: 'Primus',
          statusName: '闲置中',
          sort: '呼吸机'
        }, {
          assetsName: '基础外科手术器械',
          assetsCode: '30SD45EDT123',
          commonName: '--',
          model: 'a7',
          statusName: '闲置中',
          sort: '呼吸机'
        }
      ]
    },
    //获取借用中设备table
    getBorrowAssetsList() {
      this.borrowTableData = [
        {
          assetsName: '呼吸机',
          assetsCode: '30SD45EDT123',
          commonName: '--',
          model: 'Primus',
          statusName: '闲置中',
          sort: '呼吸机',
          lendDept: '儿科',
          storageLocation: '医技楼>11f'
        },
        {
          assetsName: '眼科手术器械',
          assetsCode: '30SD45EDT123',
          commonName: '--',
          model: 'Primus',
          statusName: '闲置中',
          sort: '呼吸机',
          lendDept: '儿科',
          storageLocation: '医技楼>11f'
        }, {
          assetsName: '测试',
          assetsCode: '30SD45EDT123',
          commonName: '--',
          model: 'Primus',
          statusName: '闲置中',
          sort: '呼吸机',
          lendDept: '儿科',
          storageLocation: '医技楼>11f'
        }, {
          assetsName: '神经外科手术器械',
          assetsCode: '30SD45EDT123',
          commonName: '--',
          model: 'Primus',
          statusName: '闲置中',
          sort: '呼吸机',
          lendDept: '儿科',
          storageLocation: '医技楼>11f'
        }, {
          assetsName: '基础外科手术器械',
          assetsCode: '30SD45EDT123',
          commonName: '--',
          model: 'a7',
          statusName: '闲置中',
          sort: '呼吸机',
          lendDept: '儿科',
          storageLocation: '医技楼>11f'
        }
      ]
    },
    //获取借用统计table
    getRentStatisticsList() {
      this.rentStatisticsTable = [
        {
          classificationName: '呼吸机',
          rentTotal: '30',
          rentTotalTime: '8328.95',
          rentTotalMoney: '398,910.000',
          deptMoney: '398,910.000',
          averageTime: '8328.95',
          averageMoney: '398,910.000',
        },
        {
          classificationName: '眼科手术器械',
          rentTotal: '12',
          rentTotalTime: '4548.45',
          rentTotalMoney: '310.000',
          deptMoney: '310.000',
          averageTime: '4548.45',
          averageMoney: '310.000',
        }, {
          classificationName: '基础外科手术器械',
          rentTotal: '1',
          rentTotalTime: '302.95',
          rentTotalMoney: '10,000',
          deptMoney: '10,000',
          averageTime: '302.95',
          averageMoney: '398,910.000',
        }, {
          classificationName: '呼吸机',
          rentTotal: '9',
          rentTotalTime: '466.89',
          rentTotalMoney: '9,910.000',
          deptMoney: '9,910.000',
          averageTime: '466.89',
          averageMoney: '9,910.000',
        },
      ]
    },
    /** 初始化 柱状图 */
    initBarEchart(item) {
      var chartDom = document.getElementById(item);
      var myChart = echarts.init(chartDom);
      var option;
      let dataObj = {
        bar_frequency: [
          { value: 1900, name: "其他", title: '租赁次数排行/次', color: '#61E29D' },
          { value: 2330, name: "基础外科手术器械", title: '租赁次数排行/次', color: '#61E29D' },
          { value: 3200, name: "神经外科手术器械", title: '租赁次数排行/次', color: '#61E29D' },
          { value: 3800, name: "眼科手术器械", title: '租赁次数排行/次', color: '#61E29D' },
          { value: 4000, name: "呼吸机", title: '租赁次数排行/次', color: '#61E29D' },
        ],
        bar_amount: [
          { value: 1900, name: "其他", title: '租赁金额排行/元', color: '#FFCA64' },
          { value: 2330, name: "基础外科手术器械", title: '租赁金额排行/元', color: '#FFCA64' },
          { value: 3200, name: "神经外科手术器械", title: '租赁金额排行/元', color: '#FFCA64' },
          { value: 3800, name: "眼科手术器械", title: '租赁金额排行/元', color: '#FFCA64' },
          { value: 4000, name: "呼吸机", title: '租赁金额排行/元', color: '#FFCA64' },
        ],
      };
      option = {
        title: {
          text: dataObj[item][0].title,
          left: "2%",
          top: "1%",
          textStyle: {
            color: '#ffffff', // 标题颜色
            fontWeight: '500',
            fontSize: 16,
          },
        },
        tooltip: {
          trigger: "axis",
          axisPointer: {
            type: "shadow",
          },
        },
        legend: {},
        grid: {
          top: "18%",
          left: "3%",
          right: "10%",
          bottom: "1%",
          containLabel: true,
        },
        xAxis: {
          type: "value",
          axisLine: {
            show: true,
            lineStyle: {
              color: ["rgba(233, 233, 233,0.1)"], //分割线的颜色
              type: "dashed",
            },
          },
          interval: "auto",
          axisLabel: {
            textStyle: {
              color: "rgba(230,247,255,0.6)", //坐标值的具体的颜色
            },
          },
          splitLine: {
            show: true,
            lineStyle: {
              color: ["rgba(233, 233, 233,0.1)"], //分割线的颜色
              type: "dashed",
            },
          },
        },
        yAxis: {
          type: "category",
          data: dataObj[item].map((item) => item.name),
          axisLine: {
            show: true,
            lineStyle: {
              color: ["rgba(233, 233, 233,0.1)"], //分割线的颜色
              type: "dashed",
            },
          },
          axisLabel: {
            textStyle: {
              color: "rgba(230,247,255,0.6)", //坐标值的具体的颜色
            },
          },
          splitLine: {
            show: true,
            lineStyle: {
              color: ["rgba(233, 233, 233,0.1)"], //分割线的颜色
              type: "dashed",
            },
          },
        },
        series: [
          {
            name: "",
            type: "bar",
            data: dataObj[item].map((item) => item.value),
            barWidth: 10,
            itemStyle: {
              normal: {
                color: dataObj[item][0].color
              },
            },
          },
        ],
      };

      myChart.resize();
      myChart.clear();
      option && myChart.setOption(option);
      // 先移除点击事件 解决点击事件重复绑定
      myChart.getZr().off('click')
      // 点击事件
      myChart.getZr().on('click', (params) => {
        this.toMore('lease')
      })
      window.addEventListener("resize", () => {
        myChart.resize();
      });
    },
    /** 初始化饼图 */
    initPie(item) {
      var chartDom = document.getElementById(item);
      var myChart = echarts.init(chartDom);
      var option;
      let dataObj = {
        pie_borrow: [
          { value: 423, name: "设备类型1", percentage: '34', title: '借用总数' },
          { value: 200, name: "设备类型2", percentage: '34', title: '借用总数' },
          { value: 200, name: "设备类型3", percentage: '34%', title: '借用总数' },
        ],
        pie_canBorrow: [
          { value: 423, name: "设备类型1", percentage: '34', title: '可借总数' },
          { value: 200, name: "设备类型2", percentage: '34', title: '可借总数' },
          { value: 200, name: "设备类型3", percentage: '34', title: '可借总数' },
          { value: 200, name: "设备类型4", percentage: '34', title: '可借总数' },
        ],
      };
      const xdata = Array.from(dataObj[item], ({ name }) => name)
      option = {
        title: [
          {
            text: dataObj[item][0].title,
            left: "28%",
            top: "40%",
            textStyle: {
              color: "#FFFFFF",
              fontSize: 12,
              fontWeight: "100",
            },
            textAlign: 'center',
            textVerticalAlign: 'center',
          },
          {
            text: "1220",
            left: "28%",
            top: "59%",
            textStyle: {
              fontSize: "12",
              color: "#FFFFFF",
              foontWeight: "600",
            },
            textAlign: 'center',
            textVerticalAlign: 'center',
          },
        ],
        tooltip: {
          trigger: "item",
          formatter: function (params) {
            let marker = params.marker || "",
              name = params.name || "",
              value = params.value || "",
              percent = params.percent || "0";
            let spanVal = `<span style="padding: 0px 10px 0 5px;font-weight: bolder;color: #333;">${value}</span>`;
            return `${marker}${name}${spanVal}${percent}%`;
          },
        },
        legend: {
          // 取消legend和echarts的联动
          selectedMode: false,
          icon: 'circle',
          orient: 'vartical',
          x: 'left',
          top: 'center',
          left: '50%',
          bottom: '0%',
          data: xdata,
          itemWidth: 8,
          itemHeight: 8,
          itemGap: 16,
          textStyle: {
            color: '#B3C2DD' //  字体颜色
          },
          formatter: function (name) {
            var oa = dataObj[item]
            for (var i = 0; i < dataObj[item].length; i++) {
              if (name === oa[i].name) {
                return ' ' + name + ' ' + oa[i].percentage + ' ' + oa[i].value
              }
            }
          }
        },
        series: [
          {
            name: "",
            type: "pie",
            center: ["30%", "50%"],
            radius: ["70%", "88%"],
            avoidLabelOverlap: false,
            label: {
              show: false,
              position: "center",
            },
            emphasis: {
              label: {
                show: true,
                fontWeight: "bold",
              },
            },
            labelLine: {
              show: false,
            },
            data: dataObj[item],
          },
          {
            type: "gauge",
            center: ["30%", "50%"], // 饼状图的位置
            radius: "90%",
            // 如何是版本是4 这里是359.999；不能是360；否则圆环不能正常显示
            // 如果是版本是5，这里可以是360
            startAngle: 360,
            endAngle: 0,
            splitNumber: 25,
            zlevel: 10,
            axisLine: {
              show: false,
            },
            splitLine: {
              show: true, // 是否显示分隔线。 如果设置为true,外层虚线才能看见
              length: 1, // 分隔线与轴线的距离。这里表现是虚线的宽度
              lineStyle: {
                width: 5, // 分隔线线长。支持相对半径的百分比。
                color: "rgba(230,247,255,0.2)", // 线的颜色
              },
            },
            axisLabel: {
              //刻度标签
              show: false,
            },
            axisTick: {
              //刻度样式
              show: false,
            },
            detail: {
              show: false,
            },
            //仪表盘指针。
            pointer: {
              // 不显示仪表盘中的指针
              show: false,
            },
            data: [],
          },
        ],
      };
      myChart.resize();
      myChart.clear();
      option && myChart.setOption(option);
      window.addEventListener("resize", () => {
        myChart.resize();
      });
    },
  },
};
</script>
<style lang="scss" scoped>
@import "../../../../assets/sino-ui/common/var.scss";
%sic-common-styles {
  flex-shrink: 0;
  background: rgba(53, 98, 219, 0.06);
  border-radius: 2px;
  box-sizing: border-box;
}
.statistics-view {
  height: 100%;
  padding: 0px 40px;
  display: flex;
  align-items: center;
  .table-icon {
    width: 16px;
    margin-right: 3px;
  }
  .status-box {
    display: flex;
    align-items: center;
  }
  .sic-content-left,
  .sic-content-right {
    height: 100%;
    width: calc(50% - 0.5rem);
  }
  .sic-content-left {
    @extend %sic-common-styles;
    margin-right: 1rem;
  }
  .box-top {
    display: flex;
    justify-content: space-between;
    margin-bottom: 16px;
    div:nth-child(1) {
      font-weight: 500;
      font-size: 16px;
    }
    div:nth-child(2) {
      font-size: 14px;
      color: #8bddf5;
    }
  }
  .box-echarts {
    display: flex;
  }
  .box-content {
    height: calc(100% - 30px);
    width: 100%;
  }
  .sic-content-left {
    @extend %sic-common-styles;
    .sic-content-columnBox {
      height: calc(100%) !important;
      display: flex;
      flex-wrap: wrap;
      .sic-content-3column {
        background: rgba(133, 145, 206, 0.05);
        width: calc((100% / 3) - 0.8rem);
        height: 46%;
        margin-right: 1rem;
        border-radius: 8px;
        margin-bottom: 1rem;
        display: flex;
        align-items: center;
        padding: 26px 12px;
        img {
          vertical-align: middle;
          margin-right: 8px;
        }
        .statistics_top_title {
          font-weight: 400;
          font-size: 14px;
          color: #7eaef9;
        }
        .statistics_top_value {
          font-weight: 500;
          font-size: 18px;
          margin-top: 6px;
          color: #ffffff;
          .unit {
            font-weight: bold;
            font-size: 12px;
            margin-left: 3px;
          }
        }
      }
      .sic-content-3column:nth-child(3n) {
        margin-right: 0 !important;
      }
    }
    .box-barEcharts {
      display: flex;
      justify-content: space-between;
      height: calc(100%) !important;
    }
    .sic-content-leftSearch {
      height: 10%;
      padding: 16px;
      .search-form {
        height: 34px;
        line-height: 34px;
        width: 100%;
        ::v-deep .el-input__inner {
          background: center;
          border: 1px solid $input-border-color;
          border-radius: 0;
          color: $color;
          height: inherit;
          font-family: PingFangSC-Medium, PingFang SC;
        }

        ::v-deep .el-range-input {
          background: center;
          color: $color;
          font-size: 12px !important;
        }

        ::v-deep .el-range-separator,
        ::v-deep .el-range__icon {
          color: #3769be;
          line-height: 30px !important;
        }
      }
    }
    .sic-content-leftTop {
      height: 26%;
      padding: 0 16px 16px 16px;
    }
    .sic-content-leftCenter {
      height: calc(32% - 8px);
      margin-top: 16px;
      padding: 16px;
    }
    .sic-content-leftBottom {
      height: calc(32% - 8px);
      padding: 0 16px 16px 16px;
    }
  }
  .sic-content-right {
    .sic-content-rightTop {
      height: 26%;
      padding: 16px;
      @extend %sic-common-styles;
    }
    .sic-content-rightCenter {
      height: calc(37% - 16px);
      margin-top: 16px;
      padding: 16px;
      @extend %sic-common-styles;
    }
    .sic-content-rightBottom {
      height: calc(37% - 16px);
      margin-top: 16px;
      padding: 16px;
      @extend %sic-common-styles;
    }
  }
  .sic-search-box {
    height: 100%;
    width: calc(25% - 0.5rem);
  }
}
::v-deep .el-form-item {
  margin-bottom: 0px;
}

::v-deep.el-form-item .el-form-item__content {
  line-height: 34px !important;
  height: 34px;
}
::v-deep .el-form-item .el-date-editor .el-range-input {
  height: 30px !important;
}
</style>
