<template>
  <div class="main">
    <el-dialog v-dialogDrag :visible.sync="addDialogShow" :close-on-click-modal="false" :before-close="closeDialog" class="personDialog">
      <template slot="title">
        <span class="dialog-title">{{ dialogTitle }}</span>
      </template>
      <div class="dialog-content" style="width: 100%">
        <el-form ref="formInline" :model="formInline" :rules="rules" :inline="true" label-width="auto" size="medium">
          <div class="formRow" style="display: flex">
            <el-form-item label="标签名称：" prop="labelName">
              <el-input v-if="type !== 'detail'" v-model.trim="formInline.labelName" placeholder="请输入标签名称"></el-input>
              <span class="form-detail-span" v-else>{{ formInline.labelName }}</span>
            </el-form-item>
          </div>
          <div class="formRow" style="display: flex">
            <el-form-item label="键值：" prop="dictValue">
              <el-input v-if="type !== 'detail'" v-model.trim="formInline.dictValue" placeholder="请输入键值"></el-input>
              <span class="form-detail-span" v-else>{{ formInline.dictValue }}</span>
            </el-form-item>
          </div>
          <div class="formRow" style="display: flex">
            <el-form-item label="排序：" prop="sort">
              <el-input v-if="type !== 'detail'" v-model.trim="formInline.sort" placeholder="请输入序号" onkeyup="value=Number(value.replace(/[^\d]/g,''));"></el-input>
              <span class="form-detail-span" v-else>{{ formInline.sort }}</span>
            </el-form-item>
          </div>
          <div class="formRow" style="display: flex">
            <el-form-item label="描述：" prop="description">
              <el-input v-if="type !== 'detail'" v-model.trim="formInline.description" maxlength="500" placeholder="请输入描述，限制五百字" type="textarea" onkeyup="if(value.length>500)value=value.slice(0,500)" show-word-limit></el-input>
              <span class="form-detail-span" v-else>{{ formInline.description }}</span>
            </el-form-item>
          </div>
        </el-form>
      </div>
      <span slot="footer" class="dialog-footer" v-if="type !== 'detail'">
        <el-button class="sino-button-sure" @click="closeDialog" >取 消</el-button>
        <el-button class="sino-button-sure" type="primary" @click="dictConfigSaveFn">保 存</el-button>
      </span>
    </el-dialog>
  </div>
</template>
<script>
import { submitDictConfig } from '@/utils/api'
export default {
  name: 'addDictConfig',
  props: {
    addDialogShow: {
      type: Boolean,
      default: false
    },
    dialogTitle: {
      type: String,
      default: ''
    },
    rowData: {
      type: Object,
      default: function () {
        return {}
      }
    },
    type: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      formInline: {
        labelName: '',
        dictValue: '',
        description: '',
        sort: 0
      },
      rules: {
        labelName: [
          { required: true, message: '请输入标签名称', trigger: 'blur' },
          { min: 2, max: 50, message: '长度在 2 到 50 个字符', trigger: 'blur' }
        ],
        dictValue: [
          { required: true, message: '请输入键值', trigger: 'blur' },
          { min: 1, max: 50, message: '长度在 1 到 50 个字符', trigger: 'blur' }
        ]
      }
    }
  },
  watch: {},
  mounted() {
    Object.assign(this.formInline, this.rowData)
  },
  methods: {
    // 取消按钮
    closeDialog() {
      this.$emit('configCloseDialog')
    },
    // 确认按钮
    dictConfigSaveFn() {
      this.formInline.sort = (this.formInline.sort === '' || this.formInline.sort == null) ? this.formInline.sort : Number(this.formInline.sort)
      this.$refs.formInline.validate((valid) => {
        if (valid) {
          submitDictConfig(this.formInline).then((res) => {
            const data = res.data
            if (data.code === '200') {
              this.$message({
                message: this.dialogTitle + '配置成功！',
                type: 'success'
              })
              this.$emit('configSure')
            } else {
              this.$message({
                message: data.msg,
                type: 'warning'
              })
            }
          })
        } else {
          console.log('error submit!!')
          return false
        }
      })
    }
  }
}
</script>

<style lang="scss" type="text/css" scoped>
// .main {
//   width: 50%;
//   height: 40px;
//   display: inline-block;
//   line-height: 40px;
// }
.formRow {
  width: 100%;
  display: flex;
  ::v-deep .el-form-item {
    flex: 1;
    display: flex;
  }
  ::v-deep .el-form-item__content {
    flex: 1;
  }
}
</style>
