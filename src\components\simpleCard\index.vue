<template>
  <div ref="boxCard" class="box-card" :style="cstyle">
    <div v-if="showTitle" class="card-title">
      <span class="card-name" :class="{ floorName: floorName }" :style="{ width: $slots && $slots['title-right'] ? '50%' : '70%' }">
        <span v-if="floorName"> {{ floorName ? `${floorName} -` : '' }}</span>
        {{ title }}
      </span>
      <slot name="title-left" />
      <slot name="title-right" />
      <svg-icon v-if="hasExpand" class="right-expand" name="right-expand" @click="$emit('emit-expand')" />
    </div>
    <div v-if="scrollbarHover" ref="cardBody" v-scrollbarHover class="card-body">
      <slot name="content" />
    </div>
    <div v-if="!scrollbarHover" ref="cardBody" class="card-body">
      <slot name="content" />
    </div>
  </div>
</template>
<script>
export default {
  name: 'simpleCard',
  props: {
    floorName: {
      type: String,
      default: ''
    },
    // 标题
    title: {
      type: String,
      default: ''
    },
    showTitle: {
      type: Boolean,
      default: true
    },
    // 是否显示展开按钮 与@emit-expand配合使用
    hasExpand: {
      type: Boolean,
      default: false
    },
    // 自定义样式
    cstyle: {
      type: Object,
      default: () => {}
    },
    // 是否直接显示滚动条
    scrollbarHover: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {}
  },
  mounted() {
    // console.log(this.$slots['title-right']);
  },
  methods: {}
}
</script>
<style lang="scss" scoped>
.box-card {
  background: rgba(53,98,219,.06);
  border-radius: 2px;
  display: flex;
  flex-direction: column;
  .card-title {
    padding: 16px 16px 0 16px;
    position: relative;
    display: flex;
    align-items: center;
    display: flex;
    justify-content: space-between;

    .card-name {
      // padding-left: 10px;
      flex: 1;
      font-size: 16px;
      font-weight: 500;
      color: #fff;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
    .floorName {
      text-overflow: ellipsis;
      white-space: nowrap;
      overflow: hidden;
    }
    .right-expand {
      font-size: 24px;
      cursor: pointer;
    }
  }

  .card-body {
    overflow-y: auto;
    flex: 1;
  }
  .title-left {
    position: absolute;
  }
}
</style>
