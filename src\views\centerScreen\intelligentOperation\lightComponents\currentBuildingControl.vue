<template>
  <div class="currentBuildingControl">
    <div class="building-list-item">
      <p class="item-title">{{infoData.constructName || '-'}}</p>
      <div class="item-content">
        <p class="allNum">总：{{infoData.count || 0}}</p>
        <p class="onNum">{{infoData.openCount || 0}}</p>
        <p class="offNum">{{infoData.closeCount || 0}}</p>
      </div>
    </div>
    <el-table
      class="table-center-transfer"
      :data="tableData"
      height="calc(100% - 4.2rem)"
      :cell-style="$tools.setCell(3)"
      :header-cell-style="$tools.setHeaderCell(3)"
      style="width: 100%"
      @row-click="floorClick"
      element-loading-background="rgba(0, 0, 0, 0.2)"
    >
      <el-table-column align="center" fixed prop="floorName" width="80" show-overflow-tooltip label="楼层"></el-table-column>
      <el-table-column align="center" fixed prop="count" show-overflow-tooltip label="总数"></el-table-column>
      <el-table-column align="center" fixed prop="openCount" show-overflow-tooltip label="开启"></el-table-column>
      <el-table-column align="center" fixed prop="closeCount" show-overflow-tooltip label="关闭"></el-table-column>
      <!-- <el-table-column :key="5" align="center" fixed label="操作">
        <template slot-scope="scope">
          <div>
            <div class="top_type_item" @click.stop="switchOperation(0, scope.row)">
              <span>全关</span>
            </div>
            <div class="top_type_item" @click.stop="switchOperation(1, scope.row)">
              <span>全开</span>
            </div>
          </div>
        </template>
      </el-table-column> -->
    </el-table>
  </div>
</template>

<script>
import { GetFloorLightingList, LightOpenOrClose } from '@/utils/centerScreenApi'
import { mapGetters } from 'vuex'
export default {
  name: 'currentBuildingControl',
  props: {
    requestParams: {
      type: Object,
      default: () => {
        return {}
      }
    },
    infoData: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  data() {
    return {
      tableData: []
    }
  },
  computed: {
    ...mapGetters({
      socketIemcMsgs: 'socketIemcMsgs'
    })
  },
  watch: {
    socketIemcMsgs(data) {
      const projectData = JSON.parse(data)
      // 匹配设备相同的接收消息刷新页面
      if (projectData.projectCode === this.requestParams.projectCode && projectData.data === 'LightRefresh') {
        // 收到消息刷新页面
        console.log('匹配成功', projectData)
        this.getFloorLightingList()
      }
    }
  },
  created() {
    this.getFloorLightingList()
  },
  methods: {
    switchOperation(status, item) {
      const param = {
        type: 4,
        forceSwitch: null,
        outputStatus: status,
        serviceSpaceId: item.floorId
      }
      try {
        window.chrome.webview.hostObjects.sync.bridge.ShowDialog(true)
      } catch (error) {}
      this.$confirm('您正在强制执行开/关操作，当前操作数量N。确认是否执行？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        cancelButtonClass: 'sino-button-sure',
        confirmButtonClass: 'sino-button-sure',
        customClass: 'confirm-box-class',
        type: 'warning'
      }).then(() => {
        try {
          window.chrome.webview.hostObjects.sync.bridge.ShowDialog(false)
        } catch (error) {}
        LightOpenOrClose(param).then(res => {
          if (res.data.code === '200') {
            this.getFloorLightingList()
          } else {
            this.$message.warning(res.data.message)
          }
        })
      }).catch(() => {
        try {
          window.chrome.webview.hostObjects.sync.bridge.ShowDialog(false)
        } catch (error) {}
      })
    },
    floorClick(row) {
      try {
        window.chrome.webview.hostObjects.sync.bridge.GetLightingSsmCode(row.floorId)
      } catch (error) {}
    },
    getFloorLightingList() {
      const newArr = []
      GetFloorLightingList({ projectCode: this.requestParams.projectCode, constructionId: this.requestParams.constructionId, name: this.requestParams.constructionName }).then((res) => {
        if (res.data.code === '200' && res.data.data.length) {
          this.tableData = res.data.data
          this.tableData.forEach(item => {
            newArr.push(item.spaceIds)
          })
          try {
            window.chrome.webview.hostObjects.sync.bridge.SetLightingBackgroundColor(newArr.join(','))
          } catch (error) {}
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.currentBuildingControl{
  background: rgba(1, 11, 59, .5);
  height: calc(95vh - 26rem - 3.2rem);
  /* overflow: auto; */
  margin: 10px;
  .building-list-item{
    padding: 12px 19px 12px 19px;
    .item-title{
      padding-left: 25px;
      background: url('~@/assets/images/center/location.png') no-repeat left center / 16px 16px;
    }
    .item-content{
      padding-top: 4px;
      display: flex;
      align-content: center;
      justify-content: space-around;
      font-size: 16px;
      font-weight: 500;
      color: #FFFFFF;
      line-height: 22px;
      .allNum{
        color: #FFE3A6;
      }
      .onNum{
        padding-left: 30px;
        background: url('~@/assets/images/center/on-light.png') no-repeat left center / 17px 22px;
      }
      .offNum{
        padding-left: 30px;
        background: url('~@/assets/images/center/off-light.png') no-repeat left center / 17px 22px;
      }
    }
  }
  ::v-deep .el-table {
    border: none !important;
    .el-table__header thead th {
      background: rgba(15, 34, 103, 0.45) !important;
      padding: 8px 2px !important;
      .cell{
        padding: 0px;
      }
    }
    .el-table__body tr {
      background: rgba(17, 27, 77, 0.45) !important;
    }
    .el-table__row .el-table__cell{
      padding: 8px 0px !important;
    }
  }
  .top_type_item {
    display: inline-block;
    width: 2.5rem;
    margin: 3px 2%;
    height: 1.5rem;
    line-height: 1.5rem;
    text-align: center;
    font-size: 14px;
    font-family: PingFang-SC-Medium, PingFang-SC;
    color: #93bdff;
    background: url('~@/assets/images/center/block-bg.png') no-repeat;
    background-size: 100% 100%;
    cursor: pointer;
    &:hover {
      color: #fff;
      background: url('~@/assets/images/center/block-bg-active.png') no-repeat;
      background-size: 100% 100%;
    }
  }
}
</style>
