<template>
  <div class="main">
    <el-dialog v-dialogDrag :modal="false" :visible.sync="dialogShow" custom-class="travelRecordDialog main" append-to-body :close-on-click-modal="false" :before-close="closeDialog">
      <template slot="title">
        <span class="dialog-title">通行记录</span>
      </template>
      <div class="dialog-content">
        <div class="search-box">
          <div class="search-form">
            <el-input v-model="searchParam.personName" placeholder="姓名" style="width: 150px;"></el-input>
            <el-input v-model="searchParam.deviceCode" placeholder="门禁ID" style="width: 150px;"></el-input>
            <!-- <el-input v-model="searchParam.deviceName" placeholder="门禁名称" style="width: 150px;"></el-input> -->
            <el-date-picker
              v-model="dateRange"
              type="datetimerange"
              unlink-panels
              popper-class="date-style"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              value-format="yyyy-MM-dd HH:mm:ss"
              :default-time="['00:00:00', '23:59:59']"
            >
            </el-date-picker>
            <el-select v-model="searchParam.modeIndex" placeholder="开门方式" clearable>
              <el-option v-for="item in openModeList" :key="item.value" :label="item.name" :value="item.value"> </el-option>
            </el-select>
            <el-select v-model="searchParam.inOrOut" placeholder="进出类型" clearable style="margin: 0;">
              <el-option v-for="item in inOrOutList" :key="item.value" :label="item.name" :value="item.value"> </el-option>
            </el-select>
          </div>
          <div class="search-btn">
            <el-button @click="resetForm">重置</el-button>
            <el-button @click="searchForm">查询</el-button>
          </div>
        </div>
        <div class="main-content">
          <el-table
            v-loading="tableLoading"
            :data="tableData"
            height="calc(100%)"
            :cell-style="$tools.setCell(3)"
            :header-cell-style="{...$tools.setHeaderCell(3), fontSize: '12px'}"
            style="width: 100%"
            element-loading-background="rgba(0, 0, 0, 0.2)"
          >
            <el-table-column v-for="(column, index) in tableColumn" :key="index" :prop="column.prop" :label="column.label" :min-width="column.minWidth" :sortable="column.sortable || false" show-overflow-tooltip>
              <template slot-scope="scope">
                <table-render v-if="column.render" :sc="scope" :row="scope.row" :render="column.render"></table-render>
                <div v-else-if="!column.formatter">
                  {{ scope.row[column.prop] }}
                </div>
                <div v-else>
                  {{ column.formatter(scope.row) }}
                </div>
              </template>
            </el-table-column>
            <el-table-column prop="picture" label="开门图片" width="100" show-overflow-tooltip>
              <template slot-scope="scope">
                <img style="width:48px; height:48px;cursor: pointer;" :src="scope.row.picture" @click="() => viewImage(scope.row.picture)"/>
              </template>
            </el-table-column>
          </el-table>
        </div>
        <el-pagination
          :current-page="currentPage"
          :page-sizes="[15, 20, 30, 40]"
          :page-size="pageSize"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
          class="pagination"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        ></el-pagination>
      </div>
    </el-dialog>
    <el-image-viewer v-if="showViewer" :on-close="() => showViewer = false" :url-list="iconPathList" />
  </div>
</template>

<script>
import ElImageViewer from 'element-ui/packages/image/src/image-viewer'
import {GetAccessRecordListByPage} from '@/utils/centerScreenApi'
import { monitorTypeList } from '@/assets/common/dict.js'
import moment from 'moment'
moment.locale('zh-cn')
export default {
  name: 'travelRecord',
  components: {
    ElImageViewer
  },
  props: {
    dialogShow: {
      type: Boolean,
      default: false
    },
    selectParam: {
      type: Object,
      default: () => { }
    }
  },
  data() {
    return {
      searchParam: {
        personName: '', // 姓名
        deviceCode: '', // 门禁id
        deviceName: '', // 门禁名称
        modeIndex: '', // 开门方式
        inOrOut: '' // 进出类型
      },
      dateRange: [moment().format('YYYY-MM-DD') + ' 00:00:00', moment().format('YYYY-MM-DD') + ' 23:59:59'],
      tableLoading: false,
      tableColumn: [
        {
          prop: 'recordId',
          label: '通行编码'
        },
        {
          prop: 'personName',
          label: '姓名',
          minWidth: 100
        },
        // {
        //   prop: '',
        //   label: '手机号'
        // },
        // {
        //   prop: '',
        //   label: '卡号'
        // },
        {
          prop: 'personType',
          label: '人员类型'
        },
        {
          prop: 'departmentName',
          label: '部门'
        },
        {
          prop: 'deviceName',
          label: '门禁名称'
        },
        {
          prop: 'deviceCode',
          label: '门禁ID'
        },
        {
          prop: 'passTime',
          label: '开门时间',
          sortable: true,
          minWidth: 120
        },
        {
          prop: 'modeIndex',
          label: '开门方式'
        },
        {
          prop: 'inorout',
          label: '进出类型'
        }
      ],
      tableData: [],
      currentPage: 1,
      pageSize: 15,
      total: 0,
      openModeList: [
        {name: '人证核验', value: 1},
        {name: '刷脸', value: 256},
        {name: '刷卡', value: 16},
        {name: '人证核验证号码', value: 17},
        {name: '刷卡+刷脸', value: 272},
        {name: '温度核验', value: 512}
      ], // 开门方式
      inOrOutList: [
        {name: '进', value: 1},
        {name: '出', value: 2}
      ], // 进出类型
      iconPathList: [], // 图片列表
      showViewer: false // 图片预览
    }
  },
  computed: {

  },
  created() {
    this.searchForm()
  },
  methods: {
    // 查看图片
    viewImage(url) {
      this.iconPathList = [url]
      this.showViewer = true
    },
    getDataList() {
      let params = {
        ...this.searchParam,
        startTime: this.dateRange[0],
        endTime: this.dateRange[1],
        page: this.currentPage,
        pageSize: this.pageSize,
        projectCode: monitorTypeList.find((e) => e.name == '门禁设备').projectCode
        // ...this.selectParam
      }
      this.tableLoading = true
      GetAccessRecordListByPage(params).then(res => {
        this.tableLoading = false
        if (res.data.code == 200) {
          this.tableData = res.data.data.list
          this.total = res.data.data.count
        }
      })
    },
    // 查询
    searchForm() {
      this.currentPage = 1
      this.getDataList()
    },
    // 重置查询
    resetForm() {
      this.dateRange = [moment().format('YYYY-MM-DD') + ' 00:00:00', moment().format('YYYY-MM-DD') + ' 23:59:59']
      Object.assign(this.searchParam, {
        personName: '', // 姓名
        deviceCode: '', // 门禁id
        deviceName: '', // 门禁名称
        modeIndex: '', // 开门方式
        inOrOut: '' // 进出类型
      })
      this.currentPage = 1
      this.getDataList()
    },
    handleSizeChange(val) {
      this.pageSize = val
      this.getDataList()
    },
    handleCurrentChange(val) {
      this.currentPage = val
      this.getDataList()
    },
    // 取消按钮
    closeDialog() {
      this.$emit('travelRecordClose')
    }
  }
}
</script>

<style lang="scss" scoped>
::v-deep .travelRecordDialog {
  z-index: 10;
  width: 70%;
  height: 84vh;
  margin-top: 6vh !important;
  // border: 1px solid #5996f9;
  background-color: transparent !important;
  pointer-events: auto;
  background-image: url('@/assets/images/table-bg.png');
  background-repeat: no-repeat;
  background-size: 100% 100%;
  box-shadow: none;
  .el-dialog__headerbtn {
    transform: translateX(-36px);
    width: 25px;
    height: 25px;
    background-image: url('@/assets/images/close.png');
    background-repeat: no-repeat;
    background-size: 100% 100%;
    .el-dialog__close::before {
      display: none;
    }
  }
  .dialog-title {
    display: inline-block;
    width: 100%;
    text-align: center;
    transform: translateY(-6px);
    color: #cbdeed;
  }
  .dialog-title::before {
    display: none;
  }
  .el-dialog__header {
    background-color: transparent;
  }
  .el-dialog__body {
    height: calc(100% - 60px);
    max-height: fit-content;
    overflow-y: hidden;
    background-color: transparent;
    padding: 0px 80px;
  }
  .dialog-content {
    width: 100%;
    height: 96%;
    background-color: transparent;
    display: flex;
    flex-direction: column;
    .search-box {
      padding: 8px 0px 24px 0px;
      display: flex;
      flex-direction: column;
      width: 100%;
      .search-form {
        display: flex;
        flex-wrap: wrap;
        align-items: center;
        width: 100%;
        .el-input {
          margin-right: 16px;
          .el-input__inner {
            height: 35px !important;
            border: 1px solid rgba(133, 145, 206, 0.5);
            border-radius: 4px;
          }
        }
      }
      .search-btn {
        margin-top: 16px;
        text-align: right;
      }
      .el-button {
        background-image: url("@/assets/images/btn.png");
        background-repeat: no-repeat;
        background-size: 100% 100%;
        height: 35px;
      }
      .el-select {
        flex: 1;
        margin-right: 16px;
        .el-input {
          .el-input__inner {
            height: 35px !important;
            border: 1px solid rgba(133, 145, 206, 0.5);
            border-radius: 4px;
          }
          .el-input__icon {
            line-height: 35px;
          }
        }
      }
      .el-date-editor {
        margin-right: 16px;
        width: 380px;
        height: 35px;
        background-color: #14233e;
        border-color: rgba(133, 145, 206, 0.5);
        .el-input__icon {
          transform: translateY(-2px);
        }
        .el-range-input {
          background-color: #14233e;
          color: rgba(255, 255, 255, 0.8);
        }
        .el-range-separator {
          color: rgba(255, 255, 255, 0.8);
          transform: translateY(-2px);
        }
      }
    }
    .main-content {
      flex: 1;
      overflow: hidden;
      .el-table {
        .el-table__fixed-right-patch {
          background: transparent;
          border-color: transparent;
        }
        .el-table__fixed-right::before, .el-table__fixed::before {
          background: transparent;
        }
      }
    }
  }
}
</style>
<style lang="scss">
.el-dialog__wrapper {
  z-index: 2000 !important;
}
</style>
