<!--
 * @Author: ycw
 * @Date: 2020-05-26 11:30:06
 * @LastEditors: ycw
 * @LastEditTime: 2020-06-03 14:07:34
 * @Description:
-->
<template>
  <div class="sino-aside-menu">
    <el-menu class="menu-list" :collapse="$store.state.isCollapse" :default-active="activeIndex" ref="menu" :default-openeds="activeArr" unique-opened>
      <template v-for="item in menuList">
        <el-submenu :index="item.menuId" class="first-level" :key="item.menuId">
          <template slot="title">
            <i class="iconfont iconimg" style="margin-right: 2%; margin-left: 4%" :class="item.icon"></i>
            <span style="margin-left: 1%">{{ item.menuName }}</span>
          </template>
          <template v-for="itemChild in item.list" class="second-level">
            <el-submenu :index="itemChild.menuId" :key="itemChild.menuId">
              <template slot="title">
                <i class="iconfont iconimg" style="margin-right: 2%; margin-left: 14%">&#xe64d;</i>
                <span class="second-level">{{ itemChild.menuName }}</span>
              </template>
              <!-- 三级菜单 -->
              <el-menu-item v-for="itemChild_Child in itemChild.list" :index="itemChild_Child.menuId" :key="itemChild_Child.menuId" @click="activeClick(itemChild_Child)">
                <span slot="title" style="margin-left: 30%; font-size: 16px; font-weight: 400; z-index: 2">{{ itemChild_Child.menuName }}</span>
              </el-menu-item>
            </el-submenu>
          </template>
        </el-submenu>
      </template>
    </el-menu>
  </div>
</template>
<script>
import axios from 'axios'
export default {
  data() {
    return {
      activeIndex: '',
      activeArr: [],
      menuList: []
    }
  },
  created() {
    axios.get('/menu').then((res) => {
      this.menuList = this.$tools.transData(res.data.data, 'menuId', 'parentId', 'list')
    })
  },
  methods: {
    activeClick(option) {
      this.$router.push('/' + option.menuHref)
      this.$store.commit('pullMeunItem', option)
    }
  }
}
</script>
<style lang="scss"></style>
