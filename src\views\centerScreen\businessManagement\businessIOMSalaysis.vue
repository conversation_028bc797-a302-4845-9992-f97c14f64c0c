<template>
  <div class="content">
    <div class="title">
      <div @click="backToWPFhome()"><i class="el-icon-arrow-left"></i>返回</div>
    </div>
    <div class="content-left">
      <div class="left-top">
        <div class="bg-title" style="padding: 0 3rem">区域综合维修分析</div>
        <div class="bg-content">
          <div v-if="repaireAnalysis" class="center-center">暂无数据</div>
          <div v-else style="width: 100%; height: 100%">
            <div id="repairAnalysisEcharts" v-loading="repairLoading" element-loading-background="rgba(0, 0, 0, 0.2)"></div>
          </div>
        </div>
      </div>
      <div class="left-bottom">
        <div class="bg-title" style="padding: 0 3rem">区域综合维修事项分析</div>
        <div class="bg-content">
          <div v-if="repaireItemAnalysis" class="center-center">暂无数据</div>
          <div v-else style="width: 100%; height: 100%">
            <div id="repairAnalysisItemEcharts" v-loading="repairItemLoading" element-loading-background="rgba(0, 0, 0, 0.2)"></div>
            <div class="pie_background" style="height: 80%; aspect-ratio: 1/1"></div>
          </div>
        </div>
      </div>
    </div>
    <div class="content-right"></div>
    <!-- <el-button class="sino-button-sure" @click="workOrderListShowChange">showDialog</el-button> -->
    <template v-if="workOrderListShow">
      <workOrderCenterList
        ref="workOrderCenterList"
        :ssmCodes="ssmCodes"
        :location="location"
        :ssmType="ssmType"
        :dialogShow="workOrderListShow"
        @configCloseDialog="configCloseDialog"
      ></workOrderCenterList>
    </template>
  </div>
</template>
<script>
import * as echarts from 'echarts'
import { getAreaMaintenanceInfo, getAreaMaintenanceMatterInfo } from '@/utils/centerScreenApi'
import workOrderCenterList from './component/workOrderCenterList.vue'
export default {
  name: 'businessIOMSalaysis',
  components: {
    workOrderCenterList
  },
  data() {
    return {
      workOrderListShow: false,
      repaireAnalysis: false,
      repaireItemAnalysis: false,
      repairLoading: false,
      repairItemLoading: false,
      location: '',
      ssmType: '',
      ssmCodes: ''
    }
  },
  mounted() {
    // 初始化 获取定位信息
    if (Object.hasOwn(this.$route.query, 'localtion') || Object.hasOwn(this.$route.query, 'ssmType') || Object.hasOwn(this.$route.query, 'ssmCodes')) {
      this.location = this.$route.query.localtion || ''
      this.ssmType = this.$route.query.ssmType || ''
      this.ssmCodes = this.$route.query.ssmCodes || ''
    }
    // this.location = '0100108'
    // this.ssmType = '5'
    // this.ssmCodes = '#,1574997196330250241,1574997196833566721,1574997197630484482'
    this.getRepairAnalysisData()
    this.getRepairAnalysisItemData()
    // this.setWPFborder()
    try {
      window.chrome.webview.addEventListener('message', (event) => {
        const data = JSON.parse(event.data)
        this.location = data.localtion || ''
        this.ssmType = data.ssmType || ''
        if (data.type === 'area') {
          this.ssmCodes = data.ssmCodes || ''
          this.getRepairAnalysisData()
          this.getRepairAnalysisItemData()
        } else if (data.type === 'tag') {
          this.ssmCodes = data.ssmCodes || ''
          this.workOrderListShow = true
          this.$nextTick(() => {
            this.$refs.workOrderCenterList.getWorkOrderTableData()
          })
          window.chrome.webview.hostObjects.sync.bridge.ShowTable(true)
        }
      })
    } catch (error) {}
    try {
      this.workOrderListShow = false
      // window.chrome.webview.hostObjects.sync.bridge.ShowTable(this.workOrderListShow)
    } catch (error) {}
  },
  methods: {
    getRepairAnalysisData() {
      this.repairLoading = true
      const spatialId = this.ssmCodes.split(',').at(-1) || ''
      getAreaMaintenanceInfo({ spatialId: spatialId }).then((res) => {
        const data = res.data
        this.repairLoading = false
        if (data.code === '200') {
          const arr = data.data.list
          if (arr.length) {
            this.repaireAnalysis = false
            const deptName = Array.from(arr, ({ deptName }) => deptName)
            const totalPrice = Array.from(arr, ({ totalPrice }) => totalPrice)
            const workCount = Array.from(arr, ({ workCount }) => workCount)
            this.$nextTick(() => {
              this.setRepairAnalysisEcharts(deptName, totalPrice, workCount)
            })
          } else {
            this.repaireAnalysis = true
          }
        }
      })
    },
    setRepairAnalysisEcharts(deptName, totalPrice, workCount) {
      const option = {
        backgroundColor: '',
        tooltip: {
          trigger: 'axis',
          backgroundColor: '#0D2169',
          borderColor: '#727382',
          textStyle: {
            color: '#fff'
          },
          axisPointer: {
            type: 'shadow'
          }
        },
        grid: {
          x: '20%',
          width: '75%',
          y: '12%'
        },
        xAxis: [
          {
            name: '维修数量（件）',
            nameGap: 25,
            nameLocation: 'middle',
            nameTextStyle: {
              color: '#878EA9'
            },
            axisTick: {
              show: false
            },
            axisLabel: {
              textStyle: {
                color: '#878EA9'
              }
            },
            splitLine: {
              lineStyle: {
                color: ['#314A89'],
                width: 1,
                type: 'solid'
              }
            }
          },
          {
            name: '耗材成本（元）',
            nameGap: 25,
            nameLocation: 'middle',
            nameTextStyle: {
              color: '#878EA9'
            },
            axisTick: {
              show: false
            },
            axisLabel: {
              textStyle: {
                color: '#878EA9'
              }
            },
            splitLine: {
              show: false
            }
          }
        ],
        yAxis: {
          boundaryGap: false,
          type: 'category',
          axisLine: {
            lineStyle: {
              type: 'solid',
              color: '#3862B7',
              width: '1'
            }
          },
          axisLabel: {
            show: true,
            textStyle: {
              color: '#878EA9'
            },
            formatter: function (val) {
              var strs = val.split('') // 字符串数组
              var str = ''
              // strs循环 forEach 每八个字符增加换行符
              strs.forEach((item, index) => {
                if (index % 8 === 0 && index !== 0) {
                  str += '\n'
                }
                str += item
              })
              return str
            }
          },
          axisTick: {
            show: false
          },
          data: deptName
        },
        series: [
          {
            name: '耗材成本',
            type: 'line',
            symbolSize: 8,
            xAxisIndex: 1,
            itemStyle: {
              normal: {
                color: '#FFE3A6'
              }
            },
            lineStyle: {
              normal: {
                color: '#FFE3A6',
                width: 2
              }
            },
            data: totalPrice
          },
          {
            name: '维修数量',
            type: 'bar',
            barWidth: 7,
            itemStyle: {
              normal: {
                color: new echarts.graphic.LinearGradient(0, 0, 0.8, 0, [
                  { offset: 0, color: '#061444 ' },
                  { offset: 1, color: '#1FFAFF' }
                ])
              }
            },
            data: workCount
          }
        ],
        dataZoom: [
          {
            yAxisIndex: [0],
            orient: 'vertical',
            show: true,
            type: 'slider',
            start: 100,
            width: 5,
            left: '96%',
            borderColor: 'rgba(43,48,67,.1)',
            fillerColor: '#6580b8',
            zoomLock: true,
            showDataShadow: false,
            backgroundColor: 'rgba(43,48,67,.1)',
            showDetail: false,
            // realtime: true,
            filterMode: 'filter',
            handleIcon: 'M-9.35,34.56V42m0-40V9.5m-2,0h4a2,2,0,0,1,2,2v21a2,2,0,0,1-2,2h-4a2,2,0,0,1-2-2v-21A2,2,0,0,1-11.35,9.5Z',
            handleStyle: {
              color: '#6580b8',
              borderColor: '#6580b8'
            },
            maxValueSpan: 7,
            minValueSpan: 7,
            brushSelect: false
          },
          {
            type: 'inside',
            show: false,
            yAxisIndex: [0, 1],
            height: 8
          }
        ]
      }
      const getchart = echarts.init(document.getElementById('repairAnalysisEcharts'))
      getchart.clear()
      getchart.setOption(option)
      // 随着屏幕大小调节图表
      window.addEventListener('resize', () => {
        getchart.resize()
      })
    },
    getRepairAnalysisItemData() {
      this.repairItemLoading = true
      const spatialId = this.ssmCodes.split(',').at(-1) || ''
      getAreaMaintenanceMatterInfo({ spatialId: spatialId }).then((res) => {
        const data = res.data
        this.repairItemLoading = false
        if (data.code === '200') {
          const dataList = data.data.list
          if (dataList.length) {
            this.repaireItemAnalysis = false
            const dataArr = dataList.map((e) => {
              return {
                name: e.matterName,
                value: e.workCount
              }
            })
            this.$nextTick(() => {
              this.setRepairAnalysisItemEcharts(dataArr)
            })
          } else {
            this.repaireItemAnalysis = true
          }
        }
      })
    },
    setRepairAnalysisItemEcharts(arr) {
      const getchart = echarts.init(document.getElementById('repairAnalysisItemEcharts'))
      const valueList = Array.from(arr, (item) => item.value)
      const nameList = Array.from(arr, (item) => item.name)
      const data = []
      var color = ['rgba(31, 250, 255, 0.3)', 'rgba(255, 227, 166, 0.28)', 'rgba(147, 130, 255, 0.26)', 'rgba(0, 248, 114, 0.24)', 'rgba(46, 119, 251, 0.3)']
      var borderColor = ['rgba(31, 250, 255, 1)', 'rgba(255, 227, 166, 1)', 'rgba(147, 130, 255, 1)', 'rgba(0, 248, 114, 1)', 'rgba(46, 119, 251, 1)']
      for (var i = 0; i < nameList.length; i++) {
        const randomRgbColor = this.$tools.randomRgbColor('array')
        data.push({
          value: valueList[i],
          name: nameList[i],
          itemStyle: {
            normal: {
              borderWidth: 1,
              shadowBlur: 200,
              borderColor: borderColor[i] ?? randomRgbColor[1],
              color: color[i] ?? randomRgbColor[0],
              label: {
                normal: { show: true }
              }
            }
          }
        })
      }
      var seriesObj = [
        {
          name: '',
          type: 'pie',
          clockWise: false,
          center: ['50%', '40%'],
          radius: ['40%', '55%'],
          hoverAnimation: true,
          label: {
            normal: {
              show: false,
              position: 'center',
              formatter: '{value|{c}}\n{label|{b}}',
              rich: {
                value: {
                  padding: 5,
                  align: 'center',
                  verticalAlign: 'middle',
                  fontSize: 20
                },
                label: {
                  align: 'center',
                  verticalAlign: 'middle',
                  fontSize: 12
                }
              }
            },
            emphasis: {
              show: true,
              textStyle: {
                fontSize: '12',
                color: '#fff'
              }
            }
          },
          data: data
        }
      ]
      const option = {
        backgroundColor: 'rgba(128, 128, 128, 0)',
        tooltip: {
          show: false
        },
        legend: {
          orient: 'horizontal',
          type: 'scroll',
          left: 'center',
          bottom: '15',
          data: nameList,
          itemWidth: 8,
          itemHeight: 8,
          itemGap: 16,
          textStyle: {
            color: '#B3C2DD' //  字体颜色
          }
        },
        toolbox: {
          show: false
        },
        series: seriesObj
      }
      getchart.clear()
      getchart.setOption(option)
      // 随着屏幕大小调节图表
      window.addEventListener('resize', () => {
        getchart.resize()
      })
      getchart.dispatchAction({
        type: 'highlight',
        seriesIndex: 0,
        dataIndex: 0
      })
      getchart.on('mouseover', (e) => {
        // 取消默认高亮
        getchart.dispatchAction({
          type: 'downplay',
          seriesIndex: 0,
          dataIndex: 0
        })
      })
      // 鼠标移出后默认高亮
      getchart.on('mouseout', (e) => {
        getchart.dispatchAction({
          type: 'highlight',
          seriesIndex: 0,
          dataIndex: 0
        })
      })
    },
    configCloseDialog() {
      this.workOrderListShow = false
      try {
        window.chrome.webview.hostObjects.sync.bridge.ShowTable(false)
      } catch (error) {}
    },
    setWPFborder() {
      const json = {
        left: 22
      }
      window.chrome.webview.hostObjects.sync.bridge.SetLocation(JSON.stringify(json))
    },
    backToWPFhome() {
      window.chrome.webview.hostObjects.sync.bridge.Back('businessIOMS')
    },
    workOrderListShowChange() {
      this.workOrderListShow = true
      this.$nextTick(() => {
        this.$refs.workOrderCenterList.getWorkOrderTableData()
      })
      // window.chrome.webview.hostObjects.sync.bridge.ShowTable(true)
    }
  }
}
</script>
<style lang="scss" scoped>
.content {
  width: 100%;
  height: 100%;
  position: relative;
  display: flex;
  .title {
    height: 30px;
    position: relative;
    div {
      cursor: pointer;
      position: absolute;
      top: 5px;
      left: 0;
      width: 80px;
      height: 30px;
      line-height: 30px;
      text-align: center;
      color: #618ad3;
      font-size: 0.875rem;
      white-space: nowrap;
      background-image: url(../../../assets/images/peace/btn-back.png);
      background-size: 100% 100%;
      background-repeat: no-repeat;
    }
  }
  .content-left {
    width: 20%;
    height: calc(100% - 35px);
    padding-top: 35px;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
  }
  .left-top {
    width: 100%;
    height: calc(50% - 5px);
    background: red;
    background: url('~@/assets/images/center/ioms-analysis-bg.png') no-repeat;
    background-size: 100% 100%;
  }
  .left-bottom {
    width: 100%;
    height: 50%;
    background: red;
    background: url('~@/assets/images/center/ioms-analysis-bg.png') no-repeat;
    background-size: 100% 100%;
  }
  .bg-title {
    margin-top: 5px;
    // height: 23%;
    height: 2.8rem;
    line-height: 2.8rem;
    color: #d4e3f9;
    padding: 0 5rem;
    font-family: TRENDS;
    /*设置为伸缩容器*/
    display: -webkit-box;
    display: -moz-box;
    display: -ms-flexbox;
    display: -webkit-flex;
    display: flex;
    /*垂直居中*/
    -webkit-box-align: center; /*旧版本*/
    -moz-box-align: center; /*旧版本*/
    -ms-flex-align: center; /*混合版本*/
    -webkit-align-items: center; /*新版本*/
    align-items: center; /*新版本*/
    justify-content: space-between;
  }
  .bg-content {
    position: relative;
    box-sizing: border-box;
    padding: 10px 10px 15px 10px;
    width: 100%;
    height: calc(100% - 2.5rem);
    #repairAnalysisEcharts,
    #repairAnalysisItemEcharts {
      width: 100%;
      height: 100%;
      z-index: 2;
    }
  }
}
</style>
