<!-- 管线管理 -->
<template>
  <div class="pipelineManage">
    <div class="el-dialog right-content">
      <div class="bg-title el-dialog__header">
        <span>{{ roomData.title }}</span>
      </div>
      <div class="bg-content room-info-box">
        <div class="sys-box">
          <pipeline-component ref="pipelineComponent" :roomData="roomData" />
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import pipelineComponent from './sysTypeComponent/pipelineComponent'
export default {
  name: 'pipelineManage',
  components: { pipelineComponent },
  data() {
    return {
      roomData: {
        title: '',
        modelCode: ''
      }
    }
  },
  computed: {},
  created() {},
  mounted() {
    if (this.$route.query.areaData) {
      this.roomData = JSON.parse(this.$route.query.areaData)
    }
    try {
      window.chrome.webview.addEventListener('message', (event) => {
        this.roomData = JSON.parse(event.data)
        this.$nextTick(() => {
          this.$refs.pipelineComponent.getCategoryList()
        })
      })
    } catch (errpr) {}
  },
  methods: {}
}
</script>

<style lang="scss" scoped>
.pipelineManage {
  width: 100%;
  height: 100%;
  margin-top: 2%;
  max-height: 95%;
  .right-content {
    width: 22%;
    // width: 100%;
    height: 100%;
    margin: 0 0 3.125rem auto;
    background: url('~@/assets/images/center/bg-80.png') no-repeat;
    background-size: 100% 100%;
    box-sizing: border-box;
    padding: 10px;
    position: relative;
    .bg-title {
      height: 2.8rem;
      line-height: 2.8rem;
      padding: 0 10px 0 2.2rem;
      color: #dceaff;
      font-family: TRENDS;
      // 不可被选中
      -webkit-user-select: none;
      display: flex;
      span {
        width: calc(100% - 2.2rem);
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
      i {
        float: right;
        line-height: 2.8rem;
        font-size: 1.5rem;
        cursor: pointer;
      }
    }
    .bg-content {
      position: relative;
      box-sizing: border-box;
      padding: 10px 2px 2px 2px;
      width: 100%;
      height: calc(100% - 3rem);
    }
    .room-info-box {
      color: #fff;
      display: flex;
      flex-direction: column;
      .sys-box {
        width: 100%;
        height: 100%;
        // flex: 1;
        .sys-box-content {
          width: 100%;
          height: 100%;
          padding: 10px;
          box-sizing: border-box;
        }
        // height: calc(100% - 24px);
      }
    }
  }
}
</style>
