<template>
  <div class="left-content-item">
    <CardTitle title="科室接诊量排行" position="left" />
    <div v-loading="loading" class="card-content">
      <ContentList :list="visitDocList" :option="{name: 'dept<PERSON>lia<PERSON>', value: 'zg', unit:'人/次'}" />
    </div>
  </div>
</template>
<script>
import CardTitle from './title'
import ContentList from './contentList'
import { deptconsultation } from '@/utils/runOverviewSJY'
export default {
  components: {
    CardTitle,
    ContentList
  },
  props: {
    date: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      loading: false,
      visitDocList: [

      ]
    }
  },
  watch: {
    date: {
      handler() {
        this.getList()
      },
      immediate: true,
      deep: true
    }
  },
  mounted() {
  },
  methods: {
    getList () {
      this.loading = true
      deptconsultation(this.date).then(res => {
        if (res.data.code == 200) {
          this.visitDocList = res.data.data
        }
      }).catch(() => {
        this.$message.error('获取科室接诊量排行失败')
      }).finally(() => {
        this.loading = false
      })
    }
  }
}
</script>
<style lang="scss" scoped>
.left-content-item {
  width: 100%;
  height: calc(100% / 3 - 7px);
  background: url("~@/assets/images/qhdsys/new-card-bg.png") no-repeat;
  background-size: 100% 100%;
  margin-bottom: 0;
  .card-content {
    height: calc(100% - 36px);
    width: 100%;
    padding: 16px;
  }
}
</style>
