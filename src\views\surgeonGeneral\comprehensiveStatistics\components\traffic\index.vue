<template>
  <div class="echarts-bottomCenter">
    <BgTitle showMore  @moreClick="showDetail">
      院区人数分析
    </BgTitle>
    <div class="bg-content">
      <div class="top">
        <div v-for="(item, index) in list" :key="index" class="item">
          <img src="@/assets/images/comprehensiveStatistics/userNum.png" width="50" height="56">
          <div style="width: calc(100% - 50px);height: 56px;">
            <div class="title">{{ item.name }}</div>
            <div class="value" :style="{color: item.color}">{{item.value}}</div>
          </div>
        </div>
      </div>
      <div id="floorNumStatiscics" class="bottom">
        <ContentList :list="dataList" :option="{valueWidth:'60px', nameWidth: '60px'}" chartName="楼层人数统计">
          <template #filter>
            <el-dropdown trigger="click" @command="typeChange">
              <span class="el-dropdown-link" style="color: #FFFFFF;">
                {{ selectList.find((v) => v.value == orderType)?.name ?? "" }}
                <i class="el-icon-caret-bottom"></i>
              </span>
              <el-dropdown-menu slot="dropdown">
                <el-dropdown-item
                  v-for="item in selectList"
                  :key="item.value"
                  :command="item.value"
                  :class="{ isBjxl: orderType == item.value }"
                >
                  {{ item.name }}
                </el-dropdown-item>
              </el-dropdown-menu>
            </el-dropdown>
          </template>
        </ContentList>
      </div>
    </div>
    <TrafficList v-if="visible" :visible="visible" @close="visible = false" />
  </div>
</template>
<script>
import BgTitle from '../../components/common/bgTitle'
import ContentList from '../../components/common/contentList'
import TrafficList from './detail.vue'
import { personFlowList, getAssemble} from '@/utils/comprehensiveStatistics.js'
export default {
  components: {
    BgTitle,
    ContentList,
    TrafficList
  },
  data() {
    return {
      list: [
        { key: 'enterNum', name: '进入人次', value: 0, color: '#FFCA64' },
        { key: 'leaveNum', name: '离开人次', value: 0, color: '#FF2D55' }
      ],
      orderType: 1,
      selectList: [
        { value: 1, name: '进入人次' },
        { value: 2, name: '离开人次' }
      ],
      dataList: [

      ],
      visible: false

    }
  },
  mounted () {
    this.getTopData()
    this.getData()
  },
  methods: {
    showDetail (data) {
      this.visible = true
    },
    typeChange (val) {
      this.orderType = val
      this.getData()
    },
    getTopData() {
      getAssemble({type: 1}).then((res) => {
        if (res.data.code == 200) {
          this.list.forEach(el => {
            el.value = res.data.data[el.key]
          })
        }
      })
    },
    getData() {
      let params = {
        spaceId: ''
      }
      personFlowList(params).then((res) => {
        if (res.data.code == 200) {
          this.dataList = res.data.data.map((item) => {
            return {
              name: item.regionName,
              value: this.orderType == 1 ? item.enterNum : item.leaveNum
            }
          })
        }
      })
    }
  }
}
</script>
<style lang="scss" scoped>
.echarts-bottomCenter{
  height: 100%;
  width: calc(33% - 8px);
  background: url("~@/assets/images/bg-content1.png") no-repeat;
  background-size: 100% 100%;
  margin-top: 16px;
  box-sizing: border-box;
  .bg-content{
    padding: 16px;
    box-sizing: border-box;
    width: 100%;
    height: calc(100% - 44px);
  }
  .top{
    background: rgba(133,145,206,0.05);
    height: 92px;
    width: 100%;
    display: flex;
    justify-content: space-between;
    padding: 18px;
    .item{
      width: 100%;
      height: 100%;
      display: flex;
      align-items: center;
      padding: 0 44px;
      .title{
          height: 50%;
          width: 100%;
          padding-bottom: 5px;
          color: #B0E3FA;
          font-size: 14px;
          display: flex;
          align-items: center; /* 垂直居中 */
          justify-content: center; /* 水平居中 */
        }
        .value {
          height: 50%;
          background: url("~@/assets/images/comprehensiveStatistics/item_bg.png")
            no-repeat;
          background-size: 100% 100%;
          color: #ffca64;
          display: flex;
          align-items: center; /* 垂直居中 */
          justify-content: center; /* 水平居中 */
          font-size: 20px;
        }
    }
  }
  #floorNumStatiscics{
    height: calc(100% - 92px);
    margin-top: 10px;
    width: 100%;
  }
}
</style>
