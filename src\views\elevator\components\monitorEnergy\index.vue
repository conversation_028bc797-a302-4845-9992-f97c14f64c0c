<template>
  <div class="energyComponent">
    <div class="energy-main">
      <div class="search-data">
        <el-dropdown trigger="click" @command="dataTypeCommand">
          <span class="el-dropdown-link"> {{ dataTypeList.find((v) => v.value == statisticalType)?.name ?? '' }} <i class="el-icon-caret-bottom"></i> </span>
          <el-dropdown-menu slot="dropdown">
            <el-dropdown-item v-for="item in dataTypeList" :key="item.value" :command="item.value" :class="{ isBjxl: statisticalType == item.value }">{{ item.name }}</el-dropdown-item>
          </el-dropdown-menu>
        </el-dropdown>
        <el-date-picker
          v-model="statisticalDate"
          class="datePickerInput"
          popper-class="date-style"
          type="daterange"
          value-format="yyyy-MM-dd"
          :disabled="statisticalType != 5"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          @change="getEnergyAllData"
          @focus="setWPFBgShow()"
          @blur="setWPFBgHide()"
        >
        </el-date-picker>
      </div>
      <div class="main-scroll">
        <ModuleCard title="能耗统计" class="module-container">
          <div slot="content" class="module-content content-view">
            <div id="stat_chart" class="content-left"></div>
            <div class="content-right">
              <div class="right-item">
                <div class="item-head">
                  <p class="item-title">能耗总量</p>
                  <p class="item-value">
                    <span>{{ kgceAnalysisData.value }}</span>
                    <span>{{ electricityDict.unit }}</span>
                  </p>
                </div>
                <p class="item-line"></p>
                <div style="display: flex; height: 14px">
                  <p class="item-ratio">
                    <span class="item-ratio-name">同比</span>
                    <i
                      :class="kgceAnalysisData.yoyValueRatio > 0 ? 'el-icon-top-right' : 'el-icon-bottom-left'"
                      :style="{
                        color: kgceAnalysisData.yoyValueRatio > 0 ? '#FF2D55' : '#61E29D',
                        background: kgceAnalysisData.yoyValueRatio > 0 ? 'rgba(255, 45, 85, .2)' : 'rgba(97, 226, 157, .2)'
                      }"
                    ></i>
                    <span :style="{ color: kgceAnalysisData.yoyValueRatio > 0 ? '#FF2D55' : '#61E29D' }">{{ kgceAnalysisData.yoyValueRatio || '-' }}%</span>
                  </p>
                  <p class="item-ratio" style="margin-left: 6px">
                    <span class="item-ratio-name">环比</span>
                    <i
                      :class="kgceAnalysisData.momValueRatio > 0 ? 'el-icon-top-right' : 'el-icon-bottom-left'"
                      :style="{
                        color: kgceAnalysisData.momValueRatio > 0 ? '#FF2D55' : '#61E29D',
                        background: kgceAnalysisData.momValueRatio > 0 ? 'rgba(255, 45, 85, .2)' : 'rgba(97, 226, 157, .2)'
                      }"
                    ></i>
                    <span :style="{ color: kgceAnalysisData.momValueRatio > 0 ? '#FF2D55' : '#61E29D' }">{{ kgceAnalysisData.momValueRatio || '-' }}%</span>
                  </p>
                </div>
                <!-- <div v-else style="height: 14px"></div> -->
                <div class="item-head" style="margin-top: 40px">
                  <p class="item-title">费用总数</p>
                  <p class="item-value">
                    <span>{{ kgceAnalysisData.valueCost || '-' }}</span>
                    <span>元</span>
                  </p>
                </div>
              </div>
            </div>
          </div>
        </ModuleCard>
        <ModuleCard title="能耗排行" class="module-container" style="max-height: 30%; min-height: 11%; display: flex; flex-direction: column">
          <div slot="content" v-loading="rankeLoading" class="content-ranke">
            <div class="box-content">
              <div v-for="(item, index) in rankingList" :key="index" class="jindutiao" @click="selectRankeData(item)">
                <el-tooltip slot="title-right" popper-class="tooltip" effect="dark" placement="bottom" :content="item.emodelName" :disabled="item.emodelName.length <= 5">
                  <span class="progress-label">{{ item.emodelName }}</span>
                </el-tooltip>
                <el-progress :stroke-width="8" :percentage="item.ratio" :color="[rankingSelect == item.emodelId ? '#f6de88' : '#0A84FF']" :show-text="false" define-back-color="#394257"></el-progress>
                <div class="progress-right-label">
                  <span class="progress-right-lab1el">{{ filterValue(item.value) + electricityDict.unit }}</span> &nbsp;
                  <span class="progress-label-45">{{ (filterValue(item.valueCost) || '-') + '元' }}</span>
                </div>
              </div>
            </div>
          </div>
        </ModuleCard>
        <ModuleCard title="能耗趋势" class="module-container" style="height: 30%">
          <div slot="title-right" class="middle-right">
            <div :class="{ 'is-activeTab': isType === 'build' }" @click="changeAnalysisData('build')">建筑</div>
            <div :class="{ 'is-activeTab': isType === 'point' }" @click="changeAnalysisData('point')">表计</div>
          </div>
          <div slot="content" v-loading="analysisLoading" class="content-analysis">
            <div class="checkbox-contain">
              <div ref="checkBoxGroup" class="echart-checkBoxGroup">
                <el-checkbox v-for="(item, index) in echartCheckBoxGroup" ref="checkboxes" :key="index" v-model="item.value" :style="{marginRight: checkBoxMarginRight + 'px'}" @change="handleCheckGroupChange">{{
                  item.label
                }}</el-checkbox>
              </div>
              <el-popover
                v-if="panlCheckBoxGroup.length"
                placement="bottom-end"
                :visible-arrow="false"
                popper-class="popper-checkBoxGroup"
                width="100"
                trigger="click">
                <el-checkbox v-for="(item, index) in panlCheckBoxGroup" ref="checkboxes" :key="index" v-model="item.value" style="margin-bottom: 8px" @change="handleCheckGroupChange">{{
                  item.label
                }}</el-checkbox>
                <img slot="reference" src="@/assets/images/checkbox_add.png" />
              </el-popover>
            </div>
            <div id="trend_chart" class="trend_chart"></div>
          </div>
        </ModuleCard>
        <div class="main-module" style="height: 30%">
          <div class="main-module-title">
            <div class="title-left">
              <p class="left-text text-active">电表能耗排行</p>
            </div>
          </div>
          <div class="main-module-content">
            <el-table
              ref="srollTable"
              v-el-table-infinite-scroll="tableLoadMore"
              v-loading="deviceTableLoading"
              class="table-center-transfer"
              :data="deviceList"
              height="calc(100% - 0px)"
              style="width: 100%"
              :cell-style="{ padding: '8px', backgroundColor: 'transparent', color: '#fff', border: 'none', padding: '3px 0px' }"
              :header-cell-style="{ background: '#8591CE26!important', color: '#8BDDF5FF', padding: '0px', fontWeight: 'bold' }"
              element-loading-background="rgba(0, 0, 0, 0.2)"
              @cell-click="tableClick"
            >
              <el-table-column prop="measurePointName" show-overflow-tooltip label="设备名称"></el-table-column>
              <el-table-column prop="energyValue" show-overflow-tooltip label="耗能(kwh)" width="110" sortable>
                <template slot-scope="scope">{{ filterValue(scope.row.energyValue) }}</template>
              </el-table-column>
            </el-table>
          </div>
        </div>
      </div>
    </div>
    <!-- 设备能耗 -->
    <template v-if="deviceEnergyShow">
      <deviceEnergy
        :show="deviceEnergyShow"
        :requestParams="requestParams"
        @closeDialog="closeDeviceEnergyDialog"
      />
    </template>
  </div>
</template>

<script>
import * as echarts from 'echarts'
import dianIcon from '@/assets/images/qhdsys/dianIcon.png'
import { KgceLogin, GetEnergyAndClassifyTree, GetModelEnergyDataList, GetModelFormulaPoint, GetPointHistoryList } from '@/utils/energyConsumption'
import moment from 'moment'
moment.locale('zh-cn')
export default {
  name: 'elevatorEnergy',
  components: {
    deviceEnergy: () => import('@/views/centerScreen/businessManagement/component/deviceEnergy.vue')
  },
  props: {
    roomData: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      isType: 'build',
      statisticalType: 3, // 选中日期类型
      statisticalDate: [moment().startOf('month').format('YYYY-MM-DD') + ' 00:00:00', moment().endOf('month').format('YYYY-MM-DD') + ' 23:59:59'], // 选中日期
      dataTypeList: [
        { value: 1, name: '今日' },
        { value: 2, name: '本周' },
        { value: 3, name: '本月' },
        { value: 4, name: '本年' },
        { value: 5, name: '自定义' }
      ],
      kgceToken: '',
      menuList: [], // 能耗类型菜单
      energyId: '', // 当前选择能耗类型id
      kgceAnalysisData: {
        value: 0,
        momValueRatio: 0,
        yoyValueRatio: 0,
        valueCost: 0
      },
      electricityDict: {
        id: 'SU035',
        unit: 'kWh',
        children: ['电梯用电']
      },
      emodelId: '',
      buildEmodelIList: '',
      deviceTableLoading: false,
      deviceList: [],
      pagination: {
        total: 0,
        pageSize: 15,
        pageNo: 1
      },
      requestParams: {}, // 设备能耗请求参数
      deviceEnergyShow: false, // 设备能耗弹窗
      rankeLoading: false, // 能耗排行加载
      rankingList: [], // 能耗排行
      rankingSelect: '', // 能耗排行选中
      analysisLoading: false,
      checkBoxGroup: {},
      panlCheckBoxGroup: [],
      echartCheckBoxGroup: [],
      checkBoxMarginRight: 20 // 右边距
    }
  },
  computed: {
    filterValue() {
      return (value, type) => {
        value = value && !isNaN(Number(value)) ? Number(value) : 0
        // return value ? value.toFixed(2) : 0
        if (type == 'toFixed') {
          return value.toFixed(2)
        } else {
          if (value > 1000) {
            return (value / 10000).toFixed(2) + '万'
          } else {
            return value.toFixed(2)
          }
        }
      }
    },
    filterName() {
      return (name) => {
        name = name.replace(/用水/g, '').replace(/用电/g, '')
        return name
      }
    }
  },
  watch: {
    // 监听模型层级数据变化
    roomData: {
      handler(newVal, oldVal) {
        const areaData = newVal.areaData
        if (areaData.ssmType == 3) {
          this.emodelId = areaData.parentId
          this.buildEmodelIList = areaData.parentId
          this.rankingSelect = ''
          this.getEnergyAllData()
          // 院区视角 查询院区下的楼栋列表
        } else if (areaData.ssmType == 1) {
          this.emodelId = areaData.parentId
          this.buildEmodelIList = areaData.childList.join(',')
          this.rankingSelect = ''
          this.getEnergyAllData()
        }
      },
      deep: true
      // immediate: true
    }
  },
  mounted() {
    console.log(this.roomData.areaData)
    this.emodelId = this.roomData.areaData.parentId
    this.buildEmodelIList = this.roomData.areaData.childList.join(',')
    this.kgceLogin()
    // 初始化跳转路由地址及参数 elevatorMonitor?areaData={"ssmType":"1","parentId":"1724753192383705090","ssmName":"主院区","childList":["1724753192815718402","1724753193000267778","1724753193138679809","1724753193365172225","1731219360110956545"]}&time=2024/5/28 14:42:26
    // try {
    //   window.chrome.webview.addEventListener('message', (event) => {
    //     const data = JSON.parse(JSON.parse(event.data).areaData)
    //     // 楼栋视角 全查楼栋不向下查询楼层数据
    //     if (data.ssmType == 4) {
    //       this.emodelId = data.parentId
    //       this.buildEmodelIList = data.parentId
    //       this.rankingSelect = ''
    //       this.getEnergyAllData()
    //       // 院区视角 查询院区下的楼栋列表
    //     } else if (data.ssmType == 3) {
    //       this.emodelId = data.parentId
    //       this.buildEmodelIList = data.areaData.childList.join(',')
    //       this.rankingSelect = ''
    //       this.getEnergyAllData()
    //     }
    //   })
    // } catch (errpr) {}
  },
  methods: {
    // 能耗登录
    kgceLogin() {
      KgceLogin(KECG_ACCOUNT).then((res) => {
        if (res.data.code === 200) {
          this.kgceToken = res.data.result.token
          this.getEnergyAndClassifyTree()
        }
      })
    },
    // 时间类型切换
    dataTypeCommand(val) {
      const date = {
        1: [moment().format('YYYY-MM-DD') + ' 00:00:00', moment().format('YYYY-MM-DD') + ' 23:59:59'],
        2: [moment().startOf('isoWeek').format('YYYY-MM-DD') + ' 00:00:00', moment().endOf('isoWeek').format('YYYY-MM-DD') + ' 23:59:59'],
        3: [moment().startOf('month').format('YYYY-MM-DD') + ' 00:00:00', moment().endOf('month').format('YYYY-MM-DD') + ' 23:59:59'],
        4: [moment().startOf('year').format('YYYY-MM-DD') + ' 00:00:00', moment().endOf('year').format('YYYY-MM-DD') + ' 23:59:59'],
        5: []
      }
      this.statisticalDate = date[val]
      this.statisticalType = val
      if (val != 5) {
        this.getEnergyAllData()
      }
    },
    // 获取能耗所有数据列表集合
    getEnergyAllData(val) {
      // 自定义时间
      if (val && val.length) {
        this.statisticalDate = [val[0] + ' 00:00:00', val[1] + ' 23:59:59']
      }
      if (!this.statisticalDate.length) {
        this.statisticalType = 1
        this.statisticalDate = [moment().format('YYYY-MM-DD') + ' 00:00:00', moment().format('YYYY-MM-DD') + ' 23:59:59']
      }
      const dataType = {
        1: 'day',
        2: 'day',
        3: 'month',
        4: 'year',
        5: 'day'
      }
      this.kgceAnalysis(dataType)
      setTimeout(() => {
        this.$nextTick(() => {
          this.steStatEchart(80)
        })
      }, 100)
      this.getBuildRanking()
      // 获取建筑对应表计列表后再获取表计分析排行数据
      this.getPointParamsByModelId(this.emodelId).then((params) => {
        this.pointParams = params
        this.changeAnalysisData(this.isType)
        this.pagination.pageNo = 1
        this.getDeviceList()
      })
    },
    // 能耗分析
    kgceAnalysis(dataType) {
      const params = {
        emodelId: this.emodelId,
        energyId: this.energyId,
        timeCategory: dataType[this.statisticalType],
        startDate: this.statisticalDate[0],
        endDate: this.statisticalDate[1]
      }
      this.kgceAnalysisData = {}
      this.getModelEnergyDataList(params)
        .then((res) => {
          const resData = res.data.data
          if (resData.length) {
            this.kgceAnalysisData = {
              value: this.getListFieldSum(resData, 'value'), // 当前值
              momValueRatio: this.getListFieldSum(resData, 'momValueRatio', resData.length > 1, 'toFixed'), // 环比比率
              yoyValueRatio: this.getListFieldSum(resData, 'yoyValueRatio', resData.length > 1, 'toFixed'), // 同比比率
              valueCost: this.getListFieldSum(resData, 'valueCost') // 费用总数
            }
          } else {
            this.kgceAnalysisData = {
              value: 0,
              momValueRatio: 0,
              yoyValueRatio: 0,
              valueCost: 0
            }
          }
        })
        .catch()
    },
    // 获取能耗排行-建筑
    getBuildRanking(emodelId = '') {
      const dataType = {
        1: 'day',
        2: 'day',
        3: 'month',
        4: 'year',
        5: 'day'
      }
      const param = {
        aggregation: 'sum',
        valueName: 'value',
        groupBy: 'emodelId',
        timeCategory: dataType[this.statisticalType],
        startDate: this.statisticalDate[0],
        endDate: this.statisticalDate[1],
        emodelId: emodelId || this.buildEmodelIList, // 模型id
        energyId: this.energyId
      }
      this.rankeLoading = true
      this.getModelEnergyDataList(param).then((res) => {
        this.rankeLoading = false
        let arr = []
        arr = res.data.data
        if (arr.length) {
          arr.sort((a, b) => {
            return b.value - a.value
          })
          const total = arr.reduce((sum, num) => {
            return sum + num.value
          }, 0)
          arr.forEach((item) => {
            item.ratio = Number((item.value / total) * 100)
          })
          this.rankingList = arr
        } else {
          this.rankingList = []
        }
      })
    },
    getListFieldSum(list, field, isRatio = false, type = '') {
      if (isRatio) return 0
      const sum = list.reduce((prev, cur) => {
        return prev + Number(cur[field]) ?? 0
      }, 0)
      return this.filterValue(sum, type)
    },
    // 获取模型能源数据列表
    getModelEnergyDataList(params) {
      return new Promise((resolve, reject) => {
        GetModelEnergyDataList(params, this.kgceToken)
          .then((res) => {
            if (res.data.code === 200) {
              if (res.data.data.length) {
                res.data.data.forEach((item) => {
                  this.electricityDict.unit = item.energyUnitEn
                })
              }
              resolve(res)
            } else {
              reject(res)
            }
          })
          .catch((err) => {
            reject(err)
          })
      })
    },
    // 获取能源及分类的树结构
    getEnergyAndClassifyTree() {
      GetEnergyAndClassifyTree({}, this.kgceToken).then((res) => {
        if (res.data.code === 200) {
          const slectList = res.data.data.filter((item) => item.id == this.electricityDict.id)
          if (slectList.length && slectList[0].children.length) {
            this.menuList = slectList[0].children.filter((item) => this.electricityDict.children.includes(item.name))
          }
          this.energyId = Array.from(this.menuList, ({ id }) => id).toString()
          this.getEnergyAllData()
        }
      })
    },
    // 获取建筑能耗分析
    getBuildAnalysis(emodelId) {
      const dataType = {
        1: 'hour',
        2: 'day',
        3: 'day',
        4: 'month',
        5: 'day'
      }
      const params = {
        timeCategory: dataType[this.statisticalType],
        emodelId: (emodelId || this.buildEmodelIList) + ',' + this.energyId, // 模型id
        energyId: this.energyId,
        startDate: this.statisticalDate[0],
        endDate: this.statisticalDate[1]
      }
      this.analysisLoading = true
      const newObj = { 总能耗: [] }
      this.getModelEnergyDataList(params)
        .then((res) => {
          this.analysisLoading = false
          let key = ''
          res.data.data.forEach((item) => {
            key = item.emodelName
            // key = item.emodelName == this.showContentType ? '总能耗' : item.emodelName
            if (newObj[key]) {
              newObj[key].push(item)
            } else {
              newObj[key] = [item]
            }
            // 根绝dataTime字段，一致的在总能耗中进行累加 不一致的新增
            const sumFindIndex = newObj['总能耗'].findIndex((e) => e.dataTime == item.dataTime)
            if (sumFindIndex != -1) {
              newObj['总能耗'][sumFindIndex].value += Number(item.value)
            } else {
              newObj['总能耗'].push({
                ...item,
                value: Number(item.value)
              })
            }
          })
          this.calculateVisibleItems(newObj)
          setTimeout(() => {
            this.$nextTick(() => {
              this.electricityLineEchart(newObj)
            })
          }, 100)
        })
        .catch()
    },
    // 获取表计能耗分析
    getPointAnalysis() {
      const dataType = {
        1: 'hour',
        2: 'day',
        3: 'day',
        4: 'month',
        5: 'day'
      }
      const params = {
        ...this.pointParams,
        dataType: dataType[this.statisticalType],
        startDate: this.statisticalDate[0],
        endDate: this.statisticalDate[1]
      }
      this.analysisLoading = true
      GetPointHistoryList(params, this.kgceToken)
        .then((res) => {
          this.analysisLoading = false
          if (res.data.code == 200) {
            const data = res.data.data
            const newObj = { 总能耗: [] }
            data.forEach((item) => {
              const key = item.measurePointName
              if (newObj[key]) {
                newObj[key].push(item)
              } else {
                newObj[key] = [item]
              }
              // 根绝dataTime字段，一致的在总能耗中进行累加 不一致的新增
              const sumFindIndex = newObj['总能耗'].findIndex((e) => e.dataTime == item.dataTime)
              if (sumFindIndex != -1) {
                newObj['总能耗'][sumFindIndex].value += Number(item.value)
              } else {
                newObj['总能耗'].push({
                  ...item,
                  value: Number(item.value)
                })
              }
            })
            this.calculateVisibleItems(newObj)
            setTimeout(() => {
              this.$nextTick(() => {
                this.electricityLineEchart(newObj)
              })
            }, 100)
          }
        })
        .catch()
    },
    // 根据当前模型获取测点列表参数
    getPointParamsByModelId(emodelId) {
      return GetModelFormulaPoint({ modelId: emodelId }, this.kgceToken).then((res) => {
        if (res.data.code == 200) {
          const list = res.data.data
          const elevatorPoint = ['08e6b7c03ec64e158b30fd93187de45f', 'f49b3cdc2ffd4495a12551ad3592bfcf', '84a7d9525c4f4d10b1769b330c179f38', '4b76c829120142e2aced96ed8e6b0100']
          const measurePointIds = Array.from(list, ({ measurePointId }) => measurePointId).filter((item) => elevatorPoint.includes(item))
          const params = {
            measurePointId: measurePointIds.toString(), // 测点编号
            domainKeyword: '70020000', // 正向有功总电能
            sum: false // 是否计算合计值
          }
          return params
        } else {
          return {}
        }
      })
    },
    // 获取设备列表
    getDeviceList() {
      this.deviceTableLoading = true
      if (Object.keys(this.pointParams).length) {
        const params = {
          ...this.pointParams,
          dataType: 'day',
          startDate: this.statisticalDate[0],
          endDate: this.statisticalDate[1]
        }
        GetPointHistoryList(params, this.kgceToken).then((res) => {
          this.deviceTableLoading = false
          if (res.data.code == 200 && res.data.data.length) {
            const list = []
            res.data.data.forEach((item) => {
              const index = list.findIndex((e) => e.measurePointId == item.measurePointId)
              if (index == -1) {
                list.push({
                  measurePointId: item.measurePointId,
                  measurePointName: item.measurePointName,
                  energyValue: Number(item.value)
                })
              } else {
                list[index].energyValue = (list[index].energyValue || 0) + Number(item.value)
              }
            })
            this.deviceList = list
            this.pagination.total = this.deviceList.length
          } else {
            this.pagination.total = 0
            this.deviceList = []
          }
        })
      } else {
        this.deviceTableLoading = false
        this.pagination.total = 0
        this.deviceList = []
      }
    },
    // 列表点击事件打开表计趋势弹窗
    tableClick(row) {
      Object.assign(this.requestParams, {
        ...row,
        kgceToken: this.kgceToken
      })
      this.deviceEnergyShow = true
      try {
        window.chrome.webview.hostObjects.sync.bridge.ShowDialog(true)
      } catch (error) {}
    },
    // 关闭表计趋势弹窗
    closeDeviceEnergyDialog() {
      this.deviceEnergyShow = false
      try {
        window.chrome.webview.hostObjects.sync.bridge.ShowDialog(false)
      } catch (error) {}
    },
    tableLoadMore() {
      if (this.pagination.total > this.pagination.pageNo * this.pagination.pageSize) {
        this.pagination.pageNo++
        this.getDeviceList()
      }
    },
    // 点击切换能耗排行的类型
    changeAnalysisData(type, emodelId = '') {
      this.isType = type
      // this.rankingList = []
      this.getPointParamsByModelId(emodelId || this.emodelId).then((params) => {
        this.pointParams = params
        if (type === 'build') {
          this.getBuildAnalysis(this.rankingSelect || emodelId)
        } else if (type === 'point') {
          this.getPointAnalysis(this.rankingSelect || emodelId)
        }
        this.pagination.pageNo = 1
        this.getDeviceList()
      })
    },
    // 选中能耗排行
    selectRankeData(row) {
      this.rankingSelect = this.rankingSelect == row.emodelId ? '' : row.emodelId
      this.changeAnalysisData(this.isType, row.emodelId)
    },
    // 改变能耗趋势选中标签状态
    handleCheckGroupChange() {
      // 获取未选中的标签
      const filterEchartsList = [...this.echartCheckBoxGroup.filter((item) => !item.value), ...this.panlCheckBoxGroup.filter((item) => !item.value)]
      const filterEchartsLabels = filterEchartsList.map((item) => item.label)
      const newObj = JSON.parse(JSON.stringify(this.checkBoxGroup))
      // 删除未选中标签对应折线图的数据
      Object.keys(this.checkBoxGroup).forEach((key) => {
        if (filterEchartsLabels.includes(key)) {
          delete newObj[key]
        }
      })
      // 重新计算显示的标签
      this.$nextTick(() => {
        this.electricityLineEchart(newObj)
      })
    },
    // 计算显示的标签
    calculateVisibleItems(newObj) {
      // 获取所有标签数据并存进对象为后面增删做准备
      this.checkBoxGroup = newObj
      // 根据统计类型获取所有标签并生成checkbox需要的数据格式
      this.echartCheckBoxGroup = Object.keys(newObj).map((key) => {
        return {
          label: key, // 标签名
          value: true // 是否选中
        }
      })
      // 获取显示的标签总宽度
      const checkBoxGroupWidth = this.$refs.checkBoxGroup.offsetWidth
      let totalWidth = 0
      let visibleItemCount = 0
      this.$nextTick(() => {
        // 遍历标签计算总宽度，如果超过容器宽度则停止
        for (let i = 0; i < this.$refs.checkboxes.length; i++) {
          const checkboxWidth = this.$refs.checkboxes[i].$el.offsetWidth + this.checkBoxMarginRight
          if (totalWidth + checkboxWidth > checkBoxGroupWidth) {
            break
          }
          totalWidth += checkboxWidth
          visibleItemCount++
        }
        this.panlCheckBoxGroup = this.echartCheckBoxGroup.slice(visibleItemCount)
      })
    },
    // 进度图
    steStatEchart(value) {
      const getchart = echarts.init(document.getElementById('stat_chart'))
      const option = {
        graphic: {
          elements: [
            {
              type: 'image',
              z: 3,
              style: {
                image: dianIcon,
                width: 28,
                height: 28
              },
              left: 'center',
              top: '33%',
              position: [100, 100]
            }
          ]
        },
        title: [
          {
            text: '电力',
            x: 'center',
            top: '55%',
            textStyle: {
              color: '#FFFFFF',
              fontSize: 14,
              fontWeight: '100'
            }
          }
        ],
        polar: {
          radius: ['100%', '50%'],
          center: ['50%', '50%']
        },
        angleAxis: {
          max: 100,
          show: false
        },
        radiusAxis: {
          type: 'category',
          show: true,
          axisLabel: {
            show: false
          },
          axisLine: {
            show: false
          },
          axisTick: {
            show: false
          }
        },
        series: [
          {
            name: '',
            type: 'bar',
            //   roundCap: true,
            barWidth: 10,
            showBackground: true,
            backgroundStyle: {
              color: 'rgba(255, 255, 255, .3)'
            },
            data: [value],
            coordinateSystem: 'polar',
            itemStyle: {
              normal: {
                color: '#FFCA64'
              }
            }
          },
          {
            name: '',
            type: 'pie',
            startAngle: 90,
            radius: ['88%'],
            hoverAnimation: false,
            center: ['50%', '50%'],
            itemStyle: {
              color: 'rgba(66, 66, 66, 0)',
              borderWidth: 1,
              borderColor: 'rgba(255, 255, 255, 0.2)'
            },
            data: [100]
          },
          {
            name: '',
            type: 'pie',
            startAngle: 90,
            radius: ['62%'],
            hoverAnimation: false,
            center: ['50%', '50%'],
            itemStyle: {
              color: 'rgba(255,255,255,0.05)',
              borderWidth: 1,
              borderColor: 'rgba(255, 255, 255, 0.2)'
            },
            data: [100]
          }
        ]
      }
      getchart.clear()
      getchart.setOption(option)
      // 随着屏幕大小调节图表
      window.addEventListener('resize', () => {
        getchart.resize()
      })
    },
    // 折线图
    electricityLineEchart(data) {
      const getchart = echarts.init(document.getElementById('trend_chart'))
      let option
      if (Object.keys(data).length) {
        option = {
          dataZoom: [
            {
              type: 'inside',
              start: 0,
              end: 100
            }
          ],
          tooltip: {
            trigger: 'axis',
            confine: true,
            axisPointer: {
              lineStyle: {
                color: '#FFE3A6'
              }
            },
            backgroundColor: '#0C2269',
            borderColor: '#5B617C',
            textStyle: {
              color: '#fff'
            }
          },
          grid: {
            top: '20%',
            left: '15%',
            right: '4$%',
            bottom: '12%'
          },
          // legend: {
          //   x: 'center',
          //   top: '4',
          //   data: Object.keys(data),
          //   itemWidth: 8,
          //   itemHeight: 8,
          //   pageTextStyle: {
          //     color: '#fff'
          //   },
          //   textStyle: {
          //     color: '#B3C2DD' //  字体颜色
          //   }
          // },
          xAxis: {
            // type: 'category',
            // data: nameList,
            type: 'time',
            boundaryGap: false,
            axisTick: {
              show: false
            },
            axisLine: {
              lineStyle: {
                color: '#303F69'
              }
            },
            axisLabel: {
              textStyle: {
                fontSize: 10,
                color: '#8C9EB5'
              }
            }
          },
          yAxis: {
            type: 'value',
            name: '单位：' + this.electricityDict.unit,
            nameTextStyle: {
              color: '#A4AFC1',
              fontSize: 12,
              padding: [0, 0, 0, 0]
            },
            axisTick: {
              show: false
            },
            // axisLine: {
            //   lineStyle: {
            //     color: '#609ee9'
            //   }
            // },
            axisLabel: {
              margin: 10,
              textStyle: {
                fontSize: 10,
                color: '#8C9EB5'
              }
            },
            splitLine: {
              show: true,
              lineStyle: {
                color: ['rgba(230, 247, 255, 0.20)'],
                width: 1,
                type: 'dashed'
              }
            }
          },
          series: Object.keys(data).map((key) => {
            return {
              name: key,
              type: 'line',
              // smooth: true,
              showSymbol: false,
              symbol: 'circle',
              symbolSize: 6,
              // data: valueList,
              data: data[key].map((item) => [item.dataTime, item.value]),
              itemStyle: {
                normal: {
                  color: '#FFE3A6'
                }
              },
              lineStyle: {
                normal: {
                  width: 2
                }
              }
            }
          })
        }
      } else {
        option = {
          title: {
            text: '暂无数据',
            x: 'center',
            y: 'center',
            textStyle: {
              fontSize: 14,
              fontWeight: 'normal',
              color: '#999'
            }
          }
        }
      }
      getchart.clear()
      getchart.setOption(option)
      // 随着屏幕大小调节图表
      window.addEventListener('resize', () => {
        getchart.resize()
      })
    },
    switchPanel() {},
    setWPFBgShow() {
      try {
        window.chrome.webview.hostObjects.sync.bridge.DateMask(true)
      } catch (error) {}
    },
    setWPFBgHide() {
      try {
        window.chrome.webview.hostObjects.sync.bridge.DateMask(false)
      } catch (error) {}
    }
  }
}
</script>

<style lang="scss" scoped>
.module-content {
  padding: 10px 0;
  box-sizing: border-box;
}

.energyComponent {
  width: 100%;
  height: 100%;
  .energy-main {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    .search-data {
      display: flex;
      background: rgba(133, 145, 206, 0.15);
      padding: 0px 10px;
      ::v-deep .el-dropdown {
        padding: 7px 6px;
        .el-dropdown-link {
          font-size: 14px;
          font-weight: 500;
          color: #8bddf5;
          line-height: 16px;
          position: relative;
          cursor: pointer;
        }
        .el-dropdown-link::after {
          content: '';
          position: absolute;
          width: 1px;
          height: 12px;
          background: rgba(133, 145, 206, 0.5);
          top: 50%;
          right: -6px;
          transform: translateY(-50%);
        }
      }
      ::v-deep .datePickerInput {
        flex: 1;
        padding: 8px 10px;
        height: 16px;
        box-sizing: content-box;
        background: none;
        border: none;
        .el-input__icon,
        .el-range-separator {
          line-height: 16px;
          color: #b0e3fa;
        }
        .el-range-input {
          background: none;
          color: #a4afc1;
        }
      }
    }
    .main-scroll {
      flex: 1;
      overflow: auto;
      .proportion {
        display: flex;
        margin-top: 17px;
        .proportion_item {
          flex: 1;
          display: flex;
          flex-direction: column;
          justify-content: center;
          align-items: center;
          .proportion_data {
            display: flex;
            p {
              font-size: 14px;
              font-family: HarmonyOS Sans SC-Bold, HarmonyOS Sans SC;
              font-weight: bold;
              color: #ffffff;
              margin-left: 5px;
            }
            .up {
              color: #ff2d55;
            }
            .down {
              color: #61e29d;
            }
          }
          .proportion_img {
            margin-top: 5px;
          }
          span {
            font-size: 14px;
            font-family: HarmonyOS Sans SC-Regular, HarmonyOS Sans SC;
            font-weight: 400;
            color: #a4acb9;
          }
        }
      }
      .content-view {
        display: flex;
        .content-left {
          width: 40%;
        }
        .content-right {
          width: 60%;
          .right-item {
            padding: 20px 26px 30px 16px;
            // background: linear-gradient(90deg, rgba(255, 255, 255, .1) 0%, rgba(255, 255, 255, 0) 100%);
          }
          .item-line {
            height: 1px;
            width: 100%;
            margin: 8px 0px;
            background: linear-gradient(90deg, rgba(34, 143, 225, 1) 0%, rgba(34, 143, 225, 0) 100%);
          }
          .item-head {
            display: flex;
            align-items: end;
          }
          .item-title {
            font-size: 14px;
            font-weight: 400;
            color: #b0e3fa;
          }
          .item-value {
            margin-left: 8px;
            span:first-child {
              font-size: 18px;
              font-weight: bold;
              color: #ffca64;
            }
            span:last-child {
              margin-left: 3px;
              font-size: 12px;
              font-weight: 400;
              color: #ffffff;
              line-height: 14px;
            }
          }
          .item-ratio {
            font-size: 12px;
            i {
              font-size: 14px;
              margin-left: 6px;
              margin-right: 4px;
            }
            .item-ratio-name {
              color: #ffffff;
            }
          }
        }
      }
      .middle-right {
        display: flex;
        align-items: center;
        .is-activeTab {
          background: url('@/assets/images/qhdsys/bg-kj.png') no-repeat !important;
        }
        div {
          cursor: pointer;
          width: 52px;
          height: 26px;
          line-height: 26px;
          text-align: center;
          background: rgba(255, 255, 255, 0.1) linear-gradient(90deg, rgba(10, 132, 255, 0) 0%, rgba(10, 132, 255, 0) 100%);
          border-radius: 0px 0px 0px 0px;
          opacity: 1;
          font-size: 14px;
          font-family: HarmonyOS Sans SC-Regular, HarmonyOS Sans SC;
          font-weight: 400;
          color: #8bddf5;
        }
        div:nth-child(2) {
          margin: 0 6px;
        }
      }
      .content-ranke {
        width: 100%;
        height: calc(100%);
        box-sizing: border-box;
        padding: 0px 10px 0px 10px;
        overflow: auto;
        .jindutiao {
          cursor: pointer;
          display: flex;
          height: 30px;
          align-items: center;
          & > span {
            font-size: 14px;
          }
          .el-progress {
            width: 60%;
            margin: 0 16px;
            display: flex;
            align-items: center;
          }
          .progress-label {
            opacity: 0.8;
            flex-shrink: 0;
            width: 20%;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
          }
          .progress-right-label {
            font-size: 14px;
            flex-shrink: 0;
          }
        }
        &::-webkit-scrollbar {
          display: none; /* Chrome Safari */
        }
      }
      .content-analysis {
        width: 100%;
        height: calc(100%);
        box-sizing: border-box;
        overflow: hidden;
        .checkbox-contain {
          height: 25px;
          line-height: 25px;
          display: flex;
          justify-content: space-between;
          // flex-wrap: nowrap;
          .echart-checkBoxGroup {
            width: calc(100% - 25px);
            padding-left: 10px;
            // white-space: nowrap;
            overflow: hidden;
          }
          img {
            width: 18px;
            height: 18px;
            cursor: pointer;
            margin: auto 0;
            flex-shrink: 1;
          }
          .panel {
            position: absolute;
            right: 0;
            top: 20px;
            background-color: #374b79;
            padding: 8px;
            z-index: 9;
            height: 100px;
            overflow: auto;
            .el-checkbox-group {
              display: flex;
              flex-direction: column;
              align-items: flex-end;
              .el-checkbox {
                width: 100%;
                display: flex;
                justify-content: space-between;
                align-items: center;
                margin-right: 0;
                margin-bottom: 5px;
              }
            }
          }
        }
        .trend_chart {
          width: 100%;
          height: calc(100% - 25px);
        }
      }
      .main-module {
        .main-module-title {
          padding: 7px 0px 7px 38px;
          background: url('~@/assets/images/qhdsys/bg-bt.png') no-repeat center / 100% 100%;
          display: flex;
          justify-content: space-between;
          .title-left {
            display: flex;
            .left-text {
              cursor: pointer;
              font-size: 16px;
              font-family: HarmonyOS Sans SC-Bold;
              color: #a6afbf;
              line-height: 30px;
            }
            .text-active {
              font-weight: bold;
              color: #ffffff;
              text-shadow: 0px 0px 9px #158eff;
            }
          }
        }
        .main-module-content {
          height: calc(100% - 44px);
        }
        ::v-deep .table-center-transfer {
          border: 0 !important;
        }
      }
      .main-analysis {
        height: 100%;
        display: flex;
        .analysis-content-chart {
          height: 100%;
          width: 50%;
        }
        .analysis-content-list {
          height: 100%;
          width: 50%;
          padding: 16px 16px 16px 0;
          display: flex;
          flex-direction: column;
          box-sizing: border-box;
          justify-content: space-evenly;
          .list-item {
            position: relative;
            display: flex;
            align-items: center;
            padding: 4px 0px 4px 27px;
            background: rgba(42, 54, 68, 0.4);
            .list-item-color {
              width: 7px;
              height: 7px;
              border-radius: 50%;
              position: absolute;
              top: 50%;
              transform: translateY(-50%);
              left: 10px;
            }
            .list-item-name {
              font-size: 12px;
              font-weight: 400;
              color: #a4acb9;
              line-height: 14px;
              min-width: 40px;
            }
            .list-item-value {
              font-size: 16px;
              font-weight: 400;
              color: #ffffff;
              line-height: 19px;
            }
          }
          .list-item::before {
            content: '';
            width: 2px;
            height: 2px;
            position: absolute;
            background: #9bb8c7;
            left: 0;
            top: 0;
          }
        }
      }
    }
    .main-scroll::-webkit-scrollbar {
      display: none; /* Chrome Safari */
    }
  }
}
</style>
<style lang="scss">
.popper-checkBoxGroup {
  display: flex;
  flex-direction: column;
  .el-checkbox {
    margin-right: 0;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    .el-checkbox__label {
      display: inline;
    }
  }
}
</style>
