<template>
  <div class="main">
    <el-dialog
      v-dialogDrag
      :modal="false"
      :visible.sync="warnTableShow"
      custom-class="mainDialog main"
      append-to-body
      :close-on-click-modal="false"
      :before-close="closeDialog"
      class="personDialog"
      :center="true"
    >
      <template slot="title">
        <span class="dialog-titles">待确认报警</span>
      </template>
      <div class="dialog-content">
        <el-table
          @row-dblclick="rowClick"
          :data="tableData"
          height="300"
          stripe
          :cell-style="$tools.setCell(3)"
          :header-cell-style="$tools.setHeaderCell(3)"
          style="width: 100%;"
          element-loading-background="rgba(0, 0, 0, 0.2)"
          :row-class-name="rowClass"
        >
          <el-table-column fixed prop="alarmSource" show-overflow-tooltip label="报警来源"></el-table-column>
          <el-table-column fixed prop="deviceName" show-overflow-tooltip label="报警设备"></el-table-column>
          <el-table-column fixed prop="alarmLevel" show-overflow-tooltip label="报警等级">
            <template slot-scope="scope">
              <span>{{
                policeLevelOption.filter((e) => e.dictValue === Number(scope.row.alarmLevel)).length ? policeLevelOption.filter((e) => e.dictValue === Number(scope.row.alarmLevel))[0].labelName : ''
              }}</span>
            </template>
          </el-table-column>
          <el-table-column fixed prop="alarmTime" show-overflow-tooltip label="报警时间"></el-table-column>
        </el-table>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import { getDictDataList } from '@/utils/api'
export default {
  name: 'warnTable',
  data() {
    return {
      policeLevelOption: [],
      tableData: [
        // {
        //   alarmSource: '氧气系统',
        //   deviceName: '氧气站',
        //   alarmLevel: '紧急',
        //   alarmTime: '2022-01-09 12:09'
        // },
      ],
      warnTableShow: true
    }
  },
  watch: {},
  created() {},
  mounted() {
    const dictList = JSON.parse(localStorage.getItem('dictList'))
    if (dictList === null || dictList === undefined) {
      this.setDictStorage()
    } else {
      this.policeLevelOption = this.getSelectDictOption(dictList, '警情级别')
    }
    // 初始化表格数据
    if (Object.hasOwn(this.$route.query, 'data')) {
      this.tableData.push(JSON.parse(this.$route.query.data))
    }
    try {
      window.chrome.webview.addEventListener('message', (event) => {
        this.tableData.push(JSON.parse(event.data))
      })
    } catch (errpr) {}
  },
  methods: {
    // 字典信息重新赋值
    setDictStorage() {
      getDictDataList({}).then((res) => {
        const data = res.data
        if (data.code === '200') {
          localStorage.setItem('dictList', JSON.stringify(data.data))
          this.policeLevelOption = this.getSelectDictOption(data.data, '警情级别')
        }
      })
    },
    getSelectDictOption(list, type) {
      const option = list.length ? list.filter((e) => e.dictName === type) : []
      const optionList = option.length ? option[0].dictConfigList : []
      return optionList
    },
    // 取消按钮
    closeDialog() {
      window.chrome.webview.hostObjects.sync.bridge.CloseWindow()
    },
    rowClick(row) {
      const index = this.tableData.findIndex((e) => e.id === row.id)
      this.$confirm('当前有处置中警情，是否确认关闭!', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        cancelButtonClass: 'sino-button-sure',
        confirmButtonClass: 'sino-button-sure',
        customClass: 'confirm-box-class',
        type: 'warning'
      })
        .then(() => {
          this.tableData.splice(index, 1)
          try {
            window.chrome.webview.hostObjects.sync.bridge.MonitoringID(row.id)
          } catch (error) {}
          if (!this.tableData.length) {
            this.closeDialog()
          }
        })
        .catch(() => {
          // callback(new Error('cancel'))
        })
    },
    rowClass: function (row) {
      if (Number(row.row.alarmLevel) === 0) {
        return 'redBg'
      }
    }
  }
}
</script>

<style lang="scss" type="text/css" scoped>
::v-deep .mainDialog {
  position: absolute;
  bottom: 20%;
  left: 35%;
  top: auto !important;
  width: 30%;
  // margin-top: 22vh !important;
  // height: 30vh;
  // background: #031553;
  // margin: 0 !important;
  // border: 1px dashed #eee;
  // -webkit-box-shadow: 0px -6px 8px 2px rgba(170, 11, 11, 0.2), /*上边阴影  红色*/ -6px 0px 8px 2px rgba(170, 11, 11, 0.2), /*左边阴影  绿色*/ 6px 0px 8px 2px rgba(170, 11, 11, 0.2),
  //   /*右边阴影  蓝色*/ 0px 6px 8px 2px rgba(170, 11, 11, 0.2); /*下边阴影  黄色*/
  // box-shadow: 0px -6px 8px 2px rgba(170, 11, 11, 0.2), /*上边阴影  红色*/ -6px 0px 8px 2px rgba(170, 11, 11, 0.2), /*左边阴影  绿色*/ 6px 0px 8px 2px rgba(170, 11, 11, 0.2),
  //   /*右边阴影  蓝色*/ 0px 6px 8px 2px rgba(170, 11, 11, 0.2); /*下边阴影  黄色*/

  .dialog-titles {
    display: inline-block;
    width: 100%;
    color: #fff;
  }
  .el-dialog__body {
    padding: 0;
    // max-height: calc(100% - 130px);
    // height: auto;
    overflow: hidden;
    .dialog-content {
      width: 100%;
      height: 100%;
      background: transparent;
      padding: 20px 55px 0;
      margin-bottom: 50px;
      .el-table {
        border: none !important;
        tr {
          background-color: #222f52 !important;
          // color: #8bddf5 !important;
          th {
            background-color: transparent;
            // color: #fff;
          }
        }
        .redBg {
          background: #341f51;
        }
      }
    }
  }
}
::v-deep .el-dialog {
  // background-position: 0 10px;
  background-color: transparent;
  background-image: url('@/assets/images/peace/body.png');
  background-size: 100% 100%;
  background-repeat: no-repeat;
}
::v-deep .el-dialog__header {
  padding: 0 20px 21px !important;
}
::v-deep .el-icon-close {
  width: 36px !important;
  height: 36px;
  background-repeat: no-repeat;
  // background: url('@/assets/images/peace/icon.png') !important;
  background-size: 36px 36px;
}
::v-deep .el-icon-close:before {
  content: '';
}
::v-deep .el-table--striped .el-table__body tr.el-table__row--striped td {
  background-color: rgba(168,172,171,0.08) !important; /* def2ff f2faff */
}

</style>
