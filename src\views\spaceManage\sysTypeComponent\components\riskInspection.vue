<template>
  <div class="dialog-content">
    <el-dialog
      v-dialogDrag
      width="80%"
      :modal="false"
      :visible.sync="hiddenDangerDetailsListShow"
      custom-class="detailDialog main"
      :close-on-click-modal="false"
      :before-close="hiddenDangerDetailCloseDialog"
    >
      <template slot="title">
        <span class="dialog-title">风险详情</span>
      </template>
      <div class="form-detail">
        <div class="plan-content">
          <ul class="item-row">
            <li class="width33">
              <span class="li-first-span">风险点名称</span><span class="li-last-span">{{ detailsInfo.riskName }}</span>
            </li>
            <li class="width33">
              <span class="li-first-span">风险位置</span><span class="li-last-span">{{ detailsInfo.riskPlace }}</span>
            </li>
            <li class="width33">
              <span class="li-first-span">风险研判</span><span class="li-last-span">{{ detailsInfo.riskLevelName }}</span>
            </li>
          </ul>
          <ul class="item-row">
            <li class="width33">
              <span class="li-first-span">责任部门</span><span class="li-last-span">{{ detailsInfo.taskTeamName }}</span>
            </li>
            <li class="width33">
              <span class="li-first-span">责任人</span><span class="li-last-span">{{ detailsInfo.responsiblePersonName }}</span>
            </li>
            <li class="width33">
              <span class="li-first-span">风险点编号</span><span class="li-last-span">{{ detailsInfo.riskCode }}</span>
            </li>
          </ul>
          <ul class="item-row">
            <li class="width33">
              <span class="li-first-span">应急电话</span><span class="li-last-span">{{ detailsInfo.urgentPhone }}</span>
            </li>
            <li class="width33">
              <span class="li-first-span">应急联系电话</span><span class="li-last-span">{{ detailsInfo.urgentContactPhone }}</span>
            </li>
            <li class="width33">
              <span class="li-first-span">事故后果</span><span class="li-last-span">{{ detailsInfo.accidentTypeList.map((item) => item.dictLabel).join('，') }}</span>
            </li>
          </ul>
          <ul class="item-row">
            <li class="width33">
              <span class="li-first-span">风险类型</span><span class="li-last-span">{{ detailsInfo.riskTypeName }}</span>
            </li>
            <li class="width33">
              <span class="li-first-span">管控层级</span><span class="li-last-span">{{ detailsInfo.free1Name }}</span>
            </li>
          </ul>
          <p style="padding: 1rem"></p>
          <ul class="item-row">
            <li class="width95">
              <span class="li-first-span">图片</span>
              <p class="li-last-span">
                <span v-for="(img, index) in detailsInfo.attachmentUrl" :key="index">
                  <el-image style="width: 100px; height: 100px; margin-right: 15px" :src="img" :preview-src-list="[img]"> </el-image>
                </span>
              </p>
              <span></span>
            </li>
          </ul>
          <ul class="item-row">
            <li class="width95">
              <span class="li-first-span">预案流程图</span>
              <p class="li-last-span">
                <span v-for="(img, index) in detailsInfo.processUrl" :key="index">
                  <el-image style="width: 100px; height: 100px; margin-right: 15px" :src="img" :preview-src-list="[img]"> </el-image>
                </span>
              </p>
              <span></span>
            </li>
          </ul>
          <p style="padding: 1rem"></p>
          <!-- <el-table
            :data="tableData"
            :cell-style="$tools.setCell(3)"
            :header-cell-style="$tools.setHeaderCell(3)"
            style="width: 100%"
          >
            <el-table-column fixed type="index" width="80" label="序号"></el-table-column>
            <el-table-column fixed prop="alarmSource" label="报警来源"></el-table-column>
            <el-table-column fixed prop="alarmTypeName" label="报警类型"></el-table-column>
            <el-table-column fixed prop="deviceName" label="报警项名称"></el-table-column>
          </el-table> -->
          <!-- 模版1 -->
          <el-table
            v-if="isFirstTableShow"
            :data="tableData"
            element-loading-background="rgba(0, 0, 0, 0.2)"
            :cell-style="{ padding: ' 8px', backgroundColor: 'transparent', border: 'none', padding: '3px' }"
            :header-cell-style="{ background: '#8591CE26!important', color: '#8BDDF5FF', padding: '4px 8px', fontWeight: 'bold' }">
            <el-table-column type="index" width="100" label="序号"></el-table-column>
            <el-table-column :label="detailsInfo.riskType === 3 ? '作业步骤' : '检查项目'" width="180" property="checkItem">
              <template slot-scope="scope">
                <span>{{ scope.row.checkItem }}</span>
              </template>
            </el-table-column>
            <el-table-column :label="detailsInfo.riskType === 3 ? '危险源或潜事件' : '标准'" width="180" property="standard">
              <template slot-scope="scope">
                <span>{{ scope.row.standard }}</span>
              </template>
            </el-table-column>
            <el-table-column :label="detailsInfo.riskType === 3 ? '可能发生的事故类型及后果' : '不符合标准情况及后果'" width="190" property="result">
              <template slot-scope="scope">
                <span>{{ scope.row.result }}</span>
              </template>
            </el-table-column>
            <el-table-columnlabel="现有控制措施">
              <el-table-column label="工程技术措施" width="150" property="technicalMeasure">
                <template slot-scope="scope">
                  <span>{{ scope.row.technicalMeasure }}</span>
                </template>
              </el-table-column>
              <el-table-column label="管理措施" width="150" property="manageMeasure">
                <template slot-scope="scope">
                  <span>{{ scope.row.manageMeasure }}</span>
                </template>
              </el-table-column>
              <el-table-column label="培训教育措施" width="150" property="educationMeasure">
                <template slot-scope="scope">
                  <span>{{ scope.row.educationMeasure }}</span>
                </template>
              </el-table-column>
              <el-table-column label="个体防护措施" width="150" property="protectiveMeasure">
                <template slot-scope="scope">
                  <span>{{ scope.row.protectiveMeasure }}</span>
                </template>
              </el-table-column>
              <el-table-column label="应急措施" width="150" property="emergencyMeasure">
                <template slot-scope="scope">
                  <span>{{ scope.row.emergencyMeasure }}</span>
                </template>
              </el-table-column>
              </el-table-column>
              <el-table-column label="建议改进(新增)措施" width="150" property="adviceMeasure">
                <template slot-scope="scope">
                  <span>{{ scope.row.adviceMeasure }}</span>
                </template>
              </el-table-column>
          </el-table>
          <!-- 模版2 -->
          <el-table
            v-else
            :data="tableData2"
            element-loading-background="rgba(0, 0, 0, 0.2)"
            :cell-style="{ padding: ' 8px', backgroundColor: 'transparent', border: 'none', padding: '3px' }"
            :header-cell-style="{ background: '#8591CE26!important', color: '#8BDDF5FF', padding: '4px 8px', fontWeight: 'bold' }">
            <el-table-column type="index" width="100" label="序号"></el-table-column>
            <el-table-column label="主要风险因素" width="320" property="riskFactor">
              <template slot-scope="scope">
                <span>{{ scope.row.riskFactor }}</span>
              </template>
            </el-table-column>
            <el-table-column label="安全操作要点" width="320" property="operationElement">
              <template slot-scope="scope">
                <span>{{ scope.row.operationElement }}</span>
              </template>
            </el-table-column>
            <el-table-column label="主要风险管控措施" width="320" property="controlElement">
              <template slot-scope="scope">
                <span>{{ scope.row.controlElement }}</span>
              </template>
            </el-table-column>
            <el-table-column label="应急处置措施" width="320" property="handleElement">
              <template slot-scope="scope">
                <span>{{ scope.row.handleElement }}</span>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { GetRiskDetail } from '@/utils/centerScreenApi'
import { CheckRiskInvestigation, GetRiskInvestigationRecord } from '@/utils/spaceManage'

export default {
  name: 'riskInspection',
  props: {
    rowData: {
      type: Object,
      default: function () {
        return {}
      }
    }
  },
  data() {
    return {
      dialogFullScreen: false,
      formInline: {
        investigationType: '',
        createtTmeStart: '',
        createtTmeEnd: ''
      },
      hiddenDangerDetailsListShow: false,
      typeList: [
        {
          name: '匿名用户',
          id: 1
        },
        {
          name: '系统用户',
          id: 2
        }
      ],
      tabsActiveName: '1',
      detailsInfo: {
        accidentTypeList: []
      },
      tableData: [],
      tableData2: [],
      riskInvestigationRecordData: []
    }
  },
  computed: {
    isFirstTableShow() {
      if ((this.detailsInfo.riskType === 1 || this.detailsInfo.riskType === 2 || this.detailsInfo.riskType === 3) && this.detailsInfo.analysisList[0].templateType === '1') {
        return true
      } else {
        return false
      }
    }
  },
  mounted() {
    this.init()
    return
    if (this.tabsActiveName === '1') {
      this.getRiskDetail(this.rowData.id)
    } else {
      this.checkRiskInvestigation(this.rowData.id)
    }
  },
  beforeDestroy() {
    try {
      window.chrome.webview.hostObjects.sync.bridge.GetSwichFloorIsShow(false)
      window.chrome.webview.hostObjects.sync.bridge.ShowDialog(false)
    } catch (error) {}
  },
  created() {},
  methods: {
    init() {
      const analysisListArr = []
      const analysisListArr2 = []
      this.detailsInfo = {
        'riskName': '危化品库房',
        'planAttachment': '',
        'templateType': '1',
        'secondColumn': '',
        'seventhColumn': '',
        'riskPlace': '中科东院区123',
        'planAttachmentOssPath': '',
        'taskTeamName': '科级小组',
        'judgeResultScore': '0.1',
        'riskJudgeRecordId': '1868598782982799360',
        'taskTeamId': '1532274870150721536',
        'free1': '',
        'id': '2c91512e9391b5e00193ceefc9330290',
        'LSRSExplain': '',
        'eighthColumn': '',
        'LSRLExplain': '',
        'planFlowchartOssPath': '',
        'judgeCreatePersonCode': 'b13e3ef6055b4db08edb4eb08c738077',
        'LECDEExplain': '',
        'registerTime': '2024-12-16 18:07:21.0',
        'operationElement': ' 1.1.废弃危险化学品应存放在专门的储存场所，指定专人负责管理； 2.废弃危险化学品应交由有危险废物处置资质的单位进行处置 3.定期检查现场警示标识，发现损坏或缺失及时整改 2.1.根据所储存的危险化学品性质和特点，为作业人员配置事故柜、急救箱和个人防护用品。 2.定期检查静电消除装置、通风设施、气体检测报警装置有效性；机械通风正常通风换气次数不少于 6 次/h，事故排风换气次数不应少于 12 次/h；发现故障及时处理 3.定期检查储存室内电气线路及设施，确保防护等级满足防爆要求 3.1.按危险化学品的危险性质分区、分类、分柜（或分库）存放，禁忌类危险化学品不应混合存放。凡能混存危险化学品，采用堆垛方式码放的，货垛与货垛之间，应留有 1 m 以上的距离，包装容器应完整，两种物品不应发生接触。 2.保留与储存、使用危险化学品种类相符的化学品安全标签和安全技术说明书。 4.1.储存场所应由专人负责管理。储存场所内应张贴安全管理部门负责人、安全责任人、消防控制室等联系电话，并保持电话畅通。 2.专用储存室，易燃液体类危险化学品存放总量 0.5 t 以下或不超过一昼夜使用量；易燃液体类危险化学品存放总量 0.5 t 以上应设置专用仓库。 3.储存场所内储存液体危险化学品的单一包装不宜超过 50 L 或50 kg 5.1.建立健全各项危险化学品安全管理制度、岗位操作规程及应急预案并定期更新 2.储存场所的显著位置张贴或悬挂危险化学品管理制度、岗位操作规程及现场处置方案。 6.1.采购有危险化学品安全生产许可或经营许可资质单位的危险化学品。 2.加强危险化学品全过程闭环管理，排查危险化学品采购、储存、使用、处置等缓解安全隐患',
        'riskPlaceId': '289c518f55d245bc87cd1f1f4b238c21',
        'LECDCExplain': '',
        'riskCode': '1111',
        'processUrl': [],
        'judgeProbableScore': '1',
        'registerPerson': '巡检超管',
        'allContent': '',
        'responsiblePersonCode': '475261e7900643618a527029f15e7c85',
        'urgentPhone': '110',
        'fourthColumn': '',
        'status': '1',
        'riskElement': '',
        'riskLevel': 2,
        'planFlowchartOss': '',
        'judgeCreatePersonName': '巡检超管',
        'urgentContactPhone': '15728247510',
        'riskType': 2,
        'judgeType': 1,
        'controlElement': ' 1.废弃危险化学品存储及管理不规范安 2.未按要求设置静电消除装置；室内电气不防爆；未按要求设置气体监测报警装置 3.未按危险化学品性质要求分区、分类存贮；禁忌类混存；未留存危险化学品安全技术说明书 4.违规存储危险化学品 5.未按要求建立各项危险化学品管理制度、应急预案等 6.违规采购不合规危险化学品',
        'fifthColumn': '',
        'planAttachmentOss': '',
        'judgeScore': '0.05',
        'planLedger': '',
        'analysisList': [
          {
            'id': '1868598783309955072',
            'riskId': '2c91512e9391b5e00193ceefc9330290',
            'checkItem': '采购管理',
            'standard': '应采购有危险化学品安全生产许可或经营许可资质单位的危险化学品。',
            'result': '违规采购不合规危险化学品',
            'technicalMeasure': '/',
            'manageMeasure': '1.采购有危险化学品安全生产许可或经营许可资质单位的危险化学品。 2.加强危险化学品全过程闭环管理，排查危险化学品采购、储存、使用、处置等缓解安全隐患',
            'educationMeasure': '定期开展安全教育培训，内容应包括：安全生产相关 法律法规、标准规范，本医疗卫生机构安全生产责任制、规章制度、操作规程、危险化学品、应急预案，疏散和现场紧急情况的处理 应对措施，典型事故案例等。',
            'protectiveMeasure': '/',
            'emergencyMeasure': '做好危险化学品的合规采购，不得向未取得危险化学品安全许可的单位采购危险化学品，不得违规寄递危险化学品，发现违规采购管理危险化学品等现象及时上报领导',
            'templateType': '1'
          },
          {
            'id': '1868598783268012032',
            'riskId': '2c91512e9391b5e00193ceefc9330290',
            'checkItem': '危险化学品管理制度和安全操作规程',
            'standard': '使用危险化学品的单位，其使用条件（包括工艺）应当符合法律、行政法规的规定和国家标准、行业标准的要求，并根据所使用的危险化学品的种类、危险特性以及使用量和使用方式，建立、健全使用危险化学品的安全管理规章制度和安全操作规程，保证危险化学品的安全使用。',
            'result': '未按要求建立各项危险化学品管理制度、应急预案等',
            'technicalMeasure': '/',
            'manageMeasure': '1.建立健全各项危险化学品安全管理制度、岗位操作规程及应急预案并定期更新 2.储存场所的显著位置张贴或悬挂危险化学品管理制度、岗位操作规程及现场处置方案。',
            'educationMeasure': '定期开展安全教育培训，内容应包括：安全生产相关 法律法规、标准规范，本医疗卫生机构安全生产责任制、规章制度、操作规程、危险化学品、应急预案，疏散和现场紧急情况的处理 应对措施，典型事故案例等。',
            'protectiveMeasure': '/',
            'emergencyMeasure': '1.发生化学品火灾立即报告领导，并采取针对性灭火措施，选用适合扑救该类物品的灭火剂，正确地实施扑救。扑救毒害性、腐蚀性或燃烧产物毒害性较强的易燃品火灾，扑救人员必须佩戴防护面具，采取防护措施。若火势难以控制，请求辖区消防队救援，消防队到达现场后开展灭火。利用对讲机、电话等多种通信手段通知无关人员紧急撤离，严禁无关人员进入火场 2.发生中毒事故后，迅速将人员脱离至空气新鲜处，立即送往急诊',
            'templateType': '1'
          },
          {
            'id': '1868598783226068992',
            'riskId': '2c91512e9391b5e00193ceefc9330290',
            'checkItem': '存储场所',
            'standard': '危险化学品应储存在专用的储存场所内，不应露天存放。危险化学品的储存方式、方法以及储存数量应当符合国家标准或者国家有关规定。 储存场所内储存液体危险化学品的单一包装不宜超过 50 L 或50 kg。',
            'result': '违规存储危险化学品',
            'technicalMeasure': '1.危险化学品应储存在专用的储存场所内；储存危险化学品的场所应配备相应消防器材，消防器材有专人负责，定期检查 2.与其他建筑物贴邻设置的储存场所，不应有门、窗与相邻建筑物相通。 3.危险化学品储存场所应设置明显的标志',
            'manageMeasure': '1.储存场所应由专人负责管理。储存场所内应张贴安全管理部门负责人、安全责任人、消防控制室等联系电话，并保持电话畅通。 2.专用储存室，易燃液体类危险化学品存放总量 0.5 t 以下或不超过一昼夜使用量；易燃液体类危险化学品存放总量 0.5 t 以上应设置专用仓库。 3.储存场所内储存液体危险化学品的单一包装不宜超过 50 L 或50 kg',
            'educationMeasure': '定期开展安全教育培训，内容应包括：安全生产相关 法律法规、标准规范，本医疗卫生机构安全生产责任制、规章制度、操作规程、危险化学品、应急预案，疏散和现场紧急情况的处理 应对措施，典型事故案例等。',
            'protectiveMeasure': '正确穿戴防护用品',
            'emergencyMeasure': '1.发生化学品火灾立即报告领导，并采取针对性灭火措施，选用适合扑救该类物品的灭火剂，正确地实施扑救。扑救毒害性、腐蚀性或燃烧产物毒害性较强的易燃品火灾，扑救人员必须佩戴防护面具，采取防护措施。若火势难以控制，请求辖区消防队救援，消防队到达现场后开展灭火。利用对讲机、电话等多种通信手段通知无关人员紧急撤离，严禁无关人员进入火场 2.发生泄露，进入现成人员佩戴必要个人防护器具，根据泄漏物性质进行覆盖、收容、稀释等处理；如西楼物为易燃易爆，应严禁火种',
            'templateType': '1'
          },
          {
            'id': '1868598783184125952',
            'riskId': '2c91512e9391b5e00193ceefc9330290',
            'checkItem': '危险化学品存储',
            'standard': '应按危险化学品的危险性质分区、分类、分柜（或分库）存放，禁忌类危险化学品不应混合存放。 应保留与储存、使用危险化学品种类相符的化学品安全标签和安全技术说明书。',
            'result': '未按危险化学品性质要求分区、分类存贮；禁忌类混存；未留存危险化学品安全技术说明书',
            'technicalMeasure': '1.设置符合规范要求的专用储存室 2.使用、储存危险化学品的场所应配备相应消防器材',
            'manageMeasure': '1.按危险化学品的危险性质分区、分类、分柜（或分库）存放，禁忌类危险化学品不应混合存放。凡能混存危险化学品，采用堆垛方式码放的，货垛与货垛之间，应留有 1 m 以上的距离，包装容器应完整，两种物品不应发生接触。 2.保留与储存、使用危险化学品种类相符的化学品安全标签和安全技术说明书。',
            'educationMeasure': '定期开展安全教育培训，内容应包括：安全生产相关 法律法规、标准规范，本医疗卫生机构安全生产责任制、规章制度、操作规程、危险化学品、应急预案，疏散和现场紧急情况的处理 应对措施，典型事故案例等。',
            'protectiveMeasure': '正确穿戴防护用品',
            'emergencyMeasure': '1.发生化学品火灾立即报告领导，并采取针对性灭火措施，选用适合扑救该类物品的灭火剂，正确地实施扑救。扑救毒害性、腐蚀性或燃烧产物毒害性较强的易燃品火灾，扑救人员必须佩戴防护面具，采取防护措施。若火势难以控制，请求辖区消防队救援，消防队到达现场后开展灭火。利用对讲机、电话等多种通信手段通知无关人员紧急撤离，严禁无关人员进入火场 2.发生中毒事故后，迅速将人员脱离至空气新鲜处，立即送往急诊 3.发生爆炸事故，立即向上级汇报，并启动应急预案，对危险区域设立警示线，确保自身安全的情况下组织相关人员进入爆炸现场进行人员抢救。',
            'templateType': '1'
          },
          {
            'id': '1868598783142182912',
            'riskId': '2c91512e9391b5e00193ceefc9330290',
            'checkItem': '检测、报警装置',
            'standard': '1.储存有易燃易爆危险化学品的专用储存室外应设置静电消除器 2.储存有易燃易爆危险化学品的专用储存室内电气设备应符合防爆要求 3.设置气体浓度检测报警装置；气体浓度报警装置与防爆通风机联动',
            'result': '未按要求设置静电消除装置；室内电气不防爆；未按要求设置气体监测报警装置',
            'technicalMeasure': '1.设置气体浓度检测报警装置；气体浓度报警装置与防爆通风机联动 2.设置静电消除器 3.储存室内电气设备符合防爆要求',
            'manageMeasure': '1.根据所储存的危险化学品性质和特点，为作业人员配置事故柜、急救箱和个人防护用品。 2.定期检查静电消除装置、通风设施、气体检测报警装置有效性；机械通风正常通风换气次数不少于 6 次/h，事故排风换气次数不应少于 12 次/h；发现故障及时处理 3.定期检查储存室内电气线路及设施，确保防护等级满足防爆要求',
            'educationMeasure': '定期开展安全教育培训，内容应包括：安全生产相关 法律法规、标准规范，本医疗卫生机构安全生产责任制、规章制度、操作规程、危险化学品、应急预案，疏散和现场紧急情况的处理 应对措施，典型事故案例等。',
            'protectiveMeasure': '电气线路维修应持证上岗',
            'emergencyMeasure': '1.发生化学品火灾立即报告领导，并采取针对性灭火措施，选用适合扑救该类物品的灭火剂，正确地实施扑救。扑救毒害性、腐蚀性或燃烧产物毒害性较强的易燃品火灾，扑救人员必须佩戴防护面具，采取防护措施。若火势难以控制，请求辖区消防队救援，消防队到达现场后开展灭火。利用对讲机、电话等多种通信手段通知无关人员紧急撤离，严禁无关人员进入火场 2.发生爆炸事故，立即向上级汇报，并启动应急预案，对危险区域设立警示线，确保自身安全的情况下组织相关人员进入爆炸现场进行人员抢救。 3.发生人员触电事故，立即启动应急预案，迅速切断电源，或使用绝缘物体将触电人员尽快脱离电源，将伤员移至安全区域，必要时实施心肺复苏进行抢救，同时向上级报告，视伤情送至急诊进行救治',
            'templateType': '1'
          },
          {
            'id': '1868598783104434176',
            'riskId': '2c91512e9391b5e00193ceefc9330290',
            'checkItem': '\t\n废弃危险化学品储存、处理',
            'standard': '废弃危险化学品应存放在专门的储存场所，并指定专人负责管理；废弃危险化学品应交由有危险废物处置资质的单位进行处置',
            'result': '废弃危险化学品存储及管理不规范安',
            'technicalMeasure': '存放废弃危险化学品的场所、设施，应设置危险废弃物识别标志',
            'manageMeasure': '1.废弃危险化学品应存放在专门的储存场所，指定专人负责管理； 2.废弃危险化学品应交由有危险废物处置资质的单位进行处置 3.定期检查现场警示标识，发现损坏或缺失及时整改',
            'educationMeasure': '定期开展安全教育培训，内容应包括：安全生产相关 法律法规、标准规范，本医疗卫生机构安全生产责任制、规章制度、操作规程、危险化学品、应急预案，疏散和现场紧急情况的处理 应对措施，典型事故案例等。',
            'protectiveMeasure': '正确穿戴防护用品',
            'emergencyMeasure': '1.发生化学品火灾立即报告领导，并采取针对性灭火措施，选用适合扑救该类物品的灭火剂，正确地实施扑救。扑救毒害性、腐蚀性或燃烧产物毒害性较强的易燃品火灾，扑救人员必须佩戴防护面具，采取防护措施。若火势难以控制，请求辖区消防队救援，消防队到达现场后开展灭火。利用对讲机、电话等多种通信手段通知无关人员紧急撤离，严禁无关人员进入火场 2.发生中毒事故后，迅速将人员脱离至空气新鲜处，立即送往急诊',
            'adviceMeasure': '',
            'templateType': '1'
          }
        ],
        'responsiblePersonName': '王朋刚',
        'attachmentUrl': [],
        'handleElement': ' 1.1.发生化学品火灾立即报告领导，并采取针对性灭火措施，选用适合扑救该类物品的灭火剂，正确地实施扑救。扑救毒害性、腐蚀性或燃烧产物毒害性较强的易燃品火灾，扑救人员必须佩戴防护面具，采取防护措施。若火势难以控制，请求辖区消防队救援，消防队到达现场后开展灭火。利用对讲机、电话等多种通信手段通知无关人员紧急撤离，严禁无关人员进入火场 2.发生中毒事故后，迅速将人员脱离至空气新鲜处，立即送往急诊 2.1.发生化学品火灾立即报告领导，并采取针对性灭火措施，选用适合扑救该类物品的灭火剂，正确地实施扑救。扑救毒害性、腐蚀性或燃烧产物毒害性较强的易燃品火灾，扑救人员必须佩戴防护面具，采取防护措施。若火势难以控制，请求辖区消防队救援，消防队到达现场后开展灭火。利用对讲机、电话等多种通信手段通知无关人员紧急撤离，严禁无关人员进入火场 2.发生爆炸事故，立即向上级汇报，并启动应急预案，对危险区域设立警示线，确保自身安全的情况下组织相关人员进入爆炸现场进行人员抢救。 3.发生人员触电事故，立即启动应急预案，迅速切断电源，或使用绝缘物体将触电人员尽快脱离电源，将伤员移至安全区域，必要时实施心肺复苏进行抢救，同时向上级报告，视伤情送至急诊进行救治 3.1.发生化学品火灾立即报告领导，并采取针对性灭火措施，选用适合扑救该类物品的灭火剂，正确地实施扑救。扑救毒害性、腐蚀性或燃烧产物毒害性较强的易燃品火灾，扑救人员必须佩戴防护面具，采取防护措施。若火势难以控制，请求辖区消防队救援，消防队到达现场后开展灭火。利用对讲机、电话等多种通信手段通知无关人员紧急撤离，严禁无关人员进入火场 2.发生中毒事故后，迅速将人员脱离至空气新鲜处，立即送往急诊 3.发生爆炸事故，立即向上级汇报，并启动应急预案，对危险区域设立警示线，确保自身安全的情况下组织相关人员进入爆炸现场进行人员抢救。 4.1.发生化学品火灾立即报告领导，并采取针对性灭火措施，选用适合扑救该类物品的灭火剂，正确地实施扑救。扑救毒害性、腐蚀性或燃烧产物毒害性较强的易燃品火灾，扑救人员必须佩戴防护面具，采取防护措施。若火势难以控制，请求辖区消防队救援，消防队到达现场后开展灭火。利用对讲机、电话等多种通信手段通知无关人员紧急撤离，严禁无关人员进入火场 2.发生泄露，进入现成人员佩戴必要个人防护器具，根据泄漏物性质进行覆盖、收容、稀释等处理；如西楼物为易燃易爆，应严禁火种 5.1.发生化学品火灾立即报告领导，并采取针对性灭火措施，选用适合扑救该类物品的灭火剂，正确地实施扑救。扑救毒害性、腐蚀性或燃烧产物毒害性较强的易燃品火灾，扑救人员必须佩戴防护面具，采取防护措施。若火势难以控制，请求辖区消防队救援，消防队到达现场后开展灭火。利用对讲机、电话等多种通信手段通知无关人员紧急撤离，严禁无关人员进入火场 2.发生中毒事故后，迅速将人员脱离至空气新鲜处，立即送往急诊 6.做好危险化学品的合规采购，不得向未取得危险化学品安全许可的单位采购危险化学品，不得违规寄递危险化学品，发现违规采购管理危险化学品等现象及时上报领导',
        'judgeCreateTime': '',
        'sixthColumn': '',
        'firstColumn': '',
        'LECDLExplain': '',
        'riskLevelName': '较大风险',
        'thirdColumn': '',
        'judgeFrequencyScore': '0.5',
        'planFlowchart': '',
        'accidentTypeList': [
          {
            'dictLabel': '当心化学反应',
            'attachmentUrl': 'data:image/jpeg;base64,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',
            'dataDictId': '1',
            'dictSort': '7',
            'id': '1348830642425495552',
            'createPersonName': 'superUser',
            'dictKey': '7',
            'dictType': 2,
            'createDate': '2022-06-02 15:53:21'
          },
          {
            'dictLabel': '当心易燃物质',
            'attachmentUrl': 'data:image/jpeg;base64,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',
            'dataDictId': '1',
            'dictSort': '13',
            'id': '1348831378722979840',
            'createPersonName': 'superUser',
            'dictKey': '13',
            'dictType': 2,
            'createDate': '2022-06-02 15:53:22'
          },
          {
            'dictLabel': '当心腐蚀',
            'attachmentUrl': 'data:image/jpeg;base64,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',
            'dataDictId': '1',
            'dictSort': '18',
            'id': '1348831928415879168',
            'createPersonName': 'superUser',
            'dictKey': '18',
            'dictType': 2,
            'createDate': '2022-06-02 15:53:23'
          },
          {
            'dictLabel': '当心感染',
            'attachmentUrl': 'data:image/jpeg;base64,iVBORw0KGgoAAAANSUhEUgAAAKAAAADWCAIAAADZ1h6mAAAAGXRFWHRTb2Z0d2FyZQBBZG9iZSBJbWFnZVJlYWR5ccllPAAAAyZpVFh0WE1MOmNvbS5hZG9iZS54bXAAAAAAADw/eHBhY2tldCBiZWdpbj0i77u/IiBpZD0iVzVNME1wQ2VoaUh6cmVTek5UY3prYzlkIj8+IDx4OnhtcG1ldGEgeG1sbnM6eD0iYWRvYmU6bnM6bWV0YS8iIHg6eG1wdGs9IkFkb2JlIFhNUCBDb3JlIDUuNi1jMTQyIDc5LjE2MDkyNCwgMjAxNy8wNy8xMy0wMTowNjozOSAgICAgICAgIj4gPHJkZjpSREYgeG1sbnM6cmRmPSJodHRwOi8vd3d3LnczLm9yZy8xOTk5LzAyLzIyLXJkZi1zeW50YXgtbnMjIj4gPHJkZjpEZXNjcmlwdGlvbiByZGY6YWJvdXQ9IiIgeG1sbnM6eG1wPSJodHRwOi8vbnMuYWRvYmUuY29tL3hhcC8xLjAvIiB4bWxuczp4bXBNTT0iaHR0cDovL25zLmFkb2JlLmNvbS94YXAvMS4wL21tLyIgeG1sbnM6c3RSZWY9Imh0dHA6Ly9ucy5hZG9iZS5jb20veGFwLzEuMC9zVHlwZS9SZXNvdXJjZVJlZiMiIHhtcDpDcmVhdG9yVG9vbD0iQWRvYmUgUGhvdG9zaG9wIENDIDIwMTggKFdpbmRvd3MpIiB4bXBNTTpJbnN0YW5jZUlEPSJ4bXAuaWlkOjc5RTY1QUVCMDE2MjExRUI4MkY3QTg0MkJDMzY5ODhBIiB4bXBNTTpEb2N1bWVudElEPSJ4bXAuZGlkOjc5RTY1QUVDMDE2MjExRUI4MkY3QTg0MkJDMzY5ODhBIj4gPHhtcE1NOkRlcml2ZWRGcm9tIHN0UmVmOmluc3RhbmNlSUQ9InhtcC5paWQ6NzlFNjVBRTkwMTYyMTFFQjgyRjdBODQyQkMzNjk4OEEiIHN0UmVmOmRvY3VtZW50SUQ9InhtcC5kaWQ6NzlFNjVBRUEwMTYyMTFFQjgyRjdBODQyQkMzNjk4OEEiLz4gPC9yZGY6RGVzY3JpcHRpb24+IDwvcmRmOlJERj4gPC94OnhtcG1ldGE+IDw/eHBhY2tldCBlbmQ9InIiPz6vlG5PAAAzA0lEQVR42uxdB3xN5/t/777JzZQlEUEQJVYpSinRooOqomYjahSNRlWNiNiras/SXwetVaut0lapX9XeWxBB9p53r//3nKPnf5tx7zlJkPid55MP9577nvV+3+d5vs/zLtHhw4e3bdvm7Owsl8vNZjMR5JkQkUik0+mCg4Olf/zxR0ZGRlRUlMlk0mg0+EGonWcAXVdX1+vXr//4449SSFhYWNeuXYV6ecakSZMmV65cEQNgvV4vVMezJwUFBfhXbLVajUajUB3PnsDnglSJGZMtVMcz6YkpDRYq4tkWAWABYEEEgAURABbk6Yj0f+dV09LSUlJSEDk4OTk1bNhQoVAIAD8jcvfu3a+//nrfvn33799nAH7ppZcGDBjQt29fZ2dnwURXb1m+fHnnzp0XLFhw48YNjUaj1+vz8vJ++eWX8PDwHj16nDt3TgC4ugqUdeTIkRMnToRlLrXA33///eqrr+7atUsw0dVPLBbL6NGjv/rqK/vF8vPzocr40K9fP0GDq5NERkY6RJcRrVY7atSo48ePCwBXG1m3bt369eu5l4dXjoiISE9PFwCuHpw5Ojq6HGctXrxYALgayNKlS+FZy3Hixo0bb968KQBcpeXUqVObN28u37lqtXrOnDkCwFWaOQMhBLvlvsL27dt//vlnAeAqKtu2bTt48GAFLzJr1iyDwSAAXOUEBnbhwoUVv86FCxe++eYbAeAqJytXrrx+/XqlXGru3LnZ2dkCwFVIHj58uGzZssq6WlJSUqUYAwHgShNwq8rVuTVr1ly9elUAuEoIvObWrVsr95p6vX7+/PkCwE9fzGZzdHS0Vqut9Cvv3Lnz119/FQB+yrJjx47ffvuNzxlcB4FbrdaZM2fqdDoB4KcZGi1YsIB7+dD6qshBHtzf+syZM//5z38EgJ+arFu3jk9oJIn5WL50maFpsIT7LZYsWZKTkyMA/BQkOTmZVzDTrL7q7Xc18prqaR8qCFFyPOvBgwfVOmSqxgDPnj07NzeXc3HF3GlipY+BqMnAcaYuraTc333VqlWVlUIRAOYq8I5ffvkld+Pco72s9zA1KbSSDCJW6mKm4sVlHE82GAy8PL0AcCXI3LlzwXI5v6Rs5jQxkRoJnKmZ4N9X+mv6vCrlzqj5c3UB4IpFqPv37+euvsP7y9v3UkN3iYkQOSF5iIFMc2eIPVRcPTGi7ZiYmOo4U776AZyfnx8bG8u9vJ+nIjbGQqxmUkgPIhXRf5kk9GXNR8OBNldGfe7cuU2bNgkAP3aB642Li+NcXDphhCyouYZSXwkNrZX+UESpZeR4Y11/Jfdbf/7559VuYF41AxhBCwJT7uWfqyMbG6UnZgvR/Ptd8TmV+IRoJ48F5grud1+9erUA8GOURYsW8dEhZexEqXugjqSVoFN4by0hOmv4B+YW9XmM/q92IVN1Ahih0caNG7m/2svPSwd+YKSssb60F8WRDKLy1VIEm3Peo7CwcNasWQLAj0XmzZtnsVg4F5fNmCYWKXQku4y3xEEjIbmkzzBDry5K7iHTrl27qlHIVG0ARmjEZ7yjaGhv8av91CSLDo3KekvY5nz8q58xxeKs5DGPFDS+uvQyVQ+Ac3Nzob7cy3u6KGfHiKmkRr7dhBWjtFmkzWvq8eFi7iETzzyaALAjWb9+PZ8BNJIPw+XBL+go9ZU4Mr2ogEIqZIoabwn04aHEy5Yty8jIEACuBElMTFy7di338vVqKiKjjJT6FtHv5zChiRaQTvyb6sYPI9zn0yYkJKxZs0YAuBJk6dKlZc3gLjU0mhwp8QvRIMzlSptQB2pCDOYxk8zNgnnkPVauXHnjxg0B4ArJ8ePHN2zYwL18myaSiCgTBZiWz8uhZBpx89PEfEq45z0KCgp4JU0FgEuRBQsWcE/xi4liToxI6aIlmTzfjAmZ8si74YY3XpZzP2/37t18uj0EgP8t27dvP3DgAPfQqFdXxWt9DY/6BPm+mZSKiYmzYdYUs0TEg23Nnz+fT3QuAGxjAHmNTHZ1dp4xWUTkBlJQrqVHGIedS9q8aho9kEdX8alTp6rywLyqC/C6deuuXbvGPTSKfE/Suged2SDc0SmhxHkETeTTSSYfDxX38+bMmVNle5mqKMAPHz7k1W9Ty8spMtJMZa0KK/BOTECVSeq1MkQOs3C/UFJS0qpVqwSAH19oJBsfIQpoqkU4S0QVrg9YeLNp/KeWhgFO3M9DpF41Q6aqCPDp06c5roDECILXcVPNRGehoqMKvpCIvkIS8aylnzXVwj15mZ+fXzV7maocwFardcaMGUVFRZzPUMyYTFy9NZWgvo+8OR0yFVgHjzB1bcuDTv/www9VcC5TlQMY1XTo0CHu5d/oJOs/VE+RIxN3fXNo8qmRl8TZFEspMY/cVhVU4qoFsMFg4NVrpJA5z4m2EJWBArgSV2Vkhm4VWju/bhr4Jo+8B1/n8j8H8PLly/n0GonGDpa27m6itM1aSfbZNmTKhPbqZ88wero48VLirKwsAeBSJDk5+fPPP+dePsDb+ZNPTERsoDp9JZX9NEyLySIh7YxR1OharvYhMTGR11v8DwG8aNEiPm1fPHaoNbCZnmQ8zropoPaXGv+JoY4fDyVev379rVu3BIAr5L1CAp0jP7UQo7lCmQ2HnlhEja6tUUf36XgRdyUuKCiYO3euAPC/ZPbs2XxWqJPOmmbyCNBTnb7iyva+xUImAyFq68iRuheb80hebt++vYoMzKsSAO/evZvXCnWvv6waFGEm+Vaq9h/3kuYSyhMr/EyzPjVzn5BosVhiY2NNJpMAMLWezcyZM/mcoZw20YQglerdk3EYkVPxGgKyRZYe7+jf6ebC3VycOXPm22+/FQCmKAmvuQKD3pR17G6kZvryDY0sdDKk2BEuPbkwEtlU3mN2tMlZziO3Vemrd1U/gBFUwPtyL+/l6jxrhknkZKCCVI6hkZVGEf+6EeL/j0Yyeo8jLjYF7DeObGvTLhq6q5hrTPbw4cMVK1b8TwO8ePHivLw87v4wcpgMgSmFrogbtIzfrEVIHZWF1E26o9TqH3lSs4Xoi7yJog6pIyd+/wa+1HrCY1rMU6O1tb0V3E3HUx+Y9zQB5ruuax0f1fiJRmI2UeGpwwdnzK83IQGqnKSA3d94h38Q+u7w4EyNmHhQCWat1W3m0k5joxr9usPXpA8itSXEnbbhljJCJnpCol8j08Sxcu71VlhY+HRDpqcGMBjmjBkz1Go1d/WNnWb1qqclyRxCIzNdoB4h8qANn9V/pZdbv+HJ3+86GP9Qp3RSUT/JiVTu9sex3A1f//76wIKevb33fNuCuAeSwLIdMwyzjhCtZdQYTavG/HqZfv/99/85gPft28dnQB3p+Lzq/fd1RMMhNDLTI1/rKOMutezdK3DslPuXrt8CaHKpx5jwGjVqSKhuYx1RumjfHyjzcK1NSMFvf13oG5E6elgjdUEwCaYbh6UMtpVBVAGm+dPpNsJNzGYzmnJF1qGvfgAXFRXxNFyy6AkW4m6i2Kz90IjppA8QnTwS3Ku36adD5+l8o3LUkLCTh31mz7oj1RdRMx6gi4U548adOXHEbdDbTQlxhf3d9P3ZQQP9sh/AK5dBu0R069FYXuuvfaOjklfItH379v8hgL/66qsrV65wLi7q2031ej8DKbJSXfEiR4S5Nkm8Xm/YcMOdh9foqcHyXq+0Xr8+vdXLt4kxnxhNxInu5LVaiS6/8QvXt25Ln/FRPRrjgp+PXB0+0t+orkN8SsRUNnkPIjfNmmqUinh0cYBO8lnVq/LyNF26dBGLxS+//PITu2VSUlJ4eDj3MRvOctXWTVbfhlqS4ihCgXp5EYsoKGKY/4mLcfR3EugfvGunxTPgKimSEBmYlJg4i4hKTFwlRCImWnwuCgvTXzlb41Y8iLLh9r1CS1FI195FxKil3IG4hBLTRKxWC3PqLZdz1ziG0gQBsUwm69q16xOr56ysrBMnTjyFvQvRltPS0niERuHy0M4FlN5YHQVFcL3u8h3rPH88FE/rLnFz9f55q0udRjdy7ysfPGiWme2VnW1ISyuyWImbizKojtLXV+vve9GvUeGW70PbdHC6eecmIfnLvkh9b3D9Ru1yyIPSepoldMjkbo2Zbt73mywt18QxnbZs2bKBAweGhoY+ydp+0gCfOnWKzzIMoiAfp08+1YKoOJjpywDsSUy5Qeu+VpB/OhE7tA1yVdVdNFP6/da7abn3tfpsrdZsMTObqkhVKqnKyeCiMPbr2zR8cGDocwU371A/aPUJqzcErmlXn7jEk6IyQqYMUqupJnKkW8wSPWMqHAp41qxZs0Cqn2WAob58Nq0RfzJO6huiJkkc0keod2fJmT/0py8ls8cuX09o1yMpO1dHTyA006M0/l8Qo6np9dE+W5WwZmOCSFLI2vrdB1InntMGP09IURmeWAtbbR0Xqdu8XXE7kStD3rt3L0Km7t27P5ska//+/T/++CP38q1DnUeO1hCdtfRVVIoJeJOx5m9/+xlN/w9walpudm4GM9bZ7skFGl2BWv3/ljYtI+7o32IicS+zYUmpWcWeQabZk2Xce5kQMs2bN+9Jrpj35AAuLCyMjY3lvsAkam1+tMg5wESpmENDQ49+tGQrr9zwqqSXkt6+35RoPKlw11qGwQBMWvPAUbqw1s7cQ6Zjx4599913zyDAW7duvXjxInfv+3pH5x59tURteZSWIo4csJxk5+UmJt2vrPjiQaLeUmS0F3ZLaJOv0M+ebuGe94AsXLjwiYVMTwhg0GZeK/IqpbIFsSaiMFLkmcszWikNTszIuXkjzlHcIpFI6lLhlCObUFSkNRrN9haBENFD5HNIpz7qwT0V3HuZ4uPjly9f/kyRrKVLlz58+JD7U43o59SyGx0amTk/o5UoZDKlUqoxlroDi1vXTi26tM/w9LjToK64oMjy4KE8Ma3twUNFdx9cKr3ti2jDIXJUfwiZ3CwzYqwH/pTmqc0c3xAA9+vXr3nz5s8CwOfPn+e1ikpNT/nU6UZwVHoRK44WnaK1dWr6NGte+7/Hz/2bUrlGjXnxvXfvNg1NUPgyua579HE5MaZlJmmO/rfDJzPyEpOKd+oFBrrKVTIq1yGye18qp0Cea1f0YYTb/LXmMhJgxaWoqAgh0549e54FEz1nzhw++xqJI0fIApvqKG4l5rOQio641FDVr+tTzKSKROLQxk6tw3wVvimEJFMKl0x1/JF8A5Hd9alXVL++t9lYclUzo5+Plrg4So4yOkKFYNaoCebgAB7zXH766aejR49We4B/ooV7+WYN5JEfa4nJQgWa3MdsUH0AhLint256vpgPtlrzJ0z9491e0i1rmiTdfJ541KL6gz1R2DMzqeXqBY179D6dkn6vhGrWaBuaTCSZDjSYxTiT+DTQTIuU8eplmj59utForMYAq9VqnhvESedNlbkHGCkN42KcLf+M2aiJP/xXNOBdSb3aIcVTSFrND/uPh49PDXtT+l5f5yunGx750WdQL0lYV8lH0+OyckuZnN+/V4vXBsiJSP8oxLU/pkdENy+dZXikpmNLOfeQ6cSJE487ZHq8Pvibb745c+YM99b2env5WxGaR71GUkdai0p3ItRoG2tAQUr+9VseaanNNHpPF9eyZjdl303An8jHu11+oXL7/quElDWRwumFZg916jSJwUfmqSJOqUSnp8oay/YaEip5KQnSz5yi7DZISSe6OMncuXPffPNNX1/f6gdwZmYmry1fRURKbYYiMVOTySQcFBdaq/K4f9lvz8/1fth79/K1+waT1UKZVPsDGa0mS7qri/0bGD/fEL/xWxLatHmrlt6tm1u7dTcrAjNIDj2NUVya4RM9Wojp1Xc1Pdcr9v8l5tjLlJCQsGrVKl5zKqsKwIsXL7537x73JxncS9nhLTWFjsnuc1npv0AUC1g6p9HKL1ISU47S5+CP06oPBqPCbLZf+6bMbCqHEZ90/qdfcTPnri81iBzp1Scig6hSSXIZzk1Kzyr2MM6bpTzymlxj4Loc7Zo1a4YOHfrcc89VJx987tw5XqGRl6t0RoyF6jXKs6u+jGWuS7TqgPChDSfNvJKYEkePz+A4h0DRuGHLd3v6NmugpImW48ZAK2b+kePn3xmePDYipLCwATXUS1TGeA9CJahbhKkjI2RVZPmHxwXw7Nmz+ayoLJn4gbxRW3rrDFHZHIVhy4FEnRk4YnjQln2XHFnjYuIxaVyrY4eNXV+/MPajxNWfNXN2CuRzetaGby/06e2d9TCEGtNjLQ1jKdP7ZPlkorlOTQX3S+/YsePw4cPVBuADBw7wWuCvbk2nMWNNxEJvnWHHONMDNoio9tix9bb9FEcv5s1D3n6t6ZKlWq/a10lukUyZG/lp8tQJjbh3BNFSePjE1VFj/U3qkDLH9EipZS99G2mnjhNzX/aSyRZUD4D1ej3PJTrl06NkNYJ11CoqUrvECpzZ3WPV4rpb9tyk1x0srXqlYrnMnS5aXF5um0CU9yk3baIMKSH3n296gWNfvW3ot+/ghckTfYmqJjWKy1xGyKS3RnxoaRfKI2T666+/HsfAvMoH+D//+c/58+e5c+dOLRUjxmuoRZB0jpKCfiTlZtCCFdllhzdE5Rz85ZqGv++s1bVDG3yzfdOcPCnVw8fGtcRcu1ZZXTpuCrkdzS5cvvHhmd/rEt8y1qOmQyZlDc2UCYSXEs+YMaPSt7KtZIBTUlJAnvmcoZg5WSxS6R8lJu1wK2o6gc/WbaL0rLv24huDpGXz/G797/+yXz13+gv09COIsltY7SH9nyeW+pRpxV8wIQVuXm6Nv13Vzt/P1RacIP9Ge7e0HDvM/pT+pDXUuKMQSolLZVsGKmTqM0Lf4yXCXYnv3r27fv36Kg0wAl8+vUbit8Kkr7yrpsytw/GwbsSY4XHwqDtdeZQ0rFvr5faNyL/Hrmp0d37cnYATlJ43YubdjfmoHgD7ekXo7/vrHT//MGKQz9lDIUTSIuVuaMSIgJff0tQOEF850rnX683os917dQ39cY/i7aEXavsW2I/EDx7NvnVSR2U9RWV4YqiiyDBvhkTKR4lXrFiByLiKxsGXL1/+4osvuJev4eK0cI6YiOmZvg4fRCW+fDzj5NlHtFmpcNv2TW2VKqBNmKGoyLZGLHEJrYihgOTfJqLk2OnWMWOa6fKLBg9N3bYXxQy/HQ15oaVf3N3UO/dA00xd+6VPj2q4YHrzkDpOHipzzLwUokwlZt+4e95w0rb39/F2b9+67bGTl3ILqIFdWdl3fv3D/bn2KiJTl8K2mKlsGeSF7rqP3pcv+0rEceRlVlYWQqZKnFhcmRo8ffp0PivUST+MkDXpqHYQGrFezeqakOin1TEuStXv9dqtO9/08Lrlpig+4O3PE+bUm2LKDmcSmW+Kyi0+rG/Gtr1xjOqnZdze//vvd+5d/YcE6+avvDpg+IUxo+vHfH6fGFIR5xhzat1MqF/sst6e7vv2+kyZWIseIk/JnQcvEG0ApZ/W0kyOjA6ZROYpk0lQTRfu1bh58+a///67ygF86NChX375hXv5IF/5uHH0gFO1o5QAs52kXpmSWZfhTSKx55s9AhAm1XC/Vb+urpj6J6de3bIbGLhT/lcjfvgwJy31jv2HuXEnWaOho2qwaxfx1Sv6C1eK5eDELrJskdOO99831vRi/LooLVNkKbCWOabHStduOkIm40fDRbziMZ5LHjx+gE0mU0xMDC9uFR0pqdmY3lXQ4SNYH00K0mgeqbm7q7FuPS0xyuSusoEDirFl6nFWbSqIP9+AmhZssdTyzWsc4mT3NqrRQxs0b/mAiqBqULq6/msPtaaYI/Ts1L4psVp8at9+vYszHYZZ0zPyC4v09pyLmA6ZTKZho00hgTwGbR05cmTnzp1VCGC4Xj69RqJ2TWTDPjJT42G5bJ3B+DO5qbZ/EaMscqlB5ZpLWVyT+c3uyf6+HsXOSE6723eYLDGuFXEhNdxVLZq1sLvkpKzl835ETBfwcjqyr953u+4XdxFi8xs9JZQW6kWdw4JFIspKSyVSiVjsoE9BRCXIvetqZk4hvJR49uzZlTIwrxIATktLW7hwIffyIiKPnSZSumschEbFshwyvUqVSGsE0eilGZkeBJVbYKrTPGl0eD1Cik3YNV++fr5Xb6eE66Eib0VwfTf7CW6zwUisehIgu3a29fBItU5fvNOiy0t1O3eIJ4UGYpK7eYhEYqqdBQa4urgriNFRBVPJbDL4A3OPDk7cQ6YbN26sXLmySgC8ZMmS5ORk7vi+1kn8xhCd49CoWJKSqANrZsqllOoUFckuXfQlYgmVG7EUfTQ+rWlIvZIh8eW4s/0HWg7tdo+/d58NrkqlcK7uJuKsiL/WcvCQvIfJJfcR8Hp/sLfUV8usR52cbLbQnVGBfpnETc9pTA+ooUwXO9UqJjxWzFu9ejWf7rjHA/D169f5zDUicoliXoyYiIycQiNbQ1dobdkmqMOLrXFax9bOHcNuE/BnHE+y1gi6s3yuM70SRzExnL92q3u/B9/vus3MRStDtKnp0jP/bflWn5yrd0rZJSKsQ+1+A6G+BXQnkvnYsQJ6OzxF62bJRJrseEwPEyLlkA49dBH9ecSlOTk5ixYtqiBAFZ0+Om7cOIS/3NvTxJHS8CgdybI+Gh3BHWAjkbgY/V1UtfyUm7dkBDVKMqW5i10NFHA6a3A7kym/6bFTBYwNL2Hf7V5bZLp0RbtxU15q1v1SCJhTvS1fOAeF3iHJFiqb5mpyk0iOHPdyUtZfOFvu4p5GTYsRcdAjsA1vS2ig9PsflFo919lZ165d69SpU926dcsBDTN9tEIafODAgd27d3MvX9tXNekTERFZyrPzDRpERkGPt64vWpcuEoumT2japrP32VPPkSAXqt4LMuYtiYsa3YzhwY5ial9bl2y1kqycVJ0psbTCteZN9W/bLZ4km6i7KBVwFD0GJR78QfTFZ3I/j0Squ5L7Wl1ZpEF7Q+QQHoaTWUC7IvsylR9go9HIM1yTR41AUKinYk0J/xXqmDEwUr0pW96+nduClfcv3b4fPk60Z0tj4l6PuEmINHHFF5fnxQYrFe52LuPl6b76884Ng7noRK3YqKAJU66T7HwK3UDFgQPNR4xtffmvoOYdE94edFpkyCM8urzpKXDEOG6iuUEAj7zH4cOHd+3a9RQA3rx587lz57iXD60nGzHeTK0PqynvbWlDZ7Yqa/rVoH2q+Vb8jb7hCYP7ue75j1/KtZaGgoDp0Q369OxY9iU8Yj72iPzkeHRUgP0RHc5ONZfE1Jy97BZR5FOZKx/Xoz+/MOoT9Vfb9r81uGbqlYbEqiMWni9CMQbi10A3dYKIVy/TrFmz+KwmVhkAgzbzVd+YScTDn8+moKU+bB5RyFN27hO905PNb2Rt23ul70j1q31lnbuoXnstLu7uFZGo1Ht4zY+uP2G6muSmRIx7MOnD56lsVxmR8chB9SfNzSLiXF2q/4Uz3SLD/d7ofzcl9QYhLgP7qF18cyiaxfdFmLVrCy3vjdJ1bs0jZLp582a55zKVs7Nh6dKlvEKjHh0UA0fQQxKNPMdQlKyjXKKsdWXH9qB1a0LnLsrLyrtL2+78m7fP2nkAJ3m9z+b6RE5OIJoMeq3+h0tWK62k1dK1pQ6eNZ+4cO3TjwPuXHdNTCpITE/MzEnAQQmpuWiW36SZKaQgkzpJWq7nzyLyevo50dKwvnIL4TpReM2aNUOGDAkJCXkSLBrU7sMPP+Q+UV9EnL9ZLQ4KpdVXUrHlnZlz84nYJb9dWOGrHQPib/skJJpKI8+s+L/4/HNfrFQOGnWTFORS2RUZHeYos7v3Jv4eNS9ccC6kFrGyHZxhTUnTnzhVFHfPlJql02gBpnuzkGZfrHYZHnmXFOZR3Epa3ue3UP/WaULuXnG6EscVYK1Wq9FoevfuzZdF8wYYjA7oXrp0ifudIocpPpioJ3kWThP1udSRiO6o0er9Q1MG91fWruGt0wdIJPX1BoXR+Ci9J5PWbRAc2rW9ZMZEtyVLzCGtrpAUet9KyT9XAMaWvBe6ZvXu5qUgnnpjndR0HT1AU2yTgqKGgbV9vsWU8R4rl+tadLhK0vX8IviyEtTe1saB4i07ZXrOU1fi4uLCwsJq167NC2DeT/rbb7/xCo18PVwmT0QEaeQxVZALxhKaZiUQqc/9UZPko0Y3Sk4wJKYY7t63mk1EIiGB/saGDYy1GluI9AaF5YN/z0RlroBH0hgbNI1bssEz/0HQ0aOm2/F14h+GGEwWscTi5iTy873XuGFe164ml1q3iK6I6iC2VszFsHfPsjbuZJwQ4TxnrYT7Gi6xsbG//vqrVMqjHvlVuU6n47lCnSRyOKnd3MB7x2aO/sxKr6dTYCCuV2s1IbVakBdtiCAxJFPQqv/JiZbs15PSncLgEspcd7/c3sMIRW7zE6jQGH9yEVGpKV6nOUd1NOn/aRaVkiCGLfE2jv9Y/80O5cMsrgt2ImTasWMHnPHjYtFbtmw5efIk9/L1/VUfjLcQi6k8nJOjKkgfTbOnHHwiFYc8+kukhq8+Wn1FQsqcqM8ApqXHWT7EKVnEdJdY4onlHtHGk9RM6mAmzX4lldpGmV6m+vqPx1h5XXfhwoW8lr3kcemMjAyeUwXl0z4W+XJcH7biXpn8M+/BYjMZUMThviKbxzPQ3r2A/pdRfbZApZsfLbXdx7iJhnahLtxvcP36dV7DGnk8+Lx58x48eMC9/CvtnIaN0pMCnmnnpy4s3qLHfCN67Vq5p2X2dH6+ctWqVfHx8ZUM8MWLF3ltZC4iTjMnm6QeOiqikBBBygyZCi09+uh6v8KjGzEvL4+7KeUKMMwCL9M/pJe80xs0c7Y+flWovsJsc6o0zo7WK+U82Pn333/PkQxxAvjAgQO8Vlh0d3aOnmQkSsOjRZAswl/Zf6DxmdYWXY3jBjlxt3UGg2HmzJlc9mVybPqhuHPmzOHeYyUikvnRpPHLWurp/QT77EiYKZPEPH+x/shJ2aXbFo4dbYcOHYLWDRo0qKIAHzly5PTp09wfOKSu04C+Tnq11ZAkFklEgn12LGYiyrGqaov69VRdXpZrJVxzwBs3bhwwYIBYLK4QwHyXvy3UGkdFuRbmuxrN1jJ6dQQprsNiYnVyFuUVmSRSHvvhQfEuXLjwwgsvlB/gtLS0Y8eO8XrclHT9vt9TBWZVLqAJL43QarWgRxUC+Ny5c3wmkxV/XEF41xrPajt79myFWPSVK1fMZrNQ71VWUlJS7M8HcwDwU9koRBDukp6eDozLD7DAkqq4QAPtb3AqduQSBFdapcXHxycgIKD8ANevX1+oxCoOcK1atcoPcPfu3b28vIR6rLLSsWNH+wM8HAAcHBw8duxYoR6rpnh7e0dFRVUoTIJER0d369ZNqM0qKAsXLnQ4bckxwE5OTt99993bb78tVGjVEXd397Vr144cOdJhSU7dhb6+vnv37t20aVP79u2Fyn264ubmNmTIkEOHDo0bN45LeR4jRdBeBg4ceOTIkWPHjp05cyY+Ph5BmFgsFmLl8gmqTq/XO1yyFcVgRMGWmzVrFhYW1rVr1xYtWnC/C79hsy4uLm/RUlhYmJaWVlBQIKJFQIuvoNLAfvPy8hITE61Wq0QiKTUJYTabobKBgYE1atTw9/eXy+V8b1TOoeiutAg4VQM7IVSBALAgAsCCCAALIgAsiACwIALAgggACwALIgAsiACwIALAgjxm4dTZkJ+fb7FY7E9yEuRJCjPa1dXVtdRuKB4A40KnT5++fPmyk5MTABZG0VYRdGUyWU5Ojqen5zvvvKNUKssPcEZGBtB98cUXfXx8eEx7E+QxAyyXy9PT048dO5abm+vv719+gAEqGkj9+vVdXFyEmq1S4ubmduPGDYc21YFbhYkXiUS8VucQ5MmIWq02Go0OiREn3iQMyqmCwnGwlECMhThYEAFgQaqsSB/3DfLy8u7cuQNvAdZXq1YtlUpV7epIp9P9+eefycnJeIXOnTv7+fkReu0DDw+PoKCgilw5Ozv71q1bCoUCV65Xrx6i2+qnwbt37+74j0yYMEGv11c7gFNTUz/44INRo0YNGDBg9erVN2/e/Oijj1566SV8zc/Pd3g6uG5ERMTatWtL/rRhwwZUS6dOnfAvr+0BnybAhYWFtl8NtADXzMzMe/fu2RK/48ePnzhxooK327t374gRI6BPjw9gd3d3dsS5j4/PxYsXAXNRUdGpU6dgnByevmLFim+//RZtouTGGszyGrAQqBwul3r6JjomJubIkSO7du1ip53bjo9v0qSJ7dj8adOmXbp0afz48TNnzizHmH20j88///ynn36yWCyoqR07dpTjga1WK1okTod5LBl14FdnZ2d4Gfbx6tSpA4UD5Izu7tmzp3nz5nYe/pdffpk8eTKht0KYOHFienr6rFmz2OSi7fz6N954o0oDDO1Eda9fv57QuyqyBsd2kR7bZCegPX/+vEajWbBgwenTp2fPng2jx+uOW7Zs2bdvH/MZH6DEqGu+jw1ohw8fjrbi6elZalgJ8PCQCQmPthMGSDVq1NBqtczXxYsXw4/a2dgeWHp5ebHLaKA8mn54eHhcXJyvr++1a/+/VeLZs2dRUq1WJyUl4dfIyMjnnnuuEoBBg5o3b561DIH7gXnJyMiwOpLBgwez10QVMFk0yDfffMMeHz16NFv+/ffft32M6OhoK085c+YMNIm9wqRJk6z8JTc3t4L12LZtW/u3QNNv0KABUzgwMPDvv/8eOHAg+XdfkOgfYS+LYvYvC1AADQAqqwC4Aiq50nzwuHHjoATM55ycHBhehk+Vmiy9fPny9u3b2a8vvvgiXBTfO7Zp08b2rK+//prVM+4Cy+zt7V2RF4cNL+snuFU0O7FYfO7cOcY+LVu2DB/u3r3LkBXWvLGoMF+h3KDoVctE47mXLFnCTkk+fPgwzDVos+0KEuyStTAbbH4b9QtsmNiDr+B2uEtWVhYTcuA6c+bM4euD2Vpu1arV22+/DaPNHsEDw8aCJMIdQGNwpGnTpq+88gpMFJQP2gOfamdHIkQQS5cu3ULLwYMHt27d2r9/f5h3dsooVJYF1cXFBT8xt4ZfqKyQqTJZNNhsnz592K+w/HAnqAv2iJMTta75l19+yfpOpli5jSTCUNu9olCD8GHlfn6g2717d9Q+6hePCtVE7QNdsCQWRbwgWPHYsWMRI61Zs+aHH36ArSr1avHx8XhTQne5vvbaa3DeiLWYSnjvvfdiY2PXrVsH1skURrtBC3j11VdZS16+Fv/YWfTcuXPBohmGCZX6888/QRTZdgoSgVaPgJIt/9ZbbzGvXW6BlYabZxo+6hSfP/zww/JdCliCBCHuKullQd+YLUAZIwR/BB8J8wtvWtYqRmAh7M4KeP3//ve/UFCYB1QOw6tx8KuvvmIKhISEIBRm0yaw6g6HajydODg0NBR0iWGP06dPR3tnCBpLWdF+YbWY5gnsmTbOV0DXhwwZAvuMQAt10atXL/YnEPhyL64J0sA8KlO/bBXjOFSKZQ+g3OB3aWlpABjY42upV4Ml37BhQ82aNfFZpVKh3SCICAsLwzMzBRBGs+E7Ay27obsTLVU0VQnzBRKBSABNsliYZDQawR1grPr27QuY+/Xr5+Pjw+Wa27Zt+/3338HF2rdvj8YO48byqa5du4IrMjYfEQh0iwFm06ZNsB9oCvYXgkNhliWgUQISGBV8YJamYPwlnhMPwJRBRIewCq6HzWLaMvli5AvGqV27djNmzGjYsCHeHehev34dNgxABgcHwySw+z9CMYjNyqB4R9RbcnIyHFwFh1pUDsB4+v3798MioakqFIoOHTqASINZoBn+/PPPbDEEvps3b0adolLQ9lFNa9euBdvq0qULKLGd6//0009g3TC/bm5uq1atwvVZgBs1aoQ7+vv7A2kE07AKV69e/eyzz7777jsu/Nx2lQxoGC6Lt4APBrrMGhpw6pmZmXhaoM6knCC21B3u0871W7ZsuXPnzilTpnTu3LmgoIAJMVBX8NxsHIGmAOwBNqsMwBuvHBUVVa9ePcSfY8aMKfeIx8oBGLWAyoX5YlWh1LVF/qaF+QxU2Lw0vKZ9gNn0J+qISR2wiUO0Ldi3v/76i4k14eF69uzJrnH9xRdfQL/tKAE8CFSKBXjRokU//vhjsTKAEFoLts8qLuv+oe72a+bs2bO45p49e9gjYJSdOnXCK7OrxAJdtEtGuZkjFy9ezMvLS6EF7w5SVu4FMyrHBzNLith2v3BxeLbpHjsl8apweGxM1axZM3aFZPhyJk3IZhJwqXfeeceW6UBd7Fwcj8qkpRCWwJiXmnRkDGmxcAi6Bf5v58pQRzgI2BVbdEHUERMDSyblxwiCSYagsAmyb7/9luV6uFFF/HGlAWzkvE1qSYE22/kV6sjSUWAAfWUrouRAT1yKMdS2/tvOxaGUjOdDJaL1MJYQTgQcAiGTbTq9devWtv6Voe5wQ2Wt5gu+DZxsV+uOiIjYuHEjToRGsgdxIyY6Yrpk2Pdicwbw1ry2G30sJhp6A0t44cIFcCj7w6eZMb2oTTAgln/a37MHZordwR6aCnvLBrs4seS94KcRiU2dOpVNucArQ+9LvfitW7eYu8PaQ1cY24NWAjYObwJmxzwzfDPQYs/y9PSEQh86dIjhklBlWN1ibhKv+frrr7PZZgSQMTExMPW2uVvQN/ayZSXD2eD4aQIMy+ZwVcxiArZVVoBRUoPZz8wYYIedymhtsIHMTotoDaBCy5YtK7UkyxtgG0BtGIBx/fnz57P3hRPNyspi3QQE/HbEiBGM0UI7AAk6efJkyeQiVBZWGvqH5sKE+4jo4DKY1BvTCFAPCLLBxXB3BI2MZUYLgxLj+rAiFexleuwjOsoS1sw6FHgs9nOdOnVKtczFBOo4dOhQdoM/MFLY7ZI8BTgxOsq4OiDEJFBhe8HD2WK2fT6ssDkKxoqWOlKlSZMmaBxgVSwXY/gB9B6OBi1mGy0wNgAY6juUlsqt58oEePfu3dAVBS1lWWnGRMOknzlzhiM/Z7eTx4nMKn5ctmFDqA2QGCWDCz9w4MCAAQOKlUEkyuolswxnqdurIlCBscFPYEO4YMmxGUCrWOoYT4jawJMjkIWTXrx4MVwsIEQ7wE/wF3BS7K1Bu5YuXYoC+AneHaaeeUGEDAgC+/fvX5GsVmUCjDD3l19+qdwGiFqAm2Q5M5O15jJFCrENQs8//viD+YoHKwbwlStXWBWHcsMYolqHDRsGIKHKZlpwHBELguB27drBkMDeAgYcKTYQxTbfzlLoTZs2wUkDm5KZNbAEPS0MP/2DllLfAnF8nz59qgrAj2NqGtBlEwtwwEzil4sGQ6WAGVtxMMW3b98OCQlhC8TFxbFuAjoKM4sPc+bMwe3wIoAWSgYzA5odHR39888/MwkTHITe4wjLlVDStouFBZgxHqXmTZmMB5dKY9S6qphomBewLZhf+7QebRZNEi/JJVyGErBVAAzst2WURHl86NatG+4CgGfOnMmgmJ6e/tdff9kC/Oabb8I2AviDBw/CBSKeAT9fs2YNvCahx2GBJ+/cuTMnJ4fQo+MQ0bLtDAEV3DyMJ8OES+49hrtDrdHIcE2mr4XJ3yEkYy5YqoBhMNMGmXdBYV9f3yrkg8eOHYvAAI9oP6/GvOq6desWLFhg/4Jgm+BH7FfYyZJNvthXEFGcAjBQ+whdGjZsCFPs5eWF0LMYHUVzbETLu+++26VLF5wIInb69GkwLyZ3Bgt/6tQpprpxTZjlDh06QL8XLly4YsUKNGU0C9uesWLh+PLly6HozFAvaCFCczhjmAo74wzRVmBCYLphNvBeUAB4JftZoCcKsDctHAuzXaF2BJXLRhSQkoqCerQdUIFKgSKigg7SgtAIwQzoFSgMk80vVaDEjJvv2bMnPDczhAqqg694AKbzEdeEtQfA3333HTM4EkemTZsWGBiINl3qZYNosc3HwYXDZth53y1btkDjP/7447Ki9qeWySqHcLHPqKAffvhh7ty5r7zyCvhzyVHmqDVbF4XWYBvSAGw4zl9//dUOutBINiUCV4p4hnGQYO/QPIaXgfpOnDgRXxGkDho0CDAz5dEOoHO2NqYsOXLkSJs2bWD/mawnLBzcB9NnCkLHLvCdkZEBK4KS8AiVtedc5WgwnhvPBHoJ22vfPkPJ4KFBVY4ePcolQdaDlsmTJ+PlSw5ySExMjIqKQu1DlYH0999/jyO24anDW+BcJukPWwrbi8CGIUfQ3Rs3bqCud+zYAQBgyaHKsbGxH3zwAT6zZhZxM6wuXHVZ6YjU1NSVK1fCpNsmZxA1oTyTosJ9odmgDmzkfefOHTi71atXo1mjDBxThUZ3VMqoyvz8fF7rzBcT6BD3cZAAsljeH3SmZs2axbwDGgdYlZ3rABu2752xzzho23+ANwJVPnny5IULF1DdbC2DqUVERNi2Y6h+yevfv39/48aNjRs3LvayaEb4FU2ECZ1xHeY5p0yZUmrlQO8rMqqycjQYlf7E5hAz7amYqy5ZDKjbH+oFAw7NY1OtzEAUkG3bLCaAhL0pljhDYSAHu81kPBo0aMAaeVtBm4Dvtz0C7w7/zfQ0sDdC1TEmBOwdzRSXstV18Gq2z+Np+mBUekUA5tWbjcJcpnwBG/vDRfAr3CeYNqGHCzKZrJLDP0qmRaHBUD7QXRAxuCT4+LZt25a8Poy5LSuE1Tl8+DDbj4R2w7y1mBbm4IQJE2CrWR8PYUaDPH0f7ObmhjADLRGG0SHSCJM8PT2XLFkCl8myFV63gx7gLjCeUCbbxmE2m3G8bt26IEdcNmwDk9+zZ8/69evZUfvh4eFQa5CJsk6BkjFD5mAhdu/ejZZd1vaOAAYWHhiDToInwtnbEn5E54ymQoNt0/JocL/99hu0H/4e9+rbt2+VIFnArBUt3E9BW0ZwAjzw/nyjAtQpYACfKrapD+Mp4IzR4LiHdtAS9is0GJBDKcGl2c5/aBscNnweQAXrYfWVGXRmR8DeEe7jYUriBFYIZQDGDOW0/QmREsgBNB6xeMWHvz+13iRwY7BEtgb5no4KYkdxVK7AAIwZM6ayrjZ8+PBSj7/00ktwEAjGgG6pisF3plaVA7h8uD4zAsL1ZHaEFJZweMZFAFgAWJBnG2Bhd8KqKRxBcQAwE1lWx6VxnnlBNIVAy+FwAAc8FrEgMzINASICVmHJ6KogABUqd//+/TNnzjiMpqQO2Xz79u2TkpIAcwXHjghSiZKXl1dUVNSiRQt2VYVyAgyVtTODXRCBRQsiACyIALAgAsCCCAALAAsiACyIALAgVQ3gci8sJUhVFgst1Jgmh+kuQaqjuLm5qVQqqVqtlsvlzCJNwuZ1z4woFIqioqKcnBxp3bp1mVknMpmsIivlCFKlBGhmZ2c7Ozv/nwADAAJZLw5xqRtWAAAAAElFTkSuQmCC',
            'dataDictId': '1',
            'dictSort': '19',
            'id': '1348832078450327552',
            'createPersonName': 'superUser',
            'dictKey': '19',
            'dictType': 2,
            'createDate': '2022-06-02 15:53:23'
          }
        ]
      }
      if (this.detailsInfo.analysisList && this.detailsInfo.analysisList.length) {
        this.detailsInfo.analysisList.forEach((item) => {
          analysisListArr.push({
            checkItem: item.checkItem,
            standard: item.standard,
            result: item.result,
            technicalMeasure: item.technicalMeasure,
            manageMeasure: item.manageMeasure,
            educationMeasure: item.educationMeasure,
            protectiveMeasure: item.protectiveMeasure,
            emergencyMeasure: item.emergencyMeasure,
            adviceMeasure: item.adviceMeasure
          })
        })
        this.detailsInfo.analysisList.forEach((item) => {
          analysisListArr2.push({
            riskFactor: item.riskFactor,
            operationElement: item.operationElement,
            controlElement: item.controlElement,
            handleElement: item.handleElement
          })
        })
      }
      this.tableData = analysisListArr
      this.tableData2 = analysisListArr2
    },
    // 查询重置
    reset() {
      this.formInline.investigationType = ''
      this.formInline.createtTmeStart = ''
      this.formInline.createtTmeEnd = ''
      this.checkRiskInvestigation(this.rowData.id)
    },
    handleClick(tab) {
      if (tab.name === '1') {
        this.getRiskDetail(this.rowData.id)
      } else {
        this.checkRiskInvestigation(this.rowData.id)
      }
    },
    checkRiskInvestigation(id) {
      CheckRiskInvestigation({ riskId: id }).then((res) => {
        if (res.data.code === '200' && res.data.data.riskInvestigationId) {
          GetRiskInvestigationRecord({ ...this.formInline, riskInvestigationId: res.data.data.riskInvestigationId, currentPage: 1, pageSize: 999 }).then((res) => {
            if (res.data.code === '200') {
              this.riskInvestigationRecordData = res.data.data.list
            }
          })
        } else {
          this.$message.error(res.data.message)
        }
      })
    },
    getRiskDetail(id) {
      const analysisListArr = []
      const analysisListArr2 = []
      GetRiskDetail({ id }).then((res) => {
        if (res.data.code === '200') {
          this.detailsInfo = res.data.data
          if (res.data.data.analysisList && res.data.data.analysisList.length) {
            res.data.data.analysisList.forEach((item) => {
              analysisListArr.push({
                checkItem: item.checkItem,
                standard: item.standard,
                result: item.result,
                technicalMeasure: item.technicalMeasure,
                manageMeasure: item.manageMeasure,
                educationMeasure: item.educationMeasure,
                protectiveMeasure: item.protectiveMeasure,
                emergencyMeasure: item.emergencyMeasure,
                adviceMeasure: item.adviceMeasure
              })
            })
            res.data.data.analysisList.forEach((item) => {
              analysisListArr2.push({
                riskFactor: item.riskFactor,
                operationElement: item.operationElement,
                controlElement: item.controlElement,
                handleElement: item.handleElement
              })
            })
          }
          this.tableData = analysisListArr
          this.tableData2 = analysisListArr2
        }
      })
    },
    hiddenDangerDetailCloseDialog() {
      this.hiddenDangerDetailsListShow = false
      try {
        window.chrome.webview.hostObjects.sync.bridge.GetSwichFloorIsShow(false)
        window.chrome.webview.hostObjects.sync.bridge.ShowDialog(false)
      } catch (error) {}
    }
  }
}
</script>

<style lang="scss" scoped>
.form-detail {
  height: calc(100% - 30px);
  width: 100%;
  overflow-y: auto;
  overflow-x: auto;
  box-sizing: border-box;
  .plan-content {
    // width: 100%;
    // padding: 20px 0px 20px 20px;
    color: #b5bacb;
    font-size: 13px;
    .item-row {
      width: 100%;
      display: flex;
      padding: 12px 0px 20px 30px;
      box-sizing: border-box;
      .width33 {
        width: 33%;
        display: flex !important;
      }
      .width95 {
        width: 95%;
        display: flex;
      }
      ::v-deep .el-image__error,
      ::v-deep .el-image__placeholder {
        background: center;
      }
      .li-first-span {
        display: inline-block;
        width: 120px;
        // margin-right: 20px;
        font-size: 14px;
        font-family: PingFang-SC-Medium, PingFang-SC;
        font-weight: 500;
        color: #B0E3FA;
      }
      .li-last-span {
        display: inline-block;
        width: calc(100% - 120px);
        font-size: 14px;
        font-family: PingFang-SC-Medium, PingFang-SC;
        font-weight: 500;
        color: #ffffff;
        // align-items: center;
      }
      #audio-box {
        display: flex;
      }
      #audio-box > audio {
        width: 260px;
        height: 30px;
      }
      #audio-box > a {
        width: 40px;
        text-align: center;
        background-color: #2cc7c5;
        height: 35px;
        line-height: 35px;
        color: #fff;
        border-radius: 5px;
        margin-left: 10px;
      }
    }
  }
  ::v-deep .el-tabs {
    .el-tabs__nav {
      .el-tabs__item {
        padding: 10px 32px;
        text-align: center;
        font-size: 14px;
        color: #A4AFC1;
        height: unset;
        line-height: unset;
        background: linear-gradient(360deg, #334572 0%, rgba(38,49,79,0.14) 57%, rgba(36,46,73,0) 100%);
        border: 1px solid;
        border-image: linear-gradient(180deg, rgba(113, 125, 168, 0), rgba(113, 128, 168, 1)) 1 1;
        border-top: none;
      }
      .is-active {
        color: #B0E3FA;
        background: linear-gradient(360deg, #3A668E 0%, rgba(36,46,73,0) 100%);
        border: 1px solid;
        border-image: linear-gradient(180deg, rgba(113, 125, 168, 0), rgba(113, 128, 168, 1)) 1 1;
        border-top: none;
      }
    }
    .el-tabs__active-bar {
      display: none;
    }
    .el-tabs__nav-wrap::after {
      height: 0;
    }
  }
  .search-box {
    padding: 20px 10px;
  }
}
::v-deep .detailDialog {
  width: 60%;
  height: 90vh;
  margin-top: 7vh !important;
  background-color: transparent !important;
  background-image: url('@/assets/images/table-bg.png');
  background-repeat: no-repeat;
  background-size: 100% 100%;
  border: none;
  pointer-events: auto;
  box-shadow: none;
  .el-dialog__body {
    padding: 10px 50px;
    height: calc(100% - 60px);
    max-height: 80vh;
  }
  .dialog-title {
    display: inline-block;
    width: 100%;
    text-align: center;
    transform: translateY(-6px);
    color: #cbdeed;
  }
  .dialog-title::before {
    display: none;
  }
  .el-dialog__headerbtn {
    transform: translateX(-36px);
    width: 25px;
    height: 25px;
    background-image: url('@/assets/images/close.png');
    background-repeat: no-repeat;
    background-size: 100% 100%;
    .el-dialog__close::before {
      display: none;
    }
  }
}
::v-deep .el-table {
  border: none !important;
  .el-table__header-wrapper {
    .cell {
      padding-left: 0;
      padding-right: 0;
      text-align: center;
      white-space: nowrap;
    }
  }
  .el-table__body-wrapper {
    td.el-table__cell div {
      // overflow: hidden;
      // white-space: nowrap;
      // text-overflow: ellipsis;
    }
  }
  .el-table__body {
    tr {
      background: center;
    }
    td.el-table__cell,
    th.el-table__cell.is-leaf {
      border-right: 2px solid #0a164e;
      border-bottom: 2px solid #0a164e;
      background: rgba(56, 103, 180, 0.2);
      color: #fff;
    }
    .el-table__row:nth-child(2n - 1) {
      background: rgba(168, 172, 171, 0.08);
    }
    .el-table__row:hover {
      border: 0;
      opacity: 1;
      cursor: pointer;

      td div {
        color: rgba(255, 202, 100, 1);
      }
    }
  }
}
</style>
