<template>
  <ModuleCard title="报警统计" class="middle-content" :style="{ height: '50%' }">
    <div slot="title-right" class="middle-right">
      <i class="el-icon-s-operation" style="margin-left: 10px" @click="showAlarmList()"></i>
    </div>
    <div slot="content" style="height: 98%">
      <div class="search-data">
        <el-dropdown trigger="click" @command="handleCommand($event)">
          <span class="el-dropdown-link"> {{ dateTypeName }} <i class="el-icon-caret-bottom"></i> </span>
          <el-dropdown-menu slot="dropdown">
            <el-dropdown-item v-for="(item, index) in dateList" :key="index" :command="item.val" :class="{ isBjxl: dateType == item.val }">{{ item.name }}</el-dropdown-item>
          </el-dropdown-menu>
        </el-dropdown>
        <el-date-picker
          v-model="dateRange"
          class="datePickerInput"
          popper-class="date-style"
          unlink-panels
          type="daterange"
          value-format="yyyy-MM-dd"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          :disabled="dateType !== 'custom'"
          :picker-options="{
            firstDayOfWeek: 1
          }"
          @change="dataPickerValChange"
          @focus="setWPFBgShow()"
          @blur="setWPFBgHide()"
        >
        </el-date-picker>
      </div>
      <div class="statisticsHeader">
        <div class="alarmStatisticsItem" @click="showAlarmList()">
          <div class="name">全部报警</div>
          <div class="detail">
            <span class="value">{{ allCount }}</span>
            <span class="unit">件</span>
          </div>
        </div>
        <div class="alarmStatisticsItem" @click="showAlarmList(0)">
          <div class="name">未处理</div>
          <div class="detail">
            <span class="value">{{ unDisposed }}</span>
            <span class="unit">件</span>
          </div>
        </div>
        <div class="alarmStatisticsItem" @click="showAlarmList(1)">
          <div class="name">已处理</div>
          <div class="detail">
            <span class="value">{{ disposed }}</span>
            <span class="unit">件</span>
          </div>
        </div>
      </div>
      <div id="alarmTypeAnalysisPieEcharts" ref="alarmTypeAnalysisPieEcharts"></div>
      <div class="bottom-title">
        <div>电梯报警排名</div>
        <el-dropdown trigger="click" @command="alarmChange">
          <span class="el-dropdown-link middle-font"> {{ alarmTypeName }}<i class="el-icon-arrow-down el-icon--right middle-icon"></i> </span>
          <el-dropdown-menu slot="dropdown" class="alarmTypeDrop">
            <el-dropdown-item v-for="(item, index) in alarmTypeList" :key="index" :style="{ widht: '120px' }" :command="item.parameterId" :class="{ isBjxl: alarmType === item.parameterId }">
              {{ item.parameterName }}
            </el-dropdown-item>
          </el-dropdown-menu>
        </el-dropdown>
      </div>
      <div id="elevatorAlarmRanking" ref="elevatorAlarmRanking"></div>
      <DialogEmergencyFrame :visible.sync="allAlarmVisible" :paramsData="paramsData" @update:visible="initAlarmEvent" />
      <!-- <template v-if="allAlarmVisible">
        <AlarmRecord v-model="allAlarmVisible" :alarmTypeList="alarmTypeList"  :projectCode="projectCode"> </AlarmRecord>
      </template> -->
    </div>
  </ModuleCard>
</template>
<script>
import * as echarts from 'echarts'
import { getReasonStatisticPie, alarmTypeList, getMonitorStatistic, clinePoliceCount } from '@/utils/elevatorApi'
import dayjs from 'dayjs'
import 'dayjs/locale/zh-cn'
dayjs.locale('zh-cn')
export default {
  components: {
    // AlarmRecord: () => import('./alarmRecord'),
    DialogEmergencyFrame: () => import('@/views/normalMode/leftScreen/EmergencyDisposalNew/components/DialogEmergencyFrame')
  },
  props: {
    projectCode: {
      type: String,
      default: ''
    },
    roomData: {
      type: Object,
      default: () => {}
    }
  },

  data() {
    return {
      regionCode: '',
      allCount: 0,
      unDisposed: 0,
      disposed: 0,
      dateType: 'day',
      dateTypeName: '今日',
      dateList: [
        { name: '今日', val: 'day', startTime: dayjs().format('YYYY-MM-DD'), endTime: dayjs().format('YYYY-MM-DD') },
        { name: '本周', val: 'week', startTime: dayjs().startOf('week').format('YYYY-MM-DD'), endTime: dayjs().endOf('week').format('YYYY-MM-DD') },
        { name: '本月', val: 'month', startTime: dayjs().startOf('month').format('YYYY-MM-DD'), endTime: dayjs().endOf('month').format('YYYY-MM-DD') },
        { name: '本年', val: 'year', startTime: dayjs().startOf('year').format('YYYY-MM-DD'), endTime: dayjs().endOf('year').format('YYYY-MM-DD') },
        { name: '自定义', val: 'custom', startTime: '', endTime: '' }
      ],
      dateRange: [dayjs().format('YYYY-MM-DD'), dayjs().format('YYYY-MM-DD')],
      alarmType: '',
      alarmTypeName: '全部报警类型',
      alarmTypeList: [],
      allAlarmVisible: false,
      paramsData: {}
    }
  },
  watch: {
    roomData: {
      handler: function (val) {
        const ssmCodes = val.ssmCodes?.split(',') || []
        this.regionCode = ssmCodes.at(-1)
        this.initAlarmEvent()
      },
      deep: true,
      immediate: true
    }
  },
  mounted() {
    const ssmCodes = this.roomData.ssmCodes?.split(',') || []
    this.regionCode = ssmCodes.at(-1)
    this.getAlarmTypeList()
  },
  methods: {
    initAlarmEvent() {
      this.getAlarmTypeAnalysisData()
      this.getMonitorStatistic()
      this.getClinePoliceCount()
    },
    // 获取电梯报警数据
    getMonitorStatistic() {
      const params = { projectCode: this.projectCode, surveyEntityCode: '', page: 1, pageSize: 10, regionCode: this.regionCode, startTime: this.dateRange[0], endTime: this.dateRange[1] }
      getMonitorStatistic(params).then((res) => {
        if (res.data.code === '200') {
          this.allCount = res.data.data.allCount
          this.unDisposed = res.data.data.unDisposed
          this.disposed = res.data.data.disposed
        }
      })
    },
    // 时间切换
    handleCommand(val, time = []) {
      this.dateType = val
      const obj = this.dateList.find((el) => el.val === val)
      this.dateTypeName = obj.name
      if (val !== 'custom') {
        this.dateRange = [obj.startTime, obj.endTime]
      } else {
        this.dateRange = time
      }
      this.initAlarmEvent()
      this.getAlarmTypeList()
    },
    dataPickerValChange(val) {
      this.handleCommand('custom', val)
    },

    // 报警类型切换
    alarmChange(val) {
      this.alarmType = val
      const obj = this.alarmTypeList.find((el) => el.parameterId === val)
      this.alarmTypeName = obj.parameterName
      this.getClinePoliceCount(val)
    },
    // 获取报警类型数据
    getAlarmTypeAnalysisData() {
      getReasonStatisticPie({
        projectCode: this.projectCode,
        startTime: this.dateRange[0],
        endTime: this.dateRange[1],
        regionCode: this.regionCode
      }).then((res) => {
        const data = res.data
        if (data.code === '200') {
          this.$nextTick(() => {
            this.setAlarmTypeAnalysisEcharts(data.data)
          })
        }
      })
    },
    array2obj(array, key) {
      var resObj = {}
      for (var i = 0; i < array.length; i++) {
        resObj[array[i][key]] = array[i]
      }
      return resObj
    },
    // 报警类型数据echarts
    setAlarmTypeAnalysisEcharts(data) {
      const getchart = echarts.init(this.$refs.alarmTypeAnalysisPieEcharts)
      const nameList = Array.from(data, (item) => item.name)
      const sum = data.reduce((per, cur) => per + cur.value, 0)
      const gap = (1 * sum) / 100
      const pieData = []
      const gapData = {
        name: '',
        value: gap,
        itemStyle: {
          color: 'transparent'
        }
      }
      for (let i = 0; i < data.length; i++) {
        pieData.push({
          ...data[i],
          itemStyle: {
            normal: {
              // borderRadius: 5
            }
          }
        })
        pieData.push(gapData)
      }
      var objData = this.array2obj(pieData, 'name')
      let option = {}
      if (data.length) {
        option = {
          tooltip: {
            trigger: 'item',
            formatter: function (params) {
              if (params.name) {
                return params.marker + params.name + ' : ' + params.value + '    (' + ((params.value / sum) * 100).toFixed(2) + '%)'
              }
            }
          },
          legend: {
            type: 'scroll',
            orient: 'vertical',
            top: '10',
            x: '55%',
            pageIconColor: '#5188fc', // 激活的分页按钮颜色
            pageIconInactiveColor: '#c2c6ce', // 没激活的分页按钮颜色
            data: nameList,
            itemWidth: 8,
            itemHeight: 8,
            itemGap: 16,
            // formatter: function (name) {
            //   if (name) {
            //     return '{c|' + objData[name].percent + '}{a|' + name + '}'
            //   }
            // },
            formatter: function (name) {
              const maxLength = 6 // 设置每行的最大长度
              if (name) {
                const wrappedName = name.replace(new RegExp(`(.{${maxLength}})`, 'g'), '$1\n')
                return '{c|' + objData[name].percent + '}{a|' + wrappedName + '}'
                // return '{a|' + wrappedName + '}{c|' + objData[name].percent + '}'
              }
            },
            textStyle: {
              rich: {
                a: {
                  align: 'right',
                  fontSize: 13,
                  color: 'rgba(255,255,255,1)'
                },
                c: {
                  align: 'center',
                  width: 70,
                  fontSize: 15,
                  color: '#00C2FF'
                }
              }
            }
          },
          series: [
            {
              type: 'pie',
              roundCap: true,
              radius: ['56%', '72%'],
              center: ['30%', '50%'],
              hoverAnimation: true,
              label: {
                normal: {
                  show: false,
                  position: 'center',
                  // formatter: '{value|{d}' + '%' + '}\n{label|{b}}',
                  formatter: function (data) {
                    if (data.name) {
                      return '{value|' + objData[data.name].percent + '}\n{label|' + data.name + '}'
                    }
                  },
                  rich: {
                    value: {
                      padding: 5,
                      align: 'center',
                      verticalAlign: 'middle',
                      fontSize: 14,
                      fontWeight: '600'
                    },
                    label: {
                      align: 'center',
                      verticalAlign: 'middle',
                      color: '#A3A9AD',
                      fontWeight: '400',
                      fontSize: 14
                    }
                  }
                },
                emphasis: {
                  show: true,
                  textStyle: {
                    fontSize: '12',
                    color: '#fff'
                  }
                }
              },
              data: pieData
            },
            {
              name: '',
              type: 'pie',
              startAngle: 80,
              radius: ['54%'],
              hoverAnimation: false,
              center: ['30%', '50%'],
              tooltip: {
                show: false
              },
              itemStyle: {
                color: 'rgba(10, 25, 40, .3)',
                borderWidth: 1,
                borderColor: '#1E2C39'
              },
              data: [sum]
            },
            {
              name: '',
              type: 'pie',
              startAngle: 80,
              radius: ['48%'],
              hoverAnimation: false,
              center: ['30%', '50%'],
              tooltip: {
                show: false
              },
              itemStyle: {
                color: 'rgba(21, 36, 50, .1)'
              },
              data: [sum]
            },
            {
              name: '',
              type: 'pie',
              startAngle: 80,
              radius: ['77%', '78%'],
              hoverAnimation: false,
              center: ['30%', '50%'],
              tooltip: {
                show: false
              },
              itemStyle: {
                normal: {
                  color: 'rgba(10, 25, 40, .1)',
                  borderColor: '#1E2C39',
                  borderWidth: 1
                }
              },
              data: [sum]
            }
          ]
        }
      } else {
        option = {
          title: {
            text: '暂无数据',
            x: 'center',
            y: 'center',
            textStyle: {
              fontSize: 14,
              fontWeight: 'normal',
              color: '#999'
            }
          }
        }
      }
      getchart.clear()
      getchart.setOption(option)
      // 随着屏幕大小调节图表
      window.addEventListener('resize', () => {
        getchart.resize()
      })
      getchart.dispatchAction({
        type: 'highlight',
        seriesIndex: 0,
        dataIndex: 0
      })
      getchart.on('mouseover', (e) => {
        // 取消默认高亮
        getchart.dispatchAction({
          type: 'downplay',
          seriesIndex: 0,
          dataIndex: 0
        })
      })
      // 鼠标移出后默认高亮
      getchart.on('mouseout', (e) => {
        getchart.dispatchAction({
          type: 'highlight',
          seriesIndex: 0,
          dataIndex: 0
        })
      })
    },
    // 电梯报警排名柱状图
    getClinePoliceCount(parameterId) {
      clinePoliceCount({
        projectCode: this.projectCode,
        startTime: this.dateRange[0],
        endTime: this.dateRange[1],
        parameterId: parameterId || this.alarmType,
        regionCode: this.regionCode
      }).then((res) => {
        if (res.data.code == 200) {
          this.$nextTick(() => {
            this.initAlarmRanking(res.data.data)
          })
        }
      })
    },
    initAlarmRanking(data) {
      const getchart = echarts.init(this.$refs.elevatorAlarmRanking)
      let option = {}
      if (data.length) {
        option = {
          tooltip: {
            trigger: 'axis'
          },
          grid: {
            left: '12%',
            right: '15%',
            top: '10%',
            bottom: '5%',
            containLabel: true
          },
          xAxis: [
            {
              type: 'value',
              position: 'top',
              splitNumber: 3,
              splitLine: {
                lineStyle: {
                  color: '#283849',
                  type: 'dashed'
                }
              },
              axisLabel: {
                textStyle: {
                  color: '#fff'
                },
                fontSize: 11,
                interval: 30000,
                hideOverlap: true // 隐藏互相遮挡的文本标签
              }
            }
          ],
          yAxis: [
            {
              type: 'category',
              data: data.map((el) => el.name).reverse(),
              axisTick: { show: false },
              axisLine: {
                lineStyle: {
                  color: '#283849',
                  type: 'dashed'
                }
              },
              axisLabel: {
                textStyle: {
                  color: '#fff'
                },
                margin: 8,
                interval: 0,
                hideOverlap: true, // 隐藏互相遮挡的文本标签
                formatter: function (val) {
                  var strs = val.split('') // 字符串数组
                  var str = ''
                  // strs循环 forEach 每五个字符增加换行符
                  strs.forEach((item, index) => {
                    if (index % 6 === 0 && index !== 0) {
                      str += '\n'
                    }
                    str += item
                  })
                  return str
                }
              }
            }
          ],
          series: [
            {
              type: 'bar',
              barWidth: 8,
              sort: false,
              data: data.map((el) => el.value).reverse(),
              itemStyle: {
                normal: {
                  color: '#0A84FF'
                }
              }
            }
          ]
        }
      } else {
        option = {
          title: {
            text: '暂无数据',
            x: 'center',
            y: 'center',
            textStyle: {
              fontSize: 14,
              fontWeight: 'normal',
              color: '#999'
            }
          }
        }
      }
      getchart.clear()
      getchart.setOption(option)
      // 随着屏幕大小调节图表
      window.addEventListener('resize', () => {
        getchart.resize()
      })
      getchart.dispatchAction({
        type: 'highlight',
        seriesIndex: 0,
        dataIndex: 0
      })
      getchart.on('mouseover', (e) => {
        // 取消默认高亮
        getchart.dispatchAction({
          type: 'downplay',
          seriesIndex: 0,
          dataIndex: 0
        })
      })
      // 鼠标移出后默认高亮
      getchart.on('mouseout', (e) => {
        getchart.dispatchAction({
          type: 'highlight',
          seriesIndex: 0,
          dataIndex: 0
        })
      })
      // 先移除点击事件 解决点击事件重复绑定
      getchart.off('click')
      // 鼠标点击效果
      getchart.on('click', (params) => {
        this.allAlarmVisible = true
      })
    },
    // 打开报警记录
    showAlarmList(type) {
      this.paramsData = {
        projectCode: [this.projectCode],
        dataRange: [this.dateRange[0], this.dateRange[1]],
        alarmSpaceId: this.regionCode
      }
      // this.handlerResult = type
      this.allAlarmVisible = true
      try {
        window.chrome.webview.hostObjects.sync.bridge.ShowDialog(this.allAlarmVisible)
      } catch (error) {}
    },
    // 报警记录列表
    getAlarmTypeList() {
      alarmTypeList({ projectCode: this.projectCode }).then((res) => {
        if (res.data.code === '200') {
          this.alarmTypeList = res.data.data
          this.alarmTypeList.unshift({
            parameterId: '',
            parameterName: '全部报警类型'
          })
        }
      })
    },
    setWPFBgShow() {
      try {
        window.chrome.webview.hostObjects.sync.bridge.DateMask(true)
      } catch (error) {}
    },
    setWPFBgHide() {
      try {
        window.chrome.webview.hostObjects.sync.bridge.DateMask(false)
      } catch (error) {}
    }
  }
}
</script>
<style lang="scss" scoped>
.el-dropdown-link {
  color: #fff;
}
.isBjxl {
  color: #fff;
  background-size: 100% 100%;
}
.search-data {
  display: flex;
  background: rgba(133, 145, 206, 0.15);
  padding: 0px 10px;
  ::v-deep .el-dropdown {
    padding: 7px 6px;
    .el-dropdown-link {
      font-size: 14px;
      font-weight: 500;
      color: #8bddf5;
      line-height: 16px;
      position: relative;
      cursor: pointer;
    }
    .el-dropdown-link::after {
      content: '';
      position: absolute;
      width: 1px;
      height: 12px;
      background: rgba(133, 145, 206, 0.5);
      top: 50%;
      right: -6px;
      transform: translateY(-50%);
    }
  }
  ::v-deep .datePickerInput {
    flex: 1;
    padding: 8px 10px;
    height: 16px;
    box-sizing: content-box;
    background: none;
    border: none;
    .el-input__icon,
    .el-range-separator {
      line-height: 16px;
      color: #b0e3fa;
    }
    .el-range-input {
      background: none;
      color: #a4afc1;
    }
  }
}
.statisticsHeader {
  width: 100%;
  height: 72px;
  display: flex;
  justify-content: space-between;
  padding: 13px 16px;
  background: rgba(53, 98, 219, 0.06);
  > div {
    width: 33%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    border-right: 1px solid #fff;
    &:last-child {
      border: none;
    }
  }
  .alarmStatisticsItem {
    .detail {
      margin-top: 6px;
      .value {
        color: #ffca64;
        font-size: 20px;
        margin-right: 4px;
      }
    }
  }
}
#alarmTypeAnalysisPieEcharts {
  margin-top: 16px;
  width: 100%;
  height: 146px;
}
#elevatorAlarmRanking {
  width: calc(100% - 5px);
  margin-top: 16px;
  height: 146px;
}
.bottom-title {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 16px;
  height: 32px;
  background: rgba(133, 145, 206, 0.15);
}
.alarmTypeDrop{
  .el-dropdown-menu__item{
    width: 140px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
}
::v-deep .el-table--striped .el-table__body tr.el-table__row--striped td {
  background-color: rgba(168, 172, 171, 0.08) !important;
  /* def2ff f2faff */
}
</style>
