<template>
  <!-- 菜单管理 -->
  <div class="content special_box">
    <div class="top_content" style="margin-bottom: 20px">
      <el-input
        style="width: 217px; margin-right: 20px"
        placeholder="请输入菜单名称"
        v-model="searchDataObj.menuName"
        maxlength="25"
        @keyup.enter.native="_searchByCondition"
        onkeyup="if(value.length>25)value=value.slice(0,25)"
      ></el-input>
      <!-- <el-input
        style="width: 217px; margin-right: 20px"
        placeholder="请输入所属模块"
        v-model="searchDataObj.officeName"
        maxlength="25"
        @keyup.enter.native="_searchByCondition"
        onkeyup="if(value.length>25)value=value.slice(0,25)"
      ></el-input>
      <el-input
        style="width: 217px; margin-right: 20px"
        placeholder="请输入上级菜单"
        v-model="searchDataObj.mobile"
        maxlength="25"
        @keyup.enter.native="_searchByCondition"
        onkeyup="if(value.length>25)value=value.slice(0,25)"
      ></el-input> -->
      <el-button class="sino-button-sure" style="margin-left: 30px" @click="_searchByCondition">查询</el-button>
      <el-button class="sino-button-sure" @click="_resetCondition">重置</el-button>
      <el-button class="sino-button-sure" style="float: right" @click="selectRowData({}, 'add')"
        ><div class="img-add-icon"></div>
        新增</el-button
      >
    </div>
    <!-- </div> -->
    <div class="table_list">
      <el-table
        :data="tableData"
        height="calc(100% - 40px)"
        :cell-style="$tools.setCell(3)"
        :header-cell-style="$tools.setHeaderCell(3)"
        style="width: 100%"
        v-loading="tableLoading"
        element-loading-background="rgba(0, 0, 0, 0.2)"
      >
        <el-table-column fixed type="index" width="80" show-overflow-tooltip label="序号"></el-table-column>
        <el-table-column fixed prop="menuName" show-overflow-tooltip label="菜单名称"></el-table-column>
        <!-- <el-table-column fixed prop="menuUrl" show-overflow-tooltip label="菜单地址"></el-table-column> -->
        <el-table-column fixed prop="belongModule" show-overflow-tooltip label="所属模块">
          <template slot-scope="scope">
            <span>{{ scope.row.belongModule === 1 ? '左屏' : scope.row.belongModule === 2 ? '中屏' : scope.row.belongModule === 3 ? '右屏' : '' }}</span>
          </template>
        </el-table-column>
        <el-table-column fixed prop="menuParentName" show-overflow-tooltip label="上级菜单"></el-table-column>
        <el-table-column fixed prop="menuSort" show-overflow-tooltip label="排序"></el-table-column>
        <el-table-column fixed show-overflow-tooltip label="操作">
          <template slot-scope="scope">
            <div class="operationBtn">
              <span @click="selectRowData(scope.row, 'detail')">详情</span>
              <span @click="selectRowData(scope.row, 'edit')" style="margin: 0 10px">编辑</span>
              <span @click="selectRowData(scope.row, 'del')">删除</span>
            </div>
          </template>
        </el-table-column>
      </el-table>
      <el-pagination
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page="currentPage"
        :page-sizes="[15, 20, 30, 40]"
        :page-size="pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
        class="pagination"
      ></el-pagination>
    </div>
    <!-- 新增用户弹框 -->
    <template v-if="addDialogShow">
      <addMenu ref="addMenu" :dialogTitle="dialogTitle" :rowData="rowData" :type="type" :addDialogShow="addDialogShow" @sure="sure" @closeDialog="closeDialog"></addMenu>
    </template>
  </div>
  <!-- </sinoPanel> -->
</template>
<script type="text/ecmascript-6">
import addMenu from './dialogComponents/addMenu'
import { getMenuPage, delMenu } from '@/utils/api'
// import { getMenuPage, isMenuHaveChild, delMenu } from '@/utils/api'
export default {
  name: 'menuManagement',
  components: {
    addMenu
  },
  data() {
    return {
      tableData: [],
      currentPage: 1,
      pageSize: 15,
      total: 0,
      addDialogShow: false,
      tableLoading: false,
      searchDataObj: {
        menuName: ''
      },
      dialogTitle: '',
      rowData: {},
      type: '',
      dialogType: {
        add: '新建',
        edit: '修改',
        detail: '查看'
      }
    }
  },
  mounted() {
    this._resetCondition()
  },
  methods: {
    // 选中列表行数据
    selectRowData(row, type) {
      if (type === 'del') {
        this.userDelFn(row.id)
      } else {
        this.addDialogShow = true
        this.rowData = row
        this.type = type
        this.dialogTitle = this.dialogType[type] + '菜单'
      }
    },
    // 查询
    _searchByCondition() {
      this.currentPage = 1
      this.menuTableList()
    },
    // 重置
    _resetCondition() {
      this.searchDataObj = {
        menuName: ''
      }
      this.currentPage = 1
      this.pageSize = 15
      this.total = 0
      this.menuTableList()
    },
    // 获取菜单列表
    menuTableList() {
      const params = JSON.parse(JSON.stringify(this.searchDataObj))
      Object.assign(params, {
        page: this.currentPage,
        pageSize: this.pageSize
      })
      this.tableLoading = true
      getMenuPage(params).then((res) => {
        // console.log(res)
        const data = res.data
        this.tableLoading = false
        if (data.code === '200') {
          this.tableData = data.data.records
          this.total = data.data.total
        } else {
          this.$message({
            message: data.data.msg,
            type: 'warning'
          })
        }
      })
    },
    // 删除菜单
    userDelFn(id) {
      // isMenuHaveChild(id).then((res) => {
      //   console.log(res)
      //   delMenu()
      // })
      this.$confirm('删除后将无法恢复，确认要删除该记录吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        cancelButtonClass: 'sino-button-sure',
        confirmButtonClass: 'sino-button-sure',
        customClass: 'confirm-box-class',
        type: 'warning'
      })
        .then(() => {
          this.tableLoading = true
          delMenu(id).then((res) => {
            const data = res.data
            this.tableLoading = false
            if (data.code === '200') {
              this.$message({
                type: 'success',
                message: '删除成功！'
              })
              this.currentPage = this.currentPage === 1 ? 1 : this.currentPage
              this.menuTableList()
            } else {
              this.$message({
                type: 'warning',
                message: data.msg
              })
            }
          })
        })
        .catch(() => {})
    },
    handleSizeChange(val) {
      this.pageSize = val
      this.menuTableList()
    },
    handleCurrentChange(val) {
      this.currentPage = val
      this.menuTableList()
    },
    closeDialog() {
      this.addDialogShow = false
    },
    sure(type) {
      this.addDialogShow = false
      if (type === 'detail') {
        return false
      } else if (type === 'add') {
        this._resetCondition()
      } else {
        this.menuTableList()
      }
    }
  }
}
</script>
<style lang="scss" scoped>
.pagination {
  // bottom: 22px;
  // text-align: right;
}
.content {
  position: relative;
  width: 80%;
  height: 97%;
  background-color: #031553;
  box-sizing: border-box;
  padding: 20px 10px 10px 10px;
  margin: 10px 10px 10px 10%;

  .table_list {
    height: calc(100% - 60px);
    // overflow-y: scroll;
  }
}
</style>
