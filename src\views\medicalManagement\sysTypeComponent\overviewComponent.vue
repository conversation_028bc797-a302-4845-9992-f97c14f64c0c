<template>
  <div class="overview-chart-container">
    <div class="chart-box" style="height: calc(25%)">
      <ModuleCard title="资产状态统计" style="height: 100%">
        <div slot="title-right">
          <div class="chart-title-box">
            <div class="chart-title"></div>
            <div class="chart-btn">
              <div class="chart-icon">
                <img
                  src="../../../assets/images/order_more.png"
                  class="order-more"
                  @click="allTableChange"
                />
              </div>
            </div>
          </div>
        </div>
        <div slot="content" style="height: 100%">
          <div class="chart-div">
            <div id="pie" style="width: 100%; height: 100%"></div>
          </div>
        </div>
      </ModuleCard>
    </div>
    <div class="chart-box" style="height: calc(25%)">
      <ModuleCard title="资产排序" style="height: 100%">
        <div slot="title-right">
          <div class="chart-title-box">
            <div class="chart-title"></div>
            <div class="chart-btn">
              <div class="dropdown-div">
                <el-dropdown
                  trigger="click"
                  @command="(val) => (tagCurrent = val)"
                >
                  <span class="el-dropdown-link">
                    设备类型 <i class="el-icon-arrow-down"></i>
                  </span>
                  <el-dropdown-menu slot="dropdown">
                    <el-dropdown-item
                      :command="'1'"
                      :class="{ isBjxl: tagCurrent == '1' }"
                      >全部科室</el-dropdown-item
                    >
                  </el-dropdown-menu>
                </el-dropdown>
              </div>
              <div class="chart-icon">
                <img
                  src="../../../assets/images/order_more.png"
                  class="order-more"
                  @click="allTableChange"
                />
              </div>
            </div>
          </div>
        </div>
        <div slot="content" style="height: 100%">
          <div class="chart-div">
            <div id="bar" style="width: 100%; height: 100%"></div>
          </div>
        </div>
      </ModuleCard>
    </div>
    <div class="chart-box" style="height: calc(50%)">
      <ModuleCard title="资产分布" style="height: 100%">
        <div slot="title-right">
          <div class="chart-title-box">
            <div class="chart-title"></div>
            <div class="chart-btn">
              <div class="dropdown-div">
                <el-dropdown
                  trigger="click"
                  @command="(val) => (tagCurrent = val)"
                >
                  <span class="el-dropdown-link">
                    全部科室 <i class="el-icon-arrow-down"></i>
                  </span>
                  <el-dropdown-menu slot="dropdown">
                    <el-dropdown-item
                      :command="'1'"
                      :class="{ isBjxl: tagCurrent == '1' }"
                      >全部科室</el-dropdown-item
                    >
                  </el-dropdown-menu>
                </el-dropdown>
              </div>
              <div class="chart-icon">
                <img
                  src="../../../assets/images/order_more.png"
                  class="order-more"
                  @click="allTableChange"
                />
              </div>
            </div>
          </div>
        </div>
        <div slot="content" style="height: 100%">
          <div class="chart-div">
            <div id="bar_ranking" style="width: 100%; height: 100%"></div>
          </div>
        </div>
      </ModuleCard>
    </div>
    <dialogFrame
      :visible="visible"
      :breadcrumb="breadcrumb"
      :title="title"
      @back="back"
      @update:visible="closeDialogFrame"
    >
      <component
        :is="activeComponent"
        @openDetailComponent="openDetailComponent"
      ></component>
    </dialogFrame>
  </div>
</template>
  <script>
import * as echarts from "echarts";
export default {
  components: {
    dialogFrame: () => import("@/components/common/DialogFrame"),
    assetStatistics: () =>
      import("@/views/assetLedgerModule/assetStatistics/index.vue"),
    assetsListIndex: () => import("@/views/assetLedgerModule/listIndex.vue"),
    assetsListDetails: () =>
      import("@/views/assetLedgerModule/assetDetails/details.vue"),
  },
  data() {
    return {
      tagCurrent: "",
      visible: false,
      breadcrumb: [{ label: "资产台账", name: "assetStatistics" }],
      activeComponent: "assetStatistics",
      title: "资产台账",
    };
  },
  mounted() {
    this.getAssetDistribution();
    this.getAssetStatusStatistics();
    this.getAssetSortStatistics();
  },
  methods: {
    allTableChange() {
      this.visible = true;
    },
    /** 打开组件 */
    openDetailComponent(params) {
      this.activeComponent = params;
      let obj = {
        assetsListIndex: { label: "设备列表", name: "assetsListIndex" },
        assetsListDetails: { label: "设备详情", name: "assetsListDetails" },
      };
      this.breadcrumb.push(obj[params]);
      this.title = "";
    },
    /** 组件返回上一级 */
    back(name) {
      this.activeComponent = name;
      let arr = [];
      for (let i = 0; i < this.breadcrumb.length - 1; i++) {
        arr.push(this.breadcrumb[i]);
        if (this.breadcrumb[i].name == name) {
          break;
        }
      }
      this.breadcrumb = arr;
      if (this.breadcrumb.length) {
        this.title = this.breadcrumb.find((item) => item.name === name).label;
      }
    },
    /** 关闭弹窗 */
    closeDialogFrame() {
      this.visible = false;
      this.activeComponent = "assetStatistics";
      this.title = "资产台账";
      this.breadcrumb = [{ label: "资产台账", name: "assetStatistics" }];
    },
    /** 获取资产状态统计 */
    getAssetStatusStatistics() {
      let arr = [
        {
          count: 150,
          name: "使用中",
          percentage: 30,
        },
        {
          count: 69,
          name: "闲置中",
          percentage: 0,
        },
        {
          count: 50,
          name: "维修中",
          percentage: 0,
        },
        {
          count: 10,
          name: "已报废",
          percentage: 0,
        },
        {
          count: 40,
          name: "已外借",
          percentage: 0,
        },
        {
          count: 15,
          name: "已冲红",
          percentage: 0,
        },
      ];
      // 基于准备好的dom，初始化echarts实例
      let getchart = echarts.init(document.getElementById("pie"));
      const data = [];
      var color = ["#FF2D55", "#61E29D ", "#FFAD65", "#e38e6f", "#f2d988"];
      const xdata = Array.from(arr, ({ name }) => name);
      for (var i = 0; i < arr.length; i++) {
        const randomRgbColor = this.$tools.randomRgbColor("array");
        data.push({
          name: arr[i].name,
          value: arr[i].count,
          percentage: arr[i].percentage,
          itemStyle: {
            normal: {
              borderWidth: 1,
              shadowBlur: 200,
              color: color[i] ?? randomRgbColor[0],
              label: {
                normal: { show: true },
              },
            },
          },
        });
      }
      const option = {
        backgroundColor: "",
        title: {
          // text: "{name|" + '资产总数' + "}\n{val|" + 269 + "}",
          text: "{val|" + 269 + "}\n{name|" + "资产总数" + "}",
          top: "35%",
          left: "19%",
          textStyle: {
            rich: {
              name: {
                fontSize: 14,
                fontWeight: "normal",
                color: "#ffffff",
                padding: [10, 0],
              },
              val: {
                fontSize: 18,
                fontWeight: "bold",
                color: "#ffffff",
                padding: [0, 10],
              },
            },
          },
        },
        legend: {
          // 取消legend和echarts的联动
          selectedMode: false,
          icon: "circle",
          orient: "vartical",
          x: "left",
          top: "center",
          left: "50%",
          bottom: "0%",
          data: xdata,
          itemWidth: 8,
          itemHeight: 8,
          itemGap: 12,
          textStyle: {
            color: "#B3C2DD", //  字体颜色
          },
          formatter: function (name) {
            var oa = option.series[0].data;
            for (var i = 0; i < option.series[0].data.length; i++) {
              if (name === oa[i].name) {
                return (
                  " " +
                  name +
                  "    " +
                  oa[i].value +
                  "    " +
                  oa[i].percentage +
                  "%"
                );
              }
            }
          },
        },
        series: [
          {
            name: "",
            type: "pie",
            clockWise: false,
            center: ["27%", "50%"],
            radius: ["58%", "70%"],
            label: {
              normal: {
                show: false,
                position: "center",
                formatter: "{value|{c}}\n{label|{b}}",
                rich: {
                  value: {
                    padding: 5,
                    align: "center",
                    verticalAlign: "middle",
                    fontSize: 20,
                  },
                  label: {
                    align: "center",
                    verticalAlign: "middle",
                    fontSize: 14,
                  },
                },
              },
            },
            // hoverAnimation: false,
            data: data,
          },
        ],
      };
      getchart.clear();
      getchart.setOption(option);
      // 随着屏幕大小调节图表
      window.addEventListener("resize", () => {
        getchart.resize();
      });
    },
    /** 获取资产排序 */
    getAssetSortStatistics() {
      var chartDom = document.getElementById("bar");
      var myChart = echarts.init(chartDom);
      var option;

      option = {
        legend: {
          textStyle: {
            color: "#fff",
            fontSize: 12,
          },
          bottom: "0%",
          itemWidth: 8,
          itemHeight: 8,
          itemGap: 5,
          selectedMode: false,
        },
        // tooltip: {
        //   trigger: "axis",
        //   axisPointer: {
        //     type: "shadow",
        //   },
        //   itemSize: 12,
        //   itemGap: 5,
        // },
        grid: {
          top: "2%",
          left: "5%",
          right: "10%",
          bottom: "15%",
          containLabel: true,
        },
        xAxis: {
          type: "value",
          interval: "auto",
          axisLabel: {
            color: "#fff",
          },
          axisLine: {
            show: false,
          },
          splitLine: {
            show: false,
          },
        },
        yAxis: [
          {
            type: "category",
            data: ["麻醉机", "CT", "核磁机", "腹腔镜系统", "宫腔镜系统", "显微镜", "吸氧机"],
            axisLine: {
              show: true,
            },
            splitLine: {
              show: false,
            },
            axisLabel: {
              color: "#fff",
            },
          },
        ],
        series: [
          {
            name: "使用中",
            type: "bar",
            stack: "total",
            label: {
              show: false,
            },
            emphasis: {
              focus: "series",
            },
            data: [5, 7, 9, 10, 15, 20, 25],
          },
          {
            name: "闲置中",
            type: "bar",
            stack: "total",
            label: {
              show: false,
            },
            emphasis: {
              focus: "series",
            },
            data: [5, 7, 9, 10, 15, 20, 25],
          },
          {
            name: "维修中",
            type: "bar",
            stack: "total",
            label: {
              show: false,
            },
            emphasis: {
              focus: "series",
            },
            data: [5, 7, 9, 10, 15, 20, 25],
          },
          {
            name: "已报废",
            type: "bar",
            stack: "total",
            label: {
              show: false,
            },
            emphasis: {
              focus: "series",
            },
            data: [5, 7, 9, 10, 15, 20, 25],
          },
          {
            name: "已外借",
            type: "bar",
            stack: "total",
            label: {
              show: false,
            },
            emphasis: {
              focus: "series",
            },
            data: [5, 7, 9, 10, 15, 20, 25],
          },
          {
            name: "已冲红",
            type: "bar",
            stack: "total",
            label: {
              show: false,
            },
            emphasis: {
              focus: "series",
            },
            data: [5, 7, 9, 10, 15, 20, 25],
          },
          {
            name: "",
            type: "bar",
            barGap: "-100%",
            itemStyle: {
              normal: {
                color: "transparent",
              },
            },
            showBackground: true,
            backgroundStyle: {
              color: "rgba(180, 180, 180, 0.2)",
            },
            label: {
              show: true,
              position: "right",
              formatter: function (params) {
                let arr = [35, 49, 63, 70, 105, 140, 175];
                return `  ${arr[params.dataIndex]}`;
              },
              fontSize: 12,
              color: "#DADEE1",
            },
            // emphasis: {
            //   focus: "series",
            // },
            data: [175, 175, 175, 175, 175, 175, 175],
          },
        ],
      };

      myChart.resize();
      myChart.clear();
      option && myChart.setOption(option);
      window.addEventListener("resize", () => {
        myChart.resize();
      });
    },
    /** 资产分布统计 */
    getAssetDistribution() {
      let data = [
        {
          id: "1",
          name: "品牌名称1",
          value: 4000,
        },
        {
          id: "2",
          name: "品牌名称2",
          value: 3000,
        },
        {
          id: "3",
          name: "品牌名称3",
          value: 2500,
        },
        {
          id: "4",
          name: "品牌名称4",
          value: 2000,
        },
        {
          id: "5",
          name: "品牌名称5",
          value: 1500,
        },
        {
          id: "6",
          name: "品牌名称6",
          value: 1000,
        },
        {
          id: "7",
          name: "品牌名称7",
          value: 1000,
        },
        {
          id: "8",
          name: "品牌名称8",
          value: 800,
        },
        {
          id: "9",
          name: "品牌名称9",
          value: 700,
        },
        {
          id: "10",
          name: "品牌名称10",
          value: 550,
        },
        {
          id: "11",
          name: "品牌名称11",
          value: 400,
        },
      ];
      // 基于准备好的dom，初始化echarts实例
      let EChart = echarts.init(document.getElementById("bar_ranking"));
      //   将data数据中得value值从大到小排序，然后按顺序将value放入dataValue数组中 name放入dataName数组中 name和value一一对应
      data.sort(function (a, b) {
        return a.value - b.value;
      });
      const dataName = Array.from(data, (item) => {
        item.name = item.name || "未命名";
        const match = item.name.match(this.regex);
        const name = item.name;
        return {
          value: name,
          textStyle: {
            color: "#FFF",
          },
        };
      });
      // 配置参数
      let config = {
        legend: {},
        grid: {
          top: "5%",
          left: "3%",
          right: "8%",
          bottom: "3%",
          containLabel: true,
        },
        xAxis: {
          type: "value",
          show: false,
        },
        yAxis: [
          {
            type: "category",
            data: dataName,
            axisTick: {
              show: false, // 不显示坐标轴刻度线
            },
            axisLine: {
              show: false, // 不显示坐标轴线
            },
            splitLine: {
              // 网格线
              show: false,
            },
            axisLabel: { textStyle: { color: "#fff", fontSize: "14" } },
          },
          {
            type: "category", // 坐标轴类型
            // inverse: true, // 是否是反向坐标轴
            axisTick: {
              show: false, // 不显示坐标轴刻度线
            },
            axisLine: {
              show: false, // 不显示坐标轴线
            },
            splitLine: {
              // 网格线
              show: false,
            },
            axisLabel: {
              textStyle: {
                color: "#FFFFFFCC",
                fontSize: "14",
              },
              formatter: (value) => {
                return 4000;
              },
            },
            data: data,
          },
        ],
        series: [
          {
            type: "bar",
            stack: "total",
            label: {
              show: false,
            },
            emphasis: {
              focus: "series",
            },
            data: data,
            barWidth: 10,
            itemStyle: {
              normal: {
                // barBorderRadius: [15, 15, 15, 15],
                color: function (params) {
                  // 通过返回值的下标一一对应将颜色赋给柱子上
                  return "rgb(97, 165, 232)";
                },
              },
              // 鼠标移入改变颜色
              emphasis: {
                color: "rgb(97, 165, 232)",
              },
            },
            showBackground: true, // 显示背景色
            backgroundStyle: {
              color: "#384156",
            },
          },
        ],
        dataZoom: [
          {
            yAxisIndex: [0, 1],
            orient: "vertical",
            show: true,
            type: "slider",
            start: 100,
            // end: 100,
            width: 8,
            left: "99%",
            borderColor: "rgba(43,48,67,.1)",
            fillerColor: "#6580b8",
            zoomLock: true,
            showDataShadow: false,
            backgroundColor: "rgba(43,48,67,.1)",
            showDetail: false,
            // realtime: true,
            filterMode: "filter",
            handleIcon:
              "M-9.35,34.56V42m0-40V9.5m-2,0h4a2,2,0,0,1,2,2v21a2,2,0,0,1-2,2h-4a2,2,0,0,1-2-2v-21A2,2,0,0,1-11.35,9.5Z",
            handleStyle: {
              color: "#6580b8",
              borderColor: "#6580b8",
            },
            maxValueSpan: 9,
            minValueSpan: 9,
            brushSelect: false,
          },
          {
            type: "inside",
            show: false,
            yAxisIndex: [0, 1],
            height: 8,
          },
        ],
      };
      // 设置参数
      EChart.setOption(config);
      // 先移除点击事件 解决点击事件重复绑定
      EChart.getZr().off("click");
      // 点击事件
      EChart.getZr().on("click", (params) => {
        var pointInPixel = [params.offsetX, params.offsetY];
        if (EChart.containPixel("grid", pointInPixel)) {
          // 获取当前点击的索引值
          // 注意：若柱状图为纵向则获取x轴的索引，若柱状图为横向则需获取y轴的索引(见下方注释)
          // var xIndex=EChart.convertFromPixel({seriesIndex:0},[params.offsetX, params.offsetY])[0];
          // var xData=config.xAxis.data[xIndex];//当前点击柱子的数据
          var yIndex = EChart.convertFromPixel({ seriesIndex: 0 }, [
            params.offsetX,
            params.offsetY,
          ])[1];
          var yData = config.yAxis[1].data[yIndex]; // 当前点击柱子的数据
          // 新增变量 判断当前点击柱状图是选中还是取消选中
          let isSelected = false;
          dataName.map((e, i) => {
            // 选中的设置选中色
            if (i == yIndex && e.textStyle.color != "rgb(97, 165, 232)") {
              e.textStyle.color = "rgb(97, 165, 232)";
            } else {
              // 选中已选中的则为取消选中
              if (i == yIndex && e.textStyle.color == "rgb(97, 165, 232)") {
                isSelected = true;
              }
              // 其他的设为默认色
              e.textStyle.color = "#FFF";
            }
          });
          config.yAxis[0].data = JSON.parse(JSON.stringify(dataName));
          config.dataZoom[0].start = (yIndex / dataName.length) * 100;
          EChart.setOption(config);
          // ....  业务逻辑
          // 取消选中 则恢复过滤条件
          let id = !isSelected ? yData.id : "";
          if (this.isType === "bm") {
            this.paramsData = {
              deptId: id,
              spaceState: "",
            };
            this.getRoomCountByDeptIdList(this.isCommand, id);
          }
        }
      });
    },
  },
};
</script>
  <style lang="scss" scoped>
%flex-styles {
  display: flex;
  align-items: center;
}
.overview-chart-container {
  width: 100%;
  height: 100%;
  // .chart-box:not(:nth-child(3)) {
  //   margin-bottom: 1rem;
  // }
  .chart-box {
    display: flex;
    flex-direction: column;
    flex-shrink: 0;
    .chart-title-box {
      @extend %flex-styles;
      justify-content: space-between;
      .chart-btn {
        @extend %flex-styles;
        .dropdown-div {
          margin-right: 0.5rem;
          .el-dropdown-link {
            color: #fff;
          }
        }
        .chart-icon {
          cursor: pointer;
          @extend %flex-styles;
          .order-more {
            width: 24px;
            height: 24px;
            margin-right: 0 !important;
          }
        }
      }
    }
    .chart-div {
      height: calc(100% - 10px);
    }
  }
}
</style>
  