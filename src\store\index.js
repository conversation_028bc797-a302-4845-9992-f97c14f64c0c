/*
 * @Author: hedd
 * @Date: 2023-03-23 10:11:49
 * @LastEditTime: 2024-09-14 14:00:12
 * @FilePath: \ihcrs_client_iframe\src\store\index.js
 * @Description:
 */
/*
 * @Author: <PERSON>
 * @Date: 2021-12-14 15:17:38
 * @Last Modified by: <PERSON>
 * @Last Modified time: 2021-12-15 16:24:06
 */

import Vue from 'vue'
import Vuex from 'vuex'
import crypto from '@/utils/crypto'
import { userLogin } from '@/utils/api'
import { Message } from 'element-ui'

Vue.use(Vuex)

export default new Vuex.Store({
  state: {
    loginInfo: JSON.parse(sessionStorage.getItem('loginInfo')) || {},
    isCollapse: false,
    menuList: [],
    keepAliveList: [],
    loginFlag: false,
    iemcMsgs: '',
    dialogFullScreenState: false,
    picPrefix: sessionStorage.getItem('picPrefix') || ''
  },
  mutations: {
    setUserData: (state, data) => {
      state.loginInfo = data.user
      state.picPrefix = data.picPrefix || ''
      sessionStorage.setItem('loginInfo', JSON.stringify(data.user))
      sessionStorage.setItem('picPrefix', data.picPrefix || '')
    },
    outLogin: (state) => {
      state.loginInfo = {}
      localStorage.removeItem('loginInfo')
      localStorage.setItem('isLogin', 0)
    },
    loginState: (state, data) => {
      state.isLogin = data
      localStorage.setItem('isLogin', data)
    },
    setSocketIemcMsgs(state, data) {
      state.iemcMsgs = data
    },
    setCollapse: (state, data) => {
      state.isCollapse = data
    },
    pullMeunItem(state, menu) {
      const checkItem = state.menuList.find((item) => {
        return item.menuId === menu.menuId
      })
      if (!checkItem) {
        state.menuList.push(menu)
      }
    },
    delAllMenuItem(state, data) {
      state.menuList = data
    },
    deleteMenuItem(state, index) {
      state.menuList.splice(index, 1)
    },
    switchMenu(state, to) {
      const arr = [...state.menuList]
      arr.find((item) => {
        if ('/' + item.menuHref.toLowerCase() === to.fullPath.toLowerCase()) {
          item.active = true
        } else {
          item.active = false
        }
      })
      state.menuList = arr
    },
    keepAliveAdd(state, name) {
      if (typeof name === 'string') {
        !state.keepAliveList.includes(name) && state.keepAliveList.push(name)
      } else {
        name.map((v) => {
          v && !state.keepAliveList.includes(v) && state.keepAliveList.push(v)
        })
      }
    },
    keepAliveRemove(state, name) {
      if (typeof name === 'string') {
        state.keepAliveList = state.keepAliveList.filter((v) => {
          return v !== name
        })
      } else {
        state.keepAliveList = state.keepAliveList.filter((v) => {
          return !name.includes(v)
        })
      }
    },
    keepAliveClean(state) {
      state.keepAliveList = []
    },
    dialogFullScreen(state, data) {
      state.dialogFullScreenState = data
    }
  },
  actions: {
    login({ commit }) {
      return new Promise((resolve, reject) => {
        const { username, password } = IHCRS_ACCOUNT
        userLogin({ username, password: crypto.set(password), platform: 1 })
          .then((res) => {
            const data = res.data
            if (data.code === '200') {
              if (data.data.user && data.data.token) {
                commit('setUserData', data.data)
                resolve()
              } else {
                Message.error('获取用户信息失败')
                reject(new Error())
              }
            } else {
              Message.error('获取用户信息失败')
              reject(new Error())
            }
          })
          .catch((err) => {
            Message.error('获取用户信息失败')
            reject(err)
          })
      })
    }
  },
  modules: {},
  getters: {
    isLogin: (state) => {
      let retn = false
      if (state.loginInfo.token) {
        retn = true
      }
      return retn
    },
    socketIemcMsgs: (state) => state.iemcMsgs
  }
})
