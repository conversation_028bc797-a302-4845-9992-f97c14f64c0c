<template>
  <div class="roomInfoManagement">
    <div @click="collapseHeight" :class="collapseFlag ? 'circle-btn-top' : 'circle-btn-bottom'" class="circle-btn circle-btn-top"></div>
    <div ref="collapseWidth" class="el-dialog right-content">
      <div class="bg-title el-dialog__header" v-show="collapseFlag">
        <span>{{ sysTitle }}</span
        ><i class="el-icon-close" @click="closeRoomDialog" style="display: none"></i>
      </div>
      <div class="bg-content room-info-box" v-show="collapseFlag">
        <div class="box-type">
          <span v-for="(item, index) in roomTypeList" :key="index" :id="item.Dom" @click="activeTypeEvent(item.Dom)">{{ item.name }}</span>
        </div>
        <div class="sys-box">
          <component :ref="currentComponent" :is="currentComponent" @roomEvent="roomEvent" :roomData="dialogData" class="sys-box-content"></component>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import $ from 'jquery'

export default {
  name: 'roomInfoManagement',
  components: {
    floorSpaceComponent: () => import('./sysTypeComponent/floorSpaceComponent.vue'),
    pipelineComponent: () => import('./sysTypeComponent/pipelineComponent.vue'),
    baseInfoComponent: () => import('./sysTypeComponent/baseInfoComponent'),
    iomsComponent: () => import('./sysTypeComponent/iomsComponent'),
    imesComponent: () => import('./sysTypeComponent/imesComponent'),
    imwsComponent: () => import('./sysTypeComponent/imwsComponent'),
    icisComponent: () => import('./sysTypeComponent/icisComponent'),
    riskComponent: () => import('./sysTypeComponent/riskComponent'),
    dangerComponent: () => import('./sysTypeComponent/dangerComponent'),
    energyComponent: () => import('./sysTypeComponent/energyComponent')
  },
  props: {
    dialogData: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      collapseFlag: true,
      sysTitle: '',
      activeType: '',
      roomTypeList: [],
      floorComponentList: [
        {
          Dom: 'space',
          name: '空间',
          component: 'floorSpaceComponent'
        },
        {
          Dom: 'pipe',
          name: '管线',
          component: 'pipelineComponent'
        }
      ],
      roomComponentList: [
        {
          Dom: 'base',
          name: '基础信息',
          component: 'baseInfoComponent'
        }
      ],
      publicComponentList: [
        {
          Dom: 'ioms',
          name: '工单',
          component: 'iomsComponent'
        },
        {
          Dom: 'imes',
          name: '资产',
          component: 'imesComponent'
        },
        {
          Dom: 'imws',
          name: '医废',
          component: 'imwsComponent'
        },
        {
          Dom: 'icis',
          name: '巡检',
          component: 'icisComponent'
        },
        {
          Dom: 'risk',
          name: '风险',
          component: 'riskComponent'
        },
        {
          Dom: 'danger',
          name: '隐患',
          component: 'dangerComponent'
        },
        {
          Dom: 'energy',
          name: '能耗',
          component: 'energyComponent'
        }
      ]
    }
  },
  computed: {
    currentComponent() {
      return this.roomTypeList.find((e) => e.Dom === this.activeType)?.component
    }
  },
  created() {
    // this.initRoomInfo()
  },
  mounted() {
    // this.activeTypeEvent(this.activeType)
  },
  methods: {
    // 改变 box 高度
    collapseHeight() {
      this.$nextTick(() => {
        if (this.collapseFlag) {
          this.collapseFlag = !this.collapseFlag
          this.$refs.collapseWidth.style.width = '0'
          this.$refs.collapseWidth.style.padding = '0'
        } else {
          setTimeout(() => {
            this.collapseFlag = !this.collapseFlag
          }, 200)
          this.$refs.collapseWidth.style.width = '22%'
          this.$refs.collapseWidth.style.padding = '10px'
        }
      })
    },
    // 楼层 渲染 空间信息
    initRoomInfo() {
      this.sysTitle = this.dialogData.title
      if (this.dialogData.ssmType === 5) {
        // 五楼可看见管线
        this.roomTypeList = [this.floorComponentList[0], ...this.publicComponentList]
        // if (this.dialogData.localtion === '0100108') {
        this.roomTypeList = [...this.roomTypeList, this.floorComponentList[1]]
        // }
        if (this.activeType === 'space') {
          this.$nextTick(() => {
            this.$refs.floorSpaceComponent.initFloorSpace()
          })
        } else {
          this.activeType = 'space'
        }
      } else {
        // 房间 没有医废标签
        const publicList = this.publicComponentList.filter((e) => e.Dom !== 'imws')
        this.roomTypeList = [...this.roomComponentList, ...publicList]
        if (this.activeType === 'base') {
          this.$nextTick(() => {
            this.$refs.baseInfoComponent.getSpaceBaseInfo()
          })
        } else {
          this.activeType = 'base'
        }
      }
      this.$nextTick(() => {
        this.activeTypeEvent(this.activeType)
      })
    },
    activeTypeEvent(type) {
      this.activeType = type
      // 是否进入管线组件给wpf传参
      try {
        window.chrome.webview.hostObjects.sync.bridge.SetPipeLineVis(this.activeType === 'pipe')
      } catch (error) {}
      if (this.activeType !== 'space') {
        const JSONdata = JSON.stringify({
          type: 'function',
          selectIds: '',
          modelCode: this.dialogData.modelCode
        })
        try {
          window.chrome.webview.hostObjects.sync.bridge.GetRoomCodeType(JSONdata)
        } catch (error) {}
      }
      // 点击中间六个 动态让wpf切换标签
      // let tagJsonData = JSON.stringify({})
      // const hasWPFtag = ['ioms', 'imes', 'imws', 'icis', 'risk', 'danger'].includes(this.activeType)
      // if (hasWPFtag) {
      const name = this.roomTypeList.find((e) => e.Dom === this.activeType)?.name
      const tagJsonData = JSON.stringify({
        tabName: name,
        modelCode: this.dialogData.modelCode
      })
      // }
      console.log(tagJsonData)
      try {
        window.chrome.webview.hostObjects.sync.bridge.SpaceTabSwitch(tagJsonData)
      } catch (error) {}
      this.roomTypeList.forEach((item) => {
        if (item.Dom === type) {
          $(`#${item.Dom}`).addClass('type_active')
        } else {
          $(`#${item.Dom}`).removeClass('type_active')
        }
      })
    },
    closeRoomDialog() {
      this.$emit('configCloseDialog')
    },
    // 子组件传递事件
    roomEvent(data) {
      if (data.type === 'base') {
        this.sysTitle = this.dialogData.title + ' > ' + (data.roomName ?? '')
      } else if (data.type === 'space') {
        this.$parent.toWPFdata = data.data
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.roomInfoManagement {
  width: 100%;
  // height: 100%;
  position: absolute;
  top: 0;
  right: 0;
  // width: 22%;
  height: 100%;
  margin-top: 2%;
  max-height: 95%;
  .circle-btn {
    position: absolute;
    top: calc(2%);
    right: 0;
    width: 26px;
    height: 26px;
    cursor: pointer;
    margin: auto 0;
    margin-right: 10px;
    z-index: 2;
  }
  .circle-btn-top {
    background: url('~@/assets/images/center/btn-fold.png') no-repeat;
    background-size: 100% 100%;
  }
  .circle-btn-bottom {
    width: 44px;
    height: 44px;
    background: url('~@/assets/images/center/btn-unfold.png') no-repeat;
    background-size: 100% 100%;
  }
  .right-content {
    width: 22%;
    height: 100%;
    margin: 0 0 3.125rem auto;
    background: url('~@/assets/images/center/bg-80.png') no-repeat;
    background-size: 100% 100%;
    box-sizing: border-box;
    padding: 10px;
    position: relative;
    transition: width 0.3s linear;
    overflow: hidden;
    .bg-title {
      height: 2.8rem;
      line-height: 2.8rem;
      padding: 0 10px 0 2.2rem;
      color: #dceaff;
      font-family: TRENDS;
      // 不可被选中
      -webkit-user-select: none;
      display: flex;
      span {
        width: calc(100% - 2.2rem);
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
      i {
        float: right;
        line-height: 2.8rem;
        font-size: 1.5rem;
        cursor: pointer;
      }
    }
    .bg-content {
      position: relative;
      box-sizing: border-box;
      padding: 10px 2px 2px 2px;
      width: 100%;
      height: calc(100% - 3rem);
    }
    .room-info-box {
      color: #fff;
      display: flex;
      flex-direction: column;
      .box-type {
        width: 100%;
        margin: auto;
        display: flex;
        flex-wrap: wrap;
        justify-content: center;
        span {
          display: inline-block;
          width: fit-content;
          height: 24px;
          padding: 0 5px;
          background-color: #24396d;
          text-align: center;
          line-height: 24px;
          color: #dceaff;
          font-size: 14px;
          font-family: PingFang-SC-Medium, PingFang-SC;
          cursor: pointer;
          margin: 5px 2px 0 2px;
        }
        .type_active {
          color: #ffe3a6;
          background: url('~@/assets/images/center/border-bg-select.png') no-repeat;
          background-size: 100% 100%;
        }
      }
      .sys-box {
        width: 100%;
        height: calc(100% - 30px);
        // flex: 1;
        .sys-box-content {
          width: 100%;
          height: 100%;
          padding: 10px;
          box-sizing: border-box;
        }
        // height: calc(100% - 24px);
      }
    }
  }
}
</style>
