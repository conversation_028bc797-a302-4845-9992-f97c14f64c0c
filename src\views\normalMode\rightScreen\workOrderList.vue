<template>
  <div class="content">
    <div class="title">
      <div @click="goBack"><i class="el-icon-arrow-left"></i>返回</div>
      <span>工单管理</span>
    </div>
    <div class="form-content">
      <div class="search-form">
        <div class="form-row">
          <div class="row-label">工单状态</div>
          <div class="row-value">
            <!-- <el-checkbox class="value-first-checkbox" v-model="searchForm.check">全部</el-checkbox> -->
            <el-checkbox-group v-model="searchForm.flowcode" @change="checkboxChange($event, 'flowcode')">
              <el-checkbox v-for="order in flowcodeOption" :label="order.value" :key="order.label">{{ order.label }}</el-checkbox>
            </el-checkbox-group>
            <!-- <el-select v-model="checked" placeholder="请选择">
            <el-option v-for="item in options" :key="item.value" :label="item.label" :value="item.value"> </el-option>
          </el-select> -->
          </div>
        </div>
        <div class="form-row">
          <div class="row-label">跟踪状态</div>
          <div class="row-value">
            <el-checkbox-group v-model="searchForm.feedbackFlag" @change="checkboxChange($event, 'feedbackFlag')">
              <el-checkbox v-for="order in feedbackFlagOption" :label="order.value" :key="order.label">{{ order.label }}</el-checkbox>
            </el-checkbox-group>
          </div>
        </div>
        <div class="form-row">
          <div class="row-label">申报时间</div>
          <div class="row-value">
            <el-checkbox-group v-model="searchForm.showTimeType" @change="checkboxChange($event, 'showTimeType')">
              <el-checkbox v-for="order in showTimeTypeOption" :label="order.value" :key="order.label">{{ order.label }}</el-checkbox>
            </el-checkbox-group>
          </div>
          <div v-if="searchForm.showTimeType[0] === '4'" class="time-range">
            <el-date-picker
              value-format="yyyy-MM-dd+hh:mm:ss"
              style="margin-left: 20px"
              popper-class="timePicker"
              v-model="timeSlot"
              type="datetimerange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              @change="getWorkTableData"
            >
            </el-date-picker>
          </div>
        </div>
        <div class="form-row">
          <div class="row-label">紧急程度</div>
          <div class="row-value">
            <el-checkbox-group v-model="searchForm.urgencyDegree" @change="checkboxChange($event, 'urgencyDegree')">
              <el-checkbox v-for="order in urgencyDegreeOption" :label="order.value" :key="order.label">{{ order.label }}</el-checkbox>
            </el-checkbox-group>
          </div>
        </div>
        <div class="form-row">
          <div class="row-label">申报来源</div>
          <div class="row-value">
            <el-checkbox-group v-model="searchForm.workSources" @change="checkboxChange($event, 'workSources')">
              <el-checkbox v-for="order in workSourcesOption" :label="order.value" :key="order.label">{{ order.label }}</el-checkbox>
            </el-checkbox-group>
          </div>
        </div>
        <div class="form-row">
          <div class="row-label">申报属性</div>
          <div class="row-value">
            <el-checkbox-group v-model="searchForm.typeSources" @change="checkboxChange($event, 'typeSources')">
              <el-checkbox v-for="order in typeSourcesOption" :label="order.value" :key="order.label">{{ order.label }}</el-checkbox>
            </el-checkbox-group>
          </div>
        </div>
        <!-- <transition name="fade"> -->
        <!-- <span v-show="isShow">动画效果</span> -->
        <div ref="allForm" class="allForm">
          <div class="form-row">
            <div class="row-label">申报科室</div>
            <div class="row-value">
              <el-checkbox @change="checkOneChange($event, 'sourcesDeptCheck', 'sourcesDeptSelect')" class="value-first-checkbox" v-model="sourcesDeptCheck">全部</el-checkbox>
              <el-select @change="selectOneChange($event, 'sourcesDeptCheck', 'sourcesDeptSelect')" v-model="sourcesDeptSelect" placeholder="请选择">
                <el-option v-for="item in sourcesDeptOptions" :key="item.value" :label="item.label" :value="item.value"> </el-option>
              </el-select>
            </div>
          </div>
          <div class="form-row">
            <div class="row-label">服务地点</div>
            <div class="row-value">
              <el-checkbox @change="checkOneChange($event, 'localtionNameCheck', 'localtionNameSelect')" class="value-first-checkbox" v-model="localtionNameCheck">全部</el-checkbox>
              <el-input readonly="readonly" @focus="getLocaltion()" v-model="localtionNameSelect" placeholder="请输入内容"></el-input>
            </div>
          </div>
          <div class="form-row">
            <div class="row-label">外委服务商</div>
            <div class="row-value">
              <el-checkbox @change="checkOneChange($event, 'companyCodeCheck', 'companyCodeSelect')" class="value-first-checkbox" v-model="companyCodeCheck">全部</el-checkbox>
              <!-- <el-input @change="inputChange($event, 'companyCodeCheck', 'companyCodeSelect')" v-model="companyCodeSelect" placeholder="请输入内容"></el-input> -->
              <span class="value-span">服务商：</span
              ><el-select @change="selectOneChange($event, 'companyCodeCheck', 'companyCodeSelect')" v-model="companyCodeSelect" placeholder="请选择">
                <el-option v-for="item in serviceCompanyOptions" :key="item.id" :label="item.company_name" :value="item.id"> </el-option>
              </el-select>
              <span class="value-span">服务班组：</span
              ><el-select @change="selectOneChange($event, 'companyCodeCheck', 'typeTeamSelect')" v-model="typeTeamSelect" placeholder="请选择">
                <el-option v-for="item in typeTeamOptions" :key="item.id" :label="item.team_name" :value="item.id"> </el-option>
              </el-select>
              <span class="value-span">服务人员：</span
              ><el-select @change="selectOneChange($event, 'companyCodeCheck', 'servicePersonSelect')" v-model="servicePersonSelect" placeholder="请选择">
                <el-option v-for="item in servicePersonOptions" :key="item.id" :label="item.member_name" :value="item.id"> </el-option>
              </el-select>
            </div>
          </div>
          <div class="form-row">
            <div class="row-label">受理人</div>
            <div class="row-value">
              <el-checkbox @change="checkOneChange($event, 'createByIdCheck', 'createByIdSelect')" class="value-first-checkbox" v-model="createByIdCheck">全部</el-checkbox>
              <el-select @change="selectOneChange($event, 'createByIdCheck', 'createByIdSelect')" v-model="createByIdSelect" placeholder="请选择">
                <el-option v-for="item in createByIdOptions" :key="item.value" :label="item.label" :value="item.value"> </el-option>
              </el-select>
            </div>
          </div>
          <div class="form-row">
            <div class="row-label">响应时间</div>
            <div class="row-value">
              <el-checkbox-group v-model="searchForm.responseTimeType" @change="checkboxChange($event, 'responseTimeType')">
                <el-checkbox v-for="order in responseTimeTypeOption" :label="order.value" :key="order.label">{{ order.label }}</el-checkbox>
              </el-checkbox-group>
            </div>
          </div>
          <div class="form-row">
            <div class="row-label">满意度评价</div>
            <div class="row-value">
              <el-checkbox-group v-model="searchForm.disDegreeNew" @change="checkboxChange($event, 'disDegreeNew')">
                <el-checkbox v-for="order in disDegreeNewOption" :label="order.value" :key="order.label">{{ order.label }}</el-checkbox>
              </el-checkbox-group>
            </div>
          </div>
          <div class="form-row">
            <div class="row-label">申报信息</div>
            <div class="row-value">
              <el-checkbox @change="checkOneChange($event, 'reportInfoCheck', 'reportInfoSelect', 'reportInfoInput')" class="value-first-checkbox" v-model="reportInfoCheck">全部</el-checkbox>
              <span class="value-span">联系人：</span
              ><el-select @change="selectOneChange($event, 'reportInfoCheck', 'reportInfoSelect', 'reportInfoInput')" v-model="reportInfoSelect" placeholder="请选择">
                <el-option v-for="(item, p) in reportInfoOptions" :key="p" :label="item.label" :value="item.label"> </el-option>
              </el-select>
              <span class="value-span">电话：</span
              ><el-input @change="inputChange($event, 'reportInfoCheck', 'reportInfoInput', 'reportInfoSelect')" @blur="inputBlur()" v-model="reportInfoInput" placeholder="请输入内容"></el-input>
            </div>
          </div>
          <div class="form-row">
            <div class="row-label">服务事项</div>
            <div class="row-value">
              <el-checkbox @change="checkOneChange($event, 'itemTypeCodeCheck', 'itemTypeCodeInput')" class="value-first-checkbox" v-model="itemTypeCodeCheck">全部</el-checkbox>
              <!-- <el-input @change="inputChange($event, 'itemTypeCodeCheck', 'itemTypeCodeInput')" @blur="inputBlur()" v-model="itemTypeCodeInput" placeholder="请输入内容"></el-input> -->
              <select-tree
                style="width: 300px"
                v-model="itemTypeCodeInput"
                @getName="getItemServiceName"
                callBackData="split"
                :data="itemTreeData"
                :props="{ label: 'name', children: 'children' }"
              ></select-tree>
            </div>
          </div>
          <div class="form-row">
            <div class="row-label">申报描述</div>
            <div class="row-value">
              <el-checkbox @change="checkOneChange($event, 'questionDescriptionCheck', 'questionDescriptionInput')" class="value-first-checkbox" v-model="questionDescriptionCheck">全部</el-checkbox>
              <el-input
                style="width: 500px"
                @change="inputChange($event, 'questionDescriptionCheck', 'questionDescriptionInput')"
                @blur="inputBlur()"
                v-model="questionDescriptionInput"
                placeholder="请输入内容"
              ></el-input>
            </div>
          </div>
          <div class="form-row">
            <div class="row-label">申报工单号</div>
            <div class="row-value">
              <el-checkbox @change="checkOneChange($event, 'workNumCheck', 'workNumInput')" class="value-first-checkbox" v-model="workNumCheck">全部</el-checkbox>
              <el-input style="width: 300px" @change="inputChange($event, 'workNumCheck', 'workNumInput')" @blur="inputBlur()" v-model="workNumInput" placeholder="请输入内容"></el-input>
            </div>
          </div>
        </div>
        <!-- </transition> -->
      </div>
      <div class="search-table">
        <div class="search-btn">
          <el-button class="new-edition" style="margin-left: 30px" v-if="!showAllForm" @click="changeSeachForm(false)">展开</el-button>
          <el-button class="new-edition" style="margin-left: 30px" v-else @click="changeSeachForm(true)">收起</el-button>
          <el-button class="new-edition" @click="resetFormData()">重置</el-button>
        </div>
        <el-table
          :data="tableData"
          :cell-style="$tools.setCell(3)"
          :header-cell-style="$tools.setHeaderCell(3)"
          style="width: 100%"
          @row-click="tableRowClick"
          v-loading="tableLoading"
          element-loading-background="rgba(0, 0, 0, 0.2)"
        >
          <el-table-column prop="createDate" show-overflow-tooltip label="开单时间"></el-table-column>
          <el-table-column prop="sourcesDeptName" show-overflow-tooltip label="所属科室"></el-table-column>
          <el-table-column prop="localtionNames" show-overflow-tooltip label="服务地点"></el-table-column>
          <el-table-column prop="serviceItemName" show-overflow-tooltip label="服务事项">
            <template slot-scope="scope">
              <span>{{ scope.row.itemTypeName + '-' + scope.row.itemDetailName + '-' + scope.row.itemServiceName }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="designatePersonName" show-overflow-tooltip label="服务人员"></el-table-column>
          <el-table-column prop="designatePersonPhone" show-overflow-tooltip label="联系方式"></el-table-column>
          <el-table-column prop="questionDescription" show-overflow-tooltip label="说明"></el-table-column>
          <el-table-column prop="flowtype" show-overflow-tooltip label="状态"></el-table-column>
          <el-table-column prop="entryordersFeedbackFlag" show-overflow-tooltip label="是否回访"></el-table-column>
        </el-table>
        <el-pagination
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="pageNo"
          :page-sizes="[15, 20, 30, 40]"
          :page-size="pageSize"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
          class="pagination"
        ></el-pagination>
      </div>
    </div>
    <!-- 选择服务地点弹框 -->
    <template v-if="changeLocationShow">
      <Location ref="changeLocation" :changeLocationShow="changeLocationShow" @localSure="locationSure" @closeDialog="closeLocationDialog"></Location>
    </template>
  </div>
</template>

<script>
import { getAllOffice, getServiceCompany, getObtainAssignee, getCallerRetrieve, getDataByTypeTeam, getServicePersonName, getWorkOrderDataList, getItemTreeData } from '@/utils/peaceRightScreenApi'
import Location from './common/Location.vue'
import selectTree from '@/components/common/selectTree'
export default {
  name: 'workOrderList',
  components: {
    Location,
    selectTree
  },
  beforeRouteEnter(to, from, next) {
    // 进入页面时，先将当前页面的 name 信息存入 keep-alive 全局状态
    next((vm) => {
      vm.$store.commit('keepAliveAdd', 'workOrderList')
    })
  },
  beforeRouteLeave(to, from, next) {
    if (!['workOrderDetail'].includes(to.name)) {
      this.$store.commit('keepAliveRemove', 'workOrderList')
    }
    next()
  },
  props: {
    workTypeCode: {
      type: String,
      default: '1'
    },
    workTypeName: {
      type: String,
      default: 'WX'
    }
  },
  data() {
    return {
      changeLocationShow: false, // 服务地点弹框
      showAllForm: false, // 初始化展示所有查询表单
      flowcodeOption: [
        {
          label: '全部',
          value: ''
        },
        {
          label: '未受理',
          value: '1'
        },
        {
          label: '暂存',
          value: '7'
        },
        {
          label: '未派工',
          value: '2'
        },
        {
          label: '已派工',
          value: '3'
        },
        {
          label: '已挂单',
          value: '4'
        },
        {
          label: '已完工',
          value: '5'
        },
        {
          label: '已取消',
          value: '6'
        }
      ],
      feedbackFlagOption: [
        {
          label: '全部',
          value: ''
        },
        {
          label: '未督办',
          value: '2'
        },
        {
          label: '已督办',
          value: '3'
        },
        {
          label: '未回访',
          value: '0'
        },
        {
          label: '已回访',
          value: '1'
        },
        {
          label: '未回复',
          value: '5'
        },
        {
          label: '已回复',
          value: '4'
        }
      ],
      sourcesDeptOptions: [],
      serviceCompanyOptions: [],
      typeTeamOptions: [],
      servicePersonOptions: [],
      createByIdOptions: [],
      reportInfoOptions: [],
      showTimeTypeOption: [
        {
          label: '本年',
          value: '3'
        },
        {
          label: '昨天',
          value: '5'
        },
        {
          label: '今天',
          value: '1'
        },
        {
          label: '本周',
          value: '6'
        },
        {
          label: '本月',
          value: '2'
        },
        {
          label: '其他',
          value: '4'
        }
      ],
      urgencyDegreeOption: [
        {
          label: '全部',
          value: ''
        },
        {
          label: '一般',
          value: '2'
        },
        {
          label: '紧急事故',
          value: '0'
        },
        {
          label: '紧急催促',
          value: '1'
        }
      ],
      workSourcesOption: [
        {
          label: '全部',
          value: ''
        },
        {
          label: '移动申报',
          value: '1'
        },
        {
          label: '中心申报',
          value: '2'
        },
        {
          label: '电话申报',
          value: '0'
        },
        {
          label: '计划任务',
          value: '4'
        }
      ],
      typeSourcesOption: [
        {
          label: '全部',
          value: ''
        },
        {
          label: '医务报修',
          value: '1'
        },
        {
          label: '外委巡查',
          value: '2'
        },
        {
          label: '巡检来源',
          value: '3'
        },
        {
          label: '领导巡查',
          value: '4'
        }
      ],
      responseTimeTypeOption: [
        {
          label: '全部',
          value: ''
        },
        {
          label: '0-15分钟',
          value: '1'
        },
        {
          label: '15-30分钟',
          value: '2'
        },
        {
          label: '30-60分钟',
          value: '3'
        },
        {
          label: '超过60分钟',
          value: '4'
        }
      ],
      disDegreeNewOption: [
        {
          label: '全部',
          value: ''
        },
        {
          label: '非常差',
          value: '1'
        },
        {
          label: '差',
          value: '2'
        },
        {
          label: '一般',
          value: '3'
        },
        {
          label: '满意',
          value: '4'
        },
        {
          label: '非常满意',
          value: '5'
        }
      ],
      itemTreeData: [],
      searchForm: {
        flowcode: [''],
        feedbackFlag: [''],
        showTimeType: ['1'],
        urgencyDegree: [''],
        workSources: [''],
        typeSources: [''],
        responseTimeType: [''],
        disDegreeNew: ['']
      },
      timeSlot: [], // 时间段
      // 服务地点
      localtion: {
        region: '',
        buliding: '',
        storey: '',
        room: ''
      },
      // 服务事项
      itemService: {
        itemTypeName: '',
        itemTypeCode: '',
        itemDetailCode: '',
        itemDetailName: '',
        itemServiceName: '',
        itemServiceCode: ''
      },
      sourcesDeptCheck: true,
      sourcesDeptSelect: '',
      localtionNameCheck: true,
      localtionNameSelect: '',
      companyCodeCheck: true,
      companyCodeSelect: '',
      typeTeamSelect: '',
      servicePersonSelect: '',
      createByIdCheck: true,
      createByIdSelect: '',
      reportInfoCheck: true,
      reportInfoSelect: '',
      reportInfoInput: '',
      questionDescriptionCheck: true,
      questionDescriptionInput: '',
      workNumCheck: true,
      workNumInput: '',
      itemTypeCodeCheck: true,
      itemTypeCodeInput: '',
      tableData: [],
      pageNo: 1,
      pageSize: 15,
      total: 0,
      tableLoading: false,
      formHeight: 0
    }
  },
  mounted() {
    this.getSourcesDeptOptions() // 获取科室数据
    this.getServiceCompany() // 获取服务商
    this.getObtainAssignee() // 获取受理人
    this.getCallerRetrieve() // 获取申报信息
    this.getItemTreeData() // 获取服务事项
    this.$nextTick().then(() => {
      this.formHeight = this.$refs.allForm.offsetHeight
      this.$refs.allForm.style.height = 0
      // console.log(this.formHeight, this.$route)
      this.setFormInit(this.$route.params)
    })
  },
  methods: {
    getSourcesDeptOptions() {
      getAllOffice().then((res) => {
        if (res.data.success) {
          this.sourcesDeptOptions = res.data.body.result
        }
      })
    },
    getServiceCompany() {
      getServiceCompany().then((res) => {
        if (res.data.code === '200') {
          this.serviceCompanyOptions = res.data.data.list
        }
      })
    },
    getObtainAssignee() {
      getObtainAssignee().then((res) => {
        if (res.data.code === '200') {
          this.createByIdOptions = res.data.data.list
        }
      })
    },
    getCallerRetrieve() {
      getCallerRetrieve().then((res) => {
        if (res.data.success) {
          this.reportInfoOptions = res.data.body.result
        }
      })
    },
    getTypeTeam(val) {
      getDataByTypeTeam({ id: val }).then((res) => {
        if (res.data.code === '200') {
          this.typeTeamOptions = res.data.data.list
        }
      })
    },
    getServicePerson(val) {
      getServicePersonName({ id: val }).then((res) => {
        if (res.data.code === '200') {
          this.servicePersonOptions = res.data.data.list
        }
      })
    },
    // 查询框初始化
    setFormInit(item) {
      if (item.isHistory === '1') {
        this.searchForm.showTimeType = ['3']
        if (item.historyLocaltion !== undefined && item.historyLocaltion !== '' && item.historyLocaltion !== null) {
          var addArr = item.historyLocaltion.split(',')
          if (addArr.length >= 1) {
            this.localtion.region = addArr[0]
          }
          if (addArr.length >= 2) {
            this.localtion.buliding = addArr[1]
          }
          if (addArr.length >= 3) {
            this.localtion.storey = addArr[2]
          }
          if (addArr.length >= 4) {
            this.localtion.room = addArr[3]
          }
          this.localtionNameCheck = false
          this.localtionNameSelect = item.historyLocaltionName
        }
        if (item.historyDept !== undefined && item.historyDept !== '' && item.historyDept !== null) {
          this.sourcesDeptCheck = false
          this.sourcesDeptSelect = item.historyDept
        }
      }
      if (item.type) {
        const timeType = item.type
        if (timeType === 'day') {
          this.searchForm.showTimeType = ['1']
        } else if (timeType === 'month') {
          this.searchForm.showTimeType = ['2']
        } else if (timeType === 'weekGD') {
          this.searchForm.showTimeType = ['6']
          this.searchForm.flowcode = ['4']
        }
      }
      this.getWorkTableData()
    },
    checkboxChange(val, type) {
      this.searchForm[type] = val.length ? [val[val.length - 1]] : ['']
      if (type === 'showTimeType') {
        if (this.searchForm[type][0] !== '4') {
          this.timeSlot = []
        } else {
          return
        }
      }
      this.getWorkTableData()
    },
    checkOneChange(val, checkType, selectType, inputType) {
      if (val) {
        this[selectType] = ''
        if (inputType) {
          this[inputType] = ''
        }
        if (checkType === 'itemTypeCodeCheck') {
          this.itemService = {
            itemTypeName: '',
            itemTypeCode: '',
            itemDetailCode: '',
            itemDetailName: '',
            itemServiceName: '',
            itemServiceCode: ''
          }
        }
        this.getWorkTableData()
        return false
      }
      this[checkType] = this[selectType] === ''
      if (checkType === 'localtionNameCheck') {
        this.localtion = {
          region: '',
          buliding: '',
          storey: '',
          room: ''
        }
      }
      this.getWorkTableData()
    },
    selectOneChange(val, checkType, selectType, inputType) {
      this[checkType] = this[selectType] === ''
      this[checkType] = inputType ? this[selectType] === '' && this[inputType] === '' : this[selectType] === ''
      if (selectType === 'companyCodeSelect' && this.typeTeamOptions.length < 1) {
        this.getTypeTeam(val)
      }
      if (selectType === 'typeTeamSelect' && this.servicePersonOptions.length < 1) {
        // console.log(val)
        this.getServicePerson()
      }
      this.getWorkTableData()
    },
    inputChange(val, checkType, inputType, selectType) {
      this[checkType] = this[selectType] === '' && this[inputType] === ''
    },
    inputBlur() {
      this.getWorkTableData()
    },
    // 改变 form查询 高度
    changeSeachForm(flag) {
      this.showAllForm = !flag
      this.$refs.allForm.style.height = this.showAllForm ? `${this.formHeight}px` : 0
    },
    // 获取服务地点 start
    getLocaltion() {
      this.changeLocationShow = true
    },
    locationSure(item) {
      this.localtionNameSelect = item.name
      const ids = item.id.split('_')
      this.localtion = {
        region: ids[0],
        buliding: ids[1] || '',
        storey: ids[2] || '',
        room: ids[3] || ''
      }
      this.changeLocationShow = false
      this.localtionNameCheck = false
      this.inputBlur()
    },
    // end
    closeLocationDialog() {
      this.changeLocationShow = false
    },
    // 服务事项返回数据
    getItemServiceName(item) {
      // console.log(item)
      this.itemService = {
        itemTypeName: '',
        itemTypeCode: '',
        itemDetailCode: '',
        itemDetailName: '',
        itemServiceName: '',
        itemServiceCode: ''
      }
      const data = item.name.split('_')
      const dataId = item.id.split('_')
      if (data.length === 1) {
        Object.assign(this.itemService, {
          itemTypeName: data[0],
          itemTypeCode: dataId[0]
        })
      } else if (data.length === 2) {
        Object.assign(this.itemService, {
          itemTypeName: data[1],
          itemTypeCode: dataId[1],
          itemDetailName: data[0],
          itemDetailCode: dataId[0]
        })
      } else {
        Object.assign(this.itemService, {
          itemTypeName: data[2],
          itemTypeCode: dataId[2],
          itemDetailName: data[1],
          itemDetailCode: dataId[1],
          itemServiceName: data[0],
          itemServiceCode: dataId[0]
        })
      }
      // this.itemTypeCodeCheck = false
      this.checkOneChange(false, 'itemTypeCodeCheck', 'itemTypeCodeInput')
      // this.inputBlur()
    },
    // 获取服务事项
    getItemTreeData() {
      const params = {
        // workTypeCode: '1'
      }
      getItemTreeData(params).then((res) => {
        // console.log(res)
        if (res.status === 200) {
          // this.itemTreeData = this.$tools.listToTree(res.data, 'id', 'parent')
          this.itemTreeData = res.data
        }
      })
    },
    // 获取tabledata
    getWorkTableData() {
      const params = this.localtion
      Object.assign(params, this.itemService)
      Object.assign(params, {
        id: '',
        workTypeCode: this.workTypeCode,
        workTypeName: this.workTypeName,
        flowcode: this.searchForm.flowcode.toString(),
        urgencyDegree: this.searchForm.urgencyDegree.toString(),
        disDegreeNew: this.searchForm.disDegreeNew.toString(),
        workSources: this.searchForm.workSources.toString(),
        feedbackFlag: this.searchForm.feedbackFlag.toString(),
        responseTimeType: this.searchForm.responseTimeType.toString(),
        showTimeType: this.searchForm.showTimeType.toString(),
        typeSources: this.searchForm.typeSources.toString(),
        startTime: this.timeSlot[0] ?? '',
        endTime: this.timeSlot[1] ?? '',
        sectionStartDate: '',
        sectionEndDate: '',
        sectionStartTime: '',
        sectionEndTime: '',
        sourcesDept: this.sourcesDeptCheck ? '' : this.sourcesDeptSelect,
        sourcesDeptName: this.sourcesDeptCheck ? '' : this.sourcesDeptSelect,
        localtionName: this.localtionNameSelect ? '' : this.localtionNameSelect,
        'createBy.id': this.createByIdCheck ? '' : this.createByIdSelect,
        callerName: this.reportInfoCheck ? '' : this.reportInfoSelect,
        sourcesPhone: this.reportInfoCheck ? '' : this.reportInfoInput,
        // itemDetailCode: this.itemTypeCodeCheck ? '' : '',
        // itemDetailName: this.itemTypeCodeCheck ? '' : this.itemTypeCodeInput,
        questionDescription: this.questionDescriptionCheck ? '' : this.questionDescriptionInput,
        workNum: this.workNumCheck ? '' : this.workNumInput,
        // transportTypeCode: this.itemTypeCodeCheck ? '' : this.itemTypeCodeInput,
        companyCode: this.companyCodeCheck ? '' : this.companyCodeSelect,
        designateDeptCode: this.companyCodeCheck ? '' : this.typeTeamSelect,
        designatePersonCode: this.companyCodeCheck ? '' : this.servicePersonSelect,
        pageNo: this.pageNo,
        pageSize: this.pageSize
      })
      this.tableLoading = true
      getWorkOrderDataList(params).then((res) => {
        // console.log(res)
        this.tableLoading = false
        if (res.status === 200) {
          this.tableData = res.data.rows
          this.total = res.data.total
        } else {
          // this.$message({
          //   message: data.msg,
          //   type: 'warning'
          // })
        }
      })
    },
    resetFormData() {
      this.currentPage = 1
      this.pageSize = 15
      this.total = 0
      this.searchForm = {
        flowcode: [''],
        feedbackFlag: [''],
        showTimeType: ['3'],
        urgencyDegree: [''],
        workSources: [''],
        typeSources: [''],
        responseTimeType: [''],
        disDegreeNew: ['']
      }
      this.localtion = {
        region: '',
        buliding: '',
        storey: '',
        room: ''
      }
      this.sourcesDeptCheck = true
      this.sourcesDeptSelect = ''
      this.localtionNameCheck = true
      this.localtionNameSelect = ''
      this.companyCodeCheck = true
      this.companyCodeSelect = ''
      this.typeTeamSelect = ''
      this.servicePersonSelect = ''
      this.createByIdCheck = true
      this.createByIdSelect = ''
      this.reportInfoCheck = true
      this.reportInfoSelect = ''
      this.reportInfoInput = ''
      this.questionDescriptionCheck = true
      this.questionDescriptionInput = ''
      this.workNumCheck = true
      this.workNumInput = ''
      this.itemTypeCodeCheck = true
      this.itemTypeCodeInput = ''
      this.getWorkTableData()
    },
    handleSizeChange(val) {
      this.pageSize = val
      this.getWorkTableData()
    },
    handleCurrentChange(val) {
      this.pageNo = val
      this.getWorkTableData()
    },
    tableRowClick(row) {
      this.$router.push({ path: '/workOrderDetail', query: { id: row.id } })
    },
    goBack() {
      try {
        if (this.$route.params.sourceScreen === 'center') {
          window.chrome.webview.hostObjects.sync.bridge.ShowDialog(false)
        }
      } catch (error) {}
      this.$router.go(-1)
    }
  }
}
</script>

<style lang="scss" scoped>
@import '../../../assets/sino-ui/common/var.scss';
.content {
  position: relative;
  // background-color: #031553;
  background: url('~@/assets/images/qhdsys/bj.png') no-repeat;
  box-sizing: border-box;
  width: 100%;
  height: 100%;
  padding: 1.25rem 0.625rem 0.625rem 0.625rem;
}
.title {
  height: 30px;
  position: relative;
  div {
    cursor: pointer;
    position: absolute;
    top: 5px;
    left: 0;
    width: 80px;
    height: 30px;
    line-height: 30px;
    text-align: center;
    color: #618ad3;
    font-size: 0.875rem;
    white-space: nowrap;
    background-image: url(../../../assets/images/peace/btn-back.png);
    background-size: 100% 100%;
    background-repeat: no-repeat;
  }
  span {
    display: block;
    color: #dceaff;
    text-align: center;
    width: 200px;
    height: 100%;
    line-height: 30px;
    margin: 0 auto;
    background-image: url(../../../assets/images/war/title-bg.png);
    background-size: 100% 100%;
    background-repeat: no-repeat;
    font-family: 'TRENDS';
  }
}
.form-content {
  height: calc(100% - 30px);
  width: 100%;
  overflow-y: scroll;
}
.search-form {
  padding: 0.625rem;
  box-sizing: border-box;
  .form-row {
    height: 2.8125rem;
    display: flex;
    margin-bottom: 0.625rem;
    .row-label {
      width: 9.375rem;
      height: 2.8125rem;
      line-height: 2.8125rem;
      background: url(~@/assets/images/peace/label-box.png) no-repeat;
      background-size: 100% 100%;
      color: #77a5f1;
      font-size: 1rem;
      text-align: center;
      margin-right: 2.5rem;
    }
    .row-value {
      .value-first-checkbox {
        margin-right: 1.5625rem;
      }
      .value-span {
        font-size: 0.875rem;
        color: #acb1c4;
      }
      ::v-deep .el-checkbox-group {
        height: 45px;
        line-height: 45px;
      }
      ::v-deep .el-checkbox__label {
        color: #acb1c4;
      }
      ::v-deep .el-input {
        width: auto;
      }
      ::v-deep .el-textarea {
        width: 31.25rem;
        // height: 100%;
      }
      ::v-deep .el-select {
        margin-right: 1.5625rem;
      }
    }
    .time-range {
      ::v-deep .el-input__inner {
        background: center;
        border: 1px solid $input-border-color;
        border-radius: 0;
        color: $color;
        height: inherit;
        font-family: PingFangSC-Medium, PingFang SC;
      }
      ::v-deep .el-range-input {
        background: center;
        color: $color;
      }
      ::v-deep .el-range-separator,
      ::v-deep .el-range__icon {
        color: #3769be;
      }
    }
  }
  .allForm {
    transition: height 0.5s linear;
    overflow: hidden;
  }
}
.search-table {
  padding: 0.625rem;
  box-sizing: border-box;
  position: relative;
  .search-btn {
    position: absolute;
    right: 0.625rem;
    top: -3.125rem;
    height: 3.125rem;
  }
  ::v-deep .el-table__body tr:hover {
    cursor: pointer;
  }
}
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.4s;
}
.fade-enter, .fade-leave-to /* .fade-leave-active in <2.1.8 */ {
  opacity: 0;
}
</style>
