<template>
  <div v-if="visible" class="container">
    <div class="left-box">
      <div class="container-title">设备显示</div>
      <el-checkbox v-model="checkAllPipeline" :indeterminate="isIndeterminate" @change="handleCheckAllChange"><span style="color: #ffe3a6">全部</span></el-checkbox>
      <div v-scrollbarHover class="checkBoxAll">
        <div class="middle-div">
          <el-checkbox-group v-model="checkedPipeline" @change="handleCheckedPipelineChange">
            <div v-for="(item, index) in pipelineData" :key="index" class="middle-checkbox">
              <el-checkbox :key="index" :label="item.entityTypeId" :value="item.entityTypeId" class="checkbox-label"></el-checkbox>
              <div class="checkbox-div" :style="{ background: colorList[index] || 'rgb(255, 255, 255)' }"></div>
              <div class="checkbox-title">{{ item.entityTypeName || '' }}</div>
            </div>
          </el-checkbox-group>
        </div>
      </div>
    </div>
    <div class="right-box">
      <div class="container-title">运行状态</div>
      <div class="middle-div">
        <el-checkbox-group v-model="checkedState" @change="handleCheckedStateChange">
          <div v-for="(item,index) in stateData" :key="index" class="middle-checkbox">
            <el-checkbox :key="item.value" :label="item.value" :value="item.value" class="checkbox-label"></el-checkbox>
            <div class="checkbox-div" :style="{ background: item.color || 'rgb(255, 255, 255)' }"></div>
            <div class="checkbox-title">{{ item.label }}</div>
          </div>
        </el-checkbox-group>
      </div>
    </div>
  </div>
</template>

<script>
import { getEntityTypeByProjectCode } from '@/utils/spaceManage'
// import { pipelineData, pipelineParams } from '@/assets/common/dict.js'
export default {
  name: 'moveAllCheckbox',
  components: {},
  props: {
    roomData: {
      type: Object,
      default: () => {}
    },
    visible: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      isIndeterminate: true,
      checkAllPipeline: false,
      checkedPipeline: [],
      checkedState: ['10', '0', '6'], // 运行状态
      pipelineData: [],
      pipelineParams: {},
      pipeFlow: false,
      stateData: [
        {
          label: '正常',
          value: '0',
          color: '#61E29D'
        },
        {
          label: '异常',
          value: '10',
          color: '#FF2D55'
        },
        {
          label: '离线',
          value: '6',
          color: '#D4DEEC'
        }
      ],
      // 颜色列表
      colorList: ['#2166EB', '#9BC3FF', '#F53577', '#B18BC7', '#9BC5BD', '#E3AC4C', '#EE6D58', '#9B41D0', '#4CCBE3', '#726899']
    }
  },
  watch: {
    roomData: {
      handler(val) {
        this.getCategoryList()
      },
      deep: true
    }
  },
  mounted() {
    this.getCategoryList()
  },
  methods: {
    // 获取管线分类列表
    getCategoryList() {
      const params = this.roomData.isSpace == 0 ? {
        projectCode: this.roomData.projectCode,
        spaceId: this.roomData.spaceId,
        entityTypeId: this.roomData.entityTypeId
      } : {
        projectCode: this.roomData.projectCode,
        menuCode: this.roomData.menuCode,
        modelCode: this.roomData.modelCoding,
        entityTypeId: this.roomData.entityTypeId
      }
      if (params.projectCode) {
        getEntityTypeByProjectCode(params).then((res) => {
          const data = res.data
          if (data.code == 200 && data.data.length) {
            this.pipelineData = data.data
            const allIDs = Array.from(this.pipelineData, ({ entityTypeId }) => entityTypeId)
            this.checkedPipeline = allIDs
            this.setCheckDataToWpf()
          } else {
            this.pipelineData = []
          }
        })
      }
    },
    handleCheckAllChange(val) {
      this.checkedPipeline = val ? Array.from(this.pipelineData, ({ entityTypeId }) => entityTypeId) : []
      this.isIndeterminate = false
      this.setCheckDataToWpf()
    },
    handleCheckedPipelineChange(value) {
      const checkedCount = value.length
      this.checkAllPipeline = checkedCount === this.pipelineData.length
      this.isIndeterminate = checkedCount > 0 && checkedCount < this.pipelineData.length
      this.setCheckDataToWpf()
    },
    // 状态选择
    handleCheckedStateChange(value) {
      this.setCheckDataToWpf()
    },
    // 传输数据给wpf
    setCheckDataToWpf() {
      const modelCodeArr = this.pipelineData.filter((item) => this.checkedPipeline.includes(item.entityTypeId))
      let arr = ['IEMC-SecuritySystem', 'IEMC-FireAlarmSystem']
      const modelCodes = modelCodeArr.map((item) => {
        if (arr.includes(this.roomData.projectCode)) {
          return item.projectCode
        } else {
          return item.modelCode
        }
      }).filter((item) => item)
      const params = {
        // modelCodes: this.roomData.modelCoding ? this.roomData.modelCoding : modelCodes.toString(),
        modelCodes: modelCodes.toString(),
        projectCode: this.roomData.projectCode,
        electricityLevel: this.roomData.electricityLevel,
        states: this.checkedState.toString(),
        modelCode: this.roomData.modelCode
      }
      try {
        window.chrome.webview.hostObjects.sync.bridge.TypeViewingDevice(JSON.stringify(params))
        console.log(params, 'TypeViewingDevice')
      } catch (error) {}
    }
  }
}
</script>

<style lang="scss" scoped>
.container {
  width: 100%;
  height: 100%;
  display: flex;
  .container-title {
    padding: 7px 8px;
    background: rgba(133, 145, 206, 0.15);
    margin: 16px 0 6px 0px;
    font-size: 15px;
    font-family: HarmonyOS Sans SC-Medium, HarmonyOS Sans SC;
    font-weight: 500;
    color: #ffffff;
  }
  .left-box {
    flex: 1;
    overflow-y: auto;
    box-sizing: border-box;
  }
  .right-box {
    flex: 1;
    margin-left: 16px;
  }
  .middle-div {
    // min-width: 140px;
    .middle-checkbox {
      width: 100%;
      display: flex;
      align-items: center;
    }
    .checkbox-div {
      width: 12px;
      height: 8px;
      margin: 0 8px;
      flex-shrink: 0;
    }
    .checkbox-title {
      height: 20px;
      font-size: 14px;
      font-family: PingFang SC-Medium, PingFang SC;
      font-weight: 500;
      color: #ffffff;
      line-height: 20px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
    .checkbox-label {
      ::v-deep .el-checkbox__label {
        display: none;
      }
    }
  }
  .checkBoxAll {
    height: calc(100% - 75px);
    overflow-y: scroll;
  }
}
.middle-div:nth-child(1) {
  margin-top: 2px !important;
}
::v-deep .el-checkbox__input {
  .el-checkbox__inner {
    box-shadow: 0px 0px 3px 0px #78fff8;
    opacity: 1;
    border: 1px solid #52fffc;
    background: transparent;
  }
}
::v-deep .el-checkbox-group {
  // display: flex;
  // flex-wrap: wrap;
  .el-checkbox {
    display: block;
    margin: 3px 0;
    padding: 3px 5px 3px 0px;
    box-sizing: border-box;
    border-radius: 6px;
    .el-checkbox__label {
      font-family: PingFangSC-Medium, PingFang SC;
      font-size: 14px;
      // overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
    }
  }
}
</style>
