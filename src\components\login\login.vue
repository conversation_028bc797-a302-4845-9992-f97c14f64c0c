<!--
 * @Author: ycw
 * @Date: 2020-05-27 11:19:35
 * @LastEditors: ycw
 * @LastEditTime: 2020-05-31 11:21:54
 * @Description:
-->
<template>
  <div class="sino-login">
    <div class="sino-login-left login" :style="{ 'background-image': `url(${bgWelcome})` }">
      <div class="sino-welcome">
        <img :src="titleWelcome" alt srcset />
      </div>
      <div class="sino-version">v1.0.0</div>
    </div>
    <div class="sino-login-rigth login">
      <div class="sino-login-titile">登录</div>
      <div class="sino-login-from">
        <el-form label-position="top" label-width="80px" :model="loginForm" :rules="rules" ref="loginInfo">
          <el-form-item label="用户名" prop="userName">
            <el-input v-model="loginForm.userName" @input="checkChange" placeholder="用户名/手机号"></el-input>
          </el-form-item>
          <el-form-item label="密码" prop="password">
            <el-input v-model="loginForm.password" @input="checkChange" placeholder="用户密码" show-password></el-input>
            <div class="el-form-item__error" v-if="errmsg">
              {{ errmsg }}
            </div>
          </el-form-item>
          <el-form-item class="sino-login-btn" style="margin-top: 40px">
            <div class="login-btn" @click="loginClick">登 录</div>
          </el-form-item>
        </el-form>
      </div>
      <div class="sino-login-hint">请使用Google浏览器，最佳分辨率1920x1080</div>
    </div>
    <Vcode :show="isShow" @success="onSuccess" @close="onClose" :sliderSize="35" />
  </div>
</template>

<script>
import bgWelcome from '@assets/images/login/<EMAIL>'
import titleWelcome from '@assets/images/login/<EMAIL>'
import Vcode from 'vue-puzzle-vcode'
import { loginAPI } from '@/utils/api'
export default {
  components: {
    Vcode
  },
  data() {
    const userNameCheck = (rule, value, callback) => {
      const userNameReg = /^([\dA-Za-z_]{2,11})$/
      if (!value) {
        callback(new Error('请填写用户名/手机号'))
      } else if (!userNameReg.test(value)) {
        callback(new Error('请输入2至11位字母或数字'))
      } else {
        callback()
      }
    }
    const passwordCheck = (rule, value, callback) => {
      // const userNameReg = /^([\dA-Za-z_]{2,11})$/
      if (!value) {
        callback(new Error('请填写密码'))
      } else {
        callback()
      }
    }
    return {
      isShow: false,
      bgWelcome: bgWelcome,
      titleWelcome: titleWelcome,
      loginForm: {
        userName: 'admin',
        password: '123456',
        verificationCode: ''
      },
      verificationCode: '',
      errmsg: '',
      verificationCodemsg: '',
      rules: {
        userName: [{ validator: userNameCheck, trigger: 'blur' }],
        password: [{ validator: passwordCheck, trigger: 'blur' }]
      }
    }
  },
  created() {},
  methods: {
    loginClick() {
      this.$refs.loginInfo.validate((vaild) => {
        if (vaild) {
          this.isShow = true
        }
      })
    },
    async login() {
      const res = await loginAPI()
      const checkUserName = res.data.data.find((item) => {
        return item.phone === this.loginForm.userName || item.userName === this.loginForm.userName
      })
      if (checkUserName) {
        if (checkUserName.password === this.loginForm.password) {
          const loginInfo = 'loginInfo'
          this.$store.commit('setLoginInfo', loginInfo)
          this.$store.commit('loginState', 1)
          this.$router.push('/')
        } else {
          this.$message.error('用户名密码错误')
        }
      } else {
        this.$message.error('用户名无效')
      }
    },
    checkChange() {
      this.errmsg = ''
    },
    onSuccess() {
      this.isShow = false
      this.login()
    },
    onClose() {}
  }
}
</script>

<style lang="scss" scoped>
::v-deep .vue-auth-box_ {
  left: 60% !important;
  top: 50% !important;
}
.login {
  height: 100%;
  display: inline-block;
  vertical-align: top;
}
.sino-login-left {
  width: 358.4px;
  background-repeat: no-repeat;
  background-position: center;
  background-size: 100% 100%;
  .sino-welcome {
    height: 90%;
    img {
      width: 306px;
      display: block;
      margin: 0 auto;
      position: relative;
      top: 42%;
    }
  }
  .sino-version {
    height: 10%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 14px;
    color: #fff;
  }
}
.sino-login-rigth {
  width: 464px;
  position: relative;
  .sino-login-titile {
    height: 45px;
    line-height: 60px;
    font-size: 18px;
    font-weight: bold;
    color: #548bf9;
    text-align: center;
  }
  .sino-login-from {
    width: 360px;
    margin: 0 auto;
    padding: 20px 0;
    .el-form-item {
      margin-bottom: 10px;
      .el-form-item__label {
        padding: 0;
      }
    }
  }
  .sino-login-hint {
    width: 100%;
    color: rgba(153, 153, 153, 1);
    text-align: center;
    font-size: 12px;
    position: absolute;
    bottom: 23px;
  }
}
.sino-login-btn {
  .login-btn {
    height: 40px;
    line-height: 40px;
    font-size: 18px;
    font-weight: 400;
    text-align: center;
    color: rgba(255, 255, 255, 1);
    background: rgba(84, 139, 249, 1);
    border-radius: 27px;
    cursor: pointer;
    &:hover,
    &:focus {
      background: rgba(84, 139, 249, 0.9);
    }
  }
}
</style>
