<template>
  <div v-if="dialogShow" class="deviceInfoManagement">
    <!-- <div style="color: black; top: 50px; left: -930px; position: absolute">{{ roomData }}</div> -->
    <!-- <div style="color: red; top: 70px; left: -930px; position: absolute">{{ dialogData }}</div> -->
    <div class="right-content">
      <div class="content">
        <div class="bg-title el-dialog__header">
          <span>{{ roomData.deviceName }}</span
          ><i class="el-icon-close" @click="closeDeviceDialog"></i>
        </div>
        <div class="bg-content room-info-box">
          <div class="box-type">
            <span v-for="(item, index) in roomTypeFilterList" :id="item.Dom" :key="index" class="tabs-item" @click="activeTypeChildEvent(item.Dom)">{{ item.name }}</span>
          </div>
          <div class="sys-box">
            <!-- <component :is="currentComponent" :roomData="dialogData" class="sys-box-content"></component> -->
            <!-- <keep-alive> -->
            <component :is="currentComponent" type="device" :deviceId="roomData.deviceId" :roomData="roomData" :isMonitor="isMonitor" class="sys-box-content" @roomEvent="roomEvent"></component>
            <!-- </keep-alive> -->
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import $ from 'jquery'
import { monitorTypeList } from '@/assets/common/dict.js'
export default {
  name: 'deviceInfoManagement',
  components: {
    basicAccountComponent: () => import('./sysTypeComponent/basicAccountComponent'),
    repairComponentChlid: () => import('./sysTypeComponent/repairComponent'),
    icisComponentChlid: () => import('./sysTypeComponent/icisComponent'),
    upkeepComponentChlid: () => import('./sysTypeComponent/upkeepComponent'),
    energyComponentChlid: () => import('./sysTypeComponent/energyComponent'),
    travelRecordChlid: () => import('./sysTypeComponent/travelRecord'),
    poisonousHempSafe: () => import('./sysTypeComponent/poisonousHempSafe'),
    constructionDetails: () => import('./sysTypeComponent/constructionDetails'),
    constructionInsp: () => import('./sysTypeComponent/constructionInsp'),
    safeComponent: () => import('./sysTypeComponent/safeComponent'),
    warehouseDetails: () => import('./sysTypeComponent/warehouseDetails'),
    deptDetails: () => import('./sysTypeComponent/deptDetails'),
    assetDetails: () => import('./sysTypeComponent/assetDetails'),
    assetAlarmDetails: () => import('./sysTypeComponent/assetAlarmDetails'),
    keyAreasDetails: () => import('./sysTypeComponent/keyAreasDetails')
  },
  props: {
    dialogData: {
      type: Object,
      default: () => {}
    },
    dialogShow: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      activeTypeChild: '',
      roomTypeList: [
        {
          Dom: 'basicAccountComponent',
          name: '基础台账',
          component: 'basicAccountComponent',
          is: ['task', 'chemicals', 'SpaceChair', 'SecuritySys', 'KeyAreas']
        },
        {
          Dom: 'repairComponentChlid',
          name: '维修',
          component: 'repairComponentChlid',
          is: ['PoisonousHemp', 'OperatinRoom', 'LogisticsVehicle', 'HazMat', 'task', 'chemicals', 'SpaceChair', 'SecuritySys', 'KeyAreas']
        },
        {
          Dom: 'icisComponentChlid',
          name: '巡检',
          component: 'icisComponentChlid',
          is: ['PoisonousHemp', 'OperatinRoom', 'LogisticsVehicle', 'HazMat', 'task', 'chemicals', 'SpaceChair', 'SecuritySys', 'KeyAreas']
        },
        {
          Dom: 'upkeepComponentChlid',
          name: '保养',
          component: 'upkeepComponentChlid',
          is: ['PoisonousHemp', 'OperatinRoom', 'LogisticsVehicle', 'HazMat', 'task', 'chemicals', 'SpaceChair', 'SecuritySys', 'KeyAreas']
        },
        { // 毒麻精方-报警
          Dom: 'poisonousHempSafe',
          name: '报警',
          component: 'poisonousHempSafe',
          is: [
            'Kongtiao',
            'Yqxt',
            'Electricity',
            'Ups',
            'light',
            'ASHP',
            'WaterSupply',
            'ElevatorSystem',
            'SecuritySystem',
            'FireAlarmSystem',
            'Pipeline',
            'OperatinRoom',
            'LogisticsVehicle',
            'HazMat',
            'SpaceChair',
            'SecuritySys',
            'KeyAreas',
            // 空间
            'space',
            'ioms',
            'Airend',
            'Electricity',
            'danger',
            'imes',
            'energy',
            'task',
            'chemicals'
          ]
        },
        {
          Dom: 'safeComponent',
          name: '报警',
          component: 'safeComponent',
          is: [
            'Kongtiao',
            'Yqxt',
            'Electricity',
            'Ups',
            'light',
            'ASHP',
            'WaterSupply',
            'ElevatorSystem',
            'SecuritySystem',
            'FireAlarmSystem',
            'Pipeline',
            'PoisonousHemp',
            'LogisticsVehicle',
            'SpaceChair',
            'SecuritySys',
            'KeyAreas',
            // 空间
            'space',
            'ioms',
            'Airend',
            'Electricity',
            'danger',
            'imes',
            'energy',
            'task',
            'chemicals'
          ]
        },
        {
          Dom: 'constructionDetails',
          name: '详情',
          component: 'constructionDetails',
          is: [
            'Kongtiao',
            'Yqxt',
            'Electricity',
            'Ups',
            'light',
            'ASHP',
            'WaterSupply',
            'ElevatorSystem',
            'SecuritySystem',
            'FireAlarmSystem',
            'Pipeline',
            'PoisonousHemp',
            'OperatinRoom',
            'LogisticsVehicle',
            'HazMat',
            'SpaceChair',
            'SecuritySys',
            'KeyAreas',
            // 空间
            'space',
            'ioms',
            'Airend',
            'Electricity',
            'danger',
            'imes',
            'energy',
            'chemicals'
          ]
        },
        {
          Dom: 'constructionInsp',
          name: '巡检',
          component: 'constructionInsp',
          is: [
            'Kongtiao',
            'Yqxt',
            'Electricity',
            'Ups',
            'light',
            'ASHP',
            'WaterSupply',
            'ElevatorSystem',
            'SecuritySystem',
            'FireAlarmSystem',
            'Pipeline',
            'PoisonousHemp',
            'OperatinRoom',
            'LogisticsVehicle',
            'HazMat',
            'SpaceChair',
            'SecuritySys',
            'KeyAreas',
            // 空间
            'space',
            'ioms',
            'Airend',
            'Electricity',
            'danger',
            'imes',
            'energy',
            'chemicals'
          ]
        },
        {
          Dom: 'warehouseDetails',
          name: '物资概览',
          component: 'warehouseDetails',
          is: [
            'Kongtiao',
            'Yqxt',
            'Electricity',
            'Ups',
            'light',
            'ASHP',
            'WaterSupply',
            'ElevatorSystem',
            'SecuritySystem',
            'FireAlarmSystem',
            'Pipeline',
            'PoisonousHemp',
            'OperatinRoom',
            'LogisticsVehicle',
            'HazMat',
            'SpaceChair',
            'SecuritySys',
            'KeyAreas',
            // 空间
            'space',
            'ioms',
            'Airend',
            'Electricity',
            'danger',
            'imes',
            'energy',
            'task'
          ]
        },
        { // 重点区域详情
          Dom: 'keyAreasDetails',
          name: '详情',
          component: 'keyAreasDetails',
          is: [
            'Kongtiao',
            'Yqxt',
            'Electricity',
            'Ups',
            'light',
            'ASHP',
            'WaterSupply',
            'ElevatorSystem',
            'SecuritySystem',
            'FireAlarmSystem',
            'Pipeline',
            'PoisonousHemp',
            'OperatinRoom',
            'LogisticsVehicle',
            'HazMat',
            'SpaceChair',
            'SecuritySys',
            // 空间
            'space',
            'ioms',
            'Airend',
            'Electricity',
            'danger',
            'imes',
            'energy',
            'task',
            'chemicals'
          ]
        }
        // ,{
        //   Dom: 'travelChlid',
        //   name: '通行记录',
        //   component: 'travelRecordChlid'
        // }
        // {
        //   Dom: 'energyChild',
        //   name: '能耗',
        //   component: 'energyComponentChlid'
        // }
      ],
      roomData: {
        isDevice: 0, // 0设备
        // buildingCode: '01',
        // floorCode: '01',
        // roomCode: '01',
        deviceName: '',
        assetsId: '', // 资产id
        deviceId: ''
        // assetsId: '21e3da90d0814143accad16e7c91373d', // 资产id
        // deviceId: '00075dffac9243e5bfacec4e0042f53e' // 维修设备id
      },
      asidShow: false,
      dialogBoxShow: true
    }
  },
  computed: {
    data: function () {
      var obj = {}
      obj = JSON.parse(JSON.stringify(this.dialogData)) // this.templateData是父组件传递的对象
      return obj
    },
    currentComponent() {
      return this.roomTypeList.find((e) => e.Dom === this.activeTypeChild)?.component ?? ''
    },
    roomTypeFilterList() {
      return this.roomTypeList.filter(
        (e) => !e.is.includes(this.dialogData.tabName)
      )
    },
    // 基础信息是否显示监测
    isMonitor() {
      return ['OperatinRoom', 'HazMat', 'LogisticsVehicle', 'SpaceChair', 'SecuritySys'].includes(this.dialogData.tabName)
    }
  },
  watch: {
    dialogShow(val) {
      if (val) {
        this.activeTypeChild = ''
      }
    }
  },
  created() {
    this.dynamicNav()
    // if (this.dialogData.type === 'move') {
    // this.mergeData(this.activeTypeChild)
    // }
  },
  mounted() {
    console.log('详情信息', this.dialogData)
  },
  methods: {
    // 动态导航
    dynamicNav() {
      if (this.dialogData.detailsNav && this.dialogData.detailsNav.length) {
        let navItem = {}
        // 由具体模块动态控制详情展示那些导航
        for (let i = 0; i < this.dialogData.detailsNav.length; i++) {
          navItem = this.dialogData.detailsNav[i]
          this.roomTypeList.push({
            Dom: navItem.component,
            name: navItem.name,
            component: navItem.component,
            is: []
          })
        }
      }
      if (__PATH.VUE_APP_HOSPITAL_NODE == 'fjslyy' && this.roomData.projectCode == monitorTypeList.find((e) => e.name == '门禁设备').projectCode) {
        this.roomTypeList.push({
          Dom: 'travelChlid',
          name: '通行记录',
          component: 'travelRecordChlid'
        })
      }
      this.activeTypeChild = this.roomTypeFilterList[0].Dom
      this.mergeData(this.activeTypeChild)
    },
    roomEvent(data) {
      // if (data.type === 'basicAccount') {
      //   this.roomData.deviceName = data.deviceName
      // }
    },
    mergeData(type) {
      Object.assign(this.roomData, this.dialogData)
      this.$nextTick(() => {
        this.activeTypeChildEvent(type)
      })
    },
    activeTypeChildEvent(type) {
      console.log(type)
      this.activeTypeChild = type
      this.roomTypeList.forEach((item) => {
        if (item.Dom === type) {
          $(`#${item.Dom}`).addClass('type_active')
        } else {
          $(`#${item.Dom}`).removeClass('type_active')
        }
      })
    },
    equipmentDetail() {
      this.asidShow = true
    },
    configCloseTableDialog() {
      this.asidShow = false
    },
    closeDeviceDialog() {
      this.$emit('configCloseDialog')
    }
  }
}
</script>

<style lang="scss" scoped>
.deviceInfoManagement {
  position: absolute;
  top: 0;
  right: 0%;
  // width: 22%;
  width: 100%;
  height: 100%;
  // margin-top: 2%;
  height: 100%;
  .content {
    padding: 0px 25px 10px 35px;
    height: 100%;
  }
  .right-content {
    margin: 0 0 0 auto;
    width: 24.573%;
    background: url('~@/assets/images/qhdsys/bg-mask.png') no-repeat;
    height: 100%;
    background-color: transparent;
    // background: url('~@/assets/images/center/bg-80.png') no-repeat;
    background-size: 100% 100%;
    box-sizing: border-box;
    position: relative;
    .bg-title {
      background-color: rgba(255, 224, 152, 0.12) !important;
      height: 44px;
      line-height: 44px;
      padding: 0 10px 0 1rem;
      color: #ffca64;
      font-family: TRENDS;

      width: 100%;
      span {
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        display: inline-block;
        width: calc(100% - 40px);
      }
      i {
        float: right;
        line-height: 44px;
        font-size: 1.5rem;
        cursor: pointer;
      }
    }
    .bg-content {
      position: relative;
      box-sizing: border-box;
      padding: 10px 2px 10px 2px;
      width: 100%;
      height: calc(100% - 44px);
    }
    .room-info-box {
      color: #fff;
      display: flex;
      flex-direction: column;
      .box-type {
        width: 100%;
        margin: auto;
        display: flex;
        flex-wrap: wrap;
        justify-content: center;
        .tabs-item:nth-child(1) {
          flex: 2;
        }
        span {
          // display: inline-block;
          // width: fit-content;
          // height: 24px;
          // padding: 0 10px;
          // background-color: #24396d;
          // text-align: center;
          // line-height: 24px;
          // color: #dceaff;
          // font-size: 14px;
          // font-family: PingFang-SC-Medium, PingFang-SC;
          // cursor: pointer;
          // margin: 5px 3px 0 3px;
          flex: 2;
          display: inline-block;
          width: fit-content;
          cursor: pointer;
          padding: 10px 0;
          text-align: center;
          font-size: 16px;
          font-weight: 400;
          color: #a4afc1;
          line-height: 19px;
          overflow: hidden;
          background: linear-gradient(360deg, #334572 0%, rgba(38, 49, 79, 0.14) 57%, rgba(36, 46, 73, 0) 100%);
          border-left: 1px solid;
          border-bottom: 1px solid;
          border-image: linear-gradient(180deg, rgba(113, 125, 168, 0), rgba(113, 128, 168, 1)) 1 1;
        }
        .tabs-item:last-child {
          border-right: 1px solid;
        }
        .type_active {
          // color: #ffe3a6;
          // background: url('~@/assets/images/center/border-bg-select.png') no-repeat;
          // background-size: 100% 100%;
          position: relative;
          color: #b0e3fa;
          background: linear-gradient(360deg, #3a668e 0%, rgba(36, 46, 73, 0) 100%);
        }
      }
      .sys-box {
        width: 100%;
        flex: 1;
        max-height: calc(100% - 44px);
        .sys-box-content {
          width: 100%;
          height: 100%;
          padding: 5px 0px 0 0px;
          box-sizing: border-box;
        }
        // height: calc(100% - 24px);
      }
    }
  }
}
</style>
