<template>
  <div class="travelRecord">
    <ModuleCard v-if="!deviceId" title="通行次数排行" class="module-container" style="height: 45%">
      <div slot="title-right" class="title-right">
        <div class="toggle">
          <div v-if="roomData.ssmType <= 3" :class="sortType == 1 ? 'active-type' : ''" @click="changeSortType(1)">建筑</div>
          <div v-if="roomData.ssmType <= 3 || roomData.ssmType == 4" :class="sortType == 2 ? 'active-type' : ''" @click="changeSortType(2)">楼层</div>
          <div v-if="roomData.ssmType <= 3 || roomData.ssmType == 4 || roomData.ssmType == 5" :class="sortType == 3 ? 'active-type' : ''" @click="changeSortType(3)">设备</div>
        </div>
      </div>
      <div slot="content" v-loading="chartLoading" class="module-content" style="height: 100%">
        <div id="travelNumChart" style="width: 100%; height: 100%"></div>
      </div>
    </ModuleCard>
    <ModuleCard title="通行记录" class="module-container" style="height: 55%">
      <div slot="title-right" class="title-right">
        <img src="../../../assets/images/order_more.png" class="order-more" @click="() => isTravelRecord = true" />
      </div>
      <div slot="content" class="module-content" style="height: 100%">
        <el-table
          :key="sortType"
          v-el-table-infinite-scroll="tableLoadMore"
          v-scrollHideTooltip
          v-loading="tableLoading"
          class="table-center-transfer"
          :data="tableList"
          height="100%"
          :cell-style="{ padding: ' 8px', backgroundColor: 'transparent', border: 'none', padding: '3px' }"
          :header-cell-style="{ background: '#8591CE26!important', color: '#8BDDF5FF', padding: '4px 8px', fontWeight: 'bold' }"
          style="width: 100%"
          element-loading-background="rgba(0, 0, 0, 0.2)"
        >
          <el-table-column v-for="(column, index) in tableColumn" :key="index" :prop="column.prop" :label="column.label" :min-width="column.minWidth" :sortable="column.sortable || false" show-overflow-tooltip>
            <template slot-scope="scope">
              <table-render v-if="column.render" :sc="scope" :row="scope.row" :render="column.render"></table-render>
              <div v-else-if="!column.formatter">
                {{ scope.row[column.prop] }}
              </div>
              <div v-else>
                {{ column.formatter(scope.row) }}
              </div>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </ModuleCard>
    <travelRecordDialog v-if="isTravelRecord" :dialogShow="isTravelRecord" :selectParam="selectParam" @travelRecordClose="() => (isTravelRecord = false)" />
  </div>
</template>

<script>
import {GetAccessRecordBySpaceId, GetAccessRecordByAassertNum, GetAccessRecordList} from '@/utils/centerScreenApi'
import * as echarts from 'echarts'
import travelRecordDialog from './components/travelRecordDialog.vue'
import { monitorTypeList } from '@/assets/common/dict.js'

export default {
  name: 'travelRecord',
  components: {
    travelRecordDialog
  },
  props: {
    roomData: {
      type: Object,
      default: () => { }
    },
    deviceId: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      isTravelRecord: false,
      selectParam: {},
      sortType: 1,
      chartLoading: false,
      tableLoading: false,
      tableList: [],
      tableColumn: [
        {
          prop: 'personName',
          label: '姓名'
        },
        // {
        //   prop: '',
        //   label: '卡号'
        // },
        {
          prop: 'passTime',
          label: '开门时间',
          sortable: true,
          minWidth: 120
        },
        {
          prop: 'deviceName',
          label: '门禁名称'
        }
      ],
      pagination: {
        total: 0,
        pageSize: 15,
        pageNo: 1
      }
    }
  },
  computed: {

  },
  watch: {
    roomData: {
      handler(val) {
        this.pagination.pageNo = 1
        this.selectParam = {spaceId: this.roomData.ssmCodes?.split(',').at(-1)}
        if (this.deviceId) {
          this.selectParam = {assetsNumber: this.deviceId}
        } else {
          if (val.ssmType < 4) {
            this.changeSortType(1)
          } else if (val.ssmType == 4) {
            this.changeSortType(2)
          } else if (val.ssmType == 5) {
            this.changeSortType(3)
          }
        }
        this.getAccessRecordList()
      },
      deep: true
    },
    isTravelRecord(val) {
      try {
        window.chrome.webview.hostObjects.sync.bridge.ShowDialog(val)
      } catch (error) {}
    }
  },
  created() {
    // this.selectParam = {spaceId: '1724753193000267778'}
    this.selectParam = {spaceId: this.roomData.ssmCodes?.split(',').at(-1)}
    if (this.deviceId) {
      this.selectParam = {assetsNumber: this.deviceId}
    } else {
      if (this.roomData.ssmType < 4) {
        this.changeSortType(1)
      } else if (this.roomData.ssmType == 4) {
        this.changeSortType(2)
      } else if (this.roomData.ssmType == 5) {
        this.changeSortType(3)
      }
    }
    this.getAccessRecordList()
  },
  methods: {
    // 通行记录
    getAccessRecordList() {
      let params = {
        page: this.pagination.pageNo,
        pageSize: this.pagination.pageSize,
        projectCode: monitorTypeList.find((e) => e.name == '门禁设备').projectCode,
        ...this.selectParam
      }
      if (this.pagination.pageNo == 1) this.tableList = []
      this.tableLoading = true
      GetAccessRecordList(params).then(res => {
        this.tableLoading = false
        if (res.data.code == 200) {
          this.tableList = this.tableList.concat(res.data.data.list)
          this.pagination.total = res.data.data.count
        }
      })
    },
    tableLoadMore() {
      if (this.pagination.total > this.pagination.pageNo * this.pagination.pageSize) {
        this.pagination.pageNo++
        this.getAccessRecordList()
      }
    },
    // 空间-通行记录排行
    getAccessRecordBySpaceId() {
      let params = {
        page: 1,
        pageSize: 3,
        queryFlag: this.sortType,
        projectCode: monitorTypeList.find((e) => e.name == '门禁设备').projectCode,
        // spaceId: '1724753193000267778'
        spaceId: this.roomData.ssmCodes?.split(',').at(-1)
      }
      GetAccessRecordBySpaceId(params).then(res => {
        this.chartLoading = false
        if (res.data.code == 200) {
          let arr = res.data.data.map(item => {
            return {
              id: item.spaceId,
              name: item.spaceName,
              value: item.accessNum
            }
          })
          arr.sort((a, b) => {
            return a.value - b.value
          })
          this.barChart(arr)
        }
      })
    },
    // 设备-通行记录排行
    getAccessRecordByAassertNum() {
      let params = {
        page: 1,
        pageSize: 3,
        projectCode: monitorTypeList.find((e) => e.name == '门禁设备').projectCode,
        // spaceId: '1724753193000267778'
        spaceId: this.roomData.ssmCodes?.split(',').at(-1)
      }
      GetAccessRecordByAassertNum(params).then(res => {
        this.chartLoading = false
        if (res.data.code == 200) {
          let arr = res.data.data.map(item => {
            return {
              id: item.assetsNumber,
              name: item.surveyName,
              value: item.accessNum
            }
          })
          // arr[0].value = 100
          // arr[0].id = 666
          arr.sort((a, b) => {
            return a.value - b.value
          })
          this.barChart(arr)
        }
      })
    },
    // 柱状图
    barChart(list) {
      let chart = echarts.init(document.getElementById('travelNumChart'))
      let option = {}
      if (list.length) {
        option = {
          legend: {},
          grid: {
            top: '6%',
            left: '5%',
            right: '5%',
            bottom: '0%',
            containLabel: true
          },
          xAxis: {
            type: 'value',
            show: false
          },
          yAxis: [
            {
              type: 'category',
              data: list.map(v => v.name),
              // inverse: true,
              axisLabel: {
                inside: true,
                verticalAlign: 'bottom',
                lineHeight: 36,
                margin: 0, // 刻度标签与轴线之间的距离
                textStyle: { color: '#fff', fontSize: '14' }
              },
              axisLine: {
                show: false
              },
              axisTick: {
                show: false
              },
              splitLine: {
                show: false
              }
            },
            {
              type: 'category',
              data: list.map(v => v.value),
              // inverse: true,
              axisLabel: {
                inside: true, // 数值和图表分离
                verticalAlign: 'bottom',
                lineHeight: 34,
                margin: 0,
                textStyle: {
                  color: '#FFFFFFCC',
                  fontSize: '14'
                },
                formatter: (value) => {
                  return value + '次'
                }
              },
              axisLine: {
                show: false
              },
              axisTick: {
                show: false
              },
              splitLine: {
                show: false
              }
            }

          ],
          series: [
            {
              data: list,
              type: 'bar',
              yAxisIndex: 0,
              barWidth: 8, // 柱子宽度
              label: {
                show: false
              },
              itemStyle: {
                normal: {
                  barBorderRadius: [15, 15, 15, 15],
                  color: (params) => {
                    // 通过返回值的下标一一对应将颜色赋给柱子上
                    return '#0A84FFFF'
                  }
                },
                // 鼠标移入改变颜色
                emphasis: {
                  color: '#FFCA64FF'
                }
              },
              showBackground: true, // 显示背景色
              backgroundStyle: {
                color: '#384156'
              }
            }
          ],
          dataZoom: [
            {
              yAxisIndex: [0, 1],
              orient: 'vertical',
              show: true,
              type: 'slider',
              start: 100,
              // end: 100,
              width: 8,
              left: '99%',
              borderColor: 'rgba(43,48,67,.1)',
              fillerColor: '#6580b8',
              zoomLock: true,
              showDataShadow: false,
              backgroundColor: 'rgba(43,48,67,.1)',
              showDetail: false,
              // realtime: true,
              filterMode: 'filter',
              handleIcon: 'M-9.35,34.56V42m0-40V9.5m-2,0h4a2,2,0,0,1,2,2v21a2,2,0,0,1-2,2h-4a2,2,0,0,1-2-2v-21A2,2,0,0,1-11.35,9.5Z',
              handleStyle: {
                color: '#6580b8',
                borderColor: '#6580b8'
              },
              maxValueSpan: 5,
              minValueSpan: 5,
              brushSelect: false
            },
            {
              type: 'inside',
              show: false,
              yAxisIndex: [0, 1],
              height: 8
            }
          ]
        }
      } else {
        option = {
          title: {
            text: '暂无数据',
            x: 'center',
            y: 'center',
            textStyle: {
              fontSize: 14,
              fontWeight: 'normal',
              color: '#999'
            }
          }
        }
      }
      chart.clear()
      chart.setOption(option)
      chart.off('click')
      // 点击事件
      chart.on('click', (params) => {
        this.pagination.pageNo = 1
        if (this.sortType == 1 || this.sortType == 2) {
          this.selectParam = {spaceId: params.data.id}
          this.getAccessRecordList()
        } else if (this.sortType == 3) {
          this.selectParam = {assetsNumber: params.data.id}
          this.getAccessRecordList()
        }
      })
    },
    changeSortType(sortType) {
      this.sortType = sortType
      this.$nextTick(() => {
        this.chartLoading = true
        if (sortType == 1 || sortType == 2) {
          this.getAccessRecordBySpaceId()
        } else {
          this.getAccessRecordByAassertNum()
        }
      })
    }
  }
}

</script>

<style lang="scss" scoped>
.travelRecord {
  width: 100%;
  height: 100%;
  .title-right {
    display: flex;
    align-items: center;
    line-height: normal;
    .right-svg {
      color: #fff;
      font-size: 24px;
      cursor: pointer;
    }
    .toggle {
      display: flex;
      cursor: pointer;
      font-size: 14px;
    }
    .toggle > div {
      padding: 4px 10px;
      color: #8bddf5 !important;
      text-align: center;
      background-color: #213251;
      box-sizing: border-box;
      border: 1px solid #213251;
      opacity: 1;
    }
    .active-type {
      background: rgba(255, 255, 255, 0.1) linear-gradient(90deg, rgba(10, 132, 255, 0.4) 0%, rgba(10, 132, 255, 0) 100%);
      border: 1px solid #abf0ff !important;
    }
    .order-more {
      width: 24px;
      height: 24px;
      margin-right: 0 !important;
      margin-left: 8px;
      cursor: pointer;
    }
  }
}
</style>
