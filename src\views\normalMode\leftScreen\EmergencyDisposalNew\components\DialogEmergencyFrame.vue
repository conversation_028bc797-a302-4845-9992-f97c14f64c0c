<template>
  <el-dialog v-dialogDrag class="component DialogEmergencyFrame" :visible="visible" width="80%"
             :title="dialogFrameTitle" :close-on-click-modal="false" :show-close="false"
             custom-class="DialogEmergencyFrame__dialog">
    <div class="DialogEmergencyFrame__head">
      <div class="DialogEmergencyFrame__head__close" @click="closeDialogFrame"></div>
      <div v-if="currentAlarmId" class="DialogEmergencyFrame__head__nav">
        <div class="DialogEmergencyFrame__head__back" @click="onAlarmIdChange()">
        </div>
        <div class="DialogEmergencyFrame__head__breadcrumb">
          <div class="DialogEmergencyFrame__head__breadcrumb__item" @click="onAlarmIdChange()">全部报警</div>
          <div class="DialogEmergencyFrame__head__breadcrumb__item">报警详情</div>
        </div>
      </div>
    </div>
    <div class="DialogEmergencyFrame__content">
      <EmergencyDetail v-if="currentAlarmId" :alarmId="currentAlarmId" :tab="toTab" @cancel="onAlarmIdChange()">
      </EmergencyDetail>
      <EmergencyDataTable v-if="visible" v-show="showTable" :show="showTable" :paramsData="paramsData" @detail="onAlarmIdChange" />
    </div>
  </el-dialog>
</template>

<script>

import EmergencyDataTable from './EmergencyDataTable.vue'
import EmergencyDetail from './EmergencyDetail.vue'

export default {
  name: 'DialogEmergencyFrame',
  components: {
    EmergencyDataTable,
    EmergencyDetail
  },
  props: {
    alarmId: {
      type: String
    },
    visible: {
      type: Boolean,
      required: true
    },
    targetTab: String,
    paramsData: {
      type: Object,
      default: () => {}
    }
  },
  emits: ['update:visible', 'update:alarmId'],
  data() {
    return {
      currentAlarmId: '',
      toTab: ''
    }
  },
  computed: {
    dialogFrameTitle: function () {
      return this.currentAlarmId ? '报警详情' : '全部报警'
    },
    showTable: function () {
      return !this.currentAlarmId
    }
  },
  watch: {
    alarmId: {
      handler: function (val) {
        this.currentAlarmId = val
      },
      immediate: true
    },
    targetTab: {
      handler: function (val) {
        this.toTab = val ?? ''
      },
      immediate: true
    },
    visible(val) {
      if (val && !this.alarmId) {
        this.currentAlarmId = ''
      }
    }
  },
  methods: {
    // 修改alarmId
    onAlarmIdChange(id = '', tab = '') {
      this.currentAlarmId = id
      this.$emit('update:alarmId', id)
      this.toTab = tab
    },
    closeDialogFrame() {
      this.$emit('update:visible', false)
      this.$emit('update:alarmId', '')
    }
  }
}

</script>

<style lang="scss" scoped>
/* 全部数据弹窗，做为业务主窗体 */
.DialogEmergencyFrame {

  ::v-deep .DialogEmergencyFrame__dialog.el-dialog {
    background: url('@/assets/images/qhdsys/1536x951.png') no-repeat center center / 100% 100%;
    margin-top: 6vh !important;
    height: 88vh;
    overflow: hidden;

    .el-dialog__header {
      padding: 10px 20px 10px;
      text-align: center;

      .el-dialog__title {
        height: 20px;
        font-size: 18px;
        font-family: Source Han Sans CN-Regular, Source Han Sans CN;
        font-weight: 400;
        color: #cbdeed;
        line-height: 18px;
      }
    }

    .el-dialog__body {
      height: calc(100% - 40px);
    }

    .el-dialog__footer {
      padding-top: 0;
      padding-bottom: 40px;
      padding-right: 40px;
    }
  }

  &__head {
    position: absolute;
    left: 52px;
    top: 18px;
    right: 52px;
    display: flex;
    flex-flow: row-reverse nowrap;
    justify-content: space-between;

    &__close {
      width: 36px;
      height: 36px;
      background: url('@/assets/images/qhdsys/bg-icon-gb.png') no-repeat;
      cursor: pointer;
    }

    &__nav {
      display: flex;
      align-items: center;
    }

    &__back {
      width: 36px;
      height: 36px;
      background: url('@/assets/images/qhdsys/bg-icon-fx.png') no-repeat;
      cursor: pointer;
      transform: rotateY(180deg);
    }

    &__breadcrumb {
      margin-left: 8px;
      display: inline-flex;

      &__item:first-child {
        color: #A6AFBF;
        cursor: pointer;
      }

      &__item:last-child {
        color: #B0E3FA;

        &::before {
          content: '/';
          margin: 0 6px;
          color: #A6AFBF;
        }
      }
    }
  }

  &__content {
    height: 100%;
    overflow: hidden;
  }
}
</style>

<style lang="scss">
.component.DialogEmergencyFrame {

  // 新版弹窗 去除按钮的背景色
  .DialogEmergencyFrame__content {
    .el-button.sino-button-sure {
      height: 33px;
      line-height: 33px;
      padding: 0 10px;
      background: url('~@/assets/images/qhdsys/button-bg.png') no-repeat center/100% 33px transparent;

      &:not(.is-disabled):hover {
        background: url('~@/assets/images/qhdsys/button-bg-hover.png') no-repeat center/100% 33px transparent;
      }
    }

    .el-form-item {
      .el-form-item__label {
        color: #B0E3FA;
        line-height: 32px;
      }

      .el-form-item__content {
        line-height: 32px;
      }

      &:last-of-type {
        margin-bottom: 16px;
      }

      .el-input__inner {
        line-height: 30px;
      }

      .el-input--suffix {
        .el-input__icon {
          line-height: 32px;
        }
      }

      .el-date-editor {
        padding: 0 10px;

        .el-range-input {
          height: 30px;
        }

        .el-input__icon,
        .el-range-separator {
          line-height: 30px;
        }
      }
    }

    .el-table {
      .el-table__cell {
        padding: 5px 0 !important;
        border: none;

        .cell {
          font-size: 14px;
          line-height: 30px;
        }
      }
    }
  }
}
</style>
