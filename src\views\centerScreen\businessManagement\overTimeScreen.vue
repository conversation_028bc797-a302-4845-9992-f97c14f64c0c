<template>
  <div class="content">
    <!-- <el-button class="sino-button-sure" @click="overTimeListShow = !overTimeListShow">showDialog</el-button> -->
    <overTimeList v-if="overTimeListShow" ref="overTimeList" :type="type" :location="location" :ssmType="ssmType" :dialogShow="overTimeListShow" @configCloseDialog="configCloseDialog"></overTimeList>
  </div>
</template>
<script>
import overTimeList from './component/overTimeList'
export default {
  components: {
    overTimeList
  },
  name: 'overTimeScreen',
  data() {
    return {
      type: 'IOMS', // IOMS IPAS IDPS
      location: '',
      ssmType: '',
      overTimeListShow: true
    }
  },
  beforeRouteEnter(to, from, next) {
    if (from.path === '/workOrderDetail') {
      next((vm) => {
        vm.overTimeListShow = true
      })
    } else {
      next()
    }
  },
  mounted() {
    // 初始化 获取定位信息
    if (Object.hasOwn(this.$route.query, 'type')) {
      this.type = this.$route.query.type || ''
    }
    try {
      window.chrome.webview.addEventListener('message', (event) => {
        // const data = JSON.parse(event.data)
        this.overTimeListShow = true
        window.chrome.webview.hostObjects.sync.bridge.ShowTable(true)
        this.$nextTick(() => {
          this.$refs.overTimeList.getDepartMedicalWasteTableList()
        })
      })
    } catch (error) {}
  },
  methods: {
    configCloseDialog() {
      this.overTimeListShow = false
      try {
        window.chrome.webview.hostObjects.sync.bridge.ShowTable(false)
      } catch (error) {}
    }
  }
}
</script>
<style lang="scss" scoped>
.content {
  width: 100%;
  height: 100%;
}
</style>
