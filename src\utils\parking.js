import http from './http'

const ICIS_API = __PATH.VUE_APP_ICIS_API
// 获取停车场运营总览
export function operateTotal (params) {
  return http.postRequest(`${ICIS_API}/parking/operateTotalDetail`, params)
}
// 停车场入场记录
export function getInParkingRecord(params) {
  return http.requestPost(`${ICIS_API}/parking/inParkingRecord`, params)
}
// 停车场出场记录
export function getOutParkingRecord(params) {
  return http.requestPost(`${ICIS_API}/parking/outParkingRecord`, params)
}
// 车位监控
export function superviseTotal (params) {
  return http.getQueryQS(`${ICIS_API}/parking/parkingSuperviseInfo`, params)
}
// 流量压力分析
export function flowAnalysis (params) {
  return http.getQueryQS(`${ICIS_API}/parking/passagewayStress`, params)
}
