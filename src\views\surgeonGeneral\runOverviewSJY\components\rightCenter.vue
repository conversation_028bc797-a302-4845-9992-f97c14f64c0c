<template>
  <div class="left-content-item">
    <CardTitle title="科室椅位状态分布" position="right" >
      <template slot="right">
        <el-dropdown trigger="click" @command="dataTypeCommand">
          <span class="el-dropdown-link dropdown-title">
            {{ sortFieldTypeList.find((v) => v.value == sortField)?.name ?? "" }}
            <i class="el-icon-caret-bottom"></i>
          </span>
          <el-dropdown-menu slot="dropdown">
            <el-dropdown-item
              v-for="item in sortFieldTypeList"
              :key="item.value"
              :command="item.value"
              :class="{ isBjxl: sortField == item.value }"
            >
              {{ item.name }}
            </el-dropdown-item>
          </el-dropdown-menu>
        </el-dropdown>
        <el-dropdown trigger="click" @command="orderTypeCommand">
          <span class="el-dropdown-link dropdown-title">
            {{ ascTypeList.find((v) => v.value == asc)?.name ?? "" }}
            <i class="el-icon-caret-bottom"></i>
          </span>
          <el-dropdown-menu slot="dropdown">
            <el-dropdown-item
              v-for="item in ascTypeList"
              :key="item.value"
              :command="item.value"
              :class="{ isBjxl: asc == item.value }"
            >
              {{ item.name }}
            </el-dropdown-item>
          </el-dropdown-menu>
        </el-dropdown>
      </template>
    </CardTitle>
    <div v-loading="chartLoading"  class="card-content" >
      <div id="deptChartsRanking"></div>
    </div>
  </div>
</template>
<script>
import CardTitle from './title'
import * as echarts from 'echarts'
import { deptChaiRanking } from '@/utils/runOverviewSJY'
export default {
  components: {
    CardTitle
  },
  data() {
    return {
      sortField: 'TOTAL',
      sortFieldTypeList: [
        { value: 'TOTAL', name: '椅位总数' },
        { value: 'INUSE', name: '使用中' },
        { value: 'IDLE', name: '空闲' },
        { value: 'ABNORMAL', name: '异常占用' }
      ],
      asc: false,
      ascTypeList: [
        { value: false, name: '从多到少' },
        { value: true, name: '从少到多' }
      ],
      chartLoading: false,
      deptChartsRanking: null,
      chartData: [

      ]
    }
  },
  mounted() {
    this.getData()
  },
  methods: {
    dataTypeCommand (val) {
      this.sortField = val
      this.getData()
    },
    orderTypeCommand (val) {
      this.asc = val
      this.getData()
    },
    getData () {
      this.chartLoading = true
      let params = {
        sortField: this.sortField,
        asc: this.asc
      }
      deptChaiRanking(params).then(res => {
        if (res.data.code == 200) {
          this.chartData = res.data.data
        }
      }).finally(() => {
        this.chartLoading = false
        this.initEcharts(this.chartData || [])
      })
    },
    initEcharts(data) {
      // 初始化统计表
      // 基于准备好的dom，初始化echarts实例
      this.deptChartsRanking = echarts.init(document.getElementById('deptChartsRanking'))
      const barWidth = 10 /* 进度条宽度 */
      let inUseList = [] /* 正常 */
      let abnormalList = [] /* 异常 */
      let idleList = [] /* 空闲 */
      let chairTotalList = [] /* 进度条数值总量 */
      let topName = [] /* 进度条名称 */
      data.forEach((item, i) => {
        topName[i] = {
          value: item.relevantDeptName || '-',
          textStyle: {
            color: '#FFF'
          }
        }
        chairTotalList[i] = item.totalCount
        inUseList[i] = item.inUse
        abnormalList[i] = item.abnormal
        idleList[i] = item.idle
      })
      let config = {}
      if (data.length) {
        // 配置参数
        config = {
          background: '#ffff',
          barCategoryGap: '20%',
          tooltip: {
            show: false,
            textStyle: {
              fontSize: 16
            }
          },
          grid: {
            left: '2%',
            right: '10%',
            top: '13%',
            bottom: '0%',
            containLabel: true
          },
          dataZoom: [],
          legend: {
            show: true,
            top: '3%',
            itemHeight: 8,
            itemWidth: 8,
            itemGap: 20,
            textStyle: {
              fontSize: 12,
              color: '#fff'
            },
            selectedMode: false
          },
          xAxis: {
            show: false,
            type: 'value'
          },
          yAxis: [
            {
              type: 'category',
              inverse: true,
              triggerEvent: true,
              axisTick: {
                show: false
              },
              axisLine: {
                show: false
              },
              axisLabel: {
                textStyle: {
                  color: '#fff',
                  fontSize: 14
                },
                interval: 0 // 强制显示所有 label
              },
              data: topName
            }
          ],
          series: [
            {
              name: '使用中',
              stack: 'total',
              type: 'bar',
              barWidth: barWidth,
              data: inUseList,
              itemStyle: {
                color: 'rgba(0, 85, 188, 0.6)',
                borderColor: '#0055BC',
                borderWidth: 1
              }
            },
            {
              name: '异常占用',
              type: 'bar',
              barWidth: barWidth,
              stack: 'total',
              data: abnormalList,
              itemStyle: {
                color: 'rgba(255, 0, 83, 0.6)',
                borderColor: '#FF0053',
                borderWidth: 1
              }
            },
            {
              name: '空闲',
              type: 'bar',
              barWidth: barWidth,
              stack: 'total',
              data: idleList,
              itemStyle: {
                color: 'rgba(212, 222, 236, 0.6)',
                borderColor: '#D4DEEC',
                borderWidth: 1
              }
            },
            // total
            {
              type: 'bar',
              zlevel: 1,
              barWidth: barWidth,
              barGap: '-100%',
              label: {
                show: true,
                position: 'right',
                valueAnimation: true,
                fontSize: 14,
                distance: 15,
                color: '#DADEE1'
              },
              itemStyle: {
                color: 'transparent'
              },
              data: chairTotalList
            }
          ]
        }
      } else {
        config = {
          title: {
            text: '暂无数据',
            x: 'center',
            y: 'center',
            textStyle: {
              fontSize: 14,
              fontWeight: 'normal',
              color: '#999'
            }
          }
        }
      }
      if (data.length > 4) {
        config.dataZoom = [
          {
            yAxisIndex: [0],
            orient: 'vertical',
            show: true,
            type: 'slider',
            startValue: 0,
            end: 50,
            width: 8,
            left: '99%',
            borderColor: '#88ddf5',
            fillerColor: '#88ddf5',
            zoomLock: true,
            showDataShadow: false,
            backgroundColor: '#88ddf5',
            showDetail: false,
            // realtime: true,
            filterMode: 'filter',
            handleIcon:
              'M-9.35,34.56V42m0-40V9.5m-2,0h4a2,2,0,0,1,2,2v21a2,2,0,0,1-2,2h-4a2,2,0,0,1-2-2v-21A2,2,0,0,1-11.35,9.5Z',
            handleStyle: {
              color: '#88ddf5',
              borderColor: '#88ddf5'
            },
            maxValueSpan: 4,
            minValueSpan: 4,
            brushSelect: false
          },
          {
            type: 'inside',
            show: false,
            yAxisIndex: [0],
            height: 8
          }
        ]
      }
      // 设置参数
      this.deptChartsRanking.setOption(config)
      // 先移除点击事件 解决点击事件重复绑定
      // this.deptChartsRanking.off('click')
      // // 点击事件
      // this.deptChartsRanking.on('click', (params) => {
      //   const index = params.dataIndex
      //   // 新增变量 判断当前点击柱状图是选中还是取消选中
      //   let isSelected = false
      //   let name = ''
      //   topName.map((e, i) => {
      //     // 选中的设置选中色
      //     if (i == index && e.textStyle.color != '#FFCA64FF') {
      //       e.textStyle.color = '#FFCA64FF'
      //       name = e.value
      //     } else {
      //       // 选中已选中的则为取消选中
      //       if (i == index && e.textStyle.color == '#FFCA64FF') {
      //         isSelected = true
      //       }
      //       // 其他的设为默认色
      //       e.textStyle.color = '#FFF'
      //     }
      //   })
      //   config.yAxis.data = JSON.parse(JSON.stringify(topName))
      //   this.deptChartsRanking.setOption(config)
      // })
    }
  }

}
</script>
<style lang="scss" scoped>
.left-content-item {
  width: 100%;
  height: calc(100% / 3 - 7px);
  background: url("~@/assets/images/qhdsys/new-card-bg.png") no-repeat;
  background-size: 100% 100%;
  margin-bottom: 10px;
  .card-content {
    height: calc(100% - 36px);
    width: 100%;
    padding: 16px;
    #deptChartsRanking{
      width: 100%;
      height: 100%;
    }
  }
}
.dropdown-title{
  color: #fff;
}
</style>
