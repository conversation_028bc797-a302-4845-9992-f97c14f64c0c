<!--
 * @Author: hedd
 * @Date: 2023-11-18 16:32:27
 * @LastEditTime: 2024-07-17 15:38:54
 * @FilePath: \ihcrs_client_iframe\src\views\spaceManage\sysTypeComponent\components\upkeepRemindDialog.vue
 * @Description:
-->
<template>
  <div class="upkeepRemindDialog">
    <el-dialog v-dialogDrag :modal="false" width="40%" :visible.sync="dialogShow" custom-class="remindDialog main" :close-on-click-modal="false" :before-close="closeDialog">
      <template slot="title">
        <span class="dialog-title">保养提醒({{ remindData.count }})</span>
      </template>
      <div class="remind_content">
        <div class="form-search">
          <span>统计时间:</span>
          <el-select v-model="dataType" placeholder="请选择统计时间" @change="changeDateType">
            <el-option v-for="item in tagList" :key="item.value" :label="item.text" :value="item.value"> </el-option>
          </el-select>
        </div>
        <div v-scrollbarHover class="remind-list">
          <div v-for="item in remindData.dateList" :key="item.id" class="time-line-box">
            <div class="top-date">
              <div class="data-ymd">{{ item.taskStartTime }}</div>
              <div class="defaule-icon"></div>
            </div>
            <div class="box-content">
              <div class="time-work-order" @click="openUpkeepDialog(item.taskStartTime)">
                <span class="time-work-order-event">保养任务：</span>
                <span class="work-order-detail">{{ item.count }}个</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </el-dialog>
    <template v-if="upKeepTableListShow">
      <upKeepTableListDialog
        ref="upKeepTableListDialog"
        :upkeepTime="activeUpkeepTime"
        :deviceId="deviceId"
        :dialogShow="upKeepTableListShow"
        @configCloseDialog="closeUpkeepDialog"
      ></upKeepTableListDialog>
    </template>
  </div>
</template>

<script>
import { GetInspTaskRemindNum } from '@/utils/spaceManage'
import upKeepTableListDialog from '../../components/upKeepTableListDialog.vue'
export default {
  name: 'upkeepRemindDialog',
  components: {
    upKeepTableListDialog
  },
  props: {
    dialogShow: {
      type: Boolean,
      default: false
    },
    deviceId: {
      type: String,
      default: ''
    },
    redmineCount: {
      type: Number,
      default: 0
    },
    tagCurrent: {
      type: String,
      default: 'day'
    },
    upkeepList: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      dataType: '',
      tagList: [
        { text: '今日', value: 'day' },
        { text: '本周', value: 'week' },
        { text: '本月', value: 'month' },
        { text: '本年', value: 'year' }
      ],
      remindData: {
        count: 0,
        dateList: []
      },
      upKeepTableListShow: false,
      activeUpkeepTime: ''
    }
  },
  mounted() {
    this.remindData = {
      count: this.redmineCount,
      dateList: this.upkeepList
    }
    this.dataType = this.tagCurrent
  },
  methods: {
    // 改变统计时间
    changeDateType(val) {
      this.dataType = val
      const params = {
        deviceId: this.deviceId,
        dateType: this.dataType,
        systemCode: 2
      }
      GetInspTaskRemindNum(params).then((res) => {
        if (res.data.code === '200') {
          this.remindData = res.data.data
        }
      })
    },
    // 打开保养提醒弹窗
    openUpkeepDialog(time) {
      this.activeUpkeepTime = time
      this.upKeepTableListShow = true
    },
    // 关闭保养提醒弹窗
    closeUpkeepDialog() {
      this.upKeepTableListShow = false
      try {
        window.chrome.webview.hostObjects.sync.bridge.ShowDialog(true)
      } catch (error) {}
    },
    closeDialog() {
      try {
        window.chrome.webview.hostObjects.sync.bridge.ShowDialog(false)
      } catch (error) {}
      this.$emit('configCloseDialog')
    }
  }
}
</script>

<style lang="scss" scoped>
.upkeepRemindDialog {
  width: 100%;
  height: 100%;
  .remind_content {
    width: 100%;
    height: 100%;
    padding: 10px 20px;
    .form-search {
      height: 50px;
      line-height: 50px;
      font-size: 14px;
      font-family: PingFang SC, PingFang SC;
      color: #b0e3fa;
      & > span {
        margin-right: 10px;
      }
      ::v-deep .el-select {
        width: 200px;
        height: 32px;
        .el-input__inner {
          height: 32px;
          line-height: 32px;
        }
        .el-input__icon {
          line-height: 32px;
        }
      }
    }
    .remind-list {
      height: calc(100% - 50px);
      overflow-y: auto;
      .time-line-box {
        margin-bottom: 6px;
        .top-date {
          height: 24px;
          line-height: 24px;
          display: flex;
          .data-ymd {
            min-width: 85px;
            font-size: 14px;
            font-family: PingFang SC-Medium, PingFang SC;
            color: #fff;
            font-weight: 600;
          }
          .defaule-icon {
            width: 12px;
            height: 12px;
            margin: auto 10px;
            background: #fff;
            border: 2px solid #ffe3a6;
            border-radius: 50%;
          }
          .data-hms {
            font-size: 14px;
            font-family: HarmonyOS Sans SC, HarmonyOS Sans SC;
            color: #fff;
          }
        }
        .box-content {
          margin-left: 100px;
          padding-left: 20px;
          border-left: 2px solid #f6f5fa;
          .time-work-order {
            background: rgba(246, 245, 250, 0.1);
            border-radius: 4px;
            padding: 10px;
            font-family: HarmonyOS Sans SC, HarmonyOS Sans SC;
            cursor: pointer;
            .time-work-order-event {
              font-size: 16px;
              color: #ffe3a6;
            }
            .work-order-detail {
              font-size: 14px;
              color: #fff;
              padding-left: 10px;
            }
          }
        }
      }
    }
  }
  ::v-deep .remindDialog {
    width: 80%;
    height: 60vh;
    margin-top: 18vh !important;
    background-color: transparent !important;
    background-image: url('@/assets/images/table-bg-small.png') !important;
    background-repeat: no-repeat;
    background-size: 100% 100%;
    border: none;
    pointer-events: auto;
    box-shadow: none;
    .el-dialog__body {
      padding: 10px 50px 30px 50px;
      height: calc(100% - 80px);
      max-height: 80vh;
    }
    .dialog-title {
      display: inline-block;
      width: 100%;
      text-align: center;
      transform: translateY(-6px);
      color: #cbdeed;
    }
    .dialog-title::before {
      display: none;
    }
    .el-dialog__headerbtn {
      transform: translateX(-36px);
      width: 25px;
      height: 25px;
      background-image: url('@/assets/images/close.png');
      background-repeat: no-repeat;
      background-size: 100% 100%;
      .el-dialog__close::before {
        display: none;
      }
    }
  }
}
</style>
