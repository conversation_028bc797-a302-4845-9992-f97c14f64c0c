<template>
  <div class="content">
    <div class="left-content">
      <div class="left-top">
        <div class="bg-title">后勤服务班组</div>
        <div class="bg-content">
          <div class="case-discuss">
            <div class="case-discuss-content" :style="'width: ' + currentCase.length * 13.28 + 'rem'">
              <div v-for="(item, index) in currentCase" :key="index" class="discuss-conent-children">
                <div class="case-img">
                  <img src="../../../assets/images/qhdsys/count.png" />
                  <div class="case-num">
                    <p>{{ item.num }}<span>&nbsp;人</span></p>
                    <p style="font-size: 0.75rem; margin-top: 0.4rem; color: #cfe1f7">人数</p>
                  </div>
                  <div class="case-anim-icon"></div>
                </div>
                <div class="case-tit">
                  <p class="case-con">{{ item.team_name }}</p>
                  <p class="case-con" :title="item.phone">{{ item.phone }}</p>
                </div>
              </div>
            </div>
          </div>
          <div class="case-control-l case-control-btn" @click="lastCase"><i class="el-icon-arrow-left"></i></div>
          <div class="case-control-r case-control-btn" @click="nextCase"><i class="el-icon-arrow-right"></i></div>
        </div>
      </div>
      <div class="left-center">
        <div class="bg-title">后勤服务工作综合分析</div>
        <div class="bg-content" style="display: flex">
          <div class="analysis-order"></div>
          <div class="analysis-box">
            <div class="analysis-box-top">
              <div class="day-order" @click="jumpWorkOrder('day')">
                <p>本日工单</p>
                <p>{{ serviceAnalysis.todayNum }}<span>&nbsp;单</span></p>
              </div>
              <div class="month-order" @click="jumpWorkOrder('month')">
                <p>本月工单</p>
                <p>{{ serviceAnalysis.monthNum }}<span>&nbsp;单</span></p>
              </div>
              <div class="die-order" @click="jumpWorkOrder('weekGD')">
                <p style="color: #ff5454">本周挂单</p>
                <p>{{ serviceAnalysis.weekHangNum }}<span>&nbsp;单</span></p>
              </div>
              <div class="day-analysis">
                <div class="day-analysis-left">
                  <p>
                    <span>本日完成</span><span>{{ serviceAnalysis.toDayFinish }}</span>
                  </p>
                  <p>
                    <span>本日未完成</span><span>{{ serviceAnalysis.toDayUnFinish }}</span>
                  </p>
                </div>
                <div class="day-analysis-right">
                  <!-- <el-progress class="circle-box" type="circle" :percentage="(72 / (72 + 28)*100)"></el-progress> -->
                  <circle-box comColor="#FFC884" boxId="1" :percent="serviceAnalysis.completeRate" />
                  <p>完成率</p>
                </div>
              </div>
            </div>
            <div class="analysis-box-bottom">
              <div class="out-time-order">
                <div class="out-time-order-left">
                  <p>{{ serviceAnalysis.countOverTimes }}<span>&nbsp;单</span></p>
                  <p>本日超时工单</p>
                </div>
                <div class="out-time-order-right">
                  <circle-box comColor="#6FFDE2" boxId="2" :percent="serviceAnalysis.countOverTimesRate" />
                  <p>占比</p>
                </div>
              </div>
              <div class="month-average-order">
                <!-- <p>4<span>&nbsp;时</span>&nbsp;&nbsp;15<span>&nbsp;分</span>&nbsp;&nbsp;41<span>&nbsp;秒</span></p> -->
                <p>{{ serviceAnalysis.finishTime }}</p>
                <p>当月平均完工时长</p>
              </div>
              <div class="month-analysis">
                <div class="month-analysis-left">
                  <!-- <p>72<span>&nbsp;%</span></p> -->
                  <p>{{ serviceAnalysis.satisfiedRate }}</p>
                  <p>本月满意度</p>
                </div>
                <div class="month-analysis-right">
                  <!-- <p>92<span>&nbsp;%</span></p> -->
                  <p>{{ serviceAnalysis.callBackRate }}</p>
                  <p>7日回访率</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="left-bottom">
        <div class="bg-title">服务工单动态</div>
        <div class="bg-content">
          <el-table
            :data="workOrderTableData"
            height="100%"
            stripe
            :cell-style="$tools.setCell(3)"
            :header-cell-style="$tools.setHeaderCell(3)"
            style="width: 100%"
            element-loading-background="rgba(0, 0, 0, 0.2)"
          >
            <el-table-column prop="createDate" show-overflow-tooltip label="时间"></el-table-column>
            <el-table-column prop="sourcesDeptName" show-overflow-tooltip label="申报科室">
              <template slot-scope="scope">
                {{ scope.row.sourcesDeptName === 'undefined' ? '' : scope.row.sourcesDeptName }}
              </template>
            </el-table-column>
            <el-table-column prop="createByName" show-overflow-tooltip label="申报人"></el-table-column>
            <el-table-column prop="workTypeName" show-overflow-tooltip label="工单类型"></el-table-column>
            <el-table-column prop="designateDeptName" show-overflow-tooltip label="服务班组"></el-table-column>
            <el-table-column prop="designatePersonName" show-overflow-tooltip label="服务人员"></el-table-column>
            <el-table-column prop="flowType" show-overflow-tooltip label="工单状态"></el-table-column>
            <el-table-column show-overflow-tooltip label="操作">
              <template slot-scope="scope">
                <div class="operationBtn">
                  <span v-if="scope.row.flowCode !== '5'" style="margin-right: 10px" @click="selectRowData(scope.row, 'cui')">催单</span>
                  <span @click="selectRowData(scope.row, 'detail')">详情</span>
                </div>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>
    </div>
    <div class="right-content">
      <div class="right-top">
        <div class="bg-title">后勤设备状态分析</div>
        <div class="bg-content" style="display: flex">
          <div class="equiue-state-analysis-left">
            <div class="cricle-analysis">
              <div id="analysisEcharts"></div>
            </div>
          </div>
          <div class="equiue-state-analysis-right">
            <div class="analysis-right-top">
              <div class="order-box">
                <div class="order-bg"></div>
                <div class="order-content">
                  <p>{{ maintenance.notTenance }}</p>
                  <p>待维保</p>
                </div>
              </div>
              <div class="order-box">
                <div class="order-bg"></div>
                <div class="order-content">
                  <p>{{ maintenance.inspection }}</p>
                  <p>待巡检</p>
                </div>
              </div>
              <div class="order-box">
                <div class="order-bg"></div>
                <div class="order-content">
                  <p>{{ maintenance.notMaintenance }}</p>
                  <p>待维修</p>
                </div>
              </div>
            </div>
            <div class="analysis-right-right">
              <div class="order-box">
                <div class="order-bg"></div>
                <div class="order-content">
                  <p>{{ maintenance.maintenance }}</p>
                  <p>维保到期</p>
                </div>
              </div>
              <div class="order-box">
                <div class="order-bg"></div>
                <div class="order-content">
                  <p>{{ maintenance.lifeTime }}</p>
                  <p>寿命到期</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="right-center">
        <div class="bg-title">今日任务分析</div>
        <div class="bg-content">
          <div class="order-dynamic">
            <div class="order-dynamic-num">
              <div class="dynamic-num-box">
                <p>{{ taskAnalysisStatistics.sum }}</p>
                <p>任务总数</p>
              </div>
              <div class="dynamic-num-box">
                <p>{{ taskAnalysisStatistics.notAll }}</p>
                <p style="color: #6b96df">待执行</p>
              </div>
              <div class="dynamic-num-box">
                <p>{{ taskAnalysisStatistics.percentageComplete }}</p>
                <p style="color: #6b96df">完成率</p>
              </div>
              <div class="dynamic-num-box">
                <p>{{ taskAnalysisStatistics?.timeoutAll ?? 0 }}</p>
                <p>超时任务</p>
              </div>
            </div>
          </div>
          <el-table
            :data="todayTaskAnalysis"
            height="calc(100% - 3.3rem)"
            :cell-style="$tools.setCell(3)"
            :header-cell-style="$tools.setHeaderCell(3)"
            style="width: 100%; margin-top: 0.3rem"
            element-loading-background="rgba(0, 0, 0, 0.2)"
            stripe
          >
            <el-table-column prop="taskName" show-overflow-tooltip label="任务名称"></el-table-column>
            <el-table-column prop="totalCount" show-overflow-tooltip label="任务点数"></el-table-column>
            <el-table-column prop="distributionTeamName" show-overflow-tooltip label="执行部门"></el-table-column>
            <!-- <el-table-column prop="planPersonName" show-overflow-tooltip label="执行人"></el-table-column> -->
            <el-table-column
              prop="taskStatus"
              show-overflow-tooltip
              label="状态"
              :formatter="
                (row, column, cellValue, index) => {
                  return cellValue === null ? '' : cellValue === 1 ? '未完成' : '已完成'
                }
              "
            ></el-table-column>
            <!-- <el-table-column prop="designatePersonName" show-overflow-tooltip label="完成率" :formatter="(row, column) => {return row}"></el-table-column> -->
            <el-table-column prop="designatePersonName" show-overflow-tooltip label="完成率">
              <template slot-scope="scope">
                {{ ((scope.row.hasCount / scope.row.totalCount) * 100).toFixed(2) }}%
              </template>
            </el-table-column>
            <el-table-column show-overflow-tooltip label="操作">
              <template slot-scope="scope">
                <div class="operationBtn">
                  <span @click="selectRowData(scope.row, 'edit')">详情</span>
                </div>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>
      <div class="right-bottom">
        <div class="bg-title">值班记录</div>
        <div class="bg-content" style="display: flex">
          <div class="duty-record-after">
            <el-timeline>
              <el-timeline-item v-for="(duty, index) in dutyRecord" :key="index" :icon="duty.icon" :color="duty.color">
                <slot name="timestamp">
                  <div class="timestamp-row">
                    <div :style="{ 'font-size': index === 0 ? '1rem' : '0.875rem' }">{{ duty.name }}</div>
                    <div :style="{ 'font-size': index === 0 ? '1rem' : '0.875rem', color: index === 0 ? '#fff' : '#a0a6bc' }">{{ duty.plan }}</div>
                  </div>
                </slot>
              </el-timeline-item>
            </el-timeline>
          </div>
          <div class="duty-record-now">
            <p class="now-time">当前值班时间</p>
            <div class="time-slot">
              <p>
                开始时间 <span>{{ shiftRecord?.createTime }}</span>
              </p>
              <p>
                结束时间 <span>{{ shiftRecord?.endTime }}</span>
              </p>
            </div>
            <div class="duty-btn">
              <div v-if="shiftRecord?.theDayData" class="duty-now-btn duty-now-btn-disable" style="margin-right: 10px">
                <p>已打卡</p>
                <p v-if="countDown">{{ countDown }}</p>
              </div>
              <div v-else class="duty-now-btn" style="margin-right: 10px" @click="dutyEvent">
                <p>值班打卡</p>
              </div>
              <div class="duty-now-btn" @click="shiftsEvent">交接班</div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <template v-if="dialogVisibleSignIn">
      <signInForm ref="signForm" :dialogVisibleSignIn="dialogVisibleSignIn" :currentShift="shiftRecord" @closeDialogSignIn="closeDialogSignIn" @sureSignIn="sureSignIn"></signInForm>
    </template>
    <template v-if="dialogVisibleShifts">
      <shiftsForm ref="shiftsForm" :dialogVisible="dialogVisibleShifts" :shiftsData="shiftRecord.theDayData" @closeDialogShifts="closeDialogShifts" @sureShifts="sureShifts"></shiftsForm>
    </template>
  </div>
</template>
<script>
import $ from 'jquery'
import * as echarts from 'echarts'
import circleBox from '@/components/common/circleBox.vue'
import shiftsForm from './components/shiftsForm'
import signInForm from './components/signInForm'
// getMaintenance
import { getComprehensiveWorkOrderInfo, getDynamicWorkOrderList, urgeWorkOder, getTodayTaskAnalysis, getDataByTypeTeam, getShiftRecord, getMaintenance } from '@/utils/peaceRightScreenApi'
import moment from 'moment'
moment.locale('zh-cn')
export default {
  name: 'peopleWorkOrder',
  components: {
    shiftsForm,
    signInForm,
    circleBox
  },
  data() {
    return {
      selectedCase: 0,
      currentCase: [],
      workOrderTableData: [],
      todayTaskAnalysis: [],
      dutyRecord: [
        {
          name: '上一班次记录',
          plan: '暂无记录',
          icon: 'el-icon-time',
          color: '#06164E'
        },
        {
          name: '遗留问题',
          plan: '暂无记录'
        },
        {
          name: '值班情况',
          plan: '暂无记录'
        }
      ],
      serviceAnalysis: {
        callBackNum: 0,
        callBackRate: '0.0%',
        closedCompleted: 0,
        completeRate: 0,
        countOverTimes: '0',
        countOverTimesRate: 0,
        finishTime: '0秒',
        monthNum: 0,
        satisfiedRate: '0.00%',
        todayNum: 0,
        unCompleteRate: '0.00%',
        weekHangNum: 0,
        toDayFinish: 0,
        toDayUnFinish: 0
      },
      maintenance: {
        notTenance: '0',
        maintenance: '0',
        lifeTime: '0',
        inspection: '0',
        notMaintenance: '0'
      },
      taskAnalysisStatistics: {
        sum: 0,
        notAll: 0,
        percentageComplete: '0.00%',
        TimeoutAll: '0.00%'
      },
      dialogVisibleSignIn: false,
      dialogVisibleShifts: false,
      shiftRecord: {},
      currentTime: '',
      currentTimer: null,
      countDown: 0,
      timer: null
    }
  },
  mounted() {
    this.search()
    this.getShiftRecord()
    this.timer = setInterval(() => {
      setTimeout(() => {
        this.search()
      }, 0)
    }, 1000 * timerNum)
    this.$once('hook:beforeDestroy', () => {
      clearInterval(this.timer)
      this.timer = null
    })
  },
  destroyed() {
    clearInterval(this.currentTimer)
  },
  methods: {
    search() {
      this.getWorkOrderTableData()
      this.GetTodayTaskAnalysis()
      this.getServiceAnalysis()
      this.getMaintenance()
      this.getTypeTeam()
    },
    getTypeTeam() {
      getDataByTypeTeam({}).then((res) => {
        if (res.data.code === '200') {
          this.currentCase = res.data.data.list
        }
      })
    },
    // 后勤服务工作综合分析
    getServiceAnalysis() {
      getComprehensiveWorkOrderInfo().then((res) => {
        const data = res.data
        if (data.code === '200') {
          const arr = data.data.resultMap
          arr.completeRate = this.toPoint(arr.completeRate)
          arr.countOverTimesRate = this.toPoint(arr.countOverTimesRate)
          this.serviceAnalysis = data.data.resultMap
        } else {
          // this.$message({
          //   message: data.data.msg,
          //   type: 'warning'
          // })
        }
      })
    },
    GetTodayTaskAnalysis() {
      const param = {
        pageNo: 1,
        pageSize: 999,
        taskStartTime: moment().format('YYYY-MM-DD') + ' 00:00:00',
        taskEndTime: moment().format('YYYY-MM-DD') + ' 23:59:59'
      }
      getTodayTaskAnalysis(param).then((res) => {
        const data = res.data
        if (data.code === '200') {
          const { list, ...other } = data.data
          this.todayTaskAnalysis = list
          this.taskAnalysisStatistics = other
        } else {
          // this.$message({
          //   message: data.data.msg,
          //   type: 'warning'
          // })
        }
      })
    },
    getWorkOrderTableData() {
      getDynamicWorkOrderList().then((res) => {
        const data = res.data
        if (data.code === '200') {
          this.workOrderTableData = data.data.list
        } else {
          // this.$message({
          //   message: data.data.msg,
          //   type: 'warning'
          // })
        }
      })
    },
    // 后勤设备状态分析
    getMaintenance() {
      getMaintenance().then((res) => {
        const data = res.data
        if (data.code === '200') {
          Object.assign(this.maintenance, {
            notTenance: data.data.notTenance.notTenance,
            maintenance: data.data.maintenance.maintenance,
            lifeTime: data.data.lifeTime.lifeTime,
            inspection: data.data.hashMap.data.inspection.inspection,
            notMaintenance: data.data.hashMap.data.notMaintenance.notMaintenance
          })
          this.$nextTick(() => {
            const valueList = Object.values(this.maintenance)
            this.getAnalysisEcharts(valueList)
          })
        } else {
          // this.$message({
          //   message: data.message,
          //   type: 'warning'
          // })
        }
      })
    },
    toPoint(str) {
      var data = str.replace('%', '')
      return (data / 100).toFixed(2)
    },
    // 后勤设备状态分析Echarts
    getAnalysisEcharts(valueList) {
      const getchart = echarts.init(document.getElementById('analysisEcharts'))
      getchart.resize()
      const nameList = ['待维保', '维保到期', '寿命到期', '待巡检', '待维修']
      var data = []
      var color = ['rgba(31, 250, 255, 0.3)', 'rgba(255, 227, 166, 0.28)', 'rgba(147, 130, 255, 0.26)', 'rgba(0, 248, 114, 0.24)', 'rgba(46, 119, 251, 0.3)']
      var borderColor = ['rgba(31, 250, 255, 1)', 'rgba(255, 227, 166, 1)', 'rgba(147, 130, 255, 1)', 'rgba(0, 248, 114, 1)', 'rgba(46, 119, 251, 1)']
      for (var i = 0; i < nameList.length; i++) {
        data.push({
          value: valueList[i],
          name: nameList[i],
          itemStyle: {
            normal: {
              borderWidth: 1,
              shadowBlur: 200,
              borderColor: borderColor[i],
              color: color[i],
              label: {
                normal: { show: true }
              }
            }
          }
        })
      }
      var seriesObj = [
        {
          name: '',
          type: 'pie',
          clockWise: false,
          center: ['49%', '50%'],
          radius: ['55%', '80%'],
          hoverAnimation: true,
          label: {
            normal: {
              show: false,
              position: 'center',
              formatter: '{value|{c}}\n{label|{b}}',
              rich: {
                value: {
                  padding: 5,
                  align: 'center',
                  verticalAlign: 'middle',
                  fontSize: 20
                },
                label: {
                  align: 'center',
                  verticalAlign: 'middle',
                  fontSize: 12
                }
              }
            },
            emphasis: {
              show: true,
              textStyle: {
                fontSize: '12',
                color: '#fff'
              }
            }
          },
          data: data
        }
      ]
      const option = {
        backgroundColor: 'rgba(128, 128, 128, 0)',
        tooltip: {
          show: false
        },
        legend: {
          show: false
        },
        toolbox: {
          show: false
        },
        series: seriesObj
      }
      getchart.clear()
      getchart.setOption(option)
      // 随着屏幕大小调节图表
      window.addEventListener('resize', () => {
        getchart.resize()
      })
      getchart.dispatchAction({
        type: 'highlight',
        seriesIndex: 0,
        dataIndex: 0
      })
      getchart.on('mouseover', (e) => {
        // 取消默认高亮
        getchart.dispatchAction({
          type: 'downplay',
          seriesIndex: 0,
          dataIndex: 0
        })
      })
      // 鼠标移出后默认高亮
      getchart.on('mouseout', (e) => {
        getchart.dispatchAction({
          type: 'highlight',
          seriesIndex: 0,
          dataIndex: 0
        })
      })
    },
    caseScroll() {
      // 上下页操作
      if (this.selectedCase < 0 || this.currentCase.length <= 4) {
        this.selectedCase = 0
        $('.case-discuss-content:visible').animate({ left: -this.selectedCase * 13.28 + 'rem' }, 1000)
        return
      }
      if (this.selectedCase > this.currentCase.length - 5) {
        this.selectedCase = this.currentCase.length - 5
      }
      $('.case-discuss-content:visible').animate({ left: -this.selectedCase * 13.28 + 'rem' }, 1000)
    },
    nextCase() {
      // 下一页
      this.selectedCase++
      this.caseScroll()
    },
    lastCase() {
      // 上一页
      this.selectedCase--
      this.caseScroll()
    },
    jumpWorkOrder(type) {
      this.$router.push({ name: 'workOrderList', params: { type: type } })
    },
    // table 操作
    selectRowData(row, type) {
      if (type === 'detail') {
        this.$router.push({ path: '/workOrderDetail', query: { id: row.id } })
      } else if (type === 'cui') {
        this.$confirm('是否催促尽快完成此工单?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          cancelButtonClass: 'sino-button-sure',
          confirmButtonClass: 'sino-button-sure',
          customClass: 'confirm-box-class',
          type: 'warning'
        })
          .then(() => {
            // this.changeUserState(params)
            urgeWorkOder({ id: row.id }).then((res) => {
              if (res.data.code === '200') {
                this.$message({
                  type: 'success',
                  message: '催单成功！'
                })
                this.getWorkOrderTableData()
              } else {
                // this.$message({
                //   message: res.data.message,
                //   type: 'warning'
                // })
              }
            })
          })
          .catch(() => {})
      } else if (type === 'edit') {
        this.$router.push({ path: '/patrolInspectionDetails', query: { id: row.id, dataInfo: row } })
      }
    },
    // 初始化获取班次信息
    getShiftRecord() {
      const that = this
      // that.shiftRecord.endDate = '2022-06-22 16:47:00'
      getShiftRecord({}).then((res) => {
        const data = res.data
        if (data.code === '200') {
          this.shiftRecord = data.data[0]
          const lastDayData = data.data[0].lastDayData
          // 获取当前时间
          if (data.data[0].date) {
            that.currentTime = moment(data.data[0].date).format('YYYY-MM-DD HH:mm:ss')
            // 定时器
            this.currentTimer = setInterval(() => {
              const timeFormat = 'YYYY-MM-DD hh:mm:ss'
              that.currentTime = moment(that.currentTime).add(1, 'second').format('YYYY-MM-DD HH:mm:ss')
              const isAvailable = moment(that.currentTime, timeFormat).isBetween(moment(that.shiftRecord.createDate + ':00', timeFormat), moment(this.shiftRecord.endDate + ':00', timeFormat))
              // const isTimeEvel = moment(that.currentTime, timeFormat).isBetween(moment(this.shiftRecord.endDate + ':00', timeFormat), moment(this.shiftRecord.endDate + ':00', timeFormat).add(1, 'day'))
              // 判断当前时间是否在班次时间内 如果不在班次时间内 则清除定时器 并且更新班次信息
              if (!isAvailable) {
                clearInterval(that.currentTimer)
                that.currentTimer = null
                that.getShiftRecord()
              }
              // const timeDiff = moment('2022-06-22 16:47:50').diff(moment(that.currentTime), 'seconds')
              const timeDiff = moment(that.shiftRecord.endDate + ':00').diff(moment(that.currentTime), 'seconds')
              if (timeDiff <= 600 && timeDiff > 0) {
                that.countDown = moment.utc(timeDiff * 1000).format('mm:ss')
              } else {
                that.countDown = 0
              }
            }, 1000)
          }
          // 获取上个班次
          if (lastDayData) {
            this.dutyRecord[0].plan = moment(lastDayData.signInTime).format('YYYY-MM-DD HH:mm:ss') || '暂无记录'
            this.dutyRecord[1].plan = lastDayData.remainingProblems || '暂无记录'
            this.dutyRecord[2].plan = lastDayData.dutySituation || '暂无记录'
          }
        } else {
          // this.$message({
          //   message: data.message,
          //   type: 'warning'
          // })
        }
      })
    },
    // 值班打卡
    dutyEvent() {
      this.dialogVisibleSignIn = true
    },
    // ----------------------------签到弹窗操作
    closeDialogSignIn() {
      this.dialogVisibleSignIn = false
    },
    sureSignIn() {
      this.dialogVisibleSignIn = false
      this.getShiftRecord()
    },
    // 交接班
    shiftsEvent() {
      if (!this.shiftRecord.theDayData) {
        return this.$message({
          message: '请值班打卡后再进行交接班！',
          type: 'warning'
        })
      }
      this.dialogVisibleShifts = true
    },
    sureShifts() {
      this.dialogVisibleShifts = false
      this.getShiftRecord()
    },
    closeDialogShifts() {
      this.dialogVisibleShifts = false
    }
  }
}
</script>
<style lang="scss" scoped>
.content {
  position: relative;
  // background-color: #031553;
  background: url('~@/assets/images/qhdsys/bj.png') no-repeat;

  box-sizing: border-box;
  width: 100%;
  height: 100%;
  padding: 20px 10px 10px 10px;
  display: flex;
  justify-content: space-between;
  .left-content,
  .right-content {
    width: 50%;
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    flex: 1;
  }
  .left-content {
    padding-right: 5px;
    .left-top {
      width: 100%;
      height: calc(24% - 5px);
      background: url('~@/assets/images/qhdsys/bg-24.png') no-repeat;
      background-size: 100% 100%;
      .case-discuss {
        overflow: hidden;
        position: relative;
        width: calc(100% - 2.875rem);
        height: 100%;
        margin: 0 auto;
      }

      .case-discuss-content {
        position: absolute;
        top: 0;
        left: 0;
        display: flex;
        height: 100%;
      }

      .discuss-conent-children {
        display: flex;
        box-sizing: border-box;
        width: 13.28rem;
        margin: 0 8px;
        padding: 14px 0;
        height: 100%;
      }
      .case-img {
        width: 35%;
        height: 90%;
        margin-top: 5%;
        position: relative;
        img {
          width: 100%;
          height: 100%;
        }
        .case-num {
          position: absolute;
          width: 4rem;
          text-align: center;
          top: 30%;
          color: #fff;
          font-size: 1.25rem;
          font-weight: bold;
          span {
            font-weight: 400;
            font-size: 0.75rem;
          }
        }
        // .case-anim-icon {
        //   position: absolute;
        //   bottom: 10%;
        //   left: calc(50% - 1rem);
        //   width: 2rem;
        //   height: 2rem;
        //   background: url('~@/assets/images/peace/icon-people.png') no-repeat;
        //   background-size: 100% 100%;
        //   animation: jump 1s ease-out infinite alternate-reverse;
        // }
      }
      .case-tit {
        width: 65%;
        padding-left: 0.8rem;
        box-sizing: border-box;
      }
      .case-tit > p:nth-child(1) {
        margin-top: 53%;
        margin-bottom: 12px;
        font-size: 0.875rem;
        color: #7eaef9;
      }
      .case-tit > p:nth-child(2) {
        font-size: 1rem;
        color: #fff;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
        font-family: DIN;
      }
      .case-control-btn {
        position: absolute;
        width: 1.25rem;
        height: 50px;
        line-height: 50px;
        text-align: center;
        top: 45%;
        font-size: 24px;
        color: #5996f9;
        margin-top: (calc(-1 * (150px - 100px) / 2));
        cursor: pointer;
        background-color: #163274;
        color: #5996f9;
        i {
          margin: auto;
        }
        &.case-control-l {
          left: 5px;
          // background: url(~@/assets/images/peace/btn-left.png) no-repeat;
          // background-size: 100% 100%;
        }

        &.case-control-r {
          right: 5px;
          // background: url(~@/assets/images/peace/btn-right.png) no-repeat;
          // background-size: 100% 100%;
        }
      }
      .case-control-btn:hover {
        // color: #ffe3a6;
        background-color: #373e5f;
        color: #eed6a0;
      }
    }
    .left-center {
      width: 100%;
      height: calc(32% - 5px);
      background: url('~@/assets/images/qhdsys/bg-32.png') no-repeat;
      background-size: 100% 100%;
      .analysis-order {
        width: 7rem;
        height: 7rem;
        margin: auto 0.5rem;
        background: url('~@/assets/images/peace/people-left-center-order.png') no-repeat;
        background-size: 100% 100%;
      }
      .analysis-box {
        width: calc(100% - 8rem);
        height: 100%;
        padding: 0 1rem;
        box-sizing: border-box;
        display: flex;
        flex-direction: column;
        justify-content: space-evenly;
        .analysis-box-top,
        .analysis-box-bottom {
          height: calc(40% - 5px);
          display: flex;
          justify-content: space-between;
        }
        .day-order,
        .month-order,
        .die-order {
          width: 20.5%;
          height: 100%;
          display: flex;
          flex-direction: column;
          justify-content: space-evenly;
          cursor: pointer;
          :first-child {
            font-size: 0.875rem;
            color: #7eaef9;
            text-align: center;
          }
          :last-child {
            font-size: 1.25rem;
            color: #fff;
            text-align: center;
            span {
              font-size: 0.75rem;
            }
          }
        }
        .day-analysis,
        .out-time-order,
        .month-average-order,
        .month-analysis {
          width: 32%;
          height: 100%;
        }
        .day-order,
        .month-order,
        .die-order,
        .day-analysis,
        .out-time-order,
        .month-average-order,
        .month-analysis {
          background: url('~@/assets/images/peace/people-left-center.png') no-repeat;
          background-size: 100% 100%;
          padding: 0.3125rem;
          box-sizing: border-box;
        }
        .day-analysis,
        .out-time-order {
          display: flex;
          justify-content: space-around;
        }
        .day-analysis-left {
          width: 60%;
          display: flex;
          flex-direction: column;
          justify-content: space-evenly;
          padding: 0 0.5rem 0 1.2rem;
          box-sizing: border-box;
          p {
            display: flex;
            justify-content: space-between;
            font-size: 0.875rem;
            :first-child {
              color: #7eaef9;
              text-align: center;
            }
            :last-child {
              color: #fff;
              text-align: center;
              font-weight: bold;
            }
          }
        }
        .day-analysis-right,
        .out-time-order-right {
          width: 40%;
          display: flex;
          justify-content: space-around;
          flex-direction: column;
          p {
            text-align: center;
            color: #fff;
            font-size: 0.75rem;
          }
        }
        .out-time-order-left {
          width: 60%;
          display: flex;
          flex-direction: column;
          justify-content: space-evenly;
          padding: 0 0.5rem 0 1.2rem;
          box-sizing: border-box;
          p {
            text-align: center;
            font-size: 0.875rem;
          }
          :first-child {
            color: #fff;
            font-size: 1.25rem;
            span {
              font-size: 0.75rem;
              color: #fff;
            }
          }
          :last-child {
            color: #7eaef9;
          }
        }
        .month-average-order {
          display: flex;
          flex-direction: column;
          justify-content: space-evenly;
          padding: 0 0.5rem 0 1.2rem;
          box-sizing: border-box;
          p {
            text-align: center;
            font-size: 0.875rem;
          }
          :first-child {
            color: #fff;
            font-size: 1.25rem;
            span {
              font-size: 0.75rem;
              color: #fff;
            }
          }
          :last-child {
            color: #7eaef9;
          }
        }
        .month-analysis {
          display: flex;
          justify-content: space-around;
          // flex-direction: column;
          // justify-content: space-evenly;
          // padding: 0 0.5rem 0 1.2rem;
          // box-sizing: border-box;
          .month-analysis-left,
          .month-analysis-right {
            display: flex;
            flex-direction: column;
            justify-content: space-evenly;
            padding: 0 0.5rem 0 1.2rem;
            box-sizing: border-box;
            p {
              text-align: center;
              font-size: 0.875rem;
            }
            :first-child {
              color: #fff;
              font-size: 1.25rem;
              span {
                font-size: 0.75rem;
                color: #fff;
              }
            }
            :last-child {
              color: #7eaef9;
            }
          }
        }
      }
    }
    .left-bottom {
      width: 100%;
      height: calc(43% - 5px);
      background: url('~@/assets/images/qhdsys/bg-43.png') no-repeat;
      background-size: 100% 100%;
    }
  }
  .right-content {
    padding-left: 5px;
    .right-top {
      width: 100%;
      height: calc(30% - 5px);
      background: url('~@/assets/images/qhdsys/bg-30.png') no-repeat;
      background-size: 100% 100%;
      .equiue-state-analysis-left {
        width: 25%;
        height: 100%;
        display: flex;
        .cricle-analysis {
          width: 12rem;
          height: 12rem;
          margin: auto;
          background: url('~@/assets/images/peace/people-right-top-bg.png') no-repeat;
          background-size: 100% 100%;
          display: flex;
          #analysisEcharts {
            width: 80%;
            height: 80%;
            // padding-bottom: 78%;
            margin: auto;
          }
        }
      }
      .equiue-state-analysis-right {
        width: 75%;
        height: 100%;
        display: flex;
        flex-direction: column;
        justify-content: space-around;
        .analysis-right-top,
        .analysis-right-right {
          height: 50%;
          display: flex;
          .order-box {
            width: 30%;
            height: 100%;
            // padding: 5%;
            box-sizing: border-box;
            display: flex;
            .order-bg {
              margin: auto 0;
              width: 30%;
              height: 0;
              padding-bottom: 30%;
              background: url('~@/assets/images/peace/people-right-top-order.png') no-repeat;
              background-size: 100% 100%;
            }
            .order-content {
              margin: auto 0.8rem;
              // width: 30%;
              height: 70%;
              // padding-bottom: 30%;
              display: flex;
              flex-direction: column;
              justify-content: space-around;
              :first-child {
                font-size: 1.25rem;
                color: #fff;
                text-align: center;
              }
              :last-child {
                font-size: 0.875rem;
                color: #7eaef9;
                text-align: center;
              }
            }
          }
        }
      }
    }
    .right-center {
      width: 100%;
      height: calc(38% - 5px);
      background: url('~@/assets/images/qhdsys/bg-38.png') no-repeat;
      background-size: 100% 100%;
      .order-dynamic {
        height: 3rem;
        width: 70%;
        margin-left: 2%;
        background: url('~@/assets/images/peace/people-right-center-dynamic.png') no-repeat;
        background-size: 100% 100%;
        box-sizing: border-box;
        padding-left: 8%;
        .order-dynamic-num {
          width: 65%;
          height: 100%;
          display: flex;
          justify-content: space-around;
          color: #fff;
          .dynamic-num-box:first-child {
            color: #fee2a6;
          }
          .dynamic-num-box:last-child {
            color: #f95354;
          }
          .dynamic-num-box {
            display: flex;
            flex-direction: column;
            justify-content: space-around;
            text-align: center;
            :first-child {
              font-size: 1.1875rem;
            }
            :last-child {
              font-size: 0.8125rem;
            }
          }
        }
      }
    }
    .right-bottom {
      width: 100%;
      height: calc(32% - 5px);
      background: url('~@/assets/images/qhdsys/bg-32.png') no-repeat;
      background-size: 100% 100%;
      .duty-record-after {
        width: 60%;
        height: 100%;
        padding: 20px 10px 0 10px;
        box-sizing: border-box;
        overflow-y: scroll;
        ::v-deep .el-timeline-item {
          padding-bottom: 0.5rem;
        }
        ::v-deep .el-timeline-item__tail {
          border-left: 0.0625rem solid #3d425a;
        }
        ::v-deep .el-timeline-item__node--normal {
          left: -0.015rem;
          width: 0.5rem;
          height: 0.5rem;
        }
        ::v-deep .el-timeline-item__icon {
          color: #e6cf9d;
          font-size: 1rem;
        }
        ::v-deep .el-timeline-item__node {
          background: #e6cf9d;
        }
        .timestamp-row {
          padding: 2px;
          display: flex;
          font-family: PingFang SC;
          :first-child {
            color: #d0bd95;
            font-size: 0.875rem;
            margin-right: 1.25rem;
            white-space: nowrap;
          }
          :last-child {
            color: #a0a6bc;
            font-size: 0.875rem;
          }
        }
      }
      .duty-record-now {
        width: 40%;
        height: 100%;
        padding: 10px 10px 20px 3rem;
        box-sizing: border-box;
        text-align: left;
        display: flex;
        justify-content: space-around;
        flex-direction: column;
        .now-time {
          font-size: 0.875rem;
          color: #e8d09e;
        }
        .time-slot {
          height: 4rem;
          display: flex;
          justify-content: space-around;
          flex-direction: column;
          font-size: 0.875rem;
          color: #b6bbcb;
          padding-left: 2rem;
          position: relative;
          &::after {
            content: '';
            position: absolute;
            width: 1.5rem;
            height: 4rem;
            top: 0;
            left: -0.2rem;
            background: url('~@/assets/images/peace/duty-time-solt.png') no-repeat;
            background-size: 100% 100%;
          }
          span {
            padding-left: 0.625rem;
            font-size: 1rem;
            font-family: PingFang SC;
            font-weight: 500;
            color: #ffffff;
          }
        }
        .duty-btn {
          display: flex;
        }
        .duty-now-btn {
          width: 7rem;
          height: 4rem;
          text-align: center;
          // align-items: center;
          line-height: 2.65rem;
          padding: 0.625rem 0;
          box-sizing: border-box;
          color: #fff;
          cursor: pointer;
          display: flex;
          flex-direction: column;
          justify-content: center;
          background: url('~@/assets/images/peace/duty-btn.png') no-repeat;
          background-size: 100% 100%;
          &:hover,
          &:focus {
            color: #dbc69a;
            background: url('~@/assets/images/peace/duty-btn-hover.png') no-repeat;
            background-size: 100% 100%;
          }
          p {
            height: 1.375rem;
            line-height: 1.375rem;
          }
        }
        .duty-now-btn-disable {
          cursor: no-drop;
          &:hover,
          &:focus {
            color: #fff;
            background: url('~@/assets/images/peace/duty-btn.png') no-repeat;
            background-size: 100% 100%;
          }
        }
      }
    }
  }
  .bg-title {
    // margin-top: 5px;
    height: 2.5rem;
    line-height: 2.5rem;
    color: #d4e3f9;
    padding-left: 3rem;
    font-family: TRENDS;
  }
  .bg-content {
    position: relative;
    box-sizing: border-box;
    padding: 10px 10px 15px 10px;
    width: 100%;
    height: calc(100% - 2.5rem);
  }
  ::v-deep div.el-table__fixed-body-wrapper {
    background: center;
  }
  ::v-deep .el-table {
    border: none !important;
  }
  // ::v-deep .el-table--enable-row-hover .el-table__body tr:hover {
  //   box-sizing: border-box;
  //   td.el-table__cell {
  //     background-color: #343c62 !important;
  //     border-top: 1px solid #ffe3a6;
  //     border-bottom: 1px solid #ffe3a6;
  //   }
  // }
}
::v-deep .el-table--striped .el-table__body tr.el-table__row--striped td {
  background-color: rgba(168,172,171,0.08) !important; /* def2ff f2faff */
}
</style>
