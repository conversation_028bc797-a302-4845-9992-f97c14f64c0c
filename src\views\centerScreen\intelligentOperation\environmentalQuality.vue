<template>
  <div class="environmentalQuality">
    <div class="right-content">
      <div class="top_title">
        <span class="dialog-title">空间清单</span>
      </div>
      <div class="top_type">
        <div
          v-for="(item, index) in typeItemList"
          :key="index"
          class="top_type_item"
          @click="changeType(item.type)"
          :class="{ activeType: activeType === item.type, disableType: !hasSelect.includes(item.type) }"
        >
          <span>{{ item.name }}</span>
        </div>
      </div>
      <div class="bottom_List">
        <template v-if="activeType === 'WenDu'">
          <div v-for="(item, index) in locatonList" :key="index" class="item_location" :class="index === 2 && areaBoxShow ? 'item_location_active': ''">
            <i></i><span class="item_location_name">{{ item.locationName }}</span
            ><span class="item_location_val" v-for="(val, idx) in item.value" :key="idx" :style="getColorByVal(val)">{{ val }}{{ item.unit }}{{ idx + 1 !== item.value.length ? '，' : '' }}</span>
          </div>
        </template>
        <template v-if="activeType === 'ZhaoMing'">
          <div v-for="(item, index) in locatonLightList" :key="index" class="item_lighting_location">
            <div>
              <i></i><span class="item_location_name">{{ item.locationName }}</span>
            </div>
            <div class="item_location_val">
              <div>回路数：{{ item.total }}</div>
              <div>
                开启：<span style="color: #7bffc1">{{ item.open }}</span
                ><span v-if="item.total > 1">/{{ item.total }}</span>
              </div>
            </div>
          </div>
        </template>
      </div>
    </div>
    <div class="bottom_table" v-if="areaBoxShow">
      <div class="table_left">
        <div class="top_title">
          <span class="dialog-title">服务于空间的设备</span>
        </div>
        <div class="table_left_content">
          <el-table
            class="table_list"
            :data="tableData"
            height="100%"
            :cell-style="$tools.setCell(3)"
            :header-cell-style="$tools.setHeaderCell(3)"
            style="width: 100%"
            v-loading="tableLoading"
            element-loading-background="rgba(0, 0, 0, 0.2)"
          >
            <el-table-column fixed prop="quipmentName" align="center" show-overflow-tooltip label="设备名称"></el-table-column>
            <el-table-column fixed prop="quipmentType" align="center" show-overflow-tooltip label="设备类型"></el-table-column>
            <el-table-column fixed prop="operationData" align="center" show-overflow-tooltip label="运行数据"></el-table-column>
            <el-table-column fixed show-overflow-tooltip align="center" label="操作">
              <template slot-scope="scope">
                <el-switch
                  v-if="scope.row.quipmentType === '照明回路'"
                  v-model="scope.row.lightingState"
                  active-color="#FFE3A6"
                  inactive-color="#2C385A"
                  :active-value="0"
                  :inactive-value="1"
                  @change="equStatusChange($event, scope.row.id)"
                >
                </el-switch>
                <div v-else class="operationBtn">
                  <span @click="selectConfigRowData(scope.row)">调整</span>
                </div>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>
      <div class="table_right">
        <div class="top_title">
          <span class="dialog-title">空间明细</span>
        </div>
        <div class="table_right_content">
          <div>空间ID：SP2300098</div>
          <div>空间名称：5楼西侧</div>
          <div>温度：16.2°C</div>
          <div>湿度：55%</div>
          <div>PM2.5：45mg/m³</div>
        </div>
      </div>
    </div>
    <!-- 调控弹窗 -->
    <template v-if="regDialogShow">
      <regulation ref="regulation" :rowData="rowData" :regDialogShow="regDialogShow" @sure="submitDialog" @closeDialog="closeDialog"></regulation>
    </template>
  </div>
</template>
<script>
import regulation from './components/regulation.vue'
export default {
  name: 'environmentalQuality',
  components: {
    regulation
  },
  data() {
    return {
      activeType: 'WenDu',
      tableData: [
        {
          id: 1,
          quipmentName: '送风机组001',
          quipmentType: '送风机组',
          operationData: '送风温度：20℃， 送风湿度：25%'
        },
        {
          id: 2,
          quipmentName: '东侧大厅照明',
          quipmentType: '照明回路',
          operationData: '开关状态：开启',
          lightingState: 0
        },
        {
          id: 3,
          quipmentName: '西侧大厅照明',
          quipmentType: '照明回路',
          operationData: '开关状态：开启',
          lightingState: 1
        },
        {
          id: 4,
          quipmentName: '南侧大厅照明',
          quipmentType: '照明回路',
          operationData: '开关状态：开启',
          lightingState: 0
        },
        {
          id: 5,
          quipmentName: '北侧大厅照明',
          quipmentType: '照明回路',
          operationData: '开关状态：开启',
          lightingState: 1
        }
      ],
      tableLoading: false,
      typeItemList: [
        {
          type: 'WenDu',
          name: '温度'
        },
        {
          type: '2',
          name: '湿度'
        },
        {
          type: '3',
          name: 'CO2浓度'
        },
        {
          type: '4',
          name: 'CO浓度'
        },
        {
          type: '5',
          name: 'O2浓度'
        },
        {
          type: 'ZhaoMing',
          name: '照明'
        },
        {
          type: '7',
          name: '烟感状态'
        },
        {
          type: '8',
          name: '漏水状态'
        }
      ],
      hasSelect: ['WenDu', 'ZhaoMing'],
      locatonList: [
        {
          locationName: '5F西侧',
          value: ['19', '16'],
          unit: '℃'
        },
        {
          locationName: '5F302室内',
          value: ['16'],
          unit: '℃'
        },
        {
          locationName: '5F304室',
          value: ['15'],
          unit: '℃'
        },
        {
          locationName: '5F309室',
          value: ['15'],
          unit: '℃'
        },
        {
          locationName: '5F西东侧',
          value: ['19'],
          unit: '℃'
        },
        {
          locationName: '5F408室',
          value: ['16'],
          unit: '℃'
        }
      ],
      locatonLightList: [
        {
          locationName: '5F西侧',
          total: 2,
          open: 2
        },
        {
          locationName: '5F东侧',
          total: 2,
          open: 1
        },
        {
          locationName: '5F南侧',
          total: 1,
          open: 1
        },
        {
          locationName: '5F北侧',
          total: 1,
          open: 0
        }
      ],
      regDialogShow: false, // 调控弹窗
      rowData: {}, // 选中调控的数据
      areaBoxShow: false // 区域弹窗
    }
  },
  created() {},
  mounted() {
    try {
      window.chrome.webview.addEventListener('message', (event) => {
        const data = JSON.parse(event.data)
        if (data.type === 'hjzl') {
          this.areaBoxShow = true
        }
        // if (data.type === 'tag') {
        //   this.workOrderListChange()
        // }
      })
    } catch (error) {}
    this.setWPFSelectType()
  },
  methods: {
    changeType(type) {
      if (!this.hasSelect.includes(type)) return
      this.activeType = type
      this.setWPFSelectType()
    },
    // 传递选中类型
    setWPFSelectType() {
      try {
        window.chrome.webview.hostObjects.sync.bridge.GetEnvironmentSwitch(this.activeType)
      } catch (error) {}
    },
    // 温度颜色赋值
    getColorByVal(val) {
      let color = ''
      if (val > 28) {
        color = 'rgba(255,84,84,'
      } else if (val > 26) {
        color = 'rgba(255,157,96,'
      } else if (val > 21) {
        color = 'rgba(123,255,193,'
      } else if (val > 14) {
        color = 'rgba(122,205,255,'
      } else {
        color = 'rgba(116,118,255,'
      }
      return {
        color: color + '1)'
        // backgroundColor: color + '0.22)',
        // borderColor: color + '1)'
      }
    },
    // 照明开关切换
    equStatusChange(event, id) {
    },
    // 设备调整
    selectConfigRowData(val) {
      this.rowData = val
      this.regDialogShow = true
      window.chrome.webview.hostObjects.sync.bridge.ShowDialog(true)
    },
    // 调控弹窗提交
    submitDialog() {
      this.closeDialog()
      this.tableLoading = true
      setTimeout(() => {
        this.tableLoading = false
      }, 1500)
    },
    // 调控弹窗关闭
    closeDialog() {
      this.regDialogShow = false
      try {
        window.chrome.webview.hostObjects.sync.bridge.ShowDialog(false)
      } catch (error) {}
    }
  }
}
</script>
<style lang="scss" scoped>
.environmentalQuality {
  width: 100%;
  height: 100%;
  position: relative;
  .right-content {
    position: absolute;
    top: 0;
    right: 0;
    width: 20%;
    margin-top: 2%;
    // height: auto;
    height: 96%;
    background: url('~@/assets/images/center/mask-short.png') no-repeat;
    background-size: 100% 100%;
    padding: 2px;
    box-sizing: border-box;
    padding: 10px 10px 20px 10px;
    .top_type {
      width: calc(100% - 20px);
      padding: 15px 0;
      // height: 4.75rem;
      margin: 0 auto;
      background-color: rgba(1, 11, 59, 0.5);
      display: flex;
      // justify-content: space-around;
      flex-wrap: wrap;
      .top_type_item {
        width: 29%;
        height: 2.5rem;
        line-height: 2.5rem;
        margin: 3px 2%;
        text-align: center;
        font-size: 14px;
        font-family: PingFang-SC-Medium, PingFang-SC;
        color: #93bdff;
        background: url('~@/assets/images/center/block-bg.png') no-repeat;
        background-size: 100% 100%;
        cursor: pointer;
      }
      .activeType {
        background: url('~@/assets/images/center/block-bg-active.png') no-repeat;
        background-size: 100% 100%;
        color: #ffe3a6;
      }
      .disableType {
        background: url('~@/assets/images/center/block-bg-disable.png') no-repeat;
        background-size: 100% 100%;
        color: #4c65a0;
      }
    }
    .bottom_List {
      max-height: calc(90vh - 13rem);
      // overflow-y: scroll;
      .item_location,
      .item_lighting_location {
        font-size: 14px;
        font-family: PingFang-SC-Medium, PingFang-SC;
        i {
          display: inline-block;
          width: 1rem;
          height: 1rem;
          background: url('~@/assets/images/center/location.png') no-repeat;
          background-size: 100% 100%;
        }
        .item_location_name {
          color: #ffffff;
          margin: 0 0.9375rem 0 0.625rem;
        }
      }
      .item_location {
        padding: 0.525rem;
        height: 1rem;
        line-height: 1rem;
        margin: 0.625rem 0;
      }
      .item_location_active {
        background: #343C62;
        border-radius: 10px;
      }
      .item_lighting_location {
        height: 3rem;
        padding: 0.325rem;
        display: flex;
        flex-direction: column;
        justify-content: space-around;
        i {
          vertical-align: bottom;
        }
        > div {
          height: 1rem;
          line-height: 1rem;
          font-size: 14px;
        }
        .item_location_val {
          padding: 0 2rem;
          display: flex;
          > div {
            width: 50%;
            font-size: 14px;
            font-family: PingFang-SC-Medium, PingFang-SC;
            color: #ffe3a6;
          }
        }
      }
    }
  }
  .bottom_table {
    position: absolute;
    bottom: 0;
    right: 20.5%;
    width: 51%;
    height: 30%;
    background: url('~@/assets/images/center/mask-bottom-bg.png') no-repeat;
    background-size: 100% 100%;
    padding: 2px;
    box-sizing: border-box;
    padding: 10px;
    display: flex;
    .table_left {
      flex: 1;
      ::v-deep .el-switch {
        height: 1rem;
        .el-switch__core {
          height: 1rem;
        }
        .el-switch__core:after {
          width: 20px;
          height: 20px;
          top: -3px;
          left: -4px;
          // box-shadow: 0px 2px 7px 0px #a4a4b7;
        }
      }
      ::v-deep .el-switch.is-checked {
        .el-switch__core::after {
          left: 100% !important;
        }
      }
    }
    .table_left_content {
      width: 100%;
      height: calc(100% - 2.5rem);
      ::v-deep .el-table {
        tr {
          background: center;
        }
        .el-table__header thead th {
          background: rgba(46, 73, 137, 0.1) !important;
        }
        div.el-table__fixed-body-wrapper,
        div.el-table__body-wrapper {
          background: rgba(2, 19, 82, 0.2);
        }
      }
    }
    .table_right {
      width: 31%;
      .table_right_content {
        padding: 10px;
        box-sizing: border-box;
        font-size: 14px;
        font-family: PingFang-SC-Medium, PingFang-SC;
        color: #ffffff;
        > div {
          padding: 6px 10px;
        }
      }
    }
  }
  .top_title {
    height: 2.5rem;
    line-height: 2.5rem;
    padding-left: 0.9375rem;
    .dialog-title {
      color: #fff;
      font-family: PingFangSC-Medium, PingFang SC;
    }

    .dialog-title::before {
      content: '';
      display: inline-block;
      width: 2px;
      height: 16px;
      background: #ffe3a6;
      margin-right: 10px;
      vertical-align: middle;
    }
  }
}
</style>
<!-- <style scoped>
::-webkit-scrollbar-track {
  background: center;
}
</style> -->
