<template>
  <div class="list-index-view">
    <div class="view-header-box">
      <div class="header-left">
        <el-form :model="searchForm" class="search-form" inline ref="formRef">
          <el-form-item>
            <el-input v-model="searchForm.input" placeholder="租赁编码" size="small"></el-input>
          </el-form-item>
          <el-form-item>
            <el-select v-model="searchForm.dept" placeholder="租赁科室" size="small">
              <el-option v-for="item in deptOptions" :key="item.value" :label="item.label" :value="item.value">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-select v-model="searchForm.state" placeholder="执行状态" size="small">
              <el-option v-for="item in stateOptions" :key="item.value" :label="item.label" :value="item.value">
              </el-option>
            </el-select>
          </el-form-item>
        </el-form>
      </div>
      <div class="header-right">
        <el-button class="sino-button-sure" @click="resetForm">重置</el-button>
        <el-button class="sino-button-sure" @click="handleSearchForm">查询</el-button>
      </div>
    </div>
    <div class="view-content" style="height: calc(100% - 4rem);">
      <div class="table-view">
        <el-table ref="table" v-loading="tableLoading" :data="tableData" :resizable="false" height="calc(100% - 50px)"
          :cell-style="$tools.setCell(3)" :header-cell-style="$tools.setHeaderCell(3)" stripe
          @selection-change="handleSelectionChange">
          <el-table-column type="selection" width="55" align="center" class-name="EmergencyDataTable__cell--selection">
          </el-table-column>
          <el-table-column prop="code" label="租赁单编码" show-overflow-tooltip></el-table-column>
          <el-table-column prop="way" label="租赁方式" show-overflow-tooltip></el-table-column>
          <el-table-column prop="state" label="执行状态" show-overflow-tooltip>
            <template slot-scope="scope">
              <div class="status-box">
                <img class="table-icon" :src='icon_5' />
                <span style="color:#61E29D">{{scope.row.state}}</span>
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="dept" label="租赁科室" show-overflow-tooltip></el-table-column>
          <el-table-column prop="time" label="租赁时间" show-overflow-tooltip></el-table-column>
          <el-table-column prop="returnTime" label="预计归还时间" show-overflow-tooltip></el-table-column>
          <el-table-column prop="money" label="预计租赁金额" show-overflow-tooltip></el-table-column>
          <el-table-column prop="assetsCount" label="资产数量" show-overflow-tooltip></el-table-column>
          <el-table-column label="操作" width="140px">
            <template slot-scope="scope">
              <span class="operation-span">审核</span>
              <span class="operation-span" @click="operating('detail', scope.row)">详情</span>
            </template>
          </el-table-column>
        </el-table>
        <el-pagination :current-page="currentPage" :page-sizes="[15, 20, 30, 40]" :page-size="pageSize"
          layout="total, sizes, prev, pager, next, jumper" :total="total" class="pagination"
          @size-change="handleSizeChange" @current-change="handleCurrentChange"></el-pagination>
      </div>
    </div>
  </div>
</template>
  <script>
import icon_5 from '@/assets/images/icon-5.png'
export default {
  name: 'leaseList',
  components: {
  },
  data() {
    return {
      icon_5,
      tableLoading: false,
      searchForm: {
        input: "",
        dept: "",
        state: '',
      },
      deptOptions: [],
      stateOptions: [],
      navIdx: 0,
      currentPage: 1,
      pageSize: 15,
      total: 5,
      tableData: [],
      multipleSelection: []
    };
  },
  mounted() {
    this.getDataList()
  },
  methods: {
    getDataList() {
      this.tableData = [
        {
          code: 'JYD202400001',
          way: '自提',
          state: '已完成',
          time: '2024-10-14 01:22:03',
          dept: '测试部门',
          returnTime: '2024-10-23 02:36:52',
          money: '2002',
          assetsCount: '2'
        }, {
          code: 'JYD202400001',
          way: '自提',
          state: '已完成',
          time: '2024-10-14 01:22:03',
          dept: '测试部门',
          returnTime: '2024-10-23 02:36:52',
          money: '2002',
          assetsCount: '5'
        }, {
          code: 'JYD202400001',
          way: '自提',
          state: '已完成',
          time: '2024-10-14 01:22:03',
          dept: '测试部门1',
          returnTime: '2024-10-23 02:36:52',
          money: '7989',
          assetsCount: '2'
        }, {
          code: 'JYD202400001',
          way: '自提',
          state: '已完成',
          time: '2024-10-14 01:22:03',
          dept: '测试部门2',
          returnTime: '2024-10-23 02:36:52',
          money: '4563',
          assetsCount: '1'
        }, {
          code: 'JYD202400001',
          way: '自提',
          state: '已完成',
          time: '2024-10-14 01:22:03',
          dept: '护理部',
          returnTime: '2024-10-23 02:36:52',
          money: '1563',
          assetsCount: '4'
        }
      ]
    },
    /** 重置 */
    resetForm() {
      this.$refs.formRef.resetFields()
      this.getDataList()
    },
    /** 查询 */
    handleSearchForm() {
      this.getDataList()
    },
    /** 条数变化事件 */
    handleSizeChange(val) {
      this.pageSize = val
      this.getDataList()
    },
    /** 页数变化事件 */
    handleCurrentChange(val) {
      this.currentPage = val
      this.getDataList()
    },
    /** 多选table */
    handleSelectionChange(val) {
      this.multipleSelection = val;
    },
    /** 跳转详情 */
    operating(type, row) {
      switch (type) {
        case 'detail':
          this.$emit('openDetailComponent', 'leaseDetail')
          break;
        default:
          break;
      }
    }
  },
};
  </script>
  <style lang="scss" scoped>
.list-index-view {
  position: relative;
  height: 100%;
  padding: 0px 40px;
}
.view-header-box {
  display: flex;
  justify-content: space-between;
}
.view-content {
  height: calc(100% - 4rem);
  .table-view {
    height: 100%;
  }
}
.operation-span {
  cursor: pointer;
  color: #8bddf5;
}
.operation-span:nth-child(1) {
  margin-right: 10px;
  color: rgba(133, 145, 206, 0.5);
}
::v-deep
  .el-table--striped
  .el-table__body
  tr.el-table__row--striped
  td.el-table__cell {
  background: none;
}
.table-icon {
  width: 16px;
  margin-right: 3px;
}
.status-box {
  display: flex;
  align-items: center;
}
</style>
