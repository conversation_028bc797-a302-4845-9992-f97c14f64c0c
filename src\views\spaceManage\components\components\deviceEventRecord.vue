<template>
  <div class="deviceEventRecord">
    <div v-if="eventsTypeList.length" class="deviceEventRecord-left">
      <div v-for="(item, index) in eventsTypeList" :key="item.id" class="left-item" :class="{'item-active': activeItem == index}" @click="eventsTypeChange(index)">
        {{ item.metadataName }}
      </div>
    </div>
    <div class="deviceEventRecord-right">
      <div class="table-main">
        <el-table
          v-loading="tableLoading"
          :data="tableData"
          height="calc(100%)"
          :cell-style="$tools.setCell(3)"
          :header-cell-style="$tools.setHeaderCell(3)"
          style="width: 100%"
          element-loading-background="rgba(0, 0, 0, 0.2)"
        >
          <el-table-column v-for="item in tableColumn" :key="item.id" width="150" :prop="item.id" :label="item.name" show-overflow-tooltip>
            <template slot-scope="scope">
              <span v-if="item.valueType">
                <span v-if="item.valueType.type === 'enum'">
                  {{ item.valueType.elements.find(el => el.value === String(scope.row[item.id]))?.text }}
                </span>
                <span v-else-if="item.valueType.type === 'boolean'">
                  {{ scope.row[item.id] == 'true' ? item.valueType.trueText : item.valueType.falseText }}
                </span>
                <span v-else>
                  {{ scope.row[item.id] }}
                </span>
              </span>
              <span v-else>
                {{ scope.row[item.id] }}
              </span>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <el-pagination
        v-if="tableColumn"
        :current-page="currentPage"
        :page-sizes="[15, 20, 30, 40]"
        :page-size="pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
        class="pagination"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      ></el-pagination>
    </div>
  </div>
</template>

<script>
import { GetDeviceEventsTypeList, GetQueryInstanceEvent } from '@/utils/spaceManage'
export default {
  name: 'deviceEventRecord',
  props: {
    deviceId: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      activeItem: 0,
      eventsTypeList: [],
      tableLoading: false,
      tableData: [],
      tableColumn: [],
      currentPage: 1,
      pageSize: 15,
      total: 0
    }
  },
  created() {
    this.getDeviceEventsTypeList()
  },
  methods: {
    eventsTypeChange(index) {
      this.activeItem = index
      this.tableColumn = this.eventsTypeList[index]?.valueType.properties
      this.getQueryInstanceEvent()
    },
    // 事件类型列表
    getDeviceEventsTypeList() {
      GetDeviceEventsTypeList(this.deviceId).then(res => {
        if (res.data.code == 200) {
          this.eventsTypeList = res.data.data
          this.eventsTypeList.map(item => item.valueType = JSON.parse(item.valueType))
          this.tableColumn = this.eventsTypeList[0]?.valueType.properties
          if (this.eventsTypeList.length) this.getQueryInstanceEvent()
        }
      })
    },
    // 根据事件获取table
    getQueryInstanceEvent() {
      let param = {
        deviceId: this.eventsTypeList[this.activeItem]?.factoryCode,
        eventId: this.eventsTypeList[this.activeItem]?.metadataTag,
        params: {
          pageSize: this.pageSize,
          pageIndex: this.currentPage - 1
        }
      }
      this.tableLoading = true
      GetQueryInstanceEvent(param).then(res => {
        this.tableLoading = false
        if (res.status === 200) {
          this.tableData = res.data.result?.data
          this.total = res.data.result?.total
        }
      })
    },
    handleSizeChange(val) {
      this.pageSize = val
      this.getQueryInstanceEvent()
    },
    handleCurrentChange(val) {
      this.currentPage = val
      this.getQueryInstanceEvent()
    }
  }
}
</script>

<style lang="scss" scoped>
.deviceEventRecord {
  width: 100%;
  height: 100%;
  display: flex;
  .deviceEventRecord-left {
    width: 220px;
    height: 100%;
    background: rgba(53, 98, 219, 0.06);
    padding: 16px;
    .left-item {
      cursor: pointer;
      width: 100%;
      padding: 8px 16px;
      font-size: 14px;
      color: #B0E3FA;
      border-radius: 4px;
      margin-bottom: 4px;
    }
    .item-active {
      color: #FFCA64;
      background: rgba(255, 202, 100, 0.12);
    }
  }
  .deviceEventRecord-right {
    flex: 1;
    height: 100%;
    margin-left: 16px;
    overflow: hidden;
    .table-main {
      width: 100%;
      height: calc(100% - 52px);
      overflow: hidden;
    }
  }
}
</style>
