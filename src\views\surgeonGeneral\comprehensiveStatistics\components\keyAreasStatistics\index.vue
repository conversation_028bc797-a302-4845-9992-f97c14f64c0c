<template>
  <div class="echarts-bottomLeft">
    <BgTitle showMore @moreClick="showDetail">
      <template #title>重点区域人员统计</template>
      <template #right>
        <el-date-picker
          v-if="dateType === 'custom'"
          v-model="date"
          class="datePickerInput"
          popper-class="date-style"
          type="daterange"
          value-format="yyyy-MM-dd"
          :clearable="false"
        >
        </el-date-picker>
        <el-dropdown trigger="click" @command="dataTypeCommand">
          <span class="el-dropdown-link dropdown-title">
            {{ dataTypeList.find((v) => v.value == dateType)?.name ?? "" }}
            <i class="el-icon-caret-bottom"></i>
          </span>
          <el-dropdown-menu slot="dropdown">
            <el-dropdown-item
              v-for="item in dataTypeList"
              :key="item.value"
              :command="item.value"
              :class="{ isBjxl: dateType == item.value }"
            >
              {{ item.name }}
            </el-dropdown-item>
          </el-dropdown-menu>
        </el-dropdown>
      </template>
    </BgTitle>
    <div class="bg-content">
      <TopContent :dateType="dateType" :date="date" @itemClick="showDetail"/>
      <BottomContent :dateType="dateType" :date="date" @itemClick="showDetail"/>
    </div>
    <KeyAreasListDialog  v-if="isKeyAreasListDialog" :isDialog="isKeyAreasListDialog" :roomData="roomData" @close="isKeyAreasListDialog = false"/>
  </div>
</template>
<script>
import BgTitle from '../../components/common/bgTitle'
import TopContent from './topContent'
import BottomContent from './bottomContent'
import KeyAreasListDialog from '@/views/spaceManage/sysTypeComponent/components/keyAreas/keyAreasListDialog.vue'
export default {
  components: {
    BgTitle,
    TopContent,
    BottomContent,
    KeyAreasListDialog
  },
  data() {
    return {
      date: [],
      dateType: 'day',
      dataTypeList: [
        { value: 'day', name: '今日' },
        { value: 'week', name: '本周' },
        { value: 'month', name: '本月' },
        { value: 'year', name: '本年' },
        { value: 'custom', name: '自定义' }
      ],
      isKeyAreasListDialog: false,
      roomData: {}
    }
  },
  mounted() {},
  methods: {
    // 时间类型切换
    dataTypeCommand(val) {
      this.dateType = val
    },

    showDetail (data) {
      this.isKeyAreasListDialog = true
    }
  }
}
</script>
<style lang="scss" scoped>
.echarts-bottomLeft {
  height: 100%;
  width: calc(33% - 8px);
  background: url("~@/assets/images/bg-content1.png") no-repeat;
  background-size: 100% 100%;
  margin-top: 16px;
  box-sizing: border-box;
  .bg-content {
    position: relative;
    box-sizing: border-box;
    padding: 10px;
    width: 100%;
    height: calc(100% - 44px);
    display: flex;
    flex-wrap: wrap;
  }
  .dropdown-title{
    color: #fff;
    cursor: pointer;
  }
}
.statistics_item {
  width: calc(100% - 4px);
  height: calc(50% - 4px);
  // border: 1px solid #fff;
  margin-right: 8px;
  &:nth-child(even) {
    margin-right: 0;
  }
}

.item_title {
  font-weight: 500;
  font-size: 16px;
  color: #b0e3fa;
  margin-bottom: 10px;
}
:deep(.datePickerInput) {
  height: 34px;
  width: 250px;
  box-sizing: content-box;
  background: none;
  border: none;
  .el-input__inner {
    border: none;
  }
  .el-input__prefix {
    display: none;
  }
  .el-range-separator{
    color: #fff;

  }
  .el-range-input{
    background: none;
    color: #fff
  }
}
</style>
