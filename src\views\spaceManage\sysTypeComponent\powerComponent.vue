<!--
 * @Author: hedd
 * @Date: 2024-01-15 10:48:44
 * @LastEditTime: 2024-08-22 15:01:43
 * @FilePath: \ihcrs_client_iframe\src\views\spaceManage\sysTypeComponent\powerComponent.vue
 * @Description:
-->
<template>
  <div class="powerComponent">
    <ModuleCard :title="roomData.title + ' - 设备台账'" class="module-container" style="height: 22%">
      <div slot="title-right" class="title-right">
        <div class="title-detail" @click="allTableChange('assets')">详情</div>
      </div>
      <div slot="content" style="height: 100%">
        <div class="statistics_top_new">
          <div v-for="(item, index) in statisticsData" :key="index" class="statistics_top_item">
            <img src="@/assets/images/qhdsys/statistics-new.png" alt="" />
            <div class="statistics_top_content">
              <p class="statistics_top_title">{{ item.name }}</p>
              <div class="font_bg">
                <p class="color_font" :style="{ color: item.key == 'abnormalDeviceTotal' ? '#FF2D55' : '' }">{{ deviceAccountStatistics[item.key] || 0 }}</p>
                <p class="color_unit">{{ item?.unit ?? '' }}</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </ModuleCard>
    <ModuleCard title="能耗统计" class="module-container" style="height: 28%">
      <div slot="content" class="equipment-energy" style="height: 100%">
        <div class="search-data">
          <el-dropdown trigger="click" @command="dataTypeCommand">
            <span class="el-dropdown-link"> {{ dataTypeList.find((v) => v.value == btnType)?.name ?? '' }} <i class="el-icon-caret-bottom"></i> </span>
            <el-dropdown-menu slot="dropdown">
              <el-dropdown-item v-for="item in dataTypeList" :key="item.value" :command="item.value" :class="{ isBjxl: btnType == item.value }">{{ item.name }}</el-dropdown-item>
            </el-dropdown-menu>
          </el-dropdown>
          <el-date-picker
            v-model="dataRangeValue"
            class="datePickerInput"
            popper-class="date-style"
            unlink-panels
            type="daterange"
            value-format="yyyy-MM-dd HH:mm:ss"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            :picker-options="{
              firstDayOfWeek: 1
            }"
            @change="dataPickerValChange()"
            @focus="setWPFBgShow()"
            @blur="setWPFBgHide()"
          >
          </el-date-picker>
        </div>
        <div v-loading="chartLoading" class="energy-loading-box">
          <div class="energy-statistics">
            <div>
              总能耗数据 <span>{{ energyStatistics.total || '-' }}</span> kwh
            </div>
            <!-- <div>
              费用 <span>{{ energyStatistics.cost }}</span> 元
            </div> -->
          </div>
          <div class="energy-charts">
            <div v-show="showEnergyEcharts" id="energyAnalysisEcharts"></div>
            <div v-show="!showEnergyEcharts" class="center-center">暂无数据</div>
          </div>
        </div>
      </div>
    </ModuleCard>
    <div class="module-container" style="height: 50%">
      <div class="module-header">
        <div class="title-left">
          <p class="title-left-text">设备列表</p>
        </div>
        <div v-if="keyDeviceList.length > 0" class="title-right">
          <div class="title-detail" @click="() => (isOpenKeyDevice = !isOpenKeyDevice)">{{ isOpenKeyDevice ? '收起' : '展开' }}</div>
        </div>
      </div>
      <div v-if="keyDeviceList.length > 0" v-scrollbarHover class="module-content device-list" style="height: calc(100% - 44px)">
        <div v-for="item in keyDeviceList" :key="item.surveyCode" class="device-list-item">
          <div class="item-title">
            <span class="title-text" @click="enterModel(item)">{{ item.surveyName }}</span>
            <span class="title-detail" @click="openDeviceDetail(item)">详情</span>
          </div>
          <div class="item-status">
            <div class="status-item">
              <p class="status-item-name">启停状态</p>
              <p class="status-item-value">{{ item.run }}</p>
            </div>
            <div class="status-item">
              <p class="status-item-name">离线状态</p>
              <p class="status-item-value">{{ item.line }}</p>
            </div>
            <div class="status-item">
              <p class="status-item-name">故障状态</p>
              <p class="status-item-value" :style="{color: item.warn === '报警' ? 'rgb(255, 45, 85)' : 'rgb(97, 226, 157)'}">{{ item.warn }}</p>
            </div>
          </div>
          <div v-show="isOpenKeyDevice" class="item-table">
            <el-table class="bottom-el-table" :data="item.newParamList" height="calc(100%)" style="width: 100%" element-loading-background="rgba(0, 0, 0, 0.2)">
              <el-table-column prop="paramName" show-overflow-tooltip label="名称"></el-table-column>
              <el-table-column prop="voltage" show-overflow-tooltip label="电流(A)"></el-table-column>
              <el-table-column prop="current" show-overflow-tooltip label="电压(V)"></el-table-column>
              <el-table-column prop="lineCurrent" show-overflow-tooltip label="线电压(V)"></el-table-column>
            </el-table>
          </div>
        </div>
      </div>
      <div v-else class="module-content empty" style="height: calc(100% - 44px)"><p>暂无数据</p></div>
    </div>
    <template v-if="allTableComponentListShow">
      <allTableComponentList ref="allTableComponentList" :dialogData="tableCompenentData" :dialogShow="allTableComponentListShow" :assetsList="assetsList" @configCloseDialog="configCloseTableDialog"></allTableComponentList>
    </template>
    <template v-if="deviceDetailsListShow">
      <deviceDialog ref="deviceDetailDialog" :dialogData="deviceDetailInfo" :dialogShow="deviceDetailsListShow" @deviceCloseDialog="() => changeDeviceDetailDialogShow(false)"></deviceDialog>
    </template>
  </div>
</template>

<script>
import moment from 'moment'
import * as echarts from 'echarts'
import { KgceLogin, GetPointHistoryList } from '@/utils/energyConsumption'
import { monitorTypeList } from '@/assets/common/dict.js'
import { getMonitorDeviceCount, queryAllSurveyParamList, getSurveyAssetByProjectCode, getModelFormulaPointList } from '@/utils/spaceManage'
import icon_5 from '@/assets/images/icon-5.png'
import icon_2 from '@/assets/images/icon-2.png'
import allTableComponentList from '../components/allTableComponentList.vue'
moment.locale('zh-cn', { week: { dow: 1 } })
export default {
  name: 'powerComponent',
  components: {
    allTableComponentList,
    deviceDialog: () => import('./components/deviceDetailDialog.vue')
  },
  props: {
    roomData: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    const dataType = {
      1: {
        type: 'hour',
        format: 'HH'
      },
      2: {
        type: 'day',
        format: 'DD'
      },
      3: {
        type: 'day',
        format: 'DD'
      },
      4: {
        type: 'month',
        format: 'MM'
      },
      5: {
        type: 'day',
        format: 'YYYY-MM-DD'
      }
    }
    // dataType: '' // 数据类型 （hour小时数据 day日数据 week周数据  month月数据 year年数据）
    return {
      deviceIds: '',
      assetsList: [],
      icon_2,
      icon_5,
      tableCompenentData: {}, // 一站式弹窗数据
      deviceAccountStatistics: {}, // 设备台账统计
      allTableComponentListShow: false,
      deviceDetailsListShow: false,
      deviceDetailInfo: {},
      isOpenKeyDevice: false, // 重要设备展开
      statisticsData: [
        {
          name: '设备总数',
          key: 'deviceTotal',
          unit: '台'
        },
        // {
        //   name: '监测数量',
        //   key: 'deviceMonitorTotal',
        //   unit: '台'
        // },
        {
          name: '离线设备数',
          key: 'offDeviceTotal',
          unit: '台'
        },
        {
          name: '异常设备',
          key: 'abnormalDeviceTotal',
          unit: '台'
        }
      ], // 统计数据
      keyDeviceList: [],
      dataType: dataType,
      dataTypeList: [
        { value: 1, name: '今日' },
        { value: 2, name: '本周' },
        { value: 3, name: '本月' },
        { value: 4, name: '本年' },
        { value: 5, name: '自定义' }
      ],
      dataRangeValue: [moment().startOf('week').format('YYYY-MM-DD') + ' 00:00:00', moment().endOf('week').format('YYYY-MM-DD') + ' 23:59:59'],
      btnType: '2',
      energyStatistics: {
        total: 0,
        cost: 0
      },
      chartLoading: false,
      showEnergyEcharts: false,
      kgceToken: ''
    }
  },
  watch: {
    // 监听模型层级数据变化
    roomData: {
      handler(val) {
        // 不根据房间过滤数据
        if (val.ssmType >= 6) return
        this.initData()
      },
      deep: true
    }
  },
  mounted() {
    // 初始化调用
    this.initData()
  },
  methods: {
    setWPFBgShow() {
      window.chrome.webview.hostObjects.sync.bridge.DateMask(true)
    },
    setWPFBgHide() {
      window.chrome.webview.hostObjects.sync.bridge.DateMask(false)
    },
    // 能耗登录
    kgceLogin() {
      KgceLogin(KECG_ACCOUNT).then((res) => {
        if (res.data.code === 200) {
          this.kgceToken = res.data.result.token
          this.getModelFormulaPointList()
        }
      })
    },
    // 时间类型切换
    dataTypeCommand(val) {
      const date = {
        1: [moment().format('YYYY-MM-DD') + ' 00:00:00', moment().format('YYYY-MM-DD') + ' 23:59:59'],
        2: [moment().startOf('week').format('YYYY-MM-DD') + ' 00:00:00', moment().endOf('week').format('YYYY-MM-DD') + ' 23:59:59'],
        3: [moment().startOf('month').format('YYYY-MM-DD') + ' 00:00:00', moment().endOf('month').format('YYYY-MM-DD') + ' 23:59:59'],
        4: [moment().startOf('year').format('YYYY-MM-DD') + ' 00:00:00', moment().endOf('year').format('YYYY-MM-DD') + ' 23:59:59'],
        5: []
      }
      this.dataRangeValue = date[val]
      this.btnType = val
      if (val != 5) {
        this.getModelFormulaPointList()
      }
    },
    // 切换能耗时间选择器
    dataPickerValChange() {
      this.btnType = '5'
      this.getModelFormulaPointList()
    },
    // 获取能耗数据
    getEnergyData(measurePointNo) {
      const params = {
        dataType: this.dataType[this.btnType].type,
        startDate: this.dataRangeValue[0],
        endDate: this.dataRangeValue[1],
        measurePointNo, // 测点编号
        domainKeyword: '70020000', // 正向有功总电能
        sum: false // 是否计算合计值
      }
      GetPointHistoryList(params, this.kgceToken).then((res) => {
        if (res.data.code === 200) {
          this.chartLoading = false
          const data = res.data.data
          if (data && data.length) {
            data.sort((a, b) => moment(a.time) - moment(b.time))
          }
          // 生成一个数组对象，data数组中time对象一致的合并
          const dateForData = []
          data.forEach((item) => {
            const index = dateForData.findIndex((v) => v.name === item.time)
            if (index > -1) {
              dateForData[index].value += Number(item.value)
            } else {
              dateForData.push({
                name: item.time,
                value: Number(item.value)
              })
            }
          })
          // 能耗统计
          const total = dateForData.reduce((pre, cur) => {
            return pre + cur.value
          }, 0)
          this.energyStatistics = {
            total: total.toFixed(2),
            cost: 0
          }
          this.showEnergyEcharts = true
          this.$nextTick(() => {
            this.electricityLineEchart(dateForData)
          })
        } else {
          this.showEnergyEcharts = false
          this.chartLoading = false
        }
      })
    },
    // 能耗分析
    electricityLineEchart(data) {
      const getchart = echarts.init(document.getElementById('energyAnalysisEcharts'))
      const nameList = data.map((item) => moment(item.name).format(this.dataType[this.btnType].format))
      const toFixedData = data.map((item) => {
        return {
          name: item.name,
          value: item.value.toFixed(2)
        }
      })
      const option = {
        backgroundColor: '',
        dataZoom: [
          {
            type: 'inside',
            start: 0,
            end: 100
          }
        ],
        tooltip: {
          trigger: 'axis',
          formatter: function (params) {
            return params[0].data.name + '</br>' + params[0].marker + params[0].data.value + ' kwh'
          },
          confine: true,
          axisPointer: {
            lineStyle: {
              color: '#FFE3A6'
            }
          },
          backgroundColor: '#0C2269',
          borderColor: '#5B617C',
          textStyle: {
            color: '#719DE7'
          }
        },
        grid: {
          top: '8%',
          left: '17%',
          right: '4%',
          bottom: '17%'
        },
        xAxis: {
          name: 'kwh',
          type: 'category',
          data: nameList,
          boundaryGap: false,
          axisTick: {
            show: false
          },
          axisLine: {
            // show: false,
            lineStyle: {
              color: '#303F69'
            }
          },
          axisLabel: {
            textStyle: {
              fontSize: 10,
              color: '#8C9EB5'
            }
          }
        },
        yAxis: {
          type: 'value',
          axisTick: {
            show: false
          },
          axisLabel: {
            margin: 10,
            textStyle: {
              fontSize: 10,
              color: '#8C9EB5'
            }
          },
          splitLine: {
            show: true,
            lineStyle: {
              color: ['#303F69'],
              width: 1,
              type: 'solid'
            }
          }
        },
        series: [
          {
            type: 'line',
            smooth: true,
            showSymbol: false,
            symbol: 'circle',
            symbolSize: 6,
            data: toFixedData,
            itemStyle: {
              normal: {
                color: '#FFE3A6'
              }
            },
            lineStyle: {
              normal: {
                width: 2
              }
            }
          }
        ]
      }
      getchart.clear()
      getchart.setOption(option)
      // 随着屏幕大小调节图表
      window.addEventListener('resize', () => {
        getchart.resize()
      })
    },
    // 进入模型 传参给wpf
    enterModel(item) {
      const regionCode = item.regionCode
      // if (regionCode && regionCode.length > 0) {
      window.chrome.webview.hostObjects.sync.bridge.KeyEquipmentSpace(item.modelCode, regionCode ? regionCode.split(',').at(-1) : '')
      // }
    },
    // 变配电监测设备详情
    openDeviceDetail(item) {
      Object.assign(this.deviceDetailInfo, {
        projectCode: monitorTypeList.find((e) => e.projectName === '配电监测').projectCode,
        ...this.roomData,
        surveyCode: item.surveyCode,
        entityTypeId: item.entityTypeId
      })
      this.changeDeviceDetailDialogShow(true)
    },
    changeDeviceDetailDialogShow(flag) {
      this.deviceDetailsListShow = flag
      try {
        window.chrome.webview.hostObjects.sync.bridge.ShowDialog(flag)
        window.chrome.webview.hostObjects.sync.bridge.GetSwichFloorIsShow(flag)
      } catch (error) {}
    },
    // 重点设备
    getKeyDeviceList() {
      this.ssmCodes = this.roomData.ssmCodes?.split(',') || []
      const params = {
        spaceId: this.ssmCodes.at(-1),
        keyDevice: 0,
        projectCode: monitorTypeList.find((e) => e.projectName === '配电监测').projectCode
      }
      queryAllSurveyParamList(params).then((res) => {
        if (res.data.code === '200') {
          const newArr = res.data.data
          newArr &&
            newArr.forEach((item) => {
              item.newParamList = []
              item.paramList &&
                item.paramList.forEach((v) => {
                  item.newParamList.push({
                    paramName: v.paramName,
                    voltage: v.paramValue[0],
                    current: v.paramValue[1],
                    lineCurrent: v.paramValue[2]
                  })
                })
            })
          this.keyDeviceList = newArr
          // 获取到设备列表后调用
          if (this.keyDeviceList.length) {
            // 如果能耗未登录则登录
            if (this.kgceToken) {
              this.getModelFormulaPointList()
            } else {
              this.kgceLogin()
            }
          } else {
            this.showEnergyEcharts = false
            this.chartLoading = false
            this.energyStatistics = {
              total: '-',
              cost: 0
            }
            this.showEnergyEcharts = false
            this.$nextTick(() => {
              const getchart = echarts.init(document.getElementById('energyAnalysisEcharts'))
              getchart.dispose()
            })
          }
        }
      })
    },
    initData() {
      this.chartLoading = true
      this.ssmCodes = this.roomData.ssmCodes?.split(',') || []
      this.getSurveyAssetByProjectCode().then((res) => {
        this.deviceIds = res
        this.getAssetsCount()
        this.getKeyDeviceList()
      })
    },
    // 获取所有设备列表
    getSurveyAssetByProjectCode() {
      const params = {
        projectCode: monitorTypeList.find((e) => e.projectName === '配电监测').projectCode,
        spaceId: this.ssmCodes.at(-1),
        keyDevice: '0'
      }
      return getSurveyAssetByProjectCode(params)
        .then((res) => {
          if (res.data.code === '200') {
            this.assetsList = res.data.data
            const deviceIdArr = Array.from(this.assetsList, ({ assetId }) => assetId)
            const deviceIds = deviceIdArr.length ? deviceIdArr.toString() : ''
            return deviceIds
          } else {
            this.assetsList = []
            return ''
          }
        })
        .catch(() => {
          this.assetsList = []
          return ''
        })
    },
    // 初始化设备数量
    getAssetsCount() {
      const params = {
        projectCode: monitorTypeList.find((e) => e.projectName === '配电监测').projectCode,
        spaceId: this.ssmCodes.at(-1),
        keyDevice: 0
      }
      getMonitorDeviceCount(params).then((res) => {
        if (res.data.code === '200') {
          this.deviceAccountStatistics = res.data.data
        }
      })
    },
    // 获取所有实体对应的传感器编码
    getModelFormulaPointList() {
      this.chartLoading = true
      const surveyCodes = Array.from(this.keyDeviceList, ({ surveyCode }) => surveyCode)
      getModelFormulaPointList({ surveyCode: surveyCodes.toString(), gyToken: this.kgceToken }).then((res) => {
        // 如果有数据则调用能耗接口
        const resData = res.data
        if (resData.code === '200' && resData.data.length) {
          // resData = ['ZHPN-SXDB-B2-ZAL2-4', 'ZHPN-SXDB-ZYB-10AT-6', 'ZHPN-SXDB-ZYA-10AL1']
          this.getEnergyData(resData.data.toString())
        } else {
          this.chartLoading = false
        }
      })
    },
    allTableChange(type) {
      if (!this.deviceIds || !this.deviceIds.length) {
        return this.$message.warning('暂无设备台账数据')
      }
      this.allTableComponentListShow = true
      const params = {
        spaceId: this.ssmCodes.at(-1),
        entityTypeId: ''
      }
      Object.assign(this.tableCompenentData, {
        title: '设备台账列表',
        ...params,
        isSpace: '0',
        projectCode: monitorTypeList.find((e) => e.projectName === '配电监测').projectCode,
        deviceId: this.deviceIds,
        type: type,
        keyDevice: 0,
        height: 'calc(100% - 120px)'
      })
    },
    // 关闭弹窗
    configCloseTableDialog() {
      this.allTableComponentListShow = false
      try {
        window.chrome.webview.hostObjects.sync.bridge.ShowDialog(false)
      } catch (error) {}
    }
  }
}
</script>

<style lang="scss" scoped>
@import '../style/module.scss';
.statistics_top_new {
  height: 130px;
  display: flex;
  flex-wrap: wrap;
  .statistics_top_item {
    height: 50%;
    width: 50%;
    // padding: 16px 0;
    display: flex;
    justify-content: center;
    align-items: center;
    img {
      width: 50px;
      height: 50px;
    }
  }
  .statistics_top_content {
    .statistics_top_title {
      padding-left: 20px;
      font-size: 14px;
      font-family: HarmonyOS Sans SC-Regular, HarmonyOS Sans SC;
      font-weight: 400;
      color: #b0e3fa;
      margin-bottom: 6px;
    }
    .font_bg {
      width: 125px;
      height: 32px;
      background: url(./../../../assets/images/qhdsys/Group-new.png) no-repeat 100%;
      display: flex;
      align-items: center;
      justify-content: center;
      .color_font {
        font-size: 20px;
        font-family: HarmonyOS Sans SC-Bold, HarmonyOS Sans SC;
        font-weight: bold;
        color: #ffca64;
        margin-bottom: 3px;
      }
      .color_unit {
        margin-left: 3px;
        font-size: 14px;
        font-family: HarmonyOS Sans SC-Light, HarmonyOS Sans SC;
        font-weight: 300;
        color: rgba(255, 255, 255, 0.5);
      }
    }
  }
}
.powerComponent {
  width: 100%;
  height: 100%;
  .device-list {
    overflow: auto;
    .device-list-item {
      padding-top: 16px;
      .item-title {
        background: rgba(133, 145, 206, 0.15);
        font-size: 15px;
        font-weight: 500;
        color: #ffffff;
        line-height: 18px;
        padding: 7px 8px;
        display: flex;
        align-items: center;
        .title-text {
          cursor: pointer;
          flex: 1;
          margin-right: 10px;
        }
      }
      .item-status {
        display: flex;
        .status-item {
          width: calc(100% / 3);
          text-align: center;
          padding: 15px;
        }
        .status-item-name {
          font-size: 14px;
          font-weight: 400;
          color: #ffffff;
          line-height: 16px;
        }
        .status-item-value {
          font-size: 16px;
          font-weight: bold;
          color: #ffca64;
          line-height: 19px;
          margin-top: 6px;
        }
        // .status-item:last-child {
        //   .status-item-value {
        //     color: rgb(97, 226, 157);
        //   }
        // }
      }
    }
  }
  .empty {
    display: flex;
    justify-content: center;
    align-items: center;
  }
  .title-left {
    padding-left: 20px;
  }
  .title-detail {
    cursor: pointer;
    font-size: 14px;
    font-family: HarmonyOS Sans SC-Regular, HarmonyOS Sans SC;
    font-weight: 400;
    color: #8bddf5 !important;
    margin-right: 13px;
  }
  .equipment-energy {
    .search-data {
      display: flex;
      background: rgba(133, 145, 206, 0.15);
      padding: 0px 10px;
      ::v-deep .el-dropdown {
        padding: 7px 6px;
        .el-dropdown-link {
          font-size: 14px;
          font-weight: 500;
          color: #8bddf5;
          line-height: 16px;
          position: relative;
          cursor: pointer;
        }
        .el-dropdown-link::after {
          content: '';
          position: absolute;
          width: 1px;
          height: 12px;
          background: rgba(133, 145, 206, 0.5);
          top: 50%;
          right: -6px;
          transform: translateY(-50%);
        }
      }
      ::v-deep .datePickerInput {
        flex: 1;
        padding: 8px 10px;
        height: 16px;
        box-sizing: content-box;
        background: none;
        border: none;
        .el-input__icon,
        .el-range-separator {
          line-height: 16px;
          color: #b0e3fa;
        }
        .el-range-input {
          background: none;
          color: #a4afc1;
        }
      }
    }
    .energy-loading-box {
      height: calc(100% - 33px);
      .energy-statistics {
        height: 37px;
        line-height: 37px;
        display: flex;
        justify-content: space-around;
        & > div {
          font-size: 14px;
          font-family: HarmonyOS Sans SC, HarmonyOS Sans SC;
          > span {
            font-size: 18px;
            font-family: HarmonyOS Sans SC, HarmonyOS Sans SC;
            font-weight: bold;
            color: #ffca64;
          }
        }
      }
      .energy-charts {
        height: calc(100% - 37px);
        #energyAnalysisEcharts {
          width: 100%;
          height: 100%;
          overflow: hidden;
        }
      }
    }
  }
}
// 滚动条的宽度
::v-deep .el-table__body-wrapper::-webkit-scrollbar {
  width: 6px; // 横向滚动条
  height: 6px; // 纵向滚动条 必写
}

// 注意:hover
::v-deep .el-table__body-wrapper:hover::-webkit-scrollbar {
  width: 6px; // 横向滚动条
  height: 6px; // 纵向滚动条 必写
}
::v-deep .el-table__body-wrapper::-webkit-scrollbar-corner,
::v-deep .el-table__body-wrapper::-webkit-scrollbar-track {
  background-color: transparent;
}
.module-header {
  width: 100%;
  // padding-left: 50px;
  background: url('~@/assets/images/qhdsys/bg-bt.png') no-repeat;
}
</style>
