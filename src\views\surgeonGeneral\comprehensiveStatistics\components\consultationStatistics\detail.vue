<template>
  <el-dialog
    v-dialogDrag
    :modal="false"
    :visible="visible"
    custom-class="mainDialog main"
    append-to-body
    :close-on-click-modal="false"
    :before-close="closeDia"
    class="all-table-componentList"
  >
    <template slot="title">
      <span class="dialog-title">就诊管理</span>
    </template>
    <div class="dialog-content">
      <div class="search-box">
        <div style="margin-right: 16px" class="taskInput">
          <el-input
            v-model="searchParams.fuzzyQuery"
            placeholder="患者医生名称/病历编码/诊室"
          ></el-input>
        </div>
        <div style="margin-right: 16px" class="taskInput">
          <el-input
            v-model="searchParams.chairName"
            placeholder="椅位名称"
          ></el-input>
        </div>

        <div style="margin-right: 16px" class="taskInput">
          <el-date-picker
            v-model="searchParams.time"
            type="date"
            placeholder="就诊日期"
            popper-class="date-style"
            value-format="yyyy-MM-dd"
            format="yyyy-MM-dd"
          >
          </el-date-picker>
        </div>
        <div style="margin-right: 16px" class="taskInput">
          <el-select
            v-model="searchParams.departmentId"
            placeholder="所在科室"
            filterable
            popper-class="new-select"
          >
            <el-option
              v-for="item in deptList"
              :key="item.deptId"
              :label="item.deptName"
              :value="item.deptId"
            >
            </el-option>
          </el-select>
        </div>
        <div style="margin-right: 16px" class="taskInput">
          <el-select
            v-model="searchParams.registType"
            placeholder="挂号方式"
            filterable
            popper-class="new-select"
          >
            <el-option
              v-for="item in resistTypeList"
              :key="item.id"
              :label="item.label"
              :value="item.id"
            >
            </el-option>
          </el-select>
        </div>

        <div class="search-btn">
          <el-button @click="resetSearch">重置</el-button>
          <el-button @click="handleSearch">查询</el-button>
        </div>
      </div>
      <div class="tableContent">
        <el-table
          v-loading="tableLoading"
          :data="tableData"
          height="100%"
          :cell-style="$tools.setCell(3)"
          :header-cell-style="$tools.setHeaderCell(3)"
          style="width: 100%"
          element-loading-background="rgba(0, 0, 0, 0.2)"
        >
          <el-table-column prop="caseCode" label="病历编码"  show-overflow-tooltip> </el-table-column>
          <el-table-column prop="patientName" label="患者名称"   show-overflow-tooltip> </el-table-column>
          <el-table-column prop="registType" label="挂号方式"   show-overflow-tooltip>
            <template slot-scope="scope">
              <span>{{ getColumnLabel(scope.row.registType, resistTypeList) }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="doctorName" label="主治医生"   show-overflow-tooltip> </el-table-column>
          <el-table-column prop="chairName" label="就诊椅位" width="100"> </el-table-column>
          <el-table-column prop="status" label="就诊状态" width="100">
            <template slot-scope="scope">
              <span>{{ getColumnLabel(scope.row.status, statusList) }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="appointmentDate" label="预约时间" width="250">
            <template slot-scope="scope">
              <span v-if="scope.row.registType != 6">{{ scope.row.appointmentDate }} {{ scope.row.appointmentBeginTime }}-{{ scope.row.appointmentEndTime }}</span>
              <span v-else>-</span>
            </template>
          </el-table-column>
          <el-table-column prop="signDate" label="签到时间" width="180"> </el-table-column>
          <el-table-column prop="visitBeginTime" label="就诊时间" width="180"> </el-table-column>
        </el-table>
        <el-pagination
          :current-page="currentPage"
          :page-sizes="[15, 20, 30, 40]"
          :page-size="pageSize"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
          class="pagination"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        ></el-pagination>
      </div>
    </div>
  </el-dialog>
</template>
<script>
import dayjs from 'dayjs'
import { encounterMgmt } from '@/utils/comprehensiveStatistics'
import { allDeptList } from '@/utils/newIot'
export default {
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    params: {
      type: Object,
      default: () => {
        return {}
      }

    }
  },
  data() {
    return {
      searchParams: {
        fuzzyQuery: '',
        status: '',
        time: '',
        departmentId: '',
        position: '',
        registType: ''
      },
      deptList: [],
      statusList: [
        { id: '', label: '全部' },
        { id: '-1', label: '退诊' },
        { id: '0', label: '已挂号' },
        { id: '1', label: '候诊' },
        { id: '2', label: '正在治疗' },
        { id: '3', label: '治疗结束' }
      ],
      resistTypeList: [
        { id: '', label: '全部' },
        { id: '1', label: '掌上医院' },
        { id: '2', label: '自助机' },
        { id: '3', label: '复诊预约' },
        { id: '4', label: '互联网医院' },
        { id: '5', label: '转诊' },
        { id: '6', label: '现场挂号' }
      ],
      tableLoading: false,
      tableData: [],
      currentPage: 1,
      pageSize: 15,
      total: 0
    }
  },
  mounted () {
    this.searchParams.time = this.params.date || dayjs().format('YYYY-MM-DD')
    this.searchParams.departmentId = this.params.id
    this.getUseDeptList()
    this.getList()
  },
  methods: {
    // 获取科室
    getUseDeptList() {
      allDeptList().then((res) => {
        if (res.data.code == '200') {
          this.deptList = res.data.data
        }
      })
    },

    closeDia() {
      this.$emit('close')
    },
    getColumnLabel (id, arr) {
      let item = {}
      item = arr.find(el => {
        return el.id == id
      })
      return item ? item.label : ''
    },
    getList () {
      this.tableLoading = true
      let params = {
        ...this.searchParams,
        page: this.currentPage,
        pageSize: this.pageSize
      }
      encounterMgmt(params).then(res => {
        if (res.data.code == 200) {
          this.tableData = res.data.data.records
          this.total = res.data.data.total
        }
      }).finally(() => {
        this.tableLoading = false
      })
    },
    resetSearch () {
      this.searchParams = {
        fuzzyQuery: '',
        status: '',
        time: '',
        departmentId: '',
        position: '',
        registType: ''
      }
      this.currentPage = 1
      this.getList()
    },
    handleSearch () {
      this.currentPage = 1
      this.getList()
    },
    handleSizeChange(val) {
      this.currentPage = 1
      this.pageSize = val
      this.getList()
    },
    handleCurrentChange(val) {
      this.currentPage = val
      this.getList()
    }
  }
}
</script>
<style lang="scss" scoped>
::v-deep .mainDialog {
  width: 80%;
  height: 84vh;
  margin-top: 6vh !important;
  // border: 1px solid #5996f9;
  background-color: transparent !important;
  pointer-events: auto;
  background-image: url("@/assets/images/table-bg.png");
  background-repeat: no-repeat;
  background-size: 100% 100%;
  box-shadow: none;
  .el-dialog__headerbtn {
    transform: translateX(-36px);
    width: 25px;
    height: 25px;
    background-image: url("@/assets/images/close.png");
    background-repeat: no-repeat;
    background-size: 100% 100%;
    .el-dialog__close::before {
      display: none;
    }
  }
  .dialog-title {
    display: inline-block;
    width: 100%;
    text-align: center;
    transform: translateY(-6px);
    color: #cbdeed;
  }
  .dialog-title::before {
    display: none;
  }

  .el-dialog__header {
    background-color: transparent;
  }
  .el-dialog__body {
    padding: 0;
    height: calc(100% - 60px);
    max-height: fit-content;
    overflow-y: hidden;
    background-color: transparent;
    padding: 0px 80px;
    .dialog-content {
      width: 100%;
      height: 100%;
      background-color: transparent;

      .el-table {
        background-color: transparent;
        border: 1px solid #202d4c;
        .el-table__header-wrapper {
          background-color: transparent !important;
        }
        .el-table__body-wrapper {
          background-color: transparent !important;
        }
        tr {
          background-color: transparent !important;
        }
        td {
          background-color: transparent !important;
        }
      }
    }
  }
}
:deep(.search-box) {
  display: flex;
  flex-wrap: wrap;

  .taskInput {
    .el-input {
      width: 180px;
    }
    .el-cascader {
      line-height: 35px;
      .el-input__inner {
        height: 35px !important;
      }
    }
  }

  .el-button {
    background-image: url("@/assets/images/btn.png");
    background-repeat: no-repeat;
    background-size: 100% 100%;
    height: 35px;
  }
  .search-btn {
    // margin: 10px 0;
    margin-left: auto;
  }
  .el-select {
    .el-input .el-input__inner {
      border-color: rgba(133, 145, 206, 0.5);
      width: 180px;
      height: 35px;
    }
  }

  .el-date-editor {
    width: 300px;
    height: 35px;
    background-color: #14233e;
    border-color: rgba(133, 145, 206, 0.5);
    .el-input__icon {
      transform: translateY(-2px);
    }
    .el-range-input {
      background-color: #14233e;
      color: rgba(255, 255, 255, 0.8);
    }
    .el-range-separator {
      color: rgba(255, 255, 255, 0.8);
      transform: translateY(-2px);
    }
  }
  .el-input .el-input__inner {
    border-color: rgba(133, 145, 206, 0.5);
    border-radius: 4px;
    color: rgba(255, 255, 255, 0.8);
  }
}
.search-box {
  margin: 5px 0 15px;
}

.search-box .el-input {
  // width: 120px;
  height: 35px;
}
.tableContent {
  width: 100%;
  height: calc(100% - 56px - 60px);
}
</style>
