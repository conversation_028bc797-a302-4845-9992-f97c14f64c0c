<!--
 * @Author: ycw
 * @Date: 2020-05-26 11:08:54
 * @LastEditors: ycw
 * @LastEditTime: 2020-06-03 13:37:06
 * @Description:
-->
<template>
  <div class="sino-image-logo">
    <div
      class="image-logo"
      v-if="!$store.state.isCollapse"
      :style="{ 'background-image': 'url(' + logoImage + ')' }"
    ></div>
  </div>
</template>

<script>
import normalLogo from '@assets/images/aside/logo.png'
import colourLogo from '@assets/images/aside/colourLogo.png'
export default {
  data() {
    return {
      logoImage: ''
    }
  },
  props: {
    type: {
      type: String,
      default: 'normal'
    }
  },
  mounted() {
    if (this.type === 'colour') {
      this.logoImage = colourLogo
    } else {
      this.logoImage = normalLogo
    }
  }
}
</script>

<style lang="scss" scoped>
.sino-image-logo {
  height: 64px;
  height: 64px;
  display: flex;
  align-items: center;
  justify-content: center;
  .image-logo {
    width: 128px;
    height: 48px;
    background-repeat: no-repeat;
    background-size: 100% 100%;
  }
}
</style>
