<template>
  <div class="advancedSearchDialog">
    <div class="advancedSearchContent">
      <div class="asc-title-box">
        <div class="asc-title">高级搜索</div>
        <div class="asc-btn" @click="closeDialog">
          <span>收起</span><span class="el-icon-arrow-up"></span>
        </div>
      </div>
      <div class="asc-content-box">
        <el-form
          :model="advancedSearchForm"
          class="search-form"
          inline
          label-width="150px"
          ref="advancedSearchForm"
        >
          <div class="asc-module-box" >
            <div class="asc-module-title">基础信息</div>
            <div class="asc-module-content">
              <el-form-item label="68编码:">
                <el-input
                  v-model="advancedSearchForm.input"
                  placeholder="请输入68编码"
                ></el-input>
              </el-form-item>
              <el-form-item label="甲乙类:">
                <el-select
                  v-model="advancedSearchForm.input"
                  placeholder="请选择甲乙类"
                  style="width: 11.37rem"
                >
                  <el-option
                    v-for="item in deptOptions"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  >
                  </el-option>
                </el-select>
              </el-form-item>
              <el-form-item label="资产金额:">
                <el-input
                  v-model="advancedSearchForm.input"
                  placeholder="请输入资产金额"
                  style="width: 11.37rem"
                  ><template slot="append">元</template></el-input
                >
              </el-form-item>
              <el-form-item label="出厂日期:">
                <el-date-picker
                  v-model="advancedSearchForm.input"
                  type="date"
                  format="yyyy-MM-dd"
                  value-format="yyyy-MM-dd"
                  placeholder="请选择出厂日期"
                  style="width: 11.37rem;padding: 0px;"
                >
                </el-date-picker>
              </el-form-item>
              <el-form-item label="设备情况:">
                <el-select
                  v-model="advancedSearchForm.input"
                  placeholder="请选择设备情况"
                  style="width: 11.37rem"
                >
                  <el-option
                    v-for="item in deptOptions"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  >
                  </el-option>
                </el-select>
              </el-form-item>
              <el-form-item label="产地:">
                <el-select
                  v-model="advancedSearchForm.input"
                  placeholder="请选择"
                  style="width: 11.37rem"
                >
                  <el-option
                    v-for="item in deptOptions"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  >
                  </el-option>
                </el-select>
              </el-form-item>
              <el-form-item label="规格型号:">
                <el-input
                  v-model="advancedSearchForm.input"
                  placeholder="请输入规格型号"
                ></el-input>
              </el-form-item>
              <el-form-item label="计量单位:">
                <el-select
                  v-model="advancedSearchForm.input"
                  placeholder="请选择计量单位"
                  style="width: 11.37rem"
                >
                  <el-option
                    v-for="item in deptOptions"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  >
                  </el-option>
                </el-select>
              </el-form-item>
              <el-form-item label="SN码:">
                <el-input
                  v-model="advancedSearchForm.input"
                  placeholder="请输入SN码"
                ></el-input>
              </el-form-item>
              <el-form-item label="寿命年限:">
                <el-input
                  v-model="advancedSearchForm.input"
                  placeholder="请输入寿命年限"
                  style="width: 11.37rem"
                  ><template slot="append">月</template></el-input
                >
              </el-form-item>
              <el-form-item label="供应商名称:">
                <el-select
                  v-model="advancedSearchForm.input"
                  placeholder="请选择供应商名称"
                  style="width: 11.37rem"
                >
                  <el-option
                    v-for="item in deptOptions"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  >
                  </el-option>
                </el-select>
              </el-form-item>
              <el-form-item label="生产厂商名称:">
                <el-select
                  v-model="advancedSearchForm.input"
                  placeholder="请选择生产厂商名称"
                  style="width: 11.37rem"
                >
                  <el-option
                    v-for="item in deptOptions"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  >
                  </el-option>
                </el-select>
              </el-form-item>
            </div>
          </div>
          <div class="asc-module-box" >
            <div class="asc-module-title">使用信息</div>
            <div class="asc-module-content">
              <el-form-item label="采购单编码:">
                <el-input
                  v-model="advancedSearchForm.input"
                  placeholder="请输入采购单编码"
                ></el-input>
              </el-form-item>
              <el-form-item label="采购合同编码:">
                <el-input
                  v-model="advancedSearchForm.input"
                  placeholder="请输入采购合同编码"
                ></el-input>
              </el-form-item>
              <el-form-item label="管理科室:">
                <el-select
                  v-model="advancedSearchForm.input"
                  placeholder="请选择管理科室"
                  style="width: 11.37rem"
                >
                  <el-option
                    v-for="item in deptOptions"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  >
                  </el-option>
                </el-select>
              </el-form-item>
              <el-form-item label="资产情况:">
                <el-select
                  v-model="advancedSearchForm.input"
                  placeholder="请选择资产情况"
                  style="width: 11.37rem"
                >
                  <el-option
                    v-for="item in deptOptions"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  >
                  </el-option>
                </el-select>
              </el-form-item>
              <el-form-item label="负责人:">
                <el-select
                  v-model="advancedSearchForm.input"
                  placeholder="请选择负责人"
                  style="width: 11.37rem"
                >
                  <el-option
                    v-for="item in deptOptions"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  >
                  </el-option>
                </el-select>
              </el-form-item>
              <el-form-item label="负责人电话:">
                <el-input
                  v-model="advancedSearchForm.input"
                  placeholder="请输入负责人电话"
                ></el-input>
              </el-form-item>
              <el-form-item label="维保合同编码:">
                <el-input
                  v-model="advancedSearchForm.input"
                  placeholder="请输入维保合同编码"
                ></el-input>
              </el-form-item>
              <el-form-item label="财务编码:">
                <el-input
                  v-model="advancedSearchForm.input"
                  placeholder="请输入财务编码"
                ></el-input>
              </el-form-item>
              <el-form-item label="使用科室:">
                <el-select
                  v-model="advancedSearchForm.input"
                  placeholder="请选择使用科室"
                  style="width: 11.37rem"
                >
                  <el-option
                    v-for="item in deptOptions"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  >
                  </el-option>
                </el-select>
              </el-form-item>
              <el-form-item label="存放仓库:">
                <el-select
                  v-model="advancedSearchForm.input"
                  placeholder="请选择存放仓库"
                  style="width: 11.37rem"
                >
                  <el-option
                    v-for="item in deptOptions"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  >
                  </el-option>
                </el-select>
              </el-form-item>
              <el-form-item label="启用日期:">
                <el-date-picker
                  v-model="advancedSearchForm.input"
                  type="date"
                  format="yyyy-MM-dd"
                  value-format="yyyy-MM-dd"
                  placeholder="请选择启用日期"
                  style="width: 11.37rem;padding: 0px;"
                >
                </el-date-picker>
              </el-form-item>
              <el-form-item label="维保日期:">
                <el-date-picker
                  v-model="advancedSearchForm.input"
                  type="date"
                  format="yyyy-MM-dd"
                  value-format="yyyy-MM-dd"
                  placeholder="请选择维保日期"
                  style="width: 11.37rem;padding: 0px;"
                >
                </el-date-picker>
              </el-form-item>
              <el-form-item label="资产来源:">
                <el-select
                  v-model="advancedSearchForm.input"
                  placeholder="请选择资产来源"
                  style="width: 11.37rem"
                >
                  <el-option
                    v-for="item in deptOptions"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  >
                  </el-option>
                </el-select>
              </el-form-item>
              <el-form-item label="资产用途:">
                <el-select
                  v-model="advancedSearchForm.input"
                  placeholder="请选择资产用途"
                  style="width: 11.37rem"
                >
                  <el-option
                    v-for="item in deptOptions"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  >
                  </el-option>
                </el-select>
              </el-form-item>
              <el-form-item label="资产状态:">
                <el-select
                  v-model="advancedSearchForm.input"
                  placeholder="请选择资产状态"
                  style="width: 11.37rem"
                >
                  <el-option
                    v-for="item in deptOptions"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  >
                  </el-option>
                </el-select>
              </el-form-item>
              <el-form-item label="所属区域:">
                <el-select
                  v-model="advancedSearchForm.input"
                  placeholder="请选择所属区域"
                  style="width: 11.37rem"
                >
                  <el-option
                    v-for="item in deptOptions"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  >
                  </el-option>
                </el-select>
              </el-form-item>
              <el-form-item label="主副分类:">
                <el-select
                  v-model="advancedSearchForm.input"
                  placeholder="请选择主副分类"
                  style="width: 11.37rem"
                >
                  <el-option
                    v-for="item in deptOptions"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  >
                  </el-option>
                </el-select>
              </el-form-item>
            </div>
          </div>
          <div class="asc-module-box" >
            <div class="asc-module-title">类别信息</div>
            <div class="asc-module-content">
              <el-form-item label="医疗器械分类:">
                <el-select
                  v-model="advancedSearchForm.input"
                  placeholder="请选择医疗器械分类"
                  style="width: 11.37rem"
                >
                  <el-option
                    v-for="item in deptOptions"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  >
                  </el-option>
                </el-select>
              </el-form-item>
              <el-form-item label="使用分类:">
                <el-select
                  v-model="advancedSearchForm.input"
                  placeholder="请选择使用分类"
                  style="width: 11.37rem"
                >
                  <el-option
                    v-for="item in deptOptions"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  >
                  </el-option>
                </el-select>
              </el-form-item>
              <el-form-item label="特种设备:">
                <el-select
                  v-model="advancedSearchForm.input"
                  placeholder="请选择特种设备"
                  style="width: 11.37rem"
                >
                  <el-option
                    v-for="item in deptOptions"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  >
                  </el-option>
                </el-select>
              </el-form-item>
              <el-form-item label="检验类设备:">
                <el-select
                  v-model="advancedSearchForm.input"
                  placeholder="请选择检验类设备"
                  style="width: 11.37rem"
                >
                  <el-option
                    v-for="item in deptOptions"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  >
                  </el-option>
                </el-select>
              </el-form-item>
              <el-form-item label="灭菌类设备:">
                <el-select
                  v-model="advancedSearchForm.input"
                  placeholder="请选择灭菌类设备"
                  style="width: 11.37rem"
                >
                  <el-option
                    v-for="item in deptOptions"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  >
                  </el-option>
                </el-select>
              </el-form-item>
              <el-form-item label="风险等级:">
                <el-select
                  v-model="advancedSearchForm.input"
                  placeholder="请选择风险等级"
                  style="width: 11.37rem"
                >
                  <el-option
                    v-for="item in deptOptions"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  >
                  </el-option>
                </el-select>
              </el-form-item>
              <el-form-item label="68分类:">
                <el-select
                  v-model="advancedSearchForm.input"
                  placeholder="请选择68分类"
                  style="width: 11.37rem"
                >
                  <el-option
                    v-for="item in deptOptions"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  >
                  </el-option>
                </el-select>
              </el-form-item>
              <el-form-item label="国标分类:">
                <el-select
                  v-model="advancedSearchForm.input"
                  placeholder="请选择国标分类"
                  style="width: 11.37rem"
                >
                  <el-option
                    v-for="item in deptOptions"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  >
                  </el-option>
                </el-select>
              </el-form-item>
              <el-form-item label="辐射设备:">
                <el-select
                  v-model="advancedSearchForm.input"
                  placeholder="请选择辐射设备"
                  style="width: 11.37rem"
                >
                  <el-option
                    v-for="item in deptOptions"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  >
                  </el-option>
                </el-select>
              </el-form-item>
              <el-form-item label="中医诊疗类设备:">
                <el-select
                  v-model="advancedSearchForm.input"
                  placeholder="请选择中医诊疗类设备"
                  style="width: 11.37rem"
                >
                  <el-option
                    v-for="item in deptOptions"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  >
                  </el-option>
                </el-select>
              </el-form-item>
              <el-form-item label="分类属性:">
                <el-select
                  v-model="advancedSearchForm.input"
                  placeholder="请选择分类属性"
                  style="width: 11.37rem"
                >
                  <el-option
                    v-for="item in deptOptions"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  >
                  </el-option>
                </el-select>
              </el-form-item>
              <el-form-item label="是否计量:">
                <el-select
                  v-model="advancedSearchForm.input"
                  placeholder="请选择是否计量"
                  style="width: 11.37rem"
                >
                  <el-option
                    v-for="item in deptOptions"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  >
                  </el-option>
                </el-select>
              </el-form-item>
              <el-form-item label="资产分类标准:">
                <el-select
                  v-model="advancedSearchForm.input"
                  placeholder="请选择资产分类标准"
                  style="width: 11.37rem"
                >
                  <el-option
                    v-for="item in deptOptions"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  >
                  </el-option>
                </el-select>
              </el-form-item>
              <el-form-item label="紧急生命支持类设备:">
                <el-select
                  v-model="advancedSearchForm.input"
                  placeholder="请选择紧急生命支持类设备"
                  style="width: 11.37rem"
                >
                  <el-option
                    v-for="item in deptOptions"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  >
                  </el-option>
                </el-select>
              </el-form-item>
              <el-form-item label="菌类设备:">
                <el-select
                  v-model="advancedSearchForm.input"
                  placeholder="请选择菌类设备"
                  style="width: 11.37rem"
                >
                  <el-option
                    v-for="item in deptOptions"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  >
                  </el-option>
                </el-select>
              </el-form-item>
              <el-form-item label="风险等级:">
                <el-select
                  v-model="advancedSearchForm.input"
                  placeholder="请选择风险等级"
                  style="width: 11.37rem"
                >
                  <el-option
                    v-for="item in deptOptions"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  >
                  </el-option>
                </el-select>
              </el-form-item>
              <el-form-item label="资产类型:">
                <el-select
                  v-model="advancedSearchForm.input"
                  placeholder="请选择资产类型"
                  style="width: 11.37rem"
                >
                  <el-option
                    v-for="item in deptOptions"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  >
                  </el-option>
                </el-select>
              </el-form-item>
            </div>
          </div>
        </el-form>
      </div>
      <div class="asc-footer-box">
        <el-button class="sino-button-sure" @click="closeDialog"
          >关闭</el-button
        >
        <el-button class="sino-button-sure" @click="resetForm">重置</el-button>
        <el-button class="sino-button-sure" @click="queryAdvancedSearchForm">查询</el-button>
      </div>
    </div>
  </div>
</template>
<script>
export default {
  data() {
    return {
      advancedSearchForm: {
        input: "",
      },
      deptOptions: [],
    };
  },
  methods: {
    closeDialog() {
      this.$emit("closeAdvancedSearch");
    },
    /** 重置 */
    resetForm(){
        this.$refs.advancedSearchForm.resetFields()
    },
    /** 查询 */
    queryAdvancedSearchForm(){
        this.$emit('queryAdvancedSearchForm')
    }
  },
};
</script>
<style lang="scss" scoped>
.advancedSearchDialog {
  position: absolute;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  padding: 0px 40px;
  z-index: 999999;
  box-sizing: border-box;
  .advancedSearchContent {
    width: 100%;
    height: 100%;
    padding: 14px;
    box-sizing: border-box;
    background: #374b79;
    display: flex;
    flex-direction: column;
    .asc-title-box {
      display: flex;
      align-items: center;
      justify-content: space-between;
      .asc-title {
        font-size: 16px;
        color: #fff;
        line-height: 1.25rem;
      }
      .asc-btn {
        color: #b0e3fa;
        cursor: pointer;
      }
    }
    .asc-content-box {
      flex: 1;
      overflow-y: auto;
      .asc-module-box {
        .asc-module-title {
          font-size: 14px;
          color: #fff;
          margin: 1.25rem 0px;
        }
      }
    }
    .asc-footer-box {
      text-align: right;
    }
  }
}
::v-deep .el-form--inline .el-form-item__label{
  color: #fff;
}
</style>