<template>
  <div class="list-index-view">
    <div class="view-header-box">
      <div class="header-left">
        <el-form :model="searchForm" class="search-form" inline>
          <el-form-item>
            <el-input v-model="searchForm.areaName" size="small" placeholder="重点区域名称" clearable></el-input>
          </el-form-item>
          <el-form-item>
            <el-select v-model="searchForm.officeId" size="small" placeholder="责任部门" filterable clearable>
              <el-option v-for="item in deptOptions" :key="item.id" :label="item.deptName" :value="item.id"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-date-picker
              v-model="searchForm.dateRange"
              type="daterange"
              unlink-panels
              popper-class="date-style"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              value-format="yyyy-MM-dd"
            >
            </el-date-picker>
          </el-form-item>
        </el-form>
      </div>
      <div class="header-right">
        <el-button class="sino-button-sure" @click="resetForm">重置</el-button>
        <el-button class="sino-button-sure" @click="handleSearchForm">查询</el-button>
      </div>
    </div>
    <div class="view-content">
      <el-table
        ref="table"
        v-loading="tableLoading"
        :data="tableData"
        :resizable="false"
        height="calc(100% - 46px)"
        :cell-style="$tools.setCell(3)"
        :header-cell-style="$tools.setHeaderCell(3)"
        stripe
        element-loading-background="rgba(0, 0, 0, 0.2)"
        @cell-click="viewDetail"
      >
        <el-table-column prop="areaName" label="重点区域" show-overflow-tooltip/>
        <el-table-column prop="throughCount" label="人员流量" show-overflow-tooltip sortable width="120"/>
        <el-table-column prop="abnormalCount" label="设备状态" show-overflow-tooltip sortable width="120">
          <p slot-scope="scope">
            <span style="color:#FF2D55">{{scope.row.abnormalCount}}</span>
            <span>/{{scope.row.allCount}}</span>
          </p>
        </el-table-column>
        <el-table-column prop="alarmCount" label="区域报警" show-overflow-tooltip sortable width="120"/>
        <el-table-column prop="officeName" label="责任部门" show-overflow-tooltip/>
        <el-table-column prop="principalName" label="责任人" show-overflow-tooltip width="100"/>
        <el-table-column prop="phoneNo" label="责任人电话" show-overflow-tooltip width="110"/>
        <el-table-column prop="securityPrincipalName" label="安全责任人" show-overflow-tooltip width="110"/>
        <el-table-column prop="securityPhoneNo" label="安全责任人电话" show-overflow-tooltip width="135"/>
        <el-table-column prop="spaceList" label="包含空间" show-overflow-tooltip>
          <template slot-scope="scope">
            {{ scope.row.spaceNameList?.join('、') }}
          </template>
        </el-table-column>
      </el-table>
      <el-pagination
        :current-page="currentPage"
        :page-sizes="[15, 20, 30, 40]"
        :page-size="pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
        class="pagination"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      ></el-pagination>
    </div>
  </div>
</template>

<script>
import { GetRridAreaList } from '@/utils/spaceManage'
import { getSelectedDept } from '@/utils/peaceRightScreenApi'

export default {
  name: 'keyAreasList',
  props: {
    roomData: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  data() {
    return {
      searchForm: {
        areaName: '',
        officeId: '',
        dateRange: []
      },
      deptOptions: [],
      currentPage: 1,
      pageSize: 15,
      total: 0,
      tableData: [],
      tableLoading: false
    }
  },
  computed: {

  },
  created() {
    this.getDeptListData()
    this.getRridAreaList()
  },
  methods: {
    getDeptListData() {
      getSelectedDept().then((res) => {
        if (res.data.code == 200) {
          this.deptOptions = res.data.data
        }
      })
    },
    // 获取重点区域列表
    getRridAreaList() {
      let ssmCodes = this.roomData.ssmCodes?.split(',') || []
      this.searchForm.dateRange = this.searchForm.dateRange || []
      const params = {
        areaName: this.searchForm.areaName,
        officeId: this.searchForm.officeId,
        beginDate: this.searchForm.dateRange[0],
        endDate: this.searchForm.dateRange[1],
        regionCode: ssmCodes[ssmCodes.length - 1],
        pageNo: this.currentPage,
        pageSize: this.pageSize
      }
      this.tableLoading = true
      GetRridAreaList(params).then(res => {
        this.tableLoading = false
        if (res.data.code == 200) {
          this.tableData = res.data.data.list
          this.total = Number(res.data.data.totalCount)
        }
      }).finally(() => {
        this.tableLoading = false
      })
    },
    /** 查询 */
    handleSearchForm() {
      this.getRridAreaList()
    },
    /** 重置 */
    resetForm() {
      Object.assign(this.$data.searchForm, this.$options.data().searchForm)
      this.handleSearchForm()
    },
    viewDetail(row, column) {
      if (column.property == 'throughCount') { // 人员流量
        this.$emit('openDetailComponent', {key: 'personnelFlowList', data: row})
      } else if (column.property == 'abnormalCount') { // 设备状态
        this.$emit('change', {key: 'abnormalCount', data: row.id})
      } else if (column.property == 'alarmCount') { // 区域报警数
        this.$emit('change', {key: 'alarmCount', data: row.id})
      }
    },
    /** 条数变化事件 */
    handleSizeChange(val) {
      this.pageSize = val
      this.handleSearchForm()
    },
    /** 页数变化事件 */
    handleCurrentChange(val) {
      this.currentPage = val
      this.handleSearchForm()
    }
  }
}

</script>

<style lang="scss" scoped>
.list-index-view {
  position: relative;
  height: 100%;
  padding: 0px 40px;
}
.view-header-box {
  display: flex;
  justify-content: space-between;
  ::v-deep(.el-date-editor) {
    margin-top: 4px;
    width: 300px;
    height: 32px;
    background-color: transparent;
    border-color: #3056A2;
    border-radius: 0;
    .el-input__icon {
      transform: translateY(-4px);
    }
    .el-range-input {
      height: 30px;
      background-color: transparent;
      color: rgba(255, 255, 255, 0.8);
    }
    .el-range-separator {
      color: rgba(255, 255, 255, 0.8);
      transform: translateY(-4px);
    }
  }
  .header-right {
    line-height: 40px;
  }
}
.view-content {
  height: calc(100% - 4rem);
}
::v-deep .el-table--striped .el-table__body tr.el-table__row--striped td.el-table__cell{
  background: transparent;
}
</style>
