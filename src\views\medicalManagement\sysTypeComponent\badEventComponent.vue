<template>
  <div class="AdverseEventComponent">
    <div class="module-container" style="height: calc(25%)">
      <div class="module-header">
        <div class="title-left">
          <p class="title-left-text">事件状态统计
            <el-dropdown trigger="click" @command="(val) => (tagCurrent = val)">
              <span class="el-dropdown-link"> 全部科室 <i class="el-icon-arrow-down"></i> </span>
              <el-dropdown-menu slot="dropdown">
                <el-dropdown-item :command="'1'" :class="{ isBjxl: tagCurrent == '1' }">全部科室</el-dropdown-item>
              </el-dropdown-menu>
            </el-dropdown>
          </p>
        </div>
        <div class="title-right">
          <el-dropdown trigger="click" @command="(val) => (tagCurrent = val)">
            <span class="el-dropdown-link"> 2024年度 <i class="el-icon-arrow-down"></i> </span>
            <el-dropdown-menu slot="dropdown">
              <el-dropdown-item :command="'1'" :class="{ isBjxl: tagCurrent == '1' }">2024年度</el-dropdown-item>
            </el-dropdown-menu>
          </el-dropdown>
          <div class="icon-box">
            <img src="../../../assets/images/order_more.png" class="order-more" @click="allTableChange" />
          </div>
        </div>
      </div>
      <div class="module-content" style="height: calc(100% - 44px)">
        <div id="eventStateEchart" style="height: 100%; overflow: hidden"></div>
      </div>
    </div>
    <div class="module-container" style="height: calc(25%)">
      <div class="module-header">
        <div class="title-left">
          <p class="title-left-text">事件上报状态</p>
        </div>
      </div>
      <div class="module-content" style="height: calc(100% - 48px)">
        <div id="reportStatusEchart" style="height: 100%; overflow: hidden"></div>
      </div>
    </div>
    <div class="module-container" style="height: calc(25%)">
      <div class="module-header">
        <div class="title-left">
          <p class="title-left-text">近5年不良事件趋势<span class="unit">/次</span></p>
        </div>
      </div>
      <div class="module-content" style="height: calc(100% - 20px)">
        <div id="adverseEventTrendEchart" style="height: 100%; overflow: hidden"></div>
      </div>
    </div>
    <div class="module-container" style="height: calc(25%)">
      <div class="module-header">
        <div class="title-left">
          <p class="title-left-text">品牌不良事件排行<span class="unit">/次</span></p>
        </div>
      </div>
      <div class="module-content" style="height: calc(100% - 20px)">
        <div id="adverseEventEchart" style="height: 100%; overflow: hidden"></div>
      </div>
    </div>
    <!-- 更多 -->
    <DialogFrame :visible.sync="dialogFrame.show" :title="dialogFrame.title">
      <adverseEventDetail />
    </DialogFrame>
  </div>
</template>

<script >
import * as echarts from 'echarts'
import DialogFrame from '../../../components/common/DialogFrame.vue'
import adverseEventDetail from '../components/badEvent/adverseEventDetail.vue'
export default {
  name: 'AdverseEventComponent',
  props: {
    roomData: {
      type: Object,
      default: () => { }
    }
  },
  components: {
    DialogFrame,
    adverseEventDetail
  },
  watch: {
    // 监听模型层级数据变化
    roomData: {
      handler(newVal, oldVal) {
        this.initData()
      },
      deep: true
    }
  },
  data() {
    return {
      sortType: '',
      chartData: [],
      ssmCodes: [],
      tagCurrent: '',
      dialogFrame: {
        show: false,  // 全部数据
        title: '' // 目标tab
      },
    }
  },
  mounted() {
    this.sortType = this.roomData.ssmType < 4 ? 'jz' : this.roomData.ssmType == 4 ? 'lc' : 'mk'
    // 初始化调用
    this.initData()
  },
  methods: {
    initData() {
      this.sortType = this.roomData.ssmType < 4 ? 'jz' : this.roomData.ssmType == 4 ? 'lc' : 'mk'
      this.ssmCodes = this.roomData.ssmCodes?.split(',') || []
      this.getBadPersonalLeaveList()
      this.getEventStateList()
      this.getAdverseEventTrendData()
      this.getReportStatusList()
    },
    //事件上报状态
    getReportStatusList() {
      let arr = [
        {
          count: 144,
          name: "未上报",
          percentage: 30
        }, {
          count: 122,
          name: "已上报",
          percentage: 25
        }
      ]
      // 基于准备好的dom，初始化echarts实例
      let getchart = echarts.init(document.getElementById('reportStatusEchart'))
      const data = []
      var color = ['#FF2D55', '#61E29D ', '#FFAD65', '#e38e6f', '#f2d988']
      const xdata = Array.from(arr, ({ name }) => name)
      for (var i = 0; i < arr.length; i++) {
        const randomRgbColor = this.$tools.randomRgbColor('array')
        data.push({
          name: arr[i].name,
          value: arr[i].count,
          percentage: arr[i].percentage,
          itemStyle: {
            borderWidth: 1,
            shadowBlur: 200,
            color: color[i] ?? randomRgbColor[0],
            label: {
              show: true
            }
          }
        })
      }
      const option = {
        backgroundColor: '',
        title: {
          text: "{name|" + '事件总数' + "}\n{val|" + 13269 + "}",
          top: "30%",
          left: "39%",
          textStyle: {
            rich: {
              name: {
                fontSize: 14,
                fontWeight: "normal",
                color: '#ffffff',
                padding: [10, 10],
              },
              val: {
                fontSize: 18,
                fontWeight: "bold",
                color: "#ffffff",
                padding: [0, 10],
              },
            },
          },
        },
        series: [
          {
            name: '',
            type: 'pie',
            clockWise: false,
            center: ['50%', '50%'],
            radius: ['58%', '75%'],
            label: {
              show: false,
              position: 'center',
              formatter: '{value|{c}}\n{label|{b}}',
              rich: {
                value: {
                  padding: 5,
                  align: 'center',
                  verticalAlign: 'middle',
                  fontSize: 20
                },
                label: {
                  align: 'center',
                  verticalAlign: 'middle',
                  fontSize: 14
                }
              },
            },
            labelLine: {
              length: 10,
              length2: 20
            },
            label: {
              formatter: params => {
                return (
                  '{name|' + params.name + '  ' + params.data.percentage + '%' + '}\n{value|' + params.data.value + '}'
                );
              },
              rich: {
                name: {
                  fontSize: 12,
                  padding: [0, 0, 0, 10],
                  color: '#ffffff'
                },
                value: {
                  fontSize: 12,
                  fontWeight: 'bolder',
                  padding: [10, 0, 0, 20],
                  color: '#ffffff'
                }
              }
            },
            // hoverAnimation: false,
            data: data
          }
        ]
      }
      getchart.clear()
      getchart.setOption(option)
      // 随着屏幕大小调节图表
      window.addEventListener('resize', () => {
        getchart.resize()
      })
    },
    //事件状态
    getEventStateList() {
      let arr = [
        {
          count: 150,
          name: "待审核数",
          percentage: 30
        }, {
          count: 69,
          name: "已通过数",
          percentage: 0
        }, {
          count: 50,
          name: "未通过数",
          percentage: 0
        }
      ]
      // 基于准备好的dom，初始化echarts实例
      let getchart = echarts.init(document.getElementById('eventStateEchart'))
      const data = []
      var color = ['#FF2D55', '#61E29D ', '#FFAD65', '#e38e6f', '#f2d988']
      const xdata = Array.from(arr, ({ name }) => name)
      for (var i = 0; i < arr.length; i++) {
        const randomRgbColor = this.$tools.randomRgbColor('array')
        data.push({
          name: arr[i].name,
          value: arr[i].count,
          percentage: arr[i].percentage,
          itemStyle: {
            borderWidth: 1,
            shadowBlur: 200,
            color: color[i] ?? randomRgbColor[0],
            label: {
              show: true
            }
          }
        })
      }
      const option = {
        backgroundColor: '',
        title: {
          text: "{name|" + '事件总数' + "}\n{val|" + 269 + "}",
          top: "30%",
          left: "22%",
          textStyle: {
            rich: {
              name: {
                fontSize: 14,
                fontWeight: "normal",
                color: '#ffffff',
                padding: [10, 0],
              },
              val: {
                fontSize: 18,
                fontWeight: "bold",
                color: "#ffffff",
                padding: [0, 10],
              },
            },
          },
        },
        legend: {
          // 取消legend和echarts的联动
          selectedMode: false,
          icon: 'circle',
          orient: 'vartical',
          x: 'left',
          top: 'center',
          left: '52%',
          bottom: '0%',
          data: xdata,
          itemWidth: 8,
          itemHeight: 8,
          itemGap: 16,
          textStyle: {
            color: '#B3C2DD' //  字体颜色
          },
          formatter: function (name) {
            var oa = option.series[0].data
            for (var i = 0; i < option.series[0].data.length; i++) {
              if (name === oa[i].name) {
                return ' ' + name + '    ' + oa[i].value + '    ' + oa[i].percentage + '%'
              }
            }
          }
        },
        series: [
          {
            name: '',
            type: 'pie',
            clockWise: false,
            center: ['30%', '50%'],
            radius: ['58%', '75%'],
            label: {
                show: false,
                position: 'center',
                formatter: '{value|{c}}\n{label|{b}}',
                rich: {
                  value: {
                    padding: 5,
                    align: 'center',
                    verticalAlign: 'middle',
                    fontSize: 20
                  },
                  label: {
                    align: 'center',
                    verticalAlign: 'middle',
                    fontSize: 14
                  }
              },
            },
            // hoverAnimation: false,
            data: data
          }
        ]
      }
      getchart.clear()
      getchart.setOption(option)
      // 随着屏幕大小调节图表
      window.addEventListener('resize', () => {
        getchart.resize()
      })
    },
    // 不良事件趋势
    getAdverseEventTrendData() {
      let resData = {
        xAxisData: ['2020', '2021', '2022', '2023', '2024'],
        seriesData: [5, 10, 16, 17, 30],
      }
      let maxArr = []
      let maxArr2 = []
      if (!resData || !resData.xAxisData || resData.xAxisData.length == 0) {
        resData = {
          xAxisData: [''],
          seriesData: [0]
        }
        maxArr = [100000]
      } else {
        const max = Math.max(...resData.seriesData)
        for (let i = 0; i < resData.seriesData.length; i++) {
          maxArr.push(100000)
          maxArr2.push(max + 5)
        }
      }
      // 基于准备好的dom，初始化echarts实例
      let getchart = echarts.init(document.getElementById('adverseEventTrendEchart'))
      const option = {
        backgroundColor: '',
        tooltip: {
          trigger: 'axis',
        },
        grid: {
          left: "20px",
          right: "20px",
          top: "30px",
          bottom: "30px",
          containLabel: true,
        },
        xAxis: {
          type: 'category',
          data: resData.xAxisData,
          axisTick: {
            show: false,
          },
          splitLine: {
            show: true,
            lineStyle: {
              color: "rgba(255,255,255,0.2)",
              type: 'dashed'
            },
          },
          axisLabel: {
            show: true,
            color: "#ffffff",
            interval: 0,
            formatter: function (value) {
              if (value && value.length > 5) {
                value = value.replace(/\s/g, '').replace(/(.{5})/g, "$1\n");
                value = value.slice(0, value.length - 2)
              }
              return value
            },
            textStyle: {
              color: 'rgba(255,255,255,0.1)',
              fontSize: 10,
              fontWeight: 500,
            }
          },
          axisLine: {
            lineStyle: {
              color: "rgba(255,255,255,0.2)",
            },
          }
        },
        yAxis: [{
          name: '',
          nameTextStyle: { color: 'rgba(255,255,255,0.2)' },
          type: 'value',
          lineStyle: {
            show: "none",
          },
          axisTick: {
            show: false,
          },
          axisLine: {
            show: true,
            lineStyle: {
              color: "rgba(255,255,255,0.2)",
            },
          },
          axisLabel: {
            show: true,
            color: "rgba(255,255,255,0.5)",
          },
          splitLine: {
            show: true,
            lineStyle: {
              color: "rgba(255,255,255,0.2)",
              type: 'dashed'
            },
          }
        }, {
          type: 'value',
          lineStyle: {
            show: "none",
          },
          axisTick: {
            show: false,
          },
          axisLine: {
            show: false,
          },
          axisLabel: {
            show: false,
          },
          splitLine: {
            show: false,
          }
        }],
        series: [
          {
            name: '近5年不良事件趋势',
            type: 'bar',
            barWidth: 24,
            barMinHeight: 3,
            z: 12,
            barGap: '10%',
            itemStyle: {
              color: "#8BDDF5",
            },
            emphasis: {
              disabled: true,
            },
            label: {
              show: false,
              position: 'top',
              fontSize: 12
            },
            data: resData.seriesData,
          }, {
            name: '背景1',
            yAxisIndex: 0,
            tooltip: {
              show: false
            },
            type: 'bar',
            barWidth: 24,
            barMinHeight: 3,
            barGap: '-100%',
            barCateGoryGap: '10%',
            itemStyle: {
              color: 'rgba(255,255,255,0.1)'
            },
            emphasis: {
              disabled: true,
            },
            data: maxArr2
          }, {
            name: '背景2',
            yAxisIndex: 1,
            tooltip: {
              show: false
            },
            type: 'bar',
            barWidth: 24,
            barMinHeight: 3,
            barGap: '-100%',
            barCateGoryGap: '10%',
            itemStyle: {
              color: 'rgba(255,255,255,0.1)'
            },
            emphasis: {
              disabled: true,
            },
            data: maxArr
          }
        ]
      }
      getchart.clear()
      getchart.setOption(option)
      // 随着屏幕大小调节图表
      window.addEventListener('resize', () => {
        getchart.resize()
      })
    },
    // 品牌不良事件排行
    getBadPersonalLeaveList() {
      let data = [
        {
          id: "1",
          name: '品牌名称1',
          value: 4000,
        }, {
          id: "2",
          name: '品牌名称2',
          value: 3000,
        }, {
          id: "3",
          name: '品牌名称3',
          value: 2500,
        }, {
          id: "4",
          name: '品牌名称4',
          value: 2000,
        }, {
          id: "5",
          name: '品牌名称5',
          value: 1500,
        }, {
          id: "6",
          name: '品牌名称6',
          value: 1000,
        }, {
          id: "7",
          name: '品牌名称7',
          value: 1000,
        }
      ]
      // 基于准备好的dom，初始化echarts实例
      let EChart = echarts.init(document.getElementById('adverseEventEchart'))
      //   将data数据中得value值从大到小排序，然后按顺序将value放入dataValue数组中 name放入dataName数组中 name和value一一对应
      data.sort(function (a, b) {
        return a.value - b.value
      })
      const dataName = Array.from(data, (item) => {
        item.name = item.name || '未命名'
        const match = item.name.match(this.regex)
        const name = item.name
        return {
          value: name,
          textStyle: {
            color: '#FFF'
          }
        }
      })
      // 配置参数
      let config = {
        legend: {},
        grid: {
          top: '5%',
          left: '3%',
          right: '8%',
          bottom: '0%',
          containLabel: true
        },
        xAxis: {
          type: 'value',
          show: false
        },
        yAxis: [
          {
            type: 'category',
            data: dataName,
            axisTick: {
              show: false // 不显示坐标轴刻度线
            },
            axisLine: {
              show: false // 不显示坐标轴线
            },
            splitLine: {
              // 网格线
              show: false
            },
            axisLabel: { textStyle: { color: '#fff', fontSize: '14' } }
          },
          {
            type: 'category', // 坐标轴类型
            // inverse: true, // 是否是反向坐标轴
            axisTick: {
              show: false // 不显示坐标轴刻度线
            },
            axisLine: {
              show: false // 不显示坐标轴线
            },
            splitLine: {
              // 网格线
              show: false
            },
            axisLabel: {
              textStyle: {
                color: '#FFFFFFCC',
                fontSize: '14'
              },
              formatter: (value) => {
                return 4000
              }
            },
            data: data
          }
        ],
        series: [
          {
            type: 'bar',
            stack: 'total',
            label: {
              show: false
            },
            emphasis: {
              focus: 'series'
            },
            data: data,
            barWidth: 8,
            itemStyle: {
              // barBorderRadius: [15, 15, 15, 15],
              color: function (params) {
                // 通过返回值的下标一一对应将颜色赋给柱子上
                return '#FFCA64'
              }
            },
            // 鼠标移入改变颜色
            emphasis: {
              color: '#FFCA64FF'
            },
            showBackground: true, // 显示背景色
            backgroundStyle: {
              color: '#384156'
            }
          }
        ],
        dataZoom: [
          {
            yAxisIndex: [0, 1],
            orient: 'vertical',
            show: true,
            type: 'slider',
            start: 100,
            // end: 100,
            width: 8,
            left: '99%',
            borderColor: 'rgba(43,48,67,.1)',
            fillerColor: '#6580b8',
            zoomLock: true,
            showDataShadow: false,
            backgroundColor: 'rgba(43,48,67,.1)',
            showDetail: false,
            // realtime: true,
            filterMode: 'filter',
            handleIcon: 'M-9.35,34.56V42m0-40V9.5m-2,0h4a2,2,0,0,1,2,2v21a2,2,0,0,1-2,2h-4a2,2,0,0,1-2-2v-21A2,2,0,0,1-11.35,9.5Z',
            handleStyle: {
              color: '#6580b8',
              borderColor: '#6580b8'
            },
            maxValueSpan: 4,
            minValueSpan: 4,
            brushSelect: false
          },
          {
            type: 'inside',
            show: false,
            yAxisIndex: [0, 1],
            height: 8
          }
        ]
      }
      // 设置参数
      EChart.setOption(config)
      // 先移除点击事件 解决点击事件重复绑定
      EChart.getZr().off('click')
      // 点击事件
      EChart.getZr().on('click', (params) => {
        var pointInPixel = [params.offsetX, params.offsetY]
        if (EChart.containPixel('grid', pointInPixel)) {
          // 获取当前点击的索引值
          // 注意：若柱状图为纵向则获取x轴的索引，若柱状图为横向则需获取y轴的索引(见下方注释)
          // var xIndex=EChart.convertFromPixel({seriesIndex:0},[params.offsetX, params.offsetY])[0];
          // var xData=config.xAxis.data[xIndex];//当前点击柱子的数据
          var yIndex = EChart.convertFromPixel({ seriesIndex: 0 }, [params.offsetX, params.offsetY])[1]
          var yData = config.yAxis[1].data[yIndex] // 当前点击柱子的数据
          // 新增变量 判断当前点击柱状图是选中还是取消选中
          let isSelected = false
          dataName.map((e, i) => {
            // 选中的设置选中色
            if (i == yIndex && e.textStyle.color != '#FFCA64FF') {
              e.textStyle.color = '#FFCA64FF'
            } else {
              // 选中已选中的则为取消选中
              if (i == yIndex && e.textStyle.color == '#FFCA64FF') {
                isSelected = true
              }
              // 其他的设为默认色
              e.textStyle.color = '#FFF'
            }
          })
          config.yAxis[0].data = JSON.parse(JSON.stringify(dataName))
          config.dataZoom[0].start = (yIndex / dataName.length) * 100
          EChart.setOption(config)
          // ....  业务逻辑
          // 取消选中 则恢复过滤条件
          let id = !isSelected ? yData.id : ''
          if (this.isType === 'bm') {
            this.paramsData = {
              deptId: id,
              spaceState: ''
            }
            this.getRoomCountByDeptIdList(this.isCommand, id)
          }
        }
      })
    },
    allTableChange() {
      this.dialogFrame.show = true
      this.dialogFrame.title = '不良事件'
    }
  }
}
</script>

<style lang="scss" scoped>
@import "../../spaceManage/style/module";
.AdverseEventComponent {
  width: 100%;
  height: 100%;
  .el-dropdown-link {
    color: #fff !important;
    margin-left: 10px;
  }
  .title-left {
    padding-left: 20px;
    .unit {
      font-size: 12px !important;
      color: #ffffff !important;
      font-weight: 400 !important;
    }
  }
  ::v-deep .el-table {
    .el-table__body tr {
      cursor: pointer;
    }
  }
}
// 滚动条的宽度
::v-deep .el-table__body-wrapper::-webkit-scrollbar {
  width: 6px; // 横向滚动条
  height: 6px; // 纵向滚动条 必写
}

// 注意:hover
::v-deep .el-table__body-wrapper:hover::-webkit-scrollbar {
  width: 6px; // 横向滚动条
  height: 6px; // 纵向滚动条 必写
}
::v-deep .el-table__body-wrapper::-webkit-scrollbar-corner,
::v-deep .el-table__body-wrapper::-webkit-scrollbar-track {
  background-color: transparent;
}
.module-header {
  width: 100%;
  // padding-left: 50px;
  background: url("~@/assets/images/qhdsys/bg-bt.png") no-repeat;
}
.order-more {
  width: 24px;
  height: 24px;
  margin-right: 0 !important;
  margin-left: 8px;
}
</style>
