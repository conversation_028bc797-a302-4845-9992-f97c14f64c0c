<template>
  <div class="information-details">
    <div class="sino-lists-wrapper">
      <div class="title-bar">
        <div class="module-title">
          <span>基础信息</span>
        </div>
      </div>
      <div class="content-warp">
        <base-info :detailsInfo="detailsInfo" ref="baseInfo"></base-info>
      </div>
      <div class="title-bar">
        <div class="module-title">
          <span>类别信息</span>
        </div>
      </div>
      <div class="content-warp">
        <category-info :detailsInfo="detailsInfo"></category-info>
      </div>
      <div class="title-bar">
        <div class="module-title">
          <span>生产和供应商信息</span>
        </div>
      </div>
      <div class="content-warp">
        <supplier-info :detailsInfo="detailsInfo"></supplier-info>
      </div>
      <div class="title-bar">
        <div class="module-title">
          <span>折旧信息</span>
        </div>
      </div>
      <div class="content-warp">
        <depreciation-info :detailsInfo="detailsInfo" :departmentShare="departmentShare"></depreciation-info>
      </div>
      <div class="title-bar">
        <div class="module-title">
          <span>财务信息</span>
        </div>
      </div>
      <div class="content-warp">
        <finance-info :detailsInfo="departmentTurn"></finance-info>
      </div>
      <div class="title-bar">
        <div class="module-title">
          <span>其他信息</span>
        </div>
      </div>
      <div class="content-warp">
        <other-info :detailsInfo="detailsInfo" :imgArr="imageArr"></other-info>
      </div>
    </div>
  </div>
</template>
<script>
import BaseInfo from './components/BaseInfo.vue'
import CategoryInfo from './components/CategoryInfo.vue'
import SupplierInfo from './components/SupplierInfo.vue'
import DepreciationInfo from './components/DepreciationInfo.vue'
import financeInfo from './components/financeInfo.vue'
import OtherInfo from './components/OtherInfo.vue'
import { getEquimentInfo, assetsExtendsInfoView } from '@/utils/equipmentApi'
export default {
  name: 'equipmentInfo',
  components: {
    BaseInfo,
    CategoryInfo,
    SupplierInfo,
    DepreciationInfo,
    financeInfo,
    OtherInfo
  },
  props: {
    deviceId: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      detailsInfo: {}, // 复制和编辑时详情信息
      departmentTurn: {},
      pageLoading: false,
      imageArr: [],
      departmentShare: []
    }
  },
  methods: {
    getAssetsInfoView(id) {
      getEquimentInfo({ id: id }).then((res) => {
        if (res.data.code === '200') {
          this.detailsInfo = res.data.data[0]
          if (res.data.data[0].pictureView) res.data.data[0].pictureView.split(',').forEach((res) => this.imageArr.push(res))
        }
      })
    },
    getAssetsChange(id) {
      assetsExtendsInfoView({ assetsId: id }).then((res) => {
        if (res.data.code === '200') {
          this.departmentTurn = res.data.data
          if (res.data.data.useDepartmentName) {
            res.data.data.useDepartmentName.split(',').forEach((item, index) => {
              this.departmentShare.push({
                value: res.data.data.officeResidualRatio.split(',')[index],
                name: item
              })
            })
            console.log('分摊', this.departmentShare)
          }
        }
      })
    }
  },
  created() {
    // this.getAssetsInfoView({
    //   id: this.$route.query.id
    // })
    const id = this.deviceId
    this.getAssetsInfoView(id)
    this.getAssetsChange(id)
  }
}
</script>

<style lang="scss" scoped>
.information-details {
  margin: 0 auto;
  width: 96%;
  .sino-lists-wrapper {
    .title-bar {
      width: calc(100% - 12px);
      padding-left: 12px;
      height: 32px;
      line-height: 32px;
      background-color: #263057;
      border-radius: 5px;
      .module-title {
        font-size: 14px;
        color: #ffe3a6;
      }
    }
    .content-warp {
      width: calc(100% - 100px);
      padding: 16px 50px;
      .info {
        width: 100%;
        height: 50px;
      }
    }
  }
}
</style>
