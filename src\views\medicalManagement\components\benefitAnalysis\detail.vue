<template>
  <div class="list-index-view">
    <div class="view-header-box">
      <div class="nav-box" :class="{ active: item.value == navIdx }" v-for="(item, index) in navOptions" :key="index"
        @click="handleNavIdx(item.value)">
        <div class="nav-div">{{ item.label }}</div>
      </div>
    </div>
    <div class="view-search-box">
      <div class="search-left">
        <el-form :model="searchForm" class="search-form" inline ref="formRef">
          <el-form-item>
            <el-date-picker v-model="searchForm.year" type="year" placeholder="2024" size="small">
            </el-date-picker>
          </el-form-item>
          <el-form-item>
            <el-select v-model="searchForm.dept" placeholder="使用科室" size="small">
              <el-option v-for="item in deptOptions" :key="item.value" :label="item.label" :value="item.value">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item v-if="navIdx==='2'||navIdx==='3'">
            <el-select v-model="searchForm.assetsType" placeholder="设备类型" size="small">
              <el-option v-for="item in assetsTypeOptions" :key="item.value" :label="item.label" :value="item.value">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item v-if="navIdx==='3'">
            <el-select v-model="searchForm.statisticalDimension" placeholder="统计维度" size="small">
              <el-option v-for="item in statisticalDimensionOptions" :key="item.value" :label="item.label"
                :value="item.value">
              </el-option>
            </el-select>
          </el-form-item>
        </el-form>
      </div>
      <div class="search-right">
        <el-button class="sino-button-sure" @click="resetForm">重置</el-button>
        <el-button class="sino-button-sure" @click="handleSearchForm">查询</el-button>
      </div>
    </div>
    <div class="view-content">
      <component :is="activeComponent"></component>
    </div>
  </div>
</template>
  <script>
export default {
  name: 'benefitAnalysisDetail',
  components: {
    deptComponent: () => import("./deptComponent.vue"),
    assetsTypeComponent: () => import("./assetsTypeComponent.vue"),
    singleDeviceComponent: () => import("./singleDeviceComponent.vue"),
  },
  data() {
    return {
      activeComponent: "deptComponent",
      deptOptions: [],
      assetsTypeOptions: [],
      statisticalDimensionOptions: [{
        label: '年度统计',
        value: '1',
      }],
      navIdx: '1',
      searchForm: {
        year: '',//年
        dept: '',//借用科室
        assetsType: '',//选择设备
        statisticalDimension: '',//统计维度
      },
      navOptions: [
        { label: "科室投资收益", value: "1", component: 'deptComponent' },
        { label: "设备分型收益", value: "2", component: 'assetsTypeComponent' },
        { label: "单设备收益", value: "3", component: 'singleDeviceComponent' },
      ],
    };
  },
  mounted() {
    this.init()
  },
  methods: {
    //初始化
    init() {

    },
    //tab切换
    handleNavIdx(val) {
      this.navIdx = val;
      this.activeComponent = this.navOptions.find((v) => v.value == val).component
    },
    /** 重置 */
    resetForm() {
      this.$refs.formRef.resetFields()
      this.init()
    },
    /** 查询 */
    handleSearchForm() {
      this.$refs.formRef.resetFields()
      this.init()
    },
  },
};
</script>
<style lang="scss" scoped>
.list-index-view {
  position: relative;
  height: 100%;
  width: 100%;
  padding: 0px 40px;
}
.view-header-box {
  height: 48px;
  line-height: 48px;
  margin-bottom: 1rem;
  display: flex;
  align-items: center;
  justify-content: center;
  box-sizing: border-box;
  .nav-box {
    height: 2.5rem;
    line-height: 2.5rem;
    width: 121px;
    font-size: 1rem;
    text-align: center;
    color: #a4afc1;
    cursor: pointer;
    box-sizing: border-box;
    border: 1px dashed #26314f;
    border-right: none;
    background: url(@/assets/images/nav-bg.png) no-repeat;
    background-size: cover;
  }
  .nav-box:last-child {
    border-right: 1px dashed #26314f;
  }
  .active {
    color: #b0e3fa;
    background: url(@/assets/images/nav-bg-xz.png) no-repeat;
    background-size: cover;
  }
}
.view-search-box {
  display: flex;
  justify-content: space-between;
}
.view-content {
  height: calc(100% - 7rem);
}
</style>
