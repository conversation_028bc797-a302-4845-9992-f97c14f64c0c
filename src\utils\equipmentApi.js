/*
 * @Author: hedd
 * @Date: 2023-07-04 14:24:16
 * @LastEditTime: 2023-09-02 12:00:38
 * @FilePath: \ihcrs_client_iframe\src\utils\equipmentApi.js
 * @Description:
 */
import http from './http'
const ieioApi = __PATH.VUE_APP_IEMS_IOMS_API
const iemsApi = __PATH.VUE_APP_IEMS_API
const moveApi = __PATH.VUE_APP_MOVE_API
// const moveBackuopsApi = __PATH.VUE_APP_MOVE_BACKUPS_API
// const imasApi = __PATH.VUE_APP_IMAS_API
// 获取指定设备的维修概况
export function getEquimentMaintenance(params) {
  return http.postParamsQS(`${ieioApi}/deviceRepair/getDeviceRepairProfiles`, params, {}, 'imesCode')
}
// const adminInfo = {
//   unitCode: 'BJSYYGLJ',
//   hospitalCode: 'BJSJ<PERSON>',
//   userId: 'd6aeca8128fa400c8f60f9c69402d0b3',
//   userName: '系统管理员',
//   roleCode: 'BJSJTY_systemAdminCode_IMES_CORE',
//   officeCode: '8afe0f36c9ea4e46a35f2c00bc2929cb',
//   sysIdentity: 'systemAdminCode',
//   departmentAssetAdminCode: '8afe0f36c9ea4e46a35f2c00bc2929cb',
//   moduleIdentity: 'IMES_CORE',
//   sysCome: 2,
//   userType: 1,
//   sysFlag: 'imes'
// }
// if (__PATH.VUE_APP_HTTP_TYPE && __PATH.VUE_APP_HTTP_TYPE === 'Intranet') {
//   adminInfo = {
//     unitCode: 'BJSYYGLJ',
//     hospitalCode: 'BJSJTY',
//     userId: 'd6aeca8128fa400c8f60f9c69402d0b3',
//     userName: '系统管理员',
//     roleCode: 'BJSJTY_systemAdminCode_IMES_CORE',
//     officeCode: '8afe0f36c9ea4e46a35f2c00bc2929cb',
//     sysIdentity: 'systemAdminCode',
//     departmentAssetAdminCode: '8afe0f36c9ea4e46a35f2c00bc2929cb',
//     moduleIdentity: 'IMES_CORE',
//     sysCome: 2,
//     userType: 1,
//     sysFlag: 'imes'
//   }
// }
// 获取实时设备路径
export function getAssetsPosition(params) {
  return http.postRequest(
    `${moveApi}/iotLabel/getAssetsPosition`,
    params,
    {
      'Content-Type': 'application/json;charset=UTF-8'
    },
    'imes'
  )
}
// 获取设备路径
export function getAssetsMovePath(params) {
  return http.postRequest(
    `${moveApi}/iotLabel/getAssetsMovePath`,
    params,
    {
      'Content-Type': 'application/json;charset=UTF-8'
    },
    {},
    'imes'
  )
}
// 获取设备列表
export function trackedAssets(params) {
  return http.postRequest(
    `${moveApi}/iotLabel/trackedAssets`,
    params,
    {
      'Content-Type': 'application/json;charset=UTF-8'
    },
    'imes'
  )
}
// 获取设备列表
// export function trackedAssets(params) {
//   return http.postParamsRequest(
//     `${moveApi}/iotLabel/trackedAssets`,
//     params,
//     {
//       'Content-Type': 'application/json;charset=UTF-8'
//     },
//     {},
//     'imes'
//   )
// }
// 获取设备列表
export function getSearchLabelCard(params) {
  return http.postRequest(
    `${moveApi}/iotLabel/searchLabelCard`,
    params,
    {
      'Content-Type': 'application/json;charset=UTF-8'
    },
    'imes'
  )
}
// 获取指定设备的信息
export function getEquimentInfo(params) {
  return http.postParamsQS(`${iemsApi}/assetsInfo/view`, params, {}, 'imes')
}
// 获取指定设备的标签信息
export function getEquimentTag(params) {
  return http.postParamsQS(`${iemsApi}/QRCode/getAssetsLabelList`, params, {}, 'imes')
}
// 获取资产转科详情
export function assetsExtendsInfoView(params) {
  return http.postParamsQS(`${iemsApi}/assetsExtendsInfo/view`, params, {}, 'imes')
}
// 获取档案分类
export function getArchivesType(params) {
  return http.postParamsQS(`${iemsApi}/assetsFileRecord/getAssetsFileType`, params, {}, 'imes')
}
// 获取档案列表
export function getArchivesList(params) {
  return http.postParamsQS(`${iemsApi}/assetsFileRecord/listData`, params, {}, 'imes')
}
// 获取保养详情
export function getUpkeeDetail(params) {
  return http.postParamsQS(`${iemsApi}/inspectionRecordController/viewDetails`, params, {}, 'imes')
}
