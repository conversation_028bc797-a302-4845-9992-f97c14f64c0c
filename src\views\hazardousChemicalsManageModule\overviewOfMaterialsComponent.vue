<template>
  <div class="inspectionComponent">
    <div class="module-container" style="height: calc(40%)">
      <div class="module-header">
        <div class="title-left">
          <p class="title-left-text">物资存量排行</p>
        </div>
        <div slot="title-right" class="title-right">
          <el-dropdown trigger="click" @command="(val) => (typeActive = val)">
            <span class="el-dropdown-link"> {{ typeList.find((v) => v.value == typeActive)?.text ?? '' }} <i class="el-icon-arrow-down"></i> </span>
            <el-dropdown-menu slot="dropdown">
              <el-dropdown-item v-for="item in typeList" :key="item.value" :command="item.value" :class="{ isBjxl: typeActive == item.value }">{{ item.text }}</el-dropdown-item>
            </el-dropdown-menu>
          </el-dropdown>
          <span class="viewMore" @click="isDialog = true"></span>
        </div>
      </div>
      <div class="module-content " style="height: calc(100% - 44px)">
        <div id="materialRanking" style="height: 100%; overflow: hidden"></div>
      </div>
    </div>
    <div class="module-container" style="height: calc(60%)">
      <div class="module-header">
        <div class="title-left">
          <p class="title-left-text">库房列表</p>
        </div>
        <div class="title-right">
          <div class="title-detail" @click="allUnfold">{{ isOpenWarehouseList ? '全部收起' : '全部展开' }}</div>
        </div>
      </div>
      <div class="module-content" style="height: calc(100% - 44px);overflow: auto;">
        <div v-if="!listData || !listData.length" class="empty-div"><span>暂无数据</span></div>
        <div v-for="(item,index) in listData" :key="index" class="device-list-item">
          <div class="item-title" @click="tabledblClick(item)">
            <span style="flex: 1; margin-right: 10px">{{ item.warehouseName || '-' }}</span>
            <span class="title-detail" @click.stop="isUnfoldTableData(index)">
              {{ item.isOpenWarehouseTableData ? '收起' : '展开' }}</span>
          </div>
          <div class="item-info">
            <div class="item-info-box">
              <p class="item-info-box-name">库存数量：</p>
              <p class="item-info-box-value">{{ item.inventory || 0 }}</p>
            </div>
            <div class="item-info-box">
              <p class="item-info-box-name">库房位置：</p>
              <p class="item-info-box-value">{{ item.warehouseAddress  || '-'}}</p>
            </div>
            <div v-show="item.isOpenWarehouseTableData" class="item-info-box">
              <p class="item-info-box-name">管理员：</p>
              <p class="item-info-box-value">{{ item.manageName || '-' }}</p>
            </div>
            <div v-show="item.isOpenWarehouseTableData" class="item-info-box">
              <p class="item-info-box-name">联系电话：</p>
              <p class="item-info-box-value">{{ item.managePhone || '-' }}</p>
            </div>
          </div>
          <div v-show="item.isOpenWarehouseTableData" class="item-table">
            <el-table
              class="table-center-transfer"
              :data="item.detail"
              height="100%"
              :cell-style="{ padding: ' 8px', backgroundColor: 'transparent', border: 'none', padding: '3px' }"
              :header-cell-style="{ background: '#8591CE26!important', color: '#8BDDF5FF', padding: '4px 8px', fontWeight: 'bold' }"
              style="width: 100%"
            >
              <el-table-column prop="materialName" label="危化品名称" width="100" show-overflow-tooltip></el-table-column>
              <el-table-column prop="materialTypeName" label="所属类型" show-overflow-tooltip></el-table-column>
              <el-table-column prop="inventory" label="库存数量" show-overflow-tooltip></el-table-column>
              <el-table-column prop="model" label="规格" width="70" show-overflow-tooltip></el-table-column>
              <el-table-column prop="maxStock" label="存储标准" show-overflow-tooltip>
                <template slot-scope="scope">
                  {{ scope.row.minStock + '-' + scope.row.maxStock}}
                </template>
              </el-table-column>
            </el-table>
          </div>
        </div>
      </div>
    </div>
    <hazMatListDialog v-if="isDialog" :isDialog="isDialog" :roomData="roomData" @close="isDialog = false" />
  </div>
</template>
<script >
import { GetMaterialRankingList, GetWarehouseList } from '@/utils/spaceManage'
import * as echarts from 'echarts'
export default {
  name: 'overviewOfMaterialsComponent',
  components: {
    hazMatListDialog: () => import('./components/hazMatListDialog')
  },
  props: {
    roomData: {
      type: Object,
      default: () => { }
    }
  },
  data() {
    return {
      isDialog: false,
      typeActive: 1,
      typeList: [
        { text: '库房', value: 1 },
        { text: '危化品类型', value: 2 }
      ],
      tableLoading: false,
      listData: [],
      isOpenWarehouseList: false
    }
  },
  watch: {
    isDialog(val) {
      try {
        window.chrome.webview.hostObjects.sync.bridge.ShowDialog(val)
      } catch (error) {}
    },
    typeActive(val) {
      this.getMaterialRankingList()
    },
    // 监听模型层级数据变化
    roomData: {
      handler(newVal, oldVal) {
        this.listData = []
        this.getMaterialRankingList()
        this.getWarehouseList()
      },
      deep: true
    }
  },
  mounted() {
    this.getMaterialRankingList()
    this.getWarehouseList()
  },
  methods: {
    tabledblClick(item) {
      this.$emit('roomEvent', {
        type: 'move',
        assetId: item.warehouseId,
        assetName: item.warehouseName,
        modelCode: item.gridCode
      })
      try {
        window.chrome.webview.hostObjects.sync.bridge.EquipmentRealView(JSON.stringify({
          assetsId: item.warehouseId,
          DeviceName: item.warehouseName,
          spaceId: item.gridCode
        }))
      } catch (error) {}
    },
    // 全部展开收起
    allUnfold() {
      this.isOpenWarehouseList = !this.isOpenWarehouseList
      this.listData.forEach(item => {
        item.isOpenWarehouseTableData = !item.isOpenWarehouseTableData
      })
    },
    // 展开收起tableData数据
    isUnfoldTableData(index) {
      this.listData[index].isOpenWarehouseTableData = !this.listData[index].isOpenWarehouseTableData
    },
    // 库房列表
    getWarehouseList() {
      let params = {
        gridCode: this.roomData.ssmCodes?.split(',')?.at(-1)
      }
      this.tableLoading = true
      GetWarehouseList(params).then(res => {
        this.tableLoading = false
        if (res.data.code == 200) {
          res.data.data.forEach(item => {
            item.isOpenWarehouseTableData = false
          })
          this.listData = res.data.data
        }
      }).catch(() => {
        this.tableLoading = false
      })
    },
    // 物资排行
    getMaterialRankingList() {
      let params = {
        gridCode: this.roomData.ssmCodes?.split(',')?.at(-1),
        type: this.typeActive
      }
      GetMaterialRankingList(params).then(res => {
        if (res.data.code == 200) {
          this.rankingChart(res.data.data.map(item => {
            return {
              name: item.warehouseName,
              value: item.inventory
            }
          }))
        } else {
          this.rankingChart([])
        }
      }).catch(() => {
        this.rankingChart([])
      })
    },
    rankingChart(data) {
      // 基于准备好的dom，初始化echarts实例
      let EChart = echarts.init(document.getElementById('materialRanking'))
      //   将data数据中得value值从大到小排序，然后按顺序将value放入dataValue数组中 name放入dataName数组中 name和value一一对应
      data.sort(function (a, b) {
        return a.value - b.value
      })
      const dataName = Array.from(data, (item) => {
        item.name = item.name || '未命名'
        const match = item.name.match(this.regex)
        const name = item.name
        return {
          value: name,
          textStyle: {
            color: '#FFF'
          }
        }
      })
      // 配置参数
      let option
      if (data.length) {
        option = {
          legend: {},
          grid: {
            top: '5%',
            left: '3%',
            right: '8%',
            bottom: '0%',
            containLabel: true
          },
          xAxis: {
            type: 'value',
            show: false
          },
          yAxis: [
            {
              type: 'category',
              data: dataName,
              axisTick: {
                show: false // 不显示坐标轴刻度线
              },
              axisLine: {
                show: false // 不显示坐标轴线
              },
              splitLine: {
                // 网格线
                show: false
              },
              axisLabel: { textStyle: { color: '#fff', fontSize: '14' } }
            },
            {
              type: 'category', // 坐标轴类型
              // inverse: true, // 是否是反向坐标轴
              axisTick: {
                show: false // 不显示坐标轴刻度线
              },
              axisLine: {
                show: false // 不显示坐标轴线
              },
              splitLine: {
                // 网格线
                show: false
              },
              axisLabel: {
                textStyle: {
                  color: '#FFFFFFCC',
                  fontSize: '14'
                },
                formatter: (value) => {
                  return value
                }
              },
              data: data
            }
          ],
          series: [
            {
              type: 'bar',
              label: {
                show: false
              },
              emphasis: {
                focus: 'series'
              },
              data: data,
              barWidth: 8,
              itemStyle: {
                normal: {
                  // barBorderRadius: [15, 15, 15, 15],
                  color: function (params) {
                    // 通过返回值的下标一一对应将颜色赋给柱子上
                    return '#0A84FF'
                  }
                },
                // 鼠标移入改变颜色
                emphasis: {
                  color: '#FFCA64FF'
                }
              },
              showBackground: true, // 显示背景色
              backgroundStyle: {
                color: '#384156'
              }
            }
          ],
          dataZoom: [
            {
              yAxisIndex: [0, 1],
              orient: 'vertical',
              show: data.length > 5,
              type: 'slider',
              start: 100,
              // end: 100,
              width: 8,
              left: '99%',
              borderColor: 'rgba(43,48,67,.1)',
              fillerColor: '#6580b8',
              zoomLock: true,
              showDataShadow: false,
              backgroundColor: 'rgba(43,48,67,.1)',
              showDetail: false,
              // realtime: true,
              filterMode: 'filter',
              handleIcon: 'M-9.35,34.56V42m0-40V9.5m-2,0h4a2,2,0,0,1,2,2v21a2,2,0,0,1-2,2h-4a2,2,0,0,1-2-2v-21A2,2,0,0,1-11.35,9.5Z',
              handleStyle: {
                color: '#6580b8',
                borderColor: '#6580b8'
              },
              maxValueSpan: 4,
              minValueSpan: 4,
              brushSelect: false
            },
            {
              type: 'inside',
              show: false,
              yAxisIndex: [0, 1],
              height: 8
            }
          ]
        }
      } else {
        option = {
          title: {
            text: '暂无数据',
            x: 'center',
            y: 'center',
            textStyle: {
              fontSize: 14,
              fontWeight: 'normal',
              color: '#4d5880'
            }
          }
        }
      }
      EChart.clear()
      // 设置参数
      EChart.setOption(option)
      // 先移除点击事件 解决点击事件重复绑定
      // EChart.getZr().off('click')
      // // 点击事件
      // EChart.getZr().on('click', (params) => {
      //   var pointInPixel = [params.offsetX, params.offsetY]
      //   if (EChart.containPixel('grid', pointInPixel)) {
      //     // 获取当前点击的索引值
      //     // 注意：若柱状图为纵向则获取x轴的索引，若柱状图为横向则需获取y轴的索引(见下方注释)
      //     // var xIndex=EChart.convertFromPixel({seriesIndex:0},[params.offsetX, params.offsetY])[0];
      //     // var xData=option.xAxis.data[xIndex];//当前点击柱子的数据
      //     var yIndex = EChart.convertFromPixel({ seriesIndex: 0 }, [params.offsetX, params.offsetY])[1]
      //     var yData = option.yAxis[1].data[yIndex] // 当前点击柱子的数据
      //     // 新增变量 判断当前点击柱状图是选中还是取消选中
      //     let isSelected = false
      //     dataName.map((e, i) => {
      //       // 选中的设置选中色
      //       if (i == yIndex && e.textStyle.color != '#FFCA64FF') {
      //         e.textStyle.color = '#FFCA64FF'
      //       } else {
      //         // 选中已选中的则为取消选中
      //         if (i == yIndex && e.textStyle.color == '#FFCA64FF') {
      //           isSelected = true
      //         }
      //         // 其他的设为默认色
      //         e.textStyle.color = '#FFF'
      //       }
      //     })
      //     option.yAxis[0].data = JSON.parse(JSON.stringify(dataName))
      //     option.dataZoom[0].start = (yIndex / dataName.length) * 100
      //     EChart.setOption(option)
      //   }
      // })
    },
    allTableChange() {

    }
  }
}
</script>

  <style lang="scss" scoped>
@import "./style/module.scss";
.inspectionComponent {
  width: 100%;
  height: 100%;
  .el-dropdown-link {
    color: #fff !important;
    margin-left: 10px;
  }
  .title-left {
    padding-left: 20px;
  }
  .chartContent {
    padding: 16px 0;
    height: calc(100% - 44px);
    display: flex;
    width: 100%;
  }
}
.module-header {
  width: 100%;
  // padding-left: 50px;
  background: url("~@/assets/images/qhdsys/bg-bt.png") no-repeat;
  .title-right {
    align-items: center;
  }
  .el-dropdown-link {
    font-size: 14px;
    font-weight: 300;
    color: #ffffff;
    line-height: 16px;
    .el-icon-arrow-down {
      font-size: 12px;
    }
  }
  .viewMore {
    display: inline-block;
    width: 24px;
    height: 24px;
    margin-left: 10px;
    background: url('@/assets/images/qhdsys/bg-gd.png') no-repeat center / 100% 100%;
  }
}
.order-more {
  width: 24px;
  height: 24px;
  margin-right: 0 !important;
  margin-left: 8px;
}
.title-detail {
  cursor: pointer;
  font-size: 14px;
  font-family: HarmonyOS Sans SC-Regular, HarmonyOS Sans SC;
  font-weight: 400;
  color: #8bddf5 !important;
  margin-right: 13px;
}

.device-list-item {
  padding-top: 16px;
  .item-title {
    // background: rgba(133, 145, 206, 0.15);
    font-size: 15px;
    font-weight: 500;
    color: #ffffff;
    line-height: 18px;
    padding: 7px 8px;
    display: flex;
    align-items: center;
    cursor: pointer;
  }
  .item-info {
    display: flex;
    flex-wrap: wrap;
    padding: 8px 0;
    line-height: 20px;
    .item-info-box {
      width: calc(100% / 2);
      text-align: center;
      display: flex;
      padding: 8px 10px;
      font-size: 14px;
      font-weight: 400;
      color: #ffffff;
      display: flex;
    }
    .item-info-box-name {
      width: 70px;
    }
    .item-info-box-value {
      flex: 1;
      margin-left: 2px;
      text-align: left;
    }
  }
}
.empty-div {
  width: 100%;
  height: 100%;
  display: flex;
  > span {
    font-size: 14px;
    color: #999;
    margin: auto;
  }
}
</style>
