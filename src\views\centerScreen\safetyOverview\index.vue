<template>
  <div class="content">
    <div class="left-content" ref="leftContent" :style="{ 'justify-content': activeType === 'risk' ? 'flex-start' : 'space-between' }">
      <div class="echarts-left-top" :class="activeType === 'risk' ? 'risk_left_top' : ''">
        <div class="bg-title">
          <span>安全运行评分</span><span class="center-legend"></span
          ><span class="right-title"
            >已安全运行 <span>{{ runScoreData.runDay || 0 }}</span> 天</span
          >
        </div>
        <div class="bg-content" style="height: calc(100% - 2.5rem)">
          <div class="operation_score">
            <div style="width: 180px; height: 180px" class="runScore animationBox">
              <div class="progressBox">
                <svg viewBox="0 0 100 100">
                  <defs>
                    <linearGradient id="green" x1="50%" y1="100%" x2="100%" y2="10%">
                      <stop offset="0%" style="stop-color: #0095f4; stop-opacity: 1" />
                      <stop offset="100%" style="stop-color: #00e1cf; stop-opacity: 1" />
                    </linearGradient>
                  </defs>
                  <path
                    d="
                    M 50 50
                    m 0 -45
                    a 45 45 0 1 1 0 90
                    a 45 45 0 1 1 0 -90
                    "
                    stroke="url(#green)"
                    fill="none"
                    stroke-linecap="square"
                    :stroke-width="runScoreData.runScore > 0 ? 9.4 : 0"
                    class="operation-score-progress"
                    style="stroke-dasharray: 0, 17.674rem; stroke-dashoffset: 0px; transition: stroke-dasharray 0.6s ease 0s, stroke 0s ease 0s"
                  ></path>
                </svg>
                <p>{{ runScoreData.runScore || 0 }}<span>分</span></p>
              </div>
            </div>
            <div>
              <span id="danger" @click="activeTypeEvent('danger', 'click')">隐患</span>
              <i></i>
              <span id="risk" @click="activeTypeEvent('risk', 'click')">风险</span>
            </div>
            <div>
              <div v-for="(item, index) in runScoreData.array" :key="index">
                <div class="triangle_box" :style="{background: 'url(' + item.bgd +') no-repeat center / 100%'}">
                  <p :style="{ color: item.color }">{{ item.value }}</p>
                  <p>{{ item.name }}</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="echarts-left-center" v-if="activeType === 'danger'">
        <div class="bg-title"><span>隐患状态分析</span><span class="center-legend"></span></div>
        <div class="bg-content" style="height: calc(100% - 2.5rem)">
          <div v-if="stateAnalysisShow" class="center-center">暂无数据</div>
          <div v-else style="width: 100%; height: 100%; position: relative">
            <div id="dangerStateEcharts"></div>
            <div class="pie_background"></div>
          </div>
        </div>
      </div>
      <div class="echarts-left-bottom" v-if="activeType === 'danger'">
        <div class="bg-title"><span>隐患数量走势</span><span class="center-legend"></span></div>
        <div class="bg-content" style="height: calc(100% - 2.5rem)">
          <div v-if="numAnalysisShow" class="center-center">暂无数据</div>
          <div v-else style="width: 100; height: 100%"><div id="dangerNumEchart"></div></div>
        </div>
      </div>
      <div class="echarts-left-center-risk" v-if="activeType === 'risk'">
        <div class="bg-title"><span>风险等级分析</span><span class="center-legend"></span></div>
        <div class="bg-content" style="height: calc(100% - 2.5rem)">
          <div v-if="levelAnalysisShow" class="center-center">暂无数据</div>
          <div v-else style="width: 100; height: 100%">
            <div style="position: relative" id="levelAnalysisEcharts"></div>
            <div class="alarm-bg"></div>
          </div>
        </div>
      </div>
    </div>
    <div class="right-content" ref="rightContent" :style="{ 'justify-content': activeType === 'risk' ? 'flex-start' : 'space-between' }">
      <div class="echarts-top" v-if="activeType === 'danger'">
        <div class="bg-title">
          <span>隐患巡检任务分析</span><span class="center-legend"></span
          ><span class="right-search"
            ><span @click="activeTaskEvent('#taskDay', 'day')" id="taskDay">今日</span><i>|</i><span @click="activeTaskEvent('#taskWeek', 'week')" id="taskWeek">本周</span><i>|</i
            ><span id="taskMonth" @click="activeTaskEvent('#taskMonth', 'month')">本月</span></span
          >
        </div>
        <div class="bg-content" style="height: calc(100% - 2.5rem)">
          <div>
            <div class="el-progress-circle" style="height: 10rem; width: 10rem">
              <svg viewBox="0 0 100 100">
                <defs>
                  <linearGradient id="blue" x1="50%" y1="100%" x2="100%" y2="10%">
                    <stop offset="0%" style="stop-color: rgb(0, 70, 80); stop-opacity: 1" />
                    <stop offset="100%" style="stop-color: #00ffcb; stop-opacity: 1" />
                  </linearGradient>
                </defs>
                <path
                  d="
        M 50 50
        m 0 -45
        a 45 45 0 1 1 0 90
        a 45 45 0 1 1 0 -90
        "
                  stroke="url(#blue)"
                  fill="none"
                  stroke-linecap="square"
                  :stroke-width="taskAnalysis.percentage > 0 ? 9.4 : 0"
                  class="el-progress-circle__path"
                  style="stroke-dasharray: 0, 17.674rem; stroke-dashoffset: 0px; transition: stroke-dasharray 0.6s ease 0s, stroke 0.6s ease 0s"
                ></path>
              </svg>
            </div>
            <p>{{ taskAnalysis.accomplishCount || 0 }}<span>已巡</span></p>
          </div>
          <div class="alarm-analysis">
            <div class="case-img">
              <img src="../../../assets/images/peace/bg-rate.png" />
              <div class="case-num">
                <p>{{ taskAnalysis.percentage || 0 }}%</p>
                <p style="font-size: 0.75rem; margin-top: 0.8rem; color: #cfe1f7">巡检完成率</p>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="echarts-center" v-if="activeType === 'danger'">
        <div class="bg-title">
          <span>部门隐患巡检分析</span><span class="center-legend"></span
          ><span class="right-search"
            ><span @click="activeDeptDangerEvent('#deptDay', 'day')" id="deptDay">今日</span><i>|</i><span @click="activeDeptDangerEvent('#deptWeek', 'week')" id="deptWeek">本周</span><i>|</i
            ><span id="deptMonth" @click="activeDeptDangerEvent('#deptMonth', 'month')">本月</span></span
          >
        </div>
        <div class="bg-content" style="height: calc(100% - 2.5rem)">
          <el-table
            class="table-center-transfer"
            :data="tableData"
            height="100%"
            :cell-style="$tools.setCell(3)"
            :header-cell-style="$tools.setHeaderCell(3)"
            style="width: 100%"
            element-loading-background="rgba(0, 0, 0, 0.2)"
            v-loading="tableLoading"
          >
            <el-table-column fixed prop="teamName" show-overflow-tooltip label="部门名称"></el-table-column>
            <el-table-column fixed prop="count" show-overflow-tooltip label="任务数"></el-table-column>
            <el-table-column fixed prop="timeoutCount" min-width="90" show-overflow-tooltip label="逾期未巡检"></el-table-column>
            <el-table-column
              fixed
              prop="percentage"
              show-overflow-tooltip
              label="完成率"
              :formatter="
                (row) => {
                  return row.percentage + '%'
                }
              "
            ></el-table-column>
          </el-table>
        </div>
      </div>
      <div class="echarts-bottom" v-if="activeType === 'danger'">
        <div class="bg-title"><span>隐患类型分析</span><span class="center-legend"></span></div>
        <div class="bg-content" style="height: calc(100% - 2.5rem)">
          <div v-if="typeAnalysisShow" class="center-center">暂无数据</div>
          <div v-else style="width: 100%; height: 100%;">
            <div id="dangerAnalysisEcharts"></div>
          </div>
        </div>
      </div>
      <div class="echarts-top-risk" v-if="activeType === 'risk'">
        <div class="bg-title"><span>风险类型分析</span><span class="center-legend"></span></div>
        <div class="bg-content" style="height: calc(100% - 2.5rem)">
          <div v-if="riskTypeAnalysisShow" class="center-center">暂无数据</div>
          <div v-else style="width: 100%; height: 100%">
            <div id="riskTypeAnalysisEcharts"></div>
          </div>
        </div>
      </div>
      <div class="echarts-center-risk" v-if="activeType === 'risk'">
        <div class="bg-title"><span>风险巡查任务分析</span><span class="center-legend"></span></div>
        <div class="bg-content" style="height: calc(100% - 2.5rem)">
          <div>
            <div class="inspection"><span></span>今日巡检</div>
            <div class="ins-echart-box">
              <div id="dayInspectionEcharts"></div>
              <p>{{ inspectionInfo.day.accomplishCount || 0 }}<span>已巡</span></p>
            </div>
            <div class="inspection-legend">
              <p><i></i> 今日已巡查</p>
              <span>{{ inspectionInfo.day.percentage || 0 }}%</span>
            </div>
          </div>
          <div>
            <div class="inspection"><span></span>本周巡检</div>
            <div class="ins-echart-box">
              <div id="weekInspectionEcharts"></div>
              <p>{{ inspectionInfo.week.accomplishCount || 0 }}<span>已巡</span></p>
            </div>
            <div class="inspection-legend">
              <p><i></i> 本周已巡查</p>
              <span>{{ inspectionInfo.week.percentage || 0 }}%</span>
            </div>
          </div>
          <!-- <div v-if="riskTypeAnalysisShow" class="center-center">暂无数据</div>
          <div v-else style="width: 100%; height: 100%">
            <div id="riskTypeAnalysisEcharts"></div>
          </div> -->
        </div>
      </div>
    </div>
    <!-- <el-button class="sino-button-sure" @click="hazardListChange('')">showDialog</el-button> -->
    <template v-if="hazardListShow">
      <hazardList ref="hazardList" :type="activeType" :riskCode="riskCode" :dialogShow="hazardListShow" @configCloseDialog="hazardListChange"></hazardList>
    </template>
  </div>
</template>
<script>
import $ from 'jquery'
import * as echarts from 'echarts'
import 'echarts-gl'
// import { getPie3D } from '@/assets/common/pie-event'
import {
  GetDangerRunScore,
  GetStateStatistics,
  GetHiddenDangerNumberAnalysis,
  GetTaskAnalysis,
  GetInspectionAnalysisList,
  GetHiddenDangerTypeAnalysis,
  GetRiskRunScore,
  GetRiskLevelStatistics,
  GetRiskTypeStatistics,
  GetInspectionTaskAnalysis
} from '@/utils/centerScreenApi'
import hazardList from './components/hazardList'
export default {
  name: 'safetyOverview',
  components: {
    hazardList
  },
  data() {
    return {
      runScoreData: {}, // 安全运行评分
      taskAnalysis: {}, // 隐患巡检任务分析
      inspectionInfo: {
        day: {},
        week: {}
      }, // 风险巡查任务分析
      hazardListShow: false, // 隐患清单
      tableData: [],
      tableLoading: false,
      stateAnalysisShow: true,
      numAnalysisShow: true,
      typeAnalysisShow: true,
      levelAnalysisShow: true,
      riskTypeAnalysisShow: true,
      activeType: '',
      riskCode: '',
      timer: null,
      getchart: null
    }
  },
  mounted() {
    this.activeTypeEvent('danger')
    this.timer = setInterval(() => {
      setTimeout(() => {
        this.activeTypeEvent(this.activeType)
      }, 0)
    }, 1000 * timerNum)
    this.$once('hook:beforeDestroy', () => {
      clearInterval(this.timer)
      this.timer = null
    })
    try {
      window.chrome.webview.addEventListener('message', (event) => {
        const data = JSON.parse(event.data)
        if (data.type === 'tag') {
          const params = {}
          if (this.activeType === 'danger') {
            params.riskCode = data.riskCode
          }
          params.placeIds = data.localtion
          this.hazardListChange(params)
        }
      })
    } catch (error) {}
  },
  methods: {
    // ```````````````````````````````````````````````````````````````````danger
    // 安全运行评分
    getDangerRunScore() {
      this.runScoreData = {}
      const newObj = {
        重点隐患: { color: '#FFFFFF', bgd: require('@/assets/images/peace/score.png') },
        一般隐患: { color: '#FFFFFF', bgd: require('@/assets/images/peace/score.png') }
      }
      GetDangerRunScore().then((res) => {
        if (res.data.code === '200') {
          res.data.data.array.forEach((item) => {
            item.color = newObj[item.name].color
            item.bgd = newObj[item.name].bgd
          })
          this.runScoreData = res.data.data
          setTimeout(() => {
            $('.operation-score-progress').css({
              'stroke-dasharray': `${17.674 * ((this.runScoreData.runScore || 0) / 100) - 0.54}rem, 17.674rem`
            })
          }, 250)
        }
      })
    },
    // 隐患清单
    hazardListChange(params) {
      this.hazardListShow = !this.hazardListShow
      this.$nextTick().then(() => {
        if (this.hazardListShow) {
          this.$refs.leftContent.style.width = 0
          this.$refs.rightContent.style.width = 0
          this.$refs.hazardList.getWorkOrderTableData(params)
        } else {
          this.$refs.leftContent.style.width = '20%'
          this.$refs.rightContent.style.width = '20%'
        }
        try {
          window.chrome.webview.hostObjects.sync.bridge.ShowTable(this.hazardListShow)
        } catch (error) {}
      })
    },
    // 获取隐患状态分析数据
    getStateStatistics() {
      GetStateStatistics().then((res) => {
        if (res.data.code === '200') {
          if (res.data.data.array.length) {
            this.stateAnalysisShow = false
            this.$nextTick(() => {
              this.departmentEchart(res.data.data.array)
            })
          } else {
            this.stateAnalysisShow = true
          }
        }
      })
    },
    // 隐患巡检任务分析
    getTaskAnalysis(val) {
      GetTaskAnalysis({ dateType: val }).then((res) => {
        if (res.data.code === '200') {
          this.taskAnalysis = res.data.data.list[0]
          // 隐患巡检任务分析 进度条赋值
          setTimeout(() => {
            $('.el-progress-circle__path').css({
              'stroke-dasharray': `${17.674 * (this.taskAnalysis.percentage / 100) - 0.54}rem, 17.674rem`
            })
          }, 250)
        }
      })
    },
    // 隐患状态分析图渲染
    departmentEchart(arr) {
      const getchart = echarts.init(document.getElementById('dangerStateEcharts'))
      getchart.resize()
      // const option = arr.map((e) => {
      //   return {
      //     name: e.name,
      //     value: e.value
      //   }
      // })
      // const Doption = getPie3D(option, 1.7)
      // Doption.grid3D.top = '-22'
      // Doption.grid3D.left = '0'
      // Object.assign(Doption.legend, {
      //   bottom: 10,
      //   left: 'center',
      //   orient: 'horizontal'
      // })
      // delete Doption.legend.top
      const valueList = Array.from(arr, (item) => item.value)
      const nameList = Array.from(arr, (item) => item.name)
      const data = []
      var color = ['rgba(31, 250, 255, 0.3)', 'rgba(255, 227, 166, 0.28)', 'rgba(147, 130, 255, 0.26)', 'rgba(0, 248, 114, 0.24)', 'rgba(46, 119, 251, 0.3)']
      var borderColor = ['rgba(31, 250, 255, 1)', 'rgba(255, 227, 166, 1)', 'rgba(147, 130, 255, 1)', 'rgba(0, 248, 114, 1)', 'rgba(46, 119, 251, 1)']
      for (var i = 0; i < nameList.length; i++) {
        data.push({
          value: valueList[i],
          name: nameList[i],
          itemStyle: {
            normal: {
              borderWidth: 1,
              shadowBlur: 200,
              borderColor: borderColor[i],
              color: color[i],
              label: {
                normal: { show: true }
              }
            }
          }
        })
      }
      var seriesObj = [
        {
          name: '',
          type: 'pie',
          clockWise: false,
          center: ['50%', '50%'],
          radius: ['45%', '65%'],
          hoverAnimation: true,
          label: {
            normal: {
              show: false,
              position: 'center',
              formatter: '{value|{c}}\n{label|{b}}',
              rich: {
                value: {
                  padding: 5,
                  align: 'center',
                  verticalAlign: 'middle',
                  fontSize: 20
                },
                label: {
                  align: 'center',
                  verticalAlign: 'middle',
                  fontSize: 12
                }
              }
            },
            emphasis: {
              show: true,
              textStyle: {
                fontSize: '12',
                color: '#fff'
              }
            }
          },
          data: data
        }
      ]
      const option = {
        backgroundColor: 'rgba(128, 128, 128, 0)',
        tooltip: {
          show: false
        },
        legend: {
          orient: 'horizontal',
          left: 'center',
          bottom: '0',
          data: nameList,
          itemWidth: 8,
          itemHeight: 8,
          itemGap: 16,
          textStyle: {
            color: '#B3C2DD' //  字体颜色
          }
        },
        toolbox: {
          show: false
        },
        series: seriesObj
      }
      getchart.clear()
      getchart.setOption(option)
      // 随着屏幕大小调节图表
      window.addEventListener('resize', () => {
        getchart.resize()
      })
      getchart.dispatchAction({
        type: 'highlight',
        seriesIndex: 0,
        dataIndex: 0
      })
      getchart.on('mouseover', (e) => {
        // 取消默认高亮
        getchart.dispatchAction({
          type: 'downplay',
          seriesIndex: 0,
          dataIndex: 0
        })
      })
      // 鼠标移出后默认高亮
      getchart.on('mouseout', (e) => {
        getchart.dispatchAction({
          type: 'highlight',
          seriesIndex: 0,
          dataIndex: 0
        })
      })
    },
    // 获取隐患数量走势数据
    getHiddenDangerNumberAnalysis() {
      GetHiddenDangerNumberAnalysis().then((res) => {
        if (res.data.code === '200') {
          if (res.data.data.seriesData && res.data.data.xAxisData) {
            this.numAnalysisShow = false
            this.$nextTick(() => {
              this.getdangerNumEchart(res.data.data)
            })
          } else {
            this.numAnalysisShow = true
          }
        }
      })
    },
    // 隐患数量走势图渲染
    getdangerNumEchart(obj) {
      const getchart = echarts.init(document.getElementById('dangerNumEchart'))
      // getchart.clear()
      getchart.resize()
      var color = 'rgba(30, 243, 249'
      var lineY = []
      var data = {
        type: 'line',
        color: color + ')',
        smooth: true,
        areaStyle: {
          normal: {
            color: new echarts.graphic.LinearGradient(
              0,
              0,
              0,
              1,
              [
                {
                  offset: 0,
                  color: color + ', 0.3)'
                },
                {
                  offset: 1,
                  color: color + ', 0)'
                }
              ],
              false
            )
          }
        },
        symbolSize: 5,
        data: obj.seriesData
      }
      lineY.push(data)
      var option = {
        backgroundColor: '',
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            lineStyle: {
              color: 'rgb(30, 243, 249)'
            }
          },
          backgroundColor: '#0C2269',
          borderColor: '#5B617C',
          textStyle: {
            color: '#719DE7'
          },
          formatter: '时间：{b0}</br>数量：{c0}'
        },
        grid: {
          top: '14%',
          left: '10%',
          right: '8%',
          bottom: '20%'
        },
        xAxis: {
          type: 'category',
          boundaryGap: false,
          data: obj.xAxisData,
          axisLabel: {
            textStyle: {
              color: '#50608B'
            },
            formatter: function (params) {
              return params.substring(5).replace('-', '.')
            }
          },
          axisLine: {
            lineStyle: {
              color: '#50608B',
              width: 1
            }
          }
        },
        yAxis: {
          type: 'value',
          axisLabel: {
            formatter: '{value}',
            textStyle: {
              color: '#50608B'
            }
          },
          splitLine: {
            lineStyle: {
              color: '#2A4382'
            }
          }
        },
        dataZoom: [
          {
            xAxisIndex: 0,
            show: true,
            type: 'slider',
            startValue: 100,
            endValue: 100,
            height: 8,
            bottom: '2%',
            borderColor: 'rgba(43,48,67,.1)',
            fillerColor: '#6580b8',
            zoomLock: false,
            showDataShadow: false,
            backgroundColor: 'rgba(43,48,67,.1)',
            showDetail: false,
            realtime: true,
            filterMode: 'filter',
            handleIcon: 'M-9.35,34.56V42m0-40V9.5m-2,0h4a2,2,0,0,1,2,2v21a2,2,0,0,1-2,2h-4a2,2,0,0,1-2-2v-21A2,2,0,0,1-11.35,9.5Z',
            handleStyle: {
              color: '#6580b8',
              borderColor: '#6580b8'
            },
            moveOnMouseMove: true,
            maxValueSpan: obj.xAxisData.length,
            minValueSpan: 5,
            brushSelect: false
          },
          {
            type: 'inside',
            show: false,
            height: 8,
            start: 100,
            end: 100
          }
        ],
        series: lineY
      }
      getchart.clear()
      getchart.setOption(option)
      // 随着屏幕大小调节图表
      window.addEventListener('resize', () => {
        getchart.resize()
      })
    },
    // 部门隐患巡检分析
    getTypeAnalysisTable(val) {
      this.tableLoading = true
      GetInspectionAnalysisList({ dateType: val })
        .then((res) => {
          this.tableLoading = false
          if (res.data.code === '200') {
            if (res.data.data.list.length) {
              this.tableData = res.data.data.list
            } else {
              this.tableData = []
            }
          }
        })
        .catch(() => {
          this.tableLoading = false
        })
    },
    // 获取隐患类型分析数据
    getTypeAnalysisInfo() {
      GetHiddenDangerTypeAnalysis().then((res) => {
        this.typeAnalysisShow = false
        if (res.data.code === '200') {
          if (res.data.data.seriesData.length && res.data.data.xAxisData.length) {
            this.$nextTick(() => {
              this.dangerTypeAnalysisEchart(res.data.data)
            })
          } else {
            this.typeAnalysisShow = true
          }
        }
      })
    },
    // 隐患类型分析图渲染
    dangerTypeAnalysisEchart(obj) {
      console.log(obj)
      const getchart = echarts.init(document.getElementById('dangerAnalysisEcharts'))
      getchart.resize()
      const option = {
        backgroundColor: '',
        tooltip: {
          trigger: 'axis',
          backgroundColor: '#0D2169',
          borderColor: '#727382',
          textStyle: {
            color: '#fff'
          },
          axisPointer: {
            type: 'shadow'
          }
        },
        grid: {
          left: '5%',
          right: '5%',
          bottom: '30%',
          top: '5%'
        },
        xAxis: [
          {
            type: 'category',
            data: obj.xAxisData,
            axisTick: {
              show: false
            },
            axisLabel: {
              show: true,
              align: 'center',
              formatter: function(value) {
                var str = value.split('')
                return str.join('\n')
              },
              margin: 4,
              textStyle: {
                color: '#878EA9',
                fontSize: 12
              }
            }
          }
        ],
        yAxis: [
          {
            type: 'value',
            axisLabel: {
              show: true,
              textStyle: {
                color: '#878EA9'
              }
            },
            axisLine: {
              lineStyle: {
                type: 'solid',
                color: '#3862B7',
                width: '1'
              }
            },
            splitLine: {
              lineStyle: {
                color: '#314A89',
                width: 1,
                type: 'solid'
              }
            },
            axisTick: {
              show: false
            }
          }
        ],
        series: [
          {
            type: 'bar',
            barWidth: 7,
            itemStyle: {
              color: new echarts.graphic.LinearGradient(0, 1, 0, 0, [
                {
                  offset: 0,
                  color: 'rgba(21, 89, 159, 0.25)'
                },
                {
                  offset: 1,
                  color: 'rgba(9, 244, 196, 0.86)'
                }
              ])
            },
            data: obj.seriesData
          }
        ]
      }
      getchart.clear()
      getchart.setOption(option)
      // 随着屏幕大小调节图表
      window.addEventListener('resize', () => {
        getchart.resize()
      })
    },
    // ```````````````````````````````````````````````````````````````````risk
    // 安全运行评分
    getRiskRunScore() {
      this.runScoreData = {}
      const newObj = {
        重大风险: { color: '#FF5454', bgd: require('@/assets/images/peace/zhongdafengxian.png') },
        较大风险: { color: '#FFBA65', bgd: require('@/assets/images/peace/jiaodafengxian.png') },
        一般风险: { color: '#FFF370', bgd: require('@/assets/images/peace/yibanfengxian.png') },
        低风险: { color: '#1FFAFF', bgd: require('@/assets/images/peace/difengxian.png') }
      }
      GetRiskRunScore().then((res) => {
        if (res.data.code === '200') {
          res.data.data.array.forEach((item) => {
            item.color = newObj[item.name].color
            item.bgd = newObj[item.name].bgd
          })
          this.runScoreData = res.data.data
        }
      })
    },
    // 风险等级分析
    // 获取 风险等级分析
    getRiskLevelAnalysisInfo() {
      GetRiskLevelStatistics().then((res) => {
        if (res.data.code === '200') {
          // 报警来源分析
          if (res.data.data.array.length) {
            this.levelAnalysisShow = false
            this.$nextTick(() => {
              this.getRiskLevelEchart(res.data.data.array)
            })
          } else {
            this.levelAnalysisShow = true
          }
        }
      })
    },
    // 风险等级分析图渲染
    getRiskLevelEchart(list) {
      const getchart = echarts.init(document.getElementById('levelAnalysisEcharts'))
      getchart.resize()
      var scaleData = list
      var xdata = list.map((item) => {
        return item.name
      })
      // const xdata = ['重大风险', '较大风险', '一般风险', '低风险', '报警来源5']
      var data = []
      var colorData = {
        低风险: {
          color: 'rgba(31, 250, 255, 0.44)',
          borderColor: 'rgba(31, 250, 255, 1)'
        },
        一般风险: {
          color: 'rgba(255, 231, 84, 0.46)',
          borderColor: 'rgba(255, 231, 84, 1)'
        },
        较大风险: {
          color: 'rgba(255, 172, 84, 0.48)',
          borderColor: 'rgba(255, 172, 84, 1)'
        },
        重大风险: {
          color: 'rgba(255, 84, 84, 0.5)',
          borderColor: 'rgba(255, 84, 84, 1)'
        }
      }
      // var color = ['rgba(255, 84, 84, 0.3)', 'rgba(255, 172, 84, 0.28)', 'rgba(255, 231, 84, 0.26)', 'rgba(31, 250, 255, 0.24)', 'rgba(46, 119, 251, 0.3)']
      // var borderColor = ['rgba(255, 84, 84, 1)', 'rgba(255, 172, 84, 1)', 'rgba(255, 231, 84, 1)', 'rgba(31, 250, 255, 1)', 'rgba(46, 119, 251, 1)']
      for (var i = 0; i < scaleData.length; i++) {
        data.push({
          value: scaleData[i].value,
          // name: xdata[i],
          name: scaleData[i].name,
          itemStyle: {
            normal: {
              borderWidth: 1,
              shadowBlur: 200,
              borderColor: colorData[scaleData[i].name].borderColor,
              color: colorData[scaleData[i].name].color,
              label: {
                normal: { show: true }
              }
            }
          }
        })
      }
      var seriesObj = [
        {
          name: '',
          type: 'pie',
          clockWise: false,
          center: ['25%', '50%'],
          radius: ['40%', '60%'],
          hoverAnimation: true,
          label: {
            normal: {
              show: false,
              position: 'center',
              formatter: '{text|{b}}\n ({d}%)',
              rich: {
                value: {
                  padding: 5,
                  align: 'center',
                  verticalAlign: 'middle',
                  fontSize: 20
                },
                label: {
                  align: 'center',
                  verticalAlign: 'middle',
                  fontSize: 12
                }
              }
            },
            emphasis: {
              show: true,
              textStyle: {
                fontSize: '12',
                color: '#fff'
              }
            }
          },
          data: data
        }
      ]
      const option = {
        backgroundColor: '',
        legend: {
          orient: 'vartical',
          x: 'left',
          top: 'center',
          left: '50%',
          bottom: '0%',
          data: xdata,
          itemWidth: 8,
          itemHeight: 8,
          itemGap: 16,
          textStyle: {
            //  fontSize: 18,//字体大小
            color: '#B3C2DD' //  字体颜色
          },
          formatter: function (name) {
            var oa = option.series[0].data
            var num = oa.reduce((sum, e) => sum + e.value, 0)
            for (var i = 0; i < option.series[0].data.length; i++) {
              if (name === oa[i].name) {
                return ' ' + name + '    ' + ((oa[i].value / num) * 100).toFixed(2) + '%'
              }
            }
          }
        },
        series: seriesObj
      }
      getchart.clear()
      getchart.setOption(option)
      getchart.dispatchAction({
        type: 'highlight',
        seriesIndex: 0,
        dataIndex: 0
      })
      getchart.on('mouseover', (e) => {
        // 取消默认高亮
        getchart.dispatchAction({
          type: 'downplay',
          seriesIndex: 0,
          dataIndex: 0
        })
      })
      // 鼠标移出后默认高亮
      getchart.on('mouseout', (e) => {
        getchart.dispatchAction({
          type: 'highlight',
          seriesIndex: 0,
          dataIndex: 0
        })
      })
      // 随着屏幕大小调节图表
      window.addEventListener('resize', () => {
        getchart.resize()
      })
    },
    // 获取 风险类型分析
    getRiskTypeAnalysisInfo() {
      GetRiskTypeStatistics().then((res) => {
        if (res.data.code === '200') {
          if (res.data.data.array.length) {
            this.riskTypeAnalysisShow = false
            this.$nextTick(() => {
              this.riskTypeAnalysisEchart(res.data.data.array)
            })
          } else {
            this.riskTypeAnalysisShow = true
          }
        }
      })
    },
    // 风险类型分析图渲染
    riskTypeAnalysisEchart(arr) {
      const getchart = echarts.init(document.getElementById('riskTypeAnalysisEcharts'))
      getchart.resize()
      const nameArr = arr.map((item) => {
        return item.name
      })
      const valueArr = arr.map((item) => {
        return item.value
      })
      const option = {
        backgroundColor: '',
        tooltip: {
          trigger: 'axis',
          backgroundColor: '#0D2169',
          borderColor: '#727382',
          textStyle: {
            color: '#fff'
          },
          axisPointer: {
            type: 'shadow'
          }
        },
        grid: {
          left: '12%',
          right: '5%',
          bottom: '10%',
          top: '10%'
        },
        xAxis: [
          {
            type: 'category',
            data: nameArr,
            axisTick: {
              show: false
            },
            axisLabel: {
              textStyle: {
                color: '#878EA9'
              }
            }
          }
        ],
        yAxis: [
          {
            type: 'value',
            axisLabel: {
              show: true,
              textStyle: {
                color: '#878EA9'
              }
            },
            axisLine: {
              lineStyle: {
                type: 'solid',
                color: '#3862B7',
                width: '1'
              }
            },
            splitLine: {
              lineStyle: {
                color: '#314A89',
                width: 1,
                type: 'solid'
              }
            },
            axisTick: {
              show: false
            }
          }
        ],
        series: [
          {
            type: 'bar',
            barWidth: 10,
            itemStyle: {
              color: new echarts.graphic.LinearGradient(0, 1, 0, 0, [
                {
                  offset: 0,
                  color: 'rgba(21, 89, 159, 0.25)'
                },
                {
                  offset: 1,
                  color: 'rgba(9, 244, 196, 0.86)'
                }
              ])
            },
            data: valueArr
          }
        ]
      }
      getchart.clear()
      getchart.setOption(option)
      // 随着屏幕大小调节图表
      window.addEventListener('resize', () => {
        getchart.resize()
      })
    },
    // 获取 风险巡查任务分析
    getInspectionInfo(type, ele) {
      GetInspectionTaskAnalysis({ dateType: type }).then((res) => {
        if (res.data.code === '200') {
          if (res.data.data.list.length) {
            this.inspectionInfo[type] = res.data.data.list[0]
            this.$nextTick(() => {
              this.InspectionTaskEchart(res.data.data.list[0], ele)
            })
          }
        }
      })
    },
    // 风险巡查任务分析图渲染
    InspectionTaskEchart(obj, ele) {
      const getchart = echarts.init(document.getElementById(ele))
      getchart.resize()
      const option = {
        backgroundColor: '',
        tooltip: {
          show: false
        },
        legend: {
          show: false
        },
        series: [
          {
            type: 'pie',
            radius: [50, 60],
            center: ['50%', '50%'],
            hoverAnimation: false,
            data: [
              {
                value: obj.accomplishCount,
                itemStyle: {
                  borderWidth: 1,
                  borderColor: '#4A79C7',
                  color: new echarts.graphic.LinearGradient(0, 1, 0, 0, [
                    {
                      offset: 0,
                      color: '#0085FD'
                    },
                    {
                      offset: 1,
                      color: '#09F4C4'
                    }
                  ])
                },
                label: {
                  show: false
                }
              },
              {
                value: obj.unfinishedCount,
                itemStyle: {
                  color: 'transparent'
                }
              }
            ]
          },
          {
            type: 'pie',
            radius: [51, 59],
            center: ['50%', '50%'],
            hoverAnimation: false,
            data: [
              {
                value: obj.accomplishCount,
                itemStyle: {
                  color: 'transparent'
                }
              },
              {
                value: obj.unfinishedCount,
                itemStyle: {
                  color: '#515D81'
                },
                label: {
                  show: false
                }
              }
            ]
          }
        ]
      }
      getchart.clear()
      getchart.setOption(option)
      // 随着屏幕大小调节图表
      window.addEventListener('resize', () => {
        getchart.resize()
      })
    },
    // 选中 隐患 风险 切换
    activeTypeEvent(type, mark) {
      this.activeType = type
      if (mark === 'click') {
        try {
          window.chrome.webview.hostObjects.sync.bridge.GetDangerorRisk(type)
        } catch (error) {}
      }
      if (type === 'danger') {
        this.getDangerRunScore()
        this.getStateStatistics()
        this.getHiddenDangerNumberAnalysis()

        this.getTypeAnalysisInfo()
        this.$nextTick(() => {
          this.activeDeptDangerEvent('#deptDay', 'day')
          this.activeTaskEvent('#taskDay', 'day')
        })
      } else if (type === 'risk') {
        this.getRiskRunScore()
        this.getRiskLevelAnalysisInfo()
        this.getRiskTypeAnalysisInfo()

        this.getInspectionInfo('day', 'dayInspectionEcharts')
        this.getInspectionInfo('week', 'weekInspectionEcharts')
      }

      $('#danger').removeClass('type_active')
      $('#risk').removeClass('type_active')
      $('#' + type).addClass('type_active')
    },
    activeDeptDangerEvent(type, val) {
      $('#deptDay').removeClass('active')
      $('#deptWeek').removeClass('active')
      $('#deptMonth').removeClass('active')
      $(type).addClass('active')
      this.getTypeAnalysisTable(val)
    },
    activeTaskEvent(type, val) {
      $('#taskDay').removeClass('active')
      $('#taskWeek').removeClass('active')
      $('#taskMonth').removeClass('active')
      $(type).addClass('active')
      this.getTaskAnalysis(val)
    }
  }
}
</script>
<style lang="scss" scoped>
.content {
  width: 100%;
  height: 100%;
  position: relative;
  .active {
    background: url('~@/assets/images/center/light-yellow.png') no-repeat;
    background-size: 100% 100%;
    color: #ffe3a6 !important;
    outline: none;
  }
  .center-center {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #4d5880;
    font-size: 16px;
  }
  .left-content {
    position: absolute;
    top: 0;
    left: 0;
    width: 20%;
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    transition: width 0.3s;
    overflow: hidden;
    .echarts-left-top {
      width: 100%;
      height: 40%;
      background: url('~@/assets/images/peace/bg-40.png') no-repeat;
      background-size: 100% 100%;
      padding: 2px;
      box-sizing: border-box;
      .right-title {
        font-size: 14px;
        font-family: PingFang-SC-Medium, PingFang-SC;
        color: #7eaef9;
        span {
          font-size: 20px;
          font-family: DIN-Bold, DIN;
          font-weight: bold;
          color: #ffe3a6;
          margin: 0 8px;
        }
      }
      .operation_score {
        display: flex;
        align-items: center;
        flex-direction: column;
        justify-content: space-around;
        box-sizing: border-box;
        padding: 0 15px;
        width: 100%;
        height: 100%;
        .animationBox {
          animation: bird 3.5s steps(149) infinite;
        }
        .runScore {
          width: 180px;
          height: 180px;
          flex-shrink: 0;
          margin: 0 auto;
          background: url('~@/assets/images/peace/cir-score-sprite.png') no-repeat;
          background-size: cover;
          position: relative;
          .progressBox {
            width: 90px;
            height: 90px;
            margin: auto;
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            p {
              position: absolute;
              top: 50%;
              left: 50%;
              transform: translate(-50%, -50%);
              font-size: 22px;
              font-family: DIN-Bold, DIN;
              font-weight: bold;
              color: #ecf5ff;
              text-align: center;
              display: flex;
              flex-direction: column;
              span {
                font-size: 13px;
                font-family: PingFangSC-Semibold, PingFang SC;
                font-weight: 600;
                color: #cfe1f7;
                text-shadow: 0px 0px 5px #3880de;
              }
            }
          }
        }
        @keyframes bird {
          from {
            background-position-x: 0px;
          }
          to {
            background-position-x: 100%;
          }
        }

        > div:nth-child(2) {
          height: 24px;
          // width: 100%;
          margin: 10px auto;
          span {
            display: inline-block;
            width: 80px;
            height: 24px;
            background-color: #24396d;
            text-align: center;
            line-height: 24px;
            color: #dceaff;
            font-size: 14px;
            font-family: PingFang-SC-Medium, PingFang-SC;
            cursor: pointer;
          }
          i {
            display: inline-block;
            width: 1px;
            background: #254168;
            height: 24px;
            margin: 0 6px;
            vertical-align: bottom;
          }
          .type_active {
            color: #ffe3a6;
            background: url('~@/assets/images/center/border-bg-select.png') no-repeat;
            background-size: 100% 100%;
          }
        }
        > div:last-child {
          display: flex;
          width: 100%;
          flex-wrap: wrap;
          > div {
            width: 50%;
            height: 60px;
            .triangle_box {
              width: 124px;
              height: 45px;
              margin: auto;
              /* background: url('~@/assets/images/peace/score.png') no-repeat; */
              background-size: 100% 100%;
              p {
                height: 22px;
                line-height: 22px;
                font-size: 14px;
                font-family: PingFang-SC-Medium, PingFang-SC;
                color: #dceaff;
                text-align: center;
                padding-left: 30px;
              }
              p:first-child {
                height: 24px;
                line-height: 24px;
                font-size: 20px;
                font-family: DIN-Bold, DIN;
                font-weight: bold;
                color: #ffffff;
              }
            }
          }
        }
      }
    }
    .echarts-left-center {
      width: 100%;
      height: 29.5%;
      background: url('~@/assets/images/peace/emergency-disposal-left.png') no-repeat;
      background-size: 100% 100%;
      box-sizing: border-box;
    }
    .echarts-left-bottom {
      width: 100%;
      height: 30.5%;
      background: url('~@/assets/images/peace/emergency-disposal-left.png') no-repeat;
      background-size: 100% 100%;
    }
    .risk_left_top {
      height: 49%;
      background: url('~@/assets/images/center/bg-49.png') no-repeat;
      background-size: 100% 100%;
    }
    .echarts-left-center-risk {
      width: 100%;
      height: 32%;
      background: url('~@/assets/images/center/bg-32.png') no-repeat;
      background-size: 100% 100%;
      box-sizing: border-box;
      margin-top: 5px;
    }
  }
  .right-content {
    position: absolute;
    top: 0;
    right: 0;
    width: 20%;
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    transition: width 0.3s;
    overflow: hidden;
    .echarts-top {
      width: 100%;
      height: 31%;
      background: url('~@/assets/images/peace/emergency-disposal-left.png') no-repeat;
      background-size: 100% 100%;
      box-sizing: border-box;
      .bg-content {
        display: flex;
        justify-content: space-around;
        > div:first-child {
          position: relative;
          width: 162px;
          height: 162px;
          margin: auto;
          background: url('~@/assets/images/center/bg-Patrolled.png') no-repeat;
          background-size: 100% 100%;
          display: flex;
          p {
            margin: auto;
            font-size: 30px;
            font-family: DIN-Bold, DIN;
            font-weight: bold;
            color: #ecf5ff;
            text-align: center;
            display: flex;
            flex-direction: column;
            span {
              font-size: 12px;
              font-family: PingFangSC-Semibold, PingFang SC;
              font-weight: 600;
              color: #cfe1f7;
              text-shadow: 0px 0px 5px #3880de;
            }
          }
          .el-progress-circle {
            position: absolute;
            transform: rotate(90deg);
          }
        }
      }
      .alarm-analysis {
        display: flex;
        justify-content: space-around;
        box-sizing: border-box;
        width: calc(100% - 200px);
        height: 160px;
        margin: auto 0;
        .case-img {
          width: 70%;
          height: 80%;
          margin: auto;
          position: relative;
          img {
            width: 100%;
            height: 100%;
          }
          .case-num {
            position: absolute;
            width: 100%;
            text-align: center;
            top: 8%;
            color: #fff;
            font-size: 1.1rem;

            font-family: DIN-Bold, DIN;
            font-weight: bold;
            span {
              font-weight: 400;
              font-size: 0.75rem;
            }
          }
        }
      }
    }
    .echarts-center {
      width: 100%;
      height: 40%;
      background: url('~@/assets/images/peace/bg-40.png') no-repeat;
      background-size: 100% 100%;
      padding: 2px;
      box-sizing: border-box;
      ::v-deep .el-table {
        border: none !important;
        .el-table__header .el-table__cell {
          padding: 5px 0 !important;
          color: #fff;
        }
        .el-table__body {
          tr {
            background: center;
          }
          td.el-table__cell,
          th.el-table__cell.is-leaf {
            border-right: 2px solid #0a164e;
            border-bottom: 2px solid #0a164e;
            background: rgba(56, 103, 180, 0.2);
            color: #fff;
          }
        }
      }
    }
    .echarts-bottom {
      width: 100%;
      height: 29%;
      background: url('~@/assets/images/peace/emergency-disposal-left.png') no-repeat;
      background-size: 100% 100%;
    }
    .echarts-top-risk {
      width: 100%;
      height: 37%;
      background: url('~@/assets/images/center/bg-37.png') no-repeat;
      background-size: 100% 100%;
      padding: 2px;
      box-sizing: border-box;
      margin-bottom: 5px;
    }
    .echarts-center-risk {
      width: 100%;
      height: 44%;
      background: url('~@/assets/images/center/bg-44.png') no-repeat;
      background-size: 100% 100%;
      padding: 2px;
      box-sizing: border-box;
      .bg-content {
        display: flex;
        justify-content: space-around;
        > div {
          width: 50%;
          text-align: center;
          display: flex;
          flex-direction: column;
          justify-content: space-around;
        }
        .inspection {
          font-size: 16px;
          font-family: PingFangSC-Medium, PingFang SC;
          // height: 6rem;
          // line-height: 6rem;
          color: #ffffff;
          > span {
            display: inline-block;
            width: 14px;
            height: 14px;
            background: url('~@/assets/images/peace/arrow-right.png') no-repeat;
            background-size: 100% 100%;
          }
        }
        .ins-echart-box {
          width: 100%;
          height: 50%;
          position: relative;
          background: url('~@/assets/images/center/bg-Patrolled.png') center center no-repeat;
          background-size: 80% 80%;
          display: flex;
          p {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            font-size: 30px;
            font-family: DIN-Bold, DIN;
            font-weight: bold;
            color: #ecf5ff;
            text-align: center;
            display: flex;
            flex-direction: column;
            span {
              font-size: 12px;
              font-family: PingFangSC-Semibold, PingFang SC;
              font-weight: 600;
              color: #cfe1f7;
              text-shadow: 0px 0px 5px #3880de;
            }
          }
        }
        .inspection-legend {
          width: 100%;
          text-align: center;
          font-size: 12px;
          font-family: PingFang-SC-Medium, PingFang-SC;
          color: #dceaff;
          p {
            margin-bottom: 8px;
          }
          span {
            color: #ffe3a6;
          }
          i {
            width: 12px;
            height: 12px;
            vertical-align: bottom;
            background: linear-gradient(160deg, #70a7ff 0%, #3cfbff 100%);
            border: 1px solid;
            border-image: linear-gradient(90deg, rgba(0, 133, 253, 1), rgba(9, 244, 196, 1)) 1 1;
            display: inline-block;
            margin-right: 8px;
          }
        }
      }
    }
  }
  .bg-title {
    margin-top: 5px;
    // height: 23%;
    height: 2.5rem;
    line-height: 2.5rem;
    // color: #d4e3f9;
    color: #dceaff;
    padding: 0 1rem 0 3rem;
    font-family: TRENDS;
    /*设置为伸缩容器*/
    display: -webkit-box;
    display: -moz-box;
    display: -ms-flexbox;
    display: -webkit-flex;
    display: flex;
    /*垂直居中*/
    -webkit-box-align: center; /*旧版本*/
    -moz-box-align: center; /*旧版本*/
    -ms-flex-align: center; /*混合版本*/
    -webkit-align-items: center; /*新版本*/
    align-items: center; /*新版本*/
    justify-content: space-between;
    .right-search {
      span {
        display: inline-block;
        width: 35px;
        height: 30px;
        font-size: 14px;
        text-align: center;
        line-height: 26px;
        margin: 0 4px;
        color: #7eaef9;
        cursor: pointer;
      }
      span:hover {
        background: url('~@/assets/images/center/light-yellow.png') no-repeat;
        background-size: 100% 100%;
        color: #ffe3a6;
      }
      i {
        color: #7eaef9;
      }
    }
  }
  .bg-content {
    position: relative;
    box-sizing: border-box;
    padding: 10px 10px 15px 10px;
    width: 100%;
    height: 77%;
    #dangerStateEcharts,
    #dangerNumEchart,
    #dangerAnalysisEcharts,
    #levelAnalysisEcharts,
    #riskTypeAnalysisEcharts,
    #dayInspectionEcharts,
    #weekInspectionEcharts {
      width: 100%;
      height: 100%;
      z-index: 2;
    }
    .alarm-bg {
      width: 12.5rem;
      height: 12.5rem;
      position: absolute;
      top: calc(50% - 6.4rem);
      left: 0%;
      background: url('~@/assets/images/peace/people-right-top-bg.png') no-repeat;
      background-size: 100% 100%;
    }
  }
}
</style>
