<template>
  <div class="airConditioning">
    <div class="right-content">
      <div class="top_title">
        <span class="dialog-title">空间清单</span>
      </div>
      <div class="top_type">
        <div class="top_type_item" @click="changeType('0')" :class="{ activeType: activeType === '0' }">
          <span>中央空调</span>
        </div>
        <div class="top_type_item" @click="changeType('1')" :class="{ activeType: activeType === '1' }">
          <span>风机盘管</span>
        </div>
        <div class="top_type_item" @click="changeType('2')" :class="{ activeType: activeType === '2' }">
          <span>制冷系统</span>
        </div>
      </div>
      <div class="center_tab">
        <span @click="changeTab('1')" :class="{ activeTab: activeTab === '1' }">设备</span>
        <i>|</i>
        <span @click="changeTab('2')" :class="{ activeTab: activeTab === '2' }">管网</span>
      </div>
      <div class="bottom_table">
        <el-table
          class="table-center-transfer"
          :data="tableData[activeType]"
          max-height="calc(90vh - 14.5rem)"
          height="calc(100% - 2rem)"
          :cell-style="$tools.setCell(3)"
          :header-cell-style="$tools.setHeaderCell(3)"
          :header-cell-class-name="activeTab === '2' ? cellClass : ''"
          style="width: 100%"
          element-loading-background="rgba(0, 0, 0, 0.2)"
          v-loading="tableLoading"
          @row-click="handleRowClick"
        >
          <el-table-column v-if="activeTab === '1'" :key="1" align="center" fixed prop="equipmentName" show-overflow-tooltip label="设备名称"></el-table-column>
          <el-table-column v-if="activeTab === '1'" :key="2" align="center" fixed prop="officeName" show-overflow-tooltip label="运行参数"></el-table-column>
          <el-table-column v-if="activeTab === '2'" :key="3" align="center" width="110" fixed type="selection"> </el-table-column>
          <el-table-column v-if="activeTab === '2'" :key="4" align="center" fixed prop="officeName" show-overflow-tooltip label="管网名称"></el-table-column>
        </el-table>
      </div>
    </div>
  </div>
</template>
<script>
export default {
  name: 'airConditioning',
  data() {
    return {
      activeType: '0',
      activeTab: '1',
      tableData: [
        [
          {
            equipmentName: '组合式空调机组#1',
            officeName: '运行状态: 开启',
            sort: 1
          },
          {
            equipmentName: '组合式空调机组#2',
            officeName: '报警状态: 正常',
            sort: 2
          },
          {
            equipmentName: '组合式空调机组#3',
            officeName: '回水温度反馈: 39.5℃',
            sort: 3
          },
          {
            equipmentName: '组合式空调机组#4',
            officeName: '送风温度反馈: 34.6℃',
            sort: 4
          }
        ],
        [
          {
            equipmentName: '风机盘管#1',
            officeName: '运行状态: 开启'
          },
          {
            equipmentName: '风机盘管#2',
            officeName: '报警状态: 正常'
          },
          {
            equipmentName: '风机盘管#3',
            officeName: '手自动状态: 自动'
          },
          {
            equipmentName: '风机盘管#4',
            officeName: '总运行时间: 25h'
          }
        ],
        [
          {
            equipmentName: '冷水机组#1',
            officeName: '冷机运行时间: 32h'
          },
          {
            equipmentName: '冷水机组#2',
            officeName: '电压: 220V'
          },
          {
            equipmentName: '冷却水泵#1',
            officeName: '运行状态: 开启'
          },
          {
            equipmentName: '冷冻水泵#2',
            officeName: '运行状态: 开启'
          }
        ]
      ],
      tableClickFlag: [false, false, false, false],
      tableLoading: false
    }
  },
  created() {},
  mounted() {},
  methods: {
    cellClass(row) {
      if (row.columnIndex === 0) {
        return 'disabledCheck'
      }
    },
    changeType(type) {
      this.activeType = type
      this.tableLoading = true
      setTimeout(() => {
        this.tableLoading = false
      }, 1500)
    },
    changeTab(tab) {
      this.activeTab = tab
      this.tableLoading = true
      setTimeout(() => {
        this.tableLoading = false
      }, 1500)
    },
    handleRowClick(row) {
      // console.log(row)
      if (this.activeTab === '1' && this.activeType === '0') {
        this.tableClickFlag[row.sort - 1] = !this.tableClickFlag[row.sort - 1]
        try {
          window.chrome.webview.hostObjects.sync.bridge.GetEnvironmentSwitch(row.sort, this.tableClickFlag[row.sort - 1])
        } catch (error) {}
      }
    }
  }
}
</script>
<style lang="scss" scoped>
.airConditioning {
  width: 100%;
  height: 100%;
  position: relative;
  .right-content {
    position: absolute;
    top: 0;
    right: 0;
    width: 20%;
    margin-top: 2%;
    // height: auto;
    max-height: 90%;
    background: url('~@/assets/images/center/mask-short.png') no-repeat;
    background-size: 100% 100%;
    padding: 2px;
    box-sizing: border-box;
    padding: 10px 10px 20px 10px;
    .top_title {
      height: 2.5rem;
      line-height: 2.5rem;
      padding-left: 0.9375rem;
      .dialog-title {
        color: #fff;
        font-family: PingFangSC-Medium, PingFang SC;
      }

      .dialog-title::before {
        content: '';
        display: inline-block;
        width: 2px;
        height: 16px;
        background: #ffe3a6;
        margin-right: 10px;
        vertical-align: middle;
      }
    }
    .top_type {
      width: calc(100% - 20px);
      padding: 15px 0;
      // height: 4.75rem;
      margin: 0 auto;
      background-color: rgba(1, 11, 59, 0.5);
      display: flex;
      // justify-content: space-around;
      flex-wrap: wrap;
      .top_type_item {
        width: 29%;
        margin: 3px 2%;
        height: 2.5rem;
        line-height: 2.5rem;
        text-align: center;
        font-size: 14px;
        font-family: PingFang-SC-Medium, PingFang-SC;
        color: #93bdff;
        background: url('~@/assets/images/center/block-bg.png') no-repeat;
        background-size: 100% 100%;
        cursor: pointer;
      }
      .activeType {
        background: url('~@/assets/images/center/block-bg-active.png') no-repeat;
        background-size: 100% 100%;
        color: #ffe3a6;
      }
    }
    .center_tab {
      padding: 15px 0;
      text-align: center;
      span {
        display: inline-block;
        width: 50px;
        height: 25px;
        font-size: 14px;
        text-align: center;
        line-height: 26px;
        color: #7eaef9;
        cursor: pointer;
      }
      span:hover,
      .activeTab {
        background: url('~@/assets/images/center/light-yellow.png') no-repeat;
        background-size: 100% 100%;
        color: #ffe3a6;
      }
      i {
        color: #7eaef9;
      }
    }
    .bottom_table {
      max-height: calc(90vh - 13rem);

      ::v-deep .el-table {
        border: none !important;
        .el-table__header thead th {
          background: rgba(19, 35, 85, 0.15) !important;
        }
        /* 去掉全选按钮 */
        .disabledCheck .cell .el-checkbox__inner {
          display: none !important;
        }
        .disabledCheck .cell::before {
          content: '显示隐藏';
          text-align: center;
        }
        .el-table__header .el-table__cell {
          padding: 5px 0 !important;
          color: #fff;
        }
        // box-sizing: border-box;
        .el-table__body {
          tr {
            background: center;
          }
          td.el-table__cell,
          th.el-table__cell.is-leaf {
            border-right: 2px solid #0a164e;
            border-bottom: 2px solid #0a164e;
            background: rgba(56, 103, 180, 0.2);
            color: #fff;
          }
        }
      }
    }
  }
}
</style>
