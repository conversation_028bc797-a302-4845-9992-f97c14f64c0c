<!--
 * @Author: hedd
 * @Date: 2024-01-30 14:12:51
 * @LastEditTime: 2024-07-17 15:38:18
 * @FilePath: \ihcrs_client_iframe\src\views\spaceManage\sysTypeComponent\components\deviceKpi\index.vue
 * @Description:
-->
<template>
  <div class="deviceKpi">
    <div class="top_type">
      <div v-for="item in typeList" :key="item.key" class="top_type_item" :class="{ activeType: typeActive === item.key }" @click="typeChange(item.key)">
        <span>{{ item.name }}</span>
      </div>
    </div>
    <div class="deviceKpi-content">
      <component :is="typeActive"></component>
    </div>
  </div>
</template>

<script>
export default {
  name: 'deviceKpi',
  components: {
    REStatis: () => import('./REStatis.vue'),
    RETrend: () => import('./RETrend.vue'),
    incomeDetail: () => import('./incomeDetail.vue'),
    invest: () => import('./invest.vue'),
    rate: () => import('./rate.vue'),
    inspect: () => import('./inspect.vue'),
    peopleNumTrend: () => import('./peopleNumTrend.vue'),
    usage: () => import('./usage.vue'),
    usefulLife: () => import('./usefulLife.vue')
  },
  data() {
    return {
      typeActive: 'REStatis',
      typeList: [
        { name: '收支统计', key: 'REStatis' },
        { name: '收支趋势', key: 'RETrend' },
        { name: '收入明细', key: 'incomeDetail' },
        { name: '投资回收', key: 'invest' },
        { name: '费率分布', key: 'rate' },
        { name: '日最高检查量', key: 'inspect' },
        { name: '检查人数趋势', key: 'peopleNumTrend' },
        { name: '使用情况', key: 'usage' },
        { name: '可用年限', key: 'usefulLife' }
      ]
    }
  },
  computed: {},
  created() {},
  methods: {
    typeChange(key) {
      this.typeActive = key
    }
  }
}
</script>

<style lang="scss" scoped>
.deviceKpi {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  .top_type {
    display: flex;
    flex-wrap: wrap;
    .top_type_item {
      cursor: pointer;
      margin-right: 8px;
      padding: 6px 8px;
      background: rgba(255, 255, 255, 0.1) linear-gradient(90deg, rgba(10, 132, 255, 0) 0%, rgba(10, 132, 255, 0) 100%);
      font-size: 14px;
      font-weight: 400;
      color: #b0e3fa;
      line-height: 14px;
      border: 1px solid transparent;
    }
    .top_type_item:last-child {
      margin: 0px;
    }
    .activeType {
      background: rgba(255, 255, 255, 0.1) linear-gradient(90deg, rgba(10, 132, 255, 0.4) 0%, rgba(10, 132, 255, 0) 100%);
      border: 1px solid;
      border-image: radial-gradient(circle, rgba(171, 240, 255, 1), rgba(226, 254, 255, 1), rgba(132, 196, 203, 1), rgba(48, 151, 166, 0)) 1 1;
    }
  }
  .deviceKpi-content {
    flex: 1;
    margin-top: 16px;
  }
}
</style>
