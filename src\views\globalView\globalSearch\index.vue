<template>
  <div class="main">
    <el-dialog
      v-dialogDrag
      :modal="false"
      :visible.sync="visible"
      custom-class="mainDialog main"
      append-to-body
      :close-on-click-modal="false"
      :before-close="closeDialog"
      class="all-table-componentList"
    >
      <template slot="title">
        <span class="dialog-title">{{ globalParams.title }}</span>
      </template>
      <div class="dialog-content">
        <AssetsList
          v-if="globalParams.searchType == 0"
          :globalParams="globalParams"
          @close="closeDialog"
        />
        <SpaceList
          v-if="globalParams.searchType == 1"
          :globalParams="globalParams"
          @close="closeDialog"
        />
      </div>
    </el-dialog>
  </div>
</template>
<script>
export default {
  name: 'globalSearch',
  components: {
    AssetsList: () => import('./components/assetsList.vue'),
    SpaceList: () => import('./components/spaceList.vue')
  },
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    globalParams: {
      type: Object,
      default: () => {
        return {
          title: '设备列表',
          searchType: 0,
          height: 'calc(100% - 40px)'
        }
      }
    },
    list: {
      type: Array,
      default: () => {}
    }
  },
  data() {
    return {}
  },

  mounted() {
    try {
      window.chrome.webview.hostObjects.sync.bridge.ShowDialog(true)
    } catch (error) {}
  },
  methods: {
    // 取消按钮
    closeDialog () {
      try {
        window.chrome.webview.hostObjects.sync.bridge.ShowDialog(false)
      } catch (error) { }
      this.$emit('close')
    }
  }
}
</script>

<style lang="scss" type="text/css" scoped>
.all-table-componentList {
  pointer-events: none;
  :deep(.el-image__error) {
    background: center;
  }
}
.preview-image {
  z-index: 9999 !important;
  :deep(.el-image-viewer__canvas) {
    color: #fff;
  }
}
:deep(.mainDialog) {
  width: 70%;
  height: 84vh;
  margin-top: 6vh !important;
  // border: 1px solid #5996f9;
  background-color: transparent !important;
  pointer-events: auto;
  background-image: url("@/assets/images/table-bg.png");
  background-repeat: no-repeat;
  background-size: 100% 100%;
  box-shadow: none;
  .el-dialog__headerbtn {
    transform: translateX(-36px);
    width: 25px;
    height: 25px;
    background-image: url("@/assets/images/close.png");
    background-repeat: no-repeat;
    background-size: 100% 100%;
    .el-dialog__close::before {
      display: none;
    }
  }
  .dialog-title {
    display: inline-block;
    width: 100%;
    text-align: center;
    transform: translateY(-6px);
    color: #cbdeed;
  }
  .dialog-title::before {
    display: none;
  }
  .el-tabs__nav-scroll {
    display: flex;
    .el-tabs__nav {
      margin: auto;
    }
  }

  .el-dialog__header {
    background-color: transparent;
  }
  .el-dialog__body {
    padding: 0;
    height: calc(100% - 60px);
    max-height: fit-content;
    overflow-y: hidden;
    background-color: transparent;
    padding: 0px 80px;
    .dialog-content {
      width: 100%;
      height: 100%;
      background-color: transparent;

      .el-table {
        background-color: transparent;
        border: 1px solid #202d4c;
        .el-table__header-wrapper {
          background-color: transparent !important;
        }
        .el-table__body-wrapper {
          background-color: transparent !important;
        }
        tr {
          background-color: transparent !important;
        }
        td {
          background-color: transparent !important;
        }
      }
    }
  }
}
</style>
