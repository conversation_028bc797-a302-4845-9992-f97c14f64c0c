<template>
  <div class="regulationsDoc">
    <div class="regulationsDoc-main">
      <div class="main-title">
        <i class="el-icon-arrow-left" @click="$router.go(-1)"></i>
        <span>预案详情</span>
        <i class="el-icon-arrow-right"></i>
        <span style="color: #FFFFFF;">法规文档</span>
      </div>
      <div id="main_content" class="main-content"></div>
    </div>
  </div>
</template>

<script>

import jsPreviewDocx from '@js-preview/docx'
import '@js-preview/docx/lib/index.css'
import jsPreviewPdf from '@js-preview/pdf'
export default {
  name: 'regulationsDoc',
  data() {
    return {
    }
  },
  computed: {

  },
  created() {
    this.$nextTick(() => {
      this.init()
    })
  },
  methods: {
    init() {
      if (this.$tools.matchFileSuffixType(this.$route.query.url) == 'pdf') {
        const myPdfPreviewer = jsPreviewPdf.init(document.getElementById('main_content'), {
          onError: (e) => {
            console.log('发生错误', e)
          },
          onRendered: () => {
            console.log('渲染完成')
          }
        })
        myPdfPreviewer.preview(this.$route.query?.url)
      } else {
        const myDocxPreviewer = jsPreviewDocx.init(document.getElementById('main_content'))
        myDocxPreviewer.preview(this.$route.query?.url).then(res => {
          console.log('预览完成')
        }).catch(e => {
          console.log('预览失败', e)
        })
      }
    }
  }
}

</script>

<style lang="scss" scoped>
.regulationsDoc {
  width: 100%;
  height: 100%;
  position: relative;
  .regulationsDoc-main {
    position: absolute;
    right: 0;
    top: 0;
    bottom: 0;
    width: 60%;
    background: url('@/assets/images/plan/planBgd.png') no-repeat center center / 100% 100%;
    display: flex;
    flex-direction: column;
    overflow: hidden;
  }
  .main-title {
    height: 40px;
    font-size: 14px;
    font-family: 'PingFang SC-Medium', 'PingFang SC';
    font-weight: 500;
    color: #B0E3FA;
    padding-left: 10px;
    display: flex;
    align-items: center;
    .el-icon-arrow-left {
      color: #8BDDF5;
      width: 25px;
      cursor: pointer;
      font-weight: 600;
    }
    .el-icon-arrow-right {
      font-size: 12px;
      margin: 0 4px;
    }
  }
  .main-content {
    flex: 1;
    padding: 16px;
    overflow: auto;
    ::v-deep(.vue-office-docx-main) {
      .docx-wrapper {
        padding-bottom: 30px;
        p {
          line-height: initial !important;
        }
        span {
          line-height: initial !important;
          display: inline-block !important;
          word-break: break-all;
        }
      }
    }
  }
}
</style>
