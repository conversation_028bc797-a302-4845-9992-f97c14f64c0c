<!--
 * @Author: hedd
 * @Date: 2023-08-16 16:11:50
 * @LastEditTime: 2023-08-25 16:19:29
 * @FilePath: \ihcrs_client_iframe\src\views\centerScreen\intelligentOperation\lightComponents\hospitalWideControl.vue
 * @Description:
-->
<template>
  <div class="hospitalWideControl">
    <div class="building-list-item" v-for="item in dataList" :key="item.name" @click="$emit('hospitalWideSelect', item)">
      <p class="item-title">{{item.constructName || '-'}}</p>
      <div class="item-content">
        <p class="allNum">总：{{item.count || 0}}</p>
        <p class="onNum">{{item.openCount || 0}}</p>
        <p class="offNum">{{item.closeCount || 0}}</p>
      </div>
    </div>
  </div>
</template>

<script>
import { GetHospitalWideList } from '@/utils/centerScreenApi'
import { mapGetters } from 'vuex'
export default {
  name: 'hospitalWideControl',
  props: {
    requestParams: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  data() {
    return {
      dataList: []
    }
  },
  computed: {
    ...mapGetters({
      socketIemcMsgs: 'socketIemcMsgs'
    })
  },
  watch: {
    socketIemcMsgs(data) {
      const projectData = JSON.parse(data)
      // 匹配设备相同的接收消息刷新页面
      if (projectData.projectCode === this.requestParams.projectCode && projectData.data === 'LightRefresh') {
        // 收到消息刷新页面
        console.log('匹配成功', projectData)
        this.getHospitalWideList()
      }
    }
  },
  created() {
    this.getHospitalWideList()
  },
  methods: {
    getHospitalWideList() {
      GetHospitalWideList({ projectCode: this.requestParams.projectCode }).then((res) => {
        if (res.data.code === '200') {
          this.dataList = res.data.data
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.hospitalWideControl{
  background: rgba(1, 11, 59, .5);
  max-height: calc(95vh - 26rem);
  overflow: auto;
  margin: 10px;
  .building-list-item{
    padding: 12px 19px 12px 19px;
    .item-title{
      padding-left: 25px;
      background: url('~@/assets/images/center/location.png') no-repeat left center / 16px 16px;
    }
    .item-content{
      padding-top: 4px;
      display: flex;
      align-content: center;
      justify-content: space-around;
      font-size: 16px;
      font-weight: 500;
      color: #FFFFFF;
      line-height: 22px;
      .allNum{
        color: #FFE3A6;
      }
      .onNum{
        padding-left: 30px;
        background: url('~@/assets/images/center/on-light.png') no-repeat left center / 17px 22px;
      }
      .offNum{
        padding-left: 30px;
        background: url('~@/assets/images/center/off-light.png') no-repeat left center / 17px 22px;
      }
    }
  }
}
</style>
