
<template>
  <micro-app
    :url="childInfo.url"
    :name="childInfo.name"
    :baseroute="`${childInfo.baseroute}`"
    router-mode="pure"
    inline
    iframe
    :data="childInfo"
    style="height: 100%"
    @datachange="handleDataChange"
  ></micro-app>
</template>
<script>
import microApp from '@micro-zoe/micro-app'
export default {
  name: 'MicroChildApp',
  data() {
    return {
      processOrigin: process.env.VUE_APP_UNIFIED_SERVER.slice(0, -1),
      origin: '',
      childInfo: {
        url: '',
        name: '',
        baseroute: '',
        path: '',
        userInfo: {userInfo: this.$store.state.loginInfo, token: this.$store.state.loginInfo.token}
      }
    }
  },
  created() {
    this.childInfo.name = 'operation'
    this.childInfo.baseroute = '/operation'
    this.childInfo.path = 'flowFormDetail'
    this.childInfo.query = {}
    this.setChildAppOrigin()
    // microApp.router.setDefaultPage({
    //   name: this.childInfo.path,
    //   path: `/${this.childInfo.path}`
    // })
    // setTimeout(() => {
    microApp.router.setDefaultPage({
      name: this.childInfo.path,
      path: '/flowFormDetail?detailId=98e01e640ed547a1ade69ec1e50fa5a4&flowId=98e01e640ed547a1ade69ec1e50fa5a4&handleType=detail'
    })
    console.log(11111111, this.childInfo)
    // }, 5000)
    // ?detailId=98e01e640ed547a1ade69ec1e50fa5a4&flowId=98e01e640ed547a1ade69ec1e50fa5a4&handleType=detail
  },
  methods: {
    setChildAppOrigin() {
      if (process.env.NODE_ENV === 'production') {
        this.origin = this.processOrigin
      } else {
        let port = ''
        switch (this.childInfo.baseroute) {
          case '/projectManage': // 工作流
          case '/assessManage': // 考核管理
          case '/work-overt': // 奖惩公开
          case '/assessManage/antiIncentive': // 考核管理-负激励考核
          case '/assessManage/performance': // 考核管理-考核任务
          case '/shiftTemp': //
            port = 3200
          default:
            break
        }
        this.origin = `http://127.0.0.1:${port}`
      }
      // this.childInfo.url = `${this.origin}${this.childInfo.baseroute}/`
      this.childInfo.url = `http://*************:12398${this.childInfo.baseroute}/`
    },
    handleDataChange(e) {
      const newVal = e.detail.data
      console.log(111, newVal)
    }
  }
}
</script>
<style lang="scss" scoped>
::v-deep micro-app {
  micro-app-body {
    background: none;
  }
}
</style>
