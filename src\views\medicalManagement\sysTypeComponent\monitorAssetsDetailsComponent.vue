<template>
  <div class="monitor-details-view">
    <div class="mdv-header-box">
      <div class="mdv-header-box-left">
        <div class="mdv-head-title">对象名称：</div>
        <div class="mdv-head-value">第1区03点</div>
      </div>
      <div class="mdv-header-box-right">
        <span class="el-icon-close" @click="closeDeviceDialog"></span>
      </div>
    </div>
    <div class="mdv-content-box">
      <div class="mdv-nav-box">
        <div class="bg-title">
          <div v-scrollMove class="box-type">
            <el-badge
              v-for="(item, index) in navData"
              :key="index"
              :hidden="item.badgeValue < 1 ? true : item.badgeHidden"
              :value="item.badgeValue"
              :max="99"
            >
              <span
                :id="item.Dom"
                class="tags-item"
                :class="{ type_active: activeType === item.Dom }"
                @click="activeTypeEvent(item.Dom)"
                >{{ item.name }}</span
              >
            </el-badge>
          </div>
          <div v-if="navData.length > 6" class="content-icon-collapse">
            <el-dropdown trigger="click" @command="roomTypeCommand">
              <img src="@/assets/images/qhdsys/bg-gd.png" alt="" />
              <el-dropdown-menu slot="dropdown">
                <el-dropdown-item
                  v-for="(item, index) in navData"
                  :key="index"
                  :command="item.Dom"
                  :class="{ isBjxl: activeType === item.Dom }"
                  >{{ item.name }}</el-dropdown-item
                >
              </el-dropdown-menu>
            </el-dropdown>
          </div>
        </div>
      </div>
      <div class="content-box">
        <component v-if="currentComponent" :is="currentComponent" :ref="currentComponent" :roomData="roomData" :assetsList="assetsList"
                     type="device" :deviceId="deviceIds" class="sys-box-content" ></component>
      </div>
    </div>
  </div>
</template>
<script>
export default {
  components: {
    assetsDetailsComponent: () => import("../components/monitor/assetsDetailsComponent.vue"),
    earlyWarningDetailsComponent: () => import("../components/monitor/earlyWarningDetailsComponent.vue"),
  },
  data() {
    return {
      activeType: "details",
      currentComponent: "assetsDetailsComponent",
      navData: [
        {
          Dom: "details",
          name: "详情",
          component: "assetsDetailsComponent",
          index: 1,
          badgeHidden: true,
          badgeValue: 0,
          is: ["badness", "deploy", "benefit"],
        },
        {
          Dom: "earlyWarning",
          name: "预警",
          component: "earlyWarningDetailsComponent",
          index: 1,
          badgeHidden: true,
          badgeValue: 0,
          is: ["badness", "deploy", "benefit"],
        },
        {
          Dom: "repair",
          name: "维修",
          component: "",
          index: 1,
          badgeHidden: true,
          badgeValue: 0,
          is: ["badness", "deploy", "benefit"],
        },
        {
          Dom: "curing",
          name: "养护",
          component: "",
          index: 1,
          badgeHidden: true,
          badgeValue: 0,
          is: ["badness", "deploy", "benefit"],
        },
        {
          Dom: "qualityControl",
          name: "质控",
          component: "",
          index: 1,
          badgeHidden: true,
          badgeValue: 0,
          is: ["badness", "deploy", "benefit"],
        },
      ],
      deviceIds: "",
      roomData: {
        tabName: "", // 当前选中的tab
        mathRandom: "", // 用于刷新数据防止子页面监听不到
        electricityLevel: "0", // 配电机房专属字段默认0机房 1电柜
        deviceId: "", // 维修设备id
        localtion: "",
        ssmType: "", // 空间等级：2医院，3区域，4建筑，5楼层，6房间
        ssmCodes: "",
        modelCode: "",
        projectCode: "", // code
        isSpace: "", // 0空间 1设备
        menuCode: "", // 分组Code
        modelCoding: "", // 机柜Code集合
        title: "",
      },
      assetsList: [],
    };
  },
  mounted() {},
  methods: {
    activeTypeEvent(type) {
      this.activeType = type;
      this.currentComponent = this.navData.find(item=>item.Dom === type).component
    },
    closeDeviceDialog() {
      this.$emit('configCloseDialog')
    }
  },
};
</script>
<style lang="scss" scoped>
.monitor-details-view {
  position: absolute;
  top: 0;
  right: 0;
  width: 24.573%;
  height: 100%;
  background: url("~@/assets/images/qhdsys/bg-mask.png") no-repeat;
  background-size: 100% 100%;
  box-sizing: border-box;
  padding: 0 25px 10px 35px;
  transition: width 0.3s linear;
  overflow: hidden;
}
.mdv-header-box {
  width: 100%;
  height: 2.5rem;
  box-sizing: border-box;
  display: flex;
  align-items: center;
  font-size: 1.125rem;
  color: #ffca64;
  justify-content: space-between;
  background: rgba(255,224,152,0.12);
  .mdv-header-box-left {
    display: flex;
  }
  .mdv-header-box-right {
    font-size: 1.5rem;
    font-weight: bolder;
    cursor: pointer;
  }
}
.mdv-content-box {
  height: calc(100% - 48px);
  .content-box {
    height: calc(100% - 39px);
    margin-top: 8px;
  }
}
.mdv-nav-box {
  color: #fff;
  display: flex;
  flex-direction: column;
  .bg-title {
    align-items: center;
  }
  .box-type {
    width: calc(100% - 8px);
    padding-top: 13px;
    display: flex;
    overflow: hidden;
    ::v-deep .el-badge {
      flex-shrink: 0;
      .el-badge__content {
        z-index: 1;
        background: #ff2d55;
        border: none;
      }
    }
    .tags-item {
      cursor: pointer;
      height: 26px;
      line-height: 26px;
      flex-shrink: 0;
      margin-left: 8px;
      // margin-top: 8px;
      padding: 0px 12px;
      background: rgba(255, 255, 255, 0.1)
        linear-gradient(
          90deg,
          rgba(10, 132, 255, 0) 0%,
          rgba(10, 132, 255, 0) 100%
        );
      font-size: 14px;
      font-weight: 400;
      color: #b0e3fa;
      // line-height: 14px;
      border: 1px solid transparent;
    }
    .tags-item:first-child {
      margin-left: 0px;
      margin-right: 8px;
    }
    .type_active {
      background: rgba(255, 255, 255, 0.1)
        linear-gradient(
          90deg,
          rgba(10, 132, 255, 0.4) 0%,
          rgba(10, 132, 255, 0) 100%
        );
      border: 1px solid;
      border-image: radial-gradient(
          circle,
          rgba(171, 240, 255, 1),
          rgba(226, 254, 255, 1),
          rgba(132, 196, 203, 1),
          rgba(48, 151, 166, 0)
        )
        1 1;
    }
  }
  .content-icon-collapse {
    margin: auto;
    margin-top: 13px;
    width: 24px;
    height: 24px;
    flex-shrink: 0;
    display: flex;
    // background: url('@/assets/images/qhdsys/bg-tab.png') no-repeat;
    &:hover {
      cursor: pointer;
    }
    img {
      margin: auto;
    }
  }
  .sys-box {
    width: 100%;
    flex: 1;
    max-height: calc(100% - 39px);
    // max-height: 100%;
    .sys-box-content {
      width: 100%;
      height: 100%;
      padding: 5px 0px 0 0px;
      box-sizing: border-box;
    }
  }
}
</style>