<template>
  <el-dialog
    v-if="visible"
    v-dialogDrag
    :modal="false"
    :close-on-click-modal="false"
    title="工单详情"
    width="60%"
    append-to-body
    :visible.sync="visible"
    :show-close="false"
    custom-class="WorkOrderDetailDialog"
    :before-close="closeDialog"
  >
    <div class="dialog-right">
      <div class="dialog-tc" @click="closeDialog"></div>
    </div>
    <div class="dialog-content">
      <el-tabs v-if="dialogDetail.length > 1" v-model="activeWorkOderType" class="tabs" @tab-click="handleWorkOderTypeClick">
        <el-tab-pane v-for="item in dialogDetail" :key="item.id" :label="item.workTypeName" :name="item.id" />
      </el-tabs>
      <el-row
        :gutter="20"
        type="flex"
        style="align-items: stretch;"
        :style="{ height: `calc(100% - ${dialogDetail.length > 1 ? '49' : '0'}px) !important` }"
      >
        <el-col :md="12">
          <div class="left-content">
            <ContentCard title="工单信息" :cstyle="{'margin-bottom': '10px' }">
              <div slot="content" class="work-order-box">
                <div class="box-left-detail">
                  <div style="margin-bottom: 15px;">
                    <div class="work-order-label">工单号 :</div>
                    <div class="work-order-value">{{ workOrderDetail?.olgTaskManagement?.workNum ?? '' }}</div>
                  </div>
                  <div>
                    <div class="work-order-label">工单类型 :</div>
                    <div class="work-order-value">{{ workOrderDetail?.olgTaskManagement?.workTypeName ?? '' }}</div>
                  </div>
                </div>
                <div class="box-right-tag" :style="{ backgroundColor: flowtypeBgColor }">{{ workOrderDetail?.olgTaskManagement?.flowtype ?? '' }}</div>
              </div>
            </ContentCard>
            <ContentCard title="报警信息" :cstyle="{ 'margin-bottom': '10px' }">
              <div slot="content" class="work-order-box">
                <div class="box-left-detail">
                  <div style="margin-bottom: 15px;">
                    <div class="work-order-label">报警ID :</div>
                    <div class="work-order-value active-ID" :style="{'cursor': sourceType!='alarm' ? 'pointer' : 'default'}" @click="sourceType!='alarm' ? jumpToAlarmDetail() : ''">{{ workOrderDetail?.olgTaskManagement?.sysForShort }}</div>
                  </div>
                </div>
              </div>
            </ContentCard>
            <ContentCard title="相关人员">
              <div slot="content" class="work-order-box">
                <div class="box-left-detail">
                  <div style="margin-bottom: 15px;">
                    <div class="work-order-label">调度人 :</div>
                    <div class="work-order-value">{{ dispatcher }}</div>
                  </div>
                  <div>
                    <div class="work-order-label">执行人 :</div>
                    <div class="work-order-value">{{ executor }}</div>
                  </div>
                </div>
              </div>
            </ContentCard>
          </div>
        </el-col>
        <el-col :md="12">
          <div class="right-content">
            <ContentCard title="工单处置流程" :cstyle="{ height: '100%' }">
              <div slot="content" class="time-line">
                <div v-for="(item, index) in workOrderDisposalProcessList" :key="index" class="time-line-box">
                  <div class="top-date">
                    <div class="data-ymd">{{ item.time.split(' ')[0] }}</div>
                    <div class="defaule-icon" :class="item.icon"></div>
                    <div class="data-hms">{{ item.time.split(' ')[1] }}</div>
                  </div>
                  <div class="box-content">
                    <div class="time-work-order">
                      <div class="time-work-order-event" :style="{ color: item.titleColor }">{{ item.title }}</div>
                      <div v-for="(cont, i) in item.content" :key="i" class="work-order-detail">
                        <span>{{ cont.label }} :</span><span style="padding-left: 10px;">{{ cont.value }}</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </ContentCard>
          </div>
        </el-col>
      </el-row>
    </div>
    <span slot="footer" class="dialog-footer">
      <DealBtn v-if="dealBtnShow" :data="workOrderDetail" :alarmDetail="alarmDetail" @submit="dealSubmit" />
    </span>
    <AlarmDetailDialog v-if="alarmDetailShow" ref="alarmDetail" :alarmId="alarmDetail.alarmId" sourceType="workOrder" :visible.sync="alarmDetailShow" />
  </el-dialog>
</template>
<script>
import {
  getWorkOrderDetail,
  getIomsDictList
} from '@/utils/peaceLeftScreenApi'
// import moment from 'moment'
export default {
  name: 'WorkOrderDetailDialog',
  components: {
    AlarmDetailDialog: () => import('../AlarmDetailDialog'),
    ContentCard: () => import('../ContentCard'),
    DealBtn: () => import('./components/DealBtn')
  },
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    alarmDetail: {
      type: Object,
      default: () => {}
    },
    dialogDetail: {
      type: Array,
      default: () => []
    },
    sourceType: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      activeWorkOderType: '',
      workOrderDetail: {},
      dealBtnShow: false,
      alarmDetailShow: false,
      flowtypeBgColor: '',
      flowTypeList: [
        {
          id: '3',
          backgroundColor: '#3562DB'
        },
        {
          id: '5',
          backgroundColor: '#08CB83'
        }
      ],
      dispatcher: '', // 调度人
      executor: '', // 执行人
      workOrderDisposalProcessList: [], // 工单处置流程
      cancelReasonOptions: [] // 取消理由
    }
  },
  mounted() {
    // const dialogDetailLength = this.dialogDetail.length
    // if (dialogDetailLength >= 1) {
    //   this.activeWorkOderType = this.dialogDetail[0].id
    // }
    this.activeWorkOderType = this.dialogDetail.find((item) => item.active)?.id ?? this.dialogDetail[0]?.id
    this.getWorkOrderDetail(this.activeWorkOderType)
  },
  methods: {
    // 根据id获取工单详情
    getWorkOrderDetail(id) {
      getWorkOrderDetail({ id: id, operSource: 'souye' }).then((res) => {
        this.workOrderDetail = res.data
        this.dealBtnShow = true
        // 已取消 获取 字典项
        if (res.data.taskRecord.length && res.data.taskRecord.some((e) => e.operationCode === '7')) {
          this.getIomsDictList('cancel_reason')
        }
        // 工单状态颜色处理
        const flowType = this.flowTypeList.find((item) => item.id === this.workOrderDetail.olgTaskManagement.flowcode)
        this.flowtypeBgColor = flowType ? flowType.backgroundColor : '#FFC000'
        this.technologicalProcess(res.data)
      })
    },
    // 获取字典
    getIomsDictList(type) {
      const params = {
        type: type
      }
      getIomsDictList(params).then((res) => {
        this.cancelReasonOptions = res.data
      })
    },
    // 工单流程数据处理
    technologicalProcess(data) {
      const taskRecord = data.taskRecord
      const workOrderDisposalProcessList = []
      const executor = []
      this.dispatcher = ''
      taskRecord.length &&
        taskRecord.forEach((item) => {
          // 创建工单
          if (item.operationCode === '1') {
            workOrderDisposalProcessList.push({
              time: data.olgTaskManagement.createDate,
              sort: 1,
              title: '创建工单',
              content: [
                {
                  label: '联系人',
                  value: data.olgTaskManagement.callerName
                },
                {
                  label: '电话',
                  value: data.olgTaskManagement.sourcesPhone
                },
                {
                  label: '工号',
                  value: data.olgTaskManagement.callerJobNum
                }
              ]
            })
          } else if (item.operationCode === '2') {
            // 已受理
            this.dispatcher = item.createBy?.name
            workOrderDisposalProcessList.push({
              time: item.createDate,
              title: item.operationType,
              sort: 2,
              content: [
                {
                  label: '调度员',
                  value: item.createBy?.name
                },
                {
                  label: '职工工号',
                  value: item.createBy?.no
                }
              ]
            })
            // 已派工
          } else if (item.operationCode === '3') {
            executor.push(item.designatePersonName)
            workOrderDisposalProcessList.push({
              time: item.createDate,
              title: item.operationType,
              sort: 3,
              content: [
                {
                  label: '服务人员',
                  value: item.designatePersonName
                },
                {
                  label: '人员电话',
                  value: item.designatePersonPhone
                }
              ]
            })
            // 已挂单
          } else if (item.operationCode === '4') {
            workOrderDisposalProcessList.push({
              time: item.createDate,
              title: item.operationType,
              sort: 4,
              content: [
                {
                  label: '挂单说明',
                  value: item.disEntryOrdersReason
                }
              ]
            })
            // 已回访
          } else if (item.operationCode === '5') {
            executor.push(item.createBy?.name)
            workOrderDisposalProcessList.push({
              time: item.createDate,
              title: item.operationType,
              sort: 5,
              content: [
                {
                  label: '回访人',
                  value: item.createBy?.name
                },
                {
                  label: '回访时间',
                  value: item.createDate
                }
              ]
            })
            // 已完工
          } else if (item.operationCode === '6') {
            workOrderDisposalProcessList.push({
              time: item.createDate,
              title: item.operationType,
              sort: 7,
              icon: 'work-order-circle',
              titleColor: '#08cb83',
              content: [
                {
                  label: '完工说明',
                  value: data.olgTaskManagement.disFinishRemark
                }
              ]
            })
            // 已取消
          } else if (item.operationCode === '7') {
            const reason = this.cancelReasonOptions.find((e) => e.value === data.olgTaskManagement.cancelReasonId)
            workOrderDisposalProcessList.push({
              time: item.createDate,
              title: item.operationType,
              sort: 8,
              content: [
                {
                  label: '取消理由',
                  value: reason?.label ?? ''
                }
              ]
            })
            // 已督办
          } else if (item.operationCode === '8') {
            workOrderDisposalProcessList.push({
              time: item.createDate,
              title: item.operationType,
              sort: 6,
              content: [
                {
                  label: '督办说明',
                  value: item.feedbackExplain
                }
              ]
            })
            // 已转单
          } else if (item.operationCode === '9') {
            workOrderDisposalProcessList.push({
              time: item.createDate,
              title: item.operationType,
              sort: 9,
              content: [
                {
                  label: '服务部门',
                  value: item.designateDeptName
                }
              ]
            })
            // 已变更
          } else if (item.operationCode === '10') {
            workOrderDisposalProcessList.push({
              time: item.createDate,
              title: item.operationType,
              sort: 10,
              content: [
                {
                  label: '原服务部门',
                  value: item.designateDeptName
                }
              ]
            })
            // 已转派
          } else if (item.operationCode === '11') {
            executor.push(item.designatePersonName)
            workOrderDisposalProcessList.push({
              time: item.createDate,
              title: item.operationType,
              sort: 11,
              content: [
                {
                  label: '服务人员',
                  value: item.designatePersonName
                },
                {
                  label: '人员电话',
                  value: item.designatePersonPhone
                }
              ]
            })
          }
        })
      // moment 时间倒序
      // workOrderDisposalProcessList.sort((a, b) => {
      //   return moment(b.time).valueOf() - moment(a.time).valueOf()
      // })
      // sort 状态排序
      // workOrderDisposalProcessList.sort((a, b) => {
      //   return moment(b.sort).valueOf() - moment(a.sort).valueOf()
      // })
      // 倒序
      workOrderDisposalProcessList.reverse()
      this.workOrderDisposalProcessList = workOrderDisposalProcessList
      // executor长度大于0并过滤空值 去重
      if (executor.length) {
        this.executor = Array.from(new Set(executor.map(e => e.includes(',') ? e.split(',') : e)
          .flat().filter((e) => e))).join(',')
          .toString()
      } else {
        this.executor = ''
      }
    },
    // 跳转报警详情
    jumpToAlarmDetail() {
      this.alarmDetailShow = true
    },
    // 关闭弹窗
    closeDialog() {
      this.$emit('update:visible', !this.visible)
    },
    // 切换工单类型
    handleWorkOderTypeClick() {
      this.getWorkOrderDetail(this.activeWorkOderType)
    },
    // 按钮操作返回
    dealSubmit() {
      this.getWorkOrderDetail(this.activeWorkOderType)
    }
  }
}
</script>
<style lang="scss" scoped>
::v-deep .el-dialog{
  background: url('@/assets/images/qhdsys/big1-mask-bg.png') no-repeat center center / 100% 100%;
  .el-dialog__header {
    padding: 10px 20px 10px;
    text-align: center;
    .el-dialog__title {
      height: 20px;
      font-size: 18px;
      font-family: Source Han Sans CN-Regular, Source Han Sans CN;
      font-weight: 400;
      color: #cbdeed;
      line-height: 18px;
    }
  }
  .el-dialog__body {
    height: calc(78vh - 120px) !important;
    overflow-y: auto;
    display: flex;
  }
  .el-dialog__footer {
    padding-top: 0;
    padding-bottom: 40px;
    padding-right: 40px;
  }
}
.WorkOrderDetailDialog {
  .dialog-right {
    position: absolute;
    top: 17px;
    right: 60px;
    display: flex;
    .dialog-tc {
      width: 36px;
      height: 36px;
      background: url('@/assets/images/qhdsys/bg-icon-gb.png') no-repeat;
    }
    .dialog-tc:hover {
      cursor: pointer;
    }
  }
  .dialog-content {
    padding: 0px 40px;
    flex: 1;
    .el-row {
      height: 100%;
    }
    .active-ID {
      color: #0379f1 !important;
    }
    .left-content {
      height: 100%;
      // background: #fff;
      padding: 10px;
      overflow-y: auto;
      .work-order-box {
        // background: rgba(246, 245, 250, 0.1);
        border-radius: 4px;
        padding: 15px 10px;
        display: flex;
        .box-left-detail {
          flex: 1;
          & > div {
            display: flex;
            font-family: PingFang SC-Regular, PingFang SC;
            .work-order-label {
              width: 80px;
              color: #7EAEF9;
              text-align: right;
            }
            .work-order-value {
              flex: 1;
              padding-left: 10px;
              color: #fff;
              font-weight: 600;
            }
          }
        }
        .box-right-tag {
          width: 66px;
          height: 24px;
          line-height: 24px;
          text-align: center;
          font-size: 14px;
          font-family: PingFang SC-Medium, PingFang SC;
          color: #fff;
          background: #ff9435;
          border-radius: 24px;
        }
      }
    }
    .right-content {
      height: 100%;
      // background: #fff;
      .time-line {
        .time-line-box {
          margin-bottom: 6px;
          .top-date {
            height: 24px;
            line-height: 24px;
            display: flex;
            .data-ymd {
              min-width: 85px;
              font-size: 14px;
              font-family: PingFang SC-Medium, PingFang SC;
              color: #fff;
              font-weight: 600;
            }
            .defaule-icon {
              width: 12px;
              height: 12px;
              margin: auto 10px;
              background: #fff;
              border: 2px solid #FFE3A6;
              border-radius: 50%;
            }
            .data-hms {
              font-size: 14px;
              font-family: PingFang SC-Regular, PingFang SC;
              color: #fff;
            }
          }
          .box-content {
            margin-left: 100px;
            padding-left: 20px;
            border-left: 2px solid #f6f5fa;
            .time-work-order {
              background: rgba(246, 245, 250, 0.1);
              border-radius: 4px;
              padding: 15px 10px 10px 10px;
              font-family: PingFang SC-Regular, PingFang SC;
              .time-work-order-event {
                font-size: 16px;
                color: #FFE3A6;
                margin-bottom: 8px;
              }
              .work-order-detail {
                color: #fff;
                padding-bottom: 5px;
              }
            }
          }
        }
      }
    }
  }
}
</style>
<style lang="scss">
.tabs {
  // background: #fff;
  padding: 5px 10px;
  .el-tabs__item{
    color: #fff;
  }
  .el-tabs__active-bar{
    background: #FFE3A6;
  }
  .el-tabs__item.is-active{
    color: #FFE3A6;
  }
  .el-tabs__nav-wrap::after{
    display: none;
  }
}
</style>
