<!--
 * @Author: hedd
 * @Date: 2023-07-04 18:38:33
 * @LastEditTime: 2023-07-06 11:20:45
 * @FilePath: \ihcrs_client_iframe_elevator\src\views\elevator\cameraTalkBox.vue
 * @Description:
-->
<template>
  <div class="content">
    <div class="right-content">
      <cameraTalkContent :rowData="$route.query" @goback="goback" />
    </div>
  </div>
</template>

<script>
import cameraTalkContent from './components/cameraTalkContent.vue'
export default {
  name: 'cameraTalkBox',
  components: {
    cameraTalkContent
  },
  data() {
    return {}
  },
  mounted() {
  },
  methods: {
    goback() {
      try {
        window.chrome.webview.hostObjects.sync.bridge.ElevatorBack()
      } catch (error) {}
    }
  }
}
</script>

<style lang="scss" scoped>
.content {
  position: relative;
  box-sizing: border-box;
  width: 100%;
  height: 70%;
  .right-content {
    position: absolute;
    top: 0;
    right: 0;
    margin-top: 65px;
    width: 22%;
    height: 100%;
  }
}
</style>
