<template>
  <div class="monitor-echarts-view">
    <div class="mev-header">
      <el-form
        :model="searchForm"
        class="search-form"
        :class="{ collapsed }"
        inline
      >
        <el-form-item label="统计图名称：">
          <el-input
            v-model="searchForm.input"
            size="mini"
            placeholder="资产名称/编码/通用名"
          ></el-input>
        </el-form-item>
        <el-form-item label="报警时间：">
          <el-date-picker
            v-model="searchForm.time"
            popper-class="date-style"
            type="datetimerange"
            unlink-panels
            size="mini"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            value-format="yyyy-MM-dd HH:mm:ss"
          >
          </el-date-picker>
        </el-form-item>
      </el-form>
    </div>
    <div class="mev-content" style="height: calc(100% - 56px)">
      <el-carousel
        style="height: calc(100% - 56px)"
        indicator-position="outside"
        :arrow="'never'"
        :interval="10000"
        :autoplay="false"
        ref="carousel"
      >
        <el-carousel-item>
          <div class="echarts-carousel-box">
            <div
              class="echarts-box"
              v-for="(item, idx) in monitorInfoData"
              :key="idx"
            >
              <div class="echart-title">{{ item.title }}</div>
              <div class="echart" v-if="item.isData">
                <div :id="`line${idx}`" style="width: 100%; height: 100%"></div>
              </div>
              <div class="echart" v-else>
                <div class="none-box">
                  <div class="none-img">
                    <img
                      src="@/assets/images/zwsj.png"
                      style="width: 100%; height: 100%"
                      alt=""
                    />
                  </div>
                  <div class="none-text">暂无数据</div>
                </div>
              </div>
            </div>
          </div>
        </el-carousel-item>
      </el-carousel>
    </div>
  </div>
</template>
<script>
import * as echarts from "echarts";
export default {
  data() {
    return {
      collapsed: false,
      searchForm: {
        input: "",
        time: [],
      },
      monitorInfoData: [
        { title: "液氦液位/%", isData: false },
        { title: "磁体压力/mBar", isData: true },
        { title: "冷头效率/mW", isData: true },
        { title: "初级水冷温度/­°C", isData: true },
      ],
    };
  },
  mounted() {
    this.monitorInfoData.forEach((item, idx) => {
      this.$nextTick(() => {
        this.initLineChart(`line${idx}`);
      });
    });
  },
  methods: {
    /** 初始化 折线图 */
    initLineChart(id) {
      var chartDom = document.getElementById(id);
      var myChart = echarts.init(chartDom);
      var option;

      option = {
        tooltip: {
          trigger: "axis",
        },
        grid: {
          top: "5%",
          left: "3%",
          right: "4%",
          bottom: "3%",
          containLabel: true,
        },
        xAxis: {
          type: "category",
          boundaryGap: false,
          interval: "auto",
          axisLine: {
            show: true,
            lineStyle: {
              color: ["rgba(233, 233, 233,0.1)"], //分割线的颜色
              type: "dashed",
            },
          },
          axisLabel: {
            textStyle: {
              color: "rgba(230,247,255,0.6)", //坐标值的具体的颜色
            },
          },
          splitLine: {
            show: true,
            lineStyle: {
              color: ["rgba(233, 233, 233,0.1)"], //分割线的颜色
              type: "dashed",
            },
          },
          data: ["1月", "2月", "3月", "4月", "5月", "6月", "7月"],
        },
        yAxis: {
          type: "value",
          axisLabel: {
            textStyle: {
              color: "rgba(230,247,255,0.6)", //坐标值的具体的颜色
            },
          },
          splitLine: {
            show: true,
            lineStyle: {
              color: ["rgba(233, 233, 233,0.1)"], //分割线的颜色
              type: "dashed",
            },
          },
        },
        series: [
          {
            name: "",
            type: "line",
            stack: "Total",
            symbol: 'none',
            lineStyle: {
              // 线的颜色
              color: this.getRandomBrightColor(),
            },
            data: [120, 132, 101, 134, 90, 230, 210],
          },
        ],
      };

      myChart.resize();
      myChart.clear();
      option && myChart.setOption(option);
      window.addEventListener("resize", () => {
        myChart.resize();
      });
    },
    /** 随机生成颜色 */
    getRandomBrightColor() {
      const r = Math.floor(Math.random() * 106) + 150;
      const g = Math.floor(Math.random() * 106) + 150;
      const b = Math.floor(Math.random() * 106) + 150;
      return `rgb(${r}, ${g}, ${b})`;
    },
    pauseAutoPlay() {
      this.$refs.carousel.pause();
    },
    startAutoPlay() {
      this.$refs.carousel.start();
    },
    handleScroll(event) {
      if (event.deltaY > 0) {
        this.$refs.carousel.next();
      } else {
        this.$refs.carousel.prev();
      }
    },
  },
};
</script>
<style lang="scss" scoped>
.monitor-echarts-view {
  height: 100%;
}
.el-form-item {
  margin-bottom: 1rem;
}
.mev-content {
  //   background: red;
  .echarts-carousel-box {
    width: 100%;
    height: 100%;
    display: flex;
    flex-wrap: wrap;
    .echarts-box {
      flex-shrink: 0;
      width: calc(50% - 0.5rem);
      height: calc(50% - 0.5rem);
      background: #3562db0f;
      margin: 0px 1rem 1rem 0px;
      .echart-title {
        padding: 0.5rem;
        font-size: 16px;
      }
      .echart {
        height: calc(100% - 2rem);
        .none-box {
          height: 100%;
          display: flex;
          flex-direction: column;
          justify-content: center;
          align-items: center;
          .none-img {
            width: 12.5rem;
            height: 8.75rem;
            margin-bottom: 1rem;
          }
          .none-text {
            font-size: 1rem;
            color: #ffffffe6;
          }
        }
      }
    }
    .echarts-box:nth-child(2n) {
      margin-right: 0px;
      margin-bottom: 0px;
    }
    .echarts-box:nth-child(3) {
      margin-bottom: 0px;
    }
  }
}
.el-carousel,
.el-carousel-item {
  height: 100% !important;
}
::v-deep .el-carousel__container {
  height: calc(100% - 31px) !important;
  box-sizing: border-box;
}
// 修改指示器的样式
::v-deep {
  .el-carousel__indicator.is-active button {
    width: 25px;
    height: 5px;
    background: linear-gradient(280deg, #1474a4 0%, #0589cb 100%);
    border-radius: 2px;
    box-sizing: border-box;
  }
  .el-carousel__button {
    width: 10px;
    height: 5px;
    background: #c4c4c4;
    border-radius: 2px;
    box-sizing: border-box;
  }
}
::v-deep .el-date-editor .el-range-input {
  height: 1.75rem;
  background: transparent;
  color: #fff;
}
::v-deep .el-date-editor--datetimerange.el-input__inner {
  width: 18.75rem;
  background: transparent;
  border-color: #3056a2;
}
</style>