<template>
  <div class="riskDangerComponent">
    <div class="tabActive">
      <div style="display: flex;">
        <div
          v-for="(i, index) in activeOption"
          :key="index"
          class="tabItem"
          :class="active == i.id ? 'selected' : ''"
          @click="changeActive(i)">
          {{ i.name }}
        </div>
      </div>
      <img src="@/assets/images/qhdsys/bg-gd.png" alt="">
    </div>
    <template v-if="active == 'risk'">
      <div class="moduleContainer" style="height: 25%">
        <div class="titleBar">风险等级统计</div>
        <div class="module-content" style="height: calc(100% - 32px)">
          <div v-if="workOrderStatisticsShow" id="workOrderStatisticsEcharts" style="width: 100%; height: 100%"></div>
          <div v-else class="center-center">暂无数据</div>
        </div>
      </div>
      <div v-if="isBuilding" class="moduleContainer" style="height: 15%">
        <div class="titleBgBar">
          <div class="titleText">风险显示色</div>
          <div class="rightBar">
            <div class="tabItem fourColor" @click="showFourColor">
              <!-- <el-image ref="fourColorImg" :src="srcList[0] || ''" :preview-src-list="srcList" class="image"></el-image> -->
              风险四色图
            </div>
          </div>
        </div>
        <div class="dyeContent" style="height: calc(100% - 44px)">
          <!-- <el-checkbox :indeterminate="isIndeterminate" v-model="checkAll" @change="handleCheckAllChange">全选</el-checkbox> -->
          <el-checkbox-group v-model="dyeCheckList" @change="handleCheckedCitiesChange">
            <el-checkbox v-for="(i, index) in dyeOption" :key="index" :label="i.name">
              <div class="colorBar" :style="{'background-color': i.color }"></div>
              {{ i.name }}
            </el-checkbox>
          </el-checkbox-group>
        </div>
      </div>
      <div v-else class="moduleContainer" style="height: 30%">
        <div class="titleBgBar">
          <div class="titleText">风险排名</div>
          <div class="rightBar">
            <div class="btnBar">
              <div
                v-for="(i, index) in riskOption"
                :key="index"
                class="tabItem"
                :class="topType == i.id ? 'selected' : ''"
                @click="changeTopType(i)">
                {{ i.name }}
              </div>
            </div>
            <div class="sortText" @click="levelTop">
              {{checkList[0]}}
              <i v-if="menuShow" class="el-icon-arrow-up"></i>
              <i v-else class="el-icon-arrow-down"></i>
              <div v-show="menuShow" class="itemMenuWrap">
                <el-checkbox-group v-model="checkList">
                  <el-checkbox v-for="(i, index) in menuOption" :key="index" :label="i.name" class="itemMenu" @change="changeLevelFilter(i)"></el-checkbox>
                </el-checkbox-group>
              </div>
            </div>
          </div>
        </div>
        <div class="module-content" style="height: calc(100% - 44px)">
          <div v-if="riskRankingShow" style="width: 100%; height: 100%">
            <div id="riskRanking"></div>
          </div>
          <div v-else class="center-center">暂无数据</div>
        </div>
      </div>
      <div class="moduleContainer" :style="{ 'height': isBuilding ? '50%' : '35%'}">
        <div class="titleBgBar">
          <div class="titleText">风险点清单</div>
          <div class="rightBar">
            <img height="24px" src="@/assets/images/qhdsys/联合筛选.png" alt="筛选"  title="筛选" @click="riskLevelTop">
            <img height="24px" src="@/assets/images/qhdsys/bg-gd.png" alt="更多列表" title="更多列表" @click="riskListDialog('risk')">
            <div v-show="riskMenuShow" class="riskMenuWrap">
              <el-checkbox-group v-model="checkRiskLevel">
                <el-checkbox v-for="(i, index) in menuRiskOption" :key="index" :label="i.name" class="itemMenu" @change="changerRiskLevelFilter(i)"></el-checkbox>
              </el-checkbox-group>
            </div>
          </div>
        </div>
        <div class="bottom-tab" style="height: calc(100% - 54px);margin: 5px 0;">
          <el-table
            v-loading="tableLoading"
            class="table-center-transfer"
            :cell-style="{ padding: ' 8px', backgroundColor: 'transparent', border: 'none', padding: '3px' }"
            :header-cell-style="{ background: '#8591CE26!important', color: '#8BDDF5FF', padding: '4px 8px', fontWeight: 'bold' }"
            :data="tableData"
            element-loading-background="rgba(0, 0, 0, 0.2)"
            height="100%"
            style="width: 100%"
            @row-dblclick="toView"
          >
            <el-table-column fixed prop="riskName" show-overflow-tooltip label="风险点名称"></el-table-column>
            <el-table-column fixed prop="riskLevelName" width="100" show-overflow-tooltip label="风险等级">
              <template slot-scope="scope">
                <span class="riskLevelBar" :class="'color' + scope.row.riskLevel"></span>
                <span :class="'text' + scope.row.riskLevel">{{scope.row.riskLevelName}}</span>
              </template>
            </el-table-column>
            <el-table-column fixed prop="taskTeamName" width="100" show-overflow-tooltip label="责任部门"></el-table-column>
            <el-table-column fixed prop="responsiblePersonName" width="90" show-overflow-tooltip label="责任人"></el-table-column>
          </el-table>
        </div>
      </div>
      <riskInspection ref="riskInspection" :rowData="rowData"></riskInspection>
    </template>
    <template v-else>
      <dangerManagementVue v-if="dangerShow" :roomData="roomData"/>
    </template>
    <template v-if="allTableComponentListShow">
      <allTableComponentList ref="allTableComponentList" :dialogData="tableCompenentData" :dialogShow="allTableComponentListShow" @configCloseDialog="configCloseTableDialog"></allTableComponentList>
    </template>
    <el-dialog
      custom-class="imageWrap"
      :visible.sync="imageVisible"
      width="50%"
      :before-close="imageClose">
      <img :src="srcList" width="100%" alt="">
    </el-dialog>
  </div>
</template>

<script>
import { GetRiskLevelStatistics, GetRiskPageList, GetRiskDeptDistribution, GetRiskSpaceDistribution, getRiskInform } from '@/utils/centerScreenApi'
import * as echarts from 'echarts'
import riskInspection from './components/riskInspection.vue'
import dangerManagementVue from './dangerManagement.vue'
import allTableComponentList from '../components/allTableComponentList.vue'
import { dialogTypeList } from '@/assets/common/dict.js'
export default {
  name: 'riskDangerComponent',
  components: {
    riskInspection,
    dangerManagementVue,
    allTableComponentList
  },
  props: {
    roomData: {
      type: Object,
      default: () => {}
    },
    // 1：科室风险分布 2：风险告知
    type: {
      type: String,
      default: '1'
    }
  },
  data() {
    return {
      ssmCodes: [],
      tableLoading: false,
      imageVisible: false,
      tableData: [],
      workOrderStatisticsShow: true,
      riskRankingShow: true,
      dangerTopShow: true,
      pageParmes: {
        pageSize: 20,
        pageNo: 1
      },
      rowData: {},
      activeOption: [
        {
          id: 'danger',
          name: '隐患管理'
        },
        {
          id: 'risk',
          name: '风险管理'
        }
      ],
      active: 'danger',
      riskOption: [
        {
          id: 'dept',
          name: '部门'
        },
        {
          id: 'space',
          name: '建筑'
        }
      ],
      topType: 'dept',
      menuShow: false,
      menuOption: [
        {
          id: '',
          name: '全部风险排序'
        },
        {
          id: '1',
          name: '重大风险'
        },
        {
          id: '2',
          name: '较大风险'
        },
        {
          id: '3',
          name: '一般风险'
        },
        {
          id: '4',
          name: '低风险'
        }
      ],
      menuRiskOption: [
        {
          id: '',
          name: '全部风险'
        },
        {
          id: '1',
          name: '重大风险'
        },
        {
          id: '2',
          name: '较大风险'
        },
        {
          id: '3',
          name: '一般风险'
        },
        {
          id: '4',
          name: '低风险'
        }
      ],
      checkList: [],
      checkRiskLevel: [],
      riskMenuShow: false,
      dyeOption: [
        {
          id: '1',
          name: '重大风险',
          color: '#FF2D55'
        },
        {
          id: '2',
          name: '较大风险',
          color: '#FF9435'
        },
        {
          id: '3',
          name: '一般风险',
          color: '#FFBE00'
        },
        {
          id: '4',
          name: '低风险',
          color: '#3CC1FF'
        }
      ],
      dyeCheckList: [],
      checkAll: false,
      isIndeterminate: false,
      dangerShow: true,
      allTableComponentListShow: false,
      tableCompenentData: {}, // 风险过滤信息
      dialogTypeList,
      srcList: '',
      isBuilding: false
    }
  },
  watch: {
    'dyeCheckList': function(val) {
      const colorArr = []
      const codesArr = []
      if (val.length > 0) {
        this.dyeOption.forEach(i => {
          val.forEach(j => {
            if (i.name == j) {
              codesArr.push(i.id)
              colorArr.push(i.color)
            }
          })
        })
      }
      const sendData = {
        riskLevel: codesArr.join(','),
        color: colorArr.join(',')
      }
      try {
        window.chrome.webview.hostObjects.sync.bridge.SetRiskDangerRoomColor(JSON.stringify(sendData))
      } catch (error) {}
    },
    roomData: {
      handler(val) {
        this.activeMethods()
      },
      deep: true
    }
    // 'roomData.ssmType': function(val) {
    //   if (val >= 5) {
    //     this.isBuilding = true
    //     this.getRiskLevelAnalysis(this.ssmCodes.at(-1))
    //     this.getRiskPageList(this.ssmCodes.at(-1), '', '')
    //   }
    // }
  },
  mounted() {
    this.checkList = [this.menuOption[0].name]
    this.checkRiskLevel = [this.menuRiskOption[0].name]
    this.activeMethods()
  },
  methods: {
    // 切换隐患/风险
    changeActive(val) {
      this.active = val.id
      if (this.active == 'risk') {
        this.riskMenuShow = false
        this.menuShow = false
        this.activeMethods()
      } else {
        try {
          window.chrome.webview.hostObjects.sync.bridge.ZHMenuBarSwitch(this.active)
        } catch (error) {}
        this.dangerShow = true
      }
    },
    // 风险等级统计
    getRiskLevelAnalysis(id) {
      GetRiskLevelStatistics({
        placeIds: this.ssmCodes.at(-1) || ''
      }).then((res) => {
        if (res.data.code === '200') {
          if (res.data.data.array.length > 0) {
            this.$nextTick(() => {
              this.dangerTypeAnalysisEchart(res.data.data.array)
            })
          } else {
            this.workOrderStatisticsShow = false
          }
        }
      })
    },
    // 风险等级统计图表
    dangerTypeAnalysisEchart(obj) {
      const legendArr = []
      obj.forEach((i, index) => {
        if (i.level == '1') i.color = '#E50000'
        if (i.level == '2') i.color = '#E76C00'
        if (i.level == '3') i.color = '#FFEA31 '
        if (i.level == '4') i.color = '#0009DC'
        legendArr.push({
          type: 'plain',
          orient: 'vertical',
          right: 15,
          itemGap: 12,
          itemWidth: 8,
          itemHeight: 8,
          top: `${100 - (index + 1) * 21}%`,
          data: [
            {
              name: i.name,
              icon: 'rect',
              borderWidth: 0
            }
          ],
          padding: 5,
          backgroundColor: 'rgba(42,54,68, 0.4)',
          formatter: (name) => {
            return `{name|${name}} {value|${obj[index].value}} {bfb|${obj[index].rate}}`
          },
          textStyle: {
            color: '#545659',
            fontSize: 14,
            rich: {
              name: {
                padding: [4, 50, 0, 4],
                color: '#A4ACB9',
                align: 'left',
                backgroundColor: 'transparent',
                width: 40
              },
              value: {
                fontSize: 16,
                color: '#fff',
                padding: [4, 10, 0, 0],
                width: 30
              },
              bfb: {
                fontSize: 16,
                padding: [4, 15, 0, 0],
                align: 'right',
                color: '#fff',
                backgroundColor: 'transparent',
                width: 40
              }
            }
          }
        })
      })
      let getchart = echarts.init(document.getElementById('workOrderStatisticsEcharts'))
      getchart.resize()
      const option = {
        tooltip: {
          trigger: 'item'
        },
        color: obj.map(i => i.color),
        legend: legendArr,
        series: [
          {
            center: ['20%', '50%'],
            type: 'pie',
            // roseType: 'radius',
            data: obj,
            label: {
              show: false
            }
          }
        ]
      }
      getchart.clear()
      getchart.setOption(option)
      // 随着屏幕大小调节图表
      window.addEventListener('resize', () => {
        getchart.resize()
      })
    },
    // 风险排名 部门
    getRiskRankingData() {
      GetRiskDeptDistribution({
        placeIds: this.ssmCodes.at(-1) || '',
        riskLevel: this.menuOption.find(i => i.name == this.checkList[0]).id
      }).then(res => {
        if (res.data.code === '200') {
          this.$nextTick(() => {
            this.getRiskRankingEchart(res.data.data, 'dept')
          })
        }
      })
    },
    // 风险排名 建筑
    getRiskRankingSpaceData() {
      GetRiskSpaceDistribution({
        parentId: this.ssmCodes.at(-1) || '',
        riskLevel: this.menuOption.find(i => i.name == this.checkList[0]).id
      }).then(res => {
        if (res.data.code === '200') {
          this.$nextTick(() => {
            this.getRiskRankingEchart(res.data.data.reverse(), 'space')
          })
        }
      })
    },
    // 风险排名图表
    getRiskRankingEchart(data, type) {
      const getchart = echarts.init(document.getElementById('riskRanking'))
      getchart.resize()
      const option = {
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow'
          }
        },
        grid: {
          top: 5,
          bottom: 2,
          right: 10,
          left: 2,
          containLabel: true
        },
        xAxis: {
          show: false
        },
        yAxis: [
          {
            data: type == 'space' ? data.map(i => i.gridName) : data.map(i => i.teamName),
            axisLine: {
              show: false
            },
            axisTick: {
              show: false
            },
            axisLabel: {
              color: '#fff',
              fontSize: 14
            }
          },
          {
            show: true,
            inverse: true,
            data: data.map(i => i.count  + '个').reverse(),
            axisLine: {
              show: false
            },
            splitLine: {
              show: false
            },
            axisTick: {
              show: false
            },
            axisLabel: {
              color: 'rgba(255,255,255 0.8)',
              fontSize: 14
            }
          }
        ],
        series: [
          {
            type: 'bar',
            showBackground: true,
            backgroundStyle: {
              borderRadius: 10
            },
            barWidth: 6,
            barGap: 30,
            data: data.map(i => i.count),
            itemStyle: {
              borderRadius: 10,
              // 鼠标移入改变颜色
              emphasis: {
                color: '#FFCA64FF'
              },
              showBackground: true, // 显示背景色
              backgroundStyle: {
                color: '#384156'
              }
            }
          }
        ],
        dataZoom: [
          {
            yAxisIndex: [0, 1],
            orient: 'vertical',
            show: true,
            type: 'slider',
            startValue: 100,
            // end: 100,
            width: 8,
            left: '99%',
            borderColor: 'rgba(43,48,67,.1)',
            fillerColor: '#6580b8',
            zoomLock: true,
            showDataShadow: false,
            backgroundColor: 'rgba(43,48,67,.1)',
            showDetail: false,
            // realtime: true,
            filterMode: 'filter',
            handleIcon: 'M-9.35,34.56V42m0-40V9.5m-2,0h4a2,2,0,0,1,2,2v21a2,2,0,0,1-2,2h-4a2,2,0,0,1-2-2v-21A2,2,0,0,1-11.35,9.5Z',
            handleStyle: {
              color: '#6580b8',
              borderColor: '#6580b8'
            },
            maxValueSpan: 4,
            minValueSpan: 4,
            brushSelect: false
          },
          {
            type: 'inside',
            show: false,
            yAxisIndex: [0, 1],
            height: 8
          }
        ]
      }
      getchart.clear()
      getchart.setOption(option)
      getchart.getZr().off('click')
      getchart.getZr().on('click', params => {
        let op = getchart.getOption()
        let pointInPixel = [params.offsetX, params.offsetY]
        if (getchart.containPixel('grid', pointInPixel)) {
          const xIndex = getchart.convertFromPixel({ seriesIndex: 0 }, pointInPixel)[1]
          let handleIndex = Number(xIndex)
          var name = op.yAxis[0].data[handleIndex]  // 当前点击的 Y轴 的名称
          if (this.topType == 'space') {
            const target = data.find(i => i.gridName == name)
            this.getRiskPageList(this.ssmCodes.at(-1), 'space', target.spaceId)
          } else {
            const target = data.find(i => i.teamName == name)
            this.getRiskPageList(this.ssmCodes.at(-1), 'dept', target.teamId)
          }
        }
      })
      // 随着屏幕大小调节图表
      window.addEventListener('resize', () => {
        getchart.resize()
      })
    },
    // 切换部门/建筑 风险
    changeTopType(i) {
      this.topType = i.id
      this.checkList = [this.menuOption[0].name]
      if (this.topType == 'space') {
        this.getRiskRankingSpaceData()
      } else {
        this.getRiskRankingData()
      }
    },
    levelTop() {
      this.menuShow = !this.menuShow
    },
    riskLevelTop() {
      this.riskMenuShow = !this.riskMenuShow
    },
    // 切换等级排名
    changeLevelFilter(val) {
      this.checkList = [val.name]
      this.menuShow = false
      if (this.topType == 'space') {
        this.getRiskRankingSpaceData()
      } else {
        this.getRiskRankingData()
      }
    },
    // 切换清单排名
    changerRiskLevelFilter(val) {
      this.checkRiskLevel = [val.name]
      this.riskMenuShow = false
      this.getRiskPageList(this.ssmCodes.at(-1), '', '')
    },
    // 风险清单列表
    getRiskPageList(id, type, filterId) {
      const params = {
        placeIds: id,
        ...this.pageParmes,
        riskLevel: this.menuRiskOption.find(i => i.name == this.checkRiskLevel[0]).id || ''
      }
      if (type == 'dept') {
        params.deptCode = filterId
      }
      if (type == 'space') {
        params.placeIds = filterId
      }
      GetRiskPageList(params).then((res) => {
        if (res.data.code === '200') {
          this.tableData = res.data.data.list
        }
      })
    },
    // 风险点详情
    toView(row) {
      this.rowData = row
      this.$refs.riskInspection.getRiskDetail(row.id)
      this.$refs.riskInspection.hiddenDangerDetailsListShow = true
      try {
        window.chrome.webview.hostObjects.sync.bridge.GetSwichFloorIsShow(true)
        window.chrome.webview.hostObjects.sync.bridge.ShowDialog(true)
      } catch (error) {}
    },
    activeMethods() {
      try {
        window.chrome.webview.hostObjects.sync.bridge.ZHMenuBarSwitch(this.active)
      } catch (error) {}
      this.ssmCodes = this.roomData.ssmCodes?.split(',') || []
      if (this.roomData.ssmType >= 5) {
        this.isBuilding = true
      } else {
        this.getRiskRankingData()
        this.isBuilding = false
      }
      this.getRiskPageList(this.ssmCodes.at(-1), '', '')
      this.getRiskLevelAnalysis(this.ssmCodes.at(-1))
    },
    // 显色全选切换
    handleCheckAllChange(val) {
      this.dyeCheckList = val ? this.dyeOption.map(i => i.name) : []
      this.isIndeterminate = false
    },
    handleCheckedCitiesChange(val) {
      if (this.dyeCheckList.length > 1) {
        this.dyeCheckList.splice(0, 1)
      }
      // let checkedCount = val.length
      // this.checkAll = checkedCount === this.dyeOption.length
      // this.isIndeterminate = checkedCount > 0 && checkedCount < this.dyeOption.length
    },
    riskListDialog(type) {
      this.allTableComponentListShow = true
      Object.assign(this.tableCompenentData, {
        ...this.roomData,
        ...this.dialogTypeList.find((e) => e.type === type),
        height: 'calc(100% - 150px)'
      })
    },
    configCloseTableDialog() {
      this.allTableComponentListShow = false
    },
    showFourColor() {
      getRiskInform({
        placeIds: this.ssmCodes.at(-1)
      }).then(res => {
        if (res.data.code === '200') {
          this.srcList = res.data.data.pictureUrl !== '' ? res.data.data.pictureUrl : ''
          if (this.srcList.length == 0) {
            this.$message({
              type: 'info',
              message: '暂无更多'
            })
          } else {
            this.imageVisible = true
            try {
              window.chrome.webview.hostObjects.sync.bridge.ShowDialog(true)
            } catch (error) { }
          }
        }
      })
    },
    imageClose() {
      this.imageVisible = false
      try {
        window.chrome.webview.hostObjects.sync.bridge.ShowDialog(true)
      } catch (error) { }
    }
  }
}
</script>
<style lang="scss" scoped>
@import '../style/module.scss';
.riskDangerComponent {
  width: 100%;
  height: 100%;
  .tabActive {
    background-color: rgba($color: #8591CE, $alpha: 0.15);
    padding: 0 8px;
    height: 50px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    .tabItem {
      margin-right: 8px;
      background-color: rgba($color: #8591CE, $alpha: 0.15);
      font-size: 14px;
      color: #B0E3FA;
      height: 26px;
      line-height: 26px;
      width: 80px;
      text-align: center;
      cursor: pointer;
    }
    .selected {
      background: url('@/assets/images/qhdsys/bg-tab2-xz.png') no-repeat center;
      background-size: 80px 26px;
      color: #8BDDF5;
    }
  }
  .moduleContainer {
    margin-bottom: 16px;
    .titleBar {
      background-color: rgba($color: #8591CE, $alpha: 0.15);
      height: 32px;
      line-height: 32px;
      padding: 0 8px;
      color: #fff;
      font-size: 15px;
    }
    .titleBgBar {
      height: 44px;
      line-height: 44px;
      background: url('@/assets/images/qhdsys/bg-bt.png') no-repeat center;
      background-size: 100% 44px;
      display: flex;
      justify-content: space-between;
      .titleText {
        margin-left: 38px;
        text-shadow: 0px 0px 9px #158EFF;
        font-weight: bold;
      }
      .rightBar {
        display: flex;
        align-items: center;
        font-size: 14px;
        margin-right: 8px;
        position: relative;
        .btnBar {
          display: flex;
          align-items: center;
        }
        .tabItem {
          margin-right: 6px;
          background-color: rgba($color: #8591CE, $alpha: 0.15);
          font-size: 14px;
          color: #B0E3FA;
          height: 26px;
          line-height: 26px;
          padding: 0 12px;
          text-align: center;
          cursor: pointer;
        }
        .selected {
          background: url('@/assets/images/qhdsys/bg-tab2-xz.png') no-repeat center;
          background-size: 52px 26px;
          color: #8BDDF5;
        }
        .sortText {
          width: 105px;
          text-align: right;
          cursor: pointer;
          position: relative;
          .itemMenuWrap {
            position: absolute;
            display: flex;
            width: 120px;
            left: -18px;
            flex-direction: column;
            background-color: #374B79;
            z-index: 9999;
            ::v-deep .el-checkbox-group {
              padding: 5px;
              line-height: 0 !important;
              display: flex;
              flex-direction: column;
              align-items: flex-start;
              .el-checkbox:hover {
                .el-checkbox__label {
                  color: #8BDDF5 !important;
                }
              }
            }
          }
        }
        .riskMenuWrap {
          padding-bottom: 5px;
          position: absolute;
          display: flex;
          width: 120px;
          left: -75px;
          top: 45px;
          flex-direction: column;
          background-color: #374B79;
          z-index: 9999;
          ::v-deep .el-checkbox-group {
            line-height: 0;
            padding: 5px;
            display: flex;
            flex-direction: column;
            align-items: flex-start;
            .el-checkbox:hover {
              .el-checkbox__label {
                color: #8BDDF5 !important;
              }
            }
          }
        }
        .fourColor {
          position: relative;
          ::v-deep .el-image {
            position: absolute;
            width: 94px;
            height: 26px;
            top: 0;
            right: 0;
            z-index: -999;
          }
        }
        .fourColor:hover {
          background: url('@/assets/images/qhdsys/bg-tab2-xz.png') no-repeat center;
          background-size: 94px 26px;
          color: #8BDDF5;
        }
      }
    }
    .dyeContent {
      padding: 8px 0 0 8px;
      ::v-deep .el-checkbox-group {
        .itemMenu {
          height: 14px;
          line-height: 14px;
          padding: 5px;
        }
        .el-checkbox {
          width: 50%;
          margin: 0;
          float: left;
          display: flex;
          align-items: center;
          .el-checkbox__label {
            display: flex;
            align-items: center;
            .colorBar {
              margin-right: 5px;
              width: 12px;
              height: 8px;
            }
          }
        }
      }
      ::v-deep .el-checkbox {
        padding: 2px 0;
      }
      ::v-deep .is-indeterminate {
        .el-checkbox__inner {
          border-color: #2e529e !important;
          background-color: unset;
        }
        .el-checkbox__inner::before {
          background-color: #52FFFC;
        }
      }
    }
    .dangerStatistics {
      padding: 16px 8px;
      height: 52px;
      display: flex;
      .dangerItem {
        width: 25%;
        font-size: 14px;
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        >div:last-child {
          font-size: 20px;
          font-weight: bold;
          color: #FFCA64;
        }
      }
    }
  }
  #workOrderStatisticsEcharts {
    width: 100%;
    height: 100%;
    position: relative;
    z-index: 2;
  }
  #riskRanking {
    width: 100%;
    height: 100%;
    position: relative;
    z-index: 2;
  }
  ::v-deep .el-table {
    border: none !important;
    .el-table__header-wrapper {
      .cell {
        padding-left: 0;
        padding-right: 0;
        text-align: center;
        white-space: nowrap;
      }
    }
    .el-table__body-wrapper {
      td.el-table__cell div {
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
      }
    }
    .el-table__body {
      tr {
        background: center;
      }
      td.el-table__cell,
      th.el-table__cell.is-leaf {
        border-right: 2px solid #0a164e;
        border-bottom: 2px solid #0a164e;
        background: rgba(56, 103, 180, 0.2);
        color: #fff;
      }
      .el-table__row:nth-child(2n - 1) {
        background: rgba(168, 172, 171, 0.08);
      }
      .el-table__row:hover {
        border: 0;
        opacity: 1;
        cursor: pointer;

        td div {
          color: rgba(255, 202, 100, 1);
        }
      }
    }
  }
}
::v-deep .imageWrap {
  background: unset !important;
  .el-dialog__header {
    padding: 0 !important;
  }
   .el-dialog__body {
    padding: 0 !important;
  }
  .el-dialog__headerbtn {
    font-size: 24px !important;
  }
}
</style>
