<template>
  <div class="left-content-item">
    <CardTitle
      title="科室椅位平均接诊量排行"
      position="right"
    >
      <template slot="right">
        <el-dropdown trigger="click" @command="dataTypeCommand">
          <span class="el-dropdown-link dropdown-title">
            {{ dataTypeList.find((v) => v.value == dateType)?.name ?? "" }}
            <i class="el-icon-caret-bottom"></i>
          </span>
          <el-dropdown-menu slot="dropdown">
            <el-dropdown-item
              v-for="item in dataTypeList"
              :key="item.value"
              :command="item.value"
              :class="{ isBjxl: dateType == item.value }"
            >
              {{ item.name }}
            </el-dropdown-item>
          </el-dropdown-menu>
        </el-dropdown>
        <el-dropdown trigger="click" @command="orderTypeCommand">
          <span class="el-dropdown-link dropdown-title">
            {{ orderTypeList.find((v) => v.value == sort)?.name ?? "" }}
            <i class="el-icon-caret-bottom"></i>
          </span>
          <el-dropdown-menu slot="dropdown">
            <el-dropdown-item
              v-for="item in orderTypeList"
              :key="item.value"
              :command="item.value"
              :class="{ isBjxl: dateType == item.value }"
            >
              {{ item.name }}
            </el-dropdown-item>
          </el-dropdown-menu>
        </el-dropdown>
      </template>
    </CardTitle>
    <div v-loading="loading" class="card-content">
      <ContentList :list="visitDocList" :option="{name: 'deptAlias', value: 'zg', unit: '人/次'}"/>
    </div>
  </div>
</template>
<script>
import CardTitle from './title'
import ContentList from './contentList'
import { averageDeptconsultation } from '@/utils/runOverviewSJY'
import dayjs from 'dayjs'
export default {
  components: {
    CardTitle,
    ContentList
  },
  data() {
    return {
      loading: false,
      dateType: 'day',

      dataTypeList: [
        { value: 'day', name: '日', date: [] },
        { value: 'month', name: '月', date: [] },
        { value: 'year', name: '年', date: [] }
      ],
      orderTypeList: [
        { value: 1, name: '从多到少' },
        { value: 2, name: '从少到多' }
        // { value: 3, name: '占比从大到小' },
        // { value: 4, name: '占比从小到大' }
      ],
      sort: 1,

      visitDocList: [

      ]
    }
  },
  mounted () {
    this.getList()
  },
  methods: {
    initDate(val) {
      let arr = []
      switch (val) {
        case 'day':
          arr = [dayjs().format('YYYY-MM-DD'), dayjs().add(1, 'day').format('YYYY-MM-DD')]
          break
        // case 'week':
        //   arr = [dayjs().startOf('week').format('YYYY-MM-DD'), dayjs().endOf('week').format('YYYY-MM-DD')]
        //   break
        case 'month':
          arr = [dayjs().startOf('month').format('YYYY-MM-DD'), dayjs().endOf('month').format('YYYY-MM-DD')]
          break
        case 'year':
          arr = [dayjs().startOf('year').format('YYYY-MM-DD'), dayjs().endOf('year').format('YYYY-MM-DD')]
          break
      }
      return arr
    },
    dataTypeCommand(val) {
      this.dateType = val
      this.getList()
    },
    orderTypeCommand(val) {
      this.sort = val
      this.getList()
    },

    getList () {
      this.loading = true
      let params = {
        visitDateStart: this.initDate(this.dateType)[0],
        visitDateEnd: this.initDate(this.dateType)[1],
        sort: this.sort
      }
      averageDeptconsultation(params).then(res => {
        if (res.data.code == 200) {
          this.visitDocList = res.data.data
        }
      }).finally(() => {
        this.loading = false
      })
    }
  }
}
</script>
<style lang="scss" scoped>
.left-content-item {
  width: 100%;
  height: calc(100% / 3 - 7px);
  background: url("~@/assets/images/qhdsys/new-card-bg.png") no-repeat;
  background-size: 100% 100%;
  margin-bottom: 10px;
  margin-bottom: 0;

  .card-content {
    height: calc(100% - 36px);
    width: 100%;
    padding: 16px;
  }
}
.dropdown-title{
  color: #fff;
}
</style>
