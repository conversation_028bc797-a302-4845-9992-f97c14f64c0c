<template>
  <div class="openDoorAnalysis">
    <div class="search-date">
      <el-dropdown trigger="click" @command="dateTypeCommand">
        <span class="el-dropdown-link"> {{ dateTypeList.find((v) => v.value == dateType)?.name ?? '' }} <i class="el-icon-caret-bottom"></i> </span>
        <el-dropdown-menu slot="dropdown">
          <el-dropdown-item v-for="item in dateTypeList" :key="item.value" :command="item.value" :class="{ isBjxl: dateType == item.value }">{{ item.name }}</el-dropdown-item>
        </el-dropdown-menu>
      </el-dropdown>
      <el-date-picker v-model="currentDate" class="datePickerInput" popper-class="date-style" type="daterange" value-format="yyyy-MM-dd" :disabled="dateType != 'customize'" range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期" @change="initData" @focus="setWPFBgShow()" @blur="setWPFBgHide()">
      </el-date-picker>
    </div>
    <div class="contnet-count">
      <div v-for="item in countData" :key="item.name" class="count-item">
        <img class="count-item-icon" :src="item.icon" :alt="item.name">
        <div class="count-item-right">
          <div class="count-item-name">{{ item.name }}</div>
          <div class="count-item-value" @click="openDoorRecord('')">{{ item.value || 0 }}</div>
        </div>
      </div>
    </div>
    <div class="contnet-main">
      <ModuleCard title="开门时间统计" class="module-container" style="height: 40%">
        <div slot="title-right" class="title-right">
          <el-dropdown trigger="click" @command="timeTagChange">
            <span class="el-dropdown-link"> {{ tagList.find((v) => v.value == timeTag)?.text ?? '' }} <i class="el-icon-arrow-down"></i> </span>
            <el-dropdown-menu slot="dropdown">
              <el-dropdown-item v-for="item in tagList" :key="item.value" :command="item.value" :disabled="!item.is()" :class="{ isBjxl: timeTag == item.value }">{{ item.text }}</el-dropdown-item>
            </el-dropdown-menu>
          </el-dropdown>
        </div>
        <div slot="content" class="module-content" style="height: 100%">
          <div id="openDoorTime" style="width: 100%; height: 100%"></div>
        </div>
      </ModuleCard>
      <ModuleCard title="开门占比" class="module-container" style="height: 30%">
        <div slot="title-right" class="title-right">
          <el-dropdown trigger="click" @command="ratioTagChange">
            <span class="el-dropdown-link"> {{ tagList.find((v) => v.value == ratioTag)?.text ?? '' }} <i class="el-icon-arrow-down"></i> </span>
            <el-dropdown-menu slot="dropdown">
              <el-dropdown-item v-for="item in tagList" :key="item.value" :command="item.value" :disabled="!item.is()" :class="{ isBjxl: ratioTag == item.value }">{{ item.text }}</el-dropdown-item>
            </el-dropdown-menu>
          </el-dropdown>
        </div>
        <div slot="content" class="module-content" style="height: 100%">
          <div id="openDoorRatio" style="width: 100%; height: 100%; overflow: hidden;"></div>
        </div>
      </ModuleCard>
      <ModuleCard title="电梯开门统计" class="module-container" style="height: 30%">
        <div slot="title-right" class="title-right">
          <el-dropdown trigger="click" @command="sortTagChange">
            <span style="display: flex;"><svg-icon class="right-svg" name="right-filter" /></span>
            <el-dropdown-menu slot="dropdown">
              <el-dropdown-item v-for="item in sortList" :key="item.value" :command="item.value" :class="{ isBjxl: sortTag == item.value }">{{ item.text }}</el-dropdown-item>
            </el-dropdown-menu>
          </el-dropdown>
        </div>
        <div slot="content" class="module-content" style="height: 100%">
          <div id="elevatorOpenDoor" style="width: 100%; height: 100%"></div>
        </div>
      </ModuleCard>
    </div>
    <OpenDoorRecord v-if="isOpenDoorRecord" :dialogShow="isOpenDoorRecord" :roomData="roomData" :selectData="selectData" @openDoorRecordClose="isOpenDoorRecord = false" />
  </div>
</template>

<script>
import {  openDooraAalysis, openDoorTimeStatistics, doorOpenedRatio, doorOpenedCntList } from '@/utils/newIot'
import openDoorIcon from '@/assets/images/elevator/openDoorIcon.png'
import moment from 'moment'
moment.locale('zh-cn')
import * as echarts from 'echarts'
import OpenDoorRecord from './openDoorRecord.vue'
export default {
  name: 'openDoorAnalysis',
  components: {
    OpenDoorRecord
  },
  props: {
    roomData: {
      type: Object,
      default: () => { }
    }
  },
  data() {
    return {
      isOpenDoorRecord: false,
      dateType: 'day', // 日期类型
      days: 0, // 间隔天数
      currentDate: [moment().format('YYYY-MM-DD'), moment().format('YYYY-MM-DD')], // 选中日期
      dateTypeList: [
        { value: 'day', name: '今日' },
        { value: 'week', name: '本周' },
        { value: 'month', name: '本月' },
        { value: 'year', name: '本年' },
        { value: 'customize', name: '自定义' }
      ],
      countData: [
        { name: '开关门总数', icon: openDoorIcon, value: 0 },
        { name: '时段平均次数', icon: openDoorIcon, value: 0 }
      ],
      timeTag: 'hour', // 开门时间时间类型
      ratioTag: 'hour', // 开门占比时间类型
      tagList: [
        { text: '小时', value: 'hour', is: () => ['day', 'week', 'month', 'year', 'customize'].includes(this.dateType) },
        { text: '日', value: 'day', is: () => ['week', 'customize'].includes(this.dateType) && this.days <= 7 },
        { text: '周', value: 'week', is: () => ['month', 'year', 'customize'].includes(this.dateType) && this.days > 28 },
        { text: '月', value: 'month', is: () => ['year', 'customize'].includes(this.dateType) && this.days > 356 }
      ],
      sortTag: 2, // 排序类型
      sortList: [
        { text: '按从高到低', value: 2 },
        { text: '按从低到高', value: 1 }
      ],
      selectData: {}
    }
  },
  computed: {

  },
  watch: {
    roomData: {
      handler(val) {
        if (val.ssmType == 3 || val.ssmType == 4) {
          this.initData()
        }
      },
      deep: true
    },
    isOpenDoorRecord(val) {
      try {
        window.chrome.webview.hostObjects.sync.bridge.ShowDialog(val)
      } catch (error) {}
    }
  },
  created () {
    this.initData()
  },
  methods: {
    openDoorRecord (type) {
      let params = {
        dateType: this.dateType,
        countType: type == 'timeTag' ? this.timeTag : type == 'ratioTag' ? this.ratioTag : '',
        dateRange: this.currentDate
      }
      // if (type == 'ratioTag') params.elevatorId = dataList[data.data[1]].surveyCode.split(',')
      // if (!type) params.elevatorId = data?.data?.code?.split(',') ?? []
      this.selectData = params
      this.isOpenDoorRecord = true
    },
    initData() {
      this.getOpenCount()
      this.getOpenCountTimePeriod()
      this.getOpenCountGroupSurvey()
      this.getOpenCountTimeRatio()

    },
    // 获取电梯开门次数
    getOpenCountGroupSurvey() {
      let param = {
        dateType: this.dateType,
        startTime: this.currentDate[0],
        endTime: this.currentDate[1],
        order: this.sortTag
      }
      doorOpenedCntList(param).then(res => {
        if (res.data.code == 200) {
          if (res.data.result.liftTotalList) {
            res.data.result.liftTotalList.forEach((el, index) => {
              el.name = el.liftName
              el.value = el.doorOpenedCntSum
            })
          }
          this.commonChart(res.data.result.liftTotalList, 'elevatorOpenDoor', '')
        }
      })
    },
    // 获取电梯开门占比
    getOpenCountTimeRatio() {
      let param = {
        dateType: this.dateType,
        startTime: this.currentDate[0],
        endTime: this.currentDate[1],
        countType: this.ratioTag
      }
      doorOpenedRatio(param).then(res => {
        if (res.data.code == 200) {
          let newArr = []
          res.data.result.forEach((item, index) => {
            item.list.forEach((v, i) => {
              newArr.push([i, index, v.count])
            })
          })
          this.openDoorRatioChart(res.data.result[0]?.list.map(v => v.timeStr), res.data.result.map(v => v.name), newArr, res.data.result)
        }
      })
    },
    // 开门时间统计
    getOpenCountTimePeriod() {
      let param = {
        dateType: this.dateType,
        sTime: this.currentDate[0],
        eTime: this.currentDate[1],
        countType: this.timeTag
      }
      openDoorTimeStatistics(param).then(res => {
        if (res.data.code == 200) {
          if (res.data.result.liftTotalList) {
            res.data.result.liftTotalList.forEach((el, index) => {
              el.value = el.doorOpenedTimeCount
              el.name = el.timeStr
            })
          }
          this.commonChart(res.data.result.liftTotalList, 'openDoorTime', 'timeTag')
        }
      })
    },
    // 获取开关门统计
    getOpenCount() {
      let param = {
        sTime: this.currentDate[0],
        eTime: this.currentDate[1],
        dateType: this.dateType,
        countType: this.dateType
      }
      openDooraAalysis(param).then(res => {
        if (res.data.code == 200) {
          this.countData[0].value = res.data.result.doorOpenedCntCount
          this.countData[1].value = res.data.result.doorOpenedCntAvg
        }
      })
    },
    // 开门占比图表
    openDoorRatioChart (xData, yData, list, data) {
      let chart = echarts.init(document.getElementById('openDoorRatio'))
      let option = {}
      if (list.length) {
        option = {
          // backgroundColor: 'transparent',
          grid: {
            left: '3%',
            right: '5%',
            top: '5%',
            bottom: '5%',
            containLabel: true
          },
          tooltip: {
            // trigger: 'axis',
            axisPointer: {
              lineStyle: {
                color: 'rgb(30, 243, 249)'
              }
            },
            backgroundColor: '#122140',
            borderColor: '#91D2FF',
            textStyle: {
              color: '#fff'
            },
            formatter: (item) => {
              let name = data[item.data[1]].name
              let time = data[item.data[1]].list[item.data[0]].timeStr
              let count = data[item.data[1]].list[item.data[0]].count
              let ratio = data[item.data[1]].list[item.data[0]].ratio
              return `${name}</br>统计时间：${time}</br>开门次数：${count}次</br>开门占比：${ratio}%`
            }
          },
          xAxis: {
            position: 'buttom',
            type: 'category',
            data: xData,
            splitArea: {
              show: true
            },
            axisLabel: {
              textStyle: {
                fontSize: 12,
                color: 'rgba(230, 247, 255, 0.50)'
              }
            },
            axisLine: {
              show: false
            },
            axisTick: {
              show: false
            }
          },
          yAxis: {
            nameLocation: 'end',
            type: 'category',
            data: yData,
            axisLine: {
              show: false
            },
            axisTick: {
              show: false
            },
            axisLabel: {
              width: 120,
              overflow: 'truncate', // 超出的部分截断
              truncate: '...', // 截断的部分用...代替
              textStyle: {
                fontSize: 14,
                color: '#fff'
              }
            },
            splitArea: {
              show: true
            }
          },
          visualMap: {
            show: false,
            min: -1,
            max: 100,
            calculable: true,
            // splitNumber: 4,
            orient: 'horizontal',
            inRange: {
              // colorLightness: [0, 1],
              color: ['transparent', 'rgba(255, 45, 85, 1)']
              // color: ['rgba(255, 45, 85, 0)', 'rgba(255, 45, 85, 1)']
              // symbolSize: [0, 100]
            }
          },
          series: [{
            name: '',
            type: 'heatmap',
            data: list,
            label: {
              // color: '#fff',
              fontSize: 10,
              show: true,
              formatter: function (data) {
                const dataValue = data.value[2]
                if (dataValue >= 100 || dataValue <= 0) {
                  return dataValue.toFixed(0)
                } else {
                  return dataValue.toFixed(1)
                }
              }
            },
            // labelLayout: {
            //   height: 200
            // },
            itemStyle: {
              // opacity: 0.1,
              borderColor: 'rgba(0, 0, 0, 0)',
              emphasis: {
                borderColor: '#FFCA64'
              }
            }
          }],
          dataZoom: [
            {
              yAxisIndex: [0, 1],
              orient: 'vertical',
              show: true,
              type: 'slider',
              start: 100,
              // end: 100,
              // width: 8,
              left: '99%',
              borderColor: 'rgba(43,48,67,.1)',
              fillerColor: '#6580b8',
              zoomLock: true,
              showDataShadow: false,
              backgroundColor: 'rgba(43,48,67,.1)',
              showDetail: false,
              // realtime: true,
              filterMode: 'filter',
              handleIcon: 'M-9.35,34.56V42m0-40V9.5m-2,0h4a2,2,0,0,1,2,2v21a2,2,0,0,1-2,2h-4a2,2,0,0,1-2-2v-21A2,2,0,0,1-11.35,9.5Z',
              handleStyle: {
                color: '#6580b8',
                borderColor: '#6580b8'
              },
              maxValueSpan: 3,
              minValueSpan: 3,
              brushSelect: false
            },
            {
              type: 'inside',
              show: false,
              yAxisIndex: [0, 1],
              height: 8
            }
          ]
        }
      } else {
        option = {
          title: {
            text: '暂无数据',
            x: 'center',
            y: 'center',
            textStyle: {
              fontSize: 14,
              fontWeight: 'normal',
              color: '#999'
            }
          }
        }
      }
      chart.clear()
      chart.setOption(option)
      chart.off('click')
      // 点击事件
      chart.on('click', (params) => {
        this.openDoorRecord('ratioTag')
      })
    },
    // 柱状图
    commonChart (list, id, type) {
      let chart = echarts.init(document.getElementById(id))
      let option = {}
      if (list && list.length) {
        option = {
          legend: {},
          grid: {
            top: '0%',
            left: '5%',
            right: '5%',
            bottom: '0%',
            containLabel: true
          },
          xAxis: {
            type: 'value',
            show: false
          },
          yAxis: [
            {
              type: 'category',
              data: list.map(v => v.name),
              axisTick: {
                show: false // 不显示坐标轴刻度线
              },
              axisLine: {
                show: false // 不显示坐标轴线
              },
              splitLine: {
                // 网格线
                show: false
              },
              axisLabel: { textStyle: { color: '#fff', fontSize: '14' } }
            },
            {
              type: 'category', // 坐标轴类型
              // inverse: true, // 是否是反向坐标轴
              axisTick: {
                show: false // 不显示坐标轴刻度线
              },
              axisLine: {
                show: false // 不显示坐标轴线
              },
              splitLine: {
                // 网格线
                show: false
              },
              axisLabel: {
                textStyle: {
                  color: '#FFFFFFCC',
                  fontSize: '14'
                },
                formatter: (value) => {
                  return value + '次'
                }
              },
              data: list.map(v => v.value)
            }
          ],
          series: [
            {
              type: 'bar',
              stack: 'total',
              label: {
                show: false
              },
              // 鼠标移入改变颜色
              emphasis: {
                focus: 'series',
                color: '#FFCA64FF'
              },
              data: list,
              barWidth: 8,
              itemStyle: {
                color: '#8BDDF5'
              },
              showBackground: true, // 显示背景色
              backgroundStyle: {
                color: '#384156'
              }
            }
          ],
          dataZoom: [
            {
              yAxisIndex: [0, 1],
              orient: 'vertical',
              show: true,
              type: 'slider',
              startValue: 100,
              // end: 100,
              width: 8,
              left: '99%',
              borderColor: 'rgba(43,48,67,.1)',
              fillerColor: '#6580b8',
              zoomLock: true,
              showDataShadow: false,
              backgroundColor: 'rgba(43,48,67,.1)',
              showDetail: false,
              // realtime: true,
              filterMode: 'filter',
              handleIcon:
                'path://M-9.35,34.56V42m0-40V9.5m-2,0h4a2,2,0,0,1,2,2v21a2,2,0,0,1-2,2h-4a2,2,0,0,1-2-2v-21A2,2,0,0,1-11.35,9.5Z',
              handleStyle: {
                color: '#6580b8',
                borderColor: '#6580b8'
              },
              maxValueSpan: 4,
              minValueSpan: 4,
              brushSelect: false
            },
            {
              type: 'inside', // 内置型数据区域缩放组件
              yAxisIndex: [0, 1], // 对应的 Y 轴索引
              start: 0, // 数据窗口范围的起始百分比
              end: 50 // 数据窗口范围的结束百分比
            }
          ]
        }
      } else {
        option = {
          title: {
            text: '暂无数据',
            x: 'center',
            y: 'center',
            textStyle: {
              fontSize: 14,
              fontWeight: 'normal',
              color: '#999'
            }
          }
        }
      }
      chart.clear()
      chart.setOption(option)
      chart.off('click')
      // 点击事件
      chart.on('click', (params) => {
        this.openDoorRecord(type)
      })
    },
    // 时间类型切换
    dateTypeCommand(val) {
      const date = {
        day: [moment().format('YYYY-MM-DD'), moment().format('YYYY-MM-DD')],
        week: [moment().startOf('isoWeek').format('YYYY-MM-DD'), moment().endOf('isoWeek').format('YYYY-MM-DD')],
        month: [moment().startOf('month').format('YYYY-MM-DD'), moment().endOf('month').format('YYYY-MM-DD')],
        year: [moment().startOf('year').format('YYYY-MM-DD'), moment().endOf('year').format('YYYY-MM-DD')],
        customize: [moment().format('YYYY-MM-DD'), moment().format('YYYY-MM-DD')]
      }
      this.currentDate = date[val]
      this.timeTag = 'hour'
      this.ratioTag = 'hour'
      this.sortTag = 2
      this.days = moment(this.currentDate[1]).diff(moment(this.currentDate[0]), 'days') + 1
      this.dateType = val
      if (val != 'customize') {
        this.initData()
      }
    },
    sortTagChange(val) {
      this.sortTag = val
      this.getOpenCountGroupSurvey()
    },
    ratioTagChange(val) {
      this.ratioTag = val
      this.getOpenCountTimeRatio()
    },
    timeTagChange(val) {
      this.timeTag = val
      this.getOpenCountTimePeriod()
    },
    setWPFBgShow() {
      try {
        window.chrome.webview.hostObjects.sync.bridge.DateMask(true)
      } catch (error) {

      }
    },
    setWPFBgHide() {
      try {
        window.chrome.webview.hostObjects.sync.bridge.DateMask(false)
      } catch (error) {

      }
    }
  }
}

</script>

<style lang="scss" scoped>
.openDoorAnalysis {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;

  .search-date {
    display: flex;
    background: rgba(133, 145, 206, 0.15);
    padding: 0px 10px;

    ::v-deep .el-dropdown {
      padding: 7px 6px;

      .el-dropdown-link {
        font-size: 14px;
        font-weight: 500;
        color: #8bddf5;
        line-height: 16px;
        position: relative;
        cursor: pointer;
      }

      .el-dropdown-link::after {
        content: '';
        position: absolute;
        width: 1px;
        height: 12px;
        background: rgba(133, 145, 206, 0.5);
        top: 50%;
        right: -6px;
        transform: translateY(-50%);
      }
    }

    ::v-deep .datePickerInput {
      flex: 1;
      padding: 8px 10px;
      height: 16px;
      box-sizing: content-box;
      background: none;
      border: none;

      .el-input__icon,
      .el-range-separator {
        line-height: 16px;
        color: #b0e3fa;
      }

      .el-range-input {
        background: none;
        color: #a4afc1;
      }
    }
  }

  .contnet-count {
    padding: 10px 8px 24px 8px;
    display: flex;

    .count-item {
      display: flex;
      align-items: flex-end;
      width: 50%;

      .count-item-icon {
        width: 50px;
        height: 56px;
      }

      .count-item-right {
        padding-top: 10px;
      }

      .count-item-name {
        padding-left: 18px;
        font-weight: 400;
        font-size: 14px;
        color: #B0E3FA;
        line-height: 16px;

      }

      .count-item-value {
        cursor: pointer;
        padding-left: 22px;
        font-weight: bold;
        font-size: 20px;
        color: #FFCA64;
        line-height: 31px;
        width: 124px;
        height: 31px;
        background: url('@/assets/images/elevator/openDoorValue.png');
        margin: 6px 0px 15px;
      }
    }
  }

  .contnet-main {
    flex: 1;
    // overflow: auto;
    .title-right {
      display: flex;
      align-items: center;

      .right-svg {
        color: #fff;
        font-size: 24px;
        cursor: pointer;
      }

      .el-dropdown {
        display: flex;
      }
    }

    .el-dropdown-link {
      font-size: 14px;
      font-weight: 300;
      color: #ffffff;
      line-height: 16px;

      .el-icon-arrow-down {
        font-size: 12px;
      }
    }
    #openDoorRatio {
      position: unset !important;
    }
  }
}
</style>
