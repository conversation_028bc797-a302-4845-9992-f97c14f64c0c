<template>
  <div class="main">
    <el-dialog
      v-dialogDrag
      :modal="false"
      :visible.sync="dialogShow"
      custom-class="mainDialog main"
      append-to-body
      :close-on-click-modal="false"
      :before-close="closeDialog"
      class="searchComponentList"
    >
      <template slot="title">
        <span class="dialog-title">{{ dialogData.title }}</span>
      </template>
      <div class="dialog-content">
        <div v-if="dialogData.type === 'ups'" class="search-box">
          <div style="margin-right: 16px" class="upsInput">
            <el-select v-model="upsSearchParams.state" placeholder="请选择状态">
              <el-option v-for="item in stateOptions" :key="item.value" :label="item.label" :value="item.value"> </el-option>
            </el-select>
          </div>
          <div class="search-btn">
            <el-button @click="resetUpsSearch">重置</el-button>
            <el-button @click="handleUpsSearch">查询</el-button>
          </div>
        </div>
        <div v-if="dialogData.type === 'building'" class="search-box">
          <div style="margin-right: 16px" class="upsInput">
            <el-select v-model="buildingSearchParams.entityTypeId" placeholder="全部" clearable>
              <el-option v-for="item in dictOptions" :key="item.entityTypeId" :label="item.entityTypeName" :value="item.entityTypeId"> </el-option>
            </el-select>
          </div>
          <div style="margin-right: 16px" class="upsInput">
            <el-select v-model="buildingSearchParams.state" placeholder="请选择状态">
              <el-option v-for="item in stateOptions" :key="item.value" :label="item.label" :value="item.value"> </el-option>
            </el-select>
          </div>
          <div class="search-btn">
            <el-button @click="resetBuildingSearch">重置</el-button>
            <el-button @click="handleBuildingSearch">查询</el-button>
          </div>
        </div>
        <div v-if="dialogData.type === 'distribution'" class="search-box">
          <div style="margin-right: 16px" class="upsInput">
            <el-select v-model="distributionSearchParams.entityTypeId" placeholder="全部">
              <el-option v-for="item in dictOptions" :key="item.entityTypeId" :label="item.entityTypeName" :value="item.entityTypeId"> </el-option>
            </el-select>
          </div>
          <div style="margin-right: 16px" class="upsInput">
            <el-select v-model="distributionSearchParams.state" placeholder="请选择状态">
              <el-option v-for="item in stateOptions" :key="item.value" :label="item.label" :value="item.value"> </el-option>
            </el-select>
          </div>
          <div class="search-btn">
            <el-button @click="resetDistributionSearch">重置</el-button>
            <el-button @click="handleDistributionSearch">查询</el-button>
          </div>
        </div>
        <el-table
          v-loading="tableLoading"
          :data="tableData"
          :height="dialogData.height"
          :cell-style="$tools.setCell(3)"
          :header-cell-style="$tools.setHeaderCell(3)"
          style="width: 100%"
          element-loading-background="rgba(0, 0, 0, 0.2)"
          @row-click="viewRealisticScenery"
        >
          <el-table-column type="index" width="120" label="序号" align="center"> </el-table-column>
          <el-table-column v-for="(column, index) in tableColumn" :key="index" :prop="column.prop" :label="column.label" :min-width="column.minWidth" show-overflow-tooltip>
            <template slot-scope="scope">
              <table-render v-if="column.render" :sc="scope" :row="scope.row" :render="column.render"></table-render>
              <div v-else-if="!column.formatter">
                {{ scope.row[column.prop] }}
              </div>
              <div v-else>
                {{ column.formatter(scope.row) }}
              </div>
            </template>
          </el-table-column>
        </el-table>
        <el-pagination
          :current-page="currentPage"
          :page-sizes="[15, 20, 30, 40]"
          :page-size="pageSize"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
          class="pagination"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        ></el-pagination>
      </div>
    </el-dialog>
  </div>
</template>
<script lang="jsx">
import { getAssetListData } from '@/utils/centerScreenApi'
import tableRender from '../../../../spaceManage/components/tableRender.vue'
import { getSurveyList, getMonitorUpsDetail, getEntityType } from '@/utils/spaceManage'
import { monitorTypeList, hasModelCodeFilterProjectName } from '@/assets/common/dict.js'

import icon_5 from '@/assets/images/icon-5.png'
import icon_3 from '@/assets/images/icon-3.png'
import icon_7 from '@/assets/images/icon-7.png'
import icon_6 from '@/assets/images/icon-6.png'
import icon_2 from '@/assets/images/icon-2.png'
export default {
  name: 'searchComponentList',
  components: {
    'table-render': tableRender
  },
  props: {
    dialogShow: {
      type: Boolean,
      default: false
    },
    dialogData: {
      type: Object,
      default: () => {
        return {
          title: '服务工单台账',
          type: 'ioms',
          height: 'calc(100% - 40px)',
          queryInfo: {}
        }
      }
    }
  },
  data() {
    return {
      icon_5,
      icon_3,
      icon_7,
      icon_6,
      icon_2,

      // 列表
      tableData: [],
      tableLoading: false,
      currentPage: 1,
      pageSize: 15,
      total: 0,
      // 列表字段
      tableColumn: [],
      stateOptions: [
        {
          value: 0,
          label: '正常'
        },
        {
          value: 1,
          label: '停止'
        },
        {
          value: 2,
          label: '故障'
        },
        {
          value: 6,
          label: '离线'
        }
      ],
      // upstable数据
      upsTableColumn: [
        {
          prop: 'imsName',
          label: '设备名称'
        },
        {
          prop: 'entityTypeName',
          label: '设备类型'
        },
        {
          prop: 'status',
          label: '状态',
          render: (h, row) => {
            return (
              <div>
                {row.row.status == '0' && (
                  <div class="status-box">
                    <img class="table-icon" src={icon_5} />
                    <span style="color:#61E29D">正常</span>
                  </div>
                )}
                {row.row.status == '1' && (
                  <div class="status-box">
                    <img class="table-icon" src={icon_6} />
                    <span style="color:#86909C">停止</span>
                  </div>
                )}
                {row.row.status == '2' && (
                  <div class="status-box">
                    <img class="table-icon" src={icon_7} />
                    <span style="color:#D25F00">故障</span>
                  </div>
                )}
                {row.row.status == '6' && (
                  <div class="status-box">
                    <img class="table-icon" src={icon_6} />
                    <span style="color:#86909C">离线</span>
                  </div>
                )}
                {row.row.status == '3' && (
                  <div class="status-box">
                    <img class="table-icon" src={icon_2} />
                    <span style="color:#FF2D55">报警</span>
                  </div>
                )}
              </div>
            )
          }
        },
        {
          prop: 'alarmStatus',
          label: '报警状态',
          render: (h, row) => {
            return (
              <div>
                {row.row.alarmStatus == 0 && (
                  <div class="status-box">
                    <span style="color:#61E29D">正常</span>
                  </div>
                )}
                {row.row.alarmStatus == 1 && (
                  <div class="status-box">
                    <span style="color:#FF2D55">报警</span>
                  </div>
                )}
              </div>
            )
          }
        }
      ],
      // 楼宇table数据
      buildingTableColumn: [
        {
          prop: 'surveyName',
          label: '设备名称'
        },
        {
          prop: 'entityTypeName',
          label: '设备类型'
        },
        {
          prop: 'state',
          label: '状态',
          render: (h, row) => {
            return (
              <div>
                {row.row.state == 0 && (
                  <div class="status-box">
                    <img class="table-icon" src={icon_5} />
                    <span style="color:#61E29D">正常</span>
                  </div>
                )}
                {row.row.state == 1 && (
                  <div class="status-box">
                    <img class="table-icon" src={icon_2} />
                    <span style="color:#FF2D55">停止</span>
                  </div>
                )}
                {row.row.state == 2 && (
                  <div class="status-box">
                    <img class="table-icon" src={icon_7} />
                    <span style="color:#D25F00">故障</span>
                  </div>
                )}
                {row.row.state == 6 && (
                  <div class="status-box">
                    <img class="table-icon" src={icon_6} />
                    <span style="color:#86909C">离线</span>
                  </div>
                )}
              </div>
            )
          }
        },
        {
          prop: 'policeState',
          label: '报警状态',
          render: (h, row) => {
            return (
              <div>
                {row.row.policeState == 0 && (
                  <div class="status-box">
                    <span style="color:#61E29D">正常</span>
                  </div>
                )}
                {row.row.policeState == 1 && (
                  <div class="status-box">
                    <span style="color:#FF2D55">报警</span>
                  </div>
                )}
              </div>
            )
          }
        }
      ],
      // 配电table数据
      distributionTableColumn: [
        {
          prop: 'surveyName',
          label: '设备名称'
        },
        {
          prop: 'entityTypeName',
          label: '设备类型'
        },
        {
          prop: 'state',
          label: '状态',
          render: (h, row) => {
            return (
              <div>
                {row.row.state == 0 && (
                  <div class="status-box">
                    <img class="table-icon" src={icon_5} />
                    <span style="color:#61E29D">正常</span>
                  </div>
                )}
                {row.row.state == 1 && (
                  <div class="status-box">
                    <img class="table-icon" src={icon_2} />
                    <span style="color:#FF2D55">停止</span>
                  </div>
                )}
                {row.row.state == 1 && (
                  <div class="status-box">
                    <img class="table-icon" src={icon_2} />
                    <span style="color:#FF2D55">停止</span>
                  </div>
                )}
                {row.row.state == 6 && (
                  <div class="status-box">
                    <img class="table-icon" src={icon_6} />
                    <span style="color:#86909C">离线</span>
                  </div>
                )}
              </div>
            )
          }
        },
        {
          prop: 'policeState',
          label: '报警状态',
          render: (h, row) => {
            return (
              <div>
                {row.row.policeState == 0 && (
                  <div class="status-box">
                    <span style="color:#61E29D">正常</span>
                  </div>
                )}
                {row.row.policeState == 1 && (
                  <div class="status-box">
                    <span style="color:#FF2D55">报警</span>
                  </div>
                )}
              </div>
            )
          }
        }
      ],
      distributionSearchParams: {
        entityTypeId: '', // id
        state: '' // 在线状态
      },
      upsSearchParams: {
        state: '' // 状态
      },
      buildingSearchParams: {
        entityTypeId: '', // id
        state: '' // 状态
      },
      dictList: [], // 字典list
      dictOptions: [] // 字典值显示
    }
  },
  watch: {},
  created() {},
  mounted() {
    this.getTableData('init')
  },
  methods: {
    // 查看实景
    viewRealisticScenery(row) {
      const viewMonitorData = monitorTypeList.find(e => e.projectCode == row.projectCode)
      // 照明不做跳转
      const noJupmFilterName = ['照明监测']
      if (noJupmFilterName.some(e => e == viewMonitorData?.projectName)) {
        return
      }
      // 受filterName包含的为使用modelcode跳转（关联模型），不是的为使用assetsid跳转（关联表计）
      const filterName = hasModelCodeFilterProjectName
      const filterMonitorFlag = filterName.some(e => e == viewMonitorData?.projectName)
      console.log('查看实景', row)
      // 如果关联了设备即跳转设备详情页
      if (viewMonitorData.wpfKey) {
        if (filterMonitorFlag) {
          if (row.modelCode) {
            const params = {
              DeviceCode: row.modelCode,
              menuName: viewMonitorData.wpfKey,
              assetsId: row.assetId,
              projectCode: row?.projectCode,
              spaceCode: row?.regionCode
            }
            console.log(params, 'EquipmentRealView')
            try {
              window.chrome.webview.hostObjects.sync.bridge.EquipmentRealView(JSON.stringify(params))
            } catch (error) {}
          } else {
            this.$message.warning('当前设备暂未录入模型编码!')
          }
        } else {
          if (row.assetId) {
            const params = {
              DeviceCode: row.modelCode,
              menuName: viewMonitorData.wpfKey,
              assetsId: row.assetId,
              projectCode: row?.projectCode,
              spaceCode: row?.regionCode
            }
            console.log(params, 'EquipmentRealView')
            try {
              window.chrome.webview.hostObjects.sync.bridge.EquipmentRealView(JSON.stringify(params))
            } catch (error) {}
          } else {
            this.$message.warning('当前设备暂未关联资产!')
          }
        }
      } else {
        // this.$message.warning('当前设备暂不支持关联!')
      }
    },
    // 过滤实体
    getEntityTypeData(type) {
      // const filterName = ['配电监测', '电表监测']
      // const filterMonitorList = monitorTypeList.filter(e => filterName.includes(e.projectName))
      // const params = {
      //   projectCode: Array.from(filterMonitorList, ({ projectCode }) => projectCode).toString()
      // }
      const params = {
        projectCode: this.dialogData.queryInfo.projectCode,
        entityTypeId: this.dialogData.queryInfo.entityTypeId || ''
      }
      getEntityType(params).then((res) => {
        if (res.data.code === '200' && res.data.data) {
          this.dictOptions = res.data.data
          if (type == 'distribution') {
            this.getDistributionTableData()
          }
          // if (this.dialogData.queryInfo.entityTypeId) {
          //   let arr = this.dialogData.queryInfo.entityTypeId.split(',')
          //   let arrNew = []
          //   this.dictList.forEach((el) => {
          //     arr.forEach((t) => {
          //       if (el.value === t) {
          //         arrNew.push({
          //           label: el.name,
          //           value: el.value
          //         })
          //       }
          //     })
          //   })
          //   this.dictOptions = JSON.parse(JSON.stringify(arrNew))
          // } else {
          //   this.dictOptions = []
          // }
        } else {
          this.dictOptions = []
        }
      })
    },
    getTableData(type) {
      this.tableLoading = true
      switch (this.dialogData.type) {
        case 'ups':
          if (type === 'init') {
            this.tableColumn = this.upsTableColumn
            this.tableLoading = false
          }
          this.getUpsTableData()
          break
        case 'building':
          if (type === 'init') {
            this.tableColumn = this.buildingTableColumn
            this.tableLoading = false
          }
          this.getEntityTypeData()
          this.getBuildingTableData()
          break
        case 'distribution':
          if (type === 'init') {
            this.tableColumn = this.distributionTableColumn
            this.tableLoading = false
          }
          this.distributionSearchParams.entityTypeId = this.dialogData.queryInfo.entityTypeId || ''
          this.getEntityTypeData('distribution')
          break
      }
    },
    // 获取ups列表
    getUpsTableData() {
      const params = {
        pageSize: this.pageSize,
        page: this.currentPage,
        status: this.upsSearchParams.state,
        projectCode: '01f4bd80e5c060809aae72c7470e8be30'
      }
      getMonitorUpsDetail(params).then((res) => {
        if (res.data.code === '200') {
          this.tableData = res.data.data.list
          this.total = res.data.data.totalCount
          this.tableLoading = false
        }
      })
    },
    // 获取楼宇列表
    getBuildingTableData() {
      const params = {
        projectCode: this.dialogData.queryInfo.projectCode,
        entityTypeId: this.buildingSearchParams.entityTypeId ? this.buildingSearchParams.entityTypeId : this.dialogData.queryInfo.entityTypeId ? this.dialogData.queryInfo.entityTypeId : '',
        state: this.buildingSearchParams.state,
        pageSize: this.pageSize,
        page: this.currentPage
      }
      getSurveyList(params).then((res) => {
        if (res.data.code === '200') {
          console.log(res.data.data)
          this.tableData = res.data.data.list
          this.total = res.data.data.totalCount
          this.tableLoading = false
        }
      })
    },
    // 获取配电列表
    getDistributionTableData() {
      const params = {
        projectCode: this.dialogData.queryInfo.projectCode,
        // projectCode: this.dictOptions.filter(v => v.entityTypeId == this.distributionSearchParams.entityTypeId).projectCode,
        entityTypeId: this.distributionSearchParams.entityTypeId ? this.distributionSearchParams.entityTypeId : this.dialogData.queryInfo.entityTypeId ? this.dialogData.queryInfo.entityTypeId : '',
        state: this.distributionSearchParams.state,
        pageSize: this.pageSize,
        page: this.currentPage
      }
      getSurveyList(params).then((res) => {
        if (res.data.code === '200') {
          this.tableData = res.data.data.list
          this.total = res.data.data.totalCount
          this.tableLoading = false
        }
      })
    },
    handleSizeChange(val) {
      this.pageSize = val
      this.getTableData('page')
    },
    handleCurrentChange(val) {
      this.currentPage = val
      this.getTableData('page')
    },

    // 取消按钮
    closeDialog() {
      this.$emit('configCloseDialog')
    },
    // ups列表重置
    resetUpsSearch() {
      this.pageSize = 15
      this.currentPage = 1
      this.upsSearchParams = {
        state: ''
      }
      this.getUpsTableData()
    },
    // ups列表查询
    handleUpsSearch() {
      this.pageSize = 15
      this.currentPage = 1
      this.getUpsTableData()
    },
    // 楼宇重置
    resetBuildingSearch() {
      this.pageSize = 15
      this.currentPage = 1
      this.buildingSearchParams = {
        entityTypeId: '',
        state: ''
      }
      this.getBuildingTableData()
    },
    // 楼宇查询
    handleBuildingSearch() {
      this.pageSize = 15
      this.currentPage = 1
      this.getBuildingTableData()
    },
    // 配电重置
    resetDistributionSearch() {
      this.pageSize = 15
      this.currentPage = 1
      this.distributionSearchParams = {
        entityTypeId: this.dialogData.queryInfo.entityTypeId, // id
        state: ''
      }
      this.getDistributionTableData()
    },
    // 配电查询
    handleDistributionSearch() {
      this.pageSize = 15
      this.currentPage = 1
      this.getDistributionTableData()
    }
  }
}
</script>

<style lang="scss" type="text/css" scoped>
.searchComponentList {
  pointer-events: none;
}

::v-deep .mainDialog {
  width: 60%;
  height: 59vh;
  margin-top: 19vh !important;
  // border: 1px solid #5996f9;
  background-color: transparent !important;
  pointer-events: auto;
  background-image: url('@/assets/images/table-bg-small.png');
  background-repeat: no-repeat;
  background-size: 100% 100%;
  box-shadow: none;
  .dialog-title::before {
    display: none;
  }
  .el-dialog__header {
    background-color: transparent;
    padding-top: 15px;
    height: 50px !important;
  }
  .el-dialog__body {
    padding: 0;
    height: calc(100% - 60px);
    max-height: fit-content;
    overflow-y: hidden;
    background-color: transparent;
    padding: 0px 80px;
    .dialog-content {
      width: 100%;
      height: 100%;
      background-color: transparent;

      .el-table {
        background-color: transparent;
        border: 1px solid #202d4c;
        .el-table__header-wrapper {
          background-color: transparent !important;
        }
        .el-table__body-wrapper {
          background-color: transparent !important;
        }
        tr {
          background-color: transparent !important;
        }
        td {
          background-color: transparent !important;
        }
      }
    }
  }
}

::v-deep .search-box {
  display: flex;
  margin-bottom: 12px;
  .upsInput {
    .el-input {
      width: 140px;
    }
  }
  .el-button {
    background-image: url('@/assets/images/btn.png');
    background-repeat: no-repeat;
    background-size: 100% 100%;
    height: 35px;
  }
  .search-btn {
    margin-left: 24px;
  }
  .el-select {
    .el-input .el-input__inner {
      border-color: rgba(133, 145, 206, 0.5);
      width: 150px;
      height: 35px;
    }
  }
  .el-cascader {
    .el-input__inner {
      height: 35px !important;
    }
  }
  .el-date-editor {
    width: 250px;
    height: 35px;
    background-color: #14233e;
    border-color: rgba(133, 145, 206, 0.5);
    .el-input__icon {
      transform: translateY(-2px);
    }
    .el-range-input {
      background-color: #14233e;
      color: rgba(255, 255, 255, 0.8);
    }
    .el-range-separator {
      color: rgba(255, 255, 255, 0.8);
      transform: translateY(-2px);
    }
  }
  .el-input .el-input__inner {
    border-color: rgba(133, 145, 206, 0.5);
    border-radius: 4px;
    color: rgba(255, 255, 255, 0.8);
  }
}
.search-box .el-input {
  width: 120px;
  height: 35px;
}
.table-icon {
  width: 16px;
  margin-right: 3px;
}
.status-box {
  display: flex;
  align-items: center;
}
</style>
