<template>
  <div class="incomeDetail">
    <div id="incomeDetail_chart"></div>
  </div>
</template>

<script>
import * as echarts from 'echarts'
import { mockIncomeData } from './mockData.js'
export default {
  name: 'incomeDetail',
  data() {
    return {
      incomeData: {
        legendList: ['检查收入', '药品收入', '耗材收入', '治疗收入'],
        nameList: ['2019', '2020', '2021', '2022', '2023'],
        checkIncome: [0, 0, 0, 0, 0],
        drugIncome: [0, 0, 0, 0, 0],
        consumableIncome: [0, 0, 0, 0, 0],
        treatmentIncome: [0, 0, 0, 0, 0]
      }
    }
  },
  computed: {},
  created() {},
  mounted() {
    if (__PATH.VUE_APP_HOSPITAL_NODE === 'outernet') {
      this.incomeData = mockIncomeData
    }
    this.$nextTick(() => {
      this.setChart()
    })
  },
  methods: {
    setChart() {
      const getchart = echarts.init(document.getElementById('incomeDetail_chart'))
      const option = {
        title: {
          text: '收入明细(万元)',
          textStyle: {
            fontSize: 16,
            fontWeight: 'normal',
            color: '#fff'
          },
          x: 20,
          top: 20
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow'
          }
        },
        legend: {
          data: this.incomeData.legendList,
          align: 'left',
          top: 30,
          textStyle: {
            color: '#fff'
          },
          itemWidth: 10,
          itemHeight: 10,
          itemGap: 35
        },
        grid: {
          top: '20%',
          left: '3%',
          right: '4%',
          bottom: '5%',
          containLabel: true
        },
        xAxis: [
          {
            type: 'category',
            data: this.incomeData.nameList,
            axisLine: {
              show: true,
              lineStyle: {
                color: '#8591CE',
                width: 1,
                type: 'solid'
              }
            },
            axisTick: {
              show: false
            },
            axisLabel: {
              show: true,
              textStyle: {
                color: '#E6F7FF'
              }
            }
          }
        ],
        yAxis: [
          {
            type: 'value',
            axisLabel: {
              formatter: '{value} '
            },
            axisTick: {
              show: false
            },
            axisLine: {
              show: false,
              lineStyle: {
                color: '#E6F7FF'
              }
            },
            splitLine: {
              lineStyle: {
                color: 'rgba(230, 247, 255, 0.20)',
                type: 'dashed'
              }
            }
          }
        ],
        series: [
          {
            name: '检查收入',
            type: 'bar',
            data: this.incomeData.checkIncome,
            barWidth: 10, // 柱子宽度
            barGap: 0.5, // 柱子之间间距
            itemStyle: {
              normal: {
                color: '#3CC1FF',
                opacity: 1
              }
            }
          },
          {
            name: '药品收入',
            type: 'bar',
            data: this.incomeData.drugIncome,
            barWidth: 10,
            barGap: 0.5,
            itemStyle: {
              normal: {
                color: '#0A84FF',
                opacity: 1
              }
            }
          },
          {
            name: '耗材收入',
            type: 'bar',
            data: this.incomeData.consumableIncome,
            barWidth: 10,
            barGap: 0.5,
            itemStyle: {
              normal: {
                color: '#61E29D',
                opacity: 1
              }
            }
          },
          {
            name: '治疗收入',
            type: 'bar',
            data: this.incomeData.treatmentIncome,
            barWidth: 10,
            barGap: 0.5,
            itemStyle: {
              normal: {
                color: '#B0E3FA',
                opacity: 1
              }
            }
          }
        ]
      }
      getchart.clear()
      getchart.setOption(option)
      // 随着屏幕大小调节图表
      window.addEventListener('resize', () => {
        getchart.resize()
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.incomeDetail {
  width: 100%;
  height: 100%;
  background: rgba(53, 98, 219, 0.06);
  #incomeDetail_chart {
    width: 100%;
    height: 100%;
  }
}
</style>
