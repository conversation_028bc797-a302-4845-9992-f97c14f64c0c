<template>
  <div class="REStatis">
    <div id="REStatis_chart"></div>
  </div>
</template>

<script>
import * as echarts from 'echarts'
import { mockREStatisData } from './mockData.js'
export default {
  name: 'REStatis',
  data() {
    return {
      REStatisData: {
        nameList: ['2019', '2020', '2021', '2022', '2023'],
        income: [0, 0, 0, 0, 0],
        expenditure: [0, 0, 0, 0, 0],
        profit: [0, 0, 0, 0, 0]
      }
    }
  },
  computed: {},
  created() {},
  mounted() {
    if (__PATH.VUE_APP_HOSPITAL_NODE === 'outernet') {
      this.REStatisData = mockREStatisData
    }
    this.$nextTick(() => {
      setTimeout(() => {
        this.setChart()
      })
    })
  },
  methods: {
    setChart() {
      const getchart = echarts.init(document.getElementById('REStatis_chart'))
      const option = {
        title: {
          text: '收支趋势图(万元)',
          textStyle: {
            fontSize: 16,
            fontWeight: 'normal',
            color: '#fff'
          },
          x: 20,
          top: 20
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow'
          }
        },
        legend: {
          data: ['收入', '支出', '利润'],
          align: 'left',
          top: 30,
          textStyle: {
            color: '#fff'
          },
          itemWidth: 10,
          itemHeight: 10,
          itemGap: 35
        },
        grid: {
          top: '20%',
          left: '3%',
          right: '4%',
          bottom: '5%',
          containLabel: true
        },
        xAxis: [
          {
            type: 'category',
            data: this.REStatisData.nameList,
            axisLine: {
              show: true,
              lineStyle: {
                color: '#8591CE',
                width: 1,
                type: 'solid'
              }
            },
            axisTick: {
              show: false
            },
            axisLabel: {
              show: true,
              textStyle: {
                color: '#E6F7FF'
              }
            }
          }
        ],
        yAxis: [
          {
            type: 'value',
            axisLabel: {
              formatter: '{value} '
            },
            axisTick: {
              show: false
            },
            axisLine: {
              show: false,
              lineStyle: {
                color: '#E6F7FF'
              }
            },
            splitLine: {
              lineStyle: {
                color: 'rgba(230, 247, 255, 0.20)',
                type: 'dashed'
              }
            }
          }
        ],
        series: [
          {
            name: '收入',
            type: 'bar',
            data: this.REStatisData.income,
            barWidth: 10, // 柱子宽度
            barGap: 0.5, // 柱子之间间距
            itemStyle: {
              normal: {
                color: '#8BDDF5',
                opacity: 1
              }
            }
          },
          {
            name: '支出',
            type: 'bar',
            data: this.REStatisData.expenditure,
            barWidth: 10,
            barGap: 0.5,
            itemStyle: {
              normal: {
                color: '#3CC1FF',
                opacity: 1
              }
            }
          },
          {
            name: '利润',
            type: 'bar',
            data: this.REStatisData.profit,
            barWidth: 10,
            barGap: 0.5,
            itemStyle: {
              normal: {
                color: '#0A84FF',
                opacity: 1
              }
            }
          }
        ]
      }
      getchart.clear()
      getchart.setOption(option)
      // 随着屏幕大小调节图表
      window.addEventListener('resize', () => {
        getchart.resize()
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.REStatis {
  width: 100%;
  height: 100%;
  background: rgba(53, 98, 219, 0.06);
  #REStatis_chart {
    width: 100%;
    height: 100%;
  }
}
</style>
