<template>
  <div class="deviceTravelRecord">
    <div class="date-type">
      <p v-for="item in typeList" :key="item.value" class="type-item" :class="{'active-type': activeType == item.value}" @click="activeTabEvent(item.value)">{{ item.name }}</p>
    </div>
    <div class="search-box">
      <el-input v-if="activeType == 1" v-model="queryParam.searchKeyword" placeholder="名称/编码" clearable @input="nameSearchInput()"></el-input>
      <el-input v-else v-model="queryParam.searchKeyword" placeholder="名称/编码" clearable @input="nameSearchInput()"></el-input>
      <el-select v-if="activeType == 2" v-model="queryParam.registType" filterable clearable placeholder="全部挂号方式" @change="tableList">
        <el-option label="现场" value="0"></el-option>
        <el-option label="网约" value="1"></el-option>
      </el-select>
      <el-date-picker v-model="queryParam.visitDate" type="date" placeholder="选择日期" popper-class="date-style" :clearable="false" value-format="yyyy-MM-dd" @change="dateChange"/>
      <el-time-select
        v-show="activeType != 1"
        v-model="queryParam.startTime"
        popper-class="date-style"
        placeholder="开始时间"
        :clearable="false"
        :picker-options="{
          start: '00:00',
          step: '01:00',
          end: '24:00'
        }"
        @change="dateChange"
      />
      <el-time-select
        v-show="activeType != 1"
        v-model="queryParam.endTime"
        popper-class="date-style"
        placeholder="结束时间"
        :clearable="false"
        :picker-options="{
          start: '00:00',
          step: '01:00',
          end: '24:00',
          minTime: queryParam.startTime
        }"
        @change="dateChange"
      />
    </div>
    <div v-if="activeType == 2" class="count-list">
      <div v-for="(item, index) in countList" :key="index" class="count-list-item" style="width: calc(100% / 3 - 10px)">
        <p class="item-name">{{ item.name }}</p>
        <p class="item-value" :style="{color: item.color}">{{ usageData[item.key] | formatSeconds }}</p>
        <p v-if="usageData.sign == 1" class="item-tip">以工作时间统计</p>
      </div>
    </div>
    <div v-show="activeType == 2" class="status-box">
      <div class="status-head">
        <div class="status-head-left">
          <img src="@/assets/images/common/title_icon.png">
          <p>椅位使用状态分布</p>
        </div>
        <div v-if="usageData.sign == 1" class="status-head-right">
          {{ usageData.cfgName || '' }} 中午时间:{{ usageData.morningHoursStart || '' }}-{{ usageData.morningHoursEnd || '' }} 下午时间:{{ usageData.afternoonHoursStart || '' }}-{{ usageData.afternoonHoursEnd || '' }}
        </div>
      </div>
      <div class="status-content">
        <div v-show="usageData.sign == 1" id="morning_chart" class="morning_chart"></div>
        <div v-show="usageData.sign == 1" id="afternoon_chart" class="afternoon_chart"></div>
        <div v-show="usageData.sign == 0" id="allDay_chart" class="allDay_chart"></div>
      </div>
    </div>
    <div style="flex: 1; overflow: hidden;">
      <el-table
        v-loading="tableLoading"
        :data="tableData"
        height="calc(100%)"
        :cell-style="$tools.setCell(3)"
        :header-cell-style="$tools.setHeaderCell(3)"
        style="width: 100%"
        element-loading-background="rgba(0, 0, 0, 0.2)"
      >
        <el-table-column v-for="(item, i) in getColumn" :key="i" :prop="item.prop" :label="item.name" :width="item.width" show-overflow-tooltip>
          <template slot-scope="scope">
            <span v-if="item.prop == 'visitBeginTime'">
              {{ (scope.row['visitDate'] || '') }}  {{ scope.row['visitBeginTime'] }} - {{ scope.row['visitEndTime'] }}
            </span>
            <span v-else-if="item.prop == 'registType'">
              {{ scope.row['registType'] == 0 ? '现场' : '网约' }}
            </span>
            <span v-else-if="item.prop == 'visitStatus'">
              {{ scope.row['visitStatus'] == 0 ? '复诊' : '初诊' }}
            </span>
            <div v-else>
              {{ scope.row[scope.column.property] }}
            </div>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <el-pagination
      :current-page="currentPage"
      :page-sizes="[15, 20, 30, 40]"
      :page-size="pageSize"
      layout="total, sizes, prev, pager, next, jumper"
      :total="total"
      class="pagination"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
    ></el-pagination>
  </div>
</template>

<script>
import { GetDoctorVisitRecord, GetPatientVisitRecord, GetChairUseStat } from '@/utils/spaceManage'
import utils from '@/assets/common/utils'
import moment from 'moment'
moment.locale('zh-cn')
import * as echarts from 'echarts'
export default {
  name: 'deviceTravelRecord',
  filters: {
    formatSeconds(seconds) {
      if (!seconds) return '-'
      const hours = Math.floor(seconds / 3600)
      const minutes = Math.floor((seconds % 3600) / 60)
      return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}`
    }
  },
  props: {
    deviceId: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      activeType: 1,
      typeList: [
        { name: '出诊记录', value: 1 },
        { name: '就诊记录', value: 2 }
      ],
      tableLoading: false,
      tableData: [],
      queryParam: {
        searchKeyword: '',
        visitDate: moment().format('YYYY-MM-DD'),
        startTime: '00:00',
        endTime: '24:00',
        registType: '' // 挂号方式
      },
      currentPage: 1,
      pageSize: 15,
      total: 0,
      countList: [
        { name: '空闲时长', key: 'idle', color: '#61E29D' },
        { name: '正常使用时长', key: 'inUse', color: '' },
        { name: '异常占用时长', key: 'abnormal', color: '#FF2D55' }
      ],
      usageData: {
        idle: 0,
        inUse: 0,
        abnormal: 0,
        sign: 0
      }
    }
  },
  computed: {
    getColumn() {
      if (this.activeType == 1) {
        return [
          { name: '医生名称', prop: 'doctorName' },
          { name: '岗位', prop: 'postName' },
          { name: '所在科室', prop: 'departmentName' },
          { name: '出诊椅位', prop: 'chairName' },
          { name: '出诊日期', prop: 'visitBeginTime', width: 240 }
        ]
      } else {
        return [
          { name: '病历编码', prop: 'caseCode' },
          { name: '患者名称', prop: 'patientName' },
          { name: '挂号方式', prop: 'registType' },
          { name: '初复诊', prop: 'visitStatus' },
          { name: '主治医生', prop: 'doctorName' },
          { name: '就诊椅位', prop: 'chairName' },
          { name: '就诊时间', prop: 'visitBeginTime', width: 340 }
        ]
      }
    }
  },
  created() {
    this.tableList()
  },
  methods: {
    dateChange(val) {
      if (this.activeType != 1) {
        this.getChairUseStat()
      }
      this.tableList()
    },
    activeTabEvent(val) {
      Object.assign(this.$data.queryParam, this.$options.data().queryParam)
      this.activeType = val
      if (val != 1) {
        this.getChairUseStat()
      }
      this.tableList()
    },
    // 获取椅位使用统计
    getChairUseStat() {
      let {visitDate, startTime, endTime} = this.queryParam
      let params = {
        monitorDeviceId: this.deviceId,
        dateType: 'day',
        startTime: `${visitDate} ${startTime}:00`,
        endTime: `${visitDate} ${endTime}:00`
      }
      GetChairUseStat(params).then(res => {
        if (res.data.code == 200) {
          this.usageData = res.data.data
          this.$nextTick(() => {
            if (this.usageData.sign == 1) {
              this.initChart('morning_chart', res.data.data.morning || [])
              this.initChart('afternoon_chart', res.data.data.afternoon || [])
            } else {
              this.initChart('allDay_chart', res.data.data.all || [])
            }
          })
        }
      })
    },
    // 初始化图表
    initChart(id, rawData) {
      let getchart = echarts.init(document.getElementById(id))
      let statusColorMap = {
        0: '#61E29D',
        1: '#D4DEEC',
        2: '#FF2D55'
      }
      let statusNameMap = {
        0: '空闲',
        1: '正常使用',
        2: '异常占用'
      }

      // 转换为 ECharts 使用的数据格式
      let data = []
      if (rawData) {
        for (let hour in rawData) {
          rawData[hour].forEach(item => {
            data.push({
              value: [
                new Date(item.beginTime).getTime(),
                new Date(item.endTime).getTime(),
                item.status
              ]
            })
          })
        }
      }

      // 获取所有时间范围
      let allTimes = data.flatMap(d => [d.value[0], d.value[1]])
      let minTime = Math.min(...allTimes)
      let maxTime = Math.max(...allTimes)

      let option = {}
      if (data.length) {
        option = {
          tooltip: {
            trigger: 'item',
            backgroundColor: '#0B1B3A',
            textStyle: {
              color: '#fff'
            },
            formatter: function (params) {
              const d = params.data.value
              return `
              状态: ${statusNameMap[d[2]]}<br>
              开始: ${new Date(d[0]).toLocaleTimeString()}<br>
              结束: ${new Date(d[1]).toLocaleTimeString()}
            `
            }
          },
          grid: {
            top: '50%',
            right: '2%',
            left: '2%'
          },
          xAxis: {
            type: 'time',
            min: minTime,
            max: maxTime,
            splitLine: { show: false },
            axisLine: {
              lineStyle: { color: '#ffffff33' }
            },
            axisLabel: {
              // padding: [10, 0, 0, 0],
              color: '#ccc'
              // formatter: val => new Date(val).getHours().toString().padStart(2, '0') + ':00'
            }
          },
          yAxis: {
            type: 'value',
            show: false
          },
          // 支持鼠标缩放和平移
          dataZoom: [
            {
              type: 'slider',
              show: false
            },
            {
              type: 'inside',
              xAxisIndex: 0,
              filterMode: 'none',
              start: 0,
              end: 100
            }
          ],
          series: [{
            type: 'custom',
            renderItem: function (params, api) {
              const start = api.value(0)
              const end = api.value(1)
              const status = api.value(2)
              const color = statusColorMap[status] || '#999'

              const coordStart = api.coord([start, 0])
              const coordSize = api.size([end - start, 10])

              return {
                type: 'rect',
                shape: {
                  x: coordStart[0],
                  y: 30,
                  width: coordSize[0],
                  height: 10
                },
                style: {
                  fill: color
                }
              }
            },
            data: data
          }]
        }
      } else {
        option = {
          title: {
            text: '暂无数据',
            x: 'center',
            y: 'center',
            textStyle: {
              fontSize: 14,
              fontWeight: 'normal',
              color: '#999'
            }
          }
        }
      }
      getchart.clear()
      getchart.setOption(option)
      // 随着屏幕大小调节图表
      window.addEventListener('resize', () => {
        getchart.resize()
      })
    },
    nameSearchInput: utils.throttle(function () {
      this.tableList()
    }, 1000),
    // 出诊记录 / 就诊记录
    tableList() {
      let {searchKeyword, visitDate, registType, startTime, endTime} = this.queryParam
      let params = this.activeType == 1 ? {
        chairCode: this.deviceId,
        page: this.currentPage,
        pageSize: this.pageSize,
        searchKeyword,
        visitDate
      } : {
        chairCode: this.deviceId,
        page: this.currentPage,
        pageSize: this.pageSize,
        searchKeyword,
        beginTime: `${visitDate} ${startTime}:00`,
        endTime: `${visitDate} ${endTime == '24:00' ? '23:59:59' : endTime + ':00'}`,
        registType
      }
      this.tableLoading = true
      this.tableData = []
      let dataFun = this.activeType == 1 ? GetDoctorVisitRecord : GetPatientVisitRecord
      dataFun(params).then(res => {
        if (res.data.code == 200) {
          this.tableData = res.data.data.records
          this.total = res.data.data.total
        }
      }).finally(() => {
        this.tableLoading = false
      })
    },
    handleSizeChange(val) {
      this.pageSize = val
      this.tableList()
    },
    handleCurrentChange(val) {
      this.currentPage = val
      this.tableList()
    }
  }
}

</script>

<style lang="scss" scoped>
.deviceTravelRecord {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  .date-type {
    padding-top: 8px;
    margin-left: 15px;
    display: flex;
    justify-content: center;
    .type-item {
      cursor: pointer;
      padding: 6px 12px;
      font-weight: 400;
      font-size: 14px;
      color: #B0E3FA;
      margin-right: 8px;
      border: 1px solid transparent;
      background: linear-gradient( 90deg, rgba(10,132,255,0) 0%, rgba(10,132,255,0) 100%), rgba(255,255,255,0.1);
    }
    .active-type {
      color: #8BDDF5;
      background: linear-gradient( 90deg, rgba(10,132,255,0.4) 0%, rgba(10,132,255,0) 100%), rgba(255,255,255,0.1);
      border: 1px solid;
      border-image: radial-gradient(circle, rgba(171, 240, 255, 1), rgba(226, 254, 255, 1), rgba(132, 196, 203, 1), rgba(48, 151, 166, 0)) 1 1;
    }
  }
  ::v-deep(.search-box) {
    padding: 10px 0px 10px 0px;
    display: flex;
    align-items: center;
    // .el-select {
    //   margin-right: 10px;
    // }
    .el-input {
      margin-right: 10px;
      width: 250px;
      .el-input__inner {
        height: 35px;
        border: 1px solid rgba(133, 145, 206, 0.5);
        border-radius: 4px;
      }
      .el-input__icon {
        line-height: 35px;
      }
      .el-input__prefix {

      }
    }
  }
  ::v-deep(.el-date-editor) {
    width: 300px;
    height: 35px;
    background-color: transparent;
    border-color: rgba(133, 145, 206, 0.5);
    .el-input__icon {
      height: 35px;
      line-height: 35px;
    }
    .el-range-input {
      background-color: transparent;
      color: rgba(255, 255, 255, 0.8);
    }
    .el-range-separator {
      color: rgba(255, 255, 255, 0.8);
      transform: translateY(-2px);
    }
  }
  .count-list {
    padding-bottom: 10px;
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    justify-content: space-between;
    .count-list-item {
      width: calc(25%);
      // width: calc(25% - 7.5px);
      padding: 10px 0px;
      flex-shrink: 0;
      // margin-right: 10px;
      text-align: center;
      background: #8591ce0d;
      position: relative;
      .item-name{
        color: #fff;
        font-size: 15px;
      }
      .item-value{
        color: #D6EFF1;
        font-size: 24px;
        margin-top: 8px;
        word-break: break-all;
      }
      .item-tip {
        color: #A3A9C0;
        font-size: 12px;
        position: absolute;
        top: 10px;
        right: 10px;
      }
    }
    .count-list-item:nth-child(4n){
      margin-right: 0px;
    }
  }
  .status-box {
    width: 100%;
    padding-bottom: 10px;
    .status-head {
      padding: 7px;
      display: flex;
      align-items: center;
      justify-content: space-between;
      background: linear-gradient( 90deg, rgba(10,132,255,0.4) 0%, rgba(10,132,255,0) 100%);
      .status-head-left {
        display: flex;
        align-items: center;
        p {
          font-weight: 800;
          font-size: 16px;
          color: #FFFFFF;
          line-height: 30px;
          text-shadow: 0px 0px 9px #158EFF;
          margin-left: 12px;
        }
        img {
          width: 20px;
          height: 20px;
        }
      }
      .status-head-right {
        font-size: 14px;
        color: #fff;
      }
    }
    .status-content {
      height: 80px;
      width: 100%;
      display: flex;
      .morning_chart, .afternoon_chart {
        width: 50%;
        height: 100%;
      }
      .allDay_chart {
        width: 100%;
        height: 100%;
      }
    }
  }
}
</style>
