<template>
  <div class="spaceDetails">
    <el-dialog v-dialogDrag :modal="false" :visible.sync="show" custom-class="bg-dialog main" :close-on-click-modal="false" :before-close="closeDialog">
      <template slot="title">
        <span class="bg-dialog-title">空间明细</span>
      </template>
      <div class="spaceDetails-content" style="height: 100%">
        <div class="content-info">
          <div class="info-item">
            <p class="item-label">空间ID：</p>
            <p class="item-value">{{ requestParams.ssmType == 6 ? requestParams.spaceId : requestParams.floorId }}</p>
          </div>
          <div class="info-item">
            <p class="item-label">空间名称：</p>
            <p class="item-value" v-if="requestParams.ssmType == 6">{{ spaceName || '' }}</p>
            <p class="item-value" v-else>{{ floorName || '' }}</p>
          </div>
        </div>
        <el-table
          :data="tableData"
          height="75%"
          :cell-style="$tools.setCell(3)"
          :header-cell-style="$tools.setHeaderCell(3)"
          style="width: 100%"
          element-loading-background="rgba(0, 0, 0, 0.2)"
        >
          <el-table-column align="center" prop="actuatorOutPutName" show-overflow-tooltip label="回路名称"></el-table-column>
          <el-table-column align="center" prop="spaceName" show-overflow-tooltip label="服务空间"></el-table-column>
          <el-table-column align="center" show-overflow-tooltip label="状态">
            <template slot-scope="scope">
              <div class="operation" style="cursor: auto">
                <img style="cursor: auto" v-if="scope.row.lightingStatus == 1" :src="require('@/assets/images/center/on-light.png')" />
                <img style="cursor: auto" v-else :src="require('@/assets/images/center/off-light.png')" />
              </div>
            </template>
          </el-table-column>
          <!-- <el-table-column :key="4" align="center" fixed width="80" show-overflow-tooltip label="操作">
            <template slot-scope="scope">
              <el-tooltip popper-class="tooltip" effect="light" placement="right">
                <div slot="content" class="light-opeartion">
                  <div v-if="scope.row.lightingStatus == 1" @click="switchOperation(0, scope.row)">关</div>
                    <div v-else @click="switchOperation(1, scope.row)">开</div>
                    <div @click="switchOperation(3, scope.row)">强开</div>
                    <div @click="switchOperation(4, scope.row)">强关</div>
                </div>
                <div class="operation">
                  <img v-if="scope.row.lightingStatus == 1" :src="require('@/assets/images/center/on-light.png')" />
                  <img v-else :src="require('@/assets/images/center/off-light.png')" />
                </div>
              </el-tooltip>
            </template>
          </el-table-column> -->
          <el-table-column width="80" show-overflow-tooltip label="操作">
            <template slot-scope="scope">
              <div slot="content" class="light-opeartion">
                <div v-if="scope.row.lightingStatus == 1" @click="switchOperation(0, scope.row)">关</div>
                <div v-else @click="switchOperation(1, scope.row)">开</div>
              </div>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { GetLightingByFloor, LightOpenOrClose } from '@/utils/centerScreenApi'
export default {
  props: {
    show: {
      type: Boolean,
      default: false
    },
    requestParams: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  data() {
    return {
      tableData: [],
      spaceName: '',
      floorName: ''
    }
  },
  created() {
    this.getRoomInfo()
  },
  methods: {
    switchOperation(status, item, value) {
      const param = {
        type: 3,
        forceSwitch: status === 1 ? '3' : '4',
        outputStatus: status,
        outputNum: '1',
        actuatorId: item.actuatorId
      }
      LightOpenOrClose(param).then((res) => {
        if (res.data.code === '200') {
          this.getRoomInfo()
          try {
            window.chrome.webview.hostObjects.sync.bridge.LightingControl(JSON.stringify({ spaceId: requestParams.spaceId, outputStatus: status }))
          } catch (error) {}
        } else {
          this.$message.warning(res.data.message)
        }
      })
    },
    getRoomInfo() {
      const param = {
        projectCode: this.requestParams.projectCode,
        spaceId: this.requestParams.spaceId,
        name: this.requestParams.ssmType == 6 ? '' : this.requestParams.floorName
      }
      GetLightingByFloor(param).then((res) => {
        if (res.data.code === '200') {
          if (this.requestParams.ssmType == 6) {
            this.tableData = res.data.data
            if (this.tableData && this.tableData.length > 0) {
              this.spaceName = this.tableData[0].spaceName
            }
          } else {
            this.tableData = res.data.data.lightingBySpace
            if (this.tableData && this.tableData.length > 0) {
              this.floorName = this.tableData[0].spaceInfo.spaceName
            }
          }
        }
      })
    },
    closeDialog() {
      this.$emit('closeDialog')
    }
  }
}
</script>

<style lang="scss" scoped>
.spaceDetails {
  ::v-deep .bg-dialog {
    cursor: pointer;
    width: 650px;
    height: 385px;
    background: url('@/assets/images/qhdsys/detailDialogNew.png');
    background-repeat: no-repeat;
    background-size: 100% 100%;
    border: none;
    pointer-events: auto;
    box-shadow: none;
    .bg-dialog-title {
      display: inline-block;
      font-size: 16px;
      color: #fff;
      height: 50px;
      line-height: 50px;
      padding: 10px 0;
    }
    .el-dialog__header {
      height: 50px !important;
    }
    .el-dialog__body {
      height: calc(100% - 50px);
      padding: 10px 20px;
      overflow: auto;
    }
    .el-dialog__headerbtn {
      width: 16px;
      height: 16px;
      right: -18px;
      top: 20px;
      background-image: url('@/assets/images/qhdsys/detailDialogClose.png') !important;
    }
  }
  .spaceDetails-content {
    ::v-deep .el-table {
      background-color: transparent;
      border: 1px solid #202d4c;
      .el-table__header-wrapper {
        background-color: transparent !important;
      }
      .el-table__body-wrapper {
        background-color: transparent !important;
      }
      tr {
        background-color: transparent !important;
      }
      td {
        background-color: transparent !important;
      }
    }
    .light-opeartion {
      height: auto;
      width: auto;
      color: #ffca64 !important;
      div {
        padding: 8px 15px;
        font-size: 14px;
        cursor: pointer;
        &:hover {
          color: #ffe3a6;
        }
      }
    }
    .content-info {
      display: flex;
      justify-content: space-between;
      padding: 10px 0 20px 0;
      box-sizing: border-box;
      .info-item {
        font-size: 14px;
        display: flex;
        .item-label {
          color: #7eaef9;
        }
        .item-value {
          color: #ffffff;
        }
      }
    }
  }
}
</style>
