/*
 * @Author: hedd
 * @Date: 2023-08-11 10:03:30
 * @LastEditTime: 2023-08-21 16:42:02
 * @FilePath: \ihcrs_pc\vitePlugins\fileList.js
 * @Description:
 */
import { join } from 'path'
import formidable from 'formidable'
import fs from 'fs'
export default function fileList() {
  return {
    name: 'vite-plugin-svg-png-files',
    configureServer(server) {
      server.middlewares.use((req, res, next) => {
        const url = req.url
        if ((url.startsWith('/meta2dStatic/svg/') || url.startsWith('/meta2dStatic/png/')) && url.endsWith('/')) {
          // join的地址和文件所在地址 需根据实际情况修改
          const pwd = decodeURI(join(__dirname, '../public', url))
          const files = fs.readdirSync(pwd, {
            withFileTypes: true
          })
          const list = []
          for (const item of files) {
            if (item.isDirectory()) {
              list.push({ name: item.name, type: 'directory' })
            } else {
              list.push({ name: item.name })
            }
          }
          res.end(JSON.stringify(list))
        } else if (url === '/img' && req.method === 'POST') {
          const form = formidable({
            uploadDir: decodeURI(join(__dirname, '../public', '/img')),
            keepExtensions: true
          })
          form.parse(req, (err, fields, files) => {
            if (!err) {
              res.end(JSON.stringify({ url: '/img/' + files.file.newFilename }))
            }
          })
        } else {
          next()
        }
      })
    }
  }
}
