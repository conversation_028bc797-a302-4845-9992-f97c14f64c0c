<template>
  <div class="main" v-loading="loading">
    <el-dialog v-dialogDrag v-if="dialogVisibleSignIn" class="personDialog" :visible.sync="dialogVisibleSignIn" :before-close="closeDialog">
      <template slot="title">
        <span class="dialog-title">值班签到</span>
      </template>
      <div>
        <el-form ref="formInline" :model="formInline" :inline="true" class="form-inline" label-width="100px" :rules="rules">
          <el-form-item label="签到时间" prop="currentDateTime">
            <el-input v-model="formInline.currentDateTime" placeholder="值班日期" disabled></el-input>
          </el-form-item>
          <br />
          <el-form-item label="班次" prop="dutyType">
            <el-radio v-for="item in shiftList" :key="item.type" v-model="formInline.dutyType" :label="item.type">{{ item.dutySignIn }}</el-radio>
          </el-form-item>
          <br />
          <el-form-item label="签到人员">
            <el-button class="new-edition" type="primary" @click="openPeopleDialog">选择</el-button>
            <div>
              <el-tag v-for="tag in checkedStaffList" :key="tag.signInPersonCode" class="camera-tag" closable @close="tagSpaceHandleClose(tag.signInPersonCode)">
                {{ tag.signInPersonName }}
              </el-tag>
            </div>
          </el-form-item>
          <br />
        </el-form>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button class="new-edition" @click="closeDialog" >取 消</el-button>
        <el-button class="new-edition" @click="editSubmit">确 定</el-button>
      </span>
    </el-dialog>
    <template v-if="peopleDialogVisible">
      <dialogConfig
        :visible="peopleDialogVisible"
        :defaultSelectedUser="defaultSelectedUser"
        @updateVisible="closePeopleDialog"
        @advancedSearchFn="submitPeopleDialog"
      ></dialogConfig>
    </template>
  </div>
</template>
<script>
import { signIn } from '@/utils/peaceRightScreenApi'
export default {
  name: 'signInForm',
  components: {
    dialogConfig: () => import('./dialogConfig.vue')
  },
  props: {
    dialogVisibleSignIn: {
      type: Boolean,
      default: false
    },
    currentShift: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  data() {
    return {
      loading: false,
      rules: {},
      formInline: {
        currentDateTime: '',
        dutyType: '',
        dutyPersonCode: '',
        dutyPersonName: ''
      },
      staffDataList: [],
      peopleDialogVisible: false,
      defaultSelectedUser: [],
      checkedStaffList: [],
      shiftList: [
        {
          dutySignIn: "早班",
          type: 0
        }, {
          dutySignIn: "中班",
          type: 2
        }, {
          dutySignIn: "晚班",
          type: 1
        }
      ]
    }
  },
  mounted() {
    console.log(this.currentShift);
    Object.assign(this.formInline, {
      currentDateTime: this.$tools.getCurrentDate(),
      dutyType: this.currentShift.type
    })
  },
  methods: {
    closeDialog() {
      this.$emit('closeDialogSignIn')
      this.reset()
      this.$refs.formInline.resetFields()
    },
    tagSpaceHandleClose(id) {
      // 根据id 删除checkedStaffList中的对应项
      const index = this.checkedStaffList.findIndex((e) => e.signInPersonCode === id)
      this.checkedStaffList.splice(index, 1)
    },
    // 重置
    reset() {
      Object.assign(this.formInline, {
        currentDateTime: this.$tools.getCurrentDate(),
        dutyType: this.currentShift.type,
        dutyPersonCode: '',
        dutyPersonName: ''
      })
    },
    /**
     * 签到保存
     */
    editSubmit() {
      this.$refs.formInline.validate((valid) => {
        if (valid) {
          this.loading = true
          const signInPersonCode = Array.from(this.checkedStaffList, ({ signInPersonCode }) => signInPersonCode)
          const signInPersonName = Array.from(this.checkedStaffList, ({ signInPersonName }) => signInPersonName)
          this.formInline.signInPersonCode = signInPersonCode.length ? signInPersonCode.join(',') : ''
          this.formInline.signInPersonName = signInPersonName.length ? signInPersonName.join(',') : ''
          const data = {
            ...this.formInline
          }
          signIn(data).then((res) => {
            this.loading = false
            const item = res.data
            if (item.code === '200') {
              this.$message.success(item.message)
              this.reset()
              this.$emit('sureSignIn')
            } else {
              this.$message.error(item.message)
            }
          })
        } else {
          this.$tools.focusFunc()
          return false
        }
      })
    },
    openPeopleDialog() {
      this.defaultSelectedUser = Array.from(this.checkedStaffList, ({ signInPersonCode }) => signInPersonCode)
      this.peopleDialogVisible = true
    },
    closePeopleDialog() {
      this.peopleDialogVisible = false
    },
    submitPeopleDialog(data) {
      this.checkedStaffList = data.map((item) => {
        return {
          signInPersonCode: item.id,
          signInPersonName: item.staffName
        }
      })
      this.peopleDialogVisible = false
    }
  }
}
</script>

<style lang="scss" type="text/css" scoped>
::v-deep .personDialog {
  .el-dialog {
    width: 48.25rem;
    height: 35.8125rem;
    margin-top: 18vh !important;
    background-color: transparent !important;
    background-image: url('@/assets/images/table-bg-small.png') !important;
    background-repeat: no-repeat;
    background-size: 100% 100%;
    border: none;
    pointer-events: auto;
    box-shadow: none;
  }
  .el-dialog__body {
    padding: 10px 50px 30px 50px;
    height: calc(100% - 150px);
  }
  .dialog-title {
    display: inline-block;
    width: 100%;
    text-align: center;
    transform: translateY(0px);
    color: #cbdeed;
  }
  .dialog-title::before {
    display: none;
  }
  .el-dialog__header {
    height: 3.125rem;
    line-height: 3.125rem;
    padding: 0;
  }
  .el-dialog__headerbtn {
    transform: translateX(-36px);
    width: 25px;
    height: 25px;
    background-image: url('@/assets/images/close.png');
    background-repeat: no-repeat;
    background-size: 100% 100%;
    .el-dialog__close::before {
      display: none;
    }
  }
}
.camera-tag {
  margin-right: 10px;
  background: rgba(255,255,255,0.1) linear-gradient(90deg, rgba(10,132,255,0) 0%, rgba(10,132,255,0) 100%);
  border: none;
  ::v-deep .el-tag__close:hover {
    color: #fff;
    background: center;
  }
}
.el-input {
  width: 222px;
}
</style>
