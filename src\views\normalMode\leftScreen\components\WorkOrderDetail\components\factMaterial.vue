<template>
  <el-dialog
    v-dialogDrag
    custom-class="factMaterial"
    append-to-body
    :visible.sync="changefactMaterialShow"
    :close-on-click-modal="false"
    :show-close="false"
    :before-close="closeDialog"
    title="耗材选择"
  >
    <div class="dialog-right">
      <div class="dialog-tc" @click="closeDialog"></div>
    </div>
    <div class="dialog-content">
      <div class="content-left">
        <el-tree class="tree" node-key="id" default-expand-all :data="treeData" :props="defaultProps" @node-click="handleNodeClick"></el-tree>
      </div>
      <div class="content-right">
        <div style="margin: 10px;">
          <el-input
            v-model="depProductName"
            style="width: 317px; margin-right: 20px;"
            placeholder="请输入耗材名称"
            maxlength="25"
            onkeyup="if(value.length>25)value=value.slice(0,25)"
            @keyup.enter.native="_searchByCondition"
          ></el-input>
          <el-button class="sino-button-sure" @click="_searchByCondition">查询</el-button>
          <el-button class="sino-button-sure" @click="_resetCondition">重置</el-button>
        </div>
        <el-table
          ref="multipleTable"
          v-loading="tableLoading"
          :data="tableData"
          height="300"
          style="width: 100%;"
          element-loading-background="rgba(0, 0, 0, 0.2)"
          @selection-change="selectionChange"
        >
          <el-table-column fixed type="selection" width="70" align="center"></el-table-column>
          <el-table-column fixed prop="depProductName" show-overflow-tooltip label="耗材名称"></el-table-column>
          <el-table-column fixed prop="remark" show-overflow-tooltip label="耗材说明"></el-table-column>
        </el-table>
        <el-pagination
          :current-page="pageNo"
          :page-sizes="[15, 20, 30, 40]"
          :page-size="pageSize"
          layout="total, sizes, prev, pager, next"
          :total="total"
          class="pagination"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        ></el-pagination>
      </div>
    </div>
    <span slot="footer" class="dialog-footer">
      <el-button class="sino-button-sure" @click="closeDialog">取 消</el-button>
      <el-button class="sino-button-sure" @click="savePeople">确 定</el-button>
    </span>
  </el-dialog>
</template>
<script>
import {
  getfactMaterialTreeData,
  getConsumablesList
} from '@/utils/peaceLeftScreenApi'
export default {
  name: 'factMaterial',
  props: {
    changefactMaterialShow: {
      type: Boolean,
      default: false
    },
    factMaterialData: {
      type: Object,
      default: function () {
        return {}
      }
    }
  },
  data() {
    return {
      tableData: [],
      tableLoading: false,
      treeData: [],
      selectNodeId: '',
      depProductName: '',
      defaultProps: {
        children: 'children',
        label: 'text'
      },
      pageNo: 1,
      pageSize: 15,
      total: 0
    }
  },
  watch: {},
  mounted() {
    this.getTreeData()
    this.getConsumablesList()
  },
  methods: {
    getTreeData() {
      getfactMaterialTreeData({}).then((res) => {
        this.treeData = this.$tools.listToTree(res.data, 'id', 'parent')
      })
    },
    getConsumablesList() {
      this.tableLoading = true
      const params = {
        workTypeCode: '1',
        catalogIdId: this.selectNodeId,
        depProductName: this.depProductName,
        pageNo: this.pageNo,
        pageSize: this.pageSize
      }
      getConsumablesList(params).then((res) => {
        this.tableData = res.data.rows
        this.total = res.data.total
        this.tableLoading = false
      })
    },
    selectionChange(selection) {
      if (selection.length > 1) {
        this.selectionParams = selection.shift()
        this.$refs.multipleTable.toggleRowSelection(this.selectionParams, false)
      } else {
        this.selectionParams = selection
      }
    },
    handleNodeClick(data) {
      this.selectNodeId = data.id
      this.getConsumablesList()
    },
    // 取消按钮
    closeDialog() {
      this.$emit('closeDialog')
    },
    // 确认按钮
    savePeople() {
      this.$emit('factMaterialSure', this.selectionParams[0])
    },
    handleSizeChange(val) {
      this.pageSize = val
      this.getConsumablesList()
    },
    handleCurrentChange(val) {
      this.pageNo = val
      this.getConsumablesList()
    },
    _searchByCondition() {
      this.pageNo = 1
      this.getConsumablesList()
    },
    _resetCondition() {
      this.pageSize = 15
      this.selectNodeId = ''
      this.depProductName = ''
      this._searchByCondition()
    }
  }
}
</script>

<style lang="scss" type="text/css" scoped>
::v-deep .el-dialog{
  background: url('@/assets/images/qhdsys/960×592.png') no-repeat center center / 100% 100% !important;
  .el-dialog__header {
    padding: 10px 20px 10px;
    text-align: center;
    .el-dialog__title {
      height: 20px;
      font-size: 18px;
      font-family: Source Han Sans CN-Regular, Source Han Sans CN;
      font-weight: 400;
      color: #cbdeed;
      line-height: 18px;
    }
  }

  .el-dialog__footer {
    padding-top: 0;
    padding-bottom: 40px;
    padding-right: 40px;
  }
}
.factMaterial {
  .dialog-right {
    position: absolute;
    top: 17px;
    right: 30px;
    display: flex;
    .dialog-tc {
      width: 36px;
      height: 36px;
      background: url('@/assets/images/qhdsys/bg-icon-gb.png') no-repeat;
    }
    .dialog-tc:hover {
      cursor: pointer;
    }
  }
  .dialog-content {
    width: 100%;
    display: flex;
    .content-left {
      width: 30%;
      margin-right: 2%;
      height: 400px;
      overflow: auto;
      // background: #fff;
    }
    .content-right {
      width: 70%;
      // background: #fff;
    }
  }
}
</style>
