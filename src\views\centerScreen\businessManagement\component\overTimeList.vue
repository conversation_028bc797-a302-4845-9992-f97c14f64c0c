<template>
  <div class="main">
    <el-dialog v-dialogDrag :modal="false" :visible.sync="dialogShow" custom-class="mainDialog main" append-to-body :close-on-click-modal="false" :before-close="closeDialog" class="personDialog">
      <template slot="title">
        <span class="dialog-title">超时{{ type | getTitleForType }}列表</span>
      </template>
      <div class="dialog-content">
        <el-table
          :data="tableData"
          height="calc(100% - 40px)"
          :cell-style="$tools.setCell(3)"
          :header-cell-style="$tools.setHeaderCell(3)"
          style="width: 100%"
          v-loading="tableLoading"
          element-loading-background="rgba(0, 0, 0, 0.2)"
        >
          <el-table-column v-for="(item, index) in column" :key="index" fixed :prop="item.field" align="center" show-overflow-tooltip :label="item.name">
            <template slot-scope="scope">
              <div v-if="item.field == 'operation'">
                <div class="operationBtn">
                  <span v-if="scope.row.flowCode !== '5'" style="margin-right: 10px" @click="selectConfigRowData(scope.row, 'cui')">催单</span>
                  <span @click="selectConfigRowData(scope.row, 'detail')">详情</span>
                </div>
              </div>
              <div v-else>
                {{ scope.row[scope.column.property] }}
              </div>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </el-dialog>
    <template v-if="workOrderDetailCenterShow">
      <el-dialog v-dialogDrag :modal="false" :visible.sync="workOrderDetailCenterShow" custom-class="detailDialog main" :close-on-click-modal="false" :before-close="workOrderDetailCloseDialog">
        <template slot="title">
          <span class="dialog-title">综合维修（{{ detailObj.flowtype }}）</span>
        </template>
        <workOrderDetailList :rowData="detailObj" />
      </el-dialog>
    </template>
  </div>
</template>
<script>
import { getCallCenterData, urgeWorkOder } from '@/utils/peaceRightScreenApi'
import workOrderDetailList from '@views/normalMode/rightScreen/components/workOrderDetailList.vue'
export default {
  name: 'overTimeList',
  props: {
    dialogShow: {
      type: Boolean,
      default: false
    },
    location: {
      type: String,
      default: ''
    },
    ssmType: {
      type: String,
      default: ''
    },
    type: {
      type: String,
      default: ''
    }
  },
  components: {
    workOrderDetailList
  },
  data() {
    return {
      tableData: [],
      tableLoading: false,
      column: [
        {
          name: '申报信息',
          field: 'callerName'
        },
        {
          name: '申报时间',
          field: 'createDate'
        },
        {
          name: '申报类型',
          field: 'workTypeName'
        },
        {
          name: '紧急程度',
          field: 'urgencyDegree'
        },
        {
          name: '服务事项',
          field: 'itemServerName'
        },
        {
          name: '申报描述',
          field: 'questionDescription'
        },
        {
          name: '工单状态',
          field: 'flowtype'
        },
        {
          name: '响应时间',
          field: 'xysj'
        },
        {
          name: '操作',
          field: 'operation'
        }
      ],
      workOrderDetailCenterShow: false,
      detailObj: {}
    }
  },
  filters: {
    getTitleForType(val) {
      if (val === 'IOMS') {
        return '工单'
      } else if (val === 'IPAS') {
        return '任务'
      } else if (val === 'IDPS') {
        return '隐患'
      }
    }
  },
  watch: {},
  created() {},
  mounted() {
    this.getDepartMedicalWasteTableList()
  },
  methods: {
    // add by yz 2018年10月15日14:20:51 比较时间差
    compareTime(time1) {
      const date1 = new Date(time1)
      const date2 = new Date()
      const timeLag = (date2 - date1) / 1000 / 60
      return timeLag
    },
    // 获取列表
    getDepartMedicalWasteTableList() {
      const params = {
        curPage: 1,
        pageSize: 50,
        selectType: 5,
        isTimeOut: 1
      }
      this.tableLoading = true
      getCallCenterData(params).then((res) => {
        const data = res.data.body
        this.tableLoading = false
        if (data.result.length) {
          this.tableData = data.result.map((e) => {
            e.xysj = this.compareTime(e.designateDeptDate) < e.overtime ? '待派工' : '已超时'
            e.urgencyDegree = e.data[0].urgencyDegree
            e.itemServerName = e.data[0].itemTypeName !== undefined && e.data[0].itemTypeName !== '' ? e.data[0].itemTypeName : ''
            e.itemServerName += e.data[0].itemServiceName !== undefined && e.data[0].itemServiceName !== '' ? '-' + e.data[0].itemDetailName : ''
            e.itemServerName += e.data[0].itemServiceName !== undefined && e.data[0].itemServiceName !== '' ? '-' + e.data[0].itemServiceName : ''
            return e
          })
        } else {
          this.tableData = []
        }
      })
    },
    selectConfigRowData(row, type) {
      if (type === 'detail') {
        // this.$router.push({ path: '/workOrderDetail', query: { id: row.id } })
        this.detailObj = row
        this.workOrderDetailCenterShow = true
        try {
          window.chrome.webview.hostObjects.sync.bridge.ShowDialog(true)
        } catch (error) {}
      } else if (type === 'cui') {
        this.$confirm('是否催促尽快完成此工单?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          cancelButtonClass: 'sino-button-sure',
          confirmButtonClass: 'sino-button-sure',
          customClass: 'confirm-box-class',
          type: 'warning'
        })
          .then(() => {
            // this.changeUserState(params)
            urgeWorkOder({ id: row.id }).then((res) => {
              if (res.data.code === '200') {
                this.$message({
                  type: 'success',
                  message: '催单成功！'
                })
                this.getDepartMedicalWasteTableList()
              } else {
                this.$message({
                  message: res.data.message,
                  type: 'warning'
                })
              }
            })
          })
          .catch(() => {})
      }
    },
    workOrderDetailCloseDialog() {
      this.workOrderDetailCenterShow = false
      try {
        window.chrome.webview.hostObjects.sync.bridge.ShowDialog(false)
      } catch (error) {}
    },
    // 取消按钮
    closeDialog() {
      this.$emit('configCloseDialog')
    }
  }
}
</script>

<style lang="scss" type="text/css" scoped>
.personDialog {
  pointer-events: none;
  ::v-deep .el-image__error {
    background: center;
  }
}
::v-deep .mainDialog {
  width: 98%;
  height: 40vh;
  margin-top: 54vh !important;
  background: url('@/assets/images/qhdsys/bottom-table-bg.png') no-repeat;
  background-size: 100% 100%;
  padding: 0 35px;
  pointer-events: auto;
  .dialog-title {
    display: inline-block;
    width: 38%;
  }
  .el-dialog__body {
    padding: 0;
    height: calc(100% - 10px);
    .dialog-content {
      width: 100%;
      height: 100%;
      .el-table {
        border: none !important;
        .redBg {
          background: #341f51;
        }
      }
    }
  }
}
::v-deep .detailDialog {
  width: 60%;
  height: 80vh;
  margin-top: 7vh !important;
  background: url('@/assets/images/table-bg.png') no-repeat;
  background-size: 100% 100%;
  pointer-events: auto;
  .el-dialog__body {
    padding: 10px 50px;
    height: calc(100% - 60px);
    max-height: 80vh;
  }
}
</style>
