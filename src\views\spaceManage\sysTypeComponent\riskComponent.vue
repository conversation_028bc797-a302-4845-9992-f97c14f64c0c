<template>
  <div class="riskComponent">
    <div class="module-container" style="height: 30%">
      <div class="module-header">
        <div class="title-left">
          <p class="title-left-icon"></p>
          <p class="title-left-text">风险等级</p>
        </div>
      </div>
      <div class="module-content" style="height: calc(100% - 2.8rem)">
        <div v-if="workOrderStatisticsShow" style="width: 100%; height: 100%">
          <div id="workOrderStatisticsEcharts"></div>
        </div>
        <div v-else class="center-center">暂无数据</div>
      </div>
    </div>
    <div class="module-container" style="height: 30%">
      <div class="module-header">
        <div class="title-left">
          <p class="title-left-icon"></p>
          <p class="title-left-text" v-if="type === '1'">科室风险分布</p>
          <p class="title-left-text" v-else>风险告知</p>
        </div>
        <div class="title-right" v-if="type === '2'">
          <p
            class="title-right-tag"
            :class="{ 'tag-active': tagCurrent == item.value }"
            v-for="item in tagList"
            :key="item.value"
            @click="
              () => {
                tagCurrent = item.value
              }
            "
          >
            {{ item.text }}
          </p>
        </div>
      </div>
      <div class="module-content" style="height: calc(100% - 2.8rem)">
        <div v-if="type === '1'" class="content-change">
          <div v-if="riskDeptShow" style="height: 100%; width: 100%">
            <div id="deptRiskEcharts"></div>
            <div class="pie_background"></div>
          </div>
          <div v-else class="center-center">暂无数据</div>
        </div>
        <div v-else class="content-change">
          <div v-if="tagCurrent === 1" class="img-warp">
            <div v-if="srcList.length === 0" class="center-center">暂无数据</div>
            <el-image v-else class="image-success" :src="srcList[0] || ''" :preview-src-list="srcList"> </el-image>
          </div>
          <div v-if="tagCurrent === 2" class="img-warp">
            <div v-if="noticeList.length === 0" class="center-center">暂无数据</div>
            <el-carousel v-else arrow="always" indicator-position="none" trigger="click" :autoplay="false">
              <el-carousel-item v-for="item in noticeList" :key="item">
                <el-image class="image-success" :src="item" :preview-src-list="noticeList"> </el-image>
              </el-carousel-item>
            </el-carousel>
          </div>
        </div>
      </div>
    </div>
    <div class="module-container" style="height: 40%">
      <div class="module-header">
        <div class="title-left">
          <p class="title-left-icon"></p>
          <p class="title-left-text">风险点清单</p>
        </div>
      </div>
      <div class="module-content" style="height: calc(100% - 2.8rem)">
        <el-table
          class="table-center-transfer"
          :data="tableData"
          height="100%"
          :cell-style="$tools.setCell(3)"
          :header-cell-style="$tools.setHeaderCell(3)"
          style="width: 100%"
          element-loading-background="rgba(0, 0, 0, 0.2)"
          v-loading="tableLoading"
          @row-dblclick="toView"
        >
          <el-table-column fixed prop="riskName" show-overflow-tooltip label="风险点名称"></el-table-column>
          <el-table-column fixed prop="riskLevelName" show-overflow-tooltip label="风险等级"></el-table-column>
          <el-table-column fixed prop="taskTeamName" show-overflow-tooltip label="责任部门"></el-table-column>
          <el-table-column fixed prop="responsiblePersonName" show-overflow-tooltip label="责任人"></el-table-column>
        </el-table>
      </div>
    </div>
    <riskInspection ref="riskInspection" :rowData="rowData"></riskInspection>
  </div>
</template>

<script>
import { GetRiskLevelStatistics, getRiskInform, GetRiskPageList, GetRiskDeptDistribution } from '@/utils/centerScreenApi'
import * as echarts from 'echarts'
import riskInspection from './components/riskInspection.vue'
export default {
  name: 'riskComponent',
  components: {
    riskInspection
  },
  props: {
    roomData: {
      type: Object,
      default: () => {}
    },
    // 1：科室风险分布 2：风险告知
    type: {
      type: String,
      default: '1'
    }
  },
  data() {
    return {
      tableLoading: false,
      tableData: [],
      workOrderStatisticsShow: true,
      riskDeptShow: false,
      tagCurrent: 1,
      tagList: [
        { text: '风险四色图', value: 1 },
        { text: '风险告知栏', value: 2 }
      ],
      srcList: [],
      noticeList: [],
      pageParmes: {
        pageSize: 20,
        pageNo: 1
      },
      rowData: {}
    }
  },
  mounted() {
    this.activeMethods()
  },
  methods: {
    // 获取风险等级
    getRiskLevelAnalysis(id) {
      GetRiskLevelStatistics({ placeIds: id }).then((res) => {
        if (res.data.code === '200') {
          this.$nextTick(() => {
            this.dangerTypeAnalysisEchart(res.data.data.array)
          })
        }
      })
    },
    // 获取风险告知
    getRiskInforms(id) {
      getRiskInform({ placeIds: id }).then((res) => {
        // alert('res' + JSON.stringify(res))
        if (res.data.code === '200') {
          this.srcList = res.data.data.pictureUrl !== '' ? [res.data.data.pictureUrl] : []
          this.noticeList = res.data.data.informUrlList
        } else {
          // alert('else')
        }
      })
    },
    // 风险清单列表
    getRiskPageList(id) {
      GetRiskPageList({ placeIds: id, ...this.pageParmes }).then((res) => {
        if (res.data.code === '200') {
          this.tableData = res.data.data.list
        }
      })
    },
    // 获取科室风险数据
    getRiskDeptDistribution(id) {
      GetRiskDeptDistribution({ placeIds: id }).then((res) => {
        if (res.data.code === '200') {
          const arr = []
          let count = 0
          res.data.data.forEach((i) => {
            count += i.count
            arr.push({
              name: i.teamName,
              count: i.count,
              percentage: (i.count / count).toFixed(2)
            })
          })
          if (arr.length > 0) {
            this.riskDeptShow = true
            this.$nextTick(() => {
              this.getAlarmSourceEchart(arr)
            })
          } else {
            this.riskDeptShow = false
          }
        }
      })
    },
    // 风险等级图表渲染
    dangerTypeAnalysisEchart(obj) {
      const xdata = []
      const ydata = []
      const colorArr = []
      obj.forEach((i) => {
        if (i.level === 1) {
          colorArr.push('#FE0000')
        } else if (i.level === 2) {
          colorArr.push('#FE6100')
        } else if (i.level === 3) {
          colorArr.push('#FEFE01')
        } else if (i.level === 4) {
          colorArr.push('#0000FD')
        }
        xdata.push(i.name)
        ydata.push(i.value)
      })
      const getchart = echarts.init(document.getElementById('workOrderStatisticsEcharts'))
      getchart.resize()
      const option = {
        backgroundColor: '',
        tooltip: {
          trigger: 'axis',
          backgroundColor: '#0D2169',
          borderColor: '#727382',
          textStyle: {
            color: '#fff'
          },
          axisPointer: {
            type: 'shadow'
          }
        },
        grid: {
          left: '12%',
          right: '5%',
          bottom: '15%',
          top: '10%'
        },
        xAxis: [
          {
            type: 'category',
            data: xdata,
            axisTick: {
              show: false
            },
            // splitLine: {
            //   show: false
            // },
            axisLabel: {
              textStyle: {
                color: '#878EA9'
              }
            }
          }
        ],
        yAxis: [
          {
            type: 'value',
            axisLabel: {
              show: true,
              textStyle: {
                color: '#878EA9'
              }
            },
            axisLine: {
              lineStyle: {
                type: 'solid',
                color: '#3862B7',
                width: '1'
              }
            },
            splitLine: {
              lineStyle: {
                color: '#314A89',
                width: 1,
                type: 'solid'
              }
            },
            axisTick: {
              show: false
            }
          }
        ],
        series: [
          {
            type: 'bar',
            barWidth: 30,
            itemStyle: {
              // color: new echarts.graphic.LinearGradient(0, 1, 0, 0, [
              //   {
              //     offset: 0,
              //     color: 'rgba(21, 89, 159, 0.25)'
              //   },
              //   {
              //     offset: 1,
              //     color: 'rgba(9, 244, 196, 0.86)'
              //   }
              // ])
              color: colorArr
            },
            data: ydata
          }
        ]
      }
      getchart.clear()
      getchart.setOption(option)
      // 随着屏幕大小调节图表
      window.addEventListener('resize', () => {
        getchart.resize()
      })
    },
    // 科室风险分布图表
    getAlarmSourceEchart(arr) {
      const getchart = echarts.init(document.getElementById('deptRiskEcharts'))
      const data = []
      var color = ['rgba(31, 250, 255, 0.3)', 'rgba(255, 227, 166, 0.28)', 'rgba(147, 130, 255, 0.26)', 'rgba(0, 248, 114, 0.24)', 'rgba(46, 119, 251, 0.3)']
      var borderColor = ['rgba(31, 250, 255, 1)', 'rgba(255, 227, 166, 1)', 'rgba(147, 130, 255, 1)', 'rgba(0, 248, 114, 1)', 'rgba(46, 119, 251, 1)']
      const xdata = Array.from(arr, ({ name }) => name)
      for (var i = 0; i < arr.length; i++) {
        const randomRgbColor = this.$tools.randomRgbColor('array')
        data.push({
          name: arr[i].name,
          value: arr[i].count,
          percentage: Math.round(arr[i].percentage * (Math.pow(10, 2)), 10),
          itemStyle: {
            normal: {
              borderWidth: 1,
              shadowBlur: 200,
              borderColor: borderColor[i] ?? randomRgbColor[1],
              color: color[i] ?? randomRgbColor[0],
              label: {
                normal: { show: true }
              }
            }
          }
        })
      }
      const option = {
        backgroundColor: '',
        legend: {
          orient: 'vartical',
          x: 'left',
          top: 'center',
          left: '55%',
          bottom: '0%',
          data: xdata,
          itemWidth: 8,
          itemHeight: 8,
          itemGap: 16,
          textStyle: {
            color: '#B3C2DD' //  字体颜色
          },
          formatter: function (name) {
            var oa = option.series[0].data
            for (var i = 0; i < option.series[0].data.length; i++) {
              if (name === oa[i].name) {
                return ' ' + (name.length > 6 ? name.substring(0, 6) + '...' : name) + ' (' + (oa[i].value ? oa[i].value : '0') + ')  ' + oa[i].percentage + '%'
              }
            }
          }
        },
        series: [
          {
            name: '',
            type: 'pie',
            clockWise: false,
            center: ['30%', '50%'],
            radius: ['47%', '70%'],
            label: {
              normal: {
                show: false,
                position: 'center',
                formatter: function (params) {
                  return params.name.length > 6 ? params.name.substring(0, 6) + '...' + '\n' + params.value : params.name + '\n' + params.value
                },
                rich: {
                  value: {
                    padding: 5,
                    align: 'center',
                    verticalAlign: 'middle',
                    fontSize: 20
                  },
                  label: {
                    align: 'center',
                    verticalAlign: 'middle',
                    fontSize: 12
                  }
                }
              },
              emphasis: {
                show: true,
                textStyle: {
                  fontSize: '12',
                  color: '#fff'
                }
              }
            },
            data: data
          }
        ]
      }
      getchart.clear()
      getchart.setOption(option)
      // 随着屏幕大小调节图表
      window.addEventListener('resize', () => {
        getchart.resize()
      })
      getchart.dispatchAction({
        type: 'highlight',
        seriesIndex: 0,
        dataIndex: 0
      })
      getchart.on('mouseover', (e) => {
        // 取消默认高亮
        getchart.dispatchAction({
          type: 'downplay',
          seriesIndex: 0,
          dataIndex: 0
        })
      })
      // 鼠标移出后默认高亮
      getchart.on('mouseout', (e) => {
        getchart.dispatchAction({
          type: 'highlight',
          seriesIndex: 0,
          dataIndex: 0
        })
      })
    },
    // 风险点详情
    toView(row) {
      this.rowData = row
      this.$refs.riskInspection.getRiskDetail(row.id)
      this.$refs.riskInspection.hiddenDangerDetailsListShow = true
      try {
        window.chrome.webview.hostObjects.sync.bridge.GetSwichFloorIsShow(true)
        window.chrome.webview.hostObjects.sync.bridge.ShowDialog(true)
      } catch (error) {}
    },
    activeMethods() {
      this.getRiskLevelAnalysis(this.roomData.ssmCodes.substring(this.roomData.ssmCodes.lastIndexOf(',') + 1))
      this.getRiskInforms(this.roomData.ssmCodes.substring(this.roomData.ssmCodes.lastIndexOf(',') + 1))
      this.getRiskPageList(this.roomData.ssmCodes.substring(this.roomData.ssmCodes.lastIndexOf(',') + 1))
      this.getRiskDeptDistribution(this.roomData.ssmCodes.substring(this.roomData.ssmCodes.lastIndexOf(',') + 1))
    }
  }
}
</script>

<style lang="scss" scoped>
@import '../style/module.scss';
.riskComponent {
  width: 100%;
  height: 100%;
  #workOrderStatisticsEcharts {
    width: 100%;
    height: 100%;
    position: relative;
    z-index: 2;
  }
  #deptRiskEcharts {
    width: 100%;
    height: 100%;
    position: relative;
    z-index: 2;
  }
  .pie_background {
    left: 30%;
  }
  .content-change {
    height: 100%;
    position: relative;
    .img-warp {
      height: 100%;
      text-align: center;
      .image-error {
        height: 100%;
        width: auto;
      }
      .image-success {
        width: 100%;
        height: 100%;
      }
      ::v-deep .el-carousel {
        height: 100% !important;
        .el-carousel__container {
          height: 100%;
        }
      }
    }
  }
}
</style>
