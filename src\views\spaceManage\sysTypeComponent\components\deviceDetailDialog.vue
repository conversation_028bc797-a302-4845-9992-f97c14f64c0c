<template>
  <div class="dialog-content">
    <el-dialog v-dialogDrag width="50%" :modal="false" :visible.sync="dialogShow" custom-class="detailDialog main"
      :close-on-click-modal="false" :before-close="closeDialog">
      <template slot="title">
        <span class="dialog-title">{{ entityData.imsName }}</span>
      </template>
      <div class="form-detail">
        <el-tabs v-model="tabsActiveName" @tab-click="handleClick">
          <el-tab-pane label="电力监测" name="1">
            <div class="tableBox">
              <div class="top-statistics">
                <div class="statistics-box">
                  <p>监测参数</p>
                  <p><span class="normal-color">{{statistics.parametersNum}}</span> 个</p>
                </div>
                <div class="statistics-box">
                  <p>正常</p>
                  <p><span class="normal-color">{{statistics.normalNum}}</span> 个</p>
                </div>
                <div class="statistics-box">
                  <p>异常</p>
                  <p><span class="abnormal-color">{{statistics.abnormalNum}}</span> 个</p>
                </div>
              </div>
              <el-table v-loading="tableLoading" :data="entityData.paramList"
                :cell-style="$tools.setCell(3)" :header-cell-style="$tools.setHeaderCell(3)" style="width: 100%"
                height="calc(100% - 98px)" element-loading-background="rgba(0, 0, 0, 0.2)">
                <el-table-column prop="ispParamName" show-overflow-tooltip label="监测参数"></el-table-column>
                <el-table-column prop="ispParamValue" show-overflow-tooltip label="监测数据">
                  <template slot-scope="scope">
                    <span>{{scope.row.ispParamValue + (scope.row.ispUnitName || '')}}</span>
                  </template>
                </el-table-column>
                <el-table-column prop="warnName" show-overflow-tooltip label="状态">
                  <template slot-scope="scope">
                    <span :style="{'color': scope.row.warnColor}">{{scope.row.warnName}}</span>
                  </template>
                </el-table-column>
                <el-table-column prop="warnStandard" show-overflow-tooltip label="区间值"></el-table-column>
              </el-table>
            </div>
          </el-tab-pane>
          <el-tab-pane v-if="entityData.entityTypeId != '30030'" label="谐波监测" name="2">
            <div class="search-box">
              <div style="margin-right: 16px" class="assetsInput">
                <span>日期：</span>
                <el-date-picker key="date" v-model="searchDate" popper-class="date-style" type="date" placeholder="日期"
                  :clearable="false" value-format="yyyy-MM-dd" format="yyyy-MM-dd" @change="dateChange">
                </el-date-picker>
              </div>
              <div v-for="(v, i) in typeArr" :key="i" :class="{
                  'search-aside-item': true,
                  'search-aside-item-active': selectedSonType === v.paramId
                }" @click="onTypeQuick(v)">
                {{ v.paramName }}
              </div>
              <div style="margin-right: 16px" class="assetsInput">
                <span>谐波阶数</span>
                <el-select v-model="harmonicOrder" multiple collapse-tags style="margin: 0 8px" value-key="paramName"
                  placeholder="请选择" @change="rankChange">
                  <el-option v-for="item in harmonicOrderArr" :key="item.paramName" :label="item.paramName"
                    :value="item"> </el-option>
                </el-select>
              </div>
              <div class="radioItem">
                <el-radio-group v-model="harmonicRadio" @change="harmonicRadioChange">
                  <el-radio v-for="item in selectedSonType == 1 ? harmonicVoltageArr : harmonicCurrentArr"
                    :key="item.paramId" :value="item.paramId" :label="item.paramId">
                    {{ item.paramName }}
                  </el-radio>
                </el-radio-group>
              </div>
              <!-- <div class="search-btn">
                <el-button @click="resetEntitySearch">重置</el-button>
                <el-button @click="handleEntitySearch">查询</el-button>
              </div> -->
            </div>
            <div ref="HarmonicWaveECharts" style="height: 300px; height: calc(100% - 77px)"></div>
          </el-tab-pane>
          <el-tab-pane v-if="entityData.entityTypeId != '30030'" label="能耗监测" name="3">
            <deviceEnergyData v-if="tabsActiveName == 3" :surveyCode="dialogData.surveyCode || entityData.imsCode"
              :dialogData="dialogData" />
          </el-tab-pane>
          <el-tab-pane v-if="entityData.entityTypeId != '30030'" label="基础台账" name="4">
            <basicAccountComponent v-if="tabsActiveName == 4" class="baseInfo-box" :hasTitle="false"
              :deviceId="entityData.assetsId" />
          </el-tab-pane>
          <el-tab-pane v-if="entityData.entityTypeId != '30030'" label="设备绩效" name="5">
            <deviceKpi v-if="tabsActiveName == 5" />
          </el-tab-pane>
          <el-tab-pane v-if="entityData.entityTypeId != '30030'" label="监测事件" name="6">
            <PowerQuality v-if="tabsActiveName == 6" showPageType="6" />
          </el-tab-pane>
          <el-tab-pane v-if="entityData.entityTypeId != '30030'" label="事件计数" name="7">
            <PowerQuality v-if="tabsActiveName == 7" showPageType="7" :objectId="entityData.imsCode" />
          </el-tab-pane>
        </el-tabs>
        <!-- deviceType 0重点设备 1配电柜 -->
        <div v-if="entityData.associationModelCode" class="el-tabs-more" @click="checkMore">
          {{ entityData.deviceType == '1' ? '查看重要设备' : '查看电表位置' }}</div>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import moment from 'moment'
import { queryParamList, queryHarmonicMonitorData, queryKeyDeviceByModelCode, getSurveyStatictiesByCode } from '@/utils/spaceManage'
import * as echarts from 'echarts'
export default {
  name: 'deviceDetailDialog',
  components: {
    deviceEnergyData: () => import('./deviceEnergyData.vue'),
    basicAccountComponent: () => import('../basicAccountComponent'),
    deviceKpi: () => import('./deviceKpi/index.vue'),
    PowerQuality: () => import('./PowerQuality.vue')
  },
  props: {
    dialogData: {
      type: Object,
      default: () => { }
    },
    dialogShow: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      harmonicOrderArr: [],
      harmonicOrder: [],
      isKeyDevice: true,
      tabsActiveName: '1',
      selectedSonType: 1, //  选择的类型,
      // 列表
      entityData: {
        paramList: [],
        imsName: '',
        imsCode: '',
        menuCode: '',
        entityTypeId: ''
      },
      statistics: {
        parametersNum: 0,
        normalNum: 0,
        abnormalNum: 0
      },
      // tableData: [],
      tableLoading: false,
      // currentPage: 1,
      // pageSize: 15,
      // total: 0,
      searchDate: moment().format('YYYY-MM-DD'),
      harmonicRadio: null,
      // 电压单选数据
      harmonicVoltageArr: [
        {
          paramName: 'A相电压',
          paramId: '0'
        },
        {
          paramName: 'B相电压',
          paramId: '1'
        },
        {
          paramName: 'C相电压',
          paramId: '2'
        }
      ],
      typeArr: [
        { paramName: '电压谐波', paramId: 1 },
        { paramName: '电流谐波', paramId: 0 }
      ],
      // 电流单选数据
      harmonicCurrentArr: [
        {
          paramName: 'A相电流',
          paramId: '0'
        },
        {
          paramName: 'B相电流',
          paramId: '1'
        },
        {
          paramName: 'C相电流',
          paramId: '2'
        }
      ],
      chartData: []
    }
  },
  computed: {},
  mounted() {
    this.initEvent()
  },
  methods: {
    // 初始化事件
    initEvent() {
      const { modelCode, projectCode, surveyCode } = this.dialogData
      let params = {
        projectCode: projectCode
      }
      if (surveyCode) {
        params.surveyCode = surveyCode
      } else {
        params.modelCode = modelCode
      }
      this.getTableData(params)
      this.getSurveyStaticties(params)
    },
    // 获取table数据
    getTableData(params) {
      this.tableLoading = true
      queryKeyDeviceByModelCode(params).then((res) => {
        this.tableLoading = false
        const resData = res.data
        if (resData.code === '200' && resData.data.length) {
          this.entityData = resData.data[0]
          // this.entityData.associationModelCode = '3AA05-4'
          // console.log(this.entityData.imsCode);
          // this.dialogData.surveyCode = this.entityData.imsCode
        } else {
          this.tableLoading = false
        }
      })
    },
    // 获取统计数据
    getSurveyStaticties(params) {
      getSurveyStatictiesByCode(params).then((res) => {
        const resData = res.data
        if (resData.code === '200') {
          Object.assign(this.statistics, resData.data)
        } else {
          this.statistics = {
            parametersNum: 0,
            normalNum: 0,
            abnormalNum: 0
          }
        }
      })
    },
    colorAdd(transparency) {
      return [
        `rgba(255, 100, 97, ${transparency})`,
        `rgba(255, 148, 53, ${transparency})`,
        `rgba(53, 98, 219, ${transparency})`,
        `rgba(0, 188, 109, ${transparency})`,
        `rgba(115, 192, 222, ${transparency})`,
        `rgba(154, 96, 180, ${transparency})`,
        `rgba(250, 200, 88, ${transparency})`,
        `rgba(9, 205, 143, ${transparency})`
      ]
    },
    // 初始化统计表
    getRenderer(data) {
      let colorHalf = this.colorAdd('.5')
      let colorZero = this.colorAdd('0')
      // 基于准备好的dom，初始化echarts实例
      let ECharts = echarts.init(this.$refs.HarmonicWaveECharts)
      // seriesData
      let seriesData = []
      data.forEach((v) => {
        v.list?.forEach((sonV, index) => {
          let obj = seriesData.find((ele) => ele.name == sonV.name)
          var value = sonV.value
          if (obj) {
            obj.data.push([v.date, value])
          } else {
            let dataObj = {
              name: sonV.name,
              type: 'line',
              data: [[v.date, value]],
              // symbol: 'circle',
              areaStyle: {
                normal: {
                  color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                    {
                      offset: 0,
                      color: colorHalf[index + 1]
                    },
                    {
                      offset: 1,
                      color: colorZero[index + 1]
                    }
                  ])
                }
              }
            }
            seriesData.push(dataObj)
          }
        })
      })

      // 其他  原始数据||谐波检测
      let xOther = {
        type: 'time',
        interval: 60 * 60 * 1000, // 固定x轴时间间隔 间隔24小时，也就是一天
        maxInterval: 60 * 60 * 1000,
        minInterval: 1000,
        min: `${this.searchDate} 00:00:00`, // 开始时间时间戳
        max: `${this.searchDate} 24:00:00`, // 结束时间时间戳 如果实际的最大日期不确定，也可以不设定这个属性
        boundaryGap: false,
        // nameLocation: 'start',
        // x轴的字
        // axisLabel: {
        //   show: true,
        //   showMinLabel: true,
        //   showMaxLabel: true,
        //   formatter: function (value, index) {
        //     // 格式化成月/日，只在第一个刻度显示年份
        //     var date = new Date(value)
        //     return date.getHours() + ':00'
        //   }
        // },
        splitLine: {
          show: false
        },
        axisTick: {
          show: false // 不显示坐标轴刻度线
        }
      }
      if (seriesData.length) {
        let option = {
          title: {
            text: `${this.entityData.imsName}  ${this.searchDate} ${this.selectedSonType == 1 ? '电压' : '电流'}谐波 `,
            left: 'center',
            top: '0',
            textStyle: {
              // 标题内容的样式
              color: '#ffffff',
              fontSize: 14, // 主题文字字体大小，默认为18px,
              top: '20px' //
            }
          },
          color: colorHalf,
          tooltip: {
            trigger: 'axis'
          },
          legend: {
            // icon: 'rect',
            show: true,
            top: '9%',
            textStyle: {
              color: '#ffffff',
              fontSize: 16
            }
          },
          xAxis: xOther,
          yAxis: {
            type: 'value',
            axisLabel: {
              formatter: '{value}'
            }
          },
          series: seriesData,
          dataZoom: [
            {
              height: 20, // 时间滚动条的高度
              type: 'inside', // type的作用是指定数据缩放的类型，slider表示使用滑动条进行缩放，inside表示使用鼠标滚轮进行缩放。
              xAxisIndex: 0, // 作用在x轴的下标（因为x轴可以有多个）
              filterMode: 'filter', // 间滚动条的过滤模式,'filter'表示滑动时间条时会直接过滤掉不在时间范围内的数据，'weakFilter'表示滑动时间条时会逐渐过滤掉不在时间范围内的数据。
              start: 0, // 默认开始位置（百分比）
              end: 100 // 默认结束位置（百分比）
            },
            {
              type: 'inside',
              xAxisIndex: 0,
              filterMode: 'filter',
              start: 0,
              end: 100
            }
          ],
          grid: {
            // 让图表占满容器
            top: '80px',
            left: '10px',
            right: '30px',
            bottom: '0',
            containLabel: true
          }
        }
        ECharts.clear()
        ECharts.setOption(option)
      } else {
        let option = {
          title: {
            text: '暂无数据',
            x: 'center',
            y: 'center',
            textStyle: {
              fontSize: 14,
              fontWeight: 'normal',
              color: '#999'
            }
          }
        }
        ECharts.clear()
        ECharts.setOption(option)
      }
    },
    // 查询重置
    reset() { },
    // 查看重要设备、电表位置 传给wpf设备的modelCode
    checkMore() {
      const sendCode = this.entityData.associationModelCode
      try {
        window.chrome.webview.hostObjects.sync.bridge.ViewImportantEquipment(sendCode.split(',')[0])
      } catch (error) { }
    },
    handleClick(tab) {
      if (tab.name === '1') {
        this.initEvent()
      } else {
        this.selectedSonType = 1
        this.searchDate = moment().format('YYYY-MM-DD')
        this.queryParamList()
        setTimeout(() => {
          this.getChartData()
        }, 200)
      }
    },
    // 时间改变
    dateChange(e) {
      setTimeout(() => {
        this.getChartData()
      }, 200)
    },
    // 谐波结束change
    rankChange() {
      setTimeout(() => {
        this.getChartData()
      }, 200)
    },
    // radio选择
    harmonicRadioChange() {
      setTimeout(() => {
        this.getChartData()
      }, 200)
    },
    getChartData() {
      const { projectCode } = this.dialogData
      let monitorEntityDetail
      const arr = this.selectedSonType == 1 ? this.harmonicVoltageArr : this.harmonicCurrentArr
      const paramName = arr.find((ele) => ele.paramId == this.harmonicRadio).paramName
      monitorEntityDetail = this.harmonicOrder
        .map((ele) => {
          return ele.paramObj.find((v) => v.paramName == paramName)?.paramId || ''
        })
        .toString()

      const params = {
        endTime: this.searchDate,
        menuCode: this.entityData.menuCode,
        monitorEntityCode: this.entityData.imsCode,
        monitorEntityDetail: monitorEntityDetail,
        projectCode: projectCode,
        queryModel: 0,
        selectedSonType: this.selectedSonType,
        startTime: this.searchDate
      }
      queryHarmonicMonitorData(params).then((res) => {
        if (res.data.code === '200') {
          let newData = res.data.data.map((ele) => {
            return {
              date: ele.date,
              list: ele.electDataResponse.map((v) => {
                return {
                  name: v.paramName,
                  value: v.paramValue
                }
              })
            }
          })
          this.chartData = newData
          this.getRenderer(this.chartData)
        }
      })
    },
    // 电压谐波or电流谐波
    onTypeQuick(v) {
      this.selectedSonType = v.paramId
      this.queryParamList()
      setTimeout(() => {
        this.getChartData()
      }, 200)
    },
    queryParamList() {
      queryParamList({ paramId: this.selectedSonType }).then((res) => {
        if (res.data.code === '200') {
          this.harmonicOrder = res.data.data && res.data.data.length ? [res.data.data[0]] : []
          this.harmonicRadio = '0'
          this.harmonicOrderArr = res.data.data
        }
      })
    },
    closeDialog() {
      this.$emit('deviceCloseDialog')
    }
    // resetEntitySearch() {},
    // handleEntitySearch() {}
  }
}
</script>

<style lang="scss" scoped>
.form-detail {
  height: calc(100% - 30px);
  width: 100%;
  overflow-y: auto;
  box-sizing: border-box;
  .tableBox {
    width: 100%;
    // padding: 20px 0px 20px 20px;
    color: #b5bacb;
    height: 440px;
    font-size: 13px;
    .top-statistics {
      height: 88px;
      width: 100%;
      display: flex;
      justify-content: space-between;
      margin-bottom: 10px;
      .statistics-box {
        width: calc(100% / 3 - 12px);
        height: 100%;
        display: flex;
        flex-direction: column;
        justify-content: space-around;
        align-items: center;
        background: rgba(53, 98, 219, 0.06);
        p {
          font-size: 15px;
          font-family: PingFang SC, PingFang SC;
          color: #ffffff;
          span {
            font-size: 24px;
            font-family: Arial, Arial;
            font-weight: bold;
          }
        }
      }
    }
  }
  ::v-deep .el-tabs {
    height: 100%;
    .el-tabs__content {
      height: calc(100% - 50px);
      .el-tab-pane {
        height: 100%;
      }
    }
    .el-tabs__nav {
      .el-tabs__item {
        padding: 10px 30px;
        text-align: center;
        font-size: 14px;
        color: #a4afc1;
        height: unset;
        line-height: unset;
        background: linear-gradient(360deg, #334572 0%, rgba(38, 49, 79, 0.14) 57%, rgba(36, 46, 73, 0) 100%);
        border: 1px solid;
        border-image: linear-gradient(180deg, rgba(113, 125, 168, 0), rgba(113, 128, 168, 1)) 1 1;
        border-top: none;
      }
      .is-active {
        color: #b0e3fa;
        background: linear-gradient(360deg, #3a668e 0%, rgba(36, 46, 73, 0) 100%);
        border: 1px solid;
        border-image: linear-gradient(180deg, rgba(113, 125, 168, 0), rgba(113, 128, 168, 1)) 1 1;
        border-top: none;
      }
    }
    .el-tabs__active-bar {
      display: none;
    }
    .el-tabs__nav-wrap::after {
      height: 0;
    }
  }
  .el-tabs-more {
    position: absolute;
    top: 30px;
    right: 140px;
    cursor: pointer;
  }
  .search-box {
    // padding: 20px 10px;
    height: 67px;
    display: flex;
    flex-direction: row;
    flex-wrap: wrap;
    .search-aside-item {
      display: inline-block;
      font-size: 14px;
      padding: 0 30px;
      height: 32px;
      line-height: 32px;
      color: #8bddf5 !important;
      border: 1px solid #a3a9c0;
      margin-right: 20px;
      border-radius: 4px;
      cursor: pointer;

      &:hover,
      &:focus {
        background: rgba(255, 255, 255, 0.1) linear-gradient(90deg, rgba(10, 132, 255, 0.4) 0%, rgba(10, 132, 255, 0) 100%);
        border: 1px solid #abf0ff !important;
      }
    }

    .search-aside-item-active {
      background: rgba(255, 255, 255, 0.1) linear-gradient(90deg, rgba(10, 132, 255, 0.4) 0%, rgba(10, 132, 255, 0) 100%);
      border: 1px solid #abf0ff !important;
      font-weight: 500;
    }
    .radioItem {
      margin-left: 10px;
      height: 32px;
      line-height: 32px;
    }
  }
  .baseInfo-box {
    height: 100%;
  }
}
::v-deep .search-box {
  display: flex;
  margin-bottom: 10px;
  .assetsInput {
    .el-input {
      width: 180px;
    }
    .el-select {
      .el-input .el-input__inner {
        border-color: rgba(133, 145, 206, 0.5);
        width: 180px;
        height: 35px;
      }
    }
  }
  .el-button {
    background-image: url('@/assets/images/btn.png');
    background-repeat: no-repeat;
    background-size: 100% 100%;
    height: 35px;
  }
  .search-btn {
    margin-left: 24px;
  }
  .el-select {
    .el-input .el-input__inner {
      border-color: rgba(133, 145, 206, 0.5);
      width: 150px;
      height: 35px;
    }
  }
  .el-cascader {
    .el-input__inner {
      height: 35px !important;
    }
  }
  .el-date-editor {
    width: 250px;
    height: 35px;
    background-color: #14233e;
    border-color: rgba(133, 145, 206, 0.5);
    .el-input__icon {
      transform: translateY(-2px);
    }
    .el-range-input {
      background-color: #14233e;
      color: rgba(255, 255, 255, 0.8);
    }
    .el-range-separator {
      color: rgba(255, 255, 255, 0.8);
      transform: translateY(-2px);
    }
  }
  .el-input .el-input__inner {
    border-color: rgba(133, 145, 206, 0.5);
    border-radius: 4px;
    color: rgba(255, 255, 255, 0.8);
  }
}
::v-deep .el-radio {
  color: #ffffff !important;
}
::v-deep .detailDialog {
  width: 50% !important;
  height: 70vh;
  margin-top: 15vh !important;
  background-color: transparent !important;
  background-image: url('@/assets/images/table-bg-small.png');
  background-repeat: no-repeat;
  background-size: 100% 100%;
  border: none;
  pointer-events: auto;
  box-shadow: none;

  .el-dialog__body {
    padding: 10px 50px;
    height: calc(100% - 60px);
    max-height: 80vh;
  }
  .dialog-title {
    display: inline-block;
    width: 100%;
    text-align: center;
    transform: translateY(-6px);
    color: #cbdeed;
  }
  .dialog-title::before {
    display: none;
  }
  .el-dialog__header {
    padding: 20px 20px 10px !important;
  }
  .el-dialog__headerbtn {
    transform: translateX(-36px);
    width: 25px;
    height: 25px;
    background-image: url('@/assets/images/close.png');
    background-repeat: no-repeat;
    background-size: 100% 100%;
    .el-dialog__close::before {
      display: none;
    }
  }
}
</style>
