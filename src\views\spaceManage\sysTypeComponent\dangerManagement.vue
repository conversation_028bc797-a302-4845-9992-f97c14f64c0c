<template>
  <div class="dangerManagement">
    <div v-if="!isBuilding" class="moduleContainer" style="height: 115px">
      <div class="titleBar">隐患统计</div>
      <div class="dangerStatistics">
        <div class="dangerItem">
          <div class="">重点隐患</div>
          <div>{{ dangerStatisticsData.majorDangerCount }}</div>
        </div>
        <div class="dangerItem">
          <div>一般隐患</div>
          <div>{{ dangerStatisticsData.commonDangerCount }}</div>
        </div>
        <div class="dangerItem">
          <div>待整改隐患</div>
          <div>{{ dangerStatisticsData.noRectificationCount }}</div>
        </div>
        <div class="dangerItem">
          <div>隐患整改率</div>
          <div>{{ dangerStatisticsData.efficient }}</div>
        </div>
      </div>
    </div>
    <div v-else class="moduleContainer" style="height: calc((100% - 115px) * 0.33)">
      <div class="titleBar">{{ roomData.title.substring(roomData.title.lastIndexOf('>') + 1, roomData.title.length) }}隐患统计</div>
      <div class="module-content" style="height: calc(100% - 32px); margin-bottom: 16px">
        <div v-if="workOrderStatisticsShow" id="dangerStatisticsEcharts" style="width: 100%; height: calc(100% - 16px)"></div>
        <div v-else class="center-center">暂无数据</div>
      </div>
    </div>
    <div v-if="!isBuilding" class="moduleContainer" style="height: calc((100% - 115px) * 0.33)">
      <div class="titleBgBar">
        <div class="titleText">隐患排名</div>
        <div class="rightBar">
          <div class="btnBar">
            <div v-for="(i, index) in dangerOption" :key="index" class="tabItem" :class="dangerTopType == i.id ? 'selected' : ''" @click="changeDangerTopType(i)">
              {{ i.name }}
            </div>
          </div>
        </div>
      </div>
      <div class="module-content" style="height: calc(100% - 44px)">
        <div v-if="dangerTopShow" id="dangerTop" style="width: 100%; height: 100%"></div>
        <div v-else class="center-center">暂无数据</div>
      </div>
    </div>
    <div v-else class="moduleContainer" style="height: calc((100% - 115px) * 0.33)">
      <div class="titleBgBar">
        <div class="titleText">显示巡查点色</div>
      </div>
      <div class="dyeContent" style="height: calc(100% - 44px)">
        <template v-if="dyeOption.length > 0">
          <el-checkbox v-model="checkAll" :indeterminate="isIndeterminate" @change="handleCheckAllChange">全选</el-checkbox>
          <el-checkbox-group v-model="dyeCheckList" @change="handleCheckedCitiesChange">
            <el-checkbox v-for="(i, index) in dyeOption" :key="index" :label="i.taskPointName">
              {{ i.taskPointName }}
            </el-checkbox>
          </el-checkbox-group>
        </template>
        <div v-else class="center-center" style="color: #a8acbd; font-size: 14px">暂无数据</div>
      </div>
    </div>
    <div class="moduleContainer" style="height: calc((100% - 115px) * 0.33)">
      <div class="titleBgBar">
        <div class="titleText">隐患列表</div>
        <div class="rightBar">
          <img height="24px" src="@/assets/images/qhdsys/联合筛选.png" alt="筛选" title="筛选" @click="riskLevelTop" />
          <img height="24px" src="@/assets/images/qhdsys/bg-gd.png" alt="更多列表" title="更多列表" @click="showDangerDialog('danger')" />
          <div v-show="riskMenuShow" class="riskMenuWrap">
            <el-checkbox-group v-model="checkRiskLevel">
              <el-checkbox v-for="(i, index) in menuDangerOption" :key="index" :label="i.name" class="itemMenu" @change="changerRiskLevelFilter(i)"></el-checkbox>
            </el-checkbox-group>
          </div>
        </div>
      </div>
      <div class="bottom-tab" style="height: calc(100% - 54px); margin: 5px 0">
        <el-table
          v-loading="tableLoading"
          :data="tableData"
          class="table-center-transfer"
          :cell-style="{ padding: ' 8px', backgroundColor: 'transparent', border: 'none', padding: '3px' }"
          :header-cell-style="{ background: '#8591CE26!important', color: '#8BDDF5FF', padding: '4px 8px', fontWeight: 'bold' }"
          height="100%"
          style="width: 100%"
          element-loading-background="rgba(0, 0, 0, 0.2)"
        >
          <!-- <el-table-column fixed prop="questionDetailType" width="110" show-overflow-tooltip label="隐患分类"></el-table-column> -->
          <el-table-column fixed prop="questionAddress" width="110" show-overflow-tooltip label="隐患位置"></el-table-column>
          <el-table-column fixed prop="riskName" show-overflow-tooltip label="隐患等级">
            <template slot-scope="scope">
              <span :class="'level' + scope.row.riskCode">{{ scope.row.riskName }}</span>
            </template>
          </el-table-column>
          <el-table-column fixed prop="dutyDeptName" show-overflow-tooltip label="责任部门"></el-table-column>
          <el-table-column fixed prop="claimPersonName" show-overflow-tooltip label="责任人"></el-table-column>
        </el-table>
      </div>
    </div>
    <div class="moduleContainer" style="height: calc((100% - 115px) * 0.33)">
      <div class="titleBgBar">
        <div class="titleText">巡查列表({{ pagination.total }})</div>
        <div class="searchData">
          <el-dropdown trigger="click" @command="dataTypeCommand">
            <span class="el-dropdown-link"> {{ dataTypeList.find((v) => v.value == statisticalType)?.name ?? '' }} <i class="el-icon-caret-bottom"></i> </span>
            <el-dropdown-menu slot="dropdown">
              <el-dropdown-item v-for="item in dataTypeList" :key="item.value" :command="item.value" :class="{ isBjxl: statisticalType == item.value }">{{ item.name }}</el-dropdown-item>
            </el-dropdown-menu>
          </el-dropdown>
          <el-date-picker
            ref="datePicker"
            v-model="statisticalDate"
            class="datePickerInput"
            popper-class="date-style"
            type="daterange"
            value-format="yyyy-MM-dd"
            :disabled="statisticalType != 5"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            @change="changeDate"
          >
          </el-date-picker>
          <img height="24px" src="@/assets/images/qhdsys/bg-gd.png" alt="更多列表" title="更多列表" @click="toTaskList('task')" />
        </div>
      </div>
      <div class="bottom-tab" style="height: calc(100% - 54px); margin: 5px 0">
        <el-table
          v-loading="tableTaskLoading"
          v-el-table-infinite-scroll="tableLoadEvent"
          :data="taskList"
          class="table-center-transfer"
          :cell-style="{ padding: ' 8px', backgroundColor: 'transparent', border: 'none', padding: '3px' }"
          :header-cell-style="{ background: '#8591CE26!important', color: '#8BDDF5FF', padding: '4px 8px', fontWeight: 'bold' }"
          height="100%"
          style="width: 100%"
          element-loading-background="rgba(0, 0, 0, 0.2)"
          @row-dblclick="selectConfigRowData"
        >
          <el-table-column fixed prop="taskName" show-overflow-tooltip label="任务名称"></el-table-column>
          <el-table-column fixed prop="planName" show-overflow-tooltip label="任务类型"></el-table-column>
          <el-table-column fixed prop="dutyDeptName" show-overflow-tooltip label="任务进度">
            <template slot-scope="scope">
              {{ ((scope.row.hasCount / scope.row.totalCount) * 100).toFixed(2) }}
            </template>
          </el-table-column>
          <el-table-column fixed width="120" prop="createPersonName" show-overflow-tooltip label="执行人员">
            <template slot-scope="scope">
              {{ scope.row.planPersonName ? scope.row.planPersonName : scope.row.distributionTeamName }}
            </template>
          </el-table-column>
        </el-table>
      </div>
    </div>
    <template v-if="allTableComponentListShow">
      <allTableComponentList ref="allTableComponentList" :dialogData="tableCompenentData" :dialogShow="allTableComponentListShow" @configCloseDialog="configCloseTableDialog"></allTableComponentList>
    </template>
    <inspectionDetail
      ref="inspectionDetail"
      systemType="xjrw"
      taskType="ipsm"
      :dataInfo="taskkDetailObj"
      @closeDialog="closeInspPointDialog"
    ></inspectionDetail>

  </div>
</template>
<script>
import {
  GetDangerStatistics,
  GetDeptDangerStatistics,
  GetDangerSubordinateStatistics,
  GetHiddenDangersList,
  getIpsmTaskListData,
  GetStateStatistics,
  getTaskPointSpaceList
} from '@/utils/centerScreenApi'
import * as echarts from 'echarts'
import moment from 'moment'
import allTableComponentList from '../components/allTableComponentList.vue'
import inspectionDetail from './components/inspectionDetail.vue'
import { dialogTypeList } from '@/assets/common/dict.js'
moment.locale('zh-cn', { week: { dow: 1}})
export default {
  components: {
    allTableComponentList,
    inspectionDetail
  },
  props: {
    roomData: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      ssmCodes: [],
      dangerOption: [
        {
          id: 'dept',
          name: '部门'
        },
        {
          id: 'space',
          name: '建筑'
        }
      ],
      dangerTopType: 'dept',
      dangerStatisticsData: {},
      dangerTopShow: true,
      menuDangerOption: [
        {
          id: '',
          name: '全部'
        },
        {
          id: '2',
          name: '重点隐患'
        },
        {
          id: '1',
          name: '一般隐患'
        }
      ],
      riskMenuShow: false,
      checkRiskLevel: ['全部'],
      tableData: [],
      tableLoading: false,
      tableTaskLoading: false,
      taskList: [],
      pagination: {
        total: 0,
        pageSize: 20,
        pageNo: 1
      },
      statisticalDate: [moment().format('YYYY-MM-DD'), moment().format('YYYY-MM-DD')], // 选中日期
      dataTypeList: [
        { id: 'day', value: 1, name: '今日' },
        { id: 'week', value: 2, name: '本周' },
        { id: 'month', value: 3, name: '本月' },
        { id: 'year', value: 4, name: '本年' },
        { id: '', value: 5, name: '自定义' }
      ],
      statisticalType: 1,
      allTableComponentListShow: false,
      tableCompenentData: {},
      dialogTypeList,
      workOrderStatisticsShow: false,
      isIndeterminate: false,
      checkAll: false,
      dyeCheckList: [],
      dyeOption: [],
      isBuilding: false,
      taskkDetailObj: {}
    }
  },
  watch: {
    roomData: {
      handler(val) {
        this.activeMethods()
        // this.ssmCodes = this.roomData.ssmCodes?.split(',') || []
        // if (newValue >= 5) {
        //   this.isBuilding = true
        //   this.getHiddenDangerData(this.roomData.ssmCodes.substring(this.roomData.ssmCodes.lastIndexOf(',') + 1))
        //   this.getFloorTaskList()
        // }
      },
      deep: true
    }
    // 'roomData.ssmType': {
    //   handler: function (newValue, oldValue) {
    //     if (newValue >= 5) {
    //       this.isBuilding = true
    //       this.getHiddenDangerData(this.roomData.ssmCodes.substring(this.roomData.ssmCodes.lastIndexOf(',') + 1))
    //       this.getFloorTaskList()
    //     }
    //   }
    // }
  },
  created() {
    this.activeMethods()
  },
  methods: {
    activeMethods() {
      this.ssmCodes = this.roomData.ssmCodes?.split(',') || []
      if (this.roomData.ssmType >= 5) {
        this.isBuilding = true
        this.workOrderStatisticsShow = true
        this.getFloorTaskList()
        this.getHiddenDangerData(this.ssmCodes.at(-1))
      } else {
        this.isBuilding = false
        const dom = document.getElementById('dangerStatisticsEcharts')
        if (dom) {
          let getchart = echarts.init(dom)
          getchart?.dispose()
        }
        this.workOrderStatisticsShow = false
        this.getDangerStatisticsData()
        this.getGetDeptDangerStatisticsData()
      }
      this.getGetHiddenDangersListData()
      this.getTaskListData()
    },
    // 双击打开巡检详情
    selectConfigRowData(row) {
      this.taskkDetailObj = row
      this.$nextTick(() => {
        this.$refs.inspectionDetail.hiddenDangerDetailsListShow = true
        this.$refs.inspectionDetail.GetTaskPointReleaseList(row.id, 'ipsm')
      })
      try {
        window.chrome.webview.hostObjects.sync.bridge.GetSwichFloorIsShow(true)
        window.chrome.webview.hostObjects.sync.bridge.ShowDialog(true)
      } catch (error) {}
    },
    closeInspPointDialog() {
    },
    // 时间类型切换
    dataTypeCommand(val) {
      const date = {
        1: [moment().format('YYYY-MM-DD') + ' 00:00:00', moment().format('YYYY-MM-DD') + ' 23:59:59'],
        2: [moment().startOf('week').format('YYYY-MM-DD') + ' 00:00:00', moment().endOf('week').format('YYYY-MM-DD') + ' 23:59:59'],
        3: [moment().startOf('month').format('YYYY-MM-DD') + ' 00:00:00', moment().endOf('month').format('YYYY-MM-DD') + ' 23:59:59'],
        4: [moment().startOf('year').format('YYYY-MM-DD') + ' 00:00:00', moment().endOf('year').format('YYYY-MM-DD') + ' 23:59:59'],
        5: []
      }
      this.statisticalDate = date[val]
      this.statisticalType = val
      if (val != 5) {
        this.changeDate()
      } else {
        this.$refs.datePicker.focus()
        console.log(this.$refs.datePicker)
      }
    },
    // 隐患统计
    getDangerStatisticsData() {
      GetDangerStatistics({placeIds: this.ssmCodes.at(-1)}).then((res) => {
        if (res.data.code == '200') {
          this.dangerStatisticsData = res.data.data
        }
      })
    },
    // 楼层隐患统计
    getHiddenDangerData(id) {
      GetStateStatistics({ placeIds: id }).then((res) => {
        if (res.data.code === '200') {
          this.dangerAnalysisEchart(res.data.data.array)
        }
      })
    },
    // 隐患统计图表
    dangerAnalysisEchart(obj) {
      const legendArr = []
      obj.forEach((i, index) => {
        if (i.name == '待认领') i.color = '#FF2D55'
        if (i.name == '待整改') i.color = '#FF9435'
        if (i.name == '已挂账') i.color = '#FFCA64'
        if (i.name == '已整改') i.color = '#3CC1FF'
        if (i.name == '已完结') i.color = '#B0E3FA'
        legendArr.push({
          type: 'plain',
          orient: 'vertical',
          right: 15,
          itemGap: 12,
          itemWidth: 8,
          itemHeight: 8,
          top: `${100 - (index + 1) * 21}%`,
          data: [
            {
              name: i.name,
              icon: 'rect',
              borderWidth: 0
            }
          ],
          padding: 5,
          backgroundColor: 'rgba(42,54,68, 0.4)',
          formatter: (name) => {
            return `{name|${name}} {value|${obj[index].value}} {bfb|${obj[index].rate}}`
          },
          textStyle: {
            color: '#545659',
            fontSize: 14,
            rich: {
              name: {
                padding: [4, 50, 0, 4],
                color: '#A4ACB9',
                align: 'left',
                backgroundColor: 'transparent',
                width: 40
              },
              value: {
                fontSize: 16,
                color: '#fff',
                padding: [4, 10, 0, 0],
                width: 30
              },
              bfb: {
                fontSize: 16,
                padding: [4, 15, 0, 0],
                align: 'right',
                color: '#fff',
                backgroundColor: 'transparent',
                width: 40
              }
            }
          }
        })
      })
      let getchart = echarts.init(document.getElementById('dangerStatisticsEcharts'))
      getchart.resize()
      const option = {
        tooltip: {
          trigger: 'item'
        },
        color: obj.map((i) => i.color),
        legend: legendArr,
        series: [
          {
            center: ['20%', '50%'],
            type: 'pie',
            roseType: 'radius',
            data: obj,
            label: {
              show: false
            }
          }
        ]
      }
      getchart.clear()
      getchart.setOption(option)
      // 随着屏幕大小调节图表
      window.addEventListener('resize', () => {
        getchart.resize()
      })
    },
    // 楼层巡检点列表
    getFloorTaskList() {
      const params = {
        pageSize: 999,
        pageNo: 1,
        dateType: 'day',
        spaceCode: this.ssmCodes.at(-1),
        spaceLevel: this.roomData.ssmType,
        spaceIdentifying: 0
      }
      if (this.roomData.projectCode) {
        params.spaceLevel = 4
      } else {
        params.spaceLevel = this.roomData.ssmType ? this.roomData.ssmType - 1 : 5
      }
      getTaskPointSpaceList(params).then((res) => {
        console.log(res)
        if (res.data.code == '200') {
          this.dyeOption = res.data.data.list
        }
      })
    },
    // 显色全选切换
    handleCheckAllChange(val) {
      this.dyeCheckList = val ? this.dyeOption.map((i) => i.taskPointName) : []
      this.isIndeterminate = false
    },
    handleCheckedCitiesChange(val) {
      console.log(val)
      let checkedCount = val.length
      this.checkAll = checkedCount === this.dyeOption.length
      this.isIndeterminate = checkedCount > 0 && checkedCount < this.dyeOption.length
    },
    // 隐患排名/部门
    getGetDeptDangerStatisticsData() {
      GetDeptDangerStatistics({
        parentId: this.ssmCodes.at(-1)
      }).then((res) => {
        if (res.data.code == '200') {
          this.setGetDeptDangerStatisticsCharts(res.data.data)
        }
      })
    },
    // 隐患排名/建筑
    getGetSpaceDangerStatisticsData() {
      GetDangerSubordinateStatistics({
        parentId: this.ssmCodes.at(-1)
      }).then((res) => {
        if (res.data.code == '200') {
          this.setGetDeptDangerStatisticsCharts(res.data.data)
        }
      })
    },
    // 隐患排名图表
    setGetDeptDangerStatisticsCharts(data) {
      const getchart = echarts.init(document.getElementById('dangerTop'))
      getchart.resize()
      const option = {
        color: ['#8BDDF5', '#FFCA64'],
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow'
          }
        },
        legend: {
          icon: 'rect',
          itemWidth: 8,
          itemHeight: 8,
          textStyle: {
            color: '#fff',
            fontSize: 14
          }
        },
        grid: {
          top: '15%',
          bottom: 2,
          right: '10%',
          left: 2,
          containLabel: true
        },
        xAxis: {
          show: false
        },
        yAxis: [
          {
            type: 'category',
            data: this.dangerTopType == 'dept' ? data.map((i) => i.deptName) : data.map((i) => i.gridName),
            axisLine: {
              show: false
            },
            axisTick: {
              show: false
            },
            axisLabel: {
              color: '#fff',
              fontSize: 14
            }
          },
          {
            show: true,
            inverse: true,
            data: data.find((i) => {
              if (this.dangerTopType == 'dept') return i.totalCount + '\n' + i.generalCount
            }),
            axisLine: {
              show: false
            },
            splitLine: {
              show: false
            },
            axisTick: {
              show: false
            },
            axisLabel: {
              color: '#fff',
              fontSize: 14
            }
          }
        ],
        series: [
          {
            name: '隐患总数',
            type: 'bar',
            showBackground: true,
            data: this.dangerTopType == 'dept' ? data.map((i) => i.totalCount) : data.map((i) => i.count),
            label: {
              show: true,
              position: 'right',
              valueAnimation: true,
              color: '#fff',
              fontSize: 12
            }
          },
          {
            name: '待整改',
            type: 'bar',
            showBackground: true,
            data: this.dangerTopType == 'dept' ? data.map((i) => i.noRectificationCount) : data.map((i) => i.noRectifySum),
            label: {
              show: true,
              position: 'right',
              valueAnimation: true,
              color: '#fff',
              fontSize: 12
            }
          }
        ],
        dataZoom: [
          {
            yAxisIndex: [0, 1],
            orient: 'vertical',
            show: true,
            type: 'slider',
            startValue: 100,
            // end: 100,
            width: 8,
            left: '99%',
            borderColor: 'rgba(43,48,67,.1)',
            fillerColor: '#6580b8',
            zoomLock: true,
            showDataShadow: false,
            backgroundColor: 'rgba(43,48,67,.1)',
            showDetail: false,
            // realtime: true,
            filterMode: 'filter',
            handleIcon: 'M-9.35,34.56V42m0-40V9.5m-2,0h4a2,2,0,0,1,2,2v21a2,2,0,0,1-2,2h-4a2,2,0,0,1-2-2v-21A2,2,0,0,1-11.35,9.5Z',
            handleStyle: {
              color: '#6580b8',
              borderColor: '#6580b8'
            },
            maxValueSpan: 4,
            minValueSpan: 4,
            brushSelect: false
          },
          {
            type: 'inside',
            show: false,
            yAxisIndex: [0, 1],
            height: 8
          }
        ]
      }
      getchart.clear()
      getchart.setOption(option)
      // 随着屏幕大小调节图表
      window.addEventListener('resize', () => {
        getchart.resize()
      })
    },
    // 切换部门/建筑 隐患
    changeDangerTopType(i) {
      this.dangerTopType = i.id
      if (this.dangerTopType == 'space') {
        this.getGetSpaceDangerStatisticsData()
      } else {
        this.getGetDeptDangerStatisticsData()
      }
    },
    riskLevelTop() {
      this.riskMenuShow = !this.riskMenuShow
    },
    // 切换清单排名
    changerRiskLevelFilter(val) {
      this.checkRiskLevel = [val.name]
      this.riskMenuShow = false
      this.getGetHiddenDangersListData()
    },
    // 隐患清单列表
    getGetHiddenDangersListData() {
      this.tableLoading = true
      GetHiddenDangersList({
        placeIds: this.ssmCodes.at(-1),
        riskCode: this.menuDangerOption.find((i) => i.name == this.checkRiskLevel[0]).id || ''
      }).then((res) => {
        if (res.data.code === '200') {
          this.tableData = res.data.data.list.reverse()
          this.tableData.forEach((i) => {
            if (i.riskCode != '2') {
              i.riskCode = '1'
              i.riskName = '一般隐患'
            }
          })
        }
        this.tableLoading = false
      })
    },
    // 巡查列表
    getTaskListData() {
      const params = {
        pageSize: this.pagination.pageSize,
        pageNo: this.pagination.pageNo,
        dateType: this.dataTypeList.find((v) => v.value == this.statisticalType)?.id,
        spaceLevel: this.roomData.ssmType,
        spaceCode: this.ssmCodes.at(-1)
      }
      if (this.roomData.projectCode) {
        params.spaceLevel = 4
      } else {
        params.spaceLevel = this.roomData.ssmType ? this.roomData.ssmType - 1 : 5
      }
      if (this.statisticalType == 5) {
        params.taskStartTime = this.statisticalDate[0]
        params.taskEndTime = this.statisticalDate[1]
      }
      this.tableTaskLoading = true
      getIpsmTaskListData(params).then((res) => {
        if (res.data.code == '200') {
          if (this.pagination.pageNo == 1) {
            this.taskList = res.data.data.list
          } else {
            this.taskList = this.taskList.concat(res.data.data.list)
          }
          this.pagination.total = res.data.data.sum.allCount
          this.tableTaskLoading = false
        }
      })
    },
    tableLoadEvent() {
      if (this.pagination.total > this.pagination.pageNo * this.pagination.pageSize) {
        this.pagination.pageNo++
        this.getTaskListData()
      }
    },
    changeDate() {
      this.pagination = {
        total: 0,
        pageSize: 20,
        pageNo: 1
      }
      this.getTaskListData()
    },
    showDangerDialog(type) {
      this.allTableComponentListShow = true
      Object.assign(this.tableCompenentData, {
        ...this.roomData,
        ...this.dialogTypeList.find((e) => e.type === type),
        height: 'calc(100% - 150px)'
      })
    },
    configCloseTableDialog() {
      this.allTableComponentListShow = false
    },
    toTaskList(type) {
      this.allTableComponentListShow = true
      Object.assign(this.tableCompenentData, {
        ...this.roomData,
        ...this.dialogTypeList.find((e) => e.type === type),
        height: 'calc(100% - 150px)'
      })
    }
  }
}
</script>
<style lang="scss" scoped>
.dangerManagement {
  height: calc(100% - 50px);
  .moduleContainer {
    .titleBar {
      background-color: rgba($color: #8591ce, $alpha: 0.15);
      height: 32px;
      line-height: 32px;
      padding: 0 8px;
      color: #fff;
      font-size: 15px;
    }
    .titleBgBar {
      height: 44px;
      line-height: 44px;
      background: url('@/assets/images/qhdsys/bg-bt.png') no-repeat center;
      background-size: 100% 44px;
      display: flex;
      justify-content: space-between;
      .titleText {
        margin-left: 38px;
        text-shadow: 0px 0px 9px #158eff;
        font-family: HarmonyOS Sans SC-Bold;
        font-size: 16px;
        font-weight: bold;
      }
      .rightBar {
        display: flex;
        align-items: center;
        font-size: 14px;
        margin-right: 8px;
        position: relative;
        .btnBar {
          display: flex;
          align-items: center;
          .tabItem {
            margin-right: 6px;
            background-color: rgba($color: #8591ce, $alpha: 0.15);
            font-size: 14px;
            color: #b0e3fa;
            height: 26px;
            line-height: 26px;
            width: 52px;
            text-align: center;
            cursor: pointer;
          }
          .selected {
            background: url('@/assets/images/qhdsys/bg-tab2-xz.png') no-repeat center;
            background-size: 52px 26px;
            color: #8bddf5;
          }
        }
        .riskMenuWrap {
          position: absolute;
          display: flex;
          width: 120px;
          left: -75px;
          top: 45px;
          flex-direction: column;
          background-color: #374b79;
          z-index: 9999;
          ::v-deep .el-checkbox-group {
            padding: 5px;
            line-height: 0;
            display: flex;
            flex-direction: column;
            align-items: flex-start;
            .el-checkbox:hover {
              .el-checkbox__label {
                color: #8bddf5 !important;
              }
            }
          }
        }
      }
      .searchData {
        margin-right: 8px;
        display: flex;
        align-items: center;
        position: relative;
      }
    }
    .dangerStatistics {
      padding: 16px 8px;
      height: 52px;
      display: flex;
      .dangerItem {
        width: 25%;
        font-size: 14px;
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        > div:last-child {
          margin-top: 16px;
          font-size: 20px;
          font-weight: bold;
          color: #ffca64;
        }
      }
    }
    .dyeContent {
      padding: 8px 0 0 8px;
      overflow-y: auto;
      .itemMenu {
        height: 14px;
        line-height: 14px;
        padding: 5px;
      }
      ::v-deep .el-checkbox-group {
        .el-checkbox {
          display: flex;
          align-items: center;
          width: calc(100% - 20px);
          margin: 4px 0;
          .el-checkbox__label {
            display: flex;
            align-items: center;
            width: 100%;
          }
          .el-progress {
            width: 80%;
            margin-right: 10px;
          }
          .el-progress-bar__inner {
            border-radius: 0;
          }
          .el-progress-bar__outer {
            border-radius: 0;
            background-color: unset !important;
          }
        }
      }
    }
    .dyeContent::-webkit-scrollbar {
      display: none;
    }
    .bottom-tab {
      .level1 {
        color: #ffca64;
      }
      .level2 {
        color: #ff2d55;
      }
    }
  }
  ::v-deep .el-table {
    border: none !important;
    .el-table__header-wrapper {
      .cell {
        padding-left: 0;
        padding-right: 0;
        text-align: center;
        white-space: nowrap;
      }
    }
    .el-table__body-wrapper {
      td.el-table__cell div {
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
      }
    }
    .el-table__body {
      tr {
        background: center;
      }
      td.el-table__cell,
      th.el-table__cell.is-leaf {
        border-right: 2px solid #0a164e;
        border-bottom: 2px solid #0a164e;
        background: rgba(56, 103, 180, 0.2);
        color: #fff;
      }
      .el-table__row:nth-child(2n - 1) {
        background: rgba(168, 172, 171, 0.08);
      }
      .el-table__row:hover {
        border: 0;
        opacity: 1;
        cursor: pointer;

        td div {
          color: rgba(255, 202, 100, 1);
        }
      }
    }
  }
  ::v-deep .el-date-editor {
    position: absolute;
    left: 100px;
    padding: 0;
    color: #fff;
    background-color: unset !important;
    border: none !important;
    width: 190px !important;
    display: -webkit-inline-box !important;
    .el-icon-date {
      display: none;
    }
    .el-range-input {
      background-color: unset !important;
      color: #fff !important;
    }
    .el-range-separator {
      color: #fff !important;
      padding: 0 8px;
    }
  }
  ::v-deep .el-dropdown-link {
    color: #fff !important;
    cursor: pointer;
  }
}
</style>
