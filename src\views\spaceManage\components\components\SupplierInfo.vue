<template>
  <div>
    <el-row>
      <el-col :span="8">
        <div class="item">
          <span class="item-title">供应商</span>
          <span class="item-content">{{ detailsInfo.supplierName || '--' }}</span>
        </div>
        <div class="item">
          <span class="item-title">生产厂家</span>
          <span class="item-content">{{ detailsInfo.manufacturerName || '--' }}</span>
        </div>
        <div class="item">
          <span class="item-title">售后电话</span>
          <span class="item-content">{{ detailsInfo.contactsPhone || '--' }}</span>
        </div>
      </el-col>
      <el-col :span="8">
        <div class="item">
          <span class="item-title">供应商联系人</span>
          <span class="item-content">{{ detailsInfo.contacts || '--' }}</span>
        </div>
        <div class="item">
          <span class="item-title">品牌</span>
          <span class="item-content">{{ detailsInfo.brandName || '--' }}</span>
        </div>
        <div class="item">
          <span class="item-title">售后工程师</span>
          <span class="item-content">{{ detailsInfo.engineer || '--' }}</span>
        </div>
      </el-col>
      <el-col :span="8">
        <div class="item">
          <span class="item-title">供应商电话</span>
          <span class="item-content">{{ detailsInfo.phone || '--' }}</span>
        </div>
        <div class="item">
          <span class="item-title">产地</span>
          <span class="item-content">{{ detailsInfo.originName || '--' }}</span>
        </div>
        <div class="item">
          <span class="item-title">出厂日期</span>
          <span class="item-content">{{ detailsInfo.deliveryTime || '--' }}</span>
        </div>
      </el-col>
    </el-row>
  </div>
</template>
<script>
export default {
  name: 'SupplierInfo',
  props: ['detailsInfo'],
  data() {
    return {}
  },
  methods: {},
  mounted() {}
}
</script>

<style scoped lang="scss">
  .item {
    margin-bottom: 12px;
    font-size: 14px;
    display: flex;
    height: 20px;
    line-height: 20px;
    .item-title {
      color: #7EAEF9;
      width: 100px;
    }
    .item-content {
      color: #fff;
    }
  }
</style>
