<template>
  <div class="form-detail">
    <div class="reserve-scorll">
      <div v-for="(item, index) in detailsInfo" :key="index" class="reserve-plan">
        <div class="plan-title">
          <!-- <div class="color-box"><i class="el-icon-time"></i></div> -->
          <div class="linear-g">
            <span class="linear-g-span1">{{ item.title }}</span>
            <span>{{ item.createRecordTime }}</span>
            <!-- <i style="display: inline-block" :ref="'itemright' + index" class="el-icon-arrow-right title-icon"></i>
            <i v-show="false" :ref="'itemdown' + index" class="el-icon-arrow-down title-icon"></i> -->
          </div>
        </div>
        <!-- 保养设备 -->
        <div v-if="item.type === 1" class="plan-content plan-content-line">
          <ul class="item-row">
            <li class="width60">
              <span class="li-first-span">保养设备编号: </span><span class="li-last-span">{{ detailData.executeInfo.assetsCode || '' }}</span>
            </li>
            <li class="width60">
              <span class="li-first-span">保养设备名称: </span
              ><span class="li-last-span">{{ type === '008' ? roomData.deviceName ?? '冷冻机组冷冻水泵008' : detailData.executeInfo.assetsName || '' }}</span>
            </li>
            <li class="width30">
              <span class="li-first-span">SN号: </span><span class="li-last-span">{{ detailData.executeInfo.snNumber || '' }}</span>
            </li>
            <li class="width60">
              <span class="li-first-span">使用科室: </span><span class="li-last-span">{{ detailData.executeInfo.useOffice || '' }}</span>
            </li>
          </ul>
        </div>
        <!-- 保养内容 -->
        <div v-if="item.type === 2" class="plan-content plan-content-line">
          <ul class="item-row">
            <el-table :data="detailData.inspectionContent">
              <el-table-column fixed prop="checkingPoints" show-overflow-tooltip label="保养项目"></el-table-column>
              <el-table-column fixed prop="content" show-overflow-tooltip label="保养要点"></el-table-column>
              <!-- <el-table-column fixed prop="details" show-overflow-tooltip label="保养内容"></el-table-column> -->
              <!-- 瑞安演示用，假数据！ -->
              <el-table-column fixed prop="details" show-overflow-tooltip label="保养内容">
                <template slot-scope="scope">
                  <span>{{ type === '008' ? '' : scope.row.details }}</span>
                </template>
              </el-table-column>
            </el-table>
          </ul>
        </div>
        <!-- 保养执行 -->
        <div v-if="item.type === 3" class="plan-content plan-content-line">
          <!-- 瑞安演示 -->
          <!-- <ul class="item-row">
            <li class="width30">
              <span class="li-first-span">保养人: </span><span class="li-last-span">{{detailData.executeInfo.implementPersonName}}</span>
            </li>
            <li class="width30">
              <span class="li-first-span">保养时间: </span><span class="li-last-span">{{detailData.executeInfo.planCreateDate}}</span>
            </li>
          </ul> -->
          <ul class="item-row">
            <li class="width30">
              <span class="li-first-span">保养人: </span><span class="li-last-span">{{ type === '008' ? '' : detailData.executeInfo.implementPersonName }}</span>
            </li>
            <li class="width30">
              <span class="li-first-span">保养时间: </span><span class="li-last-span">{{ type === '008' ? '' : detailData.executeInfo.planCreateDate }}</span>
            </li>
          </ul>
        </div>
        <!-- 保养情况 -->
        <div v-if="item.type === 4" class="plan-content plan-content-line">
          <ul class="item-row">
            <li class="width30">
              <span class="li-first-span">结果: </span><span class="li-last-span">{{ type === '008' ? '' : detailData.situation.state === '0' ? '正常' : '不合格' }}</span>
            </li>
            <li class="width30">
              <span class="li-first-span">定位状态: </span><span class="li-last-span">{{ item.currentAuditPersonName }}</span>
            </li>
          </ul>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { getUpkeeDetail } from '@/utils/equipmentApi'
export default {
  name: 'ukpeeDetail',
  components: {},
  // props: ['maintainId', 'planId', 'type'],
  props: {
    roomData: {
      type: Object,
      default: () => {}
    },
    maintainId: {
      type: String,
      default: ''
    },
    planId: {
      type: String,
      default: ''
    },
    type: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      detailsInfo: [
        {
          title: '保养设备信息',
          type: 1
        },
        {
          title: '保养内容',
          type: 2
        },
        {
          title: '保养执行',
          type: 3
        },
        {
          title: '保养情况',
          type: 4
        }
      ],
      detailData: {}
    }
  },
  created() {
    this.getUpkeeDetail()
  },
  mounted() {},
  methods: {
    // 获取保养详情
    getUpkeeDetail() {
      const params = {
        id: this.planId,
        maintainId: this.maintainId
      }
      getUpkeeDetail(params).then((res) => {
        if (res.data.code === '200') {
          this.detailData = res.data.data
        }
      })
    },
    // 展开关闭事件
    collectEvent(box, i) {
      if (this.$refs[box + 'right' + i][0].style.display === 'inline-block') {
        this.$refs[box + 'right' + i][0].style.display = 'none'
        this.$refs[box + 'down' + i][0].style.display = 'inline-block'
        this.$refs[box + i][0].show = true
      } else {
        this.$refs[box + 'right' + i][0].style.display = 'inline-block'
        this.$refs[box + 'down' + i][0].style.display = 'none'
        this.$refs[box + i][0].show = false
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.content {
  position: relative;
  background-color: #031553;
  box-sizing: border-box;
  width: 100%;
  height: 100%;
}
.title {
  cursor: pointer;
  position: absolute;
  top: 1.6rem;
  left: 0;
  width: 80px;
  height: 30px;
  line-height: 30px;
  text-align: center;
  color: #618ad3;
  font-size: 0.875rem;
  white-space: nowrap;
  background-image: url(../../../../assets/images/peace/btn-back.png);
  background-size: 100% 100%;
  background-repeat: no-repeat;
}
.form-detail {
  width: 100%;
  padding: 10px 25px;
  overflow-y: scroll;
  box-sizing: border-box;
  .reserve-scorll {
    width: 100%;
    overflow-y: auto;
    box-sizing: border-box;
  }
  .reserve-plan {
    width: 100%;
    position: relative;
    // height: inherit;
    .plan-title {
      display: flex;
      width: 100%;
      height: 30px;
      .color-box {
        height: 1.25rem;
        width: 1.25rem;
        color: #e6cf9d;
        line-height: 1.25rem;
        text-align: center;
        border-radius: 3px;
        font-size: 16px;
        margin: auto;
      }
      .linear-g {
        margin-left: 2px;
        width: calc(100% - 22px);
        height: 100%;
        background: #263057;
        font-size: 13px;
        line-height: 30px;
        font-family: PingFangSC-Medium;
        color: #ffe3a6;
        border-radius: 6px;
        cursor: pointer;
        .linear-g-span1 {
          margin: 0 15px;
          font-weight: 600;
        }
        .title-icon {
          float: right;
          line-height: 30px;
          margin-right: 5px;
        }
      }
    }
    .plan-content {
      width: calc(100% - 33px);
      margin-left: 11px;
      color: #b5bacb;
      font-size: 13px;
      .item-row {
        width: 100%;
        display: flex;
        padding: 20px 0px 20px 30px;
        box-sizing: border-box;
        .width30 {
          width: 30%;
          display: flex;
          > .li-last-span {
            width: 80px;
          }
        }
        .width45 {
          width: 45%;
        }
        .width60 {
          width: 60%;
          display: flex;
        }
        .width90 {
          width: 90%;
          display: flex;
        }
        ::v-deep .el-image__error,
        ::v-deep .el-image__placeholder {
          background: center;
        }
        .li-first-span {
          display: inline-block;
          width: 120px;
          // margin-right: 20px;
          font-size: 14px;
          font-family: PingFang-SC-Medium, PingFang-SC;
          font-weight: 500;
          color: #7eaef9;
        }
        .li-last-span {
          display: inline-block;
          width: calc(100% - 120px);
          font-size: 14px;
          font-family: PingFang-SC-Medium, PingFang-SC;
          font-weight: 500;
          color: #ffffff;
          // align-items: center;
        }
        #audio-box {
          display: flex;
        }
        #audio-box > audio {
          width: 260px;
          height: 30px;
        }
        #audio-box > a {
          width: 40px;
          text-align: center;
          background-color: #2cc7c5;
          height: 35px;
          line-height: 35px;
          color: #fff;
          border-radius: 5px;
          margin-left: 10px;
        }
      }
      .show-content {
        width: 100%;
      }
    }
  }
}
</style>
