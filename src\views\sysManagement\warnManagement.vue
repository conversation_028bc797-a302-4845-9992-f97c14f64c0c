<template>
  <!-- 警情配置 -->
  <div class="content special_box">
    <div class="top_content" style="margin-bottom: 20px">
      <el-input
        style="width: 217px; margin-right: 20px"
        placeholder="请输入报警来源"
        v-model="searchDataObj.alarmSourceName"
        maxlength="25"
        @keyup.enter.native="_searchByCondition"
        onkeyup="if(value.length>25)value=value.slice(0,25)"
      ></el-input>
      <el-input
        style="width: 217px; margin-right: 20px"
        placeholder="请输入报警类型"
        v-model="searchDataObj.alarmTypeName"
        maxlength="25"
        @keyup.enter.native="_searchByCondition"
        onkeyup="if(value.length>25)value=value.slice(0,25)"
      ></el-input>
      <el-input
        style="width: 217px; margin-right: 20px"
        placeholder="请输入报警参数"
        v-model="searchDataObj.alarmCodeName"
        maxlength="25"
        @keyup.enter.native="_searchByCondition"
        onkeyup="if(value.length>25)value=value.slice(0,25)"
      ></el-input>
      <el-button class="sino-button-sure" style="margin-left: 30px" @click="_searchByCondition">查询</el-button>
      <el-button class="sino-button-sure" @click="_resetCondition">重置</el-button>
      <el-button class="sino-button-sure" style="float: right" @click="selectRowData({}, 'add')"
        ><div class="img-add-icon"></div>
        新增</el-button
      >
    </div>
    <!-- </div> -->
    <div class="table_list">
      <el-table
        :data="tableData"
        height="calc(100% - 40px)"
        :cell-style="$tools.setCell(3)"
        :header-cell-style="$tools.setHeaderCell(3)"
        style="width: 100%"
        v-loading="tableLoading"
        element-loading-background="rgba(0, 0, 0, 0.2)"
      >
        <el-table-column type="index" width="80" show-overflow-tooltip label="序号"></el-table-column>
        <el-table-column prop="alarmSourceName" show-overflow-tooltip label="报警来源"></el-table-column>
        <el-table-column prop="alarmTypeName" show-overflow-tooltip label="报警类型"></el-table-column>
        <el-table-column prop="alarmCodeName" show-overflow-tooltip label="报警参数"></el-table-column>
        <el-table-column prop="policeLevelName" show-overflow-tooltip label="报警级别"></el-table-column>
        <!-- <el-table-column prop="alarmLinkage" show-overflow-tooltip label="报警联动">
          <template slot-scope="scope">
            <el-switch
              :active-value="0"
              :inactive-value="1"
              active-color="#FFE3A6"
              inactive-color="#5996F9"
              @change="switchHandle(scope.row, 'alarmLinkage')"
              v-model="scope.row.alarmLinkage"
            ></el-switch>
          </template>
        </el-table-column> -->
        <el-table-column prop="takeOver" show-overflow-tooltip label="报警接收">
          <template slot-scope="scope">
            <el-switch
              :active-value="0"
              :inactive-value="1"
              active-color="#FFE3A6"
              inactive-color="#5996F9"
              @change="switchHandle(scope.row, 'takeOver')"
              v-model="scope.row.takeOver"
            ></el-switch>
          </template>
        </el-table-column>
        <el-table-column prop="enabledState" show-overflow-tooltip label="启用状态">
          <template slot-scope="scope">
            <el-switch
              :active-value="0"
              :inactive-value="1"
              active-color="#FFE3A6"
              inactive-color="#5996F9"
              @change="switchHandle(scope.row, 'enabledState')"
              v-model="scope.row.enabledState"
            ></el-switch>
          </template>
        </el-table-column>
        <!-- <el-table-column prop="alarmBulletin" show-overflow-tooltip label="报警弹单">
          <template slot-scope="scope">
            <el-switch
              :active-value="0"
              :inactive-value="1"
              active-color="#FFE3A6"
              inactive-color="#5996F9"
              @change="switchHandle(scope.row, 'alarmBulletin')"
              v-model="scope.row.alarmBulletin"
            ></el-switch>
          </template>
        </el-table-column> -->
        <el-table-column prop="alarmWall" show-overflow-tooltip label="警情上墙">
          <template slot-scope="scope">
            <el-switch :active-value="0" :inactive-value="1" active-color="#FFE3A6" inactive-color="#5996F9" @change="switchHandle(scope.row, 'alarmWall')" v-model="scope.row.alarmWall"></el-switch>
          </template>
        </el-table-column>
        <el-table-column prop="audibleAlarm" show-overflow-tooltip label="声光报警">
          <template slot-scope="scope">
            <el-switch
              :active-value="0"
              :inactive-value="1"
              active-color="#FFE3A6"
              inactive-color="#5996F9"
              @change="switchHandle(scope.row, 'audibleAlarm')"
              v-model="scope.row.audibleAlarm"
            ></el-switch>
          </template>
        </el-table-column>
        <el-table-column prop="ledshow" show-overflow-tooltip label="LED显示">
          <template slot-scope="scope">
            <el-switch :active-value="0" :inactive-value="1" active-color="#FFE3A6" inactive-color="#5996F9" @change="switchHandle(scope.row, 'ledshow')" v-model="scope.row.ledshow"></el-switch>
          </template>
        </el-table-column>
        <el-table-column min-width="130px" label="操作">
          <template slot-scope="scope">
            <div class="operationBtn">
              <span @click="selectRowData(scope.row, 'detail')">详情</span>
              <span @click="selectRowData(scope.row, 'edit')" style="margin: 0 10px">编辑</span>
              <span @click="selectRowData(scope.row, 'del')">删除</span>
            </div>
          </template>
        </el-table-column>
      </el-table>
      <el-pagination
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page="currentPage"
        :page-sizes="[15, 20, 30, 40]"
        :page-size="pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
        class="pagination"
      ></el-pagination>
    </div>
    <!-- 新增警情配置弹框 -->
    <template v-if="addDialogShow">
      <addWarn ref="addWarn" :dialogTitle="dialogTitle" :rowData="rowData" :type="type" :addDialogShow="addDialogShow" @warnSure="warnSure" @closeDialog="closeDialog"></addWarn>
    </template>
  </div>
  <!-- </sinoPanel> -->
</template>
<script type="text/ecmascript-6">
import addWarn from './dialogComponents/addWarn'
import { getWarnPage, delWarn, warnState } from '@/utils/api'
export default {
  name: 'warnManagement',
  components: {
    addWarn
  },
  data() {
    return {
      tableData: [],
      currentPage: 1,
      pageSize: 15,
      total: 0,
      addDialogShow: false,
      tableLoading: false,
      searchDataObj: {
        alarmTypeName: '',
        alarmSourceName: '',
        alarmCodeName: ''
      },
      dialogTitle: '',
      rowData: {},
      type: '',
      dialogType: {
        add: '新建',
        edit: '修改',
        detail: '查看'
      }
    }
  },
  mounted() {
    this.warnTableList()
  },
  methods: {
    // 选中列表行数据
    selectRowData(row, type) {
      if (type === 'del') {
        this.warnDelFn(row.id)
      } else {
        this.addDialogShow = true
        this.rowData = row
        this.type = type
        this.dialogTitle = this.dialogType[type] + '警情配置'
      }
    },
    // 查询
    _searchByCondition() {
      this.currentPage = 1
      this.warnTableList()
    },
    // 重置
    _resetCondition() {
      this.searchDataObj = {
        alarmTypeName: '',
        alarmSourceName: '',
        alarmCodeName: ''
      }
      this.currentPage = 1
      this.pageSize = 15
      this.total = 0
      this.warnTableList()
    },
    // 点击新增警情配置
    addWarnFn() {
      this.addDialogShow = true
    },

    // 状态切换
    switchHandle(row, type) {
      const params = {
        id: row.id,
        linkageType: type,
        status: row[type]
      }
      // console.log(params)
      warnState(params)
        .then((res) => {
          const data = res.data
          this.tableLoading = false
          if (data.code === '200' && data) {
            this.$message({
              type: 'success',
              message: '切换状态成功！'
            })
            this.currentPage = this.currentPage === 1 ? 1 : this.currentPage
          } else {
            this.$message({
              type: 'warning',
              message: data.msg
            })
          }
          this.warnTableList()
        })
        .catch(() => {
          console.log('catch')
          this.warnTableList()
        })
    },
    // 删除警情配置
    warnDelFn(id) {
      this.$confirm('删除后将无法恢复，确认要删除该记录吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        cancelButtonClass: 'sino-button-sure',
        confirmButtonClass: 'sino-button-sure',
        customClass: 'confirm-box-class',
        type: 'warning'
      })
        .then(() => {
          this.tableLoading = true
          delWarn(id).then((res) => {
            const data = res.data
            this.tableLoading = false
            if (data.code === '200') {
              this.$message({
                type: 'success',
                message: '删除成功！'
              })
              this.currentPage = this.currentPage === 1 ? 1 : this.currentPage
              this.warnTableList()
            } else {
              this.$message({
                type: 'warning',
                message: data.msg
              })
            }
          })
        })
        .catch(() => {})
    },
    // 查询警情配置列表
    warnTableList() {
      const params = JSON.parse(JSON.stringify(this.searchDataObj))
      Object.assign(params, {
        page: this.currentPage,
        pageSize: this.pageSize
      })
      this.tableLoading = true
      getWarnPage(params).then((res) => {
        const data = res.data
        this.tableLoading = false
        if (data.code === '200') {
          this.tableData = data.data.records
          this.total = data.data.total
        } else {
          this.$message({
            message: data.msg,
            type: 'warning'
          })
        }
      })
    },
    handleSizeChange(val) {
      this.pageSize = val
      this.warnTableList()
    },
    handleCurrentChange(val) {
      this.currentPage = val
      this.warnTableList()
    },
    closeDialog() {
      this.addDialogShow = false
    },
    warnSure(type) {
      this.addDialogShow = false
      if (type === 'detail') {
        return false
      } else if (type === 'add') {
        this._resetCondition()
      } else {
        this.warnTableList()
      }
    }
  }
}
</script>
<style lang="scss" scoped>
.pagination {
  // bottom: 22px;
  // text-align: right;
}
.content {
  position: relative;
  width: 80%;
  height: 97%;
  background-color: #031553;
  box-sizing: border-box;
  padding: 20px 10px 10px 10px;
  margin: 10px 10px 10px 10%;

  .table_list {
    height: calc(100% - 60px);
    // overflow-y: scroll;
  }
  ::v-deep .el-switch__core:after {
    background-color: #1e3063;
  }
  ::v-deep .el-switch__core {
    width: 2.5rem!important;
  }
}
</style>
