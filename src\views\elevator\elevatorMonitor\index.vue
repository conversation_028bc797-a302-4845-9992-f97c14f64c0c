<!--
 * @Author: hedd
 * @Date: 2023-07-04 14:35:15
 * @LastEditTime: 2024-07-18 10:28:43
 * @FilePath: \ihcrs_client_iframe\src\views\elevator\elevatorMonitor\index.vue
 * @Description:
-->
<template>
  <div class="content">
    <div v-if="NODE_ENV_FLAG">
      <el-button class="sino-button-sure" @click="szzlyyChange('build')">SZZLYYBuild</el-button>
    </div>
    <div class="roomInfoManagement">
      <div ref="collapseWidth" class="right-content">
        <div v-show="collapseFlag" class="bg-title">
          <!-- 表头 -->
          <div v-scrollMove class="bg-tab">
            <div class="tab-div" :class="{ 'is-activeTab': activeTabIndex === 1 }" @click="activeTypeEvent(1)">监测总览</div>
            <div class="tab-div" :class="{ 'is-activeTab': activeTabIndex === 2 }" @click="activeTypeEvent(2)">运行统计</div>
            <div class="tab-div" :class="{ 'is-activeTab': activeTabIndex === 3 }" @click="activeTypeEvent(3)">能耗分析</div>
            <!-- <div class="center-empty"></div> -->
          </div>
        </div>
        <div style="width: 100%; height: 100%; overflow: hidden">
          <div v-show="collapseFlag" class="bg-content room-info-box">
            <div class="sys-box">
              <MonitorOverview v-if="activeTabIndex === 1" :projectCode="projectCode" :roomData="roomData"/>
              <MonitorStatistics v-if="activeTabIndex === 2" :projectCode="projectCode"/>
              <MonitorEnergy v-if="activeTabIndex === 3"  :roomData="roomData"/>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
export default {
  name: 'elevatorMonitor',
  components: {
    MonitorOverview: () => import('../components/monitorOverview/index'),
    MonitorStatistics: () => import('../components/monitorStatistics/index'),
    MonitorEnergy: () => import('../components/monitorEnergy/index')
  },
  data() {
    const NODE_ENV_FLAG = import.meta.env.DEV
    return {
      NODE_ENV_FLAG,
      collapseFlag: true,
      activeTabIndex: 1,
      projectCode: '713e24b03094410499db0b08a2eccbcc',
      roomData: {}
      // roomData:
      //   {
      //     type: 'area',
      //     buildingName: '第一住院楼',
      //     buildingCode: '01004',
      //     ssmType: '4',
      //     modelCode: 'SINOMIS01004',
      //     localtion: 'SINOMIS01004',
      //     ssmCodes: '1724753192383705090,1724753192417259521,1724753193365172225',
      //     floorName: '',
      //     floorCode: '',
      //     menuCode: '',
      //     isSpace: '0',
      //     roomCode: '',
      //     roomName: '',
      //     title: '第一住院楼',
      //     modelCoding: '',
      //     callBackBridge: '',
      //     mathRandom: '',
      //     electricityLevel: '0',
      //     areaData: {
      //       ssmType: '3',
      //       parentId: '1724753193365172225',
      //       ssmName: '第一住院楼',
      //       childList: ['1724753193415503862', '1724753193415503863', '1724753193415503864', '1724753193415503865', '1724753193415503866', '1724753193415503867', '1724753193415503868', '1724753193415503869', '1724753193415503870', '1724753193415503871', '1724753193415503872', '1724753193415503873', '1724753193415503874']
      //     }
      //   }
    }
  },
  computed: {},
  created() {
    // 初始化跳转路由地址及参数 elevatorMonitor?areaData={"ssmType":"1","parentId":"1724753192383705090","ssmName":"主院区","childList":["1724753192815718402","1724753193000267778","1724753193138679809","1724753193365172225","1731219360110956545"]}&time=2024/5/28 14:42:26
    if (this.$route.query.areaData) {
      this.roomData = JSON.parse(this.$route.query.areaData)
    }
    try {
      window.chrome.webview.addEventListener('message', (event) => {
        this.roomData = JSON.parse(JSON.parse(event.data).areaData)
      })
    } catch (errpr) {}
  },
  methods: {
    activeTypeEvent(val) {
      this.activeTabIndex = val
    },
    szzlyyChange(type) {
      let paramsData = {}
      if (type === 'build') {
        paramsData = {
          ssmType: '3',
          parentId: '1724753193365172224',
          ssmName: '第一住院楼',
          childList: ['1724753193415503862', '1724753193415503863', '1724753193415503864', '1724753193415503865', '1724753193415503866', '1724753193415503867', '1724753193415503868', '1724753193415503869', '1724753193415503870', '1724753193415503871', '1724753193415503872', '1724753193415503873', '1724753193415503874']
        }
      }
      // this.getSurveyAssetByProjectCode({ssmCodes: paramsData.ssmCodes}).then(() => {
      Object.assign(this.roomData, {
        ...paramsData
      })
    }
  }
}
</script>
<style lang="scss" scoped>
.content {
  width: 100%;
  height: 100%;
  position: relative;
  padding-top: 4px;
  box-sizing: border-box;
  .roomInfoManagement {
    width: 100%;
    position: absolute;
    top: 0;
    right: 0;
    height: 100%;
    .circle-btn {
      position: absolute;
      top: calc(2%);
      right: 0;
      width: 26px;
      height: 26px;
      cursor: pointer;
      margin: auto 0;
      margin-right: 10px;
      z-index: 2;
    }
    .circle-btn-top {
      background: url('~@/assets/images/center/btn-fold.png') no-repeat;
      background-size: 100% 100%;
    }
    .circle-btn-bottom {
      width: 44px;
      height: 44px;
      background: url('~@/assets/images/center/btn-unfold.png') no-repeat;
      background-size: 100% 100%;
    }
    .right-content {
      width: 24.573%;
      height: 100%;
      margin: 0 0 0 auto;
      background: url('~@/assets/images/qhdsys/bg-mask.png') no-repeat;
      background-size: 100% 100%;
      box-sizing: border-box;
      padding: 0 25px 10px 35px;
      position: relative;
      transition: width 0.3s linear;
      overflow: hidden;
      .bg-title {
        width: 100%;
        padding: 0;
        background: rgba(133, 145, 206, 0.15);
        overflow: hidden;
        color: #dceaff;
        font-family: HarmonyOS Sans SC-Regular, HarmonyOS Sans SC;
        // 不可被选中
        -webkit-user-select: none;
        .bg-tab {
          width: 100%;
          display: flex;
          overflow: hidden;
          box-sizing: border-box;
          justify-content: center;
          .tab-div {
            width: 100%;
            flex: 1;
            height: 40px;
            flex-shrink: 0;
            line-height: 40px;
            text-align: center;
            font-size: 16px;
            color: #a4acb9;
            background: url('@/assets/images/qhdsys/bg-tab.png') no-repeat;
            background-size: 100% 100%;
          }
          .tab-div:hover {
            cursor: pointer;
          }
          .is-activeTab {
            color: #b0e3fa;
            background: url('@/assets/images/qhdsys/bg-tab-xz.png') no-repeat;
            background-size: 100% 100%;
          }
        }
        ::v-deep .el-popover {
          width: fit-content;
          min-width: 0;
          background: #374b79;
        }
        .center-empty {
          flex: 1;
          background: url('@/assets/images/qhdsys/bg-tab.png') no-repeat;
          background-size: 100% 100%;
        }
        .icon-collapse {
          width: 48px;
          height: 40px;
          // flex-shrink: 0;
          display: flex;
          justify-content: center;
          align-items: center;
          background: url('@/assets/images/qhdsys/bg-tab.png') no-repeat;
          &:hover {
            cursor: pointer;
          }
          img {
            margin: auto;
          }
        }
      }
      .bg-content {
        position: relative;
        box-sizing: border-box;
        //   padding: 10px 2px 2px 2px;
        //   width: 100%;
        //   height: calc(100% - 40px);
        width: calc(100% + 0px);
        height: calc(100% - 40px);
        overflow-y: auto;
        overflow-x: hidden;
        /* scroll-view 不显示滚动条 */
      }
      .bg-content::-webkit-scrollbar {
        height: 0;
        width: 0;
      }
      .room-info-box {
        color: #fff;
        display: flex;
        flex-direction: column;
        .box-type {
          width: 100%;
          margin: auto;
          display: flex;
          flex-wrap: wrap;
          justify-content: center;
          span {
            display: inline-block;
            width: fit-content;
            height: 24px;
            padding: 0 5px;
            background-color: #24396d;
            text-align: center;
            line-height: 24px;
            color: #dceaff;
            font-size: 14px;
            font-family: HarmonyOS Sans SC-Regular, HarmonyOS Sans SC;
            cursor: pointer;
            margin: 5px 2px 0 2px;
          }
          .type_active {
            color: #ffe3a6;
            background: url('~@/assets/images/center/border-bg-select.png') no-repeat;
            background-size: 100% 100%;
          }
        }
        .sys-box {
          width: 100%;
          // height: calc(100% - 30px);
          height: 100%;
          // flex: 1;
          .sys-box-content {
            width: 100%;
            height: 100%;
            //   padding: 10px;
            box-sizing: border-box;
          }
          // height: calc(100% - 24px);
        }
      }
    }
  }
  .sino-button-sure {
    z-index: 9999;
  }
}
</style>
