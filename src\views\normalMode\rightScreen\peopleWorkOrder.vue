<template>
  <div class="content">
    <div class="left-content">
      <div class="left-top">
        <div class="bg-title">后勤服务班组</div>
        <div class="bg-content">
          <div class="case-discuss">
            <div class="case-discuss-content" :style="'width: ' + currentCase.length * 13.28 + 'rem'">
              <div v-for="(item, index) in currentCase" :key="index" class="discuss-conent-children">
                <div class="case-img" @click="toStaffDetail(item)">
                  <img src="../../../assets/images/qhdsys/count.png" />
                  <div class="case-num" @click="toStaffDetail(item)">
                    <p>{{ item.num }}<span>&nbsp;人</span></p>
                    <p class="personCount">人数</p>
                  </div>
                  <div class="case-anim-icon"></div>
                </div>
                <div class="case-tit">
                  <p class="case-con">{{ item.team_name }}</p>
                  <p class="case-con" :title="item.phone">{{ item.phone }}</p>
                </div>
              </div>
            </div>
          </div>
          <div class="case-control-l case-control-btn" @click="lastCase"><i class="el-icon-arrow-left"></i></div>
          <div class="case-control-r case-control-btn" @click="nextCase"><i class="el-icon-arrow-right"></i></div>
        </div>
      </div>
      <div class="left-center">
        <div class="bg-title">后勤服务工作综合分析</div>
        <div class="bg-content" style="display: flex">
          <div class="analysis-order"></div>
          <div class="analysis-box">
            <div class="analysis-box-top">
              <div class="day-order" @click="jumpWorkOrder('day')">
                <p>本日工单</p>
                <p>{{ serviceAnalysis.todayNum }}<span>&nbsp;单</span></p>
              </div>
              <div class="month-order" @click="jumpWorkOrder('month')">
                <p>本月工单</p>
                <p>{{ serviceAnalysis.monthNum }}<span>&nbsp;单</span></p>
              </div>
              <div class="die-order" @click="jumpWorkOrder('weekGD')">
                <p style="color: #ff5454">本周挂单</p>
                <p>{{ serviceAnalysis.weekHangNum }}<span>&nbsp;单</span></p>
              </div>
              <div class="day-analysis">
                <div class="day-analysis-left">
                  <p>
                    <span>本日完成</span><span>{{ serviceAnalysis.toDayFinish }}</span>
                  </p>
                  <p>
                    <span>本日未完成</span><span>{{ serviceAnalysis.toDayUnFinish }}</span>
                  </p>
                </div>
                <div class="day-analysis-right">
                  <!-- <el-progress class="circle-box" type="circle" :percentage="(72 / (72 + 28)*100)"></el-progress> -->
                  <circle-box comColor="#FFC884" boxId="1" :percent="serviceAnalysis.completeRate" />
                  <p>完成率</p>
                </div>
              </div>
            </div>
            <div class="analysis-box-bottom">
              <div class="out-time-order">
                <div class="out-time-order-left">
                  <p>{{ serviceAnalysis.countOverTimes }}<span>&nbsp;单</span></p>
                  <p>本日超时工单</p>
                </div>
                <div class="out-time-order-right">
                  <circle-box comColor="#6FFDE2" boxId="2" :percent="serviceAnalysis.countOverTimesRate" />
                  <p>占比</p>
                </div>
              </div>
              <div class="month-average-order">
                <!-- <p>4<span>&nbsp;时</span>&nbsp;&nbsp;15<span>&nbsp;分</span>&nbsp;&nbsp;41<span>&nbsp;秒</span></p> -->
                <p>{{ serviceAnalysis.finishTime }}</p>
                <p>当月平均完工时长</p>
              </div>
              <div class="month-analysis">
                <div class="month-analysis-left">
                  <!-- <p>72<span>&nbsp;%</span></p> -->
                  <p>{{ serviceAnalysis.satisfiedRate }}</p>
                  <p>本月满意度</p>
                </div>
                <div class="month-analysis-right">
                  <!-- <p>92<span>&nbsp;%</span></p> -->
                  <p>{{ serviceAnalysis.callBackRate }}</p>
                  <p>7日回访率</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="left-bottom">
        <div class="bg-title">服务工单动态</div>
        <div class="bg-content">
          <el-table
            :data="workOrderTableData"
            height="100%"
            stripe
            :cell-style="$tools.setCell(3)"
            :header-cell-style="$tools.setHeaderCell(3)"
            style="width: 100%"
            element-loading-background="rgba(0, 0, 0, 0.2)"
          >
            <el-table-column prop="createDate" show-overflow-tooltip label="时间"></el-table-column>
            <el-table-column prop="sourcesDeptName" show-overflow-tooltip label="申报科室">
              <template slot-scope="scope">
                {{ scope.row.sourcesDeptName === 'undefined' ? '' : scope.row.sourcesDeptName }}
              </template>
            </el-table-column>
            <el-table-column prop="createByName" show-overflow-tooltip label="申报人"></el-table-column>
            <el-table-column prop="workTypeName" show-overflow-tooltip label="工单类型"></el-table-column>
            <el-table-column prop="designateDeptName" show-overflow-tooltip label="服务班组"></el-table-column>
            <el-table-column prop="designatePersonName" show-overflow-tooltip label="服务人员"></el-table-column>
            <el-table-column prop="flowType" show-overflow-tooltip label="工单状态"></el-table-column>
            <el-table-column show-overflow-tooltip label="操作">
              <template slot-scope="scope">
                <div class="operationBtn">
                  <span v-if="!['5', '15', '6'].includes(scope.row.flowCode)" style="margin-right: 10px" @click="selectRowData(scope.row, 'cui')">催单</span>
                  <span @click="selectRowData(scope.row, 'detail')">详情</span>
                </div>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>
    </div>
    <div class="right-content">
      <div class="right-top">
        <div class="bg-title">后勤设备状态分析</div>
        <div class="bg-content" style="display: flex">
          <div class="equiue-state-analysis-left">
            <div class="cricle-analysis">
              <div id="analysisEcharts"></div>
            </div>
          </div>
          <div class="equiue-state-analysis-right">
            <div class="analysis-right-top">
              <div class="top-box">
                <img src="@/assets/images/peace/people-right-top-order.png" alt="" />
                <div class="leftBox-bg" @click="toDeciceDetail('')">
                  <span>设备总数</span>
                  <span>{{ maintenance.count }}</span>
                </div>
              </div>
              <div class="bottom-box">
                <div v-for="(item, index) in maintenanceList" :key="index" class="order-box" @click="toDeciceDetail(item.key)">
                  <div class="order-content">
                    <p>{{ item.value }}</p>
                    <p>{{ item.label }}</p>
                  </div>
                </div>
                <div v-for="(item, index) in currentMonthCuringList" :key="`curing${index}`" class="order-box" @click="openTaskList(item.key)">
                  <div class="order-content">
                    <p>{{ item.value }}</p>
                    <p>{{ item.label }}</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="right-center">
        <div class="bg-title">
          <div class="title-left">
            <p class="title-left-text">任务分析</p>
          </div>
          <div class="title-right">
            <div class="toggle">
              <div :class="sortType == 'xjrw' ? 'active-type' : ''" @click="changeSortType('xjrw')">巡检任务</div>
              <div :class="sortType == 'byrw' ? 'active-type' : ''" @click="changeSortType('byrw')">保养任务</div>
            </div>
            <div class="search-data">
              <el-dropdown trigger="click" @command="dataTypeCommand">
                <span class="el-dropdown-link"> {{ dataTypeList.find((v) => v.value == dateType)?.name ?? '' }} <i class="el-icon-caret-bottom"></i> </span>
                <el-dropdown-menu slot="dropdown">
                  <el-dropdown-item v-for="item in dataTypeList" :key="item.value" :command="item.value" :class="{ isBjxl: dateType == item.value }">{{ item.name }}</el-dropdown-item>
                </el-dropdown-menu>
              </el-dropdown>
            </div>
          </div>
        </div>
        <div class="bg-content">
          <div class="order-dynamic">
            <div class="order-dynamic-num">
              <div class="dynamic-num-box">
                <p>{{ taskAnalysisStatistics.allCount ?? 0 }}</p>
                <p>任务总数</p>
              </div>
              <div class="dynamic-num-box">
                <p>{{ taskAnalysisStatistics.notStarted ?? 0 }}</p>
                <p style="color: #6b96df">待执行</p>
              </div>
              <div class="dynamic-num-box">
                <p>{{ taskAnalysisStatistics.hasCountPercentage }}</p>
                <p style="color: #6b96df">完成率</p>
              </div>
              <div class="dynamic-num-box">
                <p>{{ taskAnalysisStatistics?.timeoutCount ?? 0 }}</p>
                <p>超时任务</p>
              </div>
            </div>
          </div>
          <el-table
            id="boxed"
            ref="inspTable"
            :data="todayTaskAnalysis"
            height="calc(100% - 3.3rem)"
            :cell-style="$tools.setCell(3)"
            :header-cell-style="$tools.setHeaderCell(3)"
            style="width: 100%; margin-top: 0.3rem"
            element-loading-background="rgba(0, 0, 0, 0.2)"
            stripe
          >
            <el-table-column prop="taskName" show-overflow-tooltip label="任务名称">
              <template slot-scope="scope">
                <el-link type="primary" @click="handlOperation(scope.row)">{{ scope.row.taskName }}</el-link>
              </template>
            </el-table-column>
            <el-table-column prop="cycleType" show-overflow-tooltip label="周期类型">
              <template slot-scope="scope">
                <span>{{ cycleTypeFn(scope.row.cycleType) }}</span>
              </template>
            </el-table-column>
            <el-table-column prop="taskStartTime" show-overflow-tooltip :label="sortType == 'xjrw' ? '应巡日期' : '应保养日期'"></el-table-column>
            <el-table-column prop="distributionTeamName" show-overflow-tooltip :label="sortType == 'xjrw' ? '巡检部门' : '保养部门'"></el-table-column>
            <el-table-column prop="totalCount" show-overflow-tooltip :label="sortType == 'xjrw' ? '应巡点数' : '应保养点数'"></el-table-column>
            <el-table-column prop="hasCount" show-overflow-tooltip :label="sortType == 'xjrw' ? '实巡点数' : '实保养点数'"></el-table-column>
            <el-table-column
              prop="taskStatus"
              show-overflow-tooltip
              label="状态"
              :formatter="
                (row, column, cellValue, index) => {
                  return cellValue === null ? '' : cellValue === 1 ? '未完成' : '已完成'
                }
              "
            ></el-table-column>
            <!-- <el-table-column show-overflow-tooltip label="操作">
              <template slot-scope="scope">
                <div class="operationBtn">
                  <span @click="selectRowData(scope.row, 'edit')">详情</span>
                </div>
              </template>
            </el-table-column> -->
          </el-table>
        </div>
      </div>
      <div class="right-bottom">
        <div class="bg-title">值班记录</div>
        <div class="bg-content" style="display: flex">
          <div class="duty-record-after">
            <el-timeline>
              <el-timeline-item v-for="(duty, index) in dutyRecord" :key="index" :icon="duty.icon" :color="duty.color">
                <slot name="timestamp">
                  <div class="timestamp-row">
                    <div :style="{ 'font-size': index === 0 ? '1rem' : '0.875rem' }">{{ duty.name }}</div>
                    <div :style="{ 'font-size': index === 0 ? '1rem' : '0.875rem', color: index === 0 ? '#fff' : '#a0a6bc' }">{{ duty.plan }}</div>
                  </div>
                </slot>
              </el-timeline-item>
            </el-timeline>
          </div>
          <div class="duty-record-now">
            <p class="now-time">当前值班时间</p>
            <div class="time-slot">
              <p>
                开始时间 <span>{{ shiftRecord?.createTime }}</span>
              </p>
              <p>
                结束时间 <span>{{ shiftRecord?.endTime }}</span>
              </p>
            </div>
            <div class="duty-btn">
              <div v-if="shiftRecord?.theDayData" class="duty-now-btn duty-now-btn-disable" style="margin-right: 10px">
                <p>已打卡</p>
                <p v-if="countDown">{{ countDown }}</p>
              </div>
              <div v-else class="duty-now-btn" style="margin-right: 10px" @click="dutyEvent">
                <p>值班打卡</p>
              </div>
              <div class="duty-now-btn" @click="shiftsEvent">交接班</div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <template v-if="dialogVisibleSignIn">
      <signInForm ref="signForm" :dialogVisibleSignIn="dialogVisibleSignIn" :currentShift="shiftRecord" @closeDialogSignIn="closeDialogSignIn" @sureSignIn="sureSignIn"></signInForm>
    </template>
    <template v-if="dialogVisibleShifts">
      <shiftsForm ref="shiftsForm" :dialogVisible="dialogVisibleShifts" :shiftsData="shiftRecord.theDayData" @closeDialogShifts="closeDialogShifts" @sureShifts="sureShifts"></shiftsForm>
    </template>
    <!-- 后勤状态分析弹窗 -->
    <template v-if="allTableComponentListShow">
      <allTableComponentList ref="allTableComponentList" :dialogData="tableCompenentData" :dialogShow="allTableComponentListShow" @configCloseDialog="configCloseTableDialog"></allTableComponentList>
    </template>
    <!-- 巡检保养详情 -->
    <inspectionDetail ref="inspectionDetail" :dataInfo="taskkDetailObj" :systemType="sortType"></inspectionDetail>
    <!-- 职工详情 -->
    <template v-if="staffComponentListShow">
      <staffDetail ref="staffDetail" :dialogShow="staffComponentListShow" :dialogData="staffDetailInfo" @configCloseDialog="configCloseStaffTableDialog"></staffDetail>
    </template>

    <DeviceTaskList v-if="taskDialog" v-model="taskDialog" :deviceType="deviceType" :taskType="taskType" :taskDateType="taskDateType"></DeviceTaskList>
  </div>
</template>
<script>
import $ from 'jquery'
import * as echarts from 'echarts'
import circleBox from '@/components/common/circleBox.vue'
import shiftsForm from './components/shiftsForm'
import signInForm from './components/signInForm'
// getMaintenance
import { getComprehensiveWorkOrderInfo, getDynamicWorkOrderList, urgeWorkOder, getDataByTypeTeam, getShiftRecord } from '@/utils/peaceRightScreenApi'
import { getEquipmentProfessionalTypeStatistics, getEquipmentStatusStatistics, getTaskListData } from '@/utils/spaceManage'
import allTableComponentList from '../../spaceManage/components/allTableComponentList.vue'
import inspectionDetail from '../../spaceManage/sysTypeComponent/components/inspectionDetail.vue'
import staffDetail from './components/staffDetail.vue'
import DeviceTaskList from './components/deviceTaskList.vue'
import moment from 'moment'
moment.locale('zh-cn')
export default {
  name: 'peopleWorkOrder',
  components: {
    shiftsForm,
    signInForm,
    circleBox,
    allTableComponentList,
    inspectionDetail,
    staffDetail,
    DeviceTaskList
  },
  data() {
    return {
      selectedCase: 0,
      currentCase: [],
      allTableComponentListShow: false,
      staffComponentListShow: false,
      tableCompenentData: {}, // 一站式弹窗数据
      workOrderTableData: [],
      todayTaskAnalysis: [],
      dutyRecord: [
        {
          name: '上一班次记录',
          plan: '暂无记录',
          icon: 'el-icon-time',
          color: '#06164E'
        },
        {
          name: '遗留问题',
          plan: '暂无记录'
        },
        {
          name: '值班情况',
          plan: '暂无记录'
        }
      ],
      serviceAnalysis: {
        callBackNum: 0,
        callBackRate: '0.0%',
        closedCompleted: 0,
        completeRate: 0,
        countOverTimes: '0',
        countOverTimesRate: 0,
        finishTime: '0秒',
        monthNum: 0,
        satisfiedRate: '0.00%',
        todayNum: 0,
        unCompleteRate: '0.00%',
        weekHangNum: 0,
        toDayFinish: 0,
        toDayUnFinish: 0
      },
      maintenance: {
        count: 0 // 总数
      },
      maintenanceList: [
        {
          key: 'normalAssets', // 正常
          value: 0,
          label: '正常'
        },
        {
          key: 'repairedAssets', // 待维修
          value: 0,
          label: '待维修'
        },
        {
          key: 'retiredAssets', // 闲置
          value: 0,
          label: '闲置'
        },
        {
          key: 'deactivationAsset', // 停用
          value: 0,
          label: '停用'
        },
        {
          key: 'idleAssets', // 报废
          value: 0,
          label: '报废'
        }

      ],
      currentMonthCuringList: [
        {
          key: 'monthMaintenance', // 本月待维保
          value: 0,
          label: '本月待维保'
        },
        {
          key: 'yearMaintenance', // 本年待维保
          value: 0,
          label: '本年待维保'
        },
        {
          key: 'monthInspection', // 本月待巡检
          value: 0,
          label: '本月待巡检'
        }
      ],
      deviceType: '',
      taskType: '',
      taskDateType: 'month',
      taskDialog: false,
      taskAnalysisStatistics: {
        allCount: 0, // 任务总数
        notStarted: 0, // 待执行
        underway: 0, // 进行中
        hasCountPercentage: '0.00%', // 完成率
        timeoutCount: 0 // 超时任务
      },
      currentIndex: -1,
      highlightTimer: null,
      dialogVisibleSignIn: false,
      dialogVisibleShifts: false,
      shiftRecord: {},
      currentTime: '',
      currentTimer: null,
      countDown: 0,
      timer: null,
      taskAnalysisColumns: [
        {
          prop: 'taskName',
          label: '任务名称'
        },
        {
          prop: 'totalCount',
          label: '任务点数'
        },

        {
          prop: 'distributionTeamName',
          label: '执行部门'
        },
        {
          prop: 'taskStatus',
          label: '状态',
          formatter: (row) => {
            return row.cellValue === null ? '' : row.cellValue === 1 ? '未完成' : '已完成'
          }
        },
        {
          prop: 'designatePersonName',
          label: '完成率',
          formatter: (row) => {
            return ((row.hasCount / row.totalCount) * 100).toFixed(2) + '%'
          }
        }
      ],
      sortType: '',
      dataTypeList: [
        { value: 'day', name: '今日' },
        { value: 'week', name: '本周' },
        { value: 'month', name: '本月' }
      ],
      dateType: 'day',
      taskkDetailObj: {}, // 巡检保养数据
      rolltimer: null,
      cycleTypeFn(scope) {
        if (scope === 8) return '单次'
        if (scope === 6) return '每日'
        if (scope === 0) return '每周'
        if (scope === 2) return '每月'
        if (scope === 3) return '季度'
        if (scope === 5) return '全年'
      },
      staffDetailInfo: {
        staffIds: '',
        title: '人员详情'
      }
    }
  },
  mounted() {
    this.sortType = 'xjrw'
    this.search()
    this.getShiftRecord()
    this.timer = setInterval(() => {
      setTimeout(() => {
        this.search()
      }, 0)
    }, 1000 * timerNum)
    // 此处 boxed 是 el-table 绑定的 id
    const box = document.getElementById('boxed')
    // 鼠标滑进暂停
    box.addEventListener('mouseenter', (e) => {
      // 只清除表格相关定时器，不清除图表高亮定时器
      this.clearTableTimers()
      this.autoRoll('stop')
    })
    // 鼠标滑出滚动
    box.addEventListener('mouseleave', (e) => {
      this.timer = setInterval(() => {
        setTimeout(() => {
          this.search()
        }, 0)
      }, 1000 * timerNum)
      this.autoRoll()
    })
    this.$once('hook:beforeDestroy', () => {
      this.clearAllTimers() // 使用新的方法清除所有定时器
    })
  },
  destroyed() {
    this.clearAllTimers() // 使用新的方法清除所有定时器
  },
  methods: {
    // 新增方法，清除所有定时器
    clearAllTimers() {
      if (this.timer) {
        clearInterval(this.timer)
        this.timer = null
      }
      if (this.rolltimer) {
        clearInterval(this.rolltimer)
        this.rolltimer = null
      }
      if (this.highlightTimer) {
        clearInterval(this.highlightTimer)
        this.highlightTimer = null
      }
      if (this.currentTimer) {
        clearInterval(this.currentTimer)
        this.currentTimer = null
      }
    },
    // 新增方法，只清除表格相关的定时器，不清除图表高亮定时器
    clearTableTimers() {
      if (this.timer) {
        clearInterval(this.timer)
        this.timer = null
      }
      if (this.rolltimer) {
        clearInterval(this.rolltimer)
        this.rolltimer = null
      }
    },
    search() {
      const table = this.$refs.inspTable
      const divData = table.bodyWrapper
      divData.scrollTop = 0
      // 只清除表格滚动定时器，不清除图表高亮定时器
      clearInterval(this.rolltimer)
      this.rolltimer = null
      this.getWorkOrderTableData()
      this.GetTodayTaskAnalysis()
      this.getServiceAnalysis()
      this.getRightTop()
      this.getTypeTeam()
    },
    getTypeTeam() {
      getDataByTypeTeam({}).then((res) => {
        if (res.data.code === '200') {
          this.currentCase = res.data.data.list
        }
      })
    },
    // 后勤服务工作综合分析
    getServiceAnalysis() {
      getComprehensiveWorkOrderInfo().then((res) => {
        const data = res.data
        if (data.code === '200') {
          const arr = data.data.resultMap
          arr.completeRate = this.toPoint(arr.completeRate)
          arr.countOverTimesRate = this.toPoint(arr.countOverTimesRate)
          this.serviceAnalysis = data.data.resultMap
        } else {
          // this.$message({
          //   message: data.data.msg,
          //   type: 'warning'
          // })
        }
      })
    },
    // 任务分析
    GetTodayTaskAnalysis() {
      const param = {
        systemCode: this.sortType == 'xjrw' ? '1' : '2',
        // dateType: 'year',
        dateType: this.dateType,
        pageNo: 1,
        pageSize: 999
      }
      getTaskListData(param).then((res) => {
        const data = res.data
        if (data.code === '200') {
          const { list, sum } = data.data
          this.todayTaskAnalysis = list
          if (this.todayTaskAnalysis && this.todayTaskAnalysis.length > 5) {
            this.autoRoll() // 滚动
          }
          this.taskAnalysisStatistics = sum
        } else {
          // this.$message({
          //   message: data.data.msg,
          //   type: 'warning'
          // })
        }
      })
    },
    getWorkOrderTableData() {
      getDynamicWorkOrderList().then((res) => {
        const data = res.data
        if (data.code === '200') {
          this.workOrderTableData = data.data.list
        } else {
          // this.$message({
          //   message: data.data.msg,
          //   type: 'warning'
          // })
        }
      })
    },
    getRightTop() {
      this.getMaintenance()
      this.getAssetsAnalyze()
    },
    // 后勤设备状态分析-设备状态分析
    getAssetsAnalyze() {
      getEquipmentStatusStatistics().then((res) => {
        const data = res.data
        if (data.code === '200') {
          this.maintenanceList.forEach((el) => {
            for (const key in data.data) {
              if (el.key === key) {
                el.value = data.data[key]
              }
            }
          })
          this.currentMonthCuringList.forEach((el) => {
            for (const key in data.data) {
              if (el.key === key) {
                el.value = data.data[key]
              }
            }
          })
          Object.assign(this.maintenance, {
            count: data.data.count
          })
        } else {
          // this.$message({
          //   message: data.message,
          //   type: 'warning'
          // })
        }
      })
    },
    // 后勤设备状态分析-饼图
    getMaintenance() {
      getEquipmentProfessionalTypeStatistics().then((res) => {
        const data = res.data
        if (data.code === '200') {
          this.$nextTick(() => {
            this.getAnalysisEcharts(data.data)
          })
        } else {
          // this.$message({
          //   message: data.message,
          //   type: 'warning'
          // })
        }
      })
    },
    toPoint(str) {
      var data = str.replace('%', '')
      return (data / 100).toFixed(2)
    },
    // 后勤设备状态分析Echarts
    getAnalysisEcharts(valueList) {
      const getchart = echarts.init(document.getElementById('analysisEcharts'))
      getchart.resize()
      var scaleData = valueList
      var xdata = valueList.map((item) => {
        return item.professionalName || ''
      })
      var data = []
      var colorList = ['#E88D6B', '#61E29D', '#5E89EE', '#0A84FF', '#F4D982']
      for (var i = 0; i < scaleData.length; i++) {
        data.push({
          value: scaleData[i].number,
          name: scaleData[i].professionalName,
          itemStyle: {
            normal: {
              borderWidth: 2,
              shadowBlur: 200,
              borderColor: '#0A1732',
              color: colorList[i],
              label: {
                normal: { show: true }
              }
            }
          }
        })
      }
      var seriesObj = [
        {
          name: '',
          type: 'pie',
          clockWise: false,
          center: ['25%', '50%'],
          radius: ['65%', '80%'],
          hoverAnimation: true,
          label: {
            show: false,
            position: 'center',
            formatter: '{c_style|{c}件}\n{b_style|{b}}',
            rich: {
              b_style: {
                fontSize: 14,
                fontWeight: 400,
                color: '#fff'
              },
              c_style: {
                padding: [0, 0, 10, 0],
                fontSize: 20,
                fontWeight: 'bold',
                color: '#fff'
              }
            }
          },
          emphasis: {
            label: {
              show: true,
              fontSize: '14',
              fontWeight: 'normal'
            }
          },
          labelLine: {
            show: false
          },
          data: data
        },
        {
          type: 'pie',
          radius: ['56%', '60%'],
          center: ['25%', '50%'],
          animation: false,
          hoverAnimation: false,
          data: [
            {
              value: 100
            }
          ],
          label: {
            show: false
          },
          itemStyle: {
            normal: {
              color: 'rgba(133,145,206,0.15)'
            }
          }
        },
        // 外边框
        {
          name: '外边框',
          type: 'pie',
          clockWise: false, // 顺时加载
          hoverAnimation: false, // 鼠标移入变大
          center: ['25%', '50%'],
          radius: ['85%', '84%'],
          label: {
            normal: {
              show: false
            }
          },
          data: [
            {
              value: 0,
              name: '',
              itemStyle: {
                normal: {
                  borderWidth: 1,
                  color: 'rgba(133,145,206,0.15)',
                  borderColor: 'rgba(133,145,206,0.15)'
                }
              }
            }
          ]
        },
        {
          name: '',
          type: 'pie',
          startAngle: 90,
          radius: '50%',
          animation: false,
          hoverAnimation: false,
          center: ['25%', '50%'],
          itemStyle: {
            normal: {
              labelLine: {
                show: false
              },
              color: new echarts.graphic.RadialGradient(0.5, 0.5, 1, [
                {
                  offset: 1,
                  color: 'rgba(133,145,206,0.15)'
                },
                {
                  offset: 0,
                  color: 'rgba(133,145,206,0.15)'
                }
              ]),
              shadowBlur: 60
            }
          },
          data: [
            {
              value: 100
            }
          ]
        }
      ]
      const option = {
        backgroundColor: 'rgba(128, 128, 128, 0)',
        legend: {
          selectedMode: false,
          orient: 'vertical',
          type: 'scroll',
          x: 'left',
          top: '6%',
          left: '50%',
          bottom: '0%',
          data: xdata,
          itemWidth: 8,
          itemHeight: 8,
          itemGap: 13,
          pageTextStyle: {
            color: '#fff'
          },
          textStyle: {
            //  fontSize: 18,//字体大小
            color: '#B3C2DD' //  字体颜色
          },
          formatter: function (name) {
            var oa = option.series[0].data
            var num = oa.reduce((sum, e) => sum + e.value, 0)
            for (var i = 0; i < option.series[0].data.length; i++) {
              if (name === oa[i].name) {
                return ' ' + oa[i].name + ' (' + oa[i].value + '件)     ' + ((oa[i].value / num) * 100).toFixed(2) + '%'
              }
            }
          },
          tooltip: {
            show: false
          }
        },
        series: seriesObj
      }
      getchart.clear()
      getchart.setOption(option)
      // 随着屏幕大小调节图表
      window.addEventListener('resize', () => {
        getchart.resize()
      })
      
      // 清除已有的高亮定时器
      if (this.highlightTimer) {
        clearInterval(this.highlightTimer)
        this.highlightTimer = null
      }
      
      this.currentIndex = -1
      this.highlightTimer = setInterval(() => {
        this.startTooltipLoop(getchart, valueList.length)
      }, 1000)
      // 鼠标移入暂停
      getchart.on('mouseover', (params) => {
        getchart.dispatchAction({
          type: 'downplay',
          seriesIndex: 0,
          dataIndex: this.currentIndex
        })
        clearInterval(this.highlightTimer)
        this.highlightTimer = null
      })
      // 鼠标移出继续
      getchart.on('mouseout', (params) => {
        if (this.highlightTimer) return
        this.highlightTimer = setInterval(() => {
          this.startTooltipLoop(getchart, valueList.length)
        }, 1000)
      })
      // 先移除点击事件 解决点击事件重复绑定
      getchart.off('click')
      // 图例点击事件
      getchart.on('click', (params) => {
        const name = params.name
        getchart.setOption({
          legend: { selected: { [params.name]: true } } // 取消点击图例置灰
        })
        const professionalCode = scaleData.find((item) => item.professionalName === name).professionalCode
        this.allTableComponentListShow = true
        this.tableCompenentData = {}
        Object.assign(this.tableCompenentData, {
          title: '设备台账列表',
          ...params,
          professionalCode,
          type: 'inspAsset',
          height: 'calc(100% - 120px)'
        })
      })
    },
    startTooltipLoop(getchart, length) {
      // 取消之前高亮的图形
      getchart.dispatchAction({
        type: 'downplay',
        seriesIndex: 0,
        dataIndex: this.currentIndex
      })
      this.currentIndex = (this.currentIndex + 1) % length
      // 高亮当前图形
      getchart.dispatchAction({
        type: 'highlight',
        seriesIndex: 0,
        dataIndex: this.currentIndex
      })
      // 显示tooltip
      // getchart.dispatchAction({
      //   type: 'showTip',
      //   seriesIndex: 0,
      //   dataIndex: this.currentIndex
      // })
    },
    caseScroll() {
      // 上下页操作
      if (this.selectedCase < 0 || this.currentCase.length <= 4) {
        this.selectedCase = 0
        $('.case-discuss-content:visible').animate({ left: -this.selectedCase * 13.28 + 'rem' }, 1000)
        return
      }
      if (this.selectedCase > this.currentCase.length - 5) {
        this.selectedCase = this.currentCase.length - 5
      }
      $('.case-discuss-content:visible').animate({ left: -this.selectedCase * 13.28 + 'rem' }, 1000)
    },
    nextCase() {
      // 下一页
      this.selectedCase++
      this.caseScroll()
    },
    lastCase() {
      // 上一页
      this.selectedCase--
      this.caseScroll()
    },
    jumpWorkOrder(type) {
      this.$router.push({ name: 'workOrderList', params: { type: type } })
    },
    // table 操作
    selectRowData(row, type) {
      if (type === 'detail') {
        this.$router.push({ path: '/workOrderDetail', query: { id: row.id } })
      } else if (type === 'cui') {
        this.$confirm('是否催促尽快完成此工单?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          cancelButtonClass: 'new-edition',
          confirmButtonClass: 'new-edition',
          customClass: 'confirm-box-class',
          type: 'warning'
        })
          .then(() => {
            // this.changeUserState(params)
            urgeWorkOder({ id: row.id }).then((res) => {
              if (res.data.code === '200') {
                this.$message({
                  type: 'success',
                  message: '催单成功！'
                })
                this.getWorkOrderTableData()
              } else {
                // this.$message({
                //   message: res.data.message,
                //   type: 'warning'
                // })
              }
            })
          })
          .catch(() => {})
      } else if (type === 'edit') {
        this.$router.push({ path: '/patrolInspectionDetails', query: { id: row.id, dataInfo: row } })
      }
    },
    // 巡检详情
    handlOperation(row) {
      this.taskkDetailObj = row
      this.$refs.inspectionDetail.hiddenDangerDetailsListShow = true
      this.$refs.inspectionDetail.GetTaskPointReleaseList(row.id, 'insp')
    },
    // 初始化获取班次信息
    getShiftRecord() {
      const that = this
      // that.shiftRecord.endDate = '2022-06-22 16:47:00'
      getShiftRecord({}).then((res) => {
        const data = res.data
        if (data.code === '200') {
          this.shiftRecord = data.data[0]
          const lastDayData = data.data[0].lastDayData
          // 获取当前时间
          if (data.data[0].date) {
            that.currentTime = moment(data.data[0].date).format('YYYY-MM-DD HH:mm:ss')
            // 定时器
            this.currentTimer = setInterval(() => {
              const timeFormat = 'YYYY-MM-DD hh:mm:ss'
              that.currentTime = moment(that.currentTime).add(1, 'second').format('YYYY-MM-DD HH:mm:ss')
              const isAvailable = moment(that.currentTime, timeFormat).isBetween(moment(that.shiftRecord.createDate + ':00', timeFormat), moment(this.shiftRecord.endDate + ':00', timeFormat))
              // const isTimeEvel = moment(that.currentTime, timeFormat).isBetween(moment(this.shiftRecord.endDate + ':00', timeFormat), moment(this.shiftRecord.endDate + ':00', timeFormat).add(1, 'day'))
              // 判断当前时间是否在班次时间内 如果不在班次时间内 则清除定时器 并且更新班次信息
              if (!isAvailable) {
                clearInterval(that.currentTimer)
                that.currentTimer = null
                that.getShiftRecord()
              }
              // const timeDiff = moment('2022-06-22 16:47:50').diff(moment(that.currentTime), 'seconds')
              const timeDiff = moment(that.shiftRecord.endDate + ':00').diff(moment(that.currentTime), 'seconds')
              if (timeDiff <= 600 && timeDiff > 0) {
                that.countDown = moment.utc(timeDiff * 1000).format('mm:ss')
              } else {
                that.countDown = 0
              }
            }, 1000)
          }
          // 获取上个班次
          if (lastDayData) {
            this.dutyRecord[0].plan = moment(lastDayData.signInTime).format('YYYY-MM-DD HH:mm:ss') || '暂无记录'
            this.dutyRecord[1].plan = lastDayData.remainingProblems || '暂无记录'
            this.dutyRecord[2].plan = lastDayData.dutySituation || '暂无记录'
          }
        } else {
          // this.$message({
          //   message: data.message,
          //   type: 'warning'
          // })
        }
      })
    },
    // 值班打卡
    dutyEvent() {
      this.dialogVisibleSignIn = true
    },
    // ----------------------------签到弹窗操作
    closeDialogSignIn() {
      this.dialogVisibleSignIn = false
    },
    sureSignIn() {
      this.dialogVisibleSignIn = false
      this.getShiftRecord()
    },
    // 交接班
    shiftsEvent() {
      if (!this.shiftRecord?.theDayData) {
        return this.$message({
          message: '请值班打卡后再进行交接班！',
          type: 'warning'
        })
      }
      this.dialogVisibleShifts = true
    },
    sureShifts() {
      this.dialogVisibleShifts = false
      this.getShiftRecord()
    },
    closeDialogShifts() {
      this.dialogVisibleShifts = false
    },
    // 关闭弹窗
    configCloseTableDialog() {
      this.allTableComponentListShow = false
    },
    changeSortType(sortType) {
      this.sortType = sortType
      const table = this.$refs.inspTable
      if (table && table.bodyWrapper) {
        const divData = table.bodyWrapper
        divData.scrollTop = 0
      }
      // 切换类型时先停止滚动
      if (this.rolltimer) {
        clearInterval(this.rolltimer)
        this.rolltimer = null
      }
      this.GetTodayTaskAnalysis()
    },
    // 时间类型切换
    dataTypeCommand(val) {
      this.dateType = val
      const table = this.$refs.inspTable
      if (table && table.bodyWrapper) {
        const divData = table.bodyWrapper
        divData.scrollTop = 0
      }
      // 切换时间类型时先停止滚动
      if (this.rolltimer) {
        clearInterval(this.rolltimer)
        this.rolltimer = null
      }
      this.GetTodayTaskAnalysis()
    },
    // 巡检保养弹窗
    configInspCloseTableDialog() {
      this.inspDetailsListShow = false
    },
    // 实现滚动核心方法
    // 前置条件 1.table有height 2.show-header不能设置为false 值为false时不能正确活取到scrollHeight
    autoRoll(stop) {
      // 先清除表格滚动定时器
      if (this.rolltimer) {
        clearInterval(this.rolltimer)
        this.rolltimer = null
      }
      
      if (stop) {
        return // 直接返回，不再创建新定时器
      }
      
      // 这里的 tab 是上方 table 表格绑定的ref值
      const table = this.$refs.inspTable
      if (!table || !table.bodyWrapper) return // 防止表格未加载完成
      
      const divData = table.bodyWrapper
      this.rolltimer = setInterval(() => {
        // + 4 是每秒向下滑动 4个像素  这块可以自己更改
        divData.scrollTop += 2
        // 下方判断是滑动到底部就会自己回到最上方重新开始滑动  改动像素的话 一定要满足进入这个判断  否则滚动到底部就停了
        if (divData.clientHeight + divData.scrollTop + 1 >= divData.scrollHeight) {
          divData.scrollTop = 0
        }
      }, 100)
    },
    // 打开人员详情弹窗
    toStaffDetail(val) {
      this.staffDetailInfo.staffIds = val.id
      this.staffComponentListShow = true
    },
    // 关闭人员详情弹窗
    configCloseStaffTableDialog() {
      this.staffComponentListShow = false
    },
    // 设备弹窗
    toDeciceDetail(val) {
      this.allTableComponentListShow = true
      this.tableCompenentData = {}
      Object.assign(this.tableCompenentData, {
        title: '设备台账列表',
        assetStatusCode: val,
        type: 'inspAsset',
        height: 'calc(100% - 120px)'
      })
    },
    // 设备任务弹框
    openTaskList(key) {
      this.taskDialog = true
      this.deviceType = key
      // 1 巡检 2 保养
      this.taskType = key === 'monthInspection' ? '1' : '2'
      this.taskDateType = key === 'yearMaintenance' ? 'year' : 'month'
    }
  }
}
</script>
<style lang="scss" scoped>
.content {
  position: relative;
  // background-color: #031553;
  background: center;
  // background: url('~@/assets/images/qhdsys/bj.png') no-repeat;

  box-sizing: border-box;
  width: 100%;
  height: 100%;
  padding: 20px 10px 10px 10px;
  display: flex;
  justify-content: space-between;
  .left-content,
  .right-content {
    width: 50%;
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    flex: 1;
  }
  .left-content {
    padding-right: 5px;
    .left-top {
      width: 100%;
      height: calc(24% - 5px);
      background: url('~@/assets/images/qhdsys/bg-24.png') no-repeat;
      background-size: 100% 100%;
      .case-discuss {
        overflow: hidden;
        position: relative;
        width: calc(100% - 2.875rem);
        height: 100%;
        margin: 0 auto;
      }

      .case-discuss-content {
        position: absolute;
        top: 0;
        left: 0;
        display: flex;
        height: 100%;
      }

      .discuss-conent-children {
        display: flex;
        box-sizing: border-box;
        width: 13.28rem;
        margin: 0 8px;
        padding: 14px 0;
        height: 100%;
      }
      .case-img {
        width: 35%;
        height: 90%;
        margin-top: 5%;
        position: relative;
        img {
          width: 100%;
          height: 100%;
        }
        .case-num {
          position: absolute;
          width: 4rem;
          text-align: center;
          top: 30%;
          color: #fff;
          font-size: 1.25rem;
          font-weight: bold;
          cursor: pointer;
          span {
            font-weight: 400;
            font-size: 0.75rem;
          }
          .personCount {
            font-size: 0.75rem;
            margin-top: 0.4rem;
            color: #cfe1f7;
          }
        }
        .case-num:hover {
          color: #eed6a0 !important;
        }
        // .case-anim-icon {
        //   position: absolute;
        //   bottom: 10%;
        //   left: calc(50% - 1rem);
        //   width: 2rem;
        //   height: 2rem;
        //   background: url('~@/assets/images/peace/icon-people.png') no-repeat;
        //   background-size: 100% 100%;
        //   animation: jump 1s ease-out infinite alternate-reverse;
        // }
      }
      .case-tit {
        width: 65%;
        padding-left: 0.8rem;
        box-sizing: border-box;
      }
      .case-tit > p:nth-child(1) {
        margin-top: 53%;
        margin-bottom: 12px;
        font-size: 0.875rem;
        color: #7eaef9;
      }
      .case-tit > p:nth-child(2) {
        font-size: 1rem;
        color: #fff;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
        font-family: DIN;
      }
      .case-control-btn {
        position: absolute;
        width: 1.25rem;
        height: 50px;
        line-height: 50px;
        text-align: center;
        top: 45%;
        font-size: 24px;
        color: #5996f9;
        margin-top: (calc(-1 * (150px - 100px) / 2));
        cursor: pointer;
        background-color: #163274;
        color: #5996f9;
        i {
          margin: auto;
        }
        &.case-control-l {
          left: 5px;
          // background: url(~@/assets/images/peace/btn-left.png) no-repeat;
          // background-size: 100% 100%;
        }

        &.case-control-r {
          right: 5px;
          // background: url(~@/assets/images/peace/btn-right.png) no-repeat;
          // background-size: 100% 100%;
        }
      }
      .case-control-btn:hover {
        // color: #ffe3a6;
        background-color: #373e5f;
        color: #eed6a0;
      }
    }
    .left-center {
      width: 100%;
      height: calc(32% - 5px);
      background: url('~@/assets/images/qhdsys/bg-32.png') no-repeat;
      background-size: 100% 100%;
      .analysis-order {
        width: 7rem;
        height: 7rem;
        margin: auto 0.5rem;
        background: url('~@/assets/images/peace/people-left-center-order.png') no-repeat;
        background-size: 100% 100%;
      }
      .analysis-box {
        width: calc(100% - 8rem);
        height: 100%;
        padding: 0 1rem;
        box-sizing: border-box;
        display: flex;
        flex-direction: column;
        justify-content: space-evenly;
        .analysis-box-top,
        .analysis-box-bottom {
          height: calc(40% - 5px);
          display: flex;
          justify-content: space-between;
        }
        .day-order,
        .month-order,
        .die-order {
          width: 20.5%;
          height: 100%;
          display: flex;
          flex-direction: column;
          justify-content: space-evenly;
          cursor: pointer;
          :first-child {
            font-size: 0.875rem;
            color: #7eaef9;
            text-align: center;
          }
          :last-child {
            font-size: 1.25rem;
            color: #fff;
            text-align: center;
            span {
              font-size: 0.75rem;
            }
          }
        }
        .day-analysis,
        .out-time-order,
        .month-average-order,
        .month-analysis {
          width: 32%;
          height: 100%;
        }
        .day-order,
        .month-order,
        .die-order,
        .day-analysis,
        .out-time-order,
        .month-average-order,
        .month-analysis {
          background: url('~@/assets/images/peace/people-left-center.png') no-repeat;
          background-size: 100% 100%;
          padding: 0.3125rem;
          box-sizing: border-box;
        }
        .day-analysis,
        .out-time-order {
          display: flex;
          justify-content: space-around;
        }
        .day-analysis-left {
          width: 60%;
          display: flex;
          flex-direction: column;
          justify-content: space-evenly;
          padding: 0 0.5rem 0 1.2rem;
          box-sizing: border-box;
          p {
            display: flex;
            justify-content: space-between;
            font-size: 0.875rem;
            :first-child {
              color: #7eaef9;
              text-align: center;
            }
            :last-child {
              color: #fff;
              text-align: center;
              font-weight: bold;
            }
          }
        }
        .day-analysis-right,
        .out-time-order-right {
          width: 40%;
          display: flex;
          justify-content: space-around;
          flex-direction: column;
          p {
            text-align: center;
            color: #fff;
            font-size: 0.75rem;
          }
        }
        .out-time-order-left {
          width: 60%;
          display: flex;
          flex-direction: column;
          justify-content: space-evenly;
          padding: 0 0.5rem 0 1.2rem;
          box-sizing: border-box;
          p {
            text-align: center;
            font-size: 0.875rem;
          }
          :first-child {
            color: #fff;
            font-size: 1.25rem;
            span {
              font-size: 0.75rem;
              color: #fff;
            }
          }
          :last-child {
            color: #7eaef9;
          }
        }
        .month-average-order {
          display: flex;
          flex-direction: column;
          justify-content: space-evenly;
          padding: 0 0.5rem 0 1.2rem;
          box-sizing: border-box;
          p {
            text-align: center;
            font-size: 0.875rem;
          }
          :first-child {
            color: #fff;
            font-size: 1.25rem;
            span {
              font-size: 0.75rem;
              color: #fff;
            }
          }
          :last-child {
            color: #7eaef9;
          }
        }
        .month-analysis {
          display: flex;
          justify-content: space-around;
          // flex-direction: column;
          // justify-content: space-evenly;
          // padding: 0 0.5rem 0 1.2rem;
          // box-sizing: border-box;
          .month-analysis-left,
          .month-analysis-right {
            display: flex;
            flex-direction: column;
            justify-content: space-evenly;
            padding: 0 0.5rem 0 1.2rem;
            box-sizing: border-box;
            p {
              text-align: center;
              font-size: 0.875rem;
            }
            :first-child {
              color: #fff;
              font-size: 1.25rem;
              span {
                font-size: 0.75rem;
                color: #fff;
              }
            }
            :last-child {
              color: #7eaef9;
            }
          }
        }
      }
    }
    .left-bottom {
      width: 100%;
      height: calc(43% - 5px);
      background: url('~@/assets/images/qhdsys/bg-43.png') no-repeat;
      background-size: 100% 100%;
    }
  }
  .right-content {
    padding-left: 5px;
    .right-top {
      width: 100%;
      height: calc(30% - 5px);
      background: url('~@/assets/images/qhdsys/bg-32.png') no-repeat;
      background-size: 100% 100%;
      .equiue-state-analysis-left {
        width: 50%;
        height: 100%;
        display: flex;
        .cricle-analysis {
          width: 100%;
          height: 100%;
          margin: auto;
          #analysisEcharts {
            width: 100%;
            height: 100%;
          }
        }
      }
      .equiue-state-analysis-right {
        width: 50%;
        height: 100%;
        flex-direction: column;

        .analysis-right-top {
          height: 100%;
          width: 100%;
          .top-box {
            display: flex;
            cursor: pointer;
            .leftBox-bg {
              width: 350px;
              height: 58px;
              background: linear-gradient(90deg, #1a2e59 0%, rgba(103, 150, 255, 0) 100%);
              border-radius: 0px 0px 0px 0px;
              line-height: 58px;
              color: #ffffff;
              span:nth-child(1) {
                font-size: 14px;
                font-family: HarmonyOS Sans SC, HarmonyOS Sans SC;
                font-weight: 500;
                margin: 0 16px;
              }
              span:nth-child(2) {
                font-size: 20px;
                vertical-align: bottom;
                font-family: DIN, DIN;
                font-weight: bold;
              }
              span:nth-child(2):hover {
                color: #eed6a0 !important;
              }
            }
          }
          .bottom-box {
            margin-top: 24px;
            flex-wrap: wrap;
            cursor: pointer;
            width: 100%;
            display: flex;
            .order-box {
              width: calc(25%);
              text-align: left;
              padding-bottom: 16px;
              // height: 100%;
              // padding: 5%;
              //box-sizing: border-box;
              // display: flex;
              .order-content {
                :first-child {
                  font-size: 1.25rem;
                  color: #fff;
                  text-align: left;
                }
                :first-child:hover {
                  color: #eed6a0 !important;
                }
                :last-child {
                  margin-top: 6px;
                  font-size: 0.875rem;
                  color: #7eaef9;
                  text-align: left;
                }
              }
            }
          }
        }
      }
    }
    .right-center {
      width: 100%;
      height: calc(38% - 5px);
      background: url('~@/assets/images/qhdsys/bg-38.png') no-repeat;
      background-size: 100% 100%;
      .bg-title {
        display: flex;
        justify-content: space-between;
        align-items: center;
      }

      .title-detail {
        cursor: pointer;
        font-size: 14px;
        font-family: HarmonyOS Sans SC-Regular, HarmonyOS Sans SC;
        font-weight: 400;
        color: #8bddf5 !important;
        margin-right: 13px;
      }
      .toggle {
        display: flex;
        cursor: pointer;
        font-size: 14px;
        margin-right: 16px;
      }
      .toggle > div {
        height: 1.7rem;
        line-height: 1.7rem;

        padding: 0 10px;
        color: #8bddf5 !important;
        text-align: center;
        background-color: #213251;
        box-sizing: border-box;
        border: 1px solid #213251;
        opacity: 1;
      }

      .title-right {
        display: flex;
        justify-content: space-between;
      }
      .active-type {
        background: rgba(255, 255, 255, 0.1) linear-gradient(90deg, rgba(10, 132, 255, 0.4) 0%, rgba(10, 132, 255, 0) 100%);
        border: 1px solid #abf0ff !important;
      }
      .search-data {
        display: flex;
        height: 1.7rem;
        line-height: 1.7rem;
        // background: rgba(133, 145, 206, 0.15);
        padding: 0px 10px;
        ::v-deep .el-dropdown {
          .el-dropdown-link {
            font-size: 14px;
            font-weight: 500;
            color: #8bddf5;
            line-height: 16px;
            position: relative;
            cursor: pointer;
          }
          .el-dropdown-link::after {
            content: '';
            position: absolute;
            width: 1px;
            height: 12px;
            background: rgba(133, 145, 206, 0.5);
            top: 50%;
            right: -6px;
            transform: translateY(-50%);
          }
        }
      }
      .order-dynamic {
        height: 3rem;
        width: 70%;
        margin-left: 2%;
        background: url('~@/assets/images/peace/people-right-center-dynamic.png') no-repeat;
        background-size: 100% 100%;
        box-sizing: border-box;
        padding-left: 8%;
        .order-dynamic-num {
          width: 80%;
          height: 100%;
          display: flex;
          justify-content: space-around;
          color: #fff;
          .dynamic-num-box:first-child {
            color: #fee2a6;
          }
          .dynamic-num-box:last-child {
            color: #f95354;
          }
          .dynamic-num-box {
            display: flex;
            flex-direction: column;
            justify-content: space-around;
            text-align: center;
            :first-child {
              font-size: 1.1875rem;
            }
            :last-child {
              font-size: 0.8125rem;
            }
          }
        }
      }
    }
    .right-bottom {
      width: 100%;
      height: calc(32% - 5px);
      background: url('~@/assets/images/qhdsys/bg-32.png') no-repeat;
      background-size: 100% 100%;
      .duty-record-after {
        width: 60%;
        height: 100%;
        padding: 20px 10px 0 10px;
        box-sizing: border-box;
        overflow-y: scroll;
        ::v-deep .el-timeline-item {
          padding-bottom: 0.5rem;
        }
        ::v-deep .el-timeline-item__tail {
          border-left: 0.0625rem solid #3d425a;
        }
        ::v-deep .el-timeline-item__node--normal {
          left: -0.015rem;
          width: 0.5rem;
          height: 0.5rem;
        }
        ::v-deep .el-timeline-item__icon {
          color: #e6cf9d;
          font-size: 1rem;
        }
        ::v-deep .el-timeline-item__node {
          background: #e6cf9d;
        }
        .timestamp-row {
          padding: 2px;
          display: flex;
          font-family: PingFang SC;
          :first-child {
            color: #d0bd95;
            font-size: 0.875rem;
            margin-right: 1.25rem;
            white-space: nowrap;
          }
          :last-child {
            color: #a0a6bc;
            font-size: 0.875rem;
          }
        }
      }
      .duty-record-now {
        width: 40%;
        height: 100%;
        padding: 10px 10px 20px 3rem;
        box-sizing: border-box;
        text-align: left;
        display: flex;
        justify-content: space-around;
        flex-direction: column;
        .now-time {
          font-size: 0.875rem;
          color: #e8d09e;
        }
        .time-slot {
          height: 4rem;
          display: flex;
          justify-content: space-around;
          flex-direction: column;
          font-size: 0.875rem;
          color: #b6bbcb;
          padding-left: 2rem;
          position: relative;
          &::after {
            content: '';
            position: absolute;
            width: 1.5rem;
            height: 4rem;
            top: 0;
            left: -0.2rem;
            background: url('~@/assets/images/peace/duty-time-solt.png') no-repeat;
            background-size: 100% 100%;
          }
          span {
            padding-left: 0.625rem;
            font-size: 1rem;
            font-family: PingFang SC;
            font-weight: 500;
            color: #ffffff;
          }
        }
        .duty-btn {
          display: flex;
        }
        .duty-now-btn {
          width: 7rem;
          height: 4rem;
          text-align: center;
          // align-items: center;
          line-height: 2.65rem;
          padding: 0.625rem 0;
          box-sizing: border-box;
          color: #fff;
          cursor: pointer;
          display: flex;
          flex-direction: column;
          justify-content: center;
          background: url('~@/assets/images/peace/duty-btn.png') no-repeat;
          background-size: 100% 100%;
          &:hover,
          &:focus {
            color: #dbc69a;
            background: url('~@/assets/images/peace/duty-btn-hover.png') no-repeat;
            background-size: 100% 100%;
          }
          p {
            height: 1.375rem;
            line-height: 1.375rem;
          }
        }
        .duty-now-btn-disable {
          cursor: no-drop;
          &:hover,
          &:focus {
            color: #fff;
            background: url('~@/assets/images/peace/duty-btn.png') no-repeat;
            background-size: 100% 100%;
          }
        }
      }
    }
  }
  .bg-title {
    // margin-top: 5px;
    height: 2.5rem;
    line-height: 2.5rem;
    color: #d4e3f9;
    padding-left: 3rem;
    font-family: TRENDS;
  }
  .bg-content {
    position: relative;
    box-sizing: border-box;
    padding: 10px 10px 15px 10px;
    width: 100%;
    height: calc(100% - 2.5rem);
  }
  ::v-deep div.el-table__fixed-body-wrapper {
    background: center;
  }
  ::v-deep .el-table {
    border: none !important;
  }
  // ::v-deep .el-table--enable-row-hover .el-table__body tr:hover {
  //   box-sizing: border-box;
  //   td.el-table__cell {
  //     background-color: #343c62 !important;
  //     border-top: 1px solid #ffe3a6;
  //     border-bottom: 1px solid #ffe3a6;
  //   }
  // }
}
::v-deep .el-table--striped .el-table__body tr.el-table__row--striped td {
  background-color: rgba(168, 172, 171, 0.08) !important; /* def2ff f2faff */
}
</style>
