<template>
  <div class="card">
    <template v-if="list.length">
      <div
        v-for="(item, index) in list"

        :key="index"
        class="card-content-item"
        :class="{ active: index == curActive }"
        @click="itemClick(item, index)"
      >
        <div class="name">{{ item[option.name || 'name'] }}</div>
        <div class="progress">
          <div
            class="checkd"
            :style="{ width: getProgressWidth(item[option.value ||'value']) + '%' }"
          >
            <div class="point"></div>
          </div>
        </div>
        <div class="value">{{ item[option.value ||'value'] }}{{ option.unit }}</div>
      </div>
    </template>
    <div v-else class="empty">
      暂无数据
    </div>
  </div>
</template>
<script>

export default {
  props: {
    list: {
      type: Array,
      default: () => []
    },
    option: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      curActive: ''
    }
  },
  computed: {
    maxValue() {
      if (!this.list.length) return 0
      return Math.max(...this.list.map((item) => item[this.option.value || 'value']))
    }
  },
  created () {
    this.list.forEach((el) => {
      this.$set(el, 'active', false)
    })
  },

  methods: {
    getProgressWidth(value) {
      if (this.maxValue === 0) return 0
      return ((value / this.maxValue) * 100).toFixed(2)
    },
    itemClick (item, index) {
      item.active = !item.active
      this.curActive = index
    }
  }
}
</script>
<style lang="scss" scoped>
.card {
  width: 100%;
  height: 100%;
  overflow-y: auto;
  .card-content-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    color: #fff;
    padding: 5px;
    cursor: pointer;
    .name {
      width: 155px;
      text-align: right;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
    .progress {
      width: calc(100% - 100px - 120px);
      height: 4px;
      background: rgba($color: #fff, $alpha: 0.2);
      margin: 0 10px;
      .checkd {
        background: #30dcff;
        height: 100%;
        position: relative;
        .point{
          position: absolute;
          top: -4px;
          right: -3px;
          background: url('~@/assets/images/qhdsys/list-noCheck-point-bg.png') no-repeat;
          background-size: 100% 100%;
          width: 12px;
          height: 12px;
        }
      }
    }
    .value {
      width: 120px;
      color: #30dcff;
    }
    &.active {
      color: #ffdc83;
      .progress{

        .checkd {
          background: #ffdc83;
          .point{
            background: url('~@/assets/images/qhdsys/list-checked-point-bg.png') no-repeat;
            background-size: 100% 100%;
          }

        }
      }
      .value {
        color: #ffdc83;
      }
    }
  }
  .empty{
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    color: #999;
  }
  /* Webkit 浏览器滚动条样式 */
  &::-webkit-scrollbar {
    width: 4px; /* 设置滚动条宽度为 3.2px */
  }

  &::-webkit-scrollbar-track {
    background: transparent; /* 轨道背景透明 */
  }

  &::-webkit-scrollbar-thumb {
    background-color: #88ddf5; /* 设置滚动块颜色 */
    border-radius: 2px; /* 简单圆角，匹配细滚动条 */
  }

  &::-webkit-scrollbar-thumb:hover {
    background-color: #88ddf5; /* 悬停时保持颜色不变 */
  }

  /* 隐藏滚动条两端的箭头按钮 */
  &::-webkit-scrollbar-button {
    display: none;
  }

  /* Firefox 浏览器滚动条样式 */
  // scrollbar-width: thin;
  // scrollbar-color: #88ddf5 transparent;
}
</style>
