<template>
  <div
    ref="scrollContainer"
    class="card"
    @mouseenter="pauseAutoScroll"
    @mouseleave="resumeAutoScroll"
  >
    <template v-if="list.length">
      <div
        v-for="(item, index) in list"
        :key="index"
        class="card-content-item"
        :class="{ active: index == curActive }"
        @click="itemClick(item, index)"
      >
        <div class="name">{{ item[option.name || 'name'] }}</div>
        <div class="progress">
          <div
            class="checkd"
            :style="{ width: getProgressWidth(item[option.value ||'value']) + '%' }"
          >
            <div class="point"></div>
          </div>
        </div>
        <div class="value">{{ item[option.value ||'value'] }}{{ option.unit }}</div>
      </div>
    </template>
    <div v-else class="empty">
      暂无数据
    </div>
  </div>
</template>
<script>

export default {
  props: {
    list: {
      type: Array,
      default: () => []
    },
    option: {
      type: Object,
      default: () => ({})
    },
    // 自动滚动配置
    autoScroll: {
      type: Boolean,
      default: true
    },
    scrollSpeed: {
      type: Number,
      default: 0.3 // 滚动速度，像素/帧 (0.1-慢速, 0.3-正常, 0.5-快速, 1.0-很快)
    },
    scrollDelay: {
      type: Number,
      default: 500 // 滚动到底部后的延迟时间（毫秒）
    }
  },
  data() {
    return {
      curActive: '',
      // 自动滚动相关状态
      isScrolling: false,
      isPaused: false,
      scrollTimer: null,
      animationFrame: null
    }
  },
  computed: {
    maxValue() {
      if (!this.list.length) return 0
      return Math.max(...this.list.map((item) => item[this.option.value || 'value']))
    }
  },
  watch: {
    // 监听列表变化，重新启动滚动
    list: {
      handler(newList) {
        if (this.autoScroll && newList.length > 0) {
          this.stopAutoScroll()
          this.$nextTick(() => {
            this.startAutoScroll()
          })
        } else {
          this.stopAutoScroll()
        }
      },
      immediate: false
    },
    // 监听自动滚动开关
    autoScroll(newVal) {
      if (newVal && this.list.length > 0) {
        this.startAutoScroll()
      } else {
        this.stopAutoScroll()
      }
    }
  },
  created () {
    this.list.forEach((el) => {
      this.$set(el, 'active', false)
    })
  },
  mounted() {
    // 组件挂载后启动自动滚动
    if (this.autoScroll && this.list.length > 0) {
      this.startAutoScroll()
    }
  },
  beforeDestroy() {
    // 组件销毁前清理定时器和动画帧
    this.stopAutoScroll()
  },
  methods: {
    getProgressWidth(value) {
      if (this.maxValue === 0) return 0
      return ((value / this.maxValue) * 100).toFixed(2)
    },
    itemClick (item, index) {
      item.active = !item.active
      this.curActive = index
    },
    // 开始自动滚动
    startAutoScroll() {
      if (!this.autoScroll || this.isScrolling) return

      this.isScrolling = true
      this.isPaused = false
      this.performScroll()
    },
    // 停止自动滚动
    stopAutoScroll() {
      this.isScrolling = false
      this.isPaused = false

      if (this.scrollTimer) {
        clearTimeout(this.scrollTimer)
        this.scrollTimer = null
      }

      if (this.animationFrame) {
        cancelAnimationFrame(this.animationFrame)
        this.animationFrame = null
      }
    },
    // 执行滚动动画
    performScroll() {
      if (!this.isScrolling || this.isPaused) return

      const container = this.$refs.scrollContainer
      if (!container) return

      const currentScrollTop = container.scrollTop
      const maxScrollTop = container.scrollHeight - container.clientHeight

      // 检查是否需要滚动（内容高度大于容器高度）
      if (maxScrollTop <= 0) {
        // 内容不足以滚动，停止滚动
        this.stopAutoScroll()
        return
      }

      // 检查是否滚动到底部
      if (currentScrollTop >= maxScrollTop) {
        // 滚动到底部，发送事件给父组件
        this.$emit('scroll-to-bottom')

        // 延迟后重新从顶部开始滚动
        this.scrollTimer = setTimeout(() => {
          if (this.isScrolling && !this.isPaused) {
            container.scrollTop = 0
            this.performScroll()
          }
        }, this.scrollDelay)
        return
      }

      // 继续滚动
      container.scrollTop = currentScrollTop + this.scrollSpeed

      // 使用 requestAnimationFrame 实现平滑滚动
      this.animationFrame = requestAnimationFrame(() => {
        this.performScroll()
      })
    },
    // 暂停自动滚动（鼠标移入时）
    pauseAutoScroll() {
      this.isPaused = true

      if (this.scrollTimer) {
        clearTimeout(this.scrollTimer)
        this.scrollTimer = null
      }

      if (this.animationFrame) {
        cancelAnimationFrame(this.animationFrame)
        this.animationFrame = null
      }
    },
    // 恢复自动滚动（鼠标移出时）
    resumeAutoScroll() {
      if (this.isScrolling && this.isPaused) {
        this.isPaused = false
        this.performScroll()
      }
    }
  }
}
</script>
<style lang="scss" scoped>
.card {
  width: 100%;
  height: 100%;
  overflow-y: auto;
  .card-content-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    color: #fff;
    padding: 5px;
    cursor: pointer;
    .name {
      width: 155px;
      text-align: right;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
    .progress {
      width: calc(100% - 100px - 120px);
      height: 4px;
      background: rgba($color: #fff, $alpha: 0.2);
      margin: 0 10px;
      .checkd {
        background: #30dcff;
        height: 100%;
        position: relative;
        .point{
          position: absolute;
          top: -4px;
          right: -3px;
          background: url('~@/assets/images/qhdsys/list-noCheck-point-bg.png') no-repeat;
          background-size: 100% 100%;
          width: 12px;
          height: 12px;
        }
      }
    }
    .value {
      width: 120px;
      color: #30dcff;
    }
    &.active {
      color: #ffdc83;
      .progress{

        .checkd {
          background: #ffdc83;
          .point{
            background: url('~@/assets/images/qhdsys/list-checked-point-bg.png') no-repeat;
            background-size: 100% 100%;
          }

        }
      }
      .value {
        color: #ffdc83;
      }
    }
  }
  .empty{
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    color: #999;
  }
  /* Webkit 浏览器滚动条样式 */
  &::-webkit-scrollbar {
    width: 4px; /* 设置滚动条宽度为 3.2px */
  }

  &::-webkit-scrollbar-track {
    background: transparent; /* 轨道背景透明 */
  }

  &::-webkit-scrollbar-thumb {
    background-color: #88ddf5; /* 设置滚动块颜色 */
    border-radius: 2px; /* 简单圆角，匹配细滚动条 */
  }

  &::-webkit-scrollbar-thumb:hover {
    background-color: #88ddf5; /* 悬停时保持颜色不变 */
  }

  /* 隐藏滚动条两端的箭头按钮 */
  &::-webkit-scrollbar-button {
    display: none;
  }

  /* Firefox 浏览器滚动条样式 */
  // scrollbar-width: thin;
  // scrollbar-color: #88ddf5 transparent;
}
</style>
