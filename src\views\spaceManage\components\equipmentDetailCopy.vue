<template>
  <div class="main">
    <el-dialog
      v-dialogDrag
      :modal="false"
      :visible.sync="dialogShow"
      custom-class="mainDialog main"
      append-to-body
      :close-on-click-modal="false"
      :before-close="closeDialog"
      class="all-table-componentList"
    >
      <div class="dialog-content">
        <!-- <span class="dialog-title">设备详情</span> -->
        <el-tabs v-model="tabsActiveName" @tab-click="handleClick">
          <el-tab-pane label="设备概况" name="1">
            <equipmentSituation :deviceData="assetData.find(v => v.id == deviceId)"></equipmentSituation>
          </el-tab-pane>
          <el-tab-pane label="设备详情" name="2">
            <TableContent :deviceData="assetData.find(v => v.id == deviceId)"></TableContent>
          </el-tab-pane>
          <el-tab-pane label="设备档案" name="3">
            <equipment-archives :deviceId="deviceId"></equipment-archives>
          </el-tab-pane>
        </el-tabs>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import {allAssetData} from './assetData.js'
import equipmentSituation from './equipmentSituationCopy.vue'
import TableContent from './equipmentInfoCopy.vue'
import equipmentArchives from './equipmentArchivesCopy.vue'
export default {
  name: 'equipmentDetailCopy',
  components: {
    equipmentSituation,
    TableContent,
    equipmentArchives
  },
  props: {
    deviceId: {
      type: String,
      default: ''
    },
    dialogShow: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      assetData: allAssetData[__PATH.VUE_APP_HOSPITAL_NODE] || allAssetData['szzlyy'],
      tabsActiveName: '2'
    }
  },
  computed: {},
  watch: {},
  created() {},
  mounted() {},
  methods: {
    handleClick(tab) {},
    closeDialog() {
      this.$emit('configCloseDialog')
    }
  }
}
</script>
<style lang="scss" type="text/css" scoped>
.all-table-componentList {
  pointer-events: none;
  ::v-deep .el-image__error {
    background: center;
  }
}
::v-deep .mainDialog {
  width: 82%;
  height: 88vh;
  margin-top: 6vh !important;
  // background: #031553;
  background: url('@/assets/images/table-bg.png') no-repeat;
  background-size: 100% 100%;
  pointer-events: auto;
  position: relative;
  // .dialog-title {
  //   font-size: 16px;
  //   display: inline-block;
  //   width: auto;
  //   position: absolute;
  //   top: 1.2rem;
  //   left: 1.2rem;
  // }
  .el-dialog__body {
    padding: 10px 50px;
    height: calc(100% - 40px);
    max-height: 88vh;
    // max-height: fit-content;
    overflow-y: hidden;
    .dialog-content {
      overflow: auto;
      width: 100%;
      height: calc(100% - 0.25rem);
      .statistics-top {
        height: 150px;
        width: 100%;
        display: flex;
        justify-content: center;
        > div {
          width: 10%;
          height: 100%;
          padding: 10px 0;
          box-sizing: border-box;
          display: flex;
          justify-content: space-evenly;
          flex-direction: column;
          p {
            text-align: center;
            font-size: 1rem;
            font-family: DIN-Bold, DIN;
            font-weight: bold;
            color: #ffffff;
          }
          .green-font {
            font-size: 1.25rem;
            color: #ffe3a6;
          }
        }
      }
      .el-table {
        border: none !important;
      }
      .el-tabs {
        height: 100%;
        display: flex;
        flex-direction: column;
        .el-tabs__header {
          margin: 0 0 2rem;
        }
        .el-tabs__content {
          flex: 1;
          overflow: auto;
        }
        .el-tabs__nav-wrap::after {
          height: 0 !important;
        }
        .el-tabs__nav-scroll {
          display: flex;
          justify-content: center;
        }
        .el-tab-pane {
          display: flex;
          justify-content: center;
        }
        .el-tabs__nav {
          .el-tabs__item {
            text-align: center;
            width: 10.5rem;
            font-size: 14px;
            color: #ffffff;
            height: 25px;
            line-height: 16px;
          }
          .is-top:nth-child(0) {
            padding: 0 1.25rem;
          }
          .is-top:nth-child(2) {
            padding: 0 1.25rem;
          }
          .is-active {
            color: #ffe3a6;
          }
        }
        .el-tabs__active-bar {
          background-color: #ffe3a6;
        }
        .el-tabs__nav-wrap::after {
          height: 1px;
          background-color: #7eaef9;
        }
      }
    }
    .dialog-content::-webkit-scrollbar {
      width: 8px;
    }
    .dialog-content::-webkit-scrollbar-thumb {
      border-radius: 1px;
      background: #2c5aa4;
    }
  }
}
::v-deep .detailDialog {
  width: 60%;
  height: 80vh;
  margin-top: 7vh !important;
  background: url('@/assets/images/table-bg.png') no-repeat;
  background-size: 100% 100%;
  pointer-events: auto;
  .el-dialog__body {
    padding: 10px 50px;
    height: calc(100% - 60px);
    max-height: 80vh;
    .el-table th.el-table__cell > .cell {
      width: max-content;
    }
  }
}
::v-deep .el-dialog__header {
  height: 0;
  padding: 0;
  .el-dialog__headerbtn {
    z-index: 99999;
  }
}
</style>
