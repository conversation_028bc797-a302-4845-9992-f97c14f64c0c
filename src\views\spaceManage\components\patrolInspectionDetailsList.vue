<template>
  <div v-if="ipasPointDetailShow">
    <ipasPointDetail ref="retrospect" :dialogShow="ipasPointDetailShow" :detailId="detailId" @retrospectCloseDialog="retrospectCloseDialog"></ipasPointDetail>
  </div>
  <div v-else class="form-detail">
    <div class="plan-content">
      <ul class="item-row">
        <li class="width33">
          <span class="li-first-span">任务名称</span><span class="li-last-span">{{ dataInfo.taskName }}</span>
        </li>
        <li class="width33">
          <span class="li-first-span">工作频率</span><span class="li-last-span">{{ dataInfo.cycleRole }}</span>
        </li>
        <li class="width33">
          <span class="li-first-span">周期类型</span><span class="li-last-span">{{ dataInfo.cycleType | filterList }}</span>
        </li>
      </ul>
      <ul class="item-row">
        <li class="width33">
          <span class="li-first-span">巡检小组/人员</span><span class="li-last-span">{{ dataInfo.planPersonName || dataInfo.distributionTeamName }}</span>
        </li>
        <li class="width33">
          <span class="li-first-span">实际巡检开始时间-实际巡检结束时间</span><span class="li-last-span">{{ dataInfo.executeStartTime + ' - ' + dataInfo.executeEndTime }}</span>
        </li>
        <li class="width33">
          <span class="li-first-span">完成状态</span><span class="li-last-span">{{ dataInfo.status == '1' ? '未完成' : '已完成' }}</span>
        </li>
      </ul>
      <p style="padding: 1rem"></p>
      <!-- <el-table
        :data="tableData"
        :cell-style="$tools.setCell(3)"
        :header-cell-style="$tools.setHeaderCell(3)"
        style="width: 100%"
      >
        <el-table-column fixed type="index" width="80" show-overflow-tooltip label="序号"></el-table-column>
        <el-table-column fixed prop="alarmSource" show-overflow-tooltip label="报警来源"></el-table-column>
        <el-table-column fixed prop="alarmTypeName" show-overflow-tooltip label="报警类型"></el-table-column>
        <el-table-column fixed prop="deviceName" show-overflow-tooltip label="报警项名称"></el-table-column>
      </el-table> -->
      <!-- 模版1 -->
      <el-table :data="tableData" :cell-style="$tools.setCell(3)" :header-cell-style="$tools.setHeaderCell(3)">
        <el-table-column type="index" width="70" label="序号"></el-table-column>
        <el-table-column prop="taskPointName" show-overflow-tooltip label="巡检点名称" align="center"></el-table-column>
        <el-table-column show-overflow-tooltip label="巡检结果" align="center">
          <template slot-scope="scope" :class="scope.row">
            <span :style="{ color: scope.row.state == '3' || scope.row.state == '4' ? 'red' : '' }">{{
              scope.row.state == '2' ? '合格' : scope.row.state == '3' ? '不合格' : scope.row.state == '4' ? '异常报修' : '未巡检'
            }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="hiddenDangerId" show-overflow-tooltip label="隐患编号" align="center"></el-table-column>
        <el-table-column prop="excuteTime" show-overflow-tooltip label="实际巡检时间" align="center"></el-table-column>
        <el-table-column prop="implementPersonName" show-overflow-tooltip label="执行人员" align="center"></el-table-column>
        <el-table-column prop="spyScan" show-overflow-tooltip label="定位状态" align="center"></el-table-column>
        <el-table-column prop="upDepartName" show-overflow-tooltip label="操作" align="center">
          <template slot-scope="scope">
            <el-link type="primary" :underline="false" @click="viewDetails(scope.row)">详情</el-link>
          </template>
        </el-table-column>
      </el-table>
      <el-pagination
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page="currentPage"
        :page-sizes="[15, 20, 30, 40]"
        :page-size="pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
        class="pagination"
      ></el-pagination>
    </div>
  </div>
</template>

<script>
import { GetTaskPointReleaseList } from '@/utils/spaceManage'
import ipasPointDetail from '../../centerScreen/businessManagement/component/ipasPointDetail.vue'
export default {
  name: 'patrolInspectionDetailsList',
  components: { ipasPointDetail },
  props: {
    dataInfo: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  data() {
    return {
      detailId: '',
      ipasPointDetailShow: false,
      currentPage: 1,
      pageSize: 10,
      total: 0,
      tableData: []
    }
  },
  filters: {
    filterList: function (value) {
      const option = [
        {
          cycleType: 8,
          label: '单次'
        },
        {
          cycleType: 6,
          label: '每日'
        },
        {
          cycleType: 0,
          label: '每周'
        },
        {
          cycleType: 2,
          label: '每月'
        },
        {
          cycleType: 3,
          label: '季度'
        },
        {
          cycleType: 5,
          label: '全年'
        }
      ]
      return option.find(item => item.cycleType === value)?.label ?? ''
    }
  },
  computed: {},
  mounted() {
    this.dataInfo = this.$route.query.dataInfo
    try {
      window.chrome.webview.hostObjects.sync.bridge.ShowDialog(true)
    } catch (error) {}
  },
  beforeDestroy() {
    try {
      window.chrome.webview.hostObjects.sync.bridge.ShowDialog(false)
    } catch (error) {}
  },
  created() {
    this.GetTaskPointReleaseList()
  },
  methods: {
    retrospectCloseDialog() {
      Object.assign(this, {
        detailId: '',
        ipasPointDetailShow: false
      })
    },
    // 查看详情
    viewDetails(row) {
      // taskPointId
      // taskPointTypeId
      this.detailId = row.id
      this.ipasPointDetailShow = true
    },
    GetTaskPointReleaseList() {
      GetTaskPointReleaseList({ taskId: this.dataInfo.id, pageNo: this.currentPage, pageSize: this.pageSize }).then((res) => {
        if (res.data.code === '200') {
          this.total = parseInt(res.data.data.sum)
          this.tableData = res.data.data.list
        }
      })
    },
    handleSizeChange(val) {
      this.pageSize = val
      this.GetTaskPointReleaseList()
    },
    handleCurrentChange(val) {
      this.currentPage = val
      this.GetTaskPointReleaseList()
    }
  }
}
</script>

<style lang="scss" scoped>
.form-detail {
  height: calc(100% - 30px);
  width: 100%;
  padding: 10px 25px;
  overflow-y: scroll;
  box-sizing: border-box;
  .plan-content {
    width: 100%;
    // padding: 20px 0px 20px 20px;
    color: #b5bacb;
    font-size: 13px;
    .item-row {
      width: 100%;
      display: flex;
      padding: 20px 0px 20px 30px;
      box-sizing: border-box;
      .width33 {
        width: 33%;
      }
      .width95 {
        width: 95%;
        display: flex;
      }
      ::v-deep .el-image__error,
      ::v-deep .el-image__placeholder {
        background: center;
      }
      .li-first-span {
        display: inline-block;
        width: 120px;
        // margin-right: 20px;
        font-size: 14px;
        font-family: PingFang-SC-Medium, PingFang-SC;
        font-weight: 500;
        color: #7eaef9;
      }
      .li-last-span {
        display: inline-block;
        width: calc(100% - 120px);
        font-size: 14px;
        font-family: PingFang-SC-Medium, PingFang-SC;
        font-weight: 500;
        color: #ffffff;
        // align-items: center;
      }
      #audio-box {
        display: flex;
      }
      #audio-box > audio {
        width: 260px;
        height: 30px;
      }
      #audio-box > a {
        width: 40px;
        text-align: center;
        background-color: #2cc7c5;
        height: 35px;
        line-height: 35px;
        color: #fff;
        border-radius: 5px;
        margin-left: 10px;
      }
    }
  }
}
</style>
