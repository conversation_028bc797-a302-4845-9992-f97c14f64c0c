<template>
  <div class="form-detail">
    <div class="reserve-scorll">
      <div class="reserve-plan">
        <div class="plan-title">
          <div class="linear-g">
            <span class="linear-g-span1">科室状态监测</span>
            <!-- <span>2020-21-21</span> -->
          </div>
        </div>
        <div class="plan-content">
          <ul class="item-row">
            <li class="width33">
              <span class="li-first-span">资产编码</span><span class="li-last-span">200</span>
            </li>
            <li class="width33">
              <span class="li-first-span">资产名称</span><span class="li-last-span">医用磁共振成像设备</span>
            </li>
            <li class="width33">
              <span class="li-first-span">68编码</span><span class="li-last-span">-</span>
            </li>
          </ul>
          <ul class="item-row">
            <li class="width33">
              <span class="li-first-span">财务编码</span><span class="li-last-span">-</span>
            </li>
            <li class="width33">
              <span class="li-first-span">规格型号</span><span class="li-last-span">-</span>
            </li>
            <li class="width33">
              <span class="li-first-span">出场编码</span><span class="li-last-span">-</span>
            </li>
          </ul>
          <ul class="item-row">
            <li class="width33">
              <span class="li-first-span">所在区域</span><span class="li-last-span">-</span>
            </li>
            <li class="width33">
              <span class="li-first-span">存放位置</span><span class="li-last-span">-</span>
            </li>
            <li class="width33">
              <span class="li-first-span">使用科室</span><span class="li-last-span">血液科</span>
            </li>
          </ul>
          <ul class="item-row">
            <li class="width33">
              <span class="li-first-span">管理员</span><span class="li-last-span">-</span>
            </li>
            <li class="width33">
              <span class="li-first-span">合同编码</span><span class="li-last-span">-</span>
            </li>
            <li class="width33">
              <span class="li-first-span">发票号</span><span class="li-last-span"></span>
            </li>
          </ul>
          <!-- <ul class="item-row">
            <li class="width90">
              <span class="li-first-span">隐患描述</span><span class="li-last-span">{{}}</span>
            </li>
          </ul> -->
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { GetHistoryFollowInfo } from '@/utils/centerScreenApi'
export default {
  name: 'assetsDetailsList',
  data() {
    return {
      detailsInfo: []
    }
  },
  created() {
    if (this.$route.query.id) {
      this.getHistoryFollowInfo(this.$route.query.id)
    }
  },
  mounted() {
    try {
      window.chrome.webview.hostObjects.sync.bridge.ShowDialog(true)
    } catch (error) {}
  },
  beforeDestroy() {
    try {
      window.chrome.webview.hostObjects.sync.bridge.ShowDialog(false)
    } catch (error) {}
  },
  methods: {
    getHistoryFollowInfo(id) {
      GetHistoryFollowInfo({ id }).then(res => {
        if (res.data.code === '200') {
          this.detailsInfo = res.data.data
        }
      })
    },
    // 展开关闭事件
    collectEvent(box, i) {
      if (this.$refs[box + 'right' + i][0].style.display === 'inline-block') {
        this.$refs[box + 'right' + i][0].style.display = 'none'
        this.$refs[box + 'down' + i][0].style.display = 'inline-block'
        this.$refs[box + i][0].show = true
      } else {
        this.$refs[box + 'right' + i][0].style.display = 'inline-block'
        this.$refs[box + 'down' + i][0].style.display = 'none'
        this.$refs[box + i][0].show = false
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.form-detail {
  height: calc(100% - 30px);
  width: 100%;
  padding: 10px 25px;
  overflow-y: scroll;
  box-sizing: border-box;
  .reserve-scorll {
    width: 100%;
    height: calc(100% - 50px);
    overflow-y: auto;
    box-sizing: border-box;
    padding-right: 15px;
  }
  .reserve-plan {
    width: 100%;
    position: relative;
    // height: inherit;
    .plan-title {
      display: flex;
      width: 100%;
      height: 30px;
      .color-box {
        height: 1.25rem;
        width: 1.25rem;
        color: #e6cf9d;
        line-height: 1.25rem;
        text-align: center;
        border-radius: 3px;
        font-size: 16px;
        margin: auto;
      }
      .linear-g {
        margin-left: 2px;
        width: 100%;
        height: 100%;
        background: #263057;
        font-size: 13px;
        line-height: 30px;
        font-family: PingFangSC-Medium;
        color: #ffe3a6;
        border-radius: 6px;
        cursor: pointer;
        .linear-g-span1 {
          margin: 0 15px;
          font-weight: 600;
        }
        .title-icon {
          float: right;
          line-height: 30px;
          margin-right: 5px;
        }
      }
    }
    .plan-content {
      width: calc(100% - 33px);
      // border-left: 1px solid #303758;
      margin-left: 11px;
      // padding: 20px 0px 20px 20px;
      color: #b5bacb;
      font-size: 13px;
      .item-row {
        width: 100%;
        display: flex;
        padding: 20px 0px 20px 30px;
        box-sizing: border-box;
        .width33 {
          width: 30%;
        }
        .width90 {
          width: 90%;
          display: flex;
        }
        ::v-deep .el-image__error,
        ::v-deep .el-image__placeholder {
          background: center;
        }
        .li-first-span {
          display: inline-block;
          width: 120px;
          // margin-right: 20px;
          font-size: 14px;
          font-family: PingFang-SC-Medium, PingFang-SC;
          font-weight: 500;
          color: #7eaef9;
        }
        .li-last-span {
          display: inline-block;
          width: calc(100% - 120px);
          font-size: 14px;
          font-family: PingFang-SC-Medium, PingFang-SC;
          font-weight: 500;
          color: #ffffff;
          // align-items: center;
        }
        #audio-box {
          display: flex;
        }
        #audio-box > audio {
          width: 260px;
          height: 30px;
        }
        #audio-box > a {
          width: 40px;
          text-align: center;
          background-color: #2cc7c5;
          height: 35px;
          line-height: 35px;
          color: #fff;
          border-radius: 5px;
          margin-left: 10px;
        }
      }
      .show-content {
        width: 100%;
      }
    }
  }
}
</style>
