<template>
  <div class="imwsComponent">
    <div class="medical-waste-analysis">
      <div class="box-title">
        <div><span></span>医废基本情况分析</div>
        <div class="title-right">
          <p class="title-right-tag" v-for="(item, index) in tagList" :key="item.value" @click="changeDateType(item.value)">
            <span :class="{ 'tag-active': tagCurrent == item.value }">{{ item.text }}</span
            ><i v-if="index !== tagList.length - 1">|</i>
          </p>
        </div>
      </div>
      <div class="box-content">
        <div v-if="medicalWasteAnalysisShow" class="center-center">暂无数据</div>
        <div v-else style="width: 100%; height: 100%">
          <div id="medicalWasteAnalysisEcharts"></div>
          <div class="pie_background"></div>
        </div>
      </div>
    </div>
    <div class="medical-waste-trend">
      <div class="box-title">
        <div><span></span>近一周医废收集变化趋势</div>
        <span></span>
      </div>
      <div class="box-content">
        <div v-if="medicalWasteTrendShow" class="center-center">暂无数据</div>
        <div v-else style="width: 100%; height: 100%">
          <div id="medicalWasteTrendEcharts"></div>
        </div>
      </div>
    </div>
    <div class="medical-waste-table">
      <div class="box-title">
        <div><span></span>医废收集记录</div>
        <span @click="allTableChange('imws')">更多<i class="el-icon-arrow-right"></i></span>
      </div>
      <div class="box-content" style="padding: 5px 0px">
        <el-table
          class="table-center-transfer"
          @row-dblclick="selectConfigRowData"
          :data="tableData"
          height="calc(100% - 0px)"
          :cell-style="$tools.setCell(3)"
          :header-cell-style="$tools.setHeaderCell(3)"
          style="width: 100%"
          v-loading="tableLoading"
          element-loading-background="rgba(0, 0, 0, 0.2)"
        >
          <el-table-column v-for="(column, index) in tableColumn" :key="index" :prop="column.prop" :label="column.label" :min-width="column.minWidth" show-overflow-tooltip>
            <template slot-scope="scope">
              <table-render v-if="column.render" :sc="scope" :row="scope.row" :render="column.render"></table-render>
              <div v-else-if="!column.formatter">
                {{ scope.row[column.prop] }}
              </div>
              <div v-else>
                {{ column.formatter(scope.row) }}
              </div>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </div>
    <template v-if="allTableComponentListShow">
      <allTableComponentList ref="allTableComponentList" :dialogData="tableCompenentData" :dialogShow="allTableComponentListShow" @configCloseDialog="configCloseTableDialog"></allTableComponentList>
    </template>
    <template v-if="retrospectShow">
      <retrospect ref="retrospect" :dialogShow="retrospectShow" :detailId="detailId" @retrospectCloseDialog="retrospectCloseDialog"></retrospect>
    </template>
  </div>
</template>

<script>
import allTableComponentList from '../components/allTableComponentList.vue'
import retrospect from '@views/centerScreen/businessManagement/component/retrospect.vue'
import { dialogTypeList } from '@/assets/common/dict.js'
import { getDepartMedicalWasteList } from '@/utils/centerScreenApi'
import { getRuiAnTypeAnalysisInfo, getMedicalTypeWeekTrendInfo } from '@/utils/spaceManage.js'
import * as echarts from 'echarts'
// import moment from 'moment'
export default {
  name: 'imwsComponent',
  components: {
    allTableComponentList,
    retrospect
  },
  props: {
    roomData: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      medicalWasteAnalysisShow: false, // 工单类型统计echarts是否显示
      medicalWasteTrendShow: false, // 近12个月工单走势echarts是否显示
      allTableComponentListShow: false, // 医废弹窗显示
      tableCompenentData: {}, // 医废弹窗数据
      retrospectShow: false, // 追溯详情弹窗显示
      // iomsDetailObj: {}, // 追溯详情弹窗数据
      dialogTypeList: dialogTypeList, // 过滤选中弹窗类型
      detailId: '', // 追溯详情id
      tagCurrent: 'month', // 日期类型
      tagList: [
        { text: '本周', value: 'week' },
        { text: '本月', value: 'month' },
        { text: '本年', value: 'year' }
      ],
      tableData: [], // 表格数据
      tableColumn: [
        {
          prop: 'wasteType',
          label: '医废类型'
        },
        // {
        //   prop: 'count',
        //   label: '医废数量'
        // },
        {
          prop: 'gatherWeigh',
          label: '重量'
        },
        {
          prop: 'gatherTime',
          label: '收集时间'
        },
        {
          prop: 'inventoryStatus',
          label: '状态',
          formatter: (row) => {
            // inventoryStatus === 1 '已收集'
            // inventoryStatus === 2 '已入站'
            // inventoryStatus === 3 '已出站'
            return row.inventoryStatus === '1' ? '已收集' : row.inventoryStatus === '2' ? '已入站' : row.inventoryStatus === '3' ? '已出站' : ''
          }
        }
      ], // 表格列
      tableLoading: false // 表格加载
    }
  },
  mounted() {
    this.getTypeAnalysisInfo()
    this.getDepartMedicalWasteTableList()
    this.getMedicalTypeWeekTrendInfo()
  },
  methods: {
    // 医废基本情况分析
    getTypeAnalysisInfo() {
      const params = { dateType: this.tagCurrent, spatialId: this.roomData.localtion, ssmType: this.roomData.ssmType }
      getRuiAnTypeAnalysisInfo(params).then((res) => {
        const data = res.data
        if (data.code === '200') {
          if (data.data.list.length) {
            this.medicalWasteAnalysisShow = false
            const arr = data.data.list
            let total = 0
            // arr.reduce 根据wasteType相同的数据进行累加
            const list = arr.reduce((prev, cur) => {
              total += Number(cur.totalweigh)
              const obj = prev.find((item) => item.wasteType === cur.wasteType)
              if (obj) {
                obj.totalweigh = (Number(obj.totalweigh) + Number(cur.totalweigh)).toFixed(2)
              } else {
                prev.push(cur)
              }
              return prev
            }, [])
            this.$nextTick(() => {
              this.departmentEchart(list, total)
            })
          } else {
            this.medicalWasteAnalysisShow = true
          }
        }
      })
    },
    // 分析echarts
    departmentEchart(arr, total) {
      this.getPiechart = echarts.init(document.getElementById('medicalWasteAnalysisEcharts'))
      const data = []
      var color = [
        'rgba(31, 250, 255, 0.3)',
        'rgba(255, 227, 166, 0.28)',
        'rgba(147, 130, 255, 0.26)',
        'rgba(0, 248, 114, 0.24)',
        'rgba(46, 119, 251, 0.3)',
        'rgba(255, 84, 84, 0.3)',
        'rgba(123, 255, 193, 0.3)'
      ]
      var borderColor = ['rgba(31, 250, 255, 1)', 'rgba(255, 227, 166, 1)', 'rgba(147, 130, 255, 1)', 'rgba(0, 248, 114, 1)', 'rgba(46, 119, 251, 1)', 'rgba(255, 84, 84, 1)', 'rgba(123, 255, 193, 1)']
      var xdata = arr.map((item) => {
        return item.wasteType
      })
      for (var i = 0; i < arr.length; i++) {
        data.push({
          name: arr[i].wasteType,
          value: arr[i].totalweigh,
          itemStyle: {
            normal: {
              borderWidth: 1,
              shadowBlur: 200,
              borderColor: borderColor[i],
              color: color[i],
              label: {
                normal: { show: true }
              }
            }
          }
        })
      }
      var seriesObj = [
        {
          name: '',
          type: 'pie',
          clockWise: false,
          center: ['25%', '50%'],
          radius: ['47%', '70%'],
          // hoverAnimation: false,
          label: {
            normal: {
              show: false,
              position: 'outside',
              formatter: '{b}\n {c}kg\n {d}%',
              textStyle: {
                fontSize: '12',
                color: '#fff'
              }
            },
            emphasis: {
              show: true,
              textStyle: {
                fontSize: '12',
                color: '#fff'
              }
            }
          },
          data: data
        }
      ]
      const option = {
        backgroundColor: '',
        title: {
          text: '  总重量',
          subtext: total.toFixed(2) + 'kg',
          textStyle: {
            color: '#CFE1F7',
            fontSize: 12
          },
          subtextStyle: {
            fontSize: 16,
            color: '#FFFFFF'
          },
          x: '16%',
          y: '38%'
        },
        legend: {
          orient: 'vertical',
          top: 'center',
          right: '0%',
          bottom: '0%',

          data: xdata,
          itemWidth: 8,
          itemHeight: 8,
          itemGap: 16,
          textStyle: {
            color: '#B3C2DD' //  字体颜色
          },
          formatter: function (name) {
            var oa = option.series[0].data
            for (var i = 0; i < option.series[0].data.length; i++) {
              if (name === oa[i].name) {
                return ' ' + name + ' (' + oa[i].value + ')     ' + ((oa[i].value / total) * 100).toFixed(2) + '%'
              }
            }
          }
        },
        series: seriesObj
      }
      this.getPiechart.setOption(option)
      // if (data.length > 1) {
      //   clearInterval(this.mTime)
      //   this.pieEchartSetTimeOut(data)
      // }
      // // 鼠标移出后默认高亮
      // this.getPiechart.on('mouseout', () => {
      //   clearInterval(this.mTime)
      //   this.pieEchartSetTimeOut(data)
      // })
      this.getPiechart.clear()
      this.getPiechart.setOption(option)
      // 随着屏幕大小调节图表
      window.addEventListener('resize', () => {
        this.getPiechart.resize()
      })
    },
    getMedicalTypeWeekTrendInfo() {
      const params = {
        dateType: 'sevenDay',
        spatialId: this.roomData.localtion,
        ssmType: this.roomData.ssmType
      }
      getMedicalTypeWeekTrendInfo(params).then((res) => {
        const data = res.data
        if (data.code === '200') {
          const arr = data.data.list
          if (arr.length) {
            // const sortArr = arr.sort((a, b) => a.totalweigh - b.totalweigh)
            const list = arr.reduce((prev, cur) => {
              const obj = prev.find((item) => item.gatherTime === cur.gatherTime)
              if (obj) {
                obj.Infectedweigh = (Number(obj.Infectedweigh) + Number(cur.Infectedweigh)).toFixed(2)
                obj.chemistryweigh = (Number(obj.chemistryweigh) + Number(cur.chemistryweigh)).toFixed(2)
                obj.damageweigh = (Number(obj.damageweigh) + Number(cur.damageweigh)).toFixed(2)
                obj.epidemicweigh = (Number(obj.epidemicweigh) + Number(cur.epidemicweigh)).toFixed(2)
                obj.medicineweigh = (Number(obj.medicineweigh) + Number(cur.medicineweigh)).toFixed(2)
                obj.pathoweigh = (Number(obj.pathoweigh) + Number(cur.pathoweigh)).toFixed(2)
                obj.otherweigh = (Number(obj.otherweigh) + Number(cur.otherweigh)).toFixed(2)
              } else {
                prev.push(cur)
              }
              return prev
            }, [])
            this.medicalWasteTrendShow = false
            this.$nextTick(() => {
              this.getDeptProduceEcharts(list)
            })
          } else {
            this.medicalWasteTrendShow = true
          }
        }
      })
    },
    // top5 echarts
    getDeptProduceEcharts(arr) {
      const getchart = echarts.init(document.getElementById('medicalWasteTrendEcharts'))
      const name = Array.from(arr, ({ gatherTime }) => gatherTime)
      const Infectedweigh = Array.from(arr, ({ Infectedweigh }) => Infectedweigh)
      const chemistryweigh = Array.from(arr, ({ chemistryweigh }) => chemistryweigh)
      const damageweigh = Array.from(arr, ({ damageweigh }) => damageweigh)
      const epidemicweigh = Array.from(arr, ({ epidemicweigh }) => epidemicweigh)
      const medicineweigh = Array.from(arr, ({ medicineweigh }) => medicineweigh)
      const pathoweigh = Array.from(arr, ({ pathoweigh }) => pathoweigh)
      const otherweigh = Array.from(arr, ({ otherweigh }) => otherweigh)
      const option = {
        backgroundColor: '',
        tooltip: {
          trigger: 'axis',
          backgroundColor: '#0D2169',
          borderColor: '#727382',
          textStyle: {
            color: '#fff'
          },
          axisPointer: {
            // 坐标轴指示器，坐标轴触发有效
            type: 'shadow' // 默认为直线，可选为：'line' | 'shadow'
          }
        },
        grid: {
          x: '13%',
          width: '75%',
          // y: '22%'
          bottom: '18%'
        },
        legend: {
          orient: 'horizontal',
          left: 'center',
          top: '0',
          // data: xdata,
          itemWidth: 8,
          itemHeight: 8,
          itemGap: 16,
          textStyle: {
            color: '#B3C2DD' //  字体颜色
          }
        },
        xAxis: {
          type: 'category',
          axisLine: {
            show: true,
            lineStyle: {
              color: '#878EA9',
              width: 1,
              type: 'solid'
            }
          },
          // boundaryGap: true,
          axisTick: {
            show: false
          },
          axisLabel: {
            margin: 8,
            interval: 0
          },
          data: name
        },
        yAxis: {
          type: 'value',
          axisLabel: {
            show: true,
            textStyle: {
              color: '#878EA9'
            }
          },
          axisLine: {
            lineStyle: {
              type: 'solid',
              color: '#3862B7',
              width: '1'
            }
          },
          splitLine: {
            lineStyle: {
              color: '#314A89',
              width: 1,
              type: 'solid'
            }
          },
          axisTick: {
            show: false
          }
        },
        series: [
          {
            name: '损伤类',
            data: damageweigh,
            type: 'bar',
            stack: 'one',
            barWidth: '6',
            itemStyle: {
              color: new echarts.graphic.LinearGradient(0, 1, 0, 0, [
                {
                  offset: 0,
                  color: 'rgba(175, 255, 217, 0.2)'
                },
                {
                  offset: 1,
                  color: '#2AF598 '
                }
              ])
            }
          },
          {
            name: '病理类',
            data: pathoweigh,
            type: 'bar',
            stack: 'one',
            barWidth: '6',
            itemStyle: {
              color: new echarts.graphic.LinearGradient(0, 1, 0, 0, [
                {
                  offset: 0,
                  color: 'rgba(255, 229, 143, 0.2)'
                },
                {
                  offset: 1,
                  color: '#FFE58F '
                }
              ])
            }
          },
          {
            name: '化学类',
            data: chemistryweigh,
            type: 'bar',
            stack: 'one',
            barWidth: '6',
            itemStyle: {
              color: new echarts.graphic.LinearGradient(0, 1, 0, 0, [
                {
                  offset: 0,
                  color: 'rgba(124, 174, 255, 0.2)'
                },
                {
                  offset: 1,
                  color: '#7CAEFF'
                }
              ])
            }
          },
          {
            name: '感染类',
            data: Infectedweigh,
            type: 'bar',
            stack: 'one',
            barWidth: '6',
            itemStyle: {
              color: new echarts.graphic.LinearGradient(0, 1, 0, 0, [
                {
                  offset: 0,
                  color: 'rgba(255, 198, 144, 0.2)'
                },
                {
                  offset: 1,
                  color: '#FFC690'
                }
              ])
            }
          },
          {
            name: '药物类',
            data: medicineweigh,
            type: 'bar',
            stack: 'one',
            barWidth: '6',
            itemStyle: {
              color: new echarts.graphic.LinearGradient(0, 1, 0, 0, [
                {
                  offset: 0,
                  color: 'rgba(255, 96, 96, 0.2)'
                },
                {
                  offset: 1,
                  color: '#FF6060'
                }
              ])
            }
          },
          {
            name: '涉疫类',
            data: epidemicweigh,
            type: 'bar',
            stack: 'one',
            barWidth: '6',
            itemStyle: {
              color: new echarts.graphic.LinearGradient(0, 1, 0, 0, [
                {
                  offset: 0,
                  color: 'rgba(0, 248, 114, 0.2)'
                },
                {
                  offset: 1,
                  color: 'rgba(0, 248, 114, 1) '
                }
              ])
            }
          },
          {
            name: '其他类',
            data: otherweigh,
            type: 'bar',
            stack: 'one',
            barWidth: '6',
            itemStyle: {
              color: new echarts.graphic.LinearGradient(0, 1, 0, 0, [
                {
                  offset: 0,
                  color: 'rgba(147, 130, 255, 0.2)'
                },
                {
                  offset: 1,
                  color: 'rgba(147, 130, 255, 1)'
                }
              ])
            }
          }
        ],
        dataZoom: [
          {
            xAxisIndex: 0,
            show: true,
            type: 'slider',
            startValue: 0,
            endValue: 0,
            height: 8,
            bottom: '0%',
            borderColor: 'rgba(43,48,67,.1)',
            fillerColor: '#6580b8',
            zoomLock: false,
            showDataShadow: false,
            backgroundColor: 'rgba(43,48,67,.1)',
            showDetail: false,
            realtime: true,
            filterMode: 'filter',
            handleIcon: 'M-9.35,34.56V42m0-40V9.5m-2,0h4a2,2,0,0,1,2,2v21a2,2,0,0,1-2,2h-4a2,2,0,0,1-2-2v-21A2,2,0,0,1-11.35,9.5Z',
            handleStyle: {
              color: '#6580b8',
              borderColor: '#6580b8'
            },
            moveOnMouseMove: true,
            maxValueSpan: name.length,
            minValueSpan: 2,
            brushSelect: false
          },
          {
            type: 'inside',
            show: false,
            height: 8,
            start: 100,
            end: 100
          }
        ]
      }
      getchart.clear()
      getchart.setOption(option)
      // 随着屏幕大小调节图表
      window.addEventListener('resize', () => {
        getchart.resize()
      })
    },
    changeDateType(val) {
      this.tagCurrent = val
      this.getTypeAnalysisInfo()
    },
    allTableChange(type) {
      this.allTableComponentListShow = true
      Object.assign(this.tableCompenentData, {
        ...this.roomData,
        ...this.dialogTypeList.find((e) => e.type === type)
      })
    },
    configCloseTableDialog() {
      this.allTableComponentListShow = false
    },
    // 获取列表
    getDepartMedicalWasteTableList() {
      const params = {
        dateType: 'all',
        pageNo: 1,
        pageSize: 100,
        spatialId: this.roomData.localtion,
        ssmType: this.roomData.ssmType
      }
      this.tableLoading = true
      getDepartMedicalWasteList(params).then((res) => {
        const data = res.data
        this.tableLoading = false
        if (data.rows.length) {
          this.tableData = data.rows
        } else {
          this.tableData = []
        }
      })
    },
    // 双击 以及操作点击事件
    selectConfigRowData(row) {
      // this.iomsDetailObj = row
      this.detailId = row.id
      this.retrospectShow = true
      try {
        window.chrome.webview.hostObjects.sync.bridge.ShowDialog(true)
      } catch (error) {}
    },
    retrospectCloseDialog() {
      this.retrospectShow = false
      window.chrome.webview.hostObjects.sync.bridge.ShowDialog(false)
    }
  }
}
</script>

<style lang="scss" scoped>
.imwsComponent {
  width: 100%;
  height: 100%;
  .medical-waste-analysis {
    width: 100%;
    height: calc(30% - 10px);
    padding-bottom: 10px;
  }
  .medical-waste-trend {
    width: 100%;
    height: calc(35% - 20px);
    border-top: 1px solid #2e4989;
    border-bottom: 1px solid #2e4989;
    padding: 10px 0;
  }
  .medical-waste-table {
    width: 100%;
    height: calc(35% - 10px);
    padding-top: 10px;
  }
  .box-title {
    height: 30px;
    line-height: 30px;
    font-size: 14px;
    font-family: PingFangSC-Medium, PingFang SC;
    color: #ffffff;
    display: flex;
    justify-content: space-between;
    :last-child {
      margin-right: 10px;
      font-size: 14px;
      font-family: PingFang-SC-Medium, PingFang-SC;
      color: #7eaef9;
      cursor: pointer;
    }
    > div {
      > span {
        display: inline-block;
        width: 2px;
        height: 16px;
        background: #ffe3a6;
        margin-right: 6px;
        vertical-align: middle;
      }
    }
    .title-right {
      display: flex;
      margin-right: 0px;
      .title-right-tag {
        span {
          display: inline-block;
          width: fit-content;
          padding: 0px 4px;
          height: 30px;
          font-size: 14px;
          text-align: center;
          line-height: 26px;
          color: #7eaef9;
          margin-right: 0px;
          cursor: pointer;
        }
        i {
          color: #7eaef9;
          margin: 0 3px !important;
        }
      }
      .tag-active {
        color: #ffe3a6 !important;
        background: url('~@/assets/images/center/light-yellow.png') no-repeat center / 100%;
      }
      // .title-right-tag:last-child {
      //   border-right: none;
      // }
    }
  }
  .box-content {
    width: 100%;
    height: calc(100% - 30px);
    position: relative;
    box-sizing: border-box;
    padding: 10px 10px 15px 10px;
    .pie_background {
      left: 26%;
    }
    #medicalWasteAnalysisEcharts,
    #medicalWasteTrendEcharts {
      position: relative;
      width: 100%;
      height: 100%;
      z-index: 2;
    }
    ::v-deep .el-table {
      border: none !important;
      .el-table__header-wrapper {
        .cell {
          padding-left: 0;
          padding-right: 0;
          text-align: center;
          white-space: nowrap;
        }
      }
      .el-table__body-wrapper {
        td.el-table__cell div {
          overflow: hidden;
          white-space: nowrap;
          text-overflow: ellipsis;
        }
      }
      .el-table__body {
        tr {
          background: center;
        }
        td.el-table__cell,
        th.el-table__cell.is-leaf {
          border-right: 2px solid #0a164e;
          border-bottom: 2px solid #0a164e;
          background: rgba(56, 103, 180, 0.2);
          color: #fff;
        }
      }
    }
  }
  ::v-deep .detailDialog {
    width: 60%;
    height: 80vh;
    margin-top: 7vh !important;
    background: url('@/assets/images/table-bg.png') no-repeat;
    background-size: 100% 100%;
    pointer-events: auto;
    .el-dialog__body {
      padding: 10px 50px;
      height: calc(100% - 60px);
      max-height: 80vh;
    }
  }
}
</style>
