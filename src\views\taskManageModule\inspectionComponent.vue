<template>
  <div class="inspection-container">
    <ModuleCard title="巡检任务统计" style="height: calc(20%)" class="container-box">
      <div slot="title-right">
        <div class="chart-title-box">
          <div class="dropdown-div">
            <el-dropdown trigger="click" @command="(val) => dateActive = val">
              <span class="el-dropdown-link">
                {{ dateTagList.find((v) => v.value == dateActive)?.text ?? "" }}
                <i class="el-icon-arrow-down"></i>
              </span>
              <el-dropdown-menu slot="dropdown">
                <el-dropdown-item v-for="item in dateTagList" :key="item.value" :command="item.value" :class="{ isBjxl: dateActive == item.value }">
                  {{ item.text }}
                </el-dropdown-item>
              </el-dropdown-menu>
            </el-dropdown>
          </div>
          <div class="chart-icon" @click="allTableChange">
            <span>详情</span>
            <i class="el-icon-arrow-right"></i>
          </div>
        </div>
      </div>
      <div slot="content" class="top-content" style="height: 100%">
        <div class="top-fiex">
          <div class="top-fiex-div">
            <div class="fiex-tu">
              <div class="left"></div>
              <div class="right">
                <div>应巡</div>
                <div class="right-bot point-cursor">
                  <span>{{ spaceData.shouldMaintainNum || 0 }}</span>
                  <span>个</span>
                </div>
              </div>
            </div>
            <div class="fiex-tu">
              <div class="left"></div>
              <div class="right">
                <div>已巡</div>
                <div class="right-bot point-cursor">
                  <span>{{ spaceData.alreadyMaintainNum  || 0 }}</span>
                  <span>个</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </ModuleCard>
    <ModuleCard title="施工巡检统计" style="height: calc(80%)" class="container-box">
      <div slot="title-right">
        <div class="chart-icon" @click="handleAllCollapsed">
          <span>全部{{ collapseText }}</span>
          <span style="margin-left: 5px" :class="collapseIcon"></span>
        </div>
      </div>
      <div slot="content" v-loading="deviceTableLoading" class="realTime-module">
        <div v-for="(item, index) in inspectionData" :key="index" class="realTime-module-box">
          <div class="rtm-header-box">
            <div class="rtm-header-title">
              <p class="title-text">{{ item.workName || '-' }}</p>
              <p class="title-status" :style="{color: `rgba(${planStatusObj[item.planStatus].color} ,1)`, background: `rgba(${planStatusObj[item.planStatus].color} ,0.2)`}">
                {{ planStatusObj[item.planStatus].name }}
              </p>
            </div>
            <div class="rtm-header-btn-text" @click.stop="handleCollapseClick(index)">
              <span>{{ item.collapsed ? "展开" : "收起" }}</span>
              <span style="margin-left: 5px" :class="item.collapsed ? 'el-icon-arrow-down' : 'el-icon-arrow-up'"></span>
            </div>
          </div>
          <div class="rtm-status-box">
            <div v-for="(v, i) in statusData" :key="i" class="rtm-status">
              <div class="rtm-status-title">{{ v.name }}</div>
              <div class="rtm-status-value">
                {{ item[v.key] }}
              </div>
            </div>
          </div>
          <div v-if="!item.collapsed" class="rtm-table-box">
            <el-table
              v-el-table-infinite-scroll="tableLoadMore"
              v-scrollHideTooltip
              class="table-center-transfer"
              :data="item.operationRecordList"
              height="100%"
              :cell-style="{ padding: ' 8px', backgroundColor: 'transparent', border: 'none', padding: '3px',}" :header-cell-style="{ background: '#8591CE26!important', color: '#8BDDF5FF', padding: '4px 8px', fontWeight: 'bold',}"
              style="width: 100%"
              @row-dblclick="tabledblClick"
            >
              <el-table-column prop="taskName" show-overflow-tooltip label="巡检名称" width="120"></el-table-column>
              <el-table-column prop="workStartTime" show-overflow-tooltip label="应巡日期" width="120"></el-table-column>
              <el-table-column prop="cycleType" show-overflow-tooltip label="周期类型" width="95">
                <template slot-scope="scope">
                  {{ typeOptions[scope.row.cycleType] }}
                </template>
              </el-table-column>
              <el-table-column prop="actualExecutionUserName" show-overflow-tooltip label="巡检人员" width="95"></el-table-column>
              <el-table-column prop="taskStatus" show-overflow-tooltip label="完成状态" width="95">
                <template slot-scope="scope">
                  <div class="table-icon" :style="{ color: scope.row.taskStatus == 1 ? '#61E29D' : '#FF2D55' }">
                    <img v-if="scope.row.taskStatus == '0'" src="@/assets/images/icon-2.png" />
                    <img v-else src="@/assets/images/icon-5.png" />
                    <span>{{ scope.row.taskStatus == 1 ? '已完成' : '未完成'}}</span>
                  </div>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </div>
      </div>
    </ModuleCard>
    <!-- 更多 -->
    <dialogFrame :visible="visible" :breadcrumb="breadcrumb" :title="title" @back="back" @update:visible="closeDialogFrame">
      <component :is="activeComponent" :currentItem="currentItem" @openDetailComponent="openDetailComponent"></component>
    </dialogFrame>
  </div>
</template>
<script>
import { countMaintainPlanBySpaceId, constructionPatrolList } from '@/utils/spaceManage'
export default {
  components: {
    dialogFrame: () => import('@/components/common/DialogFrame'),
    patrolList: () => import('./components/patrolList.vue'),
    patrolDetail: () => import('./components/patrolDetail.vue')
  },
  props: {
    roomData: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      deviceTableLoading: false,
      dateActive: 1,
      dateTagList: [
        { text: '今日', value: 1 },
        { text: '本周', value: 2 },
        { text: '本月', value: 3 },
        { text: '本年', value: 4 }
      ],
      statusData: [
        { name: '作业类型：', key: 'workTypeName' },
        { name: '巡查进度：', key: 'maintainSchedule' },
        { name: '巡查位置：', key: 'workLocationName' }
      ],
      planStatusObj: {
        0: { name: '未完成', color: '255, 45, 85' },
        1: { name: '已完成', color: '97, 226, 157' },
        2: { name: '进行中', color: '60, 193, 255' }
      },
      typeOptions: {
        8: '单次',
        6: '每日',
        0: '每周',
        2: '每月',
        3: '季度',
        5: '全年'
      },

      allCollapsed: true,
      pagination: {
        total: 0,
        pageSize: 15,
        pageNo: 1
      },
      inspectionData: [],
      // 空间管理数据
      spaceData: {
        alreadyMaintainNum: 0,
        shouldMaintainNum: 0
      },
      visible: false,
      breadcrumb: [{ label: '巡查列表', name: 'patrolList' }],
      activeComponent: 'patrolList',
      title: '巡查列表',
      currentItem: {}
    }
  },
  computed: {
    collapseText() {
      return this.allCollapsed ? '展开' : '收起'
    },
    collapseIcon() {
      return this.allCollapsed ? 'el-icon-arrow-down' : 'el-icon-arrow-up'
    }
  },
  watch: {
    visible(val) {
      try {
        window.chrome.webview.hostObjects.sync.bridge.ShowDialog(val)
      } catch (error) {}
    },
    dateActive(val) {
      this.pagination.pageNo = 1
      try {
        window.chrome.webview.hostObjects.sync.bridge.WorkOrderScreening(JSON.stringify({ btnType: val }))
      } catch (err) {}
      this.countMaintainPlanBySpaceId()
      this.constructionPatrolList()
    }
  },
  mounted() {
    this.countMaintainPlanBySpaceId()
    this.constructionPatrolList()
  },
  methods: {
    tabledblClick(row) {
      this.$emit('roomEvent', {
        type: 'insp',
        assetId: row.id,
        assetName: row.taskName,
        modelCode: row.id
      })
      try {
        window.chrome.webview.hostObjects.sync.bridge.EquipmentRealView(JSON.stringify({
          assetsId: row.id,
          spaceId: row.localId
        }))
      } catch (error) {}
    },
    constructionPatrolList() {
      let params = {
        spaceLevel: this.roomData.ssmType,
        spaceCode: this.roomData.ssmCodes?.split(',')?.at(-1),
        btnType: this.dateActive,
        currentPage: this.pagination.pageNo,
        size: this.pagination.pageSize
      }
      this.deviceTableLoading = true
      if (this.pagination.pageNo == 1) this.inspectionData = []
      constructionPatrolList(params).then(res => {
        this.deviceTableLoading = false
        if (res.data.code == 200) {
          console.log(res.data.data)
          res.data.data.list.forEach(item => {
            item.collapsed = true
          })
          this.inspectionData = this.inspectionData.concat(res.data.data.list)
          this.pagination.total = res.data.data.count
        }
      }).catch(() => {
        this.deviceTableLoading = false
      })
    },
    // 巡检统计
    countMaintainPlanBySpaceId() {
      let params = {
        spaceLevel: this.roomData.ssmType,
        spaceCode: this.roomData.ssmCodes?.split(',')?.at(-1),
        btnType: this.dateActive
      }
      countMaintainPlanBySpaceId(params).then(res => {
        console.log(res)
        if (res.data.code == 200) {
          this.spaceData = res.data.data
        }
      })
    },
    closeDialogFrame() {
      this.visible = false
      this.activeComponent = 'patrolList'
      this.title = '巡查列表'
      this.breadcrumb = [{ label: '巡查列表', name: 'patrolList' }]
    },
    /** 组件返回上一级 */
    back(name) {
      this.activeComponent = name
      let arr = []
      for (let i = 0; i < this.breadcrumb.length - 1; i++) {
        arr.push(this.breadcrumb[i])
        if (this.breadcrumb[i].name == name) {
          break
        }
      }
      this.breadcrumb = arr
      if (this.breadcrumb.length) {
        this.title = this.breadcrumb.find((item) => item.name === name).label
      }
    },
    // 组件内部操作
    openDetailComponent(params) {
      this.activeComponent = params.key
      this.currentItem = params.item
      let obj = {
        patrolDetail: { label: '任务详情', name: 'patrolDetail' }
      }
      this.breadcrumb.push(obj[params.key])
      this.title = ''
    },
    // 详情
    allTableChange() {
      this.visible = true
    },
    /** 全部展开/收起 */
    handleAllCollapsed() {
      this.allCollapsed = !this.allCollapsed
      this.inspectionData.forEach((item, i) => {
        item.collapsed = this.allCollapsed
      })
    },
    /** 展开/收起 */
    handleCollapseClick(index) {
      this.inspectionData[index].collapsed =
        !this.inspectionData[index].collapsed
      this.allCollapsed = this.inspectionData.every((item) => item.collapsed)
    },
    tableLoadMore() {
      if (
        this.pagination.total >
        this.pagination.pageNo * this.pagination.pageSize
      ) {
        this.pagination.pageNo++
        if (this.sortType == '1') {
          //   this.getAssetsTypeList({
          //     entityTypeId: this.sendCheckData.entityTypeId,
          //   });
        } else {
          //   this.getListGroupData({ spaceId: this.sendCheckData.spaceId });
        }
      }
    }
  }
}
</script>
<style lang="scss" scoped>
%flex-styles {
  display: flex;
  align-items: center;
}
.chart-title-box {
  @extend %flex-styles;
  cursor: pointer;
  .el-dropdown-link {
    font-size: 14px;
    font-weight: 300;
    color: #ffffff;
    line-height: 16px;
    .el-icon-arrow-down {
      font-size: 12px;
    }
  }
}
.chart-icon {
  cursor: pointer;
  font-size: 14px;
  margin-left: 10px;
  color: #8BDDF5;
}
.realTime-module {
  height: 100%;
  .realTime-module-box {
    // background: #8591ce26;
    margin-top: 10px;
    .rtm-header-box {
      padding: 0px 10px;
      @extend %flex-styles;
      justify-content: space-between;
      .rtm-header-btn-text {
        font-size: 14px;
        cursor: pointer;
      }
      .rtm-header-title{
        display: flex;

        .title-text {
          font-weight: 500;
          font-size: 15px;
          color: #FFFFFF;
          line-height: 20px;
        }
        .title-status {
          font-weight: 400;
          font-size: 14px;
          line-height: 16px;
          padding: 3px 8px;
          border-radius: 100px 100px 100px 100px;
          margin-left: 10px;
        }
      }
    }
    .rtm-status-box {
      padding: 16px 10px 10px 10px;
      .rtm-status {
        display: flex;
        align-items: center;
        margin-top: 10px;
        .rtm-status-title {
          width: 93px;
          font-weight: 400;
          font-size: 14px;
          color: #C4F3FE;
          line-height: 16px;
        }
        .rtm-status-value {
          flex: 1;
          font-size: 14px;
          text-align: center;
          line-height: 16px;
          text-align: left;
        }
      }
    }
    .rtm-table-box{
      .table-icon {
        display: flex;
        align-items: center;
        img {
          width: 16px;
          margin-right: 3px;
        }
        span {
          line-height: 16px;
        }
      }
    }
  }
}
.top-content {
  // height: calc(32%);
  // background: rgba(133, 145, 206, 0.15);
  display: flex;
  flex-direction: column;
  .top-fiex {
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: space-around;
    .top-fiex-div {
      display: flex;
      // padding: 10px 8px 2px;
      .fiex-tu {
        width: 50%;
        display: flex;
        align-items: center;

        .left {
          width: 50.94px;
          height: 56.15px;
          background: url("@/assets/images/qhdsys/xj-icon.png") no-repeat;
        }

        .left-to {
          background: url("@/assets/images/qhdsys/xj-icon.png") no-repeat;
        }

        .right {
          display: flex;
          flex-direction: column;
          width: 124px;
          // height: 68px;

          .right-bot {
            width: 100%;
            // height: 100%;
            margin-top: 6px;
            display: flex;
            justify-content: center;
            background: url("@/assets/images/qhdsys/bg-tu.png") no-repeat;
          }

          .right-bot span:nth-child(1) {
            margin-top: 5px;
            //   width: 37px;
            height: 23px;
            font-size: 17px;
            font-family: HarmonyOS Sans SC-Bold, HarmonyOS Sans SC;
            font-weight: bold;
            color: #ffca64;
            line-height: 23px;
            // -webkit-background-clip: text;
          }

          .right-bot span:nth-child(2) {
            margin-top: 10px;
            margin-left: 3px;
            width: 14px;
            height: 16px;
            font-size: 13px;
            font-family: HarmonyOS Sans SC-Light, HarmonyOS Sans SC;
            font-weight: 300;
            color: rgba(255, 255, 255, 0.5);
            line-height: 16px;
          }
        }

        .right div:nth-child(1) {
          font-size: 14px;
          color: #b0e3fa;
          margin-left: 18px;
        }
      }

      .fiex-tu:nth-child(1) {
        // margin-right: 32px;
      }
    }
  }
  .point-cursor {
    cursor: pointer;
  }
}
</style>
