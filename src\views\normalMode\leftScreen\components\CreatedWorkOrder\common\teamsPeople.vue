<template>
  <el-dialog
    v-dialogDrag
    custom-class="teamsPeople"
    append-to-body
    :show-close="false"
    :visible.sync="changeTeamsPeopleShow"
    :close-on-click-modal="false"
    :before-close="closeDialog"
    width="45%"
    title="服务人员选择"
  >
    <div class="dialog-right">
      <div class="dialog-tc" @click="closeDialog"></div>
    </div>
    <div class="dialog-content" style="width: 100%;">
      <el-table
        v-loading="tableLoading"
        :data="tableData"
        height="300"
        style="width: 100%;"
        element-loading-background="rgba(0, 0, 0, 0.2)"
        @selection-change="selectionChange"
      >
        <el-table-column fixed prop="deptName" show-overflow-tooltip label="服务部门"></el-table-column>
        <el-table-column fixed type="selection" width="45px"></el-table-column>
        <el-table-column fixed prop="member_name" show-overflow-tooltip label="服务人员"></el-table-column>
        <el-table-column fixed prop="phone" show-overflow-tooltip label="人员电话"></el-table-column>
        <el-table-column fixed prop="flag" show-overflow-tooltip label="派工状态">
          <template slot-scope="scope">
            <span class="redColor">{{ scope.row.flag }}</span>
          </template>
        </el-table-column>
        <el-table-column fixed prop="number" show-overflow-tooltip label="派工数量">
          <template slot-scope="scope">
            <span class="redColor">{{ scope.row.number }}</span>
          </template>
        </el-table-column>
        <el-table-column fixed prop="finishNumber" show-overflow-tooltip label="今日已完工">
          <template slot-scope="scope">
            <span class="redColor">{{ scope.row.finishNumber }}</span>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <span slot="footer" class="dialog-footer">
      <el-button class="sino-button-sure" @click="closeDialog">取 消</el-button>
      <el-button class="sino-button-sure" @click="savePeople">确 定</el-button>
    </span>
  </el-dialog>
</template>
<script>
import {
  getServicePersonName
} from '@/utils/peaceLeftScreenApi'
export default {
  name: 'teamsPeople',
  props: {
    changeTeamsPeopleShow: {
      type: Boolean,
      default: false
    },
    selectTeamsData: {
      type: Object,
      default: function () {
        return {}
      }
    }
  },
  data() {
    return {
      tableData: [],
      tableLoading: false,
      selectionParams: {}
    }
  },
  watch: {},
  mounted() {
    this.getDesignPersonByDept()
  },
  methods: {
    getDesignPersonByDept() {
      this.tableLoading = true
      getServicePersonName(this.selectTeamsData).then((res) => {
        const data = res.data.data.list
        data.map((e) => {
          e.deptName = this.selectTeamsData.deptName
        })
        this.tableData = data
        this.tableLoading = false
      })
    },
    selectionChange(selection) {
      // console.log(selection)
      if (selection.length) {
        // const person = Array.from(selection, ({ name }) => name)
        // const personSplit = selection.map((e) => {
        //   return e.id + '_' + e.name + '_' + e.mobile
        // })
        // this.selectionParams = {
        //   designatePerson: person.toString(),
        //   designatePersonValTransfer: personSplit.toString()
        // }
        this.selectionParams = selection
      } else {
        this.selectionParams = {}
      }
    },
    // 取消按钮
    closeDialog() {
      this.$emit('closeDialog')
    },
    // 确认按钮
    savePeople() {
      this.$emit('peopleSure', this.selectionParams)
      // if (Object.keys(this.selectionParams).length) {
      //   this.$emit('peopleSure', this.selectionParams)
      // }
    }
  }
}
</script>

<style lang="scss" type="text/css" scoped>
::v-deep .el-dialog{
  background: url('@/assets/images/qhdsys/864×478.png') no-repeat center center / 100% 100%;
  .el-dialog__header {
    padding: 10px 20px 10px;
    text-align: center;
    .el-dialog__title {
      height: 20px;
      font-size: 18px;
      font-family: Source Han Sans CN-Regular, Source Han Sans CN;
      font-weight: 400;
      color: #cbdeed;
      line-height: 18px;
    }
  }
  .el-dialog__footer {
    padding-top: 0;
    padding-bottom: 40px;
    padding-right: 40px;
  }
}
.teamsPeople {
  .dialog-right {
    position: absolute;
    top: 17px;
    right: 30px;
    display: flex;
    .dialog-tc {
      width: 36px;
      height: 36px;
      background: url('@/assets/images/qhdsys/bg-icon-gb.png') no-repeat;
    }
    .dialog-tc:hover {
      cursor: pointer;
    }
  }
  .el-table-column--selection .cell {
    padding-left: 0.875rem;
    padding-right: 0.875rem;
    text-overflow: initial;
  }
  .redColor {
    color: red;
  }
}
// ::v-deep .model-dialog {
//   .dialog-content {
//     width: 100%;
//     // background: #fff;
//     padding: 10px;
//   }
// }
</style>
