<template>
  <el-dialog
    v-if="visible"
    v-dialogDrag
    :modal="false"
    :close-on-click-modal="false"
    :show-close="false"
    title="警情确认"
    width="40%"
    :visible.sync="visible"
    custom-class="confirmAlarmDialog"
    :before-close="closeDialog"
  >
    <div class="dialog-right">
      <div class="dialog-tc" @click="closeDialog"></div>
    </div>
    <div class="content">
      <el-form ref="ruleForm" :model="form" :rules="rules">
        <el-form-item label="警情类型" prop="textarea">
          <el-radio-group v-model="form.radio" size="mini">
            <el-radio-button :label="2">误报</el-radio-button>
            <el-radio-button :label="1">真实报警</el-radio-button>
            <el-radio-button :label="3">演练</el-radio-button>
            <el-radio-button :label="4">调试</el-radio-button>
          </el-radio-group>
          <el-input v-model.trim="form.textarea" type="textarea" :rows="4" resize="none" placeholder="请输入备注" maxlength="100" show-word-limit style="width: 70%;" class="ipt"> </el-input>
        </el-form-item>
      </el-form>
    </div>
    <span slot="footer" class="dialog-footer">
      <el-button class="sino-button-sure" @click="closeDialog">取 消</el-button>
      <el-button class="sino-button-sure" @click="confirm('ruleForm')">确 定</el-button>
    </span>
  </el-dialog>
</template>
<script>
import {
  AlarmAffirm
} from '@/utils/peaceLeftScreenApi'
export default {
  name: 'confirmAlarmDialog',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    dialogDetail: {
      type: Object,
      default: () => {}
    },
    item: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  data() {
    return {
      form: {
        radio: 2,
        textarea: ''
      },
      rules: {
        textarea: [{ required: true, message: '请输入备注', trigger: 'change' }]
      }
    }
  },
  mounted() {},
  methods: {
    // 确警
    alarmAffirm() {
      const params = {
        alarmAffirm: this.form.radio,
        remark: this.form.textarea,
        alarmId: this.item.alarmId,
        projectCode: this.item.projectCode
      }
      AlarmAffirm(params).then((res) => {
        if (res.data.code === '200') {
          this.$emit('update:visible', !this.visible)
        } else {
          this.$message.error(res.data.msg)
        }
      })
    },
    // 关闭弹窗
    closeDialog() {
      this.$emit('update:visible', !this.visible)
    },
    confirm(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          this.alarmAffirm()
        }
      })
    }
  }
}
</script>
<style lang="scss" scoped>
::v-deep .el-dialog{
  background: url('@/assets/images/qhdsys/768×391.png') no-repeat center center / 100% 100%;
  .el-dialog__header {
    padding: 10px 20px 10px;
    text-align: center;
    .el-dialog__title {
      height: 20px;
      font-size: 18px;
      font-family: Source Han Sans CN-Regular, Source Han Sans CN;
      font-weight: 400;
      color: #cbdeed;
      line-height: 18px;
    }
  }
  .el-dialog__body {
    height: calc(100% - 40px);
  }
  .el-dialog__footer {
    padding-top: 0;
    padding-bottom: 40px;
    padding-right: 40px;
  }
}
.confirmAlarmDialog {
  .dialog-right {
    position: absolute;
    top: 17px;
    right: 30px;
    display: flex;
    .dialog-tc {
      width: 36px;
      height: 36px;
      background: url('@/assets/images/qhdsys/bg-icon-gb.png') no-repeat;
    }
    .dialog-tc:hover {
      cursor: pointer;
    }
  }
  .content {
    width: 100%;
    height: 100%;
    padding: 20px !important;
    // background-color: #fff !important;
    ::v-deep .el-radio-group {
      .el-radio-button__orig-radio:checked + .el-radio-button__inner {
        background: #3562db;
        border: none;
        color: #fff;
      }
      .el-radio-button {
        margin-right: 8px;
        border: none;
      }
      .el-radio-button__inner {
        background: rgba(53, 98, 219, 0.2);
        border: none;
        box-shadow: none;
        color: #fff;
      }
      .el-radio-button:last-child .el-radio-button__inner,
      .el-radio-button:first-child .el-radio-button__inner {
        border-radius: 0;
      }
      .el-radio-button:focus:not(.is-focus):not(:active):not(.is-disabled) {
        box-shadow: none;
        border: none;
      }
    }
  }
  .ipt {
    margin-top: 15px;
    margin-left: 80px;
  }
  ::v-deep .el-form-item__error {
    height: 20px;
    width: 300px;
    color: #f56c6c;
    font-size: 12px;
    line-height: 20px;
    padding-top: 4px;
    position: absolute;
    left: 78px;
  }
  ::v-deep .el-dialog__body {
    padding: 0 !important;
  }
}
</style>
