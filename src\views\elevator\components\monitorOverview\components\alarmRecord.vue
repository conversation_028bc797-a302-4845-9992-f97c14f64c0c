<template>
  <div class="dialog-content">
    <!-- 报警记录弹窗 -->
    <el-dialog :modal="false" :close-on-click-modal="false"  title="报警记录" :visible="dialogVisible" width="80%" :before-close="handleClose" class="dialog-url"  :show-close="false">
      <div class="dialog-right">
        <!-- <div class="dialog-fx" @click="statisticalExport"></div> -->
        <div class="dialog-tc" @click="handleClose"></div>
      </div>
      <div class="dialog-div">
        <div class="search-box">
          <div style="margin-left: 16px">
            <span>设备名称：</span>
            <el-input v-model="params.surveyName" placeholder="请输入设备名称"></el-input>
          </div>
          <div style="margin-left: 16px">
            <span>报警类型：</span>
            <el-select v-model="params.parameterId" placeholder="全部报警类型" filterable popper-class="new-select">
              <el-option v-for="item in alarmTypeList" :key="item.parameterId" :label="item.parameterName" :value="item.parameterId"> </el-option>
            </el-select>
          </div>
          <div style="margin-left: 16px">
            <span class="transY">报警时间：</span>
            <el-date-picker
              v-model="params.daterange"
              type="daterange"
              popper-class="date-style"
              value-format="yyyy-MM-dd"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
            >
            </el-date-picker>
          </div>

          <div style="margin-left: 16px">
            <span>处置状态：</span>
            <el-select v-model="params.disposeResult" placeholder="全部" filterable popper-class="new-select">
              <el-option v-for="item in handleResult" :key="item.id" :label="item.name" :value="item.id"> </el-option>
            </el-select>
          </div>
          <div class="search-btn">
            <el-button @click="resetSearch">重置</el-button>
            <el-button @click="handleSearch">查询</el-button>
          </div>
        </div>
        <el-table v-loading="tableLoading" :data="tableData" style="width: 100%">
          <!-- <el-table-column prop="roomCode" label="房间号" min-width="122" show-overflow-tooltip> </el-table-column> -->
          <el-table-column prop="surveyName" label="设备名称" min-width="122" show-overflow-tooltip> </el-table-column>
          <el-table-column prop="parameterName" label="报警类型" min-width="122" show-overflow-tooltip> </el-table-column>
          <el-table-column prop="policeReason" label="报警原因" min-width="122" show-overflow-tooltip> </el-table-column>
          <el-table-column prop="policeTime" label="报警时间" min-width="122" show-overflow-tooltip> </el-table-column>
          <el-table-column prop="disposeResultName" label="处置结果" min-width="122" show-overflow-tooltip>
            <template slot-scope="scope">
              <span :style="{ color: scope.row.disposeResult == '0' ? '#FF2D55' : '#61E29D' }"> {{ scope.row.disposeResultName }} </span>
            </template>
          </el-table-column>
          <el-table-column prop="functionDictName" label="操作" min-width="122" show-overflow-tooltip>
            <template slot-scope="scope">
              <span style="color: #5188fc; cursor: pointer; margin-right: 10px" @click="detail(scope.row)"> 详情 </span>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <div class="dialog-foot">
        <div class="foot-zs">共{{ total }}条</div>
        <div>
          <el-pagination layout="prev, pager, next" :total="total" :page-size="pageSize" :current-page="pageNo" @current-change="handleCurrentChange"> </el-pagination>
        </div>
      </div>
    </el-dialog>
    <el-dialog :modal="false" :close-on-click-modal="false"  title="报警详情" :visible="detailVisible" class="dialog-url" :before-close="detailClose" :show-close="false">
      <div class="dialog-right">
        <div class="dialog-tc" @click="detailClose"></div>
      </div>
      <div class="dialog-div">
        <div class="maintain-content-block" style="min-height: 150px">
          <div class="maintain-title">
            <span class="title-tag"></span>
            <span class="title-text">{{ header.titleOne }}</span>
          </div>
          <div class="maintain-list">
            <div
              v-for="(item, index) in option"
              :key="index"
              :class="[!item.remake ? 'maintain-list-item' : 'maintain-list-block']"
              :style="{
                display: item.inline || item.remake ? 'block' : 'inline-block'
              }"
            >
              <template>
                <label style="display: flex" class="list-item">
                  <span class="label">{{ item.label }}</span>
                  <span
                    class="value"
                    style="width: calc(100% - 100px); text-overflow: ellipsis; white-space: nowrap; overflow: hidden; display: inline-block"
                    :title="getTaskValue(taskDetail[item.value])"
                  >{{ getTaskValue(taskDetail[item.value]) }}</span
                  >
                </label>
              </template>
            </div>
            <div class="maintain-list-block">
              <label style="display: flex" class="list-item">
                <span class="label">联动摄像头：</span>
                <span v-for="(item, index) in videoTabsList" :key="index" class="value" style="margin-right: 10px; cursor: pointer; color: #5188fc">{{ item.vidiconName }}</span>
                <span v-if="!videoTabsList.length" style="color: rgb(144 147 153)">暂无关联摄像头</span>
              </label>
            </div>
          </div>
        </div>
        <div class="maintain-content-block">
          <div class="maintain-title">
            <span class="title-tag"></span>
            <span class="title-text">{{ header.titleTwo }}</span>
          </div>
          <div class="maintain-list">
            <div
              v-for="(item, index) in header.detailList"
              :key="index"
              :class="[!item.remake ? 'maintain-list-item' : 'maintain-list-block']"
              :style="{
                display: item.inline || item.remake ? 'block' : 'inline-block'
              }"
            >
              <template>
                <label style="display: flex" class="list-item">
                  <span class="label">{{ item.label }}</span>
                  <span
                    class="value"
                    style="width: calc(100% - 100px); text-overflow: ellipsis; white-space: nowrap; overflow: hidden; display: inline-block"
                    :title="getTaskValue(taskDetail[item.value])"
                  >{{ getTaskValue(taskDetail[item.value]) }}</span
                  >
                </label>
              </template>
            </div>
            <div>
              <div class="form-box">
                <el-form ref="formInline" :model="formInline" label-width="130px">
                  <el-form-item label="处置结果：" prop="disposeResult">
                    <span>
                      {{ taskDetail.disposeResultName }}
                    </span>
                  </el-form-item>
                  <el-form-item label="处置时间：" prop="disposeTime">
                    <span>{{ formInline.disposeTime || '无' }}</span>
                  </el-form-item>
                  <el-form-item label="处置结果说明：">
                    <span>{{ taskDetail.disposeText || '无' }}</span>
                  </el-form-item>
                  <el-form-item label="相关附件：" prop="file">
                    <div style="color: #5188fc; cursor: pointer">
                      <div v-for="(item, i) of urlList" :key="i">
                        <div @click="downLoad(item)">{{ item }}</div>
                      </div>
                    </div>
                  </el-form-item>
                </el-form>
              </div>
            </div>
          </div>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getPoliceList } from '@/utils/peaceLeftScreenApi.js'
import { getPoliceView, getVideoList } from '@/utils/elevatorApi.js'
export default {
  name: '',
  model: {
    prop: 'dialogVisible',
    event: 'close'
  },
  props: {
    dialogVisible: {
      type: Boolean,
      default: false
    },
    alarmTypeList: {
      type: Array,
      default: () => []
    },
    projectCode: {
      type: String,
      default: ''
    },
    // handlerResult: {
    //   type: [Number, String],
    //   default: ''
    // },
    paramsData: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      handleResult: [
        { id: 0, name: '未处置' },
        { id: 1, name: '已确认' },
        { id: 2, name: '误报' }
      ],
      params: {
        surveyName: '',
        parameterId: '',
        daterange: [],
        disposeResult: ''
      },

      tableData: [],
      //   分页
      pageNo: 1,
      pageSize: 15,
      total: 0,
      tableLoading: false,
      detailVisible: false,
      detailId: '',
      blockLoading: false,
      option: [],
      taskDetail: [],
      videoTabsList: [],
      header: {
        title: '报警中心',
        titleOne: '报警基本信息',
        titleTwo: '报警处置',
        detailNode: [
          { label: '设备名称：', value: 'surveyName', remake: false },
          { label: '报警参数名称：', value: 'parameterName', remake: false },
          { label: '报警原因：', value: 'policeReason', remake: false },
          { label: '报警数值：', value: 'policeNumber', remake: false },
          { label: '报警上限：', value: 'upperThreshold', remake: false, notHasProject: '电梯监测' },
          { label: '报警下限：', value: 'downThreshold', remake: false, notHasProject: '电梯监测' },
          { label: '报警时间：', value: 'policeTime', remake: false },
          { label: '报警处置用时：', value: 'disposeUseTime', remake: false, notHasDeal: true },
          { label: '地理位置：', value: 'gridName', remake: true }
        ],
        detailList: [
          { label: '处置人员：', value: 'personSet', width: '200' },
          { label: '处置人员电话：', value: 'personPhone', width: '200' }
        ]
      },
      formInline: {
        disposeResult: '',
        disposeText: '',
        disposeTime: '',
        file: []
      },
      urlList: []
    }
  },
  computed: {},
  watch: {},
  created() {
    Object.assign(this.params, this.paramsData)
    // this.params.disposeResult = this.handlerResult
    this.getAlarmRecordList()
  },
  mounted() {},
  methods: {
    // 推出
    handleClose() {
      try {
        // window.chrome.webview.hostObjects.sync.bridge.ShowTable(false)
        window.chrome.webview.hostObjects.sync.bridge.ShowDialog(false)
      } catch (error) {}
      this.$emit('close', false)
    },
    resetSearch() {
      this.params = {
        surveyName: '',
        parameterId: '',
        daterange: [],
        disposeResult: '',
        regionCode: this.paramsData.regionCode
      }
      this.pageSize = 15
      this.pageNo = 1
      this.getAlarmRecordList()
    },
    handleSearch() {
      this.getAlarmRecordList()
    },
    // 获取列表
    getAlarmRecordList() {
      this.tableLoading = true
      const params = {
        ...this.params,
        ...this.paramsData,
        pageNo: this.pageNo, // 当前页
        projectCode: this.projectCode, // 模型编码
        pageSize: this.pageSize, // 每页条数
        alarmLevelId: '',
        startTime: this.params.daterange[0] || '',
        endTime: this.params.daterange[1] || ''
      }
      getPoliceList(params).then((res) => {
        const { data } = res
        if (data.code === '200') {
          this.tableLoading = false
          this.tableData = data.data.list
          this.total = data.data.count
        } else {
          this.tableLoading = false
        }
      })
    },
    handleCurrentChange(val) {
      this.pageNo = val
      this.getAlarmRecordList()
    },
    detail(row) {
      this.getDetails(row.id)
      this.detailVisible = true
    },
    getTaskValue(value) {
      if (!value && String(value) !== '0') {
        return '无'
      } else {
        return value
      }
    },
    getDetails(id) {
      this.blockLoading = true
      this.option = this.header.detailNode.filter((item) => item.notHasProject !== '电梯监测')

      //  接数据
      getPoliceView({ id: id }).then((res) => {
        this.blockLoading = false
        this.taskDetail = res.data.data
        this.getVideoList()
        // this.taskDetail.assetId = '1725123552660684806'
        if (this.taskDetail.assetId) {
          this.getDeviceBasicInfo()
          this.dateTypeSwitch('日')
        }
        this.urlList = res.data.data.attachmentUrl ? res.data.data.attachmentUrl.split(',') : []
      })
    },
    // 摄像机列表
    getVideoList() {
      getVideoList({
        surveyCode: this.taskDetail.surveyCode
      }).then((res) => {
        if (res.code === '200') {
          this.videoTabsList = res.data
        }
      })
    },
    detailClose() {
      // try {
      //   window.chrome.webview.hostObjects.sync.bridge.ShowDialog(false)
      // } catch (error) {}
      this.detailVisible = false
    }
  }
}
</script>
<style lang="scss" scoped>
.dialog-content {
  position: relative;
  .dialog-url {
    ::v-deep .el-dialog {
      height: 653px;
      background: url('@/assets/images/qhdsys/bg-tc.png') no-repeat;
      background-size: 100% 100%;
      .el-dialog__header {
        padding: 11px 20px 10px;
        text-align: center;
        .el-dialog__title {
          height: 20px;
          font-size: 18px;
          font-family: Source Han Sans CN-Regular, Source Han Sans CN;
          font-weight: 400;
          color: #cbdeed;
          line-height: 20px;
        }
      }
    }
    .dialog-div {
      padding: 0 65px;
      height: 100%;
      overflow-y: auto;
      ::v-deep .el-table {
        border: 1px solid #203254 !important;
        .el-table__header-wrapper {
          .el-table__header {
            .has-gutter {
              tr {
                background: rgba(133, 145, 206, 0.15);
                // border-bottom: 2px solid #ffffff;
                th {
                  padding: 0;
                  height: 44px;
                  .cell {
                    font-size: 14px;
                    font-family: HarmonyOS Sans SC-Medium, HarmonyOS Sans SC;
                    font-weight: 500;
                    color: #8bddf5;
                  }
                }
              }
            }
          }
        }
        .el-table__body-wrapper {
          background: transparent;
          height: 360px;
          overflow: hidden;
          overflow-y: auto;
          .el-table__body {
            tbody {
              .el-table__row {
                background: transparent;
                border: 0;
                td {
                  padding: 0;
                  height: 40px;
                  border: 0;
                  .cell {
                    font-size: 14px;
                    font-family: HarmonyOS Sans SC-Regular, HarmonyOS Sans SC;
                    font-weight: 400;
                    color: #ffffff;
                  }
                }
              }
              .el-table__row:nth-child(2n - 1) {
                background: rgba(168, 172, 171, 0.08);
              }
              .el-table__row:hover {
                border: 0;
                opacity: 1;
                cursor: pointer;
                td div {
                  color: rgba(255, 202, 100, 1);
                }
              }
            }
          }
        }
      }
    }
    .dialog-foot {
      padding: 0 65px;
      margin-top: 41px;
      display: flex;
      align-items: center;
      justify-content: space-between;
      .foot-zs {
        font-size: 14px;
        font-family: HarmonyOS Sans SC-Regular, HarmonyOS Sans SC;
        font-weight: 400;
        color: #ffffff;
        line-height: 14px;
      }
      ::v-deep .el-pagination {
        .btn-prev {
          background: transparent;
          .el-icon {
            color: #ffffff;
          }
        }
        .btn-next {
          background: transparent;
          .el-icon {
            color: #ffffff;
          }
        }
      }
    }
    .dialog-right {
      position: absolute;
      top: 17px;
      right: 60px;
      display: flex;
      .dialog-fx {
        width: 36px;
        height: 36px;
        margin-right: 8px;
        background: url('@/assets/images/qhdsys/export.png') no-repeat;
      }
      .dialog-fx:hover {
        cursor: pointer;
      }
      .dialog-tc {
        width: 36px;
        height: 36px;
        background: url('@/assets/images/qhdsys/bg-icon-gb.png') no-repeat;
      }
      .dialog-tc:hover {
        cursor: pointer;
      }
    }
  }
}
::v-deep .el-loading-mask {
  background: rgb(13, 27, 54, 0.9) !important;
}
::v-deep .search-box {
  display: flex;
  flex-wrap: wrap;
  margin: 16px 0;

  .el-button {
    background-image: url('@/assets/images/btn.png');
    background-repeat: no-repeat;
    background-size: 100% 100%;
    height: 35px;
  }
  .search-btn {
    margin-left: auto;
  }

  .el-select {
    .el-input .el-input__inner {
      border-color: rgba(133, 145, 206, 0.5);
      width: 150px;
      height: 35px;
    }
  }
  .el-cascader {
    .el-input__inner {
      height: 35px !important;
    }
  }
  .el-date-editor {
    width: 290px;
    height: 35px;
    background-color: #14233e;
    border-color: rgba(133, 145, 206, 0.5);
    .el-input__icon {
      transform: translateY(-2px);
    }
    .el-range-input {
      background-color: #14233e;
      color: rgba(255, 255, 255, 0.8);
    }
    .el-range-separator {
      color: rgba(255, 255, 255, 0.8);
      transform: translateY(-2px);
    }
  }
  .el-input .el-input__inner {
    border-color: rgba(133, 145, 206, 0.5);
    border-radius: 4px;
    color: rgba(255, 255, 255, 0.8);
  }
}
.search-box  .el-input {
  width: 150px;
  height: 35px;
}

::v-deep .el-date-table {
  td {
    &.disabled {
      div {
        background: #7c8087;
      }
    }
  }
}
.maintain-content-block {
  margin-bottom: 20px;

  .maintain-title {
    height: 40px;
    line-height: 40px;
    position: relative;
    .title-tag {
      display: inline-block;
      width: 4px;
      height: 16px;
      position: relative;
      top: 3px;
      background: #5188fc;
    }

    .title-text {
      padding-left: 5px;
      font-size: 14px;
      font-weight: 600;
      color: #fff;
      // color: rgb(96 98 102 / 100%);
    }
    ::v-deep .el-radio-group {
      position: absolute;
      right: 0;
      top: 50%;
      transform: translateY(-50%);
      .el-radio-button__inner {
        padding: 5px 12px;
      }
    }
  }

  .maintain-list {
    padding: 0 0 0 26px;

    .maintain-list-item {
      display: inline-block;
      width: 33%;
      overflow: hidden;
      text-overflow: ellipsis;

      .list-item {
        display: block;
        height: 40px;
        line-height: 40px;

        .label {
          display: inline-block;
          width: 130px;
          text-align: right;
          font-weight: 400;
          color: #7eaef9;
          // color: rgb(96 98 102 / 100%);
        }

        .value {
          font-weight: 400;
          color: #fff;
          // color: rgb(144 147 153 / 100%);
        }
      }
    }

    .maintain-list-block {
      // height: 40px;
      line-height: 20px;

      .list-item {
        line-height: 40px;
      }

      .label {
        display: inline-block;
        width: 130px;
        vertical-align: top;
        text-align: right;
        font-weight: 400;
        color: #7eaef9;
        // color: rgb(96 98 102 / 100%);
      }

      .value {
        display: inline-block;
        // width: 880px;
        font-weight: 400;
        color: #fff;
        // color: rgb(144 147 153 / 100%);
      }
    }
  }
  .form-box {
    ::v-deep .el-form {
      .el-form-item {
        margin: 0;
      }
      .el-form-item__label {
        color: #7eaef9;
      }
      .el-form-item__content span {
        color: #FFF;
      }
    }
  }
}
</style>
