// 空间楼层基础信息表格数据
export const baseTableInfo = [
  {
    label: '位置',
    value: '',
    field: 'simName',
    envSign: ['bjsjtyy', 'bjsyzyyy']
  },
  {
    label: '空间名称',
    value: '',
    field: 'localSpaceName',
    envSign: ['bjsjtyy', 'bjsyzyyy']
  },
  {
    label: '本地编码',
    value: '',
    field: 'localSpaceCode',
    envSign: ['bjsjtyy', 'bjsyzyyy']
  },
  {
    label: '功能类型',
    value: '',
    field: 'functionDictName',
    envSign: ['bjsjtyy', 'bjsyzyyy']
  },
  {
    label: '空间状态',
    value: '',
    field: 'spaceState',
    envSign: ['bjsjtyy', 'bjsyzyyy']
  },
  {
    label: '建筑面积',
    value: '',
    field: 'area',
    envSign: ['bjsjtyy']
  },
  {
    label: '使用面积',
    value: '',
    field: 'useArea',
    envSign: ['bjsjtyy']
  },
  {
    label: '空间高度',
    value: '',
    field: 'hight',
    envSign: ['bjsjtyy', 'bjsyzyyy']
  },
  {
    label: '模型编码',
    value: '',
    field: 'modelCode',
    envSign: ['bjsjtyy']
  },
  {
    label: '房间编号',
    value: '',
    field: 'roomCode',
    envSign: ['bjsyzyyy']
  },
  {
    label: '',
    value: '',
    field: '',
    envSign: ['bjsjtyy', 'bjsyzyyy']
  },
  {
    label: '',
    value: '',
    field: 'centerLine',
    envSign: ['bjsjtyy', 'bjsyzyyy']
  },
  {
    label: '',
    value: '',
    field: '',
    envSign: ['bjsjtyy', 'bjsyzyyy']
  },
  {
    label: '使用部门',
    value: '',
    field: 'dmName',
    envSign: ['bjsjtyy', 'bjsyzyyy']
  },
  {
    label: '部门电话',
    value: '',
    field: 'deptPhone',
    envSign: ['bjsjtyy', 'bjsyzyyy']
  },
  {
    label: '组织机构编码',
    value: '',
    field: 'officeCode',
    envSign: ['bjsjtyy', 'bjsyzyyy']
  },
  {
    label: '财务核算编码',
    value: '',
    field: 'financeCode',
    envSign: ['bjsjtyy', 'bjsyzyyy']
  },
  {
    label: '部门负责人:',
    value: '',
    field: 'principalName',
    envSign: ['bjsjtyy', 'bjsyzyyy']
  },
  {
    label: '负责人电话',
    value: '',
    field: 'principalPhone',
    envSign: ['bjsjtyy', 'bjsyzyyy']
  },
  {
    label: '',
    value: '',
    field: '',
    envSign: ['bjsjtyy', 'bjsyzyyy']
  },
  {
    label: '',
    value: '',
    field: 'centerLine',
    envSign: ['bjsjtyy', 'bjsyzyyy']
  },
  {
    label: '',
    value: '',
    field: '',
    envSign: ['bjsjtyy', 'bjsyzyyy']
  },
  {
    label: '空间二维码',
    value: '',
    field: 'qrcodeBase64',
    type: 'img',
    envSign: ['bjsjtyy', 'bjsyzyyy']
  }
]
export const dialogTypeList = [
  {
    title: '服务工单台账',
    type: 'ioms',
    height: 'calc(100% - 200px)'
  },
  {
    title: '科室医废收集记录',
    type: 'imws',
    height: 'calc(100% - 200px)'
  },
  {
    title: '隐患清单',
    type: 'danger',
    height: 'calc(100% - 200px)'
  },
  {
    title: '空间清单',
    type: 'space',
    height: 'calc(100% - 50px)'
  },
  {
    title: '风险点列表',
    type: 'risk',
    height: 'calc(100% - 200px)'
  },
  {
    title: '巡查任务',
    type: 'task',
    height: 'calc(100% - 200px)'
  },
  {
    title: '巡检任务',
    type: 'icis',
    height: 'calc(100% - 140px)'
  },
  {
    title: '保养任务',
    type: 'upkeep',
    height: 'calc(100% - 140px)'
  }
]
export const iomsWorkOrderParams = {
  typeSources: '',
  contrastType: '',
  free1: '',
  free2: '',
  workTypeCode: '',
  urgencyDegree: '',
  disDegree: '',
  disDegreeNew: '',
  workSources: '',
  feedbackFlag: '',
  responseTime: '',
  responseTimeType: '',
  transportTypeCode: '',
  isPack: '',
  releaseType: '',
  statementFlagCode: '',
  showTimeType: 'all',
  hqfwsj: 'hqfwsj',
  startTime: '',
  endTime: '',
  sectionStartDate: '',
  sectionEndDate: '',
  sectionStartTime: '',
  sectionEndTime: '',
  sourcesDept: '',
  region: '',
  buliding: '',
  storey: '',
  room: '',
  localtionName: '',
  replyToCode: '',
  sourcesPhone: '',
  itemDetailCode: '',
  itemDetailName: '',
  itemTypeCode: '',
  itemTypeName: '',
  itemServiceCode: '',
  itemServiceName: '',
  itemList: '',
  questionDescription: '',
  workNum: '',
  restaurantId: '',
  haveStartTime: '',
  haveEndTime: '',
  orderBy: ''
}
export const pipelineParams = {
  UDataType: 302,
  Data: [
    { PipelineCode: '', PipelineCategory: 'HF', Visibility: 1 },
    { PipelineCode: '', PipelineCategory: 'PF', Visibility: 1 },
    { PipelineCode: '', PipelineCategory: 'PY', Visibility: 1 },
    { PipelineCode: '', PipelineCategory: 'SF', Visibility: 1 },
    { PipelineCode: '', PipelineCategory: 'XF', Visibility: 1 },
    { PipelineCode: '', PipelineCategory: 'FS', Visibility: 1 },
    { PipelineCode: '', PipelineCategory: 'GASP', Visibility: 1 },
    { PipelineCode: '', PipelineCategory: 'JS', Visibility: 1 },
    { PipelineCode: '', PipelineCategory: 'KTS', Visibility: 1 },
    { PipelineCode: '', PipelineCategory: 'WS', Visibility: 1 }
  ]
}
export const pipelineData = [
  { BGclolor: 'rgb(225, 000, 255)', PipelineName: '回风系统', PipelineCategory: 'HF' },
  { BGclolor: 'rgb(255, 127, 000)', PipelineName: '排风系统', PipelineCategory: 'PF' },
  { BGclolor: 'rgb(255, 192, 000)', PipelineName: '排烟系统', PipelineCategory: 'PY' },
  { BGclolor: 'rgb(000, 255, 255)', PipelineName: '送风系统', PipelineCategory: 'SF' },
  { BGclolor: 'rgb(000, 255, 000)', PipelineName: '新风系统', PipelineCategory: 'XF' },
  { BGclolor: 'rgb(255, 128, 000)', PipelineName: '废水系统', PipelineCategory: 'FS' },
  { BGclolor: 'rgb(000, 201, 153)', PipelineName: '气体管道', PipelineCategory: 'GASP' },
  { BGclolor: 'rgb(000, 255, 000)', PipelineName: '给水系统', PipelineCategory: 'JS' },
  { BGclolor: 'rgb(000, 191, 255)', PipelineName: '空调水', PipelineCategory: 'KTS' },
  { BGclolor: 'rgb(255, 119, 000)', PipelineName: '污水系统', PipelineCategory: 'WS' }
]
export const refrigeratorParams = [
  { FloorCode: '', CategoryCode: '0100101_1', Visibility: 1 },
  { FloorCode: '', CategoryCode: '0100101_2', Visibility: 1 },
  { FloorCode: '', CategoryCode: '0100101_3', Visibility: 1 },
  { FloorCode: '', CategoryCode: '0100101_4', Visibility: 1 },
  { FloorCode: '', CategoryCode: '0100101_5', Visibility: 1 }
]
export const refrigeratorData = {
  szzlyy: [
    {
      PipeName: 'E-母线槽',
      BGclolor: 'RGB(000,000,127)',
      Visibility: 0,
      parentId: 'Electrical',
      PipeId: 'Electrical_01'
    },
    {
      PipeName: 'E-高压桥架',
      BGclolor: 'RGB(000,127,095)',
      Visibility: 0,
      parentId: 'Electrical',
      PipeId: 'Electrical_11'
    },
    {
      PipeName: 'E-照明线槽',
      BGclolor: 'RGB(127,191,255)',
      Visibility: 0,
      parentId: 'Electrical',
      PipeId: 'Electrical_21'
    },
    {
      PipeName: 'E-强电普通桥架',
      BGclolor: 'RGB(000,095,127)',
      Visibility: 0,
      parentId: 'Electrical',
      PipeId: 'Electrical_31'
    },
    {
      PipeName: 'E-强电消防桥架',
      BGclolor: 'RGB(127,000,000)',
      Visibility: 0,
      parentId: 'Electrical',
      PipeId: 'Electrical_41'
    },
    {
      PipeName: 'E-强电UPS桥架',
      BGclolor: 'RGB(0,128,192)',
      Visibility: 0,
      parentId: 'Electrical',
      PipeId: 'Electrical_51'
    },
    {
      PipeName: 'E-强电充电桩桥架',
      BGclolor: 'RGB(0,128,255)',
      Visibility: 0,
      parentId: 'Electrical',
      PipeId: 'Electrical_61'
    },
    {
      PipeName: 'EF-弱电消防桥架',
      BGclolor: 'RGB(127,000,127)',
      Visibility: 0,
      parentId: 'Electrical',
      PipeId: 'Electrical_71'
    },
    {
      PipeName: 'EF-消防广播桥架',
      BGclolor: 'RGB(255,128,128)',
      Visibility: 0,
      parentId: 'Electrical',
      PipeId: 'Electrical_81'
    },
    {
      PipeName: 'EV-安防线槽',
      BGclolor: 'RGB(191,255,127)',
      Visibility: 0,
      parentId: 'Electrical',
      PipeId: 'Electrical_91'
    },
    {
      PipeName: 'EV-自定义安防线槽',
      BGclolor: 'RGB(255,128,64)',
      Visibility: 0,
      parentId: 'Electrical',
      PipeId: 'Electrical_101'
    },
    {
      PipeName: 'EV-安防UPS桥架',
      BGclolor: 'RGB(128,64,64)',
      Visibility: 0,
      parentId: 'Electrical',
      PipeId: 'Electrical_111'
    },
    {
      PipeName: 'EV-综合布线线槽',
      BGclolor: 'RGB(127,095,000)',
      Visibility: 0,
      parentId: 'Electrical',
      PipeId: 'Electrical_121'
    },
    {
      PipeName: 'EV-车位引导线槽',
      BGclolor: 'RGB(000,128,128)',
      Visibility: 0,
      parentId: 'Electrical',
      PipeId: 'Electrical_131'
    },
    {
      PipeName: 'EV-移动通讯覆盖线槽',
      BGclolor: 'RGB(127,255,255)',
      Visibility: 0,
      parentId: 'Electrical',
      PipeId: 'Electrical_141'
    },
    {
      PipeName: 'EV-联通通讯覆盖线槽',
      BGclolor: 'RGB(127,255,255)',
      Visibility: 0,
      parentId: 'Electrical',
      PipeId: 'Electrical_151'
    },
    {
      PipeName: 'EV-电信通讯覆盖线槽',
      BGclolor: 'RGB(127,255,255)',
      Visibility: 0,
      parentId: 'Electrical',
      PipeId: 'Electrical_161'
    },
    {
      PipeName: 'EV-运营商线槽',
      BGclolor: 'RGB(0,64,0)',
      Visibility: 0,
      parentId: 'Electrical',
      PipeId: 'Electrical_171'
    },
    {
      PipeName: 'EV-信息设施线槽',
      BGclolor: 'RGB(64,0,128)',
      Visibility: 0,
      parentId: 'Electrical',
      PipeId: 'Electrical_181'
    },
    {
      PipeName: 'EV-信息UPS桥架',
      BGclolor: 'RGB(128,128,0)',
      Visibility: 0,
      parentId: 'Electrical',
      PipeId: 'Electrical_191'
    },
    {
      PipeName: 'EV-弱电综合桥架',
      BGclolor: 'RGB(255,128,255)',
      Visibility: 0,
      parentId: 'Electrical',
      PipeId: 'Electrical_201'
    },
    {
      PipeName: 'EV-弱电充电桩桥架',
      BGclolor: 'RGB(0,0,255)',
      Visibility: 0,
      parentId: 'Electrical',
      PipeId: 'Electrical_211'
    },
    {
      PipeName: 'EV-BAS、能源管理线槽',
      BGclolor: 'RGB(255,127,191)',
      Visibility: 0,
      parentId: 'Electrical',
      PipeId: 'Electrical_221'
    },
    {
      PipeName: 'EV-设备自控线槽',
      BGclolor: 'RGB(0,255,128)',
      Visibility: 0,
      parentId: 'Electrical',
      PipeId: 'Electrical_231'
    },
    {
      PipeName: 'E-发电机排烟风管',
      BGclolor: 'RGB(000,255,000)',
      Visibility: 0,
      parentId: 'Electrical',
      PipeId: 'Electrical_241'
    },
    {
      PipeName: 'E-输油管',
      BGclolor: 'RGB(255,127,127)',
      Visibility: 0,
      parentId: 'Electrical',
      PipeId: 'Electrical_251'
    },
    {
      PipeName: 'E-通气管',
      BGclolor: 'RGB(191,127,255)',
      Visibility: 0,
      parentId: 'Electrical',
      PipeId: 'Electrical_261'
    },
    {
      PipeName: 'P-给水管-市政',
      BGclolor: 'RGB(000,191,255)',
      Visibility: 0,
      parentId: 'WaterSupplyAndDrainage',
      PipeId: 'WaterSupplyAndDrainage_01'
    },
    {
      PipeName: 'P-给水管-支管',
      BGclolor: 'RGB(000,191,255)',
      Visibility: 0,
      parentId: 'WaterSupplyAndDrainage',
      PipeId: 'WaterSupplyAndDrainage_11'
    },
    {
      PipeName: 'P-人防给水管',
      BGclolor: 'RGB(000,191,255)',
      Visibility: 0,
      parentId: 'WaterSupplyAndDrainage',
      PipeId: 'WaterSupplyAndDrainage_21'
    },
    {
      PipeName: 'P-给水管-1区',
      BGclolor: 'RGB(000,191,255)',
      Visibility: 0,
      parentId: 'WaterSupplyAndDrainage',
      PipeId: 'WaterSupplyAndDrainage_31'
    },
    {
      PipeName: 'P-给水管-2区',
      BGclolor: 'RGB(000,191,255)',
      Visibility: 0,
      parentId: 'WaterSupplyAndDrainage',
      PipeId: 'WaterSupplyAndDrainage_41'
    },
    {
      PipeName: 'P-给水管-3区',
      BGclolor: 'RGB(000,191,255)',
      Visibility: 0,
      parentId: 'WaterSupplyAndDrainage',
      PipeId: 'WaterSupplyAndDrainage_51'
    },
    {
      PipeName: 'P-给水管-4区',
      BGclolor: 'RGB(000,191,255)',
      Visibility: 0,
      parentId: 'WaterSupplyAndDrainage',
      PipeId: 'WaterSupplyAndDrainage_61'
    },
    {
      PipeName: 'P-给水管-5区',
      BGclolor: 'RGB(000,191,255)',
      Visibility: 0,
      parentId: 'WaterSupplyAndDrainage',
      PipeId: 'WaterSupplyAndDrainage_71'
    },
    {
      PipeName: 'P-给水管-转输',
      BGclolor: 'RGB(000,191,255)',
      Visibility: 0,
      parentId: 'WaterSupplyAndDrainage',
      PipeId: 'WaterSupplyAndDrainage_81'
    },
    {
      PipeName: 'P-冷却塔给水管',
      BGclolor: 'RGB(000,191,255)',
      Visibility: 0,
      parentId: 'WaterSupplyAndDrainage',
      PipeId: 'WaterSupplyAndDrainage_91'
    },
    {
      PipeName: 'P-热水给水-支管',
      BGclolor: 'RGB(255,63,000)',
      Visibility: 0,
      parentId: 'WaterSupplyAndDrainage',
      PipeId: 'WaterSupplyAndDrainage_101'
    },
    {
      PipeName: 'P-热水给水-1区',
      BGclolor: 'RGB(255,63,000)',
      Visibility: 0,
      parentId: 'WaterSupplyAndDrainage',
      PipeId: 'WaterSupplyAndDrainage_111'
    },
    {
      PipeName: 'P-热水给水-2区',
      BGclolor: 'RGB(255,63,000)',
      Visibility: 0,
      parentId: 'WaterSupplyAndDrainage',
      PipeId: 'WaterSupplyAndDrainage_121'
    },
    {
      PipeName: 'P-热水给水-3区',
      BGclolor: 'RGB(255,63,000)',
      Visibility: 0,
      parentId: 'WaterSupplyAndDrainage',
      PipeId: 'WaterSupplyAndDrainage_131'
    },
    {
      PipeName: 'P-热水给水-4区',
      BGclolor: 'RGB(255,63,000)',
      Visibility: 0,
      parentId: 'WaterSupplyAndDrainage',
      PipeId: 'WaterSupplyAndDrainage_141'
    },
    {
      PipeName: 'P-热水回水-支管',
      BGclolor: 'RGB(000,063,255)',
      Visibility: 0,
      parentId: 'WaterSupplyAndDrainage',
      PipeId: 'WaterSupplyAndDrainage_151'
    },
    {
      PipeName: 'P-热水回水-1区',
      BGclolor: 'RGB(000,063,255)',
      Visibility: 0,
      parentId: 'WaterSupplyAndDrainage',
      PipeId: 'WaterSupplyAndDrainage_161'
    },
    {
      PipeName: 'P-热水回水-2区',
      BGclolor: 'RGB(000,063,255)',
      Visibility: 0,
      parentId: 'WaterSupplyAndDrainage',
      PipeId: 'WaterSupplyAndDrainage_171'
    },
    {
      PipeName: 'P-热水回水-3区',
      BGclolor: 'RGB(000,063,255)',
      Visibility: 0,
      parentId: 'WaterSupplyAndDrainage',
      PipeId: 'WaterSupplyAndDrainage_181'
    },
    {
      PipeName: 'P-热水回水-4区',
      BGclolor: 'RGB(000,063,255)',
      Visibility: 0,
      parentId: 'WaterSupplyAndDrainage',
      PipeId: 'WaterSupplyAndDrainage_191'
    },
    {
      PipeName: 'P-热媒回水-支管',
      BGclolor: 'RGB(255,000,063)',
      Visibility: 0,
      parentId: 'WaterSupplyAndDrainage',
      PipeId: 'WaterSupplyAndDrainage_201'
    },
    {
      PipeName: 'P-热媒回水-1区',
      BGclolor: 'RGB(255,000,063)',
      Visibility: 0,
      parentId: 'WaterSupplyAndDrainage',
      PipeId: 'WaterSupplyAndDrainage_211'
    },
    {
      PipeName: 'P-热媒回水-2区',
      BGclolor: 'RGB(255,000,063)',
      Visibility: 0,
      parentId: 'WaterSupplyAndDrainage',
      PipeId: 'WaterSupplyAndDrainage_221'
    },
    {
      PipeName: 'P-热媒回水-3区',
      BGclolor: 'RGB(255,000,063)',
      Visibility: 0,
      parentId: 'WaterSupplyAndDrainage',
      PipeId: 'WaterSupplyAndDrainage_231'
    },
    {
      PipeName: 'P-热媒供水-支管',
      BGclolor: 'RGB(255,000,063)',
      Visibility: 0,
      parentId: 'WaterSupplyAndDrainage',
      PipeId: 'WaterSupplyAndDrainage_241'
    },
    {
      PipeName: 'P-热媒供水-1区',
      BGclolor: 'RGB(255,000,063)',
      Visibility: 0,
      parentId: 'WaterSupplyAndDrainage',
      PipeId: 'WaterSupplyAndDrainage_251'
    },
    {
      PipeName: 'P-热媒供水-2区',
      BGclolor: 'RGB(255,000,063)',
      Visibility: 0,
      parentId: 'WaterSupplyAndDrainage',
      PipeId: 'WaterSupplyAndDrainage_261'
    },
    {
      PipeName: 'P-热媒供水-3区',
      BGclolor: 'RGB(255,000,063)',
      Visibility: 0,
      parentId: 'WaterSupplyAndDrainage',
      PipeId: 'WaterSupplyAndDrainage_271'
    },
    {
      PipeName: 'P-直饮水给水-支管',
      BGclolor: 'RGB(000,255,255)',
      Visibility: 0,
      parentId: 'WaterSupplyAndDrainage',
      PipeId: 'WaterSupplyAndDrainage_281'
    },
    {
      PipeName: 'P-直饮水回水-1区',
      BGclolor: 'RGB(000,255,255)',
      Visibility: 0,
      parentId: 'WaterSupplyAndDrainage',
      PipeId: 'WaterSupplyAndDrainage_291'
    },
    {
      PipeName: 'P-直饮水回水-2区',
      BGclolor: 'RGB(000,255,255)',
      Visibility: 0,
      parentId: 'WaterSupplyAndDrainage',
      PipeId: 'WaterSupplyAndDrainage_301'
    },
    {
      PipeName: 'P-直饮水回水-3区',
      BGclolor: 'RGB(000,255,255)',
      Visibility: 0,
      parentId: 'WaterSupplyAndDrainage',
      PipeId: 'WaterSupplyAndDrainage_311'
    },
    {
      PipeName: 'P-直饮水回水-支管',
      BGclolor: 'RGB(000,063,255)',
      Visibility: 0,
      parentId: 'WaterSupplyAndDrainage',
      PipeId: 'WaterSupplyAndDrainage_321'
    },
    {
      PipeName: 'P-直饮水给水-1区',
      BGclolor: 'RGB(000,063,255)',
      Visibility: 0,
      parentId: 'WaterSupplyAndDrainage',
      PipeId: 'WaterSupplyAndDrainage_331'
    },
    {
      PipeName: 'P-直饮水给水-2区',
      BGclolor: 'RGB(000,063,255)',
      Visibility: 0,
      parentId: 'WaterSupplyAndDrainage',
      PipeId: 'WaterSupplyAndDrainage_341'
    },
    {
      PipeName: 'P-直饮水给水-3区',
      BGclolor: 'RGB(000,063,255)',
      Visibility: 0,
      parentId: 'WaterSupplyAndDrainage',
      PipeId: 'WaterSupplyAndDrainage_351'
    },
    {
      PipeName: 'P-纯水给水-支管',
      BGclolor: 'RGB(127,255,223)',
      Visibility: 0,
      parentId: 'WaterSupplyAndDrainage',
      PipeId: 'WaterSupplyAndDrainage_361'
    },
    {
      PipeName: 'P-纯水给水-1区',
      BGclolor: 'RGB(127,255,223)',
      Visibility: 0,
      parentId: 'WaterSupplyAndDrainage',
      PipeId: 'WaterSupplyAndDrainage_371'
    },
    {
      PipeName: 'P-纯水给水-2区',
      BGclolor: 'RGB(127,255,223)',
      Visibility: 0,
      parentId: 'WaterSupplyAndDrainage',
      PipeId: 'WaterSupplyAndDrainage_381'
    },
    {
      PipeName: 'P-纯水给水-3区',
      BGclolor: 'RGB(127,255,223)',
      Visibility: 0,
      parentId: 'WaterSupplyAndDrainage',
      PipeId: 'WaterSupplyAndDrainage_391'
    },
    {
      PipeName: 'P-纯水回水-支管',
      BGclolor: 'RGB(127,159,255)',
      Visibility: 0,
      parentId: 'WaterSupplyAndDrainage',
      PipeId: 'WaterSupplyAndDrainage_401'
    },
    {
      PipeName: 'P-纯水回水-1区',
      BGclolor: 'RGB(127,159,255)',
      Visibility: 0,
      parentId: 'WaterSupplyAndDrainage',
      PipeId: 'WaterSupplyAndDrainage_411'
    },
    {
      PipeName: 'P-纯水回水-2区',
      BGclolor: 'RGB(127,159,255)',
      Visibility: 0,
      parentId: 'WaterSupplyAndDrainage',
      PipeId: 'WaterSupplyAndDrainage_421'
    },
    {
      PipeName: 'P-纯水回水-3区',
      BGclolor: 'RGB(127,159,255)',
      Visibility: 0,
      parentId: 'WaterSupplyAndDrainage',
      PipeId: 'WaterSupplyAndDrainage_431'
    },
    {
      PipeName: 'P-中水给水-市政',
      BGclolor: 'RGB(000,127,255)',
      Visibility: 0,
      parentId: 'WaterSupplyAndDrainage',
      PipeId: 'WaterSupplyAndDrainage_441'
    },
    {
      PipeName: 'P-中水给水-支管',
      BGclolor: 'RGB(000,127,255)',
      Visibility: 0,
      parentId: 'WaterSupplyAndDrainage',
      PipeId: 'WaterSupplyAndDrainage_451'
    },
    {
      PipeName: 'P-中水给水-1区',
      BGclolor: 'RGB(000,127,255)',
      Visibility: 0,
      parentId: 'WaterSupplyAndDrainage',
      PipeId: 'WaterSupplyAndDrainage_461'
    },
    {
      PipeName: 'P-中水给水-2区',
      BGclolor: 'RGB(000,127,255)',
      Visibility: 0,
      parentId: 'WaterSupplyAndDrainage',
      PipeId: 'WaterSupplyAndDrainage_471'
    },
    {
      PipeName: 'P-中水给水-3区',
      BGclolor: 'RGB(000,127,255)',
      Visibility: 0,
      parentId: 'WaterSupplyAndDrainage',
      PipeId: 'WaterSupplyAndDrainage_481'
    },
    {
      PipeName: 'P-污水管',
      BGclolor: 'RGB(255,191,000)',
      Visibility: 0,
      parentId: 'WaterSupplyAndDrainage',
      PipeId: 'WaterSupplyAndDrainage_491'
    },
    {
      PipeName: 'P-人防污水管',
      BGclolor: 'RGB(127,111,063)',
      Visibility: 0,
      parentId: 'WaterSupplyAndDrainage',
      PipeId: 'WaterSupplyAndDrainage_501'
    },
    {
      PipeName: 'P-压力污水管',
      BGclolor: 'RGB(127,063,000)',
      Visibility: 0,
      parentId: 'WaterSupplyAndDrainage',
      PipeId: 'WaterSupplyAndDrainage_511'
    },
    {
      PipeName: 'P-废水管',
      BGclolor: 'RGB(191,255,000)',
      Visibility: 0,
      parentId: 'WaterSupplyAndDrainage',
      PipeId: 'WaterSupplyAndDrainage_521'
    },
    {
      PipeName: 'P-餐饮废水管',
      BGclolor: 'RGB(204,153,00)',
      Visibility: 0,
      parentId: 'WaterSupplyAndDrainage',
      PipeId: 'WaterSupplyAndDrainage_531'
    },
    {
      PipeName: 'P-压力餐饮废水管',
      BGclolor: 'RGB(127,095,000)',
      Visibility: 0,
      parentId: 'WaterSupplyAndDrainage',
      PipeId: 'WaterSupplyAndDrainage_541'
    },
    {
      PipeName: 'P-自喷废水管',
      BGclolor: 'RGB(204,102,000)',
      Visibility: 0,
      parentId: 'WaterSupplyAndDrainage',
      PipeId: 'WaterSupplyAndDrainage_551'
    },
    {
      PipeName: 'P-压力废水管',
      BGclolor: 'RGB(204,204,000)',
      Visibility: 0,
      parentId: 'WaterSupplyAndDrainage',
      PipeId: 'WaterSupplyAndDrainage_561'
    },
    {
      PipeName: 'P-雨水管',
      BGclolor: 'RGB(000,255,127)',
      Visibility: 0,
      parentId: 'WaterSupplyAndDrainage',
      PipeId: 'WaterSupplyAndDrainage_571'
    },
    {
      PipeName: 'P-压力雨水管',
      BGclolor: 'RGB(000,255,063)',
      Visibility: 0,
      parentId: 'WaterSupplyAndDrainage',
      PipeId: 'WaterSupplyAndDrainage_581'
    },
    {
      PipeName: 'P-阳台雨水管',
      BGclolor: 'RGB(000,255,127)',
      Visibility: 0,
      parentId: 'WaterSupplyAndDrainage',
      PipeId: 'WaterSupplyAndDrainage_591'
    },
    {
      PipeName: 'P-雨水回用管',
      BGclolor: 'RGB(095,127,063)',
      Visibility: 0,
      parentId: 'WaterSupplyAndDrainage',
      PipeId: 'WaterSupplyAndDrainage_601'
    },
    {
      PipeName: 'P-虹吸雨水管',
      BGclolor: 'RGB(127,255,000)',
      Visibility: 0,
      parentId: 'WaterSupplyAndDrainage',
      PipeId: 'WaterSupplyAndDrainage_611'
    },
    {
      PipeName: 'P-室内消火栓-支管',
      BGclolor: 'RGB(255,000,000)',
      Visibility: 0,
      parentId: 'WaterSupplyAndDrainage',
      PipeId: 'WaterSupplyAndDrainage_621'
    },
    {
      PipeName: 'P-消防吸水总管',
      BGclolor: 'RGB(255,000,000)',
      Visibility: 0,
      parentId: 'WaterSupplyAndDrainage',
      PipeId: 'WaterSupplyAndDrainage_631'
    },
    {
      PipeName: 'P-消火栓管-1区',
      BGclolor: 'RGB(255,000,000)',
      Visibility: 0,
      parentId: 'WaterSupplyAndDrainage',
      PipeId: 'WaterSupplyAndDrainage_641'
    },
    {
      PipeName: 'P-消火栓管-2区',
      BGclolor: 'RGB(255,000,000)',
      Visibility: 0,
      parentId: 'WaterSupplyAndDrainage',
      PipeId: 'WaterSupplyAndDrainage_651'
    },
    {
      PipeName: 'P-消火栓管-3区',
      BGclolor: 'RGB(255,000,000)',
      Visibility: 0,
      parentId: 'WaterSupplyAndDrainage',
      PipeId: 'WaterSupplyAndDrainage_661'
    },
    {
      PipeName: 'P-消火栓管-4区',
      BGclolor: 'RGB(255,000,000)',
      Visibility: 0,
      parentId: 'WaterSupplyAndDrainage',
      PipeId: 'WaterSupplyAndDrainage_671'
    },
    {
      PipeName: 'P-消火栓转输管',
      BGclolor: 'RGB(255,000,000)',
      Visibility: 0,
      parentId: 'WaterSupplyAndDrainage',
      PipeId: 'WaterSupplyAndDrainage_681'
    },
    {
      PipeName: 'P-室外消火栓管',
      BGclolor: 'RGB(255,000,000)',
      Visibility: 0,
      parentId: 'WaterSupplyAndDrainage',
      PipeId: 'WaterSupplyAndDrainage_691'
    },
    {
      PipeName: 'P-消防水炮管',
      BGclolor: 'RGB(255,000,000)',
      Visibility: 0,
      parentId: 'WaterSupplyAndDrainage',
      PipeId: 'WaterSupplyAndDrainage_701'
    },
    {
      PipeName: 'P-自喷-支管',
      BGclolor: 'RGB(255,000,255)',
      Visibility: 0,
      parentId: 'WaterSupplyAndDrainage',
      PipeId: 'WaterSupplyAndDrainage_711'
    },
    {
      PipeName: 'P-自喷-主管',
      BGclolor: 'RGB(255,000,255)',
      Visibility: 0,
      parentId: 'WaterSupplyAndDrainage',
      PipeId: 'WaterSupplyAndDrainage_721'
    },
    {
      PipeName: 'P-自喷管-1区',
      BGclolor: 'RGB(255,000,255)',
      Visibility: 0,
      parentId: 'WaterSupplyAndDrainage',
      PipeId: 'WaterSupplyAndDrainage_731'
    },
    {
      PipeName: 'P-自喷管-2区',
      BGclolor: 'RGB(255,000,255)',
      Visibility: 0,
      parentId: 'WaterSupplyAndDrainage',
      PipeId: 'WaterSupplyAndDrainage_741'
    },
    {
      PipeName: 'P-自喷管-3区',
      BGclolor: 'RGB(255,000,255)',
      Visibility: 0,
      parentId: 'WaterSupplyAndDrainage',
      PipeId: 'WaterSupplyAndDrainage_751'
    },
    {
      PipeName: 'P-自喷管-4区',
      BGclolor: 'RGB(255,000,255)',
      Visibility: 0,
      parentId: 'WaterSupplyAndDrainage',
      PipeId: 'WaterSupplyAndDrainage_761'
    },
    {
      PipeName: 'P-自喷转输管',
      BGclolor: 'RGB(255,000,255)',
      Visibility: 0,
      parentId: 'WaterSupplyAndDrainage',
      PipeId: 'WaterSupplyAndDrainage_771'
    },
    {
      PipeName: 'P-室外喷淋管',
      BGclolor: 'RGB(255,000,255)',
      Visibility: 0,
      parentId: 'WaterSupplyAndDrainage',
      PipeId: 'WaterSupplyAndDrainage_781'
    },
    {
      PipeName: 'P-防护水幕管',
      BGclolor: 'RGB(255,000,255)',
      Visibility: 0,
      parentId: 'WaterSupplyAndDrainage',
      PipeId: 'WaterSupplyAndDrainage_791'
    },
    {
      PipeName: 'P-雨淋管',
      BGclolor: 'RGB(255,000,191)',
      Visibility: 0,
      parentId: 'WaterSupplyAndDrainage',
      PipeId: 'WaterSupplyAndDrainage_801'
    },
    {
      PipeName: 'P-泡沫管',
      BGclolor: 'RGB(255,127,191)',
      Visibility: 0,
      parentId: 'WaterSupplyAndDrainage',
      PipeId: 'WaterSupplyAndDrainage_811'
    },
    {
      PipeName: 'P-固定泡沫炮管',
      BGclolor: 'RGB(255,000,127)',
      Visibility: 0,
      parentId: 'WaterSupplyAndDrainage',
      PipeId: 'WaterSupplyAndDrainage_821'
    },
    {
      PipeName: 'P-通气管',
      BGclolor: 'RGB(191,127,255)',
      Visibility: 0,
      parentId: 'WaterSupplyAndDrainage',
      PipeId: 'WaterSupplyAndDrainage_831'
    },
    {
      PipeName: 'P-冷凝水管',
      BGclolor: 'RGB(255,159,127)',
      Visibility: 0,
      parentId: 'WaterSupplyAndDrainage',
      PipeId: 'WaterSupplyAndDrainage_841'
    },
    {
      PipeName: 'P-气体灭火管',
      BGclolor: 'RGB(127,000,255)',
      Visibility: 0,
      parentId: 'WaterSupplyAndDrainage',
      PipeId: 'WaterSupplyAndDrainage_851'
    },
    {
      PipeName: 'P-高压细水雾管',
      BGclolor: 'RGB(168,082,165)',
      Visibility: 0,
      parentId: 'WaterSupplyAndDrainage',
      PipeId: 'WaterSupplyAndDrainage_861'
    },
    {
      PipeName: 'P-人防输油管',
      BGclolor: 'RGB(153,000,000)',
      Visibility: 0,
      parentId: 'WaterSupplyAndDrainage',
      PipeId: 'WaterSupplyAndDrainage_871'
    },
    {
      PipeName: 'P-人防输油回油管',
      BGclolor: 'RGB(127,031,000)',
      Visibility: 0,
      parentId: 'WaterSupplyAndDrainage',
      PipeId: 'WaterSupplyAndDrainage_881'
    },
    {
      PipeName: 'M-乙二醇供水管',
      BGclolor: 'RGB(000,165,000)',
      Visibility: 0,
      parentId: 'HeatingAndVentilation',
      PipeId: 'HeatingAndVentilation_01'
    },
    {
      PipeName: 'M-乙二醇回水管',
      BGclolor: 'RGB(000,165,000)',
      Visibility: 0,
      parentId: 'HeatingAndVentilation',
      PipeId: 'HeatingAndVentilation_11'
    },
    {
      PipeName: 'M-乙二醇补液管',
      BGclolor: 'RGB(255,255,127)',
      Visibility: 0,
      parentId: 'HeatingAndVentilation',
      PipeId: 'HeatingAndVentilation_21'
    },
    {
      PipeName: 'M-供暖供水管',
      BGclolor: 'RGB(255,000,191)',
      Visibility: 0,
      parentId: 'HeatingAndVentilation',
      PipeId: 'HeatingAndVentilation_31'
    },
    {
      PipeName: 'M-供暖回水管',
      BGclolor: 'RGB(255,000,191)',
      Visibility: 0,
      parentId: 'HeatingAndVentilation',
      PipeId: 'HeatingAndVentilation_41'
    },
    {
      PipeName: 'M-冰水供水管',
      BGclolor: 'RGB(000,165,000)',
      Visibility: 0,
      parentId: 'HeatingAndVentilation',
      PipeId: 'HeatingAndVentilation_51'
    },
    {
      PipeName: 'M-冰水回水管',
      BGclolor: 'RGB(000,165,000)',
      Visibility: 0,
      parentId: 'HeatingAndVentilation',
      PipeId: 'HeatingAndVentilation_61'
    },
    {
      PipeName: 'M-冷/热水供水管',
      BGclolor: 'RGB(000,204,153)',
      Visibility: 0,
      parentId: 'HeatingAndVentilation',
      PipeId: 'HeatingAndVentilation_71'
    },
    {
      PipeName: 'M-冷/热水回水管',
      BGclolor: 'RGB(000,204,153)',
      Visibility: 0,
      parentId: 'HeatingAndVentilation',
      PipeId: 'HeatingAndVentilation_81'
    },
    {
      PipeName: 'M-冷冻水供水管',
      BGclolor: 'RGB(000,204,153)',
      Visibility: 0,
      parentId: 'HeatingAndVentilation',
      PipeId: 'HeatingAndVentilation_91'
    },
    {
      PipeName: 'M-冷冻水回水管',
      BGclolor: 'RGB(000,204,153)',
      Visibility: 0,
      parentId: 'HeatingAndVentilation',
      PipeId: 'HeatingAndVentilation_101'
    },
    {
      PipeName: 'M-冷冻水供水管',
      BGclolor: 'RGB(127,159,255)',
      Visibility: 0,
      parentId: 'HeatingAndVentilation',
      PipeId: 'HeatingAndVentilation_111'
    },
    {
      PipeName: 'M-冷却水回水管',
      BGclolor: 'RGB(127,159,255)',
      Visibility: 0,
      parentId: 'HeatingAndVentilation',
      PipeId: 'HeatingAndVentilation_121'
    },
    {
      PipeName: 'M-冷媒管',
      BGclolor: 'RGB(127,127,255)',
      Visibility: 0,
      parentId: 'HeatingAndVentilation',
      PipeId: 'HeatingAndVentilation_131'
    },
    {
      PipeName: 'M-凝结水管',
      BGclolor: 'RGB(000,255,191)',
      Visibility: 0,
      parentId: 'HeatingAndVentilation',
      PipeId: 'HeatingAndVentilation_141'
    },
    {
      PipeName: 'M-循环管',
      BGclolor: 'RGB(255,255,127)',
      Visibility: 0,
      parentId: 'HeatingAndVentilation',
      PipeId: 'HeatingAndVentilation_151'
    },
    {
      PipeName: 'M-排水管',
      BGclolor: 'RGB(000,255,191)',
      Visibility: 0,
      parentId: 'HeatingAndVentilation',
      PipeId: 'HeatingAndVentilation_161'
    },
    {
      PipeName: 'M-热水供水管（热源侧）',
      BGclolor: 'RGB(255,000,063)',
      Visibility: 0,
      parentId: 'HeatingAndVentilation',
      PipeId: 'HeatingAndVentilation_171'
    },
    {
      PipeName: 'M-热水回水管（热源侧）',
      BGclolor: 'RGB(255,000,063)',
      Visibility: 0,
      parentId: 'HeatingAndVentilation',
      PipeId: 'HeatingAndVentilation_181'
    },
    {
      PipeName: 'M-燃气管',
      BGclolor: 'RGB(191,0,255)',
      Visibility: 0,
      parentId: 'HeatingAndVentilation',
      PipeId: 'HeatingAndVentilation_191'
    },
    {
      PipeName: 'M-空调冷凝水管',
      BGclolor: 'RGB(255,127,127)',
      Visibility: 0,
      parentId: 'HeatingAndVentilation',
      PipeId: 'HeatingAndVentilation_201'
    },
    {
      PipeName: 'M-膨胀水管',
      BGclolor: 'RGB(000,255,191)',
      Visibility: 0,
      parentId: 'HeatingAndVentilation',
      PipeId: 'HeatingAndVentilation_211'
    },
    {
      PipeName: 'M-蒸汽管',
      BGclolor: 'RGB(255,127,191)',
      Visibility: 0,
      parentId: 'HeatingAndVentilation',
      PipeId: 'HeatingAndVentilation_221'
    },
    {
      PipeName: 'M-补水管',
      BGclolor: 'RGB(000,255,191)',
      Visibility: 0,
      parentId: 'HeatingAndVentilation',
      PipeId: 'HeatingAndVentilation_231'
    },
    {
      PipeName: 'M-软化水管',
      BGclolor: 'RGB(255,255,127)',
      Visibility: 0,
      parentId: 'HeatingAndVentilation',
      PipeId: 'HeatingAndVentilation_241'
    },
    {
      PipeName: 'M-增压管',
      BGclolor: 'RGB(200,40,100)',
      Visibility: 0,
      parentId: 'HeatingAndVentilation',
      PipeId: 'HeatingAndVentilation_251'
    },
    {
      PipeName: 'M-压差测量管',
      BGclolor: 'RGB(130,171,221)',
      Visibility: 0,
      parentId: 'HeatingAndVentilation',
      PipeId: 'HeatingAndVentilation_261'
    },
    {
      PipeName: 'M-气密测量管',
      BGclolor: 'RGB(200,60,250)',
      Visibility: 0,
      parentId: 'HeatingAndVentilation',
      PipeId: 'HeatingAndVentilation_271'
    },
    {
      PipeName: 'M-尾气测量取样管',
      BGclolor: 'RGB(255,128,064)',
      Visibility: 0,
      parentId: 'HeatingAndVentilation',
      PipeId: 'HeatingAndVentilation_281'
    },
    {
      PipeName: 'M-放射性检测取样管',
      BGclolor: 'RGB(184,203,148)',
      Visibility: 0,
      parentId: 'HeatingAndVentilation',
      PipeId: 'HeatingAndVentilation_291'
    },
    {
      PipeName: 'M-处理新风管',
      BGclolor: 'RGB(000,255,000)',
      Visibility: 0,
      parentId: 'HeatingAndVentilation',
      PipeId: 'HeatingAndVentilation_301'
    },
    {
      PipeName: 'M-未处理新风管',
      BGclolor: 'RGB(000,127,000)',
      Visibility: 0,
      parentId: 'HeatingAndVentilation',
      PipeId: 'HeatingAndVentilation_311'
    },
    {
      PipeName: 'M-空调送风管',
      BGclolor: 'RGB(000,255,255)',
      Visibility: 0,
      parentId: 'HeatingAndVentilation',
      PipeId: 'HeatingAndVentilation_321'
    },
    {
      PipeName: 'M-空调回风管',
      BGclolor: 'RGB(255,000,255)',
      Visibility: 0,
      parentId: 'HeatingAndVentilation',
      PipeId: 'HeatingAndVentilation_331'
    },
    {
      PipeName: 'M-发电机排烟风管',
      BGclolor: 'RGB(000,204,051)',
      Visibility: 0,
      parentId: 'HeatingAndVentilation',
      PipeId: 'HeatingAndVentilation_341'
    },
    {
      PipeName: 'M-送风管',
      BGclolor: 'RGB(000,204,051)',
      Visibility: 0,
      parentId: 'HeatingAndVentilation',
      PipeId: 'HeatingAndVentilation_351'
    },
    {
      PipeName: 'M-排风管',
      BGclolor: 'RGB(255,127,255)',
      Visibility: 0,
      parentId: 'HeatingAndVentilation',
      PipeId: 'HeatingAndVentilation_361'
    },
    {
      PipeName: 'M-排油烟风管',
      BGclolor: 'RGB(165,165,000)',
      Visibility: 0,
      parentId: 'HeatingAndVentilation',
      PipeId: 'HeatingAndVentilation_371'
    },
    {
      PipeName: 'M-锅炉排烟风管',
      BGclolor: 'RGB(82,124,165)',
      Visibility: 0,
      parentId: 'HeatingAndVentilation',
      PipeId: 'HeatingAndVentilation_381'
    },
    {
      PipeName: 'M-消防排烟风管',
      BGclolor: 'RGB(000,000,255)',
      Visibility: 0,
      parentId: 'HeatingAndVentilation',
      PipeId: 'HeatingAndVentilation_391'
    },
    {
      PipeName: 'M-消防排烟兼排风管',
      BGclolor: 'RGB(000,000,255)',
      Visibility: 0,
      parentId: 'HeatingAndVentilation',
      PipeId: 'HeatingAndVentilation_401'
    },
    {
      PipeName: 'M-消防补风管',
      BGclolor: 'RGB(000,127,127)',
      Visibility: 0,
      parentId: 'HeatingAndVentilation',
      PipeId: 'HeatingAndVentilation_411'
    },
    {
      PipeName: 'M-消防补风兼新风管',
      BGclolor: 'RGB(000,127,127)',
      Visibility: 0,
      parentId: 'HeatingAndVentilation',
      PipeId: 'HeatingAndVentilation_421'
    },
    {
      PipeName: 'M-加压送风管',
      BGclolor: 'RGB(255,191,000)',
      Visibility: 0,
      parentId: 'HeatingAndVentilation',
      PipeId: 'HeatingAndVentilation_431'
    },
    {
      PipeName: 'M-人防排风管',
      BGclolor: 'RGB(000,255,000)',
      Visibility: 0,
      parentId: 'HeatingAndVentilation',
      PipeId: 'HeatingAndVentilation_441'
    },
    {
      PipeName: 'M-人防进风管',
      BGclolor: 'RGB(000,255,000)',
      Visibility: 0,
      parentId: 'HeatingAndVentilation',
      PipeId: 'HeatingAndVentilation_451'
    },
    {
      PipeName: 'M-排油烟补风管',
      BGclolor: 'RGB(000,204,102)',
      Visibility: 0,
      parentId: 'HeatingAndVentilation',
      PipeId: 'HeatingAndVentilation_461'
    },
    { BGclolor: 'RGB(000,255,000)', PipeName: '医疗专项', parentId: 'SpecialMedicalPurpose', PipeId: 'SpecialMedicalPurpose_1', Visibility: 0 }
  ],
  fjslyy: [
    {
      PipeName: '给排水',
      BGclolor: 'RGB(000,204,102)',
      Visibility: 0,
      parentId: 'WSDS',
      PipeId: 'WSDS'
    },
    {
      PipeName: '医用气体',
      BGclolor: 'RGB(255,191,000)',
      Visibility: 0,
      parentId: 'MEGA',
      PipeId: 'MEGA'
    },
    {
      PipeName: '电气',
      BGclolor: 'RGB(000,000,255)',
      Visibility: 0,
      parentId: 'ELEC',
      PipeId: 'ELEC'
    },
    {
      PipeName: '暖通',
      BGclolor: 'RGB(000,255,255)',
      Visibility: 0,
      parentId: 'HVAC',
      PipeId: 'HVAC'
    },
  ]
}

export const systemTypeList = {
  szzlyy: [
    {
      name: '全部',
      value: 'all'
    },
    {
      name: '暖通空调系统',
      value: 'HeatingAndVentilation'
    },
    {
      name: '电气',
      value: 'Electrical'
    },
    {
      name: '给排水系统',
      value: 'WaterSupplyAndDrainage'
    },
    {
      name: '医用气体系统',
      value: 'SpecialMedicalPurpose'
    }
    // {
    //   name: '医用气体系统',
    //   value: 'yyqt'
    // },
    // {
    //   name: '给排水系统',
    //   value: 'jps'
    // },
    // {
    //   name: '空调通风系统',
    //   value: 'kttf'
    // },
    // {
    //   name: '冷源系统',
    //   value: 'ly'
    // }
  ],
  fjslyy: [
    {
      name: '全部',
      value: 'all'
    },
    {
      name: '给排水',
      value: 'WSDS'
    },
    {
      name: '医用气体',
      value: 'MEGA'
    },
    {
      name: '电气',
      value: 'ELEC'
    },
    {
      name: '暖通',
      value: 'HVAC'
    }
  ]
}

export const kgceUser = {
  username: 'gy_energy',
  password: 'Gy12345678!1'
}
export const monitorTypeList = [
  {
    name: '锅炉房',
    wpfKey: 'Boiler',
    projectName: '锅炉检测',
    projectCode: 'IEMC-Boiler'
  },
  {
    name: '制冷机房',
    wpfKey: 'Kongtiao',
    projectName: '冷热源监测',
    projectCode: 'b5587fbbe453422ebc937cc0b7c69a3c'
  },
  {
    name: '氧气站房',
    wpfKey: 'Yqxt',
    projectName: '医用气体',
    projectCode: '73e7aab447b34971b9ae6d8dae034aa3'
  },
  {
    name: '变配电',
    wpfKey: 'Electricity',
    projectName: '配电监测',
    projectCode: 'IEMC-Electricity'
  },
  {
    name: 'UPS',
    wpfKey: 'Ups',
    projectName: 'UPS监测',
    projectCode: '01f4bd80e5c060809aae72c7470e8be30'
  },
  {
    name: 'WaterSupply',
    wpfKey: 'WaterSupply',
    projectName: '给排水监测',
    projectCode: 'b5587fbbe453422ebc937cc0b7c69wsx'
  },
  {
    name: 'ASHP',
    wpfKey: 'ASHP',
    projectName: '风冷热泵监测',
    projectCode: 'IEMC-AirCooledHeatPump'
  },
  {
    name: '电梯',
    wpfKey: 'ElevatorSystem',
    projectName: '电梯运行监测',
    projectCode: '713e24b03094410499db0b08a2eccbcc'
  },
  {
    name: '安防',
    wpfKey: 'SecuritySystem',
    projectName: '安防系统监测',
    projectCode: 'IEMC-SecuritySystem'
  },
  {
    name: '消防',
    wpfKey: 'FireAlarmSystem',
    projectName: '消防系统监测',
    projectCode: 'IEMC-FireAlarmSystem'
  },
  {
    name: '摄像头设备',
    wpfKey: 'SecuritySystem',
    projectName: '摄像头设备',
    projectCode: 'IEMC-CameraEquipment',
  },
  {
    name: '门禁设备',
    wpfKey: 'SecuritySystem',
    projectName: '门禁设备',
    projectCode: 'IEMC-AccessControlEquipment',
  },
  {
    name: '入侵设备',
    wpfKey: 'SecuritySystem',
    projectName: '入侵设备',
    projectCode: 'IEMC-IntrusionEquipment',
  },
  {
    name: '一键报警',
    wpfKey: 'SecuritySystem',
    projectName: '一键报警',
    projectCode: 'IEMC-OneKeyAlarm',
  },
  {
    name: '烟感温感',
    wpfKey: 'FireAlarmSystem',
    projectName: '烟感温感',
    projectCode: 'IEMC-SmokeAndTemperatureSensing',
  },
  {
    name: '用电安全',
    wpfKey: 'FireAlarmSystem',
    projectName: '用电安全',
    projectCode: 'IEMC-ElectricitySafe',
  },
  {
    name: '手报',
    wpfKey: 'FireAlarmSystem',
    projectName: '手报',
    projectCode: 'IEMC-HandNewspaper',
  },
  {
    name: '消防水箱',
    wpfKey: 'FireAlarmSystem',
    projectName: '消防水箱',
    projectCode: 'IEMC-FireWaterTank',
  },
  {
    name: '消防水泵',
    wpfKey: 'FireAlarmSystem',
    projectName: '消防水泵',
    projectCode: 'IEMC-FirePump',
  },
  {
    name: '防排烟风机',
    wpfKey: 'FireAlarmSystem',
    projectName: '防排烟风机',
    projectCode: 'IEMC-Anti-exhaustFan',
  },
  {
    name: '防火门',
    wpfKey: 'FireAlarmSystem',
    projectName: '防火门',
    projectCode: 'IEMC-FireDoor',
  },
  {
    name: '末端试水',
    wpfKey: 'FireAlarmSystem',
    projectName: '末端试水',
    projectCode: 'IEMC-EndTestWater',
  },
  {
    name: '空调末端',
    wpfKey: 'Airend',
    projectName: '空调监测',
    projectCode: 'fb78bc9c7e5311ec8e4f000c2912d8ca'
  },
  {
    name: '照明监测',
    wpfKey: 'light',
    projectName: '照明监测',
    projectCode: '00f4ad80e5c04809aae72c7470e8be28'
  },
  {
    name: '污水监测',
    wpfKey: '',
    projectName: '污水监测',
    projectCode: 'IEMC-Sewage'
  },
  {
    name: '电表监测',
    wpfKey: 'energy',
    projectName: '电表监测',
    projectCode: 'IEMC-EnergyMetering'
  },
  {
    name: '门禁设备',
    wpfKey: 'mjsb',
    projectName: '门禁设备',
    projectCode: 'IEMC-AccessControlEquipment',
  },
  {
    name: '毒麻精放',
    wpfKey: 'PoisonousHemp',
    projectName: '毒麻精放',
    projectCode: 'DMJFJC',
    categoryCode: 'DMJFBJZJ'
  },
  {
    name: '手术室',
    wpfKey: 'OperatinRoom',
    projectName: '手术室',
    projectCode: 'SSSJCXT',
    categoryCode: 'SSS'
  },
  {
    name: '物流小车',
    wpfKey: 'LogisticsVehicle',
    projectName: '物流小车',
    projectCode: 'AGVJQR',
    categoryCode: 'WLJQR'
  },
  {
    name: '危化品',
    wpfKey: 'HazMat',
    projectName: '危化品',
    projectCode: 'WHPKFJC',
  },
  {
    name: '重点区域',
    wpfKey: 'KeyAreas',
    projectName: '重点区域',
    projectCode: ''
  },
  {
    name: '空间椅位',
    wpfKey: 'SpaceChair',
    projectName: '空间椅位',
    projectCode: 'YWJC',
    categoryCode: 'YWSB'
  },
  {
    name: '监控管理',
    wpfKey: 'SecuritySys',
    projectName: '监控管理',
    projectCode: 'AFXT'
  }
]
// 受filterName包含的为使用modelcode跳转（关联模型），不是的为使用assetsid跳转（关联表计）
export const hasModelCodeFilterProjectName = ['冷热源监测', '医用气体', '配电监测', 'UPS监测', '风冷热泵监测', '给排水监测']
