/*
 * @Author: <PERSON>
 * @Date: 2021-12-14 15:39:39
 * @Last Modified by: mikey.z<PERSON>peng
 * @Last Modified time: 2022-03-15 10:42:07
 */
import http from './http'
import qs from 'qs'
//
/**
 *  @parms resquest 请求地址 例如：http://************:8088/request/...
 *  @param '/testIp'代表vue-cil中config，index.js中配置的代理
 */
const baseApi = __PATH.VUE_APP_BASE_API
const planApi = __PATH.VUE_APP_RESERVE_PLAN_API
const iomsApi = __PATH.VUE_APP_IOMS_API
// 获取用户信息
export function userLogin(params) {
  return http.loginRequest(`${baseApi}/sysLogin/login`, params)
}
// post请求
export function postFormAPI(params) {
  return http.postParamsQS(`${baseApi}/disposeRecord/listData`, params)
}

export function loginAPI(params) {
  return http.getQueryQS(`${baseApi}/login/login`, params)
}

// 菜单管理
export function getMenuPage(params) {
  return http.requestPost(`${baseApi}/menu/getMenuPage`, params)
}
export function getMenuList(params) {
  return http.requestPost(`${baseApi}/menu/getMenuList`, params)
}
export function submitMenu(params) {
  return http.requestPost(`${baseApi}/menu/saveOrUpdate`, params)
}
export function isMenuHaveChild(params) {
  return http.requestPost(`${baseApi}/menu/isHaveChild?id=${params}`, {})
}
export function delMenu(params) {
  return http.requestPost(`${baseApi}/menu/delMenu?id=${params}`, {})
}
// 角色管理
export function getRolePage(params) {
  return http.requestPost(`${baseApi}/role/getRolePage`, params)
}
export function getRoleList(params) {
  return http.requestPost(`${baseApi}/role/getRoleAll`, params)
}
export function roleState(params) {
  return http.requestPost(`${baseApi}/role/updateState?id=${params.id}&state=${params.state}`, {})
}
export function submitRole(params) {
  return http.requestPost(`${baseApi}/role/saveOrUpdate`, params)
}
export function delRole(params) {
  return http.requestPost(`${baseApi}/role/delRole?id=${params}`, {})
}

// 用户管理
export function getUserPage(params) {
  return http.requestPost(`${baseApi}/user/getUserListPage`, params)
}
export function userState(params) {
  return http.requestPost(`${baseApi}/user/updateState?id=${params.id}&state=${params.state}`, {})
}
export function submitUser(params) {
  return http.requestPost(`${baseApi}/user/saveUserData`, params)
}
export function delUser(params) {
  return http.requestPost(`${baseApi}/user/delUserById?id=${params}`, {})
}

// 字典管理
export function getDictPage(params) {
  return http.requestPost(`${baseApi}/dictData/getDictDataPage`, params)
}
export function getDictConfigPage(dictId, params) {
  return http.requestPost(`${baseApi}/dictData/getDictConfig?dictId=${dictId}`, params)
}
export function getDictDataList(params) {
  return http.requestPost(`${baseApi}/dictData/getDictDataList`, params)
}
export function submitDict(params) {
  return http.requestPost(`${baseApi}/dictData/saveDictData`, params)
}
export function submitDictConfig(params) {
  return http.requestPost(`${baseApi}/dictData/saveDictConfigData`, params)
}
export function delDict(params) {
  return http.requestPost(`${baseApi}/dictData/delDictById?id=${params}`, {})
}
export function delDictConfig(params) {
  return http.requestPost(`${baseApi}/dictData/delDictConfig?id=${params}`, {})
}
// 警情配置管理
// 预警配置
export function findIntelligencePrePlanEnactment(params) {
  return http.requestPost(`${planApi}/intelligencePrePlanEnactment/findIntelligencePrePlanEnactment`, params, {}, 'plan')
}
// 关联工单
export function getWorkOrderConfigList(params) {
  return http.postParamsQS(`${iomsApi}/workOrderConfigController/getWorkOrderConfigList`, params)
}
export function submitWarn(params) {
  return http.requestPost(`${baseApi}/alarmConfig/saveAlarmConfigData`, params)
}
export function getWarnPage(params) {
  return http.requestPost(`${baseApi}/alarmConfig/getAlarmConfigDataPage`, params)
}
export function delWarn(params) {
  return http.requestPost(`${baseApi}/alarmConfig/delAlarmConfigById?id=${params}`, {})
}
export function warnState(params) {
  return http.requestPost(`${baseApi}/alarmConfig/startOrCloseAlarmLinkage?${qs.stringify(params)}`, {})
}
