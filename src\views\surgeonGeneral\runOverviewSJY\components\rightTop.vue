<template>
  <div class="left-content-item">
    <CardTitle title="椅位统计" position="right" />
    <div v-loading="loading" class="card-content">
      <div class="chart-statistics">
        <div
          v-for="(item, index) in chartsNumList"
          :key="index"
          class="statistics-item"
        >
          <img
            v-if="index !== 4 && index !== 5"
            src="@/assets/images/qhdsys/charts.png"
          />
          <img
            v-else
            src="@/assets/images/qhdsys/chart-statistics.png"
            alt=""
          />
          <div class="statistics-item-num">
            <div class="statistics-item-num-title">{{ item.name }}</div>
            <div class="statistics-item-num-value" @click="openMonitorDevice(item.status)">
              <span
                class="num"
                :style="{
                  color: item.key == 'abnormalOccupancy' ? 'red' : '#ffdc83',
                }"
              >{{ item.num }}</span
              >
              <span>{{ item.unit }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>
    <monitorDeviceListDialog v-if="isMonitorDeviceList" :isDialog="isMonitorDeviceList" :roomData="roomData" :initParams="initParams" :isSpecial="true" @close="() => isMonitorDeviceList = false"/>
  </div>
</template>
<script>
import { monitorTypeList } from '@/assets/common/dict.js'
import CardTitle from './title'
import { chairStatistics } from '@/utils/runOverviewSJY'
import { formatThousand } from './utils'
export default {
  components: {
    CardTitle,
    monitorDeviceListDialog: () => import('@/views/spaceManage/components/monitorDeviceListDialog.vue')
  },
  data() {
    return {
      isMonitorDeviceList: false,
      roomData: {},
      initParams: {},
      loading: false,
      chartsNumList: [
        { key: 'chairTotal', name: '椅位总数', num: 0, unit: '台', status: '' },
        { key: 'inUse', name: '使用中', num: 0, unit: '台', status: 1 },
        { key: 'idle', name: '空闲数', num: 0, unit: '台', status: 0 },
        { key: 'abnormalOccupancy', name: '异常占用数', num: 0, unit: '台', status: 2 },
        { key: 'usageRateFormatted', name: '椅位使用率', num: 0, unit: '%', status: 1 },
        { key: 'idleRateFormatted', name: '空闲率', num: 0, unit: '%', status: 0 }
      ]
    }
  },
  mounted() {
    this.initRoomData()
    this.getChairStatistics()
  },
  methods: {
    openMonitorDevice(status) {
      this.initParams = {
        useStatus: status
      }
      this.isMonitorDeviceList = true
    },
    initRoomData() {
      const projectInfo = monitorTypeList.find((e) => e.wpfKey === 'SpaceChair')
      this.roomData = {
        tabName: 'SpaceChair',
        projectCode: projectInfo.projectCode,
        categoryCode: projectInfo.categoryCode
      }
    },
    getChairStatistics () {
      this.loading = true
      chairStatistics().then((res) => {
        if (res.data.code == 200) {
          this.chartsNumList.forEach((item) => {
            let value = res.data.data[item.key] || '0'
            item.num = formatThousand(value)
          })
        }
      }).finally(() => {
        this.loading = false
      })
    }
  }
}
</script>
<style lang="scss" scoped>
.left-content-item {
  width: 100%;
  height: calc(100% / 3 - 7px);
  background: url("~@/assets/images/qhdsys/new-card-bg.png") no-repeat;
  background-size: 100% 100%;
  margin-bottom: 10px;
  .card-content {
    height: calc(100% - 36px);
    width: 100%;
    padding: 16px;

    .chart-statistics {
      height: 100%;
      width: 100%;
      display: flex;
      flex-wrap: wrap;
      .statistics-item {
        display: flex;
        align-items: center;
        color: #fff;
        width: 50%;
        height: 56px;
        img {
          width: 50px;
          height: 56px;
        }
        .statistics-item-num {
          width: calc(100% - 50px);
          height: 100%;
          text-align: center;
          .statistics-item-num-title {
            font-size: 14px;
          }
          .statistics-item-num-value {
            background: url("~@/assets/images/qhdsys/chart-statistics-num-bg.png")
              no-repeat;
            background-size: 100% 100%;
            width: 100%;
            height: 32px;
            line-height: 32px;
            margin-top: 5px;
            .num {
              font-size: 18px;
              font-weight: bold;
              color: #ffdc83;
              margin-right: 5px;
            }
          }
        }
      }
    }
  }
}
</style>
