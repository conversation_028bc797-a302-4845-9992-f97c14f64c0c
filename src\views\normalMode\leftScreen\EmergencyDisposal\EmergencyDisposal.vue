<template>
  <div class="content" :class="screenSmall ? 'screen_small' : screenBack ? 'screen_back' : ''">
    <div v-if="pageType === 2" class="title">
      <div @click="gobackToDevice"><i class="el-icon-arrow-left"></i>返回</div>
    </div>
    <div class="search-form">
      <el-select
        v-model="searchFrom.projectCode"
        placeholder="报警来源"
        multiple
        collapse-tags
        clearable
        @change="getIncidentAndSourceGroup"
        @clear="
          () => {
            searchFrom.incidentType = ''
          }
        "
      >
        <el-option v-for="item in alarmSourceOptions" :key="item.projectCode" :label="item.projectName" :value="item.projectCode"> </el-option>
      </el-select>
      <el-select v-model="searchFrom.incidentType" placeholder="事件类型" clearable :disabled="!(searchFrom.projectCode.length == 1)">
        <el-option v-for="item in eventTypeOptions" :key="item.incidentType" :label="item.incidentName" :value="item.incidentType"> </el-option>
      </el-select>
      <el-select v-model="searchFrom.alarmLevel" placeholder="报警等级" clearable>
        <el-option v-for="item in alarmLevelOptions" :key="item.value" :label="item.label" :value="item.value"> </el-option>
      </el-select>
      <el-select ref="treeSelect" v-model="searchFrom.alarmSpaceId" clearable placeholder="空间位置" @clear="handleClear">
        <el-option hidden :value="searchFrom.alarmSpaceId" :label="areaName"> </el-option>
        <el-tree
          class="search-form-tree"
          :data="serverSpaces"
          :props="serverDefaultProps"
          :load="serverLoadNode"
          lazy
          :expand-on-click-node="false"
          :check-on-click-node="true"
          @node-click="handleNodeClick"
        >
        </el-tree>
      </el-select>
      <el-date-picker
        v-model="searchFrom.dataRange"
        popper-class="date-style"
        type="daterange"
        unlink-panels
        range-separator="至"
        start-placeholder="开始日期"
        end-placeholder="结束日期"
        value-format="yyyy-MM-dd"
        :picker-options="pickerOptions"
      >
      </el-date-picker>
      <el-button class="new-edition" style="background-color: transparent !important" @click="resetForm">重置</el-button>
      <el-button class="new-edition" style="background-color: transparent !important" @click="searchForm">查询</el-button>
    </div>
    <div class="eahart-list">
      <div class="echarts-left">
        <div class="bg-title">报警统计</div>
        <div class="bg-content">
          <div v-for="item in warnAnalysis" :key="item.name" class="right-item">
            <p class="right-title">{{ item.name }}</p>
            <div class="right-value">
              <p class="right-num">{{ item.count || 0 }}</p>
              <p v-if="item.timeType != 3" class="right-ratio">
                <span style="color: #fff; margin-right: 3px">环比</span>
                <span :style="{ color: item.ringRatioType == 1 ? '#00F872' : '#FF5454', fontStyle: 'oblique' }">{{ item.ringRatio }}</span>
                <img v-if="item.ringRatioType == 1" src="@/assets/images/peace/ratio-down.png" />
                <img v-else src="@/assets/images/peace/ratio-up.png" />
              </p>
            </div>
          </div>
        </div>
      </div>
      <div class="echarts-center">
        <div class="bg-title">近30日报警走势图</div>
        <div class="bg-content">
          <div id="trendEchart"></div>
        </div>
      </div>
      <div class="echarts-right">
        <!-- <div class="bg-title">报警来源分析</div> -->
        <div class="bg-content" style="height: 100%;">
          <div v-if="warnAnalysisShow" class="center-center">暂无数据</div>
          <div v-else style="width: 100%; height: 100%">
            <div id="alarmSourceEchart"></div>
            <!-- <div class="alarm-bg"></div> -->
            <!-- <div class="case-anim-icon"></div> -->
          </div>
        </div>
      </div>
    </div>
    <div class="batch-control">
      <el-button v-for="(item, index) in batchControlList" :key="index" :disabled="!selectAlarmList.length" class="new-edition" @click="batchControlEvent(item, selectAlarmList)">{{
        item.label
      }}</el-button>
    </div>
    <div class="table-list">
      <el-table
        ref="table"
        v-loading="tableLoading"
        :data="tableData"
        :resizable="false"
        height="calc(100% - 40px)"
        :cell-style="$tools.setCell(3)"
        :header-cell-style="$tools.setHeaderCell(3)"
        style="width: 100%"
        stripe
        element-loading-background="rgba(0, 0, 0, 0.2)"
        @selection-change="tableSelectChange"
        @sort-change="tableSortChange"
        @row-click="tableClick"
      >
        <el-table-column type="selection" width="60" align="center"></el-table-column>
        <el-table-column prop="incidentName" label="事件类型" show-overflow-tooltip></el-table-column>
        <el-table-column prop="alarmObjectName" label="对象" show-overflow-tooltip></el-table-column>
        <el-table-column prop="alarmStartTime" label="报警时间" sortable="custom" width="180">
          <template slot-scope="scope">{{ scope.row.alarmStartTime }}</template>
        </el-table-column>
        <el-table-column prop="alarmLevel" label="报警等级" sortable="custom" width="120">
          <span slot-scope="scope" :style="{ color: alarmLevelItem[scope.row.alarmLevel].color }">
            {{ alarmLevelItem[scope.row.alarmLevel].text }}
          </span>
        </el-table-column>
        <el-table-column prop="alarmValue" label="报警值" show-overflow-tooltip></el-table-column>
        <el-table-column prop="completeRegionName" label="位置" show-overflow-tooltip>
          <span slot-scope="scope">
            {{ scope.row.completeRegionName || '-' }}
          </span>
        </el-table-column>
        <el-table-column prop="alarmSource" label="报警来源" show-overflow-tooltip></el-table-column>
        <el-table-column prop="alarmStatus" label="报警处理状态" width="120">
          <div slot-scope="scope" :style="{ color: scope.row.alarmStatus == 0 ? '#FA403C' : scope.row.alarmStatus == 1 ? '#fd9434' : '#999' }">
            {{ scope.row.alarmStatus == 0 ? '未处理' : scope.row.alarmStatus == 1 ? '处理中' : '已关闭' }}
          </div>
        </el-table-column>
        <el-table-column prop="alarmAffirm" label="警情确认" width="100">
          <template slot-scope="scope">{{ alarmAffirmItem[scope.row.alarmAffirm] || '-' }}</template>
        </el-table-column>
        <!-- 0：非经典案例，1：经典案例 -->
        <el-table-column prop="classic" label="经典案例" width="100">
          <span slot-scope="scope">
            {{ scope.row.classic == 1 ? '已存' : '未存' }}
          </span>
        </el-table-column>
        <!-- 0：不屏蔽，1：屏蔽中 -->
        <el-table-column prop="shield" label="屏蔽状态" width="120">
          <div slot-scope="scope" style="display: flex; align-items: center; cursor: pointer; user-select: none">
            <el-switch
              :value="scope.row.shield == 1"
              :width="36"
              active-color="#0A84FF"
              inactive-color="#1A284A"
              @change="
                (val) => {
                  operating('shielded', scope.row)
                }
              "
            />
            <!-- <span style="margin-left: 4px">{{ scope.row.shield == 1 ? '已屏蔽' : '未屏蔽' }}</span> -->
          </div>
        </el-table-column>
        <el-table-column label="操作" width="250">
          <div slot-scope="scope" class="operationBtn">
            <!-- dispatch(派单) confirmAlarm(确警) remark(备注) shielded(屏蔽) close(关闭) viewDetails(查看详情) -->
            <span v-if="!scope.row.workNum" style="margin-right: 10px" @click="operating('dispatch', scope.row)">派单</span>
            <span v-if="![1, 2, 3, 4].includes(scope.row.alarmAffirm)" style="margin-right: 10px; color: #ff5454" @click="operating('confirmAlarm', scope.row)">警情确认</span>
            <!-- 已关闭只能查看备注 -->
            <span style="margin-right: 10px" @click="operating('remark', scope.row)">备注</span>
            <!-- 未处理关闭前先走警情确认流程 -->
            <span v-if="scope.row.alarmStatus != 2" style="margin-right: 10px" @click="operating('close', scope.row)">关闭</span>
            <span @click="operating('alarmDetails', scope.row)">查看</span>
          </div>
        </el-table-column>
      </el-table>
      <el-pagination
        :current-page="currentPage"
        :page-sizes="[15, 20, 30, 40]"
        :page-size="pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
        class="pagination"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      ></el-pagination>
    </div>
    <screenDialog v-if="scrDialog" :selectItems="screenSelectItems" :visible.sync="scrDialog" />
    <confirmAlarmDialog v-if="showConfirmAlarm" :visible.sync="showConfirmAlarm" :item="selectAlarmItem" />
    <remarkDialog v-if="remrDialog" :visible.sync="remrDialog" :item="selectAlarmItem" />
    <alarmVideoDialog v-if="alarmVideoShow" :visible.sync="alarmVideoShow" />
    <AlarmDetailDialog v-if="alarmDetailShow" ref="alarmDetail" :alarmId="selectAlarmItem.alarmId" :visible.sync="alarmDetailShow" @operating="operating" />
  </div>
</template>
<script>
import {
  GetAlarmTrendPc,
  GetAlarmSourceCount,
  getIncidentGroupByProjectCode,
  getSourceByEmpty,
  getSpaceInfoList,
  getStructureTree,
  GetAllAlarmRecord,
  GetPoliceInfoByApp,
  CloseAlarmRecord,
  AlarmAffirm,
  OneKeyDispatch,
  shield
} from '@/utils/peaceLeftScreenApi'
import { monitorTypeList } from '@/assets/common/dict.js'
import * as echarts from 'echarts'
import moment from 'moment'
moment.locale('zh-cn')
export default {
  components: {
    screenDialog: () => import('../components/screenDialog'),
    confirmAlarmDialog: () => import('../components/confirmAlarmDialog'),
    remarkDialog: () => import('../components/remarkDialog'),
    AlarmDetailDialog: () => import('../components/AlarmDetailDialog'),
    alarmVideoDialog: () => import('../components/alarmVideoDialog')
  },
  data() {
    return {
      areaName: '', // 选中 下拉树的name
      alarmSourceOptions: [], // 报警来源
      eventTypeOptions: [], // 事件类型
      serverSpaces: [], // 空间位置
      spaces: [], // 空间位置
      serverDefaultProps: {
        label: 'ssmName',
        isLeaf: 'leaf',
        children: 'children'
      },
      searchFrom: {
        projectCode: [], // 报警来源
        incidentType: '', // 事件类型
        alarmLevel: '', // 报警等级
        alarmSpaceId: '', // 空间位置
        dataRange: [] // 时间范围
      }, // 搜索条件
      alarmLevelOptions: [
        { value: '0', label: '通知' },
        { value: '1', label: '一般' },
        { value: '2', label: '紧急' },
        { value: '3', label: '重要' }
      ], // 报警等级
      pickerOptions: {
        shortcuts: [
          {
            text: '最近一周',
            onClick(picker) {
              const end = new Date()
              const start = new Date()
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 6)
              picker.$emit('pick', [start, end])
            }
          },
          {
            text: '最近一个月',
            onClick(picker) {
              const end = new Date()
              const start = new Date()
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 30)
              picker.$emit('pick', [start, end])
            }
          }
        ]
      },
      alarmLevelItem: {
        0: { text: '通知', color: '#3562DB' },
        1: { text: '一般', color: '#3562DB' },
        2: { text: '紧急', color: '#FF9435' },
        3: { text: '重要', color: '#FA403C' }
      },
      alarmAffirmItem: { 0: '未确认', 1: '真实报警', 2: '误报', 3: '演练', 4: '调试' },
      timeOrType: '', // 排序 0.按紧急降序  1.按紧急升序  2.按时间降序  3.按时间升序
      tableData: [],
      currentPage: 1,
      pageSize: 15,
      total: 0,
      tableLoading: false,
      warnAnalysis: [],
      warnAnalysisShow: true,
      timer: null,
      screenSmall: false, // 是否是小屏
      screenBack: false, // 是否是返回
      pageType: 1, // 1：默认 2：返回
      batchControlList: [
        // 批量控制
        {
          state: 1,
          label: '关闭'
        },
        {
          state: 2,
          label: '真实报警'
        },
        {
          state: 3,
          label: '误报'
        },
        {
          state: 4,
          label: '演练'
        },
        {
          state: 5,
          label: '调试'
        },
        {
          state: 6,
          label: '屏蔽'
        }
      ],
      scrDialog: false, // 屏蔽弹窗
      showConfirmAlarm: false, // 确警弹窗
      remrDialog: false, // 备注弹窗
      alarmDetailShow: false, // 报警详情弹窗
      selectAlarmList: [], // 选中的报警列表
      selectAlarmItem: {}, // 选中报警项
      screenSelectItems: [], // 屏蔽列表
      alarmVideoShow: false
    }
  },
  watch: {
    scrDialog(val) {
      if (!val) {
        this.initComponentData()
      }
    },
    showConfirmAlarm(val) {
      if (!val) {
        this.initComponentData()
      }
    },
    remrDialog(val) {
      if (!val) {
        this.initComponentData()
      }
    },
    alarmDetailShow(val) {
      if (!val) {
        this.initComponentData()
      }
    }
  },
  created() {
    // 初始化 根据模块过滤数据
    if (Object.hasOwn(this.$route.query, 'projectCode')) {
      this.searchFrom.projectCode = this.$route.query.projectCode.split(',') || []
      this.screenSmall = true
    } else {
      this.searchFrom.projectCode = []
      this.screenSmall = false
    }
    if (Object.hasOwn(this.$route.query, 'pageType')) {
      this.pageType = 2
      this.screenBack = true
      this.screenSmall = false
      try {
        window.chrome.webview.hostObjects.sync.bridge.SetIsShowShiBei(false)
      } catch (error) {}
    }
    if (Object.hasOwn(this.$route.query, 'dataType')) {
      const date = {
        0: [moment().format('YYYY-MM-DD'), moment().format('YYYY-MM-DD')],
        1: [moment().startOf('isoWeek').format('YYYY-MM-DD'), moment().endOf('isoWeek').format('YYYY-MM-DD')],
        2: [moment().startOf('month').format('YYYY-MM-DD'), moment().endOf('month').format('YYYY-MM-DD')],
        3: [moment().startOf('year').format('YYYY-MM-DD'), moment().endOf('year').format('YYYY-MM-DD')]
      }
      this.searchFrom.dataRange = date[this.$route.query.dataType]
    }
  },
  mounted() {
    this.getAlarmSource()
    this.getTreelist()
    this.search()
    this.timer = setInterval(() => {
      setTimeout(() => {
        this.search()
      }, 0)
    }, 1000 * timerNum)
    this.$once('hook:beforeDestroy', () => {
      clearInterval(this.timer)
      this.timer = null
    })
  },
  methods: {
    search() {
      if (this.scrDialog || this.showConfirmAlarm || this.remrDialog || this.alarmDetailShow) return
      this.initComponentData()
      this.getPoliceInfoByApp()
      this.getAlarmTrendPc()
      this.getAlarmSourceCount()
    },
    // 初始化组件数据
    initComponentData(tab = false) {
      this.selectAlarmList = []
      this.selectAlarmItem = {}
      // 初始化报警详情
      if (this.alarmDetailShow && !tab) {
        this.$refs.alarmDetail.getAlarmDetails()
        return
      }
      this.getDataList()
    },
    // 一键派单
    oneKeyDispatch(row) {
      const userInfo = this.$store.state.loginInfo.user
      const param = {
        alarmId: row.alarmId,
        alarmLevel: row.alarmLevel,
        alarmSourceName: row.alarmSource,
        incidentName: row.incidentName,
        spaceId: row.alarmSpaceId,
        userName: userInfo.staffName,
        userId: userInfo.staffId,
        projectCode: row.projectCode
      }
      OneKeyDispatch(param).then((res) => {
        if (res.data.code === '200') {
          this.$message({
            message: '已派单',
            type: 'success'
          })
          this.initComponentData()
        } else {
          // this.$message.error(res.data.msg)
        }
      })
    },
    // 确警
    alarmAffirm(status, list) {
      const params = {
        alarmAffirm: status,
        alarmId: list.map((item) => item.alarmId).join(','),
        projectCode: list.map((item) => item.projectCode).join(',')
      }
      AlarmAffirm(params).then((res) => {
        if (res.data.code === '200') {
          this.initComponentData()
        } else {
          // this.$message.error(res.data.msg)
        }
      })
    },
    // 关闭
    closeAlarmRecord(alarmId) {
      CloseAlarmRecord({ alarmId }).then((res) => {
        if (res.data.code === '200') {
          this.initComponentData()
        } else {
          // this.$message.error(res.data.msg)
        }
      })
    },
    // 批量处置按钮
    batchControlEvent(item, arr = this.selectAlarmList) {
      console.log(item.state, '处置状态')
      if (item.state === 1) {
        if (this.selectAlarmList.map((item) => item.alarmStatus).includes(2)) {
          this.$message.error('当前选择报警已被关闭，请重新选择')
          return
        }
        if (this.selectAlarmList.map((item) => item.alarmStatus).includes(0)) {
          this.$message.error('当前选择报警未处理，请确警后再进行关闭')
          return
        }
        this.$confirm(`是否关闭${arr.length}条报警记录?`, '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          cancelButtonClass: 'new-edition',
          confirmButtonClass: 'new-edition',
          customClass: 'confirm-box-class',
          type: 'warning'
        }).then(() => {
          this.closeAlarmRecord(arr.map((item) => item.alarmId).join(','))
        })
      } else if (item.state === 6) {
        if (this.selectAlarmList.map((item) => item.shield).includes(1)) {
          this.$message.error('当前选择报警已被屏蔽，请重新选择')
          return
        }
        this.scrDialog = !this.scrDialog
        this.screenSelectItems = arr
      } else if ([2, 3, 4, 5].includes(item.state)) {
        if (this.selectAlarmList.map((item) => item.alarmAffirm).includes(1 || 2 || 3 || 4)) {
          this.$message.error('当前选择报警已有警情确认，请重新选择')
          return
        }
        this.$confirm(`是否将${arr.length}条报警记录确警为${item.label}?`, '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          cancelButtonClass: 'new-edition',
          confirmButtonClass: 'new-edition',
          customClass: 'confirm-box-class',
          type: 'warning'
        }).then(() => {
          this.alarmAffirm(item.state - 1, arr)
        })
      }
    },
    // 子/孙组件流程操作
    operating(type, selectItem) {
      // dispatch(派单) confirmAlarm(确警) remark(备注) shielded(屏蔽) close(关闭) viewDetails(查看详情)
      this.selectAlarmItem = selectItem
      if (type === 'dispatch') {
        // 派单
        this.$confirm('是否确认派发确警工单?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          cancelButtonClass: 'new-edition',
          confirmButtonClass: 'new-edition',
          customClass: 'confirm-box-class',
          type: 'warning'
        }).then(() => {
          this.oneKeyDispatch(selectItem)
        })
      }
      // 确警
      if (type === 'confirmAlarm') {
        this.showConfirmAlarm = !this.showConfirmAlarm
      }
      // 备注
      if (type === 'remark') {
        this.remrDialog = !this.scrDialog
      }
      // 屏蔽
      if (type === 'shielded') {
        if (selectItem.shield === 1) {
          this.$confirm('是否取消屏蔽当前报警?', '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            cancelButtonClass: 'new-edition',
            confirmButtonClass: 'new-edition',
            customClass: 'confirm-box-class',
            type: 'warning'
          }).then(() => {
            shield({
              alarmId: selectItem.alarmId,
              alarmObjectId: selectItem.alarmObjectId,
              incidentType: selectItem.incidentType,
              shield: false
            }).then((res) => {
              if (res.data.code === '200') {
                this.$message({
                  message: '已取消屏蔽',
                  type: 'success'
                })
                this.initComponentData()
              }
            })
          })
        } else {
          this.batchControlEvent({ state: 6, label: '屏蔽' }, [selectItem])
        }
      }
      // 关闭
      // 未处理关闭前先走警情确认流程
      if (type === 'close') {
        if (selectItem.alarmStatus === 0) {
          this.$confirm('当前报警未处理，是否确警后再进行关闭？', '提示', {
            confirmButtonText: '确警',
            cancelButtonText: '取消关闭',
            cancelButtonClass: 'new-edition',
            confirmButtonClass: 'new-edition',
            customClass: 'confirm-box-class',
            type: 'warning'
          }).then(() => {
            this.operating('confirmAlarm', selectItem)
          })
        } else {
          this.$confirm('是否关闭当前报警记录?', '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            cancelButtonClass: 'new-edition',
            confirmButtonClass: 'new-edition',
            customClass: 'confirm-box-class',
            type: 'warning'
          }).then(() => {
            this.closeAlarmRecord(selectItem.alarmId)
          })
        }
      }
      // 报警详情
      if (type === 'alarmDetails') {
        this.alarmDetailShow = !this.alarmDetailShow
      }
      // 工单详情
      if (type === 'workOrderDetails') {
        this.workOderDialogData = []
        // 获取报警详情
        this.getAlarmDetails(selectItem.alarmId)
      }
      // 打开报警录像
      if (type === 'alarmVideo') {
        // 获取报警详情
        this.alarmVideoShow = !this.alarmVideoShow
      }
    },
    // table选择
    tableSelectChange(val) {
      this.selectAlarmList = val
    },
    // table排序
    tableSortChange(column) {
      if (column.prop === 'alarmStartTime') {
        this.timeOrType = column.order ? (column.order === 'ascending' ? 3 : 2) : ''
      } else {
        this.timeOrType = column.order ? (column.order === 'ascending' ? 1 : 0) : ''
      }
      this.getDataList()
    },
    // table点击
    tableClick(row) {
      const sendData = {
        DeviceCode: row.modelCode,
        assetsId: row.alarmDeviceId,
        assetsName: row.alarmDeviceName,
        spaceCode: row.alarmSpaceStr,
        projectCode: row.projectCode
      }
      this.$tools.alarmDataRowRealView(sendData)
      return
      // 如果关联了设备即跳转设备详情页
      if (row.modelCode) {
        const projectData = monitorTypeList.find(e => e.projectCode === row.projectCode)
        const params = {
          DeviceCode: row.modelCode,
          menuName: projectData.wpfKey,
          projectCode: projectData.projectCode
        }
        try {
          window.chrome.webview.hostObjects.sync.bridge.EquipmentRealView(JSON.stringify(params))
        } catch (error) {}
      }
    },
    // 获取全部报警记录
    getDataList() {
      const { projectCode, incidentType, alarmLevel, alarmSpaceId, dataRange } = this.searchFrom
      const params = {
        timeOrType: this.timeOrType,
        pageNo: this.currentPage,
        pageSize: this.pageSize,
        projectCode: projectCode.toString(),
        incidentType,
        alarmLevel,
        alarmSpaceId,
        startTime: dataRange[0],
        endTime: dataRange[1]
      }
      this.tableLoading = true
      GetAllAlarmRecord(params)
        .then((res) => {
          this.tableLoading = false
          if (res.data.code === '200') {
            this.tableData = res.data.data ? res.data.data.records : []
            this.total = res.data.data ? res.data.data.total : 0
            const currentTime = new Date()
            const givenTime = new Date(this.tableData[0].alarmStartTime)
            const timeDifferenceInSeconds = Math.floor((currentTime - givenTime) / 1000)
            // if (timeDifferenceInSeconds <= timerNum) {
            if (timeDifferenceInSeconds <= 20000) {
              console.log('在30秒内')
              try {
                window.chrome.webview.hostObjects.sync.bridge.IsPlayAlarmSound(true)
              } catch (error) {}
              const timer = setInterval(() => {
                try {
                  window.chrome.webview.hostObjects.sync.bridge.IsPlayAlarmSound(false)
                } catch (error) {}
                console.log('报警结束')
                clearInterval(timer)
              }, 3000)
            }
          } else {
            this.tableData = []
            this.total = 0
          }
        })
        .catch(() => {
          this.tableLoading = false
        })
    },
    // 查询
    searchForm() {
      console.log(this.searchFrom, '查询条件')
      this.currentPage = 1
      this.getDataList()
    },
    // 重置查询
    resetForm() {
      this.handleClear()
      Object.assign(this.searchFrom, {
        projectCode: [], // 报警来源
        incidentType: '', // 事件类型
        alarmLevel: '', // 报警等级
        dataRange: [], // 时间范围
        objectId: '' // 报警对象
      })
      this.currentPage = 1
      this.pageSize = 15
      this.total = 0
      this.getDataList()
    },
    // 获取服务空间树形结构
    getTreelist() {
      getStructureTree().then((res) => {
        if (res.data.code === 200) {
          this.spaces = res.data.data
          // 增加 懒加载节点
          res.data.data.map((e) => {
            e.leaf = false
          })
          // 转换为树形结构数据
          this.serverSpaces = this.$tools.transData(res.data.data, 'id', 'pid', 'children')
        }
      })
    },
    // 选择下拉树 数据
    handleNodeClick(data) {
      this.searchFrom.alarmSpaceId = data.id
      this.areaName = data.ssmName
      this.$refs.treeSelect.blur()
    },
    // 服务空间树结构点击获取自己及父节点ID并请求数据实现懒加载
    spaceTreeChange(value, resolve) {
      const child = this.$tools.ListTree(this.spaces, value.pid)
      child.push(value.id)
      const treeId = child.toString()
      const data = {
        current: 1,
        functionDictId: '',
        simCode: treeId,
        size: 999
      }
      getSpaceInfoList(data).then((res) => {
        if (res.data.code === 200) {
          if (typeof resolve === 'function') {
            var treeNodeData = JSON.parse(JSON.stringify(res.data.data.records))
            treeNodeData.map((e) => {
              e.leaf = true
            })
            resolve(treeNodeData)
          }
        }
      })
    },
    // 下拉树懒加载
    serverLoadNode(node, resolve) {
      console.log(node.level)
      if (node.level === 0) {
        return resolve(node.data)
      } else if (node.level > 0 && node.level < 4) {
        return resolve(node.data.children)
      } else {
        return this.spaceTreeChange(node.data, resolve)
      }
    },
    // 空间数据清除
    handleClear() {
      this.searchFrom.alarmSpaceId = ''
      this.areaName = ''
    },
    // 获取报警来源
    getAlarmSource() {
      getSourceByEmpty({}).then((res) => {
        if (res.data.code === '200') {
          this.alarmSourceOptions = res.data.data
        }
      })
    },
    // 获取事件类型以及报警来源分组
    getIncidentAndSourceGroup(val) {
      this.searchFrom.incidentType = ''
      getIncidentGroupByProjectCode({ projectCode: val.toString() }).then((res) => {
        if (res.data.code === '200') {
          this.eventTypeOptions = res.data.data
        }
      })
    },
    // 获取 报警统计数据
    getPoliceInfoByApp() {
      const newArr = ['本日新增', '本周新增', '本月新增', '本年新增']
      GetPoliceInfoByApp().then((res) => {
        const data = res.data
        if (data.code === '200') {
          this.warnAnalysis = data.data.countInfo
          this.warnAnalysis.forEach((item) => {
            item.name = newArr[item.timeType]
          })
        } else {
          // this.$message({
          //   message: data.msg,
          //   type: 'warning'
          // })
        }
      })
    },
    handleSizeChange(val) {
      this.pageSize = val
      this.getDataList()
    },
    handleCurrentChange(val) {
      this.currentPage = val
      this.getDataList()
    },
    // 获取 近30日报警走势图
    getAlarmTrendPc() {
      const params = {
        hourDayOrMouth: 1,
        startTime: moment().subtract(30, 'days').format('YYYY-MM-DD'),
        endTime: moment().format('YYYY-MM-DD')
      }
      GetAlarmTrendPc(params).then((res) => {
        const data = res.data
        if (data.code === '200') {
          this.getTrendEchart(data.data)
        } else {
          // this.$message({
          //   message: data.msg,
          //   type: 'warning'
          // })
        }
      })
    },
    getTrendEchart(list) {
      const getchart = echarts.init(document.getElementById('trendEchart'))
      const xArr = list.map((item) => {
        return item.time
      })
      const value = list.map((item) => {
        return item.list.reduce((a, b) => {
          return a + b.count
        }, 0)
      })
      var color = 'rgba(30, 243, 249'
      var lineY = []
      var data = {
        type: 'line',
        color: color + ')',
        smooth: true,
        areaStyle: {
          normal: {
            color: new echarts.graphic.LinearGradient(
              0,
              0,
              0,
              1,
              [
                {
                  offset: 0,
                  color: color + ', 0.3)'
                },
                {
                  offset: 1,
                  color: color + ', 0)'
                }
              ],
              false
            )
          }
        },
        symbolSize: 5,
        data: value
      }
      lineY.push(data)
      // }
      var option = {
        backgroundColor: 'rgba(128, 128, 128, 0)',
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            lineStyle: {
              color: 'rgb(30, 243, 249)'
            }
          },
          backgroundColor: '#0C2269',
          borderColor: '#5B617C',
          textStyle: {
            color: '#719DE7'
          },
          formatter: '{b0}</br>{c0}'
        },
        grid: {
          top: '14%',
          left: '4%',
          right: '4%',
          bottom: '12%'
        },
        xAxis: {
          type: 'category',
          boundaryGap: false,
          data: xArr,
          axisLabel: {
            textStyle: {
              color: '#50608B'
            },
            formatter: function (params) {
              return params.substring(5)
              // return params.substring(5).replace('-', '.')
            }
          },
          axisLine: {
            lineStyle: {
              color: '#50608B',
              width: 1
            }
          }
        },
        yAxis: {
          type: 'value',
          axisLabel: {
            formatter: '{value}',
            textStyle: {
              color: '#50608B'
            }
          },
          splitLine: {
            lineStyle: {
              color: '#2A4382'
            }
          }
        },
        series: lineY
      }
      getchart.setOption(option)
      // })
      // 随着屏幕大小调节图表
      window.addEventListener('resize', () => {
        getchart.resize()
      })
    },
    // 获取 报警来源分析数据
    getAlarmSourceCount() {
      GetAlarmSourceCount({ timeType: 3 }).then((res) => {
        const data = res.data
        if (data.code === '200') {
          if (data.data.length) {
            this.warnAnalysisShow = false
            this.$nextTick(() => {
              // 报警来源分析
              this.getAlarmSourceEchart(data.data)
            })
          } else {
            this.warnAnalysisShow = true
          }
        } else {
          // this.$message({
          //   message: data.msg,
          //   type: 'warning'
          // })
        }
      })
    },
    getAlarmSourceEchart(list) {
      const getchart = echarts.init(document.getElementById('alarmSourceEchart'))
      getchart.resize()
      var scaleData = list
      var xdata = list.map((item) => {
        return item.projectName
      })
      var data = []
      var color = ['rgba(31, 250, 255, 0.3)', 'rgba(255, 227, 166, 0.28)', 'rgba(147, 130, 255, 0.26)', 'rgba(0, 248, 114, 0.24)', 'rgba(46, 119, 251, 0.3)']
      var borderColor = ['rgba(31, 250, 255, 1)', 'rgba(255, 227, 166, 1)', 'rgba(147, 130, 255, 1)', 'rgba(0, 248, 114, 1)', 'rgba(46, 119, 251, 1)']
      for (var i = 0; i < scaleData.length; i++) {
        data.push({
          value: scaleData[i].count,
          name: scaleData[i].projectName,
          itemStyle: {
            normal: {
              borderWidth: 1,
              shadowBlur: 200,
              borderColor: borderColor[i],
              color: color[i],
              label: {
                normal: { show: true }
              }
            }
          }
        })
      }
      var seriesObj = [
        {
          name: '',
          type: 'pie',
          clockWise: false,
          center: ['25%', '50%'],
          radius: ['40%', '60%'],
          hoverAnimation: true,
          label: {
            normal: {
              show: false,
              position: 'center',
              formatter: '{text|{b}}\n{c} ({d}%)',
              rich: {
                value: {
                  padding: 5,
                  align: 'center',
                  verticalAlign: 'middle',
                  fontSize: 20
                },
                label: {
                  align: 'center',
                  verticalAlign: 'middle',
                  fontSize: 12
                }
              }
            },
            emphasis: {
              show: true,
              textStyle: {
                fontSize: '12',
                color: '#fff'
              }
            }
          },
          data: data
        }
      ]
      const option = {
        backgroundColor: 'rgba(128, 128, 128, 0)',
        legend: {
          orient: 'vartical',
          type: 'scroll',
          x: 'left',
          top: 'center',
          left: '50%',
          bottom: '0%',
          data: xdata,
          itemWidth: 8,
          itemHeight: 8,
          itemGap: 16,
          pageTextStyle: {
            color: '#fff'
          },
          textStyle: {
            //  fontSize: 18,//字体大小
            color: '#B3C2DD' //  字体颜色
          },
          formatter: function (name) {
            var oa = option.series[0].data
            var num = oa.reduce((sum, e) => sum + e.value, 0)
            for (var i = 0; i < option.series[0].data.length; i++) {
              if (name === oa[i].name) {
                return ' ' + name + ' (' + oa[i].value + ')     ' + ((oa[i].value / num) * 100).toFixed(2) + '%'
              }
            }
          }
        },
        series: seriesObj
      }
      getchart.clear()
      getchart.setOption(option)
      // 随着屏幕大小调节图表
      window.addEventListener('resize', () => {
        getchart.resize()
      })
    },
    gobackToDevice() {
      try {
        // 来源是 运行监控
        if (this.$route.query.sourcePath === '/operationMonitoring') {
          window.chrome.webview.hostObjects.sync.bridge.LeftMenuSwitch('IsCheckedYX')
        } else {
          window.chrome.webview.hostObjects.sync.bridge.SetIsShowShiBei(true)
        }
      } catch (error) {}
      this.$router.go(-1)
    }
  }
}
</script>
<style lang="scss" scoped>
@import '../../../../assets/sino-ui/common/var.scss';
.screen_small {
  padding-left: 110px !important;
  padding-bottom: 80px !important;
}
.screen_back {
  padding-bottom: 40px !important;
}
html,
.content {
  position: relative;
  // background-color: #031553;
  background: center;
  // background: url('~@/assets/images/qhdsys/bj.png') no-repeat;

  box-sizing: border-box;
  width: 100%;
  height: 100%;
  padding: 1.25rem 0.625rem 0.625rem 0.625rem;
  .title {
    height: 30px;
    // position: relative;
    position: absolute;
    left: 16px;
    top: 10px;
    div {
      cursor: pointer;
      position: absolute;
      top: 5px;
      left: 0;
      width: 80px;
      height: 30px;
      line-height: 30px;
      text-align: center;
      color: #618ad3;
      font-size: 0.875rem;
      white-space: nowrap;
      background-image: url('~@/assets/images/peace/btn-back.png');
      background-size: 100% 100%;
      background-repeat: no-repeat;
    }
  }
  .screen_back {
    .title {
      position: initial;
      div {
        left: 10px;
        top: 10px;
      }
    }
  }
  .search-form {
    height: 2.5rem;
    width: 100%;
    & > div {
      margin-right: 10px;
    }
    ::v-deep .el-input__inner {
      background: center;
      border: 1px solid $input-border-color;
      border-radius: 0;
      color: $color;
      height: inherit;
      font-family: PingFangSC-Medium, PingFang SC;
    }
    ::v-deep .el-range-input {
      background: center;
      color: $color;
    }
    ::v-deep .el-range-separator,
    ::v-deep .el-range__icon {
      color: #3769be;
    }
  }
  .eahart-list {
    width: 100%;
    height: 35%;
    margin-top: 0.7rem;
    display: flex;
    justify-content: space-between;
    .echarts-left {
      width: 20%;
      background: url('~@/assets/images/qhdsys/emergency-disposal-l.png') no-repeat;
      background-size: 100% 100%;
      padding: 4px;
      box-sizing: border-box;
      .bg-content {
        display: flex;
        flex-wrap: wrap;
        align-content: space-around;
        // padding-left: 35px;
        .right-item {
          width: 50%;
        }
        .right-title {
          font-size: 16px;
          font-weight: 500;
          color: #7eaef9;
          line-height: 19px;
        }
        .right-value {
          margin-top: 3px;
          display: flex;
          align-items: center;
        }
        .right-num {
          font-size: 26px;
          font-weight: bold;
          color: #ffffff;
          line-height: 30px;
          font-style: oblique;
        }
        .right-ratio {
          margin-left: 10px;
          padding-left: 7px;
          font-size: 14px;
          font-weight: 400;
          height: 17px;
          line-height: 17px;
          background: linear-gradient(90deg, rgba(106, 143, 211, 0.41) 0%, rgba(106, 143, 211, 0) 94%);
          img {
            margin-left: 2px;
            width: 6px;
            height: 9px;
          }
        }
      }
    }
    .echarts-center {
      width: 49%;
      background: url('~@/assets/images/qhdsys/emergency-disposal-center.png') no-repeat;
      background-size: 100% 100%;
      padding: 3px;
      box-sizing: border-box;
    }
    .echarts-right {
      width: 30%;
      background: url('~@/assets/images/qhdsys/emergency-disposa.png') no-repeat;
      background-size: 100% 100%;
      padding: 3px;
      box-sizing: border-box;
    }
    .bg-title {
      // margin-top: 5px;
      height: 2.5rem;
      line-height: 2.5rem;
      color: #d4e3f9;
      padding-left: 3rem;
      font-family: TRENDS;
    }
    .center-center {
      width: 100%;
      height: 100%;
      display: flex;
      align-items: center;
      justify-content: center;
      color: #4d5880;
      font-size: 16px;
    }
    .bg-content {
      position: relative;
      box-sizing: border-box;
      padding: 10px 10px 15px 10px;
      width: 100%;
      height: calc(100% - 2.5rem);
      display: flex;
      #trendEchart,
      #alarmSourceEchart {
        width: 100%;
        height: 100%;
        position: relative;
        z-index: 2;
      }
      .alarm-bg {
        width: 15rem;
        height: 15rem;
        position: absolute;
        top: calc(50% - 7.6rem);
        left: 4.5%;
        background: url('~@/assets/images/peace/left-operation-warn-bg.png') no-repeat;
        background-size: 100% 100%;
      }
      .case-anim-icon {
        position: absolute;
        top: calc(50% - 1rem);
        left: calc(25% - 0.5rem);
        width: 2rem;
        height: 2rem;
        background: url('~@/assets/images/peace/icon-warn.png') no-repeat;
        background-size: 100% 100%;
      }
    }
  }
  .batch-control {
    height: 2.5rem;
    margin-top: 1rem;
    .is-disabled {
      background-image: url('~@/assets/images/qhdsys/btn-bg.png') !important;
    }
  }
  .table-list {
    width: 100%;
    height: calc(65% - 7.5rem);
    margin-top: 0.8rem;
    ::v-deep .el-table th.el-table__cell > .cell {
      white-space: nowrap;
    }
  }
}
::v-deep .el-table--striped .el-table__body tr.el-table__row--striped td {
  background-color: rgba(168, 172, 171, 0.08) !important; /* def2ff f2faff */
}
::v-deep .el-switch .el-switch__core::after {
  width: 18px;
  height: 18px;
  top: 0px;
  background-color: #1E3370 !important;
}
::v-deep .el-switch.is-checked .el-switch__core::after {
  left: calc(100% - 1px) !important;
}
::v-deep .is-disabled {
  background-color: transparent !important;
}
</style>
<style lang="scss">
.search-form-tree {
  color: #fff !important;
  background: transparent !important;
  .el-tree-node__content:hover {
    background: transparent !important;
    .el-tree-node__label {
      color: #ffe3a6;
    }
  }
}
</style>
