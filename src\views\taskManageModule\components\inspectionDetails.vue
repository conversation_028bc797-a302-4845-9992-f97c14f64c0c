<template>
  <div v-if="dialogShow" class="inspectionDetails">
    <div class="right-content">
      <div class="content">
        <div class="bg-title el-dialog__header">
          <span>巡检详情</span>
          <!-- <span>{{ dialogData.deviceName }}</span> -->
          <i class="el-icon-close" @click="closeDeviceDialog"></i>
        </div>
        <div class="bg-content room-info-box">
          <div class="module-container" style="height: calc(35%); background: rgba(133,145,206,0.15);margin-bottom: 16px;">
            <div class="info-header clear">
              <p class="info-header-text fl">施工信息</p>
              <p class="info-header-status fl" :style="{color: `rgb(${statusObj[infoData.maintainStatus]?.color ?? ''}, 1)`, background: `rgb(${statusObj[infoData.maintainStatus]?.color ?? ''}, 0.2)`}">{{ statusObj[infoData.maintainStatus]?.name ?? '---' }}</p>
            </div>
            <div class="module-content info-list" style="height: calc(100% - 44px)">
              <div v-for="item in infoList" :key="item.label" class="info-list-item">
                <p class="item-label">{{ item.label }}：</p>
                <p class="item-value">{{ infoData[item.key] || '---' }}</p>
              </div>
            </div>
          </div>
          <div class="module-container" style="height: calc(65%)">
            <div class="module-header">
              <div class="title-left">
                <p class="title-left-text showFloorName">巡检</p>
              </div>
            </div>
            <div class="module-content" style="height: calc(100% - 44px); overflow: auto;">
              <div style="background: rgba(133,145,206,0.15);margin-top: 16px;">
                <div class="info-list">
                  <div class="info-list-item">
                    <p class="item-label">巡检任务名称：</p>
                    <p class="item-value">{{ infoData.maintainPlanName || '---' }}</p>
                  </div>
                  <div class="info-list-item">
                    <p class="item-label">周期类型：</p>
                    <p class="item-value">{{ typeOptions[infoData.cycleType] || '---' }}</p>
                  </div>
                  <div class="info-list-item">
                    <p class="item-label">责任班组：</p>
                    <p class="item-value">{{ infoData.workDeptName || '---' }}</p>
                  </div>
                  <div class="info-list-item">
                    <p class="item-label">巡检点：</p>
                    <p class="item-value">{{ infoData.maintainPoint || '---' }}</p>
                  </div>
                  <div class="info-list-item">
                    <p class="item-label">巡检时间：</p>
                    <p class="item-value">{{ infoData.workTime || '---' }}</p>
                  </div>
                </div>
              </div>

              <div style="background: rgba(133,145,206,0.15);margin-top: 16px;">
                <div class="info-header">
                  <p class="info-header-text">巡检内容</p>
                </div>
                <div class="info-list">
                  <div v-for="(item, index) in infoData.workContent" :key="index" class="info-list-item">
                    <p class="item-label" style="width: auto;">{{ index + 1 }}、</p>
                    <p class="item-value">{{ item || '---' }}</p>
                  </div>
                </div>
              </div>

              <div style="background: rgba(133,145,206,0.15);margin-top: 16px;">
                <div class="info-header">
                  <p class="info-header-text">巡检执行</p>
                </div>
                <div class="info-list">
                  <div class="info-list-item">
                    <p class="item-label">巡查情况：</p>
                    <p class="item-value">{{ infoData.maintainStatus || '---' }}</p>
                  </div>
                  <div class="info-list-item">
                    <p class="item-label">执行人员：</p>
                    <p class="item-value">{{ infoData.actualExecutionUserName || '---' }}</p>
                  </div>
                  <div class="info-list-item">
                    <p class="item-label">实际巡检时间：</p>
                    <p class="item-value">{{ infoData.actualExecutionTime || '---' }}</p>
                  </div>
                  <!-- <div class="info-list-item">
                    <p class="item-label">定位状态：</p>
                    <p class="item-value">{{ false || '---' }}</p>
                  </div> -->
                </div>
              </div>

              <div style="background: rgba(133,145,206,0.15);margin-top: 16px;">
                <div class="info-header">
                  <p class="info-header-text">巡检结果</p>
                </div>
                <div class="info-list">
                  <div class="info-list-item">
                    <p class="item-label">结果：</p>
                    <p class="item-value">{{ infoData.state || '---' }}</p>
                  </div>
                  <div class="info-list-item">
                    <p class="item-label">描述：</p>
                    <p class="item-value">{{ infoData.details || '---' }}</p>
                  </div>
                  <!-- <div class="info-list-item">
                    <p class="item-label">语音：</p>
                    <p class="item-value">{{ false || '---' }}</p>
                  </div> -->
                  <div class="info-list-item">
                    <p class="item-label">图片：</p>
                    <p class="item-value">
                      <img :src="$tools.imgUrlTranslation(infoData.imageUrl)">
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { GetInspectionDetails } from '@/utils/spaceManage'
export default {
  name: 'inspectionDetails',
  props: {
    dialogData: {
      type: Object,
      default: () => {}
    },
    dialogShow: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      statusObj: {
        0: {name: '未开始', color: '255, 148, 53'},
        1: {name: '进行中', color: '139, 221, 245'},
        2: {name: '作业结束', color: '97, 226, 157'},
        3: {name: '超时', color: '255, 45, 85'}
      },
      infoData: {
        workContent: []
      },
      infoList: [
        {label: '施工名称', key: 'maintainName'},
        {label: '施工类型', key: 'maintainType'},
        {label: '施工位置', key: 'maintainLocalName'},
        {label: '施工期限', key: 'maintainTime'},
        {label: '施工内容', key: 'maintainContent'},
        {label: '现场负责人', key: 'localManagePerson'},
        {label: '联系电话', key: 'maintainPhone'}
      ],
      typeOptions: {
        8: '单次',
        6: '每日',
        0: '每周',
        2: '每月',
        3: '季度',
        5: '全年'
      }
    }
  },
  computed: {

  },
  watch: {

  },
  created() {
    console.log('详情信息', this.dialogData)
    this.getInspectionDetails()
  },
  mounted() {
  },
  methods: {
    getInspectionDetails() {
      let params = {
        recordId: this.dialogData.deviceId
      }
      GetInspectionDetails(params).then(res => {
        if (res.data.code == 200) {
          this.infoData = res.data.data
        }
      })
    },
    closeDeviceDialog() {
      this.$emit('configCloseDialog')
    }
  }
}
</script>

<style lang="scss" scoped>
@import '../../spaceManage/style/module.scss';
.inspectionDetails {
  position: absolute;
  top: 0;
  right: 0%;
  // width: 22%;
  width: 100%;
  height: 100%;
  // margin-top: 2%;
  height: 100%;
  .content {
    padding: 0px 25px 10px 35px;
    height: 100%;
  }
  .right-content {
    margin: 0 0 0 auto;
    width: 24.573%;
    background: url('~@/assets/images/qhdsys/bg-mask.png') no-repeat;
    height: 100%;
    background-color: transparent;
    // background: url('~@/assets/images/center/bg-80.png') no-repeat;
    background-size: 100% 100%;
    box-sizing: border-box;
    position: relative;
    .bg-title {
      background-color: rgba(255, 224, 152, 0.12) !important;
      height: 44px;
      line-height: 44px;
      padding: 0 10px 0 1rem;
      color: #ffca64;
      font-family: TRENDS;

      width: 100%;
      span {
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        display: inline-block;
        width: calc(100% - 40px);
      }
      i {
        float: right;
        line-height: 44px;
        font-size: 1.5rem;
        cursor: pointer;
      }
    }
    .bg-content {
      position: relative;
      box-sizing: border-box;
      padding: 10px 2px 10px 2px;
      width: 100%;
      height: calc(100% - 44px);
    }
    .room-info-box {
      color: #fff;
      display: flex;
      flex-direction: column;
      .module-header {
        padding-left: 30px;
        padding-right: 10px;
        width: 100%;
        background: url("@/assets/images/qhdsys/bg-bt.png") no-repeat;
        background-size: contain;
      }
      .info-header {
        padding: 6px 8px;
        line-height: 22px;
        background: rgba(133,145,206,0.15);
        .info-header-text {
          font-weight: 500;
          font-size: 15px;
          color: #FFFFFF;
        }
        .info-header-status {
          margin-left: 10px;
          font-weight: 400;
          font-size: 14px;
          line-height: 16px;
          padding: 3px 8px;
          border-radius: 100px;
        }
      }
      .info-list {
        padding: 0px 16px;
        .info-list-item {
          display: flex;
          .item-label {
            font-weight: 400;
            font-size: 14px;
            color: #B0E3FA;
            line-height: 30px;
            width: 100px;
          }
          .item-value {
            font-weight: 400;
            font-size: 14px;
            color: #FFFFFF;
            line-height: 30px;
            img {
              margin-top: 10px;
              width: 100px;
              height: 100px;
            }
          }
        }
      }
    }
  }
}
</style>
