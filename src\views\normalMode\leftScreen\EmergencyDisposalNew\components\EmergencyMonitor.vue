<template>
  <div class="component EmergencyMonitor">
    <div class="EmergencyMonitor__head">
      <ul class="EmergencyMonitor__nav">
        <li class="EmergencyMonitor__nav__item" :class="{ active: currentMonitorType === videoType.REC }"
            @click="switchMonitor(videoType.REC)">报警录像</li>
        <li style="margin-left: 16px;" class="EmergencyMonitor__nav__item" :data-objectId="objectId"
            :class="{ active: currentMonitorType === videoType.LIVE }" @click="switchMonitor(videoType.LIVE)">实时画面
        </li>
        <!-- 摄像头选择 -->
        <PanelCameraSelect v-if="hasSource" v-model="playingList" :title="typeName"
                           class="EmergencyMonitor__control" :visible.sync="showPickerPanel" :single="singlePlayer" :source="monitorSource" />
      </ul>
    </div>

    <div class="EmergencyMonitor__content">
      <div v-if="!hasSource" class="EmergencyMonitor__empty">
        <img class="EmergencyMonitor__empty__img" src="@/assets/images/common/icon-alarm.png">
        <p class="EmergencyMonitor__empty__text">暂无可播放画面</p>
      </div>
      <div v-else class="EmergencyMonitor__data">
        <div class="EmergencyMonitor__view" :class="{ single: singlePlayer }">
          <div v-for="item of playingList" :key="item.id" class="EmergencyMonitor__item"
               @click="switchSingleView(item.id)">
            <div class="EmergencyMonitor__item__wrapper">
              <div class="EmergencyMonitor__item__player">
                <RtspCavans :rtspUrl="item.fileUrl" />
              </div>
            </div>
          </div>
        </div>
        <div class="EmergencyMonitor__bottom">
          <div class="EmergencyMonitor__split-button EmergencyMonitor__split-button--1"
               :class="{ active: singlePlayer }" @click="switchSingleView('')">1</div>
          <div class="EmergencyMonitor__split-button EmergencyMonitor__split-button--4"
               :class="{ active: !singlePlayer }" @click="switchMultipleView">4</div>
        </div>
      </div>
    </div>

  </div>
</template>

<script>

import { getCameraListByImsId, queryLimAlarmFile } from '@/utils/peaceLeftScreenApi'
export default {
  name: 'EmergencyMonitor',
  components: {
    RtspCavans: () => import('@/views/elevator/components/monitorOverview/components/rtspCavans'),
    PanelCameraSelect: () => import('./PanelCameraSelect')
  },
  props: {
    alarmId: String,
    objectId: String,
    detail: Object
  },
  data() {
    return {
      videoSource: [], // 可播的报警画面
      cameraSource: [], // 可播放的摄像头资源
      currentMonitorType: 'record', // 当前监控类型
      playingList: [], // 当前播放的资源列表
      showPickerPanel: false,
      singlePlayer: false
    }
  },
  computed: {
    // 是否有可播放的资源
    hasSource: function () {
      return this.monitorSource.length > 0
    },
    videoType: function () {
      return {
        // 录像
        REC: 'record',
        // 直播
        LIVE: 'live'
      }
    },
    monitorSource: function () {
      if (this.currentMonitorType === this.videoType.REC) {
        return this.videoSource
      } else {
        return this.cameraSource
      }
    },
    typeName: function () {
      return this.currentMonitorType === this.videoType.REC ? '报警录像' : '附近监控'
    }
  },
  watch: {
    objectId: function (val) {
      if (val) this.loadCameras()
    }
  },
  mounted() {
    this.loadVideos()
  },
  methods: {
    /**
     * 获取摄像头信息
     */
    loadCameras() {
      getCameraListByImsId({ imsCode: this.objectId, alarmStartTime: this.detail.record.alarmStartTime })
        .then(res => {
          if (res.data.code == 200) {
            this.cameraSource = res.data.data.map(item => ({
              id: item.icmId,
              name: item.icmName,
              fileUrl: item.icmRtsp
            }))
          }
        })
    },
    /** 切换监控类型 */
    switchMonitor(type) {
      if (this.currentMonitorType === type) return
      this.currentMonitorType = type
      this.switchMultipleView()
    },
    /** 切换成单独的界面 */
    switchSingleView(id) {
      const target = this.playingList.find(x => x.id === id)
      const [first] = this.playingList
      this.playingList = [target ?? first]
      this.singlePlayer = true
    },
    // 多屏播放
    switchMultipleView() {
      this.playingList = this.monitorSource.slice(0, 4)
      this.singlePlayer = false
    },
    // 获取报警画面信息
    loadVideos() {
      queryLimAlarmFile({
        alarmId: this.alarmId,
        fileType: '2'
      })
        .then(res => {
          if (res.data.code == 200) {
            const data = res.data.data
            data.forEach((item, index) => {
              const name = item.cameraName || '报警录像-' + (index + 1)
              item.name = `${name}(60s)`
            })
            this.videoSource = data
            this.switchMultipleView()
          }
        })
    }
  }
}

</script>

<style lang='scss' scoped>
.EmergencyMonitor {
  height: 100%;
  background: rgba(133, 145, 206, 0.15);
  position: relative;

  &__head {
    position: relative;
    padding: 16px;
    height: 52px;
  }

  &__nav {
    display: flex;

    &__item {
      cursor: pointer;
    }

    &__item:not(.active) {
      color: #A4AFC1;
    }
  }

  &__control {
    position: absolute;
    right: 16px;
    top: 10px;
    height: 28px;
  }

  &__view {
    padding-left: 16px;
    display: flex;
    flex-flow: row wrap;

    &.single {
      .EmergencyMonitor__item:first-child {
        flex-basis: 100%;
      }
    }
  }

  &__item {
    margin: 0 16px 16px 0;
    width: calc(50% - 16px);
    overflow: hidden;

    &__wrapper {
      width: 100%;
      height: 0;
      // padding-top: 56.25%;
      // 16:10
      padding-top: 62.5%;
      overflow: hidden;
      position: relative;
    }

    &__player {
      position: absolute;
      left: 0;
      top: 0;
      right: 0;
      bottom: 0;
    }
  }

  &__bottom {
    display: flex;
    padding: 16px;
  }

  &__split-button {
    width: 40px;
    height: 41px;
    line-height: 40px;
    text-align: center;
    font-size: 18px;
    cursor: pointer;

    &.active {
      background: url('~@/assets/images/common/bg-split-cover.png') no-repeat;
    }

    &--1 {
      background: url('~@/assets/images/common/bg-split-1.png') no-repeat;
    }

    &--4 {
      margin-left: 10px;
      background: url('~@/assets/images/common/bg-split-4.png') no-repeat;
    }
  }

  &__video {
    width: 100%;
    height: auto;
    border: solid 1px silver;
  }

  &__content {
    height: calc(100% - 52px);
    overflow: hidden;
  }

  &__empty {
    margin: 0 16px 16px;
    background-color: #06102C;
    height: calc(100% - 16px);
    display: flex;
    flex-flow: column nowrap;
    align-items: center;
    justify-content: center;

    &__img {
      pointer-events: none;
    }

    &__text {
      margin-top: 32px;
      color: #A6AFBF;
    }
  }
}
</style>
