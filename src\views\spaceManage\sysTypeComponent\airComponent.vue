<template>
  <div class="moveComponent">
    <div class="module-container" style="height: calc(22%)">
      <div class="module-header">
        <div class="title-left" style="width: 80%">
          <p class="title-left-text showFloorName">{{ roomData.title }} - 设备台账</p>
        </div>
        <div class="title-right">
          <div class="title-detail" @click="allTableChange('assets')">详情</div>
        </div>
      </div>
      <div class="module-content" style="height: calc(100% - 44px)">
        <div class="statistics_top_new">
          <div v-for="(item, index) in statisticsData" :key="index" class="statistics_top_item">
            <img src="@/assets/images/qhdsys/statistics-new.png" alt="" />
            <div class="statistics_top_content">
              <p class="statistics_top_title">{{ item.name }}</p>
              <div class="font_bg">
                <p class="color_font" :style="{ color: item.key == 'abnormalDeviceTotal' ? '#FF2D55' : '' }">{{ deviceAccountStatistics[item.key] || 0 }}</p>
                <p class="color_unit">{{ item?.unit ?? '' }}</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="module-container" style="height: calc(25%)">
      <div class="module-header">
        <div class="title-left">
          <p class="title-left-text">设备状态</p>
        </div>
      </div>
      <div class="module-content" style="height: calc(100% - 48px)">
        <div id="EChart" style="height: 100%"></div>
      </div>
    </div>
    <div class="module-container" style="height: calc(25%)">
      <div class="module-header">
        <div class="title-left">
          <!-- <p class="title-left-icon"></p> -->
          <p class="title-left-text">设备监测 {{ isSelectedTypeData.name }}</p>
        </div>
        <div class="title-right">
          <div class="icon-box">
            <!-- <img v-if="sortType == 1" src="../../../assets/images/filter.png" class="filter" @click="showCheckGroup = !showCheckGroup" /> -->
            <div v-show="showCheckGroup" class="panel-s">
              <el-checkbox-group v-model="checkList" fill="#52FFFC" @change="checkBoxChanged">
                <el-checkbox v-for="item in flowList" :key="item.value" :label="item.label"></el-checkbox>
              </el-checkbox-group>
            </div>
            <img src="../../../assets/images/order_more.png" class="order-more" @click="allTableChange('entity')" />
          </div>
        </div>
      </div>
      <div class="module-content" style="height: calc(100% - 48px)">
        <el-table
          v-el-table-infinite-scroll="tableLoadMore"
          v-loading="deviceTableLoading"
          class="table-center-transfer"
          :data="deviceList"
          height="100%"
          :cell-style="{ padding: ' 8px', backgroundColor: 'transparent', border: 'none', padding: '3px' }"
          :header-cell-style="{ background: '#8591CE26!important', color: '#8BDDF5FF', padding: '4px 8px', fontWeight: 'bold' }"
          style="width: 100%"
          element-loading-background="rgba(0, 0, 0, 0.2)"
          @row-click="tableRowClick"
          @row-dblclick="tabledblClick"
        >
          <el-table-column v-for="(column, index) in tableColumn" :key="index" :prop="column.prop" :label="column.label" :min-width="column.minWidth" show-overflow-tooltip>
            <template slot-scope="scope">
              <table-render v-if="column.render" :sc="scope" :row="scope.row" :render="column.render"></table-render>
              <div v-else-if="!column.formatter">
                {{ scope.row[column.prop] }}
              </div>
              <div v-else>
                {{ column.formatter(scope.row) }}
              </div>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </div>
    <div class="module-container" style="height: calc(28%)">
      <div class="module-header">
        <div class="title-left">
          <p class="title-left-text">设备显示</p>
        </div>
      </div>
      <div class="module-content" style="height: calc(100% - 48px)">
        <pipelineSelect :visible="isPipeline" :roomData="pipelineData" />
      </div>
    </div>
    <template v-if="allTableComponentListShow">
      <allTableComponentList ref="allTableComponentList" :dialogData="tableCompenentData" :dialogShow="allTableComponentListShow" :assetsList="assetsList" @configCloseDialog="configCloseTableDialog"></allTableComponentList>
    </template>
  </div>
</template>

<script lang="jsx">
import { monitorTypeList } from '@/assets/common/dict.js'
import { getMonitorDeviceCount, getGroupByEntityType, getGasList } from '@/utils/spaceManage'
import allTableComponentList from '../components/allTableComponentList.vue'
import icon_6 from '@/assets/images/icon-6.png'
import icon_5 from '@/assets/images/icon-5.png'
import icon_2 from '@/assets/images/icon-2.png'
import * as echarts from 'echarts'
import pipelineSelect from './components/pipelineSelect.vue'
import tableRender from '../components/tableRender.vue'
export default {
  name: 'airComponent',
  components: {
    tableRender,
    pipelineSelect,
    allTableComponentList
  },
  props: {
    roomData: {
      type: Object,
      default: () => {}
    },
    assetsList: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      icon_2,
      icon_5,
      icon_6,
      sendCheckData: {}, // 给checkbox组件传递的数据
      pipelineData: {}, // 给管线组件传递的数据
      allTableComponentListShow: false,
      deviceTableLoading: false,
      isPipeline: true,
      deviceInfo: {},
      deviceAccountStatistics: {}, // 设备台账统计
      tableCompenentData: {}, // 一站式弹窗数据
      deviceList: [], // 设备列表
      showCheckGroup: false,
      checkList: ['全部'],
      flowList: [
        {
          label: '全部',
          value: ''
        },
        {
          label: '正常',
          value: '1'
        },
        {
          label: '异常',
          value: '2'
        },
        {
          label: '离线',
          value: '6'
        }
      ],
      statisticsData: [
        {
          name: '设备总数',
          key: 'deviceTotal',
          unit: '台'
        },
        // {
        //   name: '监测数量',
        //   key: 'deviceMonitorTotal',
        //   unit: '台'
        // },
        {
          name: '离线设备数',
          key: 'offDeviceTotal',
          unit: '台'
        },
        {
          name: '异常设备',
          key: 'abnormalDeviceTotal',
          unit: '条'
        }
      ], // 统计数据
      tableColumn: [],
      deviceTableColumn: [
        {
          prop: 'surveyName',
          label: '设备名称',
          minWidth: 130
        },
        {
          prop: 'menuName',
          label: '归属系统'
        },
        {
          prop: 'status',
          label: '运行状态',
          render: (h, row) => {
            return (
              <div>
                {row.row.status == '0' && (
                  <div class="table-icon">
                    <img src={icon_5} />
                    <span style="color:#61E29D">正常</span>
                  </div>
                )}
                {row.row.status == '6' && (
                  <div class="table-icon">
                    <img src={icon_6} />
                    <span style="color:#86909C">离线</span>
                  </div>
                )}
                {row.row.status == '10' && (
                  <div class="table-icon">
                    <img src={icon_2} />
                    <span style="color:#FF2D55">异常</span>
                  </div>
                )}
              </div>
            )
          }
        }
      ],
      pagination: {
        total: 0,
        pageSize: 15,
        pageNo: 1
      },
      chartData: [],
      ssmCodes: [],
      checkedValue: '',
      isSelectedTypeData: {
        name: '',
        entityTypeId: ''
      }
    }
  },
  watch: {
    // 监听模型层级数据变化
    roomData: {
      handler(val) {
        // 不根据房间过滤数据
        if (val.ssmType >= 6) return
        this.initData()
      },
      deep: true
    }
  },
  mounted() {
    this.tableColumn = this.deviceTableColumn
    // 初始化调用
    this.initData()
  },
  methods: {
    initData() {
      this.ssmCodes = this.roomData.ssmCodes?.split(',') || []
      // 深圳肿瘤医院对应管线
      if (__PATH.VUE_APP_HOSPITAL_NODE === 'szzlyy') {
        this.pipelineData = {
          ...this.roomData,
          projectCode: monitorTypeList.find((e) => e.projectName === '空调监测').projectCode
        }
      }
      this.getEntityType()
      this.getAssetsCount()
    },
    // 根据设备类型获取实体
    getEntityType() {
      const parmas = {
        projectCode: monitorTypeList.find((e) => e.projectName === '空调监测').projectCode,
        spaceId: this.ssmCodes.at(-1)
      }
      getGroupByEntityType(parmas).then((res) => {
        if (res.data.code === '200') {
          this.chartData = res.data.data
          this.getRenderer(res.data.data)
          this.pagination.pageNo = 1
          this.getAssetsTypeList()
        }
      })
    },
    // 初始化设备数量
    getAssetsCount() {
      const parmas = {
        projectCode: monitorTypeList.find((e) => e.projectName === '空调监测').projectCode,
        spaceId: this.ssmCodes.at(-1)
      }
      getMonitorDeviceCount(parmas).then((res) => {
        if (res.data.code === '200') {
          this.deviceAccountStatistics = res.data.data
        }
      })
    },
    // 初始化统计表
    getRenderer(data) {
      // 基于准备好的dom，初始化echarts实例
      const EChart = echarts.init(document.getElementById('EChart'))
      const barWidth = 14 /* 进度条宽度 */
      const normalData = [] /* 正常 */
      const abnormalData = [] /* 异常 */
      const offlineData = [] /* 离线 */
      const attaVal = [] /* 进度条数值 */
      const topName = [] /* 进度条名称 */
      data.forEach((item, i) => {
        topName[i] = {
          value: item.entityTypeName,
          textStyle: {
            color: '#FFF'
          }
        }
        attaVal[i] = item.allCount
        normalData[i] = item.normalCount
        abnormalData[i] = item.abnormalCount
        offlineData[i] = item.offLineCount
      })
      console.log(normalData)
      // 配置参数
      const config = {
        background: '#ffff',
        tooltip: {
          show: false,
          textStyle: {
            fontSize: 16
          }
        },
        grid: {
          left: '2%',
          right: '10%',
          top: '13%',
          bottom: '0%',
          containLabel: true
        },
        legend: {
          show: true,
          top: '3%',
          itemHeight: 8,
          itemWidth: 8,
          itemGap: 20,
          textStyle: {
            fontSize: 12,
            color: '#fff'
          },
          selectedMode: false
        },
        xAxis: {
          show: false,
          type: 'value'
        },
        yAxis: [
          {
            type: 'category',
            // inverse: true,
            triggerEvent: true,
            axisTick: {
              show: false
            },
            axisLine: {
              show: false
            },
            axisLabel: {
              textStyle: {
                color: '#fff',
                fontSize: 14
              }
            },
            data: topName
          }
        ],
        series: [
          {
            name: '正常',
            stack: 'total',
            type: 'bar',
            barWidth: barWidth,
            data: normalData,
            itemStyle: {
              normal: {
                color: ' rgba(101,234,162,0.6)',
                borderColor: '#2C344C',
                borderWidth: 1
              }
            }

          },
          {
            name: '异常',
            type: 'bar',
            barWidth: barWidth,
            stack: 'total',
            data: abnormalData,
            itemStyle: {
              normal: {
                color: 'rgba(255,45,85,0.6)',
                borderColor: '#2C344C',
                borderWidth: 1
              }
            }

          },
          {
            name: '离线',
            type: 'bar',
            barWidth: barWidth,
            stack: 'total',
            data: offlineData,
            itemStyle: {
              normal: {
                color: 'rgba(212,222,236,0.6)',
                borderColor: '#2C344C',
                borderWidth: 1
              }
            }

          },
          // total
          {
            type: 'bar',
            zlevel: 1,
            barWidth: barWidth,
            barGap: '-100%',
            label: {
              show: true,
              position: 'right',
              distance: 8,
              textStyle: { color: '#df9985', fontSize: 14 }
            },
            itemStyle: {
              color: 'transparent'
            },
            data: attaVal
          }
        ],
        dataZoom: [
          {
            yAxisIndex: [0],
            orient: 'vertical',
            show: true,
            type: 'slider',
            startValue: 100,
            // end: 100,
            width: 8,
            left: '99%',
            borderColor: 'rgba(43,48,67,.1)',
            fillerColor: '#6580b8',
            zoomLock: true,
            showDataShadow: false,
            backgroundColor: 'rgba(43,48,67,.1)',
            showDetail: false,
            // realtime: true,
            filterMode: 'filter',
            handleIcon: 'M-9.35,34.56V42m0-40V9.5m-2,0h4a2,2,0,0,1,2,2v21a2,2,0,0,1-2,2h-4a2,2,0,0,1-2-2v-21A2,2,0,0,1-11.35,9.5Z',
            handleStyle: {
              color: '#6580b8',
              borderColor: '#6580b8'
            },
            maxValueSpan: 4,
            minValueSpan: 4,
            brushSelect: false
          },
          {
            type: 'inside',
            show: false,
            yAxisIndex: [0],
            height: 8
          }
        ]
      }
      // 设置参数
      EChart.setOption(config)
      // 先移除点击事件 解决点击事件重复绑定
      EChart.off('click')
      // 点击事件
      EChart.on('click', (params) => {
        console.log(params)
        const index = params.dataIndex
        // 新增变量 判断当前点击柱状图是选中还是取消选中
        let isSelected = false
        topName.map((e, i) => {
          // 选中的设置选中色
          if (i == index && e.textStyle.color != '#FFCA64FF') {
            e.textStyle.color = '#FFCA64FF'
          } else {
            // 选中已选中的则为取消选中
            if (i == index && e.textStyle.color == '#FFCA64FF') {
              isSelected = true
            }
            // 其他的设为默认色
            e.textStyle.color = '#FFF'
          }
        })
        config.yAxis.data = JSON.parse(JSON.stringify(topName))
        EChart.setOption(config)
        this.pagination.pageNo = 1
        // 取消选中 则恢复过滤条件
        let paramsId = ''
        this.isSelectedTypeData = {
          name: '',
          entityTypeId: ''
        }
        if (!isSelected) {
          let name = ''
          if (params.componentType === 'series') {
            name = `-${params.name}`
          } else {
            name = `-${params.value}`
          }
          paramsId = this.chartData[index].entityTypeId || ''
          this.isSelectedTypeData = {
            name: name,
            entityTypeId: paramsId
          }
        }
        // 设备类型
        this.getAssetsTypeList({ entityTypeId: paramsId })
        this.$emit('sendWpfData', { entityTypeId: paramsId })
      })
    },
    // 根据设备类型查询列表
    getAssetsTypeList(obj) {
      const params = {
        spaceId: this.ssmCodes.at(-1),
        page: this.pagination.pageNo,
        pageSize: this.pagination.pageSize,
        projectCode: monitorTypeList.find((e) => e.projectName === '空调监测').projectCode,
        deviceStatus: this.checkedValue || ''
      }
      Object.assign(params, obj)
      getGasList(params).then((res) => {
        if (res.data.code === '200') {
          this.deviceTableLoading = false
          if (this.pagination.pageNo === 1) {
            this.deviceList = []
          }
          this.deviceList = this.deviceList.concat(res.data.data.list)
          this.pagination.total = res.data.data.count
        } else {
          this.deviceTableLoading = false
        }
      })
    },
    tableRowClick(row) {
      // // 如果关联了设备即跳转设备详情页
      // if (row.modelCode) {
      //   const device = this.assetsList.find((item) => item.modelCode === row.modelCode)
      //   const params = {
      //     DeviceCode: device?.modelCode,
      //     DeviceName: device?.assetName,
      //     projectCode: monitorTypeList.find((e) => e.projectName === '空调监测').projectCode
      //   }
      //   try {
      //     window.chrome.webview.hostObjects.sync.bridge.EquipmentRealView(JSON.stringify(params))
      //   } catch (error) {}
      // } else {
      //   // this.$message.warning('该设备未关联资产!')
      // }
    },
    // 列表双击进入详情
    tabledblClick(row) {
      // 如果关联了设备即跳转设备详情页
      if (row.modelCode) {
        const device = this.assetsList.find((item) => item.modelCode === row.modelCode)
        this.$emit('roomEvent', {
          type: 'move',
          assetId: device.assetId,
          assetName: device.assetName,
          modelCode: device.modelCode,
          entityTypeId: row.entityTypeId
        })
        const params = {
          DeviceCode: device?.modelCode,
          DeviceName: device?.assetName,
          projectCode: monitorTypeList.find((e) => e.projectName === '空调监测').projectCode
        }
        try {
          window.chrome.webview.hostObjects.sync.bridge.EquipmentRealView(JSON.stringify(params))
        } catch (error) {}
      } else {
        // this.$message.warning('该设备未关联资产!')
      }
    },
    tableLoadMore() {
      if (this.pagination.total > this.pagination.pageNo * this.pagination.pageSize) {
        this.pagination.pageNo++
        this.getAssetsTypeList({ entityTypeId: this.isSelectedTypeData.entityTypeId })
      }
    },
    checkBoxChanged() {
      this.checkList = [this.checkList[this.checkList.length - 1]]
      this.showCheckGroup = false
      const codeList = []
      this.flowList.forEach((item) => {
        if (this.checkList[0] == item.label) {
          codeList.push(item.value)
        }
      })
      this.checkedValue = codeList[0]
      this.pagination.pageNo = 1
      this.getAssetsTypeList()
    },
    allTableChange(type) {
      const params = {
        spaceId: this.ssmCodes.at(-1),
        entityTypeId: this.isSelectedTypeData.entityTypeId
      }
      const deviceIdArr = Array.from(
        this.assetsList,
        ({ assetId }) => assetId
      )
      const deviceIds = deviceIdArr.length ? deviceIdArr.toString() : ''
      Object.assign(this.tableCompenentData, {
        title: '设备台账列表',
        ...params,
        isSpace: '0',
        projectCode: monitorTypeList.find((e) => e.projectName === '空调监测').projectCode,
        type: type,
        deviceId: deviceIds,
        height: 'calc(100% - 120px)'
      })
      this.allTableComponentListShow = true
      // 如果无数据则填充初始空间过滤数据，有数据则按照当前数据过滤
      // let params = {
      //   entityTypeId: '',
      //   spaceId: this.ssmCodes.at(-1),
      //   deviceId: this.roomData.deviceId
      // }
    },
    // 关闭弹窗
    configCloseTableDialog() {
      this.allTableComponentListShow = false
      try {
        window.chrome.webview.hostObjects.sync.bridge.ShowDialog(false)
      } catch (error) {}
    }
  }
}
</script>

<style lang="scss" scoped>
@import '../style/module.scss';
.statistics_top_new {
  height: 130px;
  display: flex;
  flex-wrap: wrap;
  .statistics_top_item {
    height: 50%;
    width: 50%;
    // padding: 16px 0;
    display: flex;
    justify-content: center;
    align-items: center;
    img {
      width: 50px;
      height: 50px;
    }
  }
  .statistics_top_content {
    .statistics_top_title {
      padding-left: 20px;
      font-size: 14px;
      font-family: HarmonyOS Sans SC-Regular, HarmonyOS Sans SC;
      font-weight: 400;
      color: #b0e3fa;
      margin-bottom: 6px;
    }
    .font_bg {
      width: 125px;
      height: 32px;
      background: url(./../../../assets/images/qhdsys/Group-new.png) no-repeat 100%;
      display: flex;
      align-items: center;
      justify-content: center;
      .color_font {
        font-size: 20px;
        font-family: HarmonyOS Sans SC-Bold, HarmonyOS Sans SC;
        font-weight: bold;
        color: #ffca64;
        margin-bottom: 3px;
      }
      .color_unit {
        margin-left: 3px;
        font-size: 14px;
        font-family: HarmonyOS Sans SC-Light, HarmonyOS Sans SC;
        font-weight: 300;
        color: rgba(255, 255, 255, 0.5);
      }
    }
  }
}
.moveComponent {
  width: 100%;
  height: 100%;
  .device-list {
    overflow: auto;
    .device-list-item {
      padding-top: 16px;
      .item-title {
        background: rgba(133, 145, 206, 0.15);
        font-size: 15px;
        font-weight: 500;
        color: #ffffff;
        line-height: 18px;
        padding: 7px 8px;
      }
      .item-status {
        display: flex;
        .status-item {
          width: calc(100% / 3);
          text-align: center;
          padding: 15px;
        }
        .status-item-name {
          font-size: 14px;
          font-weight: 400;
          color: #ffffff;
          line-height: 16px;
        }
        .status-item-value {
          font-size: 16px;
          font-weight: bold;
          color: #ffca64;
          line-height: 19px;
          margin-top: 6px;
        }
        .status-item:last-child {
          .status-item-value {
            color: #ff2d55;
          }
        }
      }
    }
  }
  .table-icon {
    display: flex;
    align-items: center;
  }
  .table-icon img {
    width: 16px;
    margin-right: 3px;
  }
  .title-left {
    padding-left: 20px;
    .showFloorName {
      width: 100%;
      text-overflow: ellipsis;
      white-space: nowrap;
      overflow: hidden;
    }
  }
  .title-detail {
    cursor: pointer;
    font-size: 14px;
    font-family: HarmonyOS Sans SC-Regular, HarmonyOS Sans SC;
    font-weight: 400;
    color: #8bddf5 !important;
    margin-right: 13px;
  }
  .toggle {
    display: flex;
    cursor: pointer;
    font-size: 14px;
  }
  .toggle > div {
    padding: 4px 10px;
    color: #8bddf5 !important;
    text-align: center;
    background-color: #213251;
    box-sizing: border-box;
    border: 1px solid #213251;
    opacity: 1;
  }
  .active-type {
    background: rgba(255, 255, 255, 0.1) linear-gradient(90deg, rgba(10, 132, 255, 0.4) 0%, rgba(10, 132, 255, 0) 100%);
    border: 1px solid #abf0ff !important;
  }
  .icon-box {
    margin-right: 0;
    display: flex;
    align-items: center;
    position: relative;
  }
}
::v-deep .el-table {
  .el-table__body tr {
    cursor: pointer;
  }
}
// 滚动条的宽度
::v-deep .el-table__body-wrapper::-webkit-scrollbar {
  width: 6px; // 横向滚动条
  height: 6px; // 纵向滚动条 必写
}

// 注意:hover
::v-deep .el-table__body-wrapper:hover::-webkit-scrollbar {
  width: 6px; // 横向滚动条
  height: 6px; // 纵向滚动条 必写
}
::v-deep .el-table__body-wrapper::-webkit-scrollbar-corner,
::v-deep .el-table__body-wrapper::-webkit-scrollbar-track {
  background-color: transparent;
}
.module-header {
  width: 100%;
  // padding-left: 50px;
  background: url('~@/assets/images/qhdsys/bg-bt.png') no-repeat;
}

::v-deep .el-checkbox__input.is-checked span.el-checkbox__inner {
  background: url('@/assets/images/<EMAIL>') !important;
  background-size: 100% 100% !important;
}
::v-deep span.el-checkbox__inner {
  border-color: #c5fff9 !important;
}
::v-deep .el-checkbox__input.is-checked + .el-checkbox__label {
  color: #8bddf5;
}
.order-more {
  width: 24px;
  height: 24px;
  margin-right: 0 !important;
  margin-left: 8px;
}
.filter {
  width: 24px;
  height: 24px;
}
::v-deep .panel-s {
  position: absolute;
  right: 0;
  top: 28px;
  background-color: #374b79;
  padding: 8px;
  z-index: 9;
  height: 120px;
  overflow: auto;
  .el-checkbox-group {
    display: flex;
    flex-direction: column;
    align-items: flex-end;
    margin-right: 0 !important;
    .el-checkbox {
      width: 100%;
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-right: 0;
      margin-bottom: 5px;
      .el-checkbox__label {
        color: #a3a9c0 !important;
      }
      .el-checkbox__input {
        // display: none;
      }
    }
  }
}
</style>
