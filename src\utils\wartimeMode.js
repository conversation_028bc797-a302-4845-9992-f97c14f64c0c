// 能耗
import http from './http'
const planApi = __PATH.VUE_APP_PLAN_API
const iemcApi = __PATH.VUE_APP_IEMC_API
const spaceApi = __PATH.VUE_APP_SPACE_API

// 获取预案列表
export function GetPlanList(params) {
  return http.postRequest(`${planApi}/preplanBasic/getConfigPage`, { ...params })
}
// 获取预案详情
export function GetPlanDetail(params) {
  return http.postRequest(`${planApi}/preplanBasic/getBasicConfigDetail`, { ...params })
}
// 获取报警系统
export function GetAlarmSystem(params) {
  return http.getRequest(`${iemcApi}/alarmConfig/getAlarmSystem`, { ...params })
}
// 根据所属单位回去部门列表
export function GetSelectedDept(params) {
  return http.getRequest(`${spaceApi}/departmentManager/department-manager/getSelectedDept`, { ...params })
}
// 获取报警处理记录
export function GetAlarmOperationRecord(params) {
  return http.postFormData(`${planApi}/clientOperationLog/getClientOperationLogByAlarmId`, { ...params })
}
// 查询当前预案执行到的步骤
export function GetPreplanStateByAlarmId(params) {
  return http.postFormData(`${planApi}/preplanBasic/getPreplanStateByAlarmId`, { ...params })
}
// 提交当前预案执行到的步骤
export function SavePreplanOperationState(params) {
  return http.postRequest(`${planApi}/preplanBasic/savePreplanOperationState`, { ...params })
}
// 提交确认事件记录
export function SaveConfirmRecord(params) {
  return http.postRequest(`${planApi}/preplanBasic/saveConfirmRecord`, { ...params })
}
// 获取确认事件记录
export function GetConfirmRecordList(params) {
  return http.postFormData(`${planApi}/preplanBasic/getConfirmRecordListByAlarmId`, { ...params })
}
