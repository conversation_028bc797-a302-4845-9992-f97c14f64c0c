<template>
  <div class="lawFlow">
    <div class="preview-img-div" ref="previewImgDiv" @mousewheel="handleMousewheel" @mousedown="handleMouseDown">
      <img
        :src="imgSrc"
        :style="{
          transform: `scale(${scale}) translate(${translateX}px, ${translateY}px)`
        }"
      />
    </div>
  </div>
</template>

<script>
import { GetPlanDetail } from '@/utils/wartimeMode'
export default {
  name: 'lawFlow',
  props: {
    alarmData: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      imgSrc: '',
      scale: 1,
      translateX: 0,
      translateY: 0
    }
  },
  computed: {},
  created() {
    this.getPlanDetail()
  },
  methods: {
    // 获取预案详情
    getPlanDetail() {
      GetPlanDetail({ id: this.alarmData.planId }).then(res => {
        if (res.data.code == 200) {
          const imgList = JSON.parse(res.data.data.regulationsFlow)
          this.imgSrc = this.$tools.imgUrlTranslation(imgList.length ? imgList[0].url : '')
          console.log(this.imgSrc)
        }
      })
    },
    handleMousewheel(event) {
      const scale = this.scale + event.wheelDelta / 3000
      if (scale < 3 && scale > 0.5) {
        this.scale = scale
      }
      return false
    },
    handleMouseDown(event) {
      event.preventDefault()
      if (event.target.tagName !== 'IMG') {
        return false
      }
      const div = this.$refs.previewImgDiv
      const originX = event.screenX
      const originY = event.screenY
      const translateX = this.translateX
      const translateY = this.translateY
      const move = (e) => {
        const afterX = e.screenX
        const afterY = e.screenY
        this.translateX = translateX + (afterX - originX) / this.scale
        this.translateY = translateY + (afterY - originY) / this.scale
      }
      div.addEventListener('mousemove', move)
      div.addEventListener('mouseup', () => {
        div.removeEventListener('mousemove', move)
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.lawFlow {
  width: 100%;
  height: 100%;
  .preview-img-div {
    overflow: hidden;
    position: relative;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    img {
      width: 50%;
    }
  }
}
</style>
