<!--
 * @Author: hedd
 * @Date: 2023-07-04 18:38:33
 * @LastEditTime: 2023-07-06 15:30:43
 * @FilePath: \ihcrs_client_iframe_elevator\src\views\elevator\cameraTalkDialog.vue
 * @Description:
-->
<template>
  <!-- <div class="camera-content"> -->
  <el-dialog custom-class="phoneTalk-loader" :visible.sync="visible" :close-on-click-modal="false" :close-on-press-escape="false" @before-close="camerTalkGoback">
    <cameraTalkContent :rowData="rowData" flexGrid="row" @goback="camerTalkGoback" />
  </el-dialog>
  <!-- </div> -->
</template>

<script>
import cameraTalkContent from './components/cameraTalkContent.vue'
export default {
  name: 'cameraTalkDialog',
  components: {
    cameraTalkContent
  },
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    rowData: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {}
  },
  mounted() {},
  methods: {
    camerTalkGoback() {
      this.$emit('update:visible', !this.visible)
    }
  }
}
</script>

<style lang="scss" scoped>
// .camera-content {
//   width: 100%;
//   height: 100%;
::v-deep .phoneTalk-loader {
  width: 60%;
  height: 50%;
  background: url('~@/assets/images/elevator/page-bg.png') no-repeat center;
  background-size: 100% 100%;
  box-shadow: none;
  .el-dialog__body {
    height: 100%;
    width: 100%;
  }
  .el-dialog__header,
  .el-dialog__footer {
    display: none;
  }
  .el-dialog__body {
    padding: 0;
  }
}
// }
</style>
