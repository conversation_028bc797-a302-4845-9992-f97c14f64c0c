<template>
  <div style="height: 100%">
    <div class="search-box">
      <div style="margin-right: 10px" class="deviceInput">
        <!-- <span>设备名称：</span> -->
        <el-input
          v-model="params.assetName"
          placeholder="请输入资产名称"
          clearable
          @clear="reset"
        ></el-input>
      </div>
      <div class="search-btn">
        <el-button @click="reset">重置</el-button>
        <el-button @click="handleSearch">查询</el-button>
      </div>
    </div>
    <div class="tableContent">
      <el-table
        v-loading="tableLoading"
        :data="tableData"
        height="100%"
        :cell-style="$tools.setCell(3)"
        :header-cell-style="$tools.setHeaderCell(3)"
        style="width: 100%"
        element-loading-background="rgba(0, 0, 0, 0.2)"
        @row-click="tableRowClick"
      >
        <el-table-column
          prop="assetName"
          label="关联资产"
          show-overflow-tooltip
        >
        </el-table-column>
        <el-table-column
          prop="assetCategoryName"
          label="资产类型"
          show-overflow-tooltip
        >
        </el-table-column>
        <el-table-column
          prop="assetTypeName"
          label="设备类型"
          show-overflow-tooltip
        >
        </el-table-column>
        <el-table-column
          prop="surveyName"
          label="设备名称"
          show-overflow-tooltip
        >
        </el-table-column>
        <el-table-column
          prop="regionName"
          label="设备位置"
          show-overflow-tooltip
        >
        </el-table-column>
        <el-table-column prop="menuName" label="归属系统" show-overflow-tooltip>
        </el-table-column>
        <el-table-column prop="status" label="运行状态" show-overflow-tooltip>
          <template slot-scope="scope">
            <div class="status-box" >
              <template  v-if="scope.row.status == '0'">
                <img class="table-icon" :src="icon_5" />
                <span style="color:#61E29D">正常</span>
              </template>
              <template v-if="scope.row.status == '6'">
                <img class="table-icon" :src="icon_6" />
                <span style="color:#86909C">离线</span>
              </template>
              <template v-if="scope.row.status == '10'">
                <img class="table-icon" :src="icon_2" />
                <span style="color:#FF2D55">异常</span>
              </template>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="操作">
          <template slot-scope="scope">
            <span class="operationBtn" @click.stop="goDetail(scope.row)">详情</span>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <el-pagination
      :current-page="currentPage"
      :page-sizes="[15, 20, 30, 40]"
      :page-size="pageSize"
      layout="total, sizes, prev, pager, next, jumper"
      :total="total"
      class="pagination"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
    ></el-pagination>
    <DeviceDetails
      v-if="isDeviceDetails"
      :dialogShow="isDeviceDetails"
      :deviceData="deviceData"
      @deviceDetailsClose="() => (isDeviceDetails = false)"
    />
  </div>
</template>
<script>
import { getGasList } from '@/utils/spaceManage'
import DeviceDetails from '@/views/spaceManage/components/deviceDetails.vue'
import { monitorTypeList, hasModelCodeFilterProjectName } from '@/assets/common/dict.js'
import icon_5 from '@/assets/images/icon-5.png'
import icon_6 from '@/assets/images/icon-6.png'
import icon_2 from '@/assets/images/icon-2.png'
export default {
  components: {
    DeviceDetails
  },
  props: {
    globalParams: {
      type: Object,
      default: () => {
        return {
          areaCode: '',
          assetName: '',
          list: [],
          total: 0
        }
      }
    }
  },
  data() {
    return {
      icon_5,
      icon_6,
      icon_2,
      tableData: [],
      tableLoading: false,
      currentPage: 1,
      pageSize: 15,
      total: 0,
      params: {
        assetName: ''
      },
      // 设备详情
      isDeviceDetails: false,
      deviceData: {}
    }
  },
  mounted() {
    this.total = this.globalParams.total
    this.tableData = this.globalParams.list
    this.params.assetName = this.globalParams.searchContent
  },
  methods: {
    getList() {
      let params = {
        pageSize: this.pageSize,
        page: this.currentPage,
        assetName: this.params.assetName
      }
      this.tableLoading = true
      getGasList(params).then((res) => {
        if (res.data.code === '200') {
          this.tableData = res.data.data.list
          this.total = res.data.data.count

        }
      }).finally(() => {
        this.tableLoading = false
      })
    },
    reset () {
      this.params.assetName = ''
      this.currentPage = 1
      this.getList()
    },
    handleSearch () {
      this.currentPage = 1
      this.getList()
    },
    goDetail (row) {
      this.deviceData = row
      this.isDeviceDetails = true
      try {
        window.chrome.webview.hostObjects.sync.bridge.ShowDialog(true)
      } catch (error) {}
    },

    handleSizeChange (val) {
      this.pageSize = val
      this.currentPage = 1
      this.getList()
    },
    handleCurrentChange (val) {
      this.currentPage = val
      this.getList()
    },
    // 列表单击，如果是设备，需要定位设备模型位置,
    tableRowClick (row) {
      let params = {}
      // 受filterName包含的为使用modelcode跳转（关联模型），不是的为使用assetsid跳转（关联表计）
      const filterName = hasModelCodeFilterProjectName
      const filterMonitorList = monitorTypeList.filter(e => filterName.includes(e.projectName)).map(v => v.projectCode)
      // 如果关联了设备即跳转设备详情页
      if ([0].includes(this.globalParams.searchType)) {
        params = {
          DeviceCode: row.modelCode,
          DeviceName: row.assetName,
          assetsId: row.assetId || row.assetsId,
          projectCode: row?.projectCode,
          spaceCode: row?.regionCode
        }
        if (filterMonitorList.includes(params.projectCode)) {
          if (params.assetsId) {
            if (params.DeviceCode) { // 设备
              try {
                window.chrome.webview.hostObjects.sync.bridge.EquipmentRealView(JSON.stringify(params))
                this.closeDialog()
              } catch (error) {}
            } else {
              this.$message.warning('当前设备暂未录入模型编码!')
            }
          } else {
            this.$message.warning('当前设备暂未关联资产!')
          }
        } else {
          if (params.assetsId) { // 点位
            try {
              this.closeDialog()
              window.chrome.webview.hostObjects.sync.bridge.EquipmentRealView(JSON.stringify(params))
            } catch (error) {}
          } else {
            this.$message.warning('当前设备暂未关联资产!')
          }
        }
      }
    },
    closeDialog () {
      this.$emit('close')
    }
  }
}
</script>
<style lang="scss" scoped>
.tableContent {
  height: calc(100% - 130px);
}
:deep(.search-box) {
  display: flex;
  flex-wrap: wrap;
  margin-bottom: 12px;

  .deviceInput {
    width: 250px;
    .el-input {
      width: 100%;
      height: 35px;
    }
    .el-cascader {
      line-height: 35px;
    }
  }
  .el-button {
    background-image: url("@/assets/images/btn.png");
    background-repeat: no-repeat;
    background-size: 100% 100%;
    height: 35px;
  }
  .search-btn {
    margin-left: auto;
  }

  .el-input .el-input__inner {
    border-color: rgba(133, 145, 206, 0.5);
    border-radius: 4px;
    color: rgba(255, 255, 255, 0.8);
  }
}
.status-box {
  display: flex;
  align-items: center;
  .table-icon {
  width: 16px;
  margin-right: 3px;
}
}
</style>
