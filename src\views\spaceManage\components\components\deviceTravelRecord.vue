<template>
  <div class="deviceTravelRecord">
    <div class="search-box">
      <el-date-picker
        v-model="dateRange"
        type="daterange"
        unlink-panels
        popper-class="date-style"
        range-separator="至"
        start-placeholder="开始日期"
        end-placeholder="结束日期"
        value-format="yyyy-MM-dd"
        @change="changeDataEvent"
      >
      </el-date-picker>
    </div>
    <div style="flex: 1; overflow: hidden;">
      <el-table
        v-loading="tableLoading"
        :data="tableData"
        height="calc(100%)"
        :cell-style="$tools.setCell(3)"
        :header-cell-style="$tools.setHeaderCell(3)"
        style="width: 100%"
        element-loading-background="rgba(0, 0, 0, 0.2)"
      >
        <el-table-column prop="assetsName" label="设备名称" show-overflow-tooltip></el-table-column>
        <el-table-column prop="peopleSum" label="通行人数"></el-table-column>
        <el-table-column prop="dateTime" label="通行时间"></el-table-column>
      </el-table>
    </div>
    <el-pagination
      :current-page="currentPage"
      :page-sizes="[15, 20, 30, 40]"
      :page-size="pageSize"
      layout="total, sizes, prev, pager, next, jumper"
      :total="total"
      class="pagination"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
    ></el-pagination>
  </div>
</template>

<script>
import { GetDeviceTravelRecord } from '@/utils/spaceManage'
import moment from 'moment'
moment.locale('zh-cn')
export default {
  name: 'deviceTravelRecord',
  props: {
    deviceId: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      tableLoading: false,
      tableData: [],
      dateRange: [moment().format('YYYY-MM-DD'), moment().format('YYYY-MM-DD')],
      currentPage: 1,
      pageSize: 15,
      total: 0
    }
  },
  watch: {
  },
  created() {
    this.getDeviceTravelRecord()
  },
  methods: {
    changeDataEvent() {
      this.getDeviceTravelRecord()
    },
    getDeviceTravelRecord() {
      const param = {
        monitorDeviceId: this.deviceId,
        sTime: this.dateRange[0] || '',
        eTime: this.dateRange[1] || '',
        pageSize: this.pageSize,
        pageNo: this.currentPage
      }
      this.tableLoading = true
      GetDeviceTravelRecord(param).then(res => {
        this.tableLoading = false
        console.log(res.data)
        if (res.data.code == 200) {
          this.tableData = res.data.result.records
          this.total = res.data.result.total
        }
      })
    },
    handleSizeChange(val) {
      this.pageSize = val
      this.getDeviceTravelRecord()
    },
    handleCurrentChange(val) {
      this.currentPage = val
      this.getDeviceTravelRecord()
    }
  }
}

</script>

<style lang="scss" scoped>
.deviceTravelRecord {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  ::v-deep(.search-box) {
    padding: 8px 0px 24px 0px;
    display: flex;
    align-items: center;
    .el-select {
      margin-right: 10px;
    }
    .el-input {
      margin-right: 10px;
      width: 200px;
      .el-input__inner {
        height: 35px;
        border: 1px solid rgba(133, 145, 206, 0.5);
        border-radius: 4px;
      }
      .el-input__icon {
        line-height: 35px;
      }
    }
  }
  ::v-deep(.el-date-editor) {
    width: 300px;
    height: 35px;
    background-color: transparent;
    border-color: rgba(133, 145, 206, 0.5);
    .el-input__icon {
      transform: translateY(-2px);
    }
    .el-range-input {
      background-color: transparent;
      color: rgba(255, 255, 255, 0.8);
    }
    .el-range-separator {
      color: rgba(255, 255, 255, 0.8);
      transform: translateY(-2px);
    }
  }

}
</style>
