<template>
  <el-dialog
    v-if="visible"
    v-dialogDrag
    :modal="false"
    :close-on-click-modal="false"
    :show-close="false"
    title="备注"
    width="30%"
    custom-class="remarkDialog"
    :visible.sync="visible"
    :before-close="closeDialog"
  >
  <div class="dialog-right">
    <div class="dialog-tc" @click="closeDialog"></div>
  </div>
  <div class="content" style="padding: 10px;">
      <el-form ref="ruleForm" :model="form" :rules="rules">
        <el-form-item prop="remark">
          <el-input v-model.trim="form.remark" type="textarea" resize="none" :rows="4" placeholder="请输入备注" maxlength="100" show-word-limit class="ipt"> </el-input>
        </el-form-item>
      </el-form>
      <!-- v-if="" -->
      <div>
        <div v-for="(item, index) in remaksData" :key="index" class="txt">
          <div style="color: #fff;">{{ item.createdTime }}</div>
          <div>
            <span style="color: #7EAEF9;">备注人 :</span>
            <span style="padding-left: 10px;color: #fff;">{{ item.operationPersonName + '（' + item.operationPersonId + '）' }}</span>
          </div>
          <div>
            <span style="color: #7EAEF9;">备注 :</span>
            <span style="padding-left: 10px;color: #fff;">{{ item.remark || '-' }}</span>
          </div>
        </div>
      </div>
    </div>
    <span slot="footer" class="dialog-footer">
      <el-button class="sino-button-sure" @click="closeDialog">取 消</el-button>
      <el-button class="sino-button-sure" @click="confirm('ruleForm')">确 定</el-button>
    </span>
  </el-dialog>
</template>
<script>
import {
  selectAlarmOperationOrRemarkById,
  insertRemarkById
} from '@/utils/peaceLeftScreenApi'
export default {
  name: 'remarkDialog',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    item: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  data() {
    return {
      form: {
        remark: ''
      },
      rules: {
        remark: [{ required: true, message: '请输入备注', trigger: 'change' }]
      },
      remaksData: []
    }
  },
  created() {
    this.getRemaksList()
  },
  mounted() {},
  methods: {
    getRemaksList() {
      const params = {
        isRemark: '1',
        operationId: this.item.alarmId
      }
      selectAlarmOperationOrRemarkById(params).then((res) => {
        if (res.data.code === '200') {
          this.remaksData = res.data.data
        }
      })
    },

    // 关闭弹窗
    closeDialog() {
      this.form.remark = ''
      this.$emit('update:visible', !this.visible)
    },
    confirm(formName) {
      const params = {
        alarmId: this.item.alarmId,
        remark: this.form.remark
      }
      this.$refs[formName].validate((valid) => {
        if (valid) {
          insertRemarkById(params).then((res) => {
            if (res.data.code === '200') {
              this.$message({
                message: '添加备注成功',
                type: 'success'
              })
              this.form.remark = ''
              this.$emit('update:visible', !this.visible)
            } else {
              this.$message.error(res.data.msg)
              this.form.remark = ''
              this.$emit('update:visible', !this.visible)
            }
          })
        } else {
        }
      })
    }
  }
}
</script>
<style lang="scss" scoped>
::v-deep .el-dialog{
  background: url('@/assets/images/qhdsys/576×326.png') no-repeat center center / 100% 100%;
  .el-dialog__header {
    padding: 10px 20px 10px;
    text-align: center;
    .el-dialog__title {
      height: 20px;
      font-size: 18px;
      font-family: Source Han Sans CN-Regular, Source Han Sans CN;
      font-weight: 400;
      color: #cbdeed;
      line-height: 18px;
    }
  }
  .el-dialog__body {
    height: calc(100% - 40px);
  }
  .el-dialog__footer {
    padding-top: 0;
    padding-bottom: 40px;
    padding-right: 40px;
  }
}
.remarkDialog {
  .dialog-right {
    position: absolute;
    top: 17px;
    right: 30px;
    display: flex;
    .dialog-tc {
      width: 36px;
      height: 36px;
      background: url('@/assets/images/qhdsys/bg-icon-gb.png') no-repeat;
    }
    .dialog-tc:hover {
      cursor: pointer;
    }
  }
  .content {
    width: 100%;
    max-height: 300px !important;
    overflow: auto;
    // background-color: #fff !important;
  }
  .ipt {
    margin: 10px auto 0;
    width: 100% !important;
  }
  ::v-deep .el-form-item__error {
    height: 20px;
    width: 300px;
    color: #f56c6c;
    font-size: 12px;
    line-height: 20px;
    padding-top: 4px;
    position: absolute;
    left: 0;
  }
  .txt {
    margin-bottom: 10px;
    width: 100%;
    padding: 5px 10px;
    background-color: #0D1E59;
    border-radius: 1px;
    div {
      line-height: 25px;
    }
  }
}
</style>
