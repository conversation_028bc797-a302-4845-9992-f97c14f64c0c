.sino-containers {
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    height: 100%;
    overflow: hidden;
    .top-content {
      display: flex;
      justify-content: space-between;
      height: 48%;
      .leftImg-box,
      .rightImg-box{
        width: 28%;
        margin-top: 10px;
        .leftImg {
          width: 100%;
          height: 100%;
          background-image: url('../../../../assets/images/demo/bg1.png');
          background-repeat: no-repeat;
          background-size: 100% 100%;
          display: flex;
          flex-direction: column;
          justify-content: center;
          position: relative;
          .title {
            font-size: 14px;
            font-family: TRENDS;
            color: #dceaff;
            position: absolute;
            top: 20px;
            left: 70px;
          }
          .el-carousel {
            padding-top: 30px;
            position: relative;
          }
          .el-carousel__item {
            height: 100%;
            background-color: transparent !important;
          }
          .el-carousel__item:nth-child(2n) {
            background-color: #99a9bf;
          }
          .el-carousel__item:nth-child(2n + 1) {
            background-color: #d3dce6;
          }
          .list {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 20px;
            .progress {
              height: 72px;
              width: 210px;
              background: url('~@/assets/images/peace/left-operation-border.png') no-repeat;
              background-size: 100% 100%;
              .p-box {
                width: 230px;
                margin-top: 20px;
                .progressBox {
                  border-radius: 30px;
                  background: rgba(0, 0, 0, 0.25);
                  box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.25), 0 1px rgba(255, 255, 255, 0.08);
                  width: 20%;
                  > span {
                    color: #dceaff;
                    font-size: 12px;
                    position: absolute;
                    padding-top: 10px;
                  }
                  .progress-moved {
                    height: 4px;
                    border-radius: 30px;
                    background-image: linear-gradient(to bottom, rgba(255, 255, 255, 0.3), rgba(255, 255, 255, 0.05));
                    transition: 0.4s linear;
                    transition-property: width, background-color;
                    width: 100%;
                    background-color: #01a797;
                    animation: progressAnimation 6s;
                  }
                }
                .progressBox1 {
                  border-radius: 30px;
                  background: rgba(0, 0, 0, 0.25);
                  box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.25), 0 1px rgba(255, 255, 255, 0.08);
                  width: 30%;
                  margin-left: 10px;
                  > span {
                    color: #dceaff;
                    font-size: 12px;
                    position: absolute;
                    padding-top: 10px;
                  }
                  .progress-moved1 {
                    height: 4px;
                    border-radius: 30px;
                    background-image: linear-gradient(to bottom, rgba(255, 255, 255, 0.3), rgba(255, 255, 255, 0.05));
                    transition: 0.4s linear;
                    transition-property: width, background-color;
                    width: 100%;
                    background-color: #01a797;
                    animation: progressAnimation 6s;
                  }
                }
              }
            }
            .upsStatusBox{
              height: 72px;
              width: 210px;
              background: #0c1b4d;
            }
            .upsStatus {
              height: 50%;
              display: flex;
              flex-direction: column;
              justify-content: space-between;
              padding-top:18px;
              .flex {
                display: flex;
                color: #dceaff;
                font-size: 12px;
                padding: 0 10px;
                > div:first-of-type {
                  margin-right: 20px;
                }
              }
            }
          }
          .left {
            display: flex;
            flex-direction: column;
            align-items: center;
            font-size: 12px;
            font-family: PingFangSC-Semibold, PingFang SC;
            font-weight: 600;
            color: #cfe1f7;
            > img {
              width: 70px;
              height: 70px;
              margin-bottom: 10px;
            }
          }
        }
        .rightImg{
            height: 100%;
            display: flex;
            flex-direction: column;
            justify-content: space-between;
            .top,.bottom{
                width: 100%;
                // height: 185px;
                background-image: url("../../../../assets/images/demo/bg1.png");
                background-repeat: no-repeat;
                background-size: 100% 100%;
                display: flex;
                flex-direction: column;
                justify-content: center;
                position: relative;
                .title{
                    font-size: 14px;
                    font-family: TRENDS;
                    color: #dceaff;
                    position: absolute;
                    padding-left:55px;
                    top: 10px;
                }
                .content{
                    height: 100px;
                    .box{
                        padding:0 30px;
                    }
                }
            }
            .top{
                height: 50%;
                .content{
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    .box{
                        width: 70px;
                        height: 80px;
                        // background-image: url("../../../../assets/images/demo/bg1.png");
                        // display: flex;
                        // flex-direction: column;
                        // align-items: center;
                        // color:#fff;
                        // font-size: 16px;;

                    }
                }
            }
            .bottom{
                height: 49%;
            }
        }
      }
      .middle-box {
        width:28%;
        text-align: center;
        flex: 1;
        .p-box {
          width: 200px;
          .progressBox {
            border-radius: 30px;
            background: rgba(0, 0, 0, 0.25);
            box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.25), 0 1px rgba(255, 255, 255, 0.08);
            width: 20%;
            .progress-moved {
              height: 8px;
              border-radius: 30px;
              background-image: linear-gradient(to bottom, rgba(255, 255, 255, 0.3), rgba(255, 255, 255, 0.05));
              transition: 0.4s linear;
              transition-property: width, background-color;
              width: 100%;
              background-color: #01a797;
              animation: progressAnimation 6s;
            }
          }
          .progressBox1 {
            border-radius: 30px;
            background: rgba(0, 0, 0, 0.25);
            box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.25), 0 1px rgba(255, 255, 255, 0.08);
            width: 30%;
            margin-left: 10px;
            .progress-moved1 {
              height: 8px;
              border-radius: 30px;
              background-image: linear-gradient(to bottom, rgba(255, 255, 255, 0.3), rgba(255, 255, 255, 0.05));
              transition: 0.4s linear;
              transition-property: width, background-color;
              width: 100%;
              background-color: #01a797;
              animation: progressAnimation 6s;
            }
          }
        }
        .cssBox {
          width: 280px;
          height: 400px;
          left: 0;
          right: 0;
          top: 0;
          bottom: 0;
          > img {
            width: 250px;
            height: 350px;
            border: 5px solid #ccc;
            border-radius: 5px;
            position: absolute;
            left: 0;
            top: 0;
            animation: rotate 15s linear infinite; //动画属性(循环匀速)
          }
        }
      }
      @keyframes rotate {
        from {
          transform: rotate(0);
        }
        to {
          transform: rotate(360deg);
        }
      }
  
      @keyframes progressAnimation {
        0% {
          width: 5%;
          background-color: #056770;
        }
        100% {
          width: 100%;
          background-color: #01a797;
        }
      }
    }
    .bot-content{
        height: 50%;
        display: flex;
        .bot-left-box{
           width:72%;
           height: 100%;
           background-image: url('../../../../assets/images/demo/bg1.png');
           background-repeat: no-repeat;
           background-size: 100% 100%;
        }
        .bot-right-box{
            width:28%;
            height: 100%;
            background-image: url('../../../../assets/images/demo/bg1.png');
           background-repeat: no-repeat;
           background-size: 100% 100%;
        }
    }
    ::v-deep .el-carousel {
      overflow: hidden;
    }
    .preBox {
      width: 30px;
      height: 30px;
      position: absolute;
      top: 50%;
      margin-top: -28px;
    }
    ::v-deep .el-carousel__indicators {
      display: none;
    }
    ::v-deep .el-carousel__arrow--left, ::v-deep .el-carousel__arrow--right {
      border-radius: 0;
      background: #587fa7;
      opacity: 0.24;
      height: 45px;
      z-index:99999;
    }
    ::v-deep .el-carousel__arrow--left {
      left: 5px;
    }
    ::v-deep .el-carousel__arrow--right {
      right: 5px;
    }
    ::v-deep .el-carousel__arrow i {
      font-size: 22px;
      color: linear-gradient(90deg, rgba(5, 19, 65, 0.9) 0%, rgba(5, 19, 65, 0.9) 67%, rgba(5, 19, 65, 0) 100%);
    }
  }