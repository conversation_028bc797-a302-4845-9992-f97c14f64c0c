<template>
  <div class="warnDisposal">
    <div class="warnDisposal-title">
      <div @click="$router.go(-1)"><i class="el-icon-arrow-left"></i>报警处置</div>
    </div>
    <div class="warnDisposal-main" style="margin-top: 22px">
      <div class="module-item">
        <div class="module-title">报警基本信息</div>
        <div class="module-content">
          <div class="content-item" v-for="(item, index) in option" :key="index" :style="{ width: `calc(100% / ${item.windtRatio})` }">
            <p class="item-label">{{ item.label }}</p>
            <el-input :value="taskDetail[item.value]" disabled></el-input>
          </div>
          <div class="content-item">
            <p class="item-label">联动摄像头</p>
            <div v-for="(item, index) in videoTabsList" :key="index" class="camera-box" @click="videoChange(item)"><span></span>{{item.vidiconName}}</div>
            <div v-if="!videoTabsList.length" class="camera-box">暂无关联摄像头</div>
          </div>
        </div>
      </div>
      <div class="warnDisposal-main" style="margin-top: 60px">
        <div class="module-item">
          <div class="module-title">报警处置</div>
          <div class="module-content">
            <div class="content-item" v-for="(item, index) in header.detailList" :key="index" :style="{ width: `calc(100% / ${item.windtRatio})` }">
              <p class="item-label">{{ item.label }}</p>
              <el-input :disabled="type === 'check'" v-model="formInline[item.value]"></el-input>
            </div>
            <div class="content-item" style="width: calc(100% / 3)">
              <p class="item-label">处置结果</p>
              <el-select v-model="formInline.disposeResult" :disabled="type === 'check'" placeholder="请选择处置结果" style="width: 100%">
                <el-option v-for="item in chuzhiList" :key="item.id" :label="item.name" :value="item.id"></el-option>
              </el-select>
            </div>
            <div class="content-item" style="width: 100%">
              <p class="item-label">处置结果说明</p>
              <el-input v-model="formInline.disposeText" :disabled="type === 'check'" placeholder="请输入处置结果说明"></el-input>
            </div>
            <div class="content-item" style="width: calc(100% / 3)">
              <p class="item-label">相关附件</p>
              <el-upload
                v-if="type != 'check'"
                ref="uploadFile"
                drag
                multiple
                class="mterial_file"
                action="string"
                :file-list="fileEcho"
                :http-request="httpRequest"
                accept=".jpg, .jpeg, .png, .JPG, .JPEG, .gif, .word, .WORD, .Excel, .pdf, .PDF, .doc, .docx, .DOCX, .xlsx, .xls, .zip, .zipx, .tar, .7z, .mp4, .mp3"
                :limit="9"
                :on-exceed="handleExceed"
                :beforeUpload="beforeAvatarUpload"
                :on-remove="handleRemove"
              >
                <i class="el-icon-upload"></i>
                <div class="el-upload__text">
                  将文件拖到此处，或
                  <em>点击上传</em>
                </div>
              </el-upload>
              <div v-else style="color: #5188fc; cursor: pointer">
                <div v-for="(item, i) of urlList" :key="i" @click="downLoad(item)" style="margin-top: 6px">{{ item }}</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="warnDisposal-footer">
      <el-button class="sino-button-sure" @click="$router.go(-1)">关闭</el-button>
      <el-button class="sino-button-sure" v-if="type !== 'check'" style="margin-left: 8px" @click="complete">保存</el-button>
    </div>
    <template v-if="cameraTalkDialogShow">
      <el-dialog custom-class="camera-loader" :visible.sync="cameraTalkDialogShow" :close-on-click-modal="false" :close-on-press-escape="false" @before-close="() => (cameraTalkDialogShow = false)">
        <div class="camera-talk-content">
          <div class="talk-header">
            <div class="left-title">{{ taskDetail.surveyName }}</div>
            <div class="right-icon" @click="() => cameraTalkDialogShow = false"></div>
          </div>
          <div class="talk-content">
            <rtspCavas ref="rtspCavas" :rtspUrl="videoUrl" :videoName="videoName" :hasCavas="Boolean(videoTabsList.length)" class="videoflv"></rtspCavas>
          </div>
        </div>
      </el-dialog>
    </template>
  </div>
</template>
<script>
import { disposePolice, getPoliceView, getVideoList, getHlvAddress } from '@/utils/elevatorApi'
export default {
  name: 'warnDisposal',
  components: {
    rtspCavas: () => import('@/views/elevatorOld/components/rtspCavas.vue')
  },
  data() {
    return {
      cameraTalkDialogShow: false,
      videoTabsList: [],
      videoUrl: '',
      videoName: '',
      type: '',
      option: [],
      taskDetail: {},
      electrical: false,
      header: {
        detailNode: [
          { label: '监测项实体名称', value: 'surveyName', windtRatio: 3 },
          { label: '报警参数名称', value: 'parameterName', windtRatio: 3 },
          { label: '报警原因', value: 'policeReason', windtRatio: 3 },
          { label: '报警数值', value: 'policeNumber', windtRatio: 3 },
          { label: '报警上限', value: 'upperThreshold', windtRatio: 3 },
          { label: '报警下限', value: 'downThreshold', windtRatio: 3 },
          { label: '报警时间', value: 'policeTime', windtRatio: 3 },
          { label: '报警位置', value: 'gridName', windtRatio: 3 }
        ],
        detailList: [
          { label: '处置人员', value: 'disposePersonName', windtRatio: 3 },
          { label: '处置人电话', value: 'disposePersonPhone', windtRatio: 3 }
        ]
      },
      formInline: {
        disposePersonName: '',
        disposePersonPhone: '',
        disposeResult: '',
        disposeText: '',
        file: []
      },
      chuzhiList: [
        { id: 1, name: '已确认' },
        { id: 2, name: '误报' }
      ],
      fileEcho: [],
      urlList: [],
      attachmentUrl: '' // 旧的附件
    }
  },
  created() {
    this.type = this.$route.query.type
    this.getDetails()
  },
  mounted() {},
  methods: {
    /**
     * 获取数据详情
     */
    getDetails() {
      this.option = this.header.detailNode
      //  接数据
      getPoliceView({
        id: this.$route.query.id
      }).then((res) => {
        this.taskDetail = res.data.data
        this.getVideoList()
        this.urlList = res.data.data.attachmentUrl ? res.data.data.attachmentUrl.split(',') : []
        if (this.$route.query.type === 'add') {
          if (res.data.data.disposeResult !== 0) {
            this.formInline.disposeResult = res.data.data.disposeResult
            this.formInline.disposeText = res.data.data.disposeText
            this.formInline.disposePersonName = res.data.data.personSet
            this.formInline.disposePersonPhone = res.data.data.personPhone
            this.attachmentUrl = res.data.data.attachmentUrl
            this.urlList.forEach((item) => {
              const imgurl = __PATH.IEMC_URL + 'uploadDispose/' + item
              this.fileEcho.push({
                url: imgurl,
                name: imgurl.split('/')[imgurl.split('/').length - 1].split('?')[0]
              })
              this.formInline.file.push(imgurl)
            })
          }
        }
      })
    },
    /**
     * 提交
     */
    complete() {
      if (!this.formInline.disposeResult) {
        this.$message.error('请选择处置结果')
        return
      }
      disposePolice({
        id: this.$route.query.id,
        ...this.formInline,
        attachmentUrl: this.attachmentUrl
      }).then((res) => {
        if (res.data.code === '200') {
          this.$message.success(res.data.message)
          this.$router.go(-1)
        } else {
          this.$message.error(res.data.message)
        }
      })
    },
    // 文档下载
    downLoad(row) {
      const url = `${__PATH.VUE_APP_IEMC_ELEVATOR_API}uploadDispose/${row}`
      // 获取后缀
      const index = row.lastIndexOf('.')
      const ext = row.substr(index + 1)
      if (ext === 'png' || ext === 'jpg' || ext === 'JPG' || ext === 'JPEG' || ext === 'gif' || ext === 'pdf' || ext === 'PDF') {
        window.open(url)
      } else {
        location.href = url
      }
    },
    //  ------------------------------------------上传文件相关-----------------------------------------
    httpRequest(item) {
      this.formInline.file.push(item.file)
    },
    handleExceed() {
      this.$message.error('最多上传九份文件')
    },
    beforeAvatarUpload(file) {
      const isLt5M = file.size / 1024 / 1024 < 5
      if (!isLt5M) {
        this.$message.error('上传文件大小不能超过 5MB!')
        return false
      }
      if (file.name.indexOf(',') !== -1) {
        this.$message.error('非法的文件名')
        return false
      }
    },
    handleRemove(file, fileList) {
      this.formInline.file = []
      fileList.forEach((item) => {
        this.formInline.file.push(item.raw)
      })
    },
    // 获取摄像机列表
    // 摄像机列表
    getVideoList() {
      getVideoList({
        surveyCode: this.taskDetail.surveyCode
      }).then((resp) => {
        const res = resp.data
        if (res.code === '200') {
          this.videoTabsList = res.data
        }
      })
    },
    // 摄像机点击事件
    videoChange(item) {
      this.cameraTalkDialogShow = true
      const params = {
        cameraId: item.vidiconId
      }
      getHlvAddress(params).then((resp) => {
        const res = resp.data
        if (res.code === '200') {
          this.videoName = item.vidiconName
          this.videoUrl = res.data
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.warnDisposal {
  position: relative;
  width: 100%;
  height: 100%;
  padding: 16px;
  background: url('~@/assets/images/common/details_bgm.png') no-repeat center / 100%;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  padding-bottom: 80px;
}
.warnDisposal-title {
  height: 30px;
  position: relative;
  div {
    cursor: pointer;
    position: absolute;
    top: 0;
    left: 0;
    width: 80px;
    height: 30px;
    line-height: 30px;
    text-align: center;
    color: #618ad3;
    font-size: 0.875rem;
    white-space: nowrap;
    background-image: url('~@/assets/images/peace/btn-back.png');
    background-size: 100% 100%;
    background-repeat: no-repeat;
  }
}
.warnDisposal-main {
  flex: 1;
  overflow: auto;
  .module-item {
    width: 70%;
    background: rgba(6, 18, 78, 0.5);
    margin: 0 auto;
    .module-title {
      height: 30px;
      line-height: 30px;
      color: #b7cfff;
      padding-left: 38px;
      background: url('~@/assets/images/common/details_module_title_bgm.png') no-repeat center / 100%;
    }
    .module-content {
      display: flex;
      flex-wrap: wrap;
      padding: 18px 20px;
      .content-item {
        padding: 12px 30px;
        :deep(.el-input) {
          .el-input__inner {
            background: rgba(3, 23, 81, 0.5);
            border-radius: 4px;
            border: 1px solid #193382;
          }
        }
        .camera-box {
          height: 32px;
          line-height: 32px;
          font-size: 14px;
          font-family: PingFang SC-Regular, PingFang SC;
          font-weight: 400;
          color: #7cd0ff;
          display: flex;
          cursor: pointer;
          span {
            display: block;
            margin: auto 5px;
            width: 16px;
            height: 16px;
            background: url('~@/assets/images/common/camera.png') no-repeat;
            background-size: 16px 16px;
          }
        }
      }
      .item-label {
        font-size: 14px;
        font-weight: 400;
        color: #ffffff;
        line-height: 24px;
        margin-bottom: 4px;
      }
      :deep(.mterial_file) {
        .el-upload-dragger {
          border: 1px solid #3056a2;
          background: rgba(3, 23, 81, 0.5);
          width: 380px;
          height: 100px;
          .el-icon-upload {
            font-size: 24px;
            line-height: 24px;
            margin: 23px 0 16px 0 !important;
          }
        }
      }
    }
  }
}
.warnDisposal-footer {
  margin: 24px auto 0;
  width: 70%;
  text-align: right;
}
::v-deep .camera-loader {
  width: 30%;
  height: 50%;
  background: url('~@/assets/images/elevator/page-bg.png') no-repeat center;
  background-size: 100% 100%;
  box-shadow: none;
  .el-dialog__body {
    height: 100%;
    width: 100%;
    padding: 0;
    .camera-talk-content {
      width: 100%;
      height: 100%;
      .talk-header {
        height: 30px;
        padding-left: 45px;
        padding-right: 10px;
        background: url('~@/assets/images/elevator/card-title-long-bg.png') no-repeat;
        background-size: 100% 100%;
        display: flex;
        justify-content: space-between;
        align-items: center;
        .left-title {
          font-size: 15px;
          font-family: Alibaba PuHuiTi-Medium, Alibaba PuHuiTi;
          font-weight: 500;
          color: #b7cfff;
        }
        .right-icon {
          width: 24px;
          height: 24px;
          background: url('~@/assets/images/elevator/dialog-close.png') no-repeat;
          background-size: 100% 100%;
          cursor: pointer;
        }
      }
      .talk-content {
        height: calc(100% - 30px);
        background: rgba(6, 18, 78, 0.5);
        border: 1px solid #3563a9;
        border-top: none;
        // padding: 24px;
        box-sizing: border-box;
      }
    }
  }
  .el-dialog__header,
  .el-dialog__footer {
    display: none;
  }
}
</style>
