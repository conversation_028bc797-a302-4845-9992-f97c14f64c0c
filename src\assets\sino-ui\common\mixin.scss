@charset "utf-8";
@import "./var.scss";

// @mixin themeify {

//     @each $theme-name,
//     $theme-map in $background_themes {
//         //!global 把局部变量强升为全局变量
//         $theme-map: $theme-map !global;

//         //判断html的data-theme的属性值  #{}是sass的插值表达式
//         //& sass嵌套里的父容器标识   @content是混合器插槽
//         [data-theme="#{$theme-name}"] & {
//             @content;
//         }
//     }
// }

// @function themed($key) {
//     @return map-get($theme-map, $key);
// }

// @mixin skin-constructor($color) {
//     @include themeify {
//         background-color: themed($color)
//     }
// }

//字体大小
@mixin font_size($size) {
    /*通过该函数设置字体大小，后期方便统一管理；*/
    @include font-dpr($size);
}

@mixin font_color($color) {
    color: $color;

    [data-theme="theme-black"] & {
        color: $font-color-theme1;
    }

    [data-theme="theme-blue"] & {
        color: $font-color-theme2;
    }

    [data-theme="theme-green"] & {
        color: $font-color-theme3;
    }
}

@mixin bg_color($color) {
    background-color: $color;

    [data-theme="theme-black"] & {
        background-color: $background-color-theme1;
    }

    [data-theme="theme-blue"] & {
        background-color: $background-color-theme2;
    }

    [data-theme="theme-green"] & {
        background-color: $background-color-theme3;
    }
}

@mixin bg_color_aside($color) {
    background-color: $color;

    [data-theme="theme-black"] & {
        background-color: $aside-background1;
    }

    [data-theme="theme-blue"] & {
        background-color: $aside-background2;
    }

    [data-theme="theme-green"] & {
        background-color: $aside-background3;
    }
}
@mixin bg_color_header($color) {
    background-color: $color;

    [data-theme="theme-black"] & {
        background-color:$background-color-header1;
    }

    [data-theme="theme-blue"] & {
        background-color: $background-color-header2;
    }

    [data-theme="theme-green"] & {
        background-color: $background-color-header3;
    }
}

@mixin overflow($num:1,$fontSize:0,$lineHeight:1.5){
    display: -webkit-box;-webkit-line-clamp:$num; overflow: hidden;
    -webkit-box-orient: vertical;
    @if $fontSize!=0 and $lineHeight{
      line-height:$lineHeight;
      @if $lineHeight < 1.2 {
        line-height:1.2; 
      }
      height: $num * $fontSize * $lineHeight;
      [data-dpr="2"] & { height: $num * $fontSize * $lineHeight * 2!important;}
      [data-dpr="3"] & { height: $num * $fontSize * $lineHeight * 3!important;}
    }
  
}

//transition兼容写法
@mixin transition($content:all .2s) {
    -moz-transition: $content;
    -webkit-transition: $content;
    -o-transition: $content;
    transition: $content;
}

//transfrom兼容
@mixin translateX($num:-50%) {
    -ms-transform: translateX($num);
    -moz-transform: translateX($num);
    -webkit-transform: translateX($num);
    -o-transform: translateX($num);
    transform: translateX($num);
}

@mixin translateY($num:-50%) {
    -ms-transform: translateY($num);
    -moz-transform: translateY($num);
    -webkit-transform: translateY($num);
    -o-transform: translateY($num);
    transform: translateY($num);
}

@mixin rotate($deg:90deg) {
    -ms-transform: rotate($deg);
    -moz-transform: rotate($deg);
    -webkit-transform: rotate($deg);
    -o-transform: rotate($deg);
    transform: rotate($deg);
}