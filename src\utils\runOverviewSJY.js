import http from './http'

const BASE_API = __PATH.VUE_APP_BASE_API
const newIotApi = __PATH.VUE_TRANSFER_API
// 接诊量排行
export function deptconsultation (params) {
  return http.postRequest(`${BASE_API}/overview/deptconsultation`, params)
}
// 科室次均费用排行
export function deptcost (params) {
  return http.postRequest(`${BASE_API}/overview/deptcost`, params)
}
// 科室收入排行
export function incomeRanking (params) {
  return http.postRequest(`${BASE_API}/overview/incomeRanking`, params)
}
// 收入分析
export function revenueAnalysis (params) {
  return http.postRequest(`${BASE_API}/overview/revenueAnalysis`, params)
}
// 收入分析
export function information (params) {
  return http.postRequest(`${BASE_API}/overview/information`, params)
}
// 椅位统计
export function chairStatistics (params) {
  // return http.postRequest(`${newIotApi}/chair/chairStatistics`, params)
  return http.postRequest(`${newIotApi}/chair/chairStatisticsRatio`, params)
}
// 平均椅位排行
export function averageDeptconsultation (params) {
  return http.postRequest(`${BASE_API}/overview/averageDeptconsultation`, params)
}
// 椅位量排行
export function deptChaiRanking (params) {
  return http.postRequest(`${newIotApi}/chair/deptChaiRanking`, params)
}
