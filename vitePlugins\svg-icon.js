/*
 * @Author: hedd
 * @Date: 2023-03-10 15:34:55
 * @LastEditTime: 2023-03-10 18:53:20
 * @FilePath: \IHCRS_alarm\vitePlugins\svg-icon.js
 * @Description:
 */
import path from 'path'
import { createSvgIconsPlugin } from 'vite-plugin-svg-icons'

export default function createSvgIcon(isBuild) {
  return createSvgIconsPlugin({
    iconDirs: [path.resolve(process.cwd(), 'src/assets/icons/')],
    symbolId: 'icon-[dir]-[name]',
    svgoOptions: isBuild
  })
}
