<template>
  <div class="deviceEnergyData">
    <div class="deviceEnergyData-head">
      <span>日期：</span>
      <el-date-picker
        v-model="statisticalDate"
        popper-class="date-style"
        type="daterange"
        value-format="yyyy-MM-dd HH:mm:ss"
        unlink-panels
        range-separator="至"
        start-placeholder="开始日期"
        end-placeholder="结束日期"
        :picker-options="{
          firstDayOfWeek: 1
        }"
        @change="dataPickerValChange()"
      >
      </el-date-picker>
      <div class="top_type">
        <div class="top_type_item" :class="{ activeType: dateType === 1 }" @click="dateChange(1)">
          <span>今日</span>
        </div>
        <div class="top_type_item" :class="{ activeType: dateType === 2 }" @click="dateChange(2)">
          <span>本月</span>
        </div>
        <div class="top_type_item" :class="{ activeType: dateType === 4 }" @click="dateChange(4)">
          <span>本年</span>
        </div>
        <div class="top_type_item" :class="{ activeType: dateType === 5 }" @click="dateChange(5)">
          <span>自定义</span>
        </div>
      </div>
    </div>
    <div v-loading="chartLoading" class="energy-loading-box">
      <div class="top-statistics">
        <p>总能耗数据</p>
        <p><span class="normal-color">{{ energyStatistics.total || '-' }}</span> kwh</p>
      </div>
      <div class="analysisLineEcharts">
        <div v-if="showEnergyEcharts" id="analysisLineEcharts"></div>
        <div v-else class="center-center">暂无数据</div>
      </div>
    </div>
  </div>
</template>

<script>
import * as echarts from 'echarts'
import moment from 'moment'
import { KgceLogin, GetPointHistoryList } from '@/utils/energyConsumption'
import { getModelFormulaPointList } from '@/utils/spaceManage'
moment.locale('zh-cn')
export default {
  name: 'deviceEnergyData',
  props: {
    // dialogData: {
    //   type: Object,
    //   default: () => {}
    // },
    surveyCode: {
      type: String,
      default: ''
    }
  },
  data() {
    const dataType = {
      1: {
        type: 'hour',
        format: 'HH',
        unit: '时'
      },
      2: {
        type: 'day',
        format: 'DD',
        unit: '日'
      },
      3: {
        type: 'day',
        format: 'DD',
        unit: '日'
      },
      4: {
        type: 'month',
        format: 'MM',
        unit: '月'
      },
      5: {
        type: 'day',
        format: 'YYYY-MM-DD',
        unit: ''
      }
    }
    return {
      dataType,
      statisticalDate: [],
      dateType: '',
      kgceToken: '',
      chartLoading: false,
      showEnergyEcharts: false,
      energyStatistics: {
        total: 0,
        cost: 0
      }
    }
  },
  computed: {},
  mounted() {
    this.dateChange(1)
  },
  methods: {
    dateChange(val) {
      const date = {
        1: [moment().format('YYYY-MM-DD') + ' 00:00:00', moment().format('YYYY-MM-DD') + ' 23:59:59'],
        2: [moment().startOf('month').format('YYYY-MM-DD') + ' 00:00:00', moment().endOf('month').format('YYYY-MM-DD') + ' 23:59:59'],
        4: [moment().startOf('year').format('YYYY-MM-DD') + ' 00:00:00', moment().endOf('year').format('YYYY-MM-DD') + ' 23:59:59'],
        5: []
      }
      this.statisticalDate = date[val]
      this.dateType = val
      this.chartLoading = true
      if (val != 5) {
        // 如果能耗未登录则登录
        if (this.kgceToken) {
          this.getModelFormulaPointList()
        } else {
          this.kgceLogin()
        }
      }
    },
    // 切换能耗时间选择器
    dataPickerValChange() {
      this.dateType = 5
      this.getModelFormulaPointList()
    },
    // 获取所有实体对应的传感器编码
    getModelFormulaPointList() {
      getModelFormulaPointList({ surveyCode: this.surveyCode, gyToken: this.kgceToken }).then((res) => {
        // 如果有数据则调用能耗接口
        const resData = res.data
        if (resData.code === '200' && resData.data.length) {
          // resData = ['ZHPN-SXDB-B2-ZAL2-4', 'ZHPN-SXDB-ZYB-10AT-6', 'ZHPN-SXDB-ZYA-10AL1']
          this.getEnergyData(resData.data.toString())
        } else {
          this.showEnergyEcharts = false
          this.chartLoading = false
        }
      })
    },
    // 能耗登录
    kgceLogin() {
      KgceLogin(KECG_ACCOUNT).then((res) => {
        if (res.data.code === 200) {
          this.kgceToken = res.data.result.token
          this.getModelFormulaPointList()
        }
      })
    },
    // 获取能耗数据
    getEnergyData(measurePointNo) {
      const params = {
        dataType: this.dataType[this.dateType].type,
        startDate: this.statisticalDate[0],
        endDate: this.statisticalDate[1],
        measurePointNo, // 测点编号
        domainKeyword: '********', // 正向有功总电能
        sum: false // 是否计算合计值
      }
      GetPointHistoryList(params, this.kgceToken).then((res) => {
        this.chartLoading = false
        if (res.data.code === 200) {
          const data = res.data.data
          if (data && data.length) {
            data.sort((a, b) => moment(a.time) - moment(b.time))
          }
          // 生成一个数组对象，data数组中time对象一致的合并
          const dateForData = []
          data.forEach((item) => {
            const index = dateForData.findIndex((v) => v.name === item.time)
            if (index > -1) {
              dateForData[index].value += Number(item.value)
            } else {
              dateForData.push({
                name: item.time,
                value: Number(item.value)
              })
            }
          })
          // 能耗统计
          const total = dateForData.reduce((pre, cur) => {
            return pre + cur.value
          }, 0)
          this.energyStatistics = {
            total: total.toFixed(2),
            cost: 0
          }
          this.showEnergyEcharts = true
          this.$nextTick(() => {
            this.electricityLineEchart(dateForData)
          })
        } else {
          this.showEnergyEcharts = false
          this.chartLoading = false
        }
      })
    },
    electricityLineEchart(data) {
      const getchart = echarts.init(document.getElementById('analysisLineEcharts'))
      const unit = this.dataType[this.dateType].unit
      const nameList = data.map((item) => moment(item.name).format(this.dataType[this.dateType].format))
      const toFixedData = data.map((item) => {
        return {
          name: item.name,
          value: item.value.toFixed(2)
        }
      })
      const option = {
        backgroundColor: '',
        dataZoom: [
          {
            type: 'inside',
            start: 0,
            end: 100
          }
        ],
        tooltip: {
          trigger: 'axis',
          formatter: function (params) {
            return params[0].data.name + '</br>' + params[0].marker + params[0].data.value + ' kwh'
          },
          axisPointer: {
            lineStyle: {
              color: '#FFE3A6'
            }
          },
          backgroundColor: '#0C2269',
          borderColor: '#5B617C',
          textStyle: {
            color: '#719DE7'
          }
        },
        grid: {
          top: '8%',
          left: '5%',
          right: '4$%',
          bottom: '12%'
        },
        xAxis: {
          name: unit,
          type: 'category',
          data: nameList,
          boundaryGap: false,
          axisTick: {
            show: false
          },
          axisLine: {
            // show: false,
            lineStyle: {
              color: '#303F69'
            }
          },
          axisLabel: {
            textStyle: {
              fontSize: 10,
              color: '#8C9EB5'
            }
          }
        },
        yAxis: {
          type: 'value',
          axisTick: {
            show: false
          },
          // axisLine: {
          //   lineStyle: {
          //     color: '#609ee9'
          //   }
          // },
          axisLabel: {
            margin: 10,
            textStyle: {
              fontSize: 10,
              color: '#8C9EB5'
            }
          },
          splitLine: {
            show: true,
            lineStyle: {
              color: ['#303F69'],
              width: 1,
              type: 'solid'
            }
          }
        },
        series: [
          {
            type: 'line',
            smooth: true,
            showSymbol: false,
            symbol: 'circle',
            symbolSize: 6,
            data: toFixedData,
            itemStyle: {
              normal: {
                color: '#FFE3A6'
              }
            },
            lineStyle: {
              normal: {
                width: 2
              }
            }
          }
        ]
      }
      getchart.clear()
      getchart.setOption(option)
      // 随着屏幕大小调节图表
      window.addEventListener('resize', () => {
        getchart.resize()
      })
    }
  }
}
</script>

<style lang="scss" scoped>
@import '../../../../assets/sino-ui/common/var.scss';
.deviceEnergyData {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  .deviceEnergyData-head {
    display: flex;
    align-items: center;
  }
  .top_type {
    display: flex;
    justify-content: space-evenly;
    flex-wrap: wrap;
    margin-left: 16px;
    .top_type_item {
      cursor: pointer;
      margin-right: 6px;
      padding: 6px 8px;
      background: rgba(255, 255, 255, 0.1) linear-gradient(90deg, rgba(10, 132, 255, 0) 0%, rgba(10, 132, 255, 0) 100%);
      font-size: 14px;
      font-weight: 400;
      color: #b0e3fa;
      line-height: 14px;
      border: 1px solid transparent;
    }
    .activeType {
      background: rgba(255, 255, 255, 0.1) linear-gradient(90deg, rgba(10, 132, 255, 0.4) 0%, rgba(10, 132, 255, 0) 100%);
      border: 1px solid;
      border-image: radial-gradient(circle, rgba(171, 240, 255, 1), rgba(226, 254, 255, 1), rgba(132, 196, 203, 1), rgba(48, 151, 166, 0)) 1 1;
    }
  }
  .energy-loading-box {
    height: calc(100% - 33px);
    display: flex;
    flex-direction: column;
    .top-statistics {
      height: 88px;
      width: 100%;
      margin: 10px 0;
      display: flex;
      flex-direction: column;
      justify-content: space-around;
      align-items: center;
      background: rgba(53, 98, 219, 0.06);
      p {
        font-size: 15px;
        font-family: PingFang SC, PingFang SC;
        color: #ffffff;
        span {
          font-size: 24px;
          font-family: Arial, Arial;
          font-weight: bold;
        }
      }
    }
    .analysisLineEcharts {
      flex: 1;
      #analysisLineEcharts {
        width: 100%;
        height: 100%;
      }
    }
  }
  ::v-deep .el-date-editor {
    width: 280px;
    height: 35px;
    background-color: #14233e;
    border-color: rgba(133, 145, 206, 0.5);
    .el-input__icon {
      transform: translateY(-2px);
    }
    .el-range-input {
      background-color: #14233e;
      color: rgba(255, 255, 255, 0.8);
    }
    .el-range-separator {
      color: rgba(255, 255, 255, 0.8);
      transform: translateY(-2px);
    }
  }
}
</style>
