<template>
  <div class="planList">
    <el-table
      v-el-table-infinite-scroll="tableLoadMore"
      class="table-center-transfer"
      :data="planList"
      height="100%"
      :cell-style="{ padding: ' 8px', backgroundColor: 'transparent', color: '#fff', border: 'none', padding: '10px 3px' }"
      :header-cell-style="{ background: '#8591CE26!important', color: '#8BDDF5FF', padding: '10px 8px', fontWeight: 'bold' }"
      style="width: 100%"
      element-loading-background="rgba(0, 0, 0, 0.2)"
      v-loading="planTableLoading"
    >
      <el-table-column prop="planName" label="" width="100" show-overflow-tooltip>
        <template slot-scope="scope">
          <img style="width:48px; height:48px;cursor: pointer;" :src="$tools.imgUrlTranslation(JSON.parse(scope.row.regulationsFlow)[0]?.url ?? '')" @click="() => viewImage(scope.row)"/>
        </template>
      </el-table-column>
      <el-table-column prop="planName" label="预案名称" width="200" show-overflow-tooltip>
        <template slot-scope="scope">
          <p class="planName" style="color: #8BDDF5;">{{ scope.row.planName }}</p>
          <p class="planName">{{ scope.row.regulationsDesc }}</p>
        </template>
      </el-table-column>
      <el-table-column prop="planCategoryName" label="预案分类" show-overflow-tooltip></el-table-column>
      <el-table-column prop="planType" label="预案类型" show-overflow-tooltip>
        <template slot-scope="scope">
          {{ scope.row.planType ? planTypeList.find(v => v.imhId == scope.row.planType)?.imhMonitorName : '' }}
        </template>
      </el-table-column>
      <el-table-column prop="alarmTypeName" label="报警类型" show-overflow-tooltip></el-table-column>
      <el-table-column prop="spaceTypeName" label="空间类型" show-overflow-tooltip></el-table-column>
      <el-table-column prop="versionNo" label="版本号" show-overflow-tooltip></el-table-column>
      <el-table-column label="操作" width="100" show-overflow-tooltip>
        <template slot-scope="scope">
          <span class="operationBtn-span" style="color: #8BDDF5; cursor: pointer;" @click="viewDetails(scope.row)">查看</span>
        </template>
      </el-table-column>
    </el-table>
    <el-image-viewer v-if="showViewer" :on-close="() => showViewer = false" :url-list="iconPathList" />
  </div>
</template>

<script>
import ElImageViewer from 'element-ui/packages/image/src/image-viewer'
import { GetPlanList, GetAlarmSystem } from '@/utils/wartimeMode'
export default {
  name: 'planList',
  components: {
    ElImageViewer
  },
  data() {
    return {
      iconPathList: [], // 图片列表
      showViewer: false, // 图片预览
      planList: [], // 设备列表
      planTableLoading: true,
      pagination: {
        total: 0,
        pageSize: 15,
        pageNo: 1
      },
      planTypeList: [] // 预案类型列表
    }
  },
  computed: {

  },
  created() {
    this.getAlarmSystem()
    this.getPlanList()
  },
  methods: {
    viewDetails(row) {
      this.$router.push({
        path: '/planDetails',
        query: {
          planType: row.planCategory,
          id: row?.id ?? '',
          alarmId: this.$route.query?.alarmId,
          alarmType: this.$route.query?.alarmType
        }
      })
    },
    // 查看图片
    viewImage(row) {
      const icon = JSON.parse(row.regulationsFlow)
      if (!icon.length) {
        this.$message.info('当前没有可预览的图片')
        return
      }
      this.iconPathList = [this.$tools.imgUrlTranslation(icon[0].url)]
      this.showViewer = true
    },
    // 获取预案类型
    getAlarmSystem() {
      GetAlarmSystem({ projectCodes: '' }).then((res) => {
        if (res.data.code == 200) {
          this.planTypeList = res.data.data
        }
      })
    },
    // 获取预案列表
    getPlanList() {
      const params = {
        planCategory: '0,1',
        pageSize: this.pagination.pageSize,
        page: this.pagination.pageNo
      }
      if (this.pagination.pageNo == 1) this.planList = []
      GetPlanList(params).then((res) => {
        this.planTableLoading = false
        if (res.data.code === '200') {
          this.planList = this.planList.concat(res.data.data.records)
          this.planList.forEach(item => {
            item.planCategoryName = item.planCategory == '0' ? '智能预案' : item.planCategory == '1' ? '常规预案' : '废除预案'
          })
          this.pagination.total = res.data.data.total
        }
      })
    },
    tableLoadMore() {
      if (this.pagination.total > this.pagination.pageNo * this.pagination.pageSize) {
        this.pagination.pageNo++
        this.getPlanList()
      }
    }
  }
}

</script>

<style lang="scss" scoped>
.planList {
  width: 100%;
  height: 100%;
  .planName {
    overflow:hidden;
    text-overflow:ellipsis;
    white-space:nowrap;
  }
}
</style>
<style>
.el-image-viewer__wrapper {
  width: 60%;
  left: auto;
  overflow: hidden;
}
</style>
