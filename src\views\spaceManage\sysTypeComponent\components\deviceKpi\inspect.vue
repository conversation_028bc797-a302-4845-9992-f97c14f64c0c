<template>
  <div class="inspect">
    <div id="inspect_chart"></div>
  </div>
</template>

<script>
import * as echarts from 'echarts'
import { mockInspectData } from './mockData.js'
export default {
  name: 'inspect',
  data() {
    return {
      inspectData: {
        yCates: ['20:00-22:00', '16:00-18:00', '12:00-14:00', '08:00-10:00', '04:00-06:00', '00:00-02:00'],
        xCates: ['2022-08-08', '2022-06-06', '2022-05-14', '2022-03-22', '2022-02-13'],
        data: [
          [0, 0, 0],
          [0, 1, 0],
          [0, 2, 0],
          [0, 3, 0],
          [0, 4, 0],
          [1, 0, 0],
          [1, 1, 0],
          [1, 2, 0],
          [1, 3, 0],
          [1, 4, 0],
          [2, 0, 0],
          [2, 1, 0],
          [2, 2, 0],
          [2, 3, 0],
          [2, 4, 0],
          [3, 0, 0],
          [3, 1, 0],
          [3, 2, 0],
          [3, 3, 0],
          [3, 4, 0],
          [4, 0, 0],
          [4, 1, 0],
          [4, 2, 0],
          [4, 3, 0],
          [4, 4, 0],
          [5, 0, 0],
          [5, 1, 0],
          [5, 2, 0],
          [5, 3, 0],
          [5, 4, 0]
        ]
      }
    }
  },
  computed: {},
  created() {},
  mounted() {
    if (__PATH.VUE_APP_HOSPITAL_NODE === 'outernet') {
      this.inspectData = mockInspectData
    }
    this.$nextTick(() => {
      this.setChart()
    })
  },
  methods: {
    setChart() {
      const getchart = echarts.init(document.getElementById('inspect_chart'))
      const model = this.inspectData
      const data = model.data.map(function (item) {
        return [item[1], item[0], item[2] || '-']
      })
      const option = {
        title: {
          text: '日最高检查量TOPS',
          textStyle: {
            fontSize: 16,
            fontWeight: 'normal',
            color: '#fff'
          },
          x: 20,
          top: 20
        },
        tooltip: {
          position: 'top'
        },
        animation: false,
        grid: {
          top: '25%',
          left: '15%',
          right: '5%',
          bottom: '10%'
        },
        xAxis: {
          type: 'category',
          axisTick: {
            show: false
          },
          axisLine: {
            show: false
          },
          axisLabel: {
            show: true,
            textStyle: {
              color: '#E6F7FF'
            }
          },
          data: model.xCates
        },
        yAxis: {
          type: 'category',
          axisTick: {
            show: false
          },
          axisLine: {
            show: false
          },
          axisLabel: {
            show: true,
            textStyle: {
              color: '#E6F7FF'
            }
          },
          data: model.yCates
        },
        visualMap: {
          min: 1,
          max: 100,
          calculable: true,
          orient: 'horizontal',
          left: 'center',
          top: '12%',
          textStyle: {
            color: '#fff'
          },
          inRange: {
            color: ['#CBE8F8', '#0151B4']
          }
        },
        series: [
          {
            name: '',
            type: 'heatmap',
            data: data,
            label: {
              normal: {
                show: true
              }
            },
            itemStyle: {
              emphasis: {
                shadowBlur: 10,
                shadowColor: 'rgba(0, 0, 0, 0.5)'
              }
            }
          }
        ]
      }
      getchart.clear()
      getchart.setOption(option)
      // 随着屏幕大小调节图表
      window.addEventListener('resize', () => {
        getchart.resize()
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.inspect {
  width: 100%;
  height: 100%;
  background: rgba(53, 98, 219, 0.06);
  #inspect_chart {
    width: 100%;
    height: 100%;
  }
}
</style>
