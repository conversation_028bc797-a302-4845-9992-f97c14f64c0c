<template>
  <div class="main">
    <el-dialog v-dialogDrag :modal="false" :visible.sync="dialogShow" custom-class="mainDialog main" append-to-body :close-on-click-modal="false" :before-close="closeDialog" class="personDialog">
      <template slot="title">
        <span class="dialog-title">资产列表</span>
      </template>
      <div class="dialog-content">
        <el-table
          @row-dblclick="selectConfigRowData"
          :data="tableData"
          height="calc(100% - 40px)"
          :cell-style="$tools.setCell(3)"
          :header-cell-style="$tools.setHeaderCell(3)"
          style="width: 100%"
          v-loading="tableLoading"
          element-loading-background="rgba(0, 0, 0, 0.2)"
        >
          <el-table-column label="序号" type="index" width="100" />
          <el-table-column v-for="item in column" :key="item.id" :prop="item.prop" show-overflow-tooltip :label="item.label" />
          <el-table-column show-overflow-tooltip label="操作">
              <template slot-scope="scope">
                <div class="operationBtn">
                  <span @click="selectConfigRowData(scope.row)">详情</span>
                </div>
              </template>
            </el-table-column>
        </el-table>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import { GetHiddenDangersList } from '@/utils/centerScreenApi'
export default {
  name: 'workOrderCenterList',
  components: {
  },
  props: {
    dialogShow: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      tableData: [],
      tableLoading: false,
      column: [
        { label: '资产状态', prop: 'value1' },
        { label: '使用状态', prop: 'value2' },
        { label: '资产编码', prop: 'value3' },
        { label: '资产名称', prop: 'value4' },
        { label: '使用科室', prop: 'value5' },
        { label: '启用日期', prop: 'value6' },
        { label: '使用年限（月）', prop: 'value7' },
        { label: '维保到期', prop: 'value8' }
      ]
    }
  },
  watch: {},
  created() {},
  mounted() {
    // this.getWorkOrderTableData()
  },
  methods: {
    selectConfigRowData(row) {
      this.$router.push({
        path: '/assetsDetails',
        query: { id: row.id }
      })
    },
    // 获取列表
    getWorkOrderTableData(params) {
      // this.tableLoading = true
      this.tableData = [
        { value1: '在用', value2: '闲置', value3: '2010-00344', value4: '医用磁共振成像设备', value5: '总务处', value6: '', value7: '60', value8: '2022-04-14' },
        { value1: '借用中', value2: '闲置', value3: '2016020618', value4: '膝关节测量系统', value5: '总务处', value6: '2020-08-27', value7: '60', value8: '' },
        { value1: '在用', value2: '闲置', value3: '2014-00059', value4: '高档多参数监护仪', value5: '总务处', value6: '', value7: '60', value8: '' }
      ]
      GetHiddenDangersList({ ...params, pageNo: 1, pageSize: 999 }).then((res) => {
        const data = res.data
        this.tableLoading = false
        if (data.code === '200') {
          // this.tableData = data.data.list
        } else {
          this.$message({
            message: data.data.msg,
            type: 'warning'
          })
        }
      })
    },
    // 取消按钮
    closeDialog() {
      this.$emit('configCloseDialog')
    }
  }
}
</script>

<style lang="scss" type="text/css" scoped>
.personDialog {
  pointer-events: none;
  ::v-deep .el-image__error {
    background: center;
  }
}
::v-deep .mainDialog {
  width: 98%;
  height: 40vh;
  margin-top: 54vh !important;
  background: url('@/assets/images/qhdsys/bottom-table-bg.png') no-repeat;
  background-size: 100% 100%;
  padding: 0 35px;
  pointer-events: auto;
  .dialog-title {
    display: inline-block;
    width: 38%;
  }
  .el-dialog__body {
    padding: 0;
    height: calc(100% - 10px);
    .dialog-content {
      width: 100%;
      height: 100%;
      .el-table {
        border: none !important;
        .redBg {
          background: #341f51;
        }
      }
    }
  }
}
</style>
