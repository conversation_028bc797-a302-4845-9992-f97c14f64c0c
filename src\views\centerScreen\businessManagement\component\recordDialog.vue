<template>
  <el-dialog v-if="visible" :modal="false" :close-on-click-modal="false" width="20%" :visible.sync="visible" custom-class="new-model-dialog" :before-close="closeDialog">
    <div class="content">
      <el-tabs v-model="tabsActiveName" class="module-tabs" stretch>
        <el-tab-pane label="实时数据" name="1">
          <div class="record-list">
            <div class="list-item">
              <p class="item-label">今日电量：</p>
              <p class="item-value">{{ recordData.find((v) => v.id == recordId).day.num + recordData.find((v) => v.id == recordId).unit }}</p>
            </div>
            <div class="list-item">
              <p class="item-label">本周电量：</p>
              <p class="item-value">{{ recordData.find((v) => v.id == recordId).week.num + recordData.find((v) => v.id == recordId).unit }}</p>
            </div>
            <div class="list-item">
              <p class="item-label">本月电量：</p>
              <p class="item-value">{{ recordData.find((v) => v.id == recordId).month.num + recordData.find((v) => v.id == recordId).unit }}</p>
            </div>
            <div class="list-item">
              <p class="item-label">本年电量：</p>
              <p class="item-value">{{ recordData.find((v) => v.id == recordId).year.num + recordData.find((v) => v.id == recordId).unit }}</p>
            </div>
            <div class="list-item">
              <p class="item-label">累计电量：</p>
              <p class="item-value">{{ recordData.find((v) => v.id == recordId).total + recordData.find((v) => v.id == recordId).unit }}</p>
            </div>
          </div>
        </el-tab-pane>
        <el-tab-pane label="历史曲线" name="2">
          <el-dropdown trigger="click" @command="dataTypeCommand">
            <span class="el-dropdown-link"> {{ dataTypeList.find((v) => v.value == statisticalType)?.name ?? '' }} <i class="el-icon-caret-bottom"></i> </span>
            <el-dropdown-menu slot="dropdown">
              <el-dropdown-item v-for="item in dataTypeList" :key="item.value" :command="item.value" :class="{ isBjxl: statisticalType == item.value }">{{ item.name }}</el-dropdown-item>
            </el-dropdown-menu>
          </el-dropdown>
          <div id="analysisLineEcharts"></div>
        </el-tab-pane>
      </el-tabs>
    </div>
  </el-dialog>
</template>
<script>
import * as echarts from 'echarts'
export default {
  name: 'remarkDialog',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    recordId: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      tabsActiveName: '1',
      statisticalType: 'day', // 选中日期类型
      dataTypeList: [
        { value: 'day', name: '今日' },
        { value: 'week', name: '本周' },
        { value: 'month', name: '本月' },
        { value: 'year', name: '本年' }
      ],
      recordData: [
        {
          id: '0100103008',
          unit: 'kwh',
          day: {
            num: 171.21,
            list: [
              { x: '00:00', y: 21 },
              { x: '02:00', y: 24 },
              { x: '04:00', y: 28 },
              { x: '06:00', y: 38 },
              { x: '08:00', y: 43.2 },
              { x: '10:00', y: 45.01 }
            ]
          },
          week: {
            num: 892.45,
            list: [
              { x: '周一', y: 150 },
              { x: '周二', y: 133 },
              { x: '周三', y: 157 },
              { x: '周四', y: 159 },
              { x: '周五', y: 155 },
              { x: '周六', y: 153.45 }
            ]
          },
          month: {
            num: 2143.88,
            list: [
              { x: '01', y: 198 },
              { x: '02', y: 194 },
              { x: '03', y: 193 },
              { x: '04', y: 191 },
              { x: '05', y: 185 },
              { x: '06', y: 171 },
              { x: '07', y: 173 },
              { x: '08', y: 199 },
              { x: '09', y: 164 },
              { x: '10', y: 175.88 },
              { x: '11', y: 197 }
            ]
          },
          year: {
            num: 4433.88,
            list: [{ x: '一月', y: 4433.88 }]
          },
          total: 112453.7
        },
        {
          id: '0100103029',
          unit: 'kwh',
          day: {
            num: 165.11,
            list: [
              { x: '00:00', y: 26 },
              { x: '02:00', y: 27 },
              { x: '04:00', y: 25 },
              { x: '06:00', y: 25.5 },
              { x: '08:00', y: 26.6 },
              { x: '10:00', y: 25.5 }
            ]
          },
          week: {
            num: 2013.28,
            list: [
              { x: '周一', y: 334 },
              { x: '周二', y: 321 },
              { x: '周三', y: 334.28 },
              { x: '周四', y: 317 },
              { x: '周五', y: 344 },
              { x: '周六', y: 322 }
            ]
          },
          month: {
            num: 2143.88,
            list: [
              { x: '01', y: 189 },
              { x: '02', y: 193 },
              { x: '03', y: 194 },
              { x: '04', y: 194.41 },
              { x: '05', y: 191.47 },
              { x: '06', y: 187 },
              { x: '07', y: 191 },
              { x: '08', y: 194 },
              { x: '09', y: 191 },
              { x: '10', y: 198 },
              { x: '11', y: 191 }
            ]
          },
          year: {
            num: 3473.48,
            list: [{ x: '一月', y: 3473.48 }]
          },
          total: 125453.1,
          list: []
        },
        {
          id: '0100103058',
          unit: 'kwh',
          day: {
            num: 141.41,
            list: [
              { x: '00:00', y: 24.41 },
              { x: '02:00', y: 25 },
              { x: '04:00', y: 21 },
              { x: '06:00', y: 26 },
              { x: '08:00', y: 25 },
              { x: '10:00', y: 21 }
            ]
          },
          week: {
            num: 942.41,
            list: [
              { x: '周一', y: 157 },
              { x: '周二', y: 164 },
              { x: '周三', y: 157.41 },
              { x: '周四', y: 154 },
              { x: '周五', y: 160 },
              { x: '周六', y: 151 }
            ]
          },
          month: {
            num: 3103.08,
            list: [
              { x: '01', y: 278 },
              { x: '02', y: 261 },
              { x: '03', y: 268 },
              { x: '04', y: 278 },
              { x: '05', y: 288.08 },
              { x: '06', y: 271 },
              { x: '07', y: 274 },
              { x: '08', y: 278 },
              { x: '09', y: 275 },
              { x: '10', y: 271 },
              { x: '11', y: 279 }
            ]
          },
          year: {
            num: 5433.98,
            list: [{ x: '一月', y: 5433.98 }]
          },
          total: 122483.1,
        },
        {
          id: '0100103066',
          unit: 'kwh',
          day: {
            num: 141.41,
            list: [
              { x: '00:00', y: 24.41 },
              { x: '02:00', y: 25 },
              { x: '04:00', y: 21 },
              { x: '06:00', y: 26 },
              { x: '08:00', y: 25 },
              { x: '10:00', y: 21 }
            ]
          },
          week: {
            num: 942.41,
            list: [
              { x: '周一', y: 157 },
              { x: '周二', y: 164 },
              { x: '周三', y: 157.41 },
              { x: '周四', y: 154 },
              { x: '周五', y: 160 },
              { x: '周六', y: 151 }
            ]
          },
          month: {
            num: 3103.08,
            list: [
              { x: '01', y: 278 },
              { x: '02', y: 261 },
              { x: '03', y: 268 },
              { x: '04', y: 278 },
              { x: '05', y: 288.08 },
              { x: '06', y: 271 },
              { x: '07', y: 274 },
              { x: '08', y: 278 },
              { x: '09', y: 275 },
              { x: '10', y: 271 },
              { x: '11', y: 279 }
            ]
          },
          year: {
            num: 5433.98,
            list: [{ x: '一月', y: 5433.98 }]
          },
          total: 152356.9,
          list: []
        },
        {
          id: '0100103045',
          unit: 'kwh',
          day: {
            num: 176.21,
            list: [
              { x: '00:00', y: 30.1 },
              { x: '02:00', y: 32.1 },
              { x: '04:00', y: 29 },
              { x: '06:00', y: 28.6 },
              { x: '08:00', y: 29 },
              { x: '10:00', y: 33 }
            ]
          },
          week: {
            num: 734.45,
            list: [
              { x: '周一', y: 124 },
              { x: '周二', y: 118 },
              { x: '周三', y: 123 },
              { x: '周四', y: 114.45 },
              { x: '周五', y: 132 },
              { x: '周六', y: 121 }
            ]
          },
          month: {
            num: 2457.88,
            list: [
              { x: '01', y: 223.4 },
              { x: '02', y: 215.48 },
              { x: '03', y: 218 },
              { x: '04', y: 233 },
              { x: '05', y: 214 },
              { x: '06', y: 222 },
              { x: '07', y: 221 },
              { x: '08', y: 201 },
              { x: '09', y: 208 },
              { x: '10', y: 221 },
              { x: '11', y: 197 }
            ]
          },
          year: {
            num: 5501.88,
            list: [{ x: '一月', y: 5501.88 }]
          },
          total: 123045.7,
          list: []
        },
        {
          id: '0100103118',
          unit: '㎡',
          day: {
            num: 3.21,
            list: [
              { x: '00:00', y: 0.51 },
              { x: '02:00', y: 0.6 },
              { x: '04:00', y: 0.49 },
              { x: '06:00', y: 0.61 },
              { x: '08:00', y: 0.56 },
              { x: '10:00', y: 0.51 }
            ]
          },
          week: {
            num: 9.45,
            list: [
              { x: '周一', y: 1.51 },
              { x: '周二', y: 1.61 },
              { x: '周三', y: 1.59 },
              { x: '周四', y: 1.56 },
              { x: '周五', y: 1.59 },
              { x: '周六', y: 1.61 }
            ]
          },
          month: {
            num: 22.88,
            list: [
              { x: '01', y: 2.08 },
              { x: '02', y: 2.2 },
              { x: '03', y: 1.98 },
              { x: '04', y: 2.11 },
              { x: '05', y: 2.34 },
              { x: '06', y: 1.88 },
              { x: '07', y: 2.11 },
              { x: '08', y: 2.01 },
              { x: '09', y: 1.99 },
              { x: '10', y: 1.89 },
              { x: '11', y: 2.88 }
            ]
          },
          year: {
            num: 45.88,
            list: [{ x: '一月', y: 45.88 }]
          },
          total: 1238.7,
          list: []
        },
        {
          id: '0100103119',
          unit: '㎡',
          day: {
            num: 3.41,
            list: [
              { x: '00:00', y: 0.6 },
              { x: '02:00', y: 0.7 },
              { x: '04:00', y: 0.55 },
              { x: '06:00', y: 0.61 },
              { x: '08:00', y: 0.59 },
              { x: '10:00', y: 0.67 }
            ]
          },
          week: {
            num: 11.41,
            list: [
              { x: '周一', y: 1.92 },
              { x: '周二', y: 1.87 },
              { x: '周三', y: 1.88 },
              { x: '周四', y: 1.98 },
              { x: '周五', y: 1.77 },
              { x: '周六', y: 1.89 }
            ]
          },
          month: {
            num: 17.08,
            list: [
              { x: '01', y: 1.52 },
              { x: '02', y: 1.61 },
              { x: '03', y: 1.56 },
              { x: '04', y: 1.52 },
              { x: '05', y: 1.69 },
              { x: '06', y: 1.55 },
              { x: '07', y: 1.54 },
              { x: '08', y: 1.53 },
              { x: '09', y: 1.52 },
              { x: '10', y: 1.57 },
              { x: '11', y: 1.61 }
            ]
          },
          year: {
            num: 39.98,
            list: [{ x: '一月', y: 39.98 }]
          },
          total: 1022.9,
          list: []
        },
        {
          id: '0100103145',
          unit: '㎡',
          day: {
            num: 2.2,
            list: [
              { x: '00:00', y: 0.4 },
              { x: '02:00', y: 0.38 },
              { x: '04:00', y: 0.29 },
              { x: '06:00', y: 0.31 },
              { x: '08:00', y: 0.36 },
              { x: '10:00', y: 0.45 }
            ]
          },
          week: {
            num: 10.8,
            list: [
              { x: '周一', y: 1.61 },
              { x: '周二', y: 1.68 },
              { x: '周三', y: 1.71 },
              { x: '周四', y: 1.51 },
              { x: '周五', y: 1.62 },
              { x: '周六', y: 1.35 }
            ]
          },
          month: {
            num: 20.6,
            list: [
              { x: '01', y: 1.8 },
              { x: '02', y: 1.68 },
              { x: '03', y: 1.77 },
              { x: '04', y: 1.87 },
              { x: '05', y: 1.5 },
              { x: '06', y: 1.65 },
              { x: '07', y: 1.87 },
              { x: '08', y: 1.78 },
              { x: '09', y: 1.67 },
              { x: '10', y: 1.99 },
              { x: '11', y: 1.67 }
            ]
          },
          year: {
            num: 40.2,
            list: [{ x: '一月', y:  40.2}]
          },
          total: 1253.1,
          list: []
        },
        {
          id: '0100103128',
          unit: '㎡',
          day: {
            num: 2.65,
            list: [
              { x: '00:00', y: 0.5 },
              { x: '02:00', y: 0.44 },
              { x: '04:00', y: 0.51 },
              { x: '06:00', y: 0.47 },
              { x: '08:00', y: 0.51 },
              { x: '10:00', y: 0.52 }
            ]
          },
          week: {
            num: 10.0,
            list: [
              { x: '周一', y: 1.92 },
              { x: '周二', y: 1.87 },
              { x: '周三', y: 1.88 },
              { x: '周四', y: 1.98 },
              { x: '周五', y: 1.77 },
              { x: '周六', y: 1.89 }
            ]
          },
          month: {
            num: 18.01,
            list: [
              { x: '01', y: 1.52 },
              { x: '02', y: 1.61 },
              { x: '03', y: 1.56 },
              { x: '04', y: 1.52 },
              { x: '05', y: 1.69 },
              { x: '06', y: 1.55 },
              { x: '07', y: 1.54 },
              { x: '08', y: 1.53 },
              { x: '09', y: 1.52 },
              { x: '10', y: 1.57 },
              { x: '11', y: 1.61 }
            ]
          },
          year: {
            num: 40.3,
            list: [{ x: '一月', y: 40.3 }]
          },
          total: 1223.1,
          list: []
        },
        {
          id: '0100103136',
          unit: '㎡',
          day: {
            num: 3.41,
            list: [
              { x: '00:00', y: 0.6 },
              { x: '02:00', y: 0.7 },
              { x: '04:00', y: 0.55 },
              { x: '06:00', y: 0.61 },
              { x: '08:00', y: 0.59 },
              { x: '10:00', y: 0.67 }
            ]
          },
          week: {
            num: 11.41,
            list: [
              { x: '周一', y: 1.92 },
              { x: '周二', y: 1.87 },
              { x: '周三', y: 1.88 },
              { x: '周四', y: 1.98 },
              { x: '周五', y: 1.77 },
              { x: '周六', y: 1.89 }
            ]
          },
          month: {
            num: 17.08,
            list: [
              { x: '01', y: 1.52 },
              { x: '02', y: 1.61 },
              { x: '03', y: 1.56 },
              { x: '04', y: 1.52 },
              { x: '05', y: 1.69 },
              { x: '06', y: 1.55 },
              { x: '07', y: 1.54 },
              { x: '08', y: 1.53 },
              { x: '09', y: 1.52 },
              { x: '10', y: 1.57 },
              { x: '11', y: 1.61 }
            ]
          },
          year: {
            num: 39.98,
            list: [{ x: '一月', y: 39.98 }]
          },
          total: 1022.9,
          list: []
        },
        {
          id: '0100103145',
          unit: '㎡',
          day: {
            num: 2.2,
            list: [
              { x: '00:00', y: 0.4 },
              { x: '02:00', y: 0.38 },
              { x: '04:00', y: 0.29 },
              { x: '06:00', y: 0.31 },
              { x: '08:00', y: 0.36 },
              { x: '10:00', y: 0.45 }
            ]
          },
          week: {
            num: 10.8,
            list: [
              { x: '周一', y: 1.61 },
              { x: '周二', y: 1.68 },
              { x: '周三', y: 1.71 },
              { x: '周四', y: 1.51 },
              { x: '周五', y: 1.62 },
              { x: '周六', y: 1.35 }
            ]
          },
          month: {
            num: 20.6,
            list: [
              { x: '01', y: 1.8 },
              { x: '02', y: 1.68 },
              { x: '03', y: 1.77 },
              { x: '04', y: 1.87 },
              { x: '05', y: 1.5 },
              { x: '06', y: 1.65 },
              { x: '07', y: 1.87 },
              { x: '08', y: 1.78 },
              { x: '09', y: 1.67 },
              { x: '10', y: 1.99 },
              { x: '11', y: 1.67 }
            ]
          },
          year: {
            num: 40.2,
            list: [{ x: '一月', y:  40.2}]
          },
          total: 1142.3,
          list: []
        }
      ]
    }
  },
  watch: {
    tabsActiveName(val) {
      if(val == 2) {
        setTimeout(() => {
          this.electricityLineEchart(this.recordData.find((v) => v.id == this.recordId)[this.statisticalType].list)
        }, 200);
      }
    }
  },
  created() {},
  mounted() {},
  methods: {
    electricityLineEchart(arr) {
      const getchart = echarts.init(document.getElementById('analysisLineEcharts'))
      const option = {
        tooltip: {
          trigger: "axis",
        },
        grid: {
          top: '10%',
          left: '5%',
          right: '5%',
          bottom: '8%',
          containLabel: true
        },
        xAxis: [
          {
            type: 'category',
            boundaryGap: true,
            axisLine: {
              //坐标轴轴线相关设置。数学上的x轴
              show: true,
              lineStyle: {
                color: '#f9f9f9'
              }
            },
            axisLabel: {
              //坐标轴刻度标签的相关设置
              textStyle: {
                color: '#d1e6eb',
                margin: 15
              }
            },
            axisTick: {
              show: false
            },
            data: arr.map(v => v.x)
          }
        ],
        yAxis: [
          {
            type: 'value',
            min: 0,
            // max: 140,
            splitNumber: 3,
            splitLine: {
              show: true,
              lineStyle: {
                color: 'rgba(222, 222, 222, 0.3)'
              }
            },
            axisLine: {
              show: false
            },
            axisLabel: {
              textStyle: {
                color: '#fff'
              }
            },
            axisTick: {
              show: false
            }
          }
        ],
        series: [
          {
            name: '注册总量',
            type: 'line',
            // smooth: true, //是否平滑曲线显示
            // 			symbol:'circle',  // 默认是空心圆（中间是白色的），改成实心圆
            showAllSymbol: true,
            symbol: 'emptyCircle',
            symbolSize: 6,
            lineStyle: {
              normal: {
                color: '#28ffb3' // 线条颜色
              },
              borderColor: '#f0f'
            },
            label: {
              show: true,
              position: 'top',
              textStyle: {
                color: '#fff'
              }
            },
            itemStyle: {
              normal: {
                color: '#28ffb3'
              }
            },
            tooltip: {
              show: false
            },
            data: arr.map(v => v.y)
          }
        ]
      }
      getchart.clear()
      getchart.setOption(option)
      // 随着屏幕大小调节图表
      window.addEventListener('resize', () => {
        getchart.resize()
      })
    },
    // 时间类型切换
    dataTypeCommand(val) {
      this.statisticalType = val
      this.electricityLineEchart(this.recordData.find((v) => v.id == this.recordId)[val].list)
    },
    // 关闭弹窗
    closeDialog() {
      this.$emit('update:visible', !this.visible)
    }
  }
}
</script>
<style lang="scss" scoped>
.content {
  width: 100%;
  height: 300px;
  #analysisLineEcharts {
    position: relative;
    width: 100%;
    height: calc(100% - 23px);
    z-index: 2;
  }
  .record-list {
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    padding: 0px 16px 40px 20px;
    .list-item {
      font-size: 14px;
      display: flex;
    }
    .item-label {
      color: #b0e3fa;
    }
    .item-value {
      color: #fff;
      margin-left: 8px;
    }
  }
  ::v-deep .el-dropdown {
    padding: 0px 6px 7px 20px;
    .el-dropdown-link {
      font-size: 14px;
      font-weight: 500;
      color: #8bddf5;
      line-height: 16px;
      position: relative;
      cursor: pointer;
    }
  }
}
::v-deep .new-model-dialog {
  background: url('../../../../assets/images/minDialogBgd.png') no-repeat center / 100% 100%;
  margin-top: 60vh !important;
  box-shadow: none;
  .el-dialog__header {
    padding: 0px;
    .el-dialog__title {
      display: none;
    }
    .el-dialog__headerbtn {
      top: 16px;
      right: 16px;
      .el-dialog__close {
        color: #7cd0ff;
      }
    }
  }
  .el-dialog__body {
    padding: 10px;
  }
}
::v-deep .module-tabs {
  height: 100%;
  .el-tabs__header {
    width: 50%;
    .el-tabs__nav-wrap::after {
      display: none;
    }
    .el-tabs__item {
      padding: 0;
      color: rgba(255, 255, 255, 0.6) !important;
    }
    .is-active {
      color: #8bddf5 !important;
    }
    .el-tabs__active-bar {
      display: none;
    }
  }
  .el-tabs__content {
    height: calc(100% - 55px);
    .el-tab-pane {
      height: 100%;
    }
  }
}
</style>
