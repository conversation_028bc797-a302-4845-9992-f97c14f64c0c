<template>
  <div class="overview-chart-container">
    <div class="chart-box" style="height: calc(15%)">
      <ModuleCard title="监测设备统计" style="height: 100%">
        <div slot="title-right">
          <div class="chart-title-box">
            <div class="chart-title"></div>
            <div class="chart-btn">
              <div class="chart-icon">
                <img
                  src="../../../assets/images/order_more.png"
                  class="order-more"
                  @click="allTableChange"
                />
              </div>
            </div>
          </div>
        </div>
        <div slot="content" style="height: 100%">
          <div class="statistics">
            <div
              v-for="(item, index) in statisticsData"
              :key="index"
              class="items"
            >
              <span class="name">{{ item.name }}</span>
              <span class="count">{{ item.count }}</span>
            </div>
          </div>
        </div>
      </ModuleCard>
    </div>
    <div class="chart-box" style="height: calc(30%)">
      <ModuleCard title="预警状态" style="height: 100%">
        <div slot="title-right">
          <div class="chart-title-box">
            <div class="chart-title">
              <span class="span"></span>
            </div>
            <div class="chart-btn">
              <div class="dropdown-div">
                <el-dropdown
                  trigger="click"
                  @command="(val) => (tagCurrent = val)"
                >
                  <span class="el-dropdown-link">
                    设备类型 <i class="el-icon-arrow-down"></i>
                  </span>
                  <el-dropdown-menu slot="dropdown">
                    <el-dropdown-item
                      :command="'1'"
                      :class="{ isBjxl: tagCurrent == '1' }"
                      >设备类型</el-dropdown-item
                    >
                  </el-dropdown-menu>
                </el-dropdown>
                <el-dropdown
                  trigger="click"
                  @command="(val) => (tagCurrent = val)"
                >
                  <span class="el-dropdown-link">
                    建筑 <i class="el-icon-arrow-down"></i>
                  </span>
                  <el-dropdown-menu slot="dropdown">
                    <el-dropdown-item
                      :command="'1'"
                      :class="{ isBjxl: tagCurrent == '1' }"
                      >建筑</el-dropdown-item
                    >
                  </el-dropdown-menu>
                </el-dropdown>
              </div>
              <div class="chart-icon">
                <img
                  src="../../../assets/images/order_more.png"
                  class="order-more"
                  @click="allTableChange"
                />
              </div>
            </div>
          </div>
        </div>
        <div slot="content" style="height: 100%">
          <div class="chart-div" style="height: calc(100% - 10px)">
            <div id="bar" style="width: 100%; height: 100%"></div>
          </div>
        </div>
      </ModuleCard>
    </div>
    <div class="chart-box" style="height: calc(30% - 32px)">
      <ModuleCard title="设备列表" style="height: 100%">
        <div slot="title-right">
          <div class="chart-title-box">
            <div class="chart-title"></div>
            <div class="chart-btn">
              <div class="dropdown-div">
                <el-dropdown
                  trigger="click"
                  @command="(val) => (tagCurrent = val)"
                >
                  <span class="el-dropdown-link">
                    全部科室 <i class="el-icon-arrow-down"></i>
                  </span>
                  <el-dropdown-menu slot="dropdown">
                    <el-dropdown-item
                      :command="'1'"
                      :class="{ isBjxl: tagCurrent == '1' }"
                      >全部科室</el-dropdown-item
                    >
                  </el-dropdown-menu>
                </el-dropdown>
              </div>
              <div class="chart-icon">
                <img
                  src="../../../assets/images/order_more.png"
                  class="order-more"
                  @click="allTableChange"
                />
              </div>
            </div>
          </div>
        </div>
        <div slot="content" style="height: 100%">
          <div
            class="chart-div"
            style="height: calc(100% - 10px)"
          >
            <el-table
              v-el-table-infinite-scroll="tableLoadMore"
              v-scrollHideTooltip
              v-loading="deviceTableLoading"
              class="table-center-transfer"
              :data="tableData"
              height="100%"
              :cell-style="{
                padding: ' 8px',
                backgroundColor: 'transparent',
                border: 'none',
                padding: '3px',
              }"
              :header-cell-style="{
                background: '#8591CE26!important',
                color: '#8BDDF5FF',
                padding: '4px 8px',
                fontWeight: 'bold',
              }"
              style="width: 100%"
              element-loading-background="rgba(0, 0, 0, 0.2)"
            >
              <el-table-column
                prop="imsName"
                show-overflow-tooltip
                label="设备名称"
              ></el-table-column>
              <el-table-column
                prop="assetsTypeName"
                show-overflow-tooltip
                label="设备类型"
                width="100"
              ></el-table-column>
              <el-table-column
                prop="alarmStatus"
                show-overflow-tooltip
                label="设备状态"
                width="120"
              >
                <span slot-scope="scope" @click="viewDevice(scope.row)">
                  <div
                    class="table-icon"
                    :style="{
                      color:
                        scope.row.alarmStatus == '开机'
                          ? '#61E29D'
                          : scope.row.alarmStatus == '开机'
                          ? '#FF2D55'
                          : '',
                    }"
                  >
                    <span> {{ scope.row.alarmStatus }}</span>
                  </div>
                </span>
              </el-table-column>
            </el-table>
          </div>
        </div>
      </ModuleCard>
    </div>
    <div class="chart-box" style="height: calc(30% - 32px)">
      <ModuleCard title="预警记录" style="height: 100%">
        <div slot="title-right">
          <div class="chart-title-box">
            <div class="chart-title"></div>
            <div class="chart-btn">
              <div class="dropdown-div">
                <el-dropdown
                  trigger="click"
                  @command="(val) => (tagCurrent = val)"
                >
                  <span class="el-dropdown-link">
                    今天 <i class="el-icon-arrow-down"></i>
                  </span>
                  <el-dropdown-menu slot="dropdown">
                    <el-dropdown-item
                      :command="'1'"
                      :class="{ isBjxl: tagCurrent == '1' }"
                      >今天</el-dropdown-item
                    >
                  </el-dropdown-menu>
                </el-dropdown>
              </div>
              <div class="chart-icon">
                <img
                  src="../../../assets/images/order_more.png"
                  class="order-more"
                  @click="allTableChange"
                />
              </div>
            </div>
          </div>
        </div>
        <div slot="content" style="height: 100%">
          <div
            class="chart-div"
            style="height: calc(100% - 10px)"
          >
            <el-table
              v-el-table-infinite-scroll="tableLoadMore"
              v-scrollHideTooltip
              v-loading="deviceTableLoading"
              class="table-center-transfer"
              :data="tableData1"
              height="100%"
              :cell-style="{
                padding: ' 4px',
                backgroundColor: 'transparent',
                border: 'none',
                padding: '3px',
              }"
              :header-cell-style="{
                background: '#8591CE26!important',
                color: '#8BDDF5FF',
                padding: '4px 4px',
                fontWeight: 'bold',
              }"
              style="width: 100%"
              element-loading-background="rgba(0, 0, 0, 0.2)"
            >
              <el-table-column
                prop="imsName"
                show-overflow-tooltip
                label="设备名称"
                width="85"
              ></el-table-column>
              <el-table-column
                prop="date"
                show-overflow-tooltip
                label="报警时间"
                width="90"
              ></el-table-column>
              <el-table-column
                prop="val"
                show-overflow-tooltip
                label="报警参数值"
                width="120"
              ></el-table-column>
              <el-table-column
                prop="scope"
                show-overflow-tooltip
                label="正常范围"
                width="100"
              ></el-table-column>
            </el-table>
          </div>
        </div>
      </ModuleCard>
    </div>
    <dialogFrame
      :visible="visible"
      :breadcrumb="breadcrumb"
      :title="title"
      @back="back"
      @update:visible="closeDialogFrame"
    >
      <component
        :is="activeComponent"
        @openDetailComponent="openDetailComponent"
      ></component>
    </dialogFrame>
  </div>
</template>
<script>
import * as echarts from "echarts";
export default {
  components: {
    dialogFrame: () => import("@/components/common/DialogFrame"),
    monitorListIndex: () => import("@/views/monitorModular/listIndex.vue"),
    monitorDetails: () =>
      import("@/views/monitorModular/monitorDetails/details.vue"),
  },
  data() {
    return {
      titleIdx: 0,
      titleList: [
        { label: "运行状态", value: "runStatus" },
        { label: "使用状态", value: "useStatus" },
      ],
      deviceTableLoading: false,
      pagination: {
        total: 0,
        pageSize: 15,
        pageNo: 1,
      },
      tableData: [
        { imsName: "设备", assetsTypeName: "MR", alarmStatus: "离线" },
        { imsName: "设备", assetsTypeName: "CT", alarmStatus: "离线" },
        { imsName: "设备", assetsTypeName: "麻醉机", alarmStatus: "开机" },
        { imsName: "设备", assetsTypeName: "输注泵", alarmStatus: "关机" },
        { imsName: "设备", assetsTypeName: "超声", alarmStatus: "待机" },
        { imsName: "设备", assetsTypeName: "显微镜", alarmStatus: "工作" },
        { imsName: "设备", assetsTypeName: "腹腔镜", alarmStatus: "开机" },
      ],
      tableData1: [
        {
          imsName: "设备",
          date: "2024/10/25 12:14",
          val: "设备件温度(34度)",
          scope: "200~423",
        },
        {
          imsName: "设备",
          date: "2024/10/25 12:14",
          val: "氦压机入水温度(23度)",
          scope: "200~423",
        },
        {
          imsName: "设备",
          date: "2024/10/25 12:14",
          val: "氦压机入水温度(23度)",
          scope: "200~423",
        },
        {
          imsName: "设备",
          date: "2024/10/25 12:14",
          val: "氦压机入水温度(23度)",
          scope: "200~423",
        },
        {
          imsName: "设备",
          date: "2024/10/25 12:14",
          val: "氦压机入水温度(23度)",
          scope: "200~423",
        },
        {
          imsName: "设备",
          date: "2024/10/25 12:14",
          val: "氦压机入水温度(23度)",
          scope: "200~423",
        },
        {
          imsName: "设备",
          date: "2024/10/25 12:14",
          val: "氦压机入水温度(23度)",
          scope: "200~423",
        },
      ],
      tagCurrent: "",
      visible: false,
      breadcrumb: [{ label: "监测设备列表", name: "monitorListIndex" }],
      activeComponent: "monitorListIndex",
      title: "监测设备列表",
      statisticsData: [
        {
          name: "设备总数",
          count: 34123,
        },
        {
          name: "正常设备",
          count: 34123,
        },
        {
          name: "异常设备",
          count: 34123,
        },
        {
          name: "离线设备",
          count: 34123,
        },
      ],
    };
  },
  mounted() {
    this.$nextTick(() => {
      // this.getAssetStatusStatistics()
      this.getAssetSortStatistics();
    });
  },
  methods: {
    allTableChange() {
      this.visible = true;
    },
    /** 打开组件 */
    openDetailComponent(params) {
      this.activeComponent = params;
      let obj = {
        monitorDetails: { label: "设备详情", name: "monitorDetails" },
      };
      this.breadcrumb.push(obj[params]);
      this.title = "";
    },
    /** 组件返回上一级 */
    back(name) {
      this.activeComponent = name;
      let arr = [];
      for (let i = 0; i < this.breadcrumb.length - 1; i++) {
        arr.push(this.breadcrumb[i]);
        if (this.breadcrumb[i].name == name) {
          break;
        }
      }
      this.breadcrumb = arr;
      if (this.breadcrumb.length) {
        this.title = this.breadcrumb.find((item) => item.name === name).label;
      }
    },
    /** 关闭弹窗 */
    closeDialogFrame() {
      this.visible = false;
      this.activeComponent = "monitorListIndex";
      this.title = "监测设备列表";
      this.breadcrumb = [{ label: "监测设备列表", name: "monitorListIndex" }];
    },
    tableLoadMore() {
      if (
        this.pagination.total >
        this.pagination.pageNo * this.pagination.pageSize
      ) {
        this.pagination.pageNo++;
        if (this.sortType == "1") {
          //   this.getAssetsTypeList({
          //     entityTypeId: this.sendCheckData.entityTypeId,
          //   });
        } else {
          //   this.getListGroupData({ spaceId: this.sendCheckData.spaceId });
        }
      }
    },
    /** 初始化监测设备统计 */
    getAssetStatusStatistics() {
      let arr = [
        {
          count: 150,
          name: "关机",
          percentage: 30,
        },
        {
          count: 69,
          name: "待机",
          percentage: 0,
        },
        {
          count: 50,
          name: "工作",
          percentage: 0,
        },
        {
          count: 10,
          name: "离线",
          percentage: 0,
        },
      ];
      // 基于准备好的dom，初始化echarts实例
      let getchart = echarts.init(document.getElementById("pie"));
      const data = [];
      var color = ["#FF2D55", "#61E29D ", "#FFAD65", "#e38e6f", "#f2d988"];
      const xdata = Array.from(arr, ({ name }) => name);
      for (var i = 0; i < arr.length; i++) {
        const randomRgbColor = this.$tools.randomRgbColor("array");
        data.push({
          name: arr[i].name,
          value: arr[i].count,
          percentage: arr[i].percentage,
          itemStyle: {
            normal: {
              borderWidth: 1,
              shadowBlur: 200,
              color: color[i] ?? randomRgbColor[0],
              label: {
                normal: { show: true },
              },
            },
          },
        });
      }
      const option = {
        backgroundColor: "",
        title: {
          // text: "{name|" + '资产总数' + "}\n{val|" + 269 + "}",
          text: "{val|" + 269 + "}\n{name|" + "资产总数" + "}",
          top: "35%",
          left: "19%",
          textStyle: {
            rich: {
              name: {
                fontSize: 14,
                fontWeight: "normal",
                color: "#ffffff",
                padding: [5, 0],
              },
              val: {
                fontSize: 18,
                fontWeight: "bold",
                color: "#ffffff",
                padding: [0, 10],
              },
            },
          },
        },
        legend: {
          // 取消legend和echarts的联动
          selectedMode: false,
          icon: "circle",
          orient: "vartical",
          x: "left",
          top: "center",
          left: "50%",
          bottom: "0%",
          data: xdata,
          itemWidth: 8,
          itemHeight: 8,
          itemGap: 12,
          textStyle: {
            color: "#B3C2DD", //  字体颜色
          },
          formatter: function (name) {
            var oa = option.series[0].data;
            for (var i = 0; i < option.series[0].data.length; i++) {
              if (name === oa[i].name) {
                return (
                  " " +
                  name +
                  "    " +
                  oa[i].value +
                  "    " +
                  oa[i].percentage +
                  "%"
                );
              }
            }
          },
        },
        series: [
          {
            name: "",
            type: "pie",
            clockWise: false,
            center: ["27%", "50%"],
            radius: ["58%", "70%"],
            label: {
              normal: {
                show: false,
                position: "center",
                formatter: "{value|{c}}\n{label|{b}}",
                rich: {
                  value: {
                    padding: 5,
                    align: "center",
                    verticalAlign: "middle",
                    fontSize: 20,
                  },
                  label: {
                    align: "center",
                    verticalAlign: "middle",
                    fontSize: 14,
                  },
                },
              },
            },
            // hoverAnimation: false,
            data: data,
          },
        ],
      };
      getchart.clear();
      getchart.setOption(option);
      // 随着屏幕大小调节图表
      window.addEventListener("resize", () => {
        getchart.resize();
      });
    },
    /** 运行状态 使用状态 切换事件  */
    handleTitleClick(index) {
      this.titleIdx = index;
    },
    /** 初始化运行状态 使用状态 */
    getAssetSortStatistics() {
      var chartDom = document.getElementById("bar");
      var myChart = echarts.init(chartDom);
      var option;

      option = {
        legend: {
          textStyle: {
            color: "#fff",
            fontSize: 12,
          },
          bottom: "0%",
          itemWidth: 8,
          itemHeight: 8,
          itemGap: 5,
          selectedMode: false,
        },
        tooltip: {
          trigger: "axis",
          axisPointer: {
            type: "shadow",
          },
          itemSize: 12,
          itemGap: 5,
        },
        grid: {
          top: "2%",
          left: "5%",
          right: "10%",
          bottom: "10%",
          containLabel: true,
        },
        xAxis: {
          type: "value",
          interval: "auto",
          axisLabel: {
            color: "#fff",
          },
          axisLine: {
            show: false,
          },
          splitLine: {
            show: false,
          },
        },
        yAxis: [
          {
            type: "category",
            data: [
              "显微镜",
              "宫腔镜系统",
              "腹腔镜系统",
              "核磁机",
              "CT",
              "麻醉机",
            ],
            axisLine: {
              show: true,
            },
            splitLine: {
              show: false,
            },
            axisLabel: {
              color: "#fff",
            },
          },
        ],
        series: [
          {
            name: "关机",
            type: "bar",
            stack: "total",
            label: {
              show: false,
            },
            emphasis: {
              focus: "series",
            },
            barWidth: 15,
            data: [5, 7, 9, 10, 15, 20],
          },
          {
            name: "待机",
            type: "bar",
            stack: "total",
            label: {
              show: false,
            },
            emphasis: {
              focus: "series",
            },
            barWidth: 15,
            data: [5, 7, 9, 10, 15, 20],
          },
          {
            name: "工作",
            type: "bar",
            stack: "total",
            label: {
              show: false,
            },
            emphasis: {
              focus: "series",
            },
            barWidth: 15,
            data: [5, 7, 9, 10, 15, 20],
          },
          {
            name: "离线",
            type: "bar",
            stack: "total",
            label: {
              show: false,
            },
            emphasis: {
              focus: "series",
            },
            barWidth: 15,
            data: [5, 7, 9, 10, 15, 20],
          },
          {
            name: "",
            type: "bar",
            barGap: "-100%",
            itemStyle: {
              normal: {
                color: "transparent",
              },
            },
            barWidth: 15,
            showBackground: true,
            backgroundStyle: {
              color: "rgba(180, 180, 180, 0.2)",
            },
            label: {
              show: true,
              position: "right",
              formatter: function (params) {
                let arr = [35, 49, 63, 70, 105, 140];
                return `  ${arr[params.dataIndex]}`;
              },
              fontSize: 12,
              color: "#DADEE1",
            },
            emphasis: {
              focus: "series",
            },
            data: [175, 175, 175, 175, 175, 175],
          },
        ],
      };

      myChart.resize();
      myChart.clear();
      option && myChart.setOption(option);
      window.addEventListener("resize", () => {
        myChart.resize();
      });
    },
  },
};
</script>
<style lang="scss" scoped>
%flex-styles {
  display: flex;
  align-items: center;
}
.overview-chart-container {
  width: 100%;
  height: 100%;
  .chart-box {
    .chart-title-box {
      @extend %flex-styles;
      justify-content: space-between;
      .span {
        margin-right: 1rem;
        cursor: pointer;
      }
      .active {
        color: #78a3dc;
      }
      .chart-btn {
        @extend %flex-styles;
        .dropdown-div {
          margin-right: 0.5rem;
          .el-dropdown-link {
            color: #fff;
          }
        }
        .chart-icon {
          cursor: pointer;
          @extend %flex-styles;
          .order-more {
            width: 24px;
            height: 24px;
            margin-right: 0 !important;
          }
        }
      }
    }
    .statistics {
      display: flex;
      justify-content: space-between;
      align-items: center;
      height: calc(100%);
      .items {
        display: flex;
        flex-direction: column;
        height: 100%;
        align-items: center;
        justify-content: space-evenly;
        .count {
          color: #ffca64;
          font-size: 16px;
          font-weight: bold;
        }
      }
    }
  }
}
.table-icon {
  display: flex;
  align-items: center;
}
.table-icon img {
  width: 16px;
  margin-right: 3px;
}
</style>
  