<template>
  <div class="bg-title">
    <div class="bg-title-content">
      <slot></slot>
      <div class="title">
        <slot name="title"></slot>
      </div>
      <div class="rightBtn">
        <slot name="right"></slot>
        <div v-if="showMore" class="more" @click="moreClick">
          <i class="el-icon-arrow-right"></i>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
export default {
  props: {
    showMore: {
      type: Boolean,
      default: false
    }
  },
  methods: {
    moreClick () {
      this.$emit('moreClick')
    }
  }
}
</script>
<style lang="scss" scoped>
.bg-title {
  // margin-top: 5px;
  width: 100%;
  height: 34px;
  line-height: 44px;
  font-size: 16px;
  font-weight: 500;
  color: #dceaff;
  display: flex;
  align-items: center;

  background: linear-gradient(
    90deg,
    rgba(10, 132, 255, 0.4) 0%,
    rgba(10, 132, 255, 0) 100%
  );
  &::before {
    content: "";
    display: inline-block;
    width: 18px;
    height: 22px;
    margin: 0 8px;
    background: url("~@/assets/images/bg-title.png") no-repeat;
    background-size: 100% 100%;
  }
  .bg-title-content {
    display: flex;
    justify-content: space-between;
    width: 100%;
    .title {
      font-weight: 500;
      font-size: 16px;
      color: #dceaff;
      text-shadow: 0px 0px 9px #158eff;
    }
    .rightBtn {
      display: flex;
      justify-content: flex-end;
      .more {
        min-width: 30px;
        text-align: center;
        cursor: pointer;
        i{
         color: #8BDDF5;
        }
      }
    }
  }
}
</style>
