<template>
  <el-dialog class="component DialogTalkDetail" v-if="visible" center v-dialogDrag :close-on-click-modal="false"
    :modal="false" :show-close="false" title="通话记录" width="768px" custom-class="DialogTalkDetail__dialog"
    :visible="visible" :before-close="closeDialog">
    <div class="DialogTalkDetail__head">
      <div class="DialogTalkDetail__close" @click="closeDialog"></div>
    </div>
    <div class="DialogTalkDetail__content">
      <div class="DialogTalkDetail__info">
        <ul class="description__content">
          <li style="margin-top: 0;">
            <div>呼叫类型</div>
            <div>{{ detail.communicateTypeName }}</div>
          </li>
          <li style="margin-top: 0;">
            <div>通话时长</div>
            <div>{{ detail.callDuration }}</div>
          </li>
          <li>
            <div>通话开始时间</div>
            <div>{{ detail.operationStartTime }}</div>
          </li>
          <li>
            <div>通话结束时间</div>
            <div>{{ detail.operationEndTime }}</div>
          </li>
          <li class="full-row">
            <div style="padding-top: 3px;">被叫人</div>
            <div class="DialogTalkDetail__attendees">
              <div class="DialogTalkDetail__attendee" v-for="item of attendeeList" :key="item.code">
                <span>{{ item.code }}</span>
                <span class="tag" :class="'tag--' + item.status">{{ item.statusText }}</span>
              </div>
            </div>
          </li>
          <li v-if="mediaType === 'audio'" class="full-row" style="margin-top: 14px;">
            <div style="padding-top: 6px;">通话录音</div>
            <div>
              <audio ref="audioRef" class="DialogTalkDetail__audio" src="http://123.com/a/c.mp3" controls></audio>
            </div>
          </li>
          <li v-if="mediaType === 'video'" class="full-row" style="margin-top: 14px;">
            <div style="padding-top: 6px;">通话视频</div>
            <div>
              <video ref="videoRef" class="DialogTalkDetail__video" src="http://123.com/a/c.mp4" controls></video>
            </div>
          </li>
        </ul>
      </div>
      <div class="DialogTalkDetail__action">
        <el-button class="sino-button-sure" @click="closeDialog">关闭</el-button>
      </div>
    </div>
  </el-dialog>
</template>
<script>


export default {
  name: 'DialogTalkDetail',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    detail: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {

    }
  },
  computed: {
    attendeeList: function () {
      const result = this.detail?.callInfoList || []
      return result.map(x => ({
        code: x.callPhone,
        statusText: x.callStatus,
        status: x.callStatus === '未接通' ? 0 : 1
      }))
    },
    mediaType: function () {
      let result = '';
      if (['2', '1'].includes(this.detail.communicateType)) {
        result = 'audio'
      } else if (this.detail.communicateType === '7') {
        result = 'video'
      }
      return result;
    }
  },
  watch: {
    visible: {
      handler: function (val) {
        if (!val) {
          this.pauseMedia()
        }
      },
      immediate: true
    }
  },
  methods: {
    // 关闭弹窗
    closeDialog() {
      this.$emit('update:visible', false)
    },
    pauseMedia() {
      let mediaPlayer;
      if (this.mediaType === 'audio' && this.$refs.audioRef) {
        mediaPlayer = this.$refs.audioRef;
      } else if (this.mediaType === 'video' && this.$refs.videoRef) {
        mediaPlayer = this.$refs.videoRef;
      }
      if (mediaPlayer && mediaPlayer.played) {
        video.pause()
      }
    }
  }
}
</script>
<style lang="scss" scoped>
.component.DialogTalkDetail {
  background: rgba(0, 0, 0, 0.6);

  ::v-deep .DialogTalkDetail__dialog.el-dialog {
    background: url('~@/assets/images/qhdsys/768×498.png') no-repeat center center / 100% 100%;
    height: 498px;
    overflow: hidden;
    margin-top: 25vh !important;

    .el-dialog__header {
      padding: 10px 20px 10px;
      text-align: center;

      .el-dialog__title {
        height: 20px;
        font-size: 18px;
        font-family: Source Han Sans CN-Regular, Source Han Sans CN;
        font-weight: 400;
        color: #cbdeed;
        line-height: 18px;
      }
    }

    .el-dialog__body {
      height: calc(100% - 40px);
      color: #fff;
    }

    .el-dialog__footer {
      padding-top: 0;
      padding-bottom: 40px;
      padding-right: 40px;
    }
  }

  .description {
    &__content {
      display: flex;
      flex-flow: row wrap;

      >li {
        flex-basis: 50%;
        display: flex;
        flex-flow: row nowrap;
        margin-top: 24px;

        &.full-row {
          flex-basis: 100%;
        }

        >div:first-of-type {
          color: #B0E3FA;
          width: 100px;
          text-align: right;

          &::after {
            content: '：';
          }

        }

        >div:last-child {
          width: 0;
          flex: 1;
          overflow: hidden;
          white-space: nowrap;
          text-overflow: ellipsis;
        }
      }
    }
  }


  .DialogTalkDetail {
    &__head {
      position: absolute;
      top: 18px;
      right: 52px;
      display: flex;
    }

    &__close {
      width: 36px;
      height: 36px;
      background: url('@/assets/images/qhdsys/bg-icon-gb.png') no-repeat;
      cursor: pointer;
    }

    &__content {
      height: 100%;
      overflow: auto;
      display: flex;
      flex-flow: column nowrap;
      padding: 20px 30px;
    }

    &__info {
      flex: 1;
    }

    &__action {
      margin-top: 30px;
      text-align: right;
    }

    &__attendees {
      display: flex;
      flex-flow: row wrap;
    }

    &__attendee {
      min-width: 100px;
      margin: 0 20px 10px 0;

      .tag {
        display: inline-block;
        margin-left: 8px;
        border-radius: 4px 4px 4px 4px;
        padding: 3px 8px;

        &--0 {
          color: #FF2D55;
          background: rgba(255, 45, 85, 0.2);
        }

        &--1 {
          color: #61E29D;
          background: rgba(97, 226, 157, 0.2);
        }
      }
    }

    &__audio {
      height: 28px;
      width: 400px;
    }

    &__video {
      width: 100%;
    }
  }
}
</style>
