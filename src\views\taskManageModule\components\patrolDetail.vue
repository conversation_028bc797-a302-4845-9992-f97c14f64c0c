<template>
  <div class="list-index-view">
    <div class="view-content">
      <el-form ref="form" :model="detailInfo" label-width="120px">
        <div class="baseInfo">
          <div class="toptip">
            <span class="green_line"></span>
            基本信息
          </div>
          <el-row :gutter="24">
            <el-col :span="8">
              <el-form-item label="作业名称：">
                <span>{{ detailInfo.taskPoint?.taskName || '---' }}</span>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="周期类型：">
                <span>{{ dataType[detailInfo.result?.cycleType] || '---' }}</span>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="巡检日期：">
                <span>{{ detailInfo.taskPoint?.actualExecutionTime || '---' }}</span>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="巡检部门：">
                <span>{{ detailInfo.taskPoint?.actualExecutionDeptName || '---' }}</span>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="巡检人员：">
                <span>{{ detailInfo.taskPoint?.actualExecutionUserName || '---' }}</span>
              </el-form-item>
            </el-col>
          </el-row>
        </div>
        <div class="assetsInfo">
          <div class="toptip">
            <span class="green_line"></span>
            巡检内容
          </div>
          <el-table
            :data="tableData"
            :cell-style="$tools.setCell(3)"
            :header-cell-style="$tools.setHeaderCell(3)"
            style="width: 100%"
            height="calc(100% - 50px)"
          >
            <el-table-column label="序号" type="index" width="100" />
            <el-table-column prop="content" show-overflow-tooltip label="名称"></el-table-column>
            <el-table-column prop="detailName" show-overflow-tooltip label="巡查内容"></el-table-column>
            <el-table-column prop="contentStandard" show-overflow-tooltip label="巡查结果"></el-table-column>
            <!-- <el-table-column prop="model" show-overflow-tooltip label="实际巡检日期"></el-table-column> -->
            <!-- <el-table-column prop="statusName" show-overflow-tooltip label="资产状态">
              <template slot-scope="scope">
                <div class="status-box">
                  <img class="table-icon" :src='icon_5' />
                  <span style="color:#61E29D">{{scope.row.statusName}}</span>
                </div>
              </template>
            </el-table-column> -->
            <!-- <el-table-column prop="dept" show-overflow-tooltip label="巡察部门"></el-table-column>
            <el-table-column prop="person" show-overflow-tooltip label="巡查人"></el-table-column>
            <el-table-column prop="state" show-overflow-tooltip label="定位状态"></el-table-column> -->
          </el-table>
        </div>
      </el-form>
    </div>
  </div>
</template>
<script>
import icon_5 from '@/assets/images/icon-5.png'
import { constructionPatrolDetail } from '@/utils/spaceManage'
export default {
  name: 'patrolDetail',
  components: {
  },
  props: {
    currentItem: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      icon_5,
      detailInfo: {},
      tableData: [],
      dataType: {
        8: '单次',
        0: '每周',
        1: '半月',
        2: '每月',
        3: '季度',
        4: '半年',
        5: '全年',
        6: '每日',
        7: '自定义周期类型'
      }
    }
  },
  mounted() {
    this.getDataList()
  },
  methods: {
    getDataList() {
      constructionPatrolDetail({recordId: this.currentItem.id}).then(res => {
        if (res.data.code == 200) {
          (res.data.data.project?.projectdetailsReleaseList ?? []).forEach(item => {
            if (res.data.data.project.templateType == 1) {
              item.name = item.content
            } else {
              item.name = item.standardRequirements
            }
          })
          this.detailInfo = res.data.data
          this.tableData = res.data.data.project?.projectdetailsReleaseList ?? []
        }
      })
    }
    // handleClose(done) {
    //   done()
    // }
  }
}
</script>
  <style lang="scss" scoped>
.list-index-view {
  position: relative;
  height: 100%;
  padding: 0px 40px;
}
.view-content {
  height: calc(100% - 4rem);
  .toptip {
    box-sizing: border-box;
    height: 50px;
    width: 100%;
    line-height: 50px;
    display: flex;
    font-size: 16px;
    align-items: center;
  }
  .green_line {
    display: inline-block;
    width: 6px;
    height: 16px;
    border-radius: 2px;
    background: #1574a4;
    margin-right: 10px;
    vertical-align: middle;
  }
  .table-icon {
    width: 16px;
    margin-right: 3px;
  }
  .status-box {
    display: flex;
    align-items: center;
  }
  .table-view {
    height: 100%;
  }
}
::v-deep .el-form {
  height: 100%;
  display: flex;
  flex-direction: column;
  .el-form-item__content {
    font-size: 0.875rem;
  }.el-form-item {
    margin-bottom: 8px !important;
  }
  .el-form-item__label {
    color: #ffffff !important;
  }
  .assetsInfo {
    flex: 1;
  }
}
</style>

