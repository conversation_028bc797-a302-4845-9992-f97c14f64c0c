<template>
  <div class="pipelineComponent">
    <div class="top_title"><span></span>管线图例</div>
    <div class="pipeline-box">
      <el-checkbox v-if="pipelineData.length" style="padding-left: 26px; margin: 5px 0 10px 0" :indeterminate="isIndeterminate" v-model="checkAllPipeline" @change="handleCheckAllChange"
        ><span style="color: #ffe3a6">全部</span></el-checkbox
      >
      <div v-else class="center-center">暂无管线</div>
      <el-checkbox-group v-model="checkedPipeline" @change="handleCheckedPipelineChange">
        <el-checkbox :style="{ backgroundColor: item.colour || 'rgb(255, 255, 255)' }" v-for="item in pipelineData" :label="item.category" :value="item.category" :key="item.category"
          >{{ item.categoryName }}</el-checkbox
        >
      </el-checkbox-group>
    </div>
    <!-- <div class="conduit-box">
      <div class="conduit-box-title">管道流量 <el-switch v-model="pipeFlow" active-color="#FFE3A6" inactive-color="#1474A4"> </el-switch></div>
    </div> -->
  </div>
</template>

<script>
import { getCategoryList } from '@/utils/spaceManage'
// import { pipelineData, pipelineParams } from '@/assets/common/dict.js'
export default {
  name: 'pipelineComponent',
  components: {},
  props: {
    roomData: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      isIndeterminate: true,
      checkAllPipeline: false,
      checkedPipeline: [],
      pipelineData: [],
      pipelineParams: {},
      pipeFlow: false
    }
  },
  mounted() {
    this.getCategoryList()
  },
  methods: {
    // 获取管线分类列表
    getCategoryList() {
      const params = {
        categoryLevel: 1,
        spaceModelCode: this.roomData.modelCode
        // spaceModelCode: 'BJSJTYY'
      }
      getCategoryList(params).then((res) => {
        const data = res.data
        if (data.code === 200 && data.data.length) {
          this.pipelineData = data.data
          const allIDs = Array.from(this.pipelineData, ({ category }) => category)
          this.checkedPipeline = allIDs
          this.setCheckDataToWpf()
        } else {
          this.pipelineData = []
        }
      })
    },
    handleCheckAllChange(val) {
      this.checkedPipeline = val ? Array.from(this.pipelineData, ({ category }) => category) : []
      this.isIndeterminate = false
      this.setCheckDataToWpf()
    },
    handleCheckedPipelineChange(value) {
      const checkedCount = value.length
      this.checkAllPipeline = checkedCount === this.pipelineData.length
      this.isIndeterminate = checkedCount > 0 && checkedCount < this.pipelineData.length
      this.setCheckDataToWpf()
    },
    // 传输数据给wpf
    setCheckDataToWpf() {
      const params = []
      const hospitalCode = this.$store.state.loginInfo.user.hospitalCode
      const areaCode = this.roomData.modelCode.replace(hospitalCode, '')
      const Area = areaCode.slice(0, 2)
      const Building = areaCode.slice(2, 5)
      const Floor = areaCode.slice(5, 7)
      this.pipelineData.forEach((item) => {
        let Visibility = 0
        if (this.checkedPipeline.includes(item.category)) {
          Visibility = 1
        }
        params.push({
          FloorCode: areaCode,
          Area,
          Building,
          Floor,
          ID: areaCode + '_' + item.category,
          CategoryCode: item.category,
          Visibility: Visibility
        })
      })
      try {
        window.chrome.webview.hostObjects.sync.bridge.GetPipelineSelectData(JSON.stringify(params))
      } catch (error) {}
    }
  }
}
</script>

<style lang="scss" scoped>
.pipelineComponent {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  .top_title {
    height: 30px;
    line-height: 30px;
    font-size: 14px;
    font-family: PingFangSC-Medium, PingFang SC;
    color: #ffffff;
    span {
      display: inline-block;
      width: 4px;
      height: 4px;
      background: #ffe3a6;
      vertical-align: middle;
      margin-right: 7px;
    }
  }
  .pipeline-box {
    height: calc(100% - 30px);
    padding: 8px 15px;
    overflow-y: auto;
    box-sizing: border-box;
    background-color: rgba(1, 11, 59, 0.5);
    ::v-deep .el-checkbox-group {
      display: flex;
      flex-wrap: wrap;
      justify-content: space-around;
      .el-checkbox {
        display: block;
        width: 40%;
        margin: 3px 0;
        padding: 6px 5px 6px 10px;
        box-sizing: border-box;
        border-radius: 6px;
        .el-checkbox__label {
          color: #061244;
          font-family: PingFangSC-Medium, PingFang SC;
          font-size: 14px;
          // overflow: hidden;
          white-space: nowrap;
          text-overflow: ellipsis;
        }
        .el-checkbox__input.is-checked span.el-checkbox__inner {
          background: url('~@/assets/images/sys/checkbox-select-deep.png') no-repeat !important;
        }
      }
    }
  }
  .conduit-box {
    height: calc(50% - 25px);
    padding: 8px 30px;
    overflow-y: auto;
    box-sizing: border-box;
    background-color: rgba(1, 11, 59, 0.5);
    font-size: 14px;
    font-family: PingFang-SC-Medium, PingFang-SC;
    font-weight: 500;
    color: #ffffff;
    ::v-deep .conduit-box-title {
      height: 30px;
      line-height: 30px;
      .el-switch {
        margin-left: 30px;
        .el-switch__core {
          background: #1474a4;
          border: 0.0625rem solid #1474a4;
        }
        .el-switch__core:after {
          width: 20px;
          height: 20px;
          top: -1px;
          left: -2px;
        }
      }
      .el-switch.is-checked .el-switch__core::after {
        left: 100%;
      }
    }
  }
}
</style>
