<template>
  <div class="content">
    <div class="sham-content"></div>
    <div class="right-content" ref="rightContent">
      <div class="echarts-top">
        <div class="bg-title" style="padding: 0 3rem">工单综合分析</div>
        <div class="bg-content" style="height: calc(100% - 2.5rem)">
          <div class="alarm-analysis">
            <div class="case-img">
              <img src="../../../assets/images/peace/histogram.png" />
              <div class="case-num">
                <p>{{ serviceAnalysis.todayNum }}<span>&nbsp;单</span></p>
                <p style="font-size: 0.75rem; margin-top: 0.8rem; color: #cfe1f7">本日工单</p>
              </div>
              <div class="case-anim-icon"></div>
            </div>
            <div class="case-img">
              <img src="../../../assets/images/peace/histogram.png" />
              <div class="case-num">
                <p>{{ serviceAnalysis.monthNum }}<span>&nbsp;单</span></p>
                <p style="font-size: 0.75rem; margin-top: 0.8rem; color: #cfe1f7">本月工单</p>
              </div>
              <div class="case-anim-icon"></div>
            </div>
            <div class="case-img">
              <img src="../../../assets/images/peace/histogram.png" />
              <div class="case-num">
                <p>{{ serviceAnalysis.weekHangNum }}<span>&nbsp;单</span></p>
                <p style="font-size: 0.75rem; margin-top: 0.8rem; color: #cfe1f7">本周挂单</p>
              </div>
              <div class="case-anim-icon"></div>
            </div>
          </div>
          <div class="workorder-analysis">
            <div class="analysis-box-top">
              <div class="day-analysis">
                <div class="day-analysis-left">
                  <p>
                    <span>本日完成</span><span>{{ serviceAnalysis.toDayFinish }}</span>
                  </p>
                  <p>
                    <span>本日未完成</span><span>{{ serviceAnalysis.toDayUnFinish }}</span>
                  </p>
                </div>
                <div class="day-analysis-right">
                  <el-progress class="circle-box" type="circle" :stroke-width="4" color="#FFE3A6" text-color="#FFF" :width="45" :percentage="serviceAnalysis.completeRate"></el-progress>
                  <!-- <circle-box comColor="#FFC884" boxId="1" :percent="serviceAnalysis.completeRate" /> -->
                  <p>完成率</p>
                </div>
              </div>
              <div class="out-time-order">
                <div class="out-time-order-left">
                  <p>今日超时工单</p>
                  <p>{{ serviceAnalysis.countOverTimes }}</p>
                  <el-progress text-color="#FFF" :percentage="serviceAnalysis.countOverTimesRate * 100"></el-progress>
                </div>
                <!-- <div class="out-time-order-right">
                  <circle-box comColor="#6FFDE2" boxId="2" :percent="serviceAnalysis.countOverTimesRate" />
                  <p>占比</p>
                </div> -->
              </div>
            </div>
            <div class="analysis-box-bottom">
              <div class="month-analysis">
                <div class="month-analysis-left">
                  <!-- <p>72<span>&nbsp;%</span></p> -->
                  <p>{{ serviceAnalysis.satisfiedRate }}</p>
                  <p>本月满意度</p>
                </div>
                <div class="month-analysis-right">
                  <!-- <p>92<span>&nbsp;%</span></p> -->
                  <p>{{ serviceAnalysis.callBackRate }}</p>
                  <p>7日回访率</p>
                </div>
              </div>
              <div class="month-average-order">
                <!-- <p>4<span>&nbsp;时</span>&nbsp;&nbsp;15<span>&nbsp;分</span>&nbsp;&nbsp;41<span>&nbsp;秒</span></p> -->
                <p style="font-size: 1rem">{{ serviceAnalysis.finishTime }}</p>
                <p>当月平均完工时长</p>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="echarts-center">
        <div class="bg-title" style="padding: 0 1rem 0 3rem">
          <span>维修事项Top5</span><span class="center-legend"></span
          ><span class="right-search"
            ><span @click="activeTop5Event('#top5Week', 'week')" id="top5Week">本周</span><i>|</i><span @click="activeTop5Event('#top5Month', 'month')" id="top5Month">本月</span><i>|</i
            ><span id="top5Year" @click="activeTop5Event('#top5Year', 'year')">本年</span></span
          >
        </div>
        <div class="bg-content services-item" style="height: calc(100% - 2.5rem)">
          <!-- <template v-if="">
          </template> -->
          <div v-if="serviceTopShow" class="center-center">暂无数据</div>
          <div v-else class="servive-item-div" v-for="(service, index) in serviceTop5" :key="index">
            <div class="item-div-title">
              <div>{{ 'Top' + (index + 1) }}</div>
              <span>{{ service.matterName }}</span>
            </div>
            <el-progress :stroke-width="8" class="services-item-progress" :percentage="(service.workCount / serviceTop5[0].workCount) * 100" :show-text="false"></el-progress>
            <span class="progress-num" :title="service.workCount">{{ service.workCount }}</span>
          </div>
        </div>
      </div>
      <div class="echarts-bottom">
        <div class="bg-title" style="padding: 0 1rem 0 3rem">
          <span>科室报修量</span><span class="center-legend"></span
          ><span class="right-search"
            ><span @click="activeDeptEvent('#deptWeek', 'week')" id="deptWeek">本周</span><i>|</i><span @click="activeDeptEvent('#deptMonth', 'month')" id="deptMonth">本月</span><i>|</i
            ><span id="deptYear" @click="activeDeptEvent('#deptYear', 'year')">本年</span></span
          >
        </div>
        <div class="bg-content" style="height: calc(100% - 2.5rem)">
          <div v-if="repaireShow" class="center-center">暂无数据</div>
          <div v-else style="width: 100; height: 100%">
            <div id="deptRepairEcharts"></div>
            <div class="pie_background"></div>
          </div>
        </div>
      </div>
    </div>
    <div class="bottom-content" ref="bottomContent">
      <div class="bg-title">
        <span>本月工单动态</span>
      </div>
      <div class="bg-content">
        <div v-if="workOrderLineShow" class="center-center">暂无数据</div>
        <div v-else style="width: 100%; height: 100%">
          <div id="workOrderEcharts"></div>
        </div>
      </div>
    </div>
    <!-- <el-button class="sino-button-sure" @click="workOrderListChange">showDialog</el-button> -->
    <template v-if="workOrderListShow">
      <workOrderCenterList ref="workOrderCenterList" :ssmCodes="ssmCodes" :location="location" :ssmType="ssmType" :dialogShow="workOrderListShow" @configCloseDialog="configCloseDialog"></workOrderCenterList>
    </template>
  </div>
</template>
<script>
import $ from 'jquery'
import * as echarts from 'echarts'
import 'echarts-gl'
// import circleBox from '@/components/common/circleBox.vue'
import workOrderCenterList from './component/workOrderCenterList.vue'
// import { getPie3D } from '@/assets/common/pie-event'
import { getComprehensiveWorkOrderInfo } from '@/utils/peaceRightScreenApi'
import { getMaintenanceMattersTop5, getDepartmentRepairInfo, getWorkOrderTrendInfo } from '@/utils/centerScreenApi'

export default {
  name: 'businessIOMS',
  components: {
    // circleBox
    workOrderCenterList
  },
  data() {
    return {
      warnAnalysis: {
        countNumByDay: 20,
        countNumByMonth: 108,
        countNumByYear: 19
      },
      serviceAnalysis: {
        callBackNum: 0,
        callBackRate: '0.0%',
        closedCompleted: 0,
        completeRate: 0,
        countOverTimes: '0',
        countOverTimesRate: 0,
        finishTime: '0秒',
        monthNum: 0,
        satisfiedRate: '0.00%',
        todayNum: 0,
        unCompleteRate: '0.00%',
        weekHangNum: 0,
        toDayFinish: 0,
        toDayUnFinish: 0
      },
      serviceTop5: [],
      serviceTopShow: true,
      repaireShow: true,
      workOrderLineShow: true,
      workOrderListShow: false,
      timer: null,
      location: '',
      ssmType: '',
      ssmCodes: ''
    }
  },
  mounted() {
    // 初始化 获取定位信息
    if (Object.hasOwn(this.$route.query, 'localtion') || Object.hasOwn(this.$route.query, 'ssmType')) {
      this.location = this.$route.query.localtion || ''
      this.ssmType = this.$route.query.ssmType || ''
    }
    // this.location = '01001'
    // this.ssmType = '4'
    this.search()
    this.timer = setInterval(() => {
      setTimeout(() => {
        this.search()
      }, 0)
    }, 1000 * timerNum)
    this.$once('hook:beforeDestroy', () => {
      clearInterval(this.timer)
      this.timer = null
    })
    try {
      window.chrome.webview.addEventListener('message', (event) => {
        const data = JSON.parse(event.data)
        this.location = data.localtion || ''
        this.ssmType = data.ssmType || ''
        if (data.type === 'tag') {
          this.ssmCodes = data.ssmCodes || ''
          this.workOrderListChange()
        }
      })
    } catch (error) {}
    try {
      this.workOrderListShow = false
      // window.chrome.webview.hostObjects.sync.bridge.ShowTable(this.workOrderListShow)
    } catch (error) {}
  },
  methods: {
    search() {
      this.activeTop5Event('#top5Week', 'week')
      this.activeDeptEvent('#deptWeek', 'week')
      this.getWorkOrderTrendInfo()
      this.getServiceAnalysis()
    },
    getWorkOrderTrendInfo() {
      getWorkOrderTrendInfo({}).then((res) => {
        const data = res.data
        if (data.code === '200') {
          const arr = data.data.list
          if (arr.length) {
            this.workOrderLineShow = false
            const dateArr = []
            const echartsData = []
            // 获取日期 以及 获取工单类型每日对应数据
            for (let i = 0; i < arr.length; i++) {
              if (dateArr.indexOf(arr[i].createDate) === -1) {
                dateArr.push(arr[i].createDate)
              }
              if (!echartsData.length || !echartsData.some((e) => e.name === arr[i].workTypeName)) {
                echartsData.push({
                  name: arr[i].workTypeName,
                  value: [arr[i].workOrderNum]
                })
              } else {
                for (let j = 0; j < echartsData.length; j++) {
                  if (echartsData[j].name === arr[i].workTypeName) {
                    echartsData[j].value.push(arr[i].workOrderNum)
                    break
                  }
                }
              }
            }
            this.$nextTick(() => {
              this.getMonthWorkOrderList(dateArr, echartsData)
            })
          } else {
            this.workOrderLineShow = true
          }
        }
      })
    },
    getDepartmentRepairInfo(type) {
      getDepartmentRepairInfo({ dateType: type }).then((res) => {
        const data = res.data
        if (data.code === '200') {
          const arr = data.data.list
          if (arr.length) {
            this.repaireShow = false
            this.$nextTick(() => {
              this.departmentEchart(arr)
            })
          } else {
            this.repaireShow = true
          }
        }
      })
    },
    departmentEchart(arr) {
      const getchart = echarts.init(document.getElementById('deptRepairEcharts'))
      // const option = arr.map((e) => {
      //   return {
      //     name: e.deptName,
      //     value: e.workCount
      //   }
      // })
      const nameList = Array.from(arr, (item) => item.deptName)
      const valueList = Array.from(arr, (item) => item.workCount)
      const data = []
      var color = ['rgba(31, 250, 255, 0.3)', 'rgba(255, 227, 166, 0.28)', 'rgba(147, 130, 255, 0.26)', 'rgba(0, 248, 114, 0.24)', 'rgba(46, 119, 251, 0.3)']
      var borderColor = ['rgba(31, 250, 255, 1)', 'rgba(255, 227, 166, 1)', 'rgba(147, 130, 255, 1)', 'rgba(0, 248, 114, 1)', 'rgba(46, 119, 251, 1)']
      for (var i = 0; i < nameList.length; i++) {
        const randomRgbColor = this.$tools.randomRgbColor('array')
        data.push({
          value: valueList[i],
          name: nameList[i],
          itemStyle: {
            normal: {
              borderWidth: 1,
              shadowBlur: 200,
              borderColor: borderColor[i] ?? randomRgbColor[1],
              color: color[i] ?? randomRgbColor[0],
              label: {
                normal: { show: true }
              }
            }
          }
        })
      }
      var seriesObj = [
        {
          name: '',
          type: 'pie',
          clockWise: false,
          center: ['50%', '50%'],
          radius: ['45%', '65%'],
          hoverAnimation: true,
          label: {
            normal: {
              show: false,
              position: 'center',
              formatter: '{value|{c}}\n{label|{b}}',
              rich: {
                value: {
                  padding: 5,
                  align: 'center',
                  verticalAlign: 'middle',
                  fontSize: 20
                },
                label: {
                  align: 'center',
                  verticalAlign: 'middle',
                  fontSize: 12
                }
              }
            },
            emphasis: {
              show: true,
              textStyle: {
                fontSize: '12',
                color: '#fff'
              }
            }
          },
          data: data
        }
      ]
      const option = {
        backgroundColor: 'rgba(128, 128, 128, 0)',
        tooltip: {
          show: false
        },
        legend: {
          type: 'scroll',
          orient: 'horizontal',
          left: 'center',
          bottom: '0',
          data: nameList,
          itemWidth: 8,
          itemHeight: 8,
          itemGap: 16,
          textStyle: {
            color: '#B3C2DD' //  字体颜色
          }
        },
        toolbox: {
          show: false
        },
        series: seriesObj
      }
      getchart.clear()
      getchart.setOption(option)
      // getchart.setOption(getPie3D(option, 1.7))
      // 随着屏幕大小调节图表
      window.addEventListener('resize', () => {
        getchart.resize()
      })
      getchart.dispatchAction({
        type: 'highlight',
        seriesIndex: 0,
        dataIndex: 0
      })
      getchart.on('mouseover', (e) => {
        // 取消默认高亮
        getchart.dispatchAction({
          type: 'downplay',
          seriesIndex: 0,
          dataIndex: 0
        })
      })
      // 鼠标移出后默认高亮
      getchart.on('mouseout', (e) => {
        getchart.dispatchAction({
          type: 'highlight',
          seriesIndex: 0,
          dataIndex: 0
        })
      })
    },
    getMonthWorkOrderList(dateArr, echartsData) {
      const seriesObj = []
      for (let q = 0; q < echartsData.length; q++) {
        seriesObj.push({
          name: echartsData[q].name,
          type: 'line',
          smooth: true,
          showSymbol: false,
          symbol: 'circle',
          symbolSize: 6,
          data: echartsData[q].value,
          itemStyle: {
            normal: {
              // color: '#FFE3A6'
            }
          },
          lineStyle: {
            normal: {
              width: 2
            }
          }
        })
      }
      const getchart = echarts.init(document.getElementById('workOrderEcharts'))
      const option = {
        backgroundColor: '',
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            lineStyle: {
              color: '#FFE3A6'
            }
          },
          backgroundColor: '#0C2269',
          borderColor: '#5B617C',
          textStyle: {
            color: '#719DE7'
          }
        },
        grid: {
          top: '6%',
          left: '4%',
          right: '10%',
          bottom: '12%'
        },
        legend: {
          textStyle: { color: '#fff' },
          right: 30,
          y: 'center',
          orient: 'vertical'
          // data: ['综合维修', '应急保洁']
        },
        // 保存为图片
        // toolbox: {
        //   feature: {
        //     saveAsImage: {}
        //   }
        // },
        xAxis: {
          type: 'category',
          data: dateArr,
          boundaryGap: false,
          axisTick: {
            show: false
          },
          axisLine: {
            lineStyle: {
              color: '#303F69'
            }
          },
          axisLabel: {
            textStyle: {
              fontSize: 10,
              color: '#8C9EB5'
            }
          }
        },
        yAxis: {
          type: 'value',
          axisTick: {
            show: false
          },
          // axisLine: {
          //   lineStyle: {
          //     color: '#609ee9'
          //   }
          // },
          axisLabel: {
            margin: 10,
            textStyle: {
              fontSize: 10,
              color: '#8C9EB5'
            }
          },
          splitLine: {
            show: true,
            lineStyle: {
              color: ['#303F69'],
              width: 1,
              type: 'solid'
            }
          }
        },
        series: seriesObj
      }
      getchart.clear()
      // })
      // 随着屏幕大小调节图表
      window.addEventListener('resize', () => {
        getchart.resize()
      })
      getchart.setOption(option)
    },
    // 后勤服务工作综合分析
    getServiceAnalysis() {
      getComprehensiveWorkOrderInfo().then((res) => {
        // console.log(res)
        const data = res.data
        if (data.code === '200') {
          const arr = data.data.resultMap
          arr.completeRate = this.toPoint(arr.completeRate)
          arr.countOverTimesRate = this.toPoint(arr.countOverTimesRate)
          this.serviceAnalysis = data.data.resultMap
        } else {
          this.$message({
            message: data.data.msg,
            type: 'warning'
          })
        }
      })
    },
    // 维修事项top5
    getMaintenanceMattersTop5(type) {
      getMaintenanceMattersTop5({ dateType: type }).then((res) => {
        const data = res.data
        if (data.code === '200') {
          const arr = data.data.list
          if (arr.length) {
            this.serviceTopShow = false
            arr.sort((a, b) => {
              return b.workCount - a.workCount
            })
            this.serviceTop5 = arr
          } else {
            this.serviceTopShow = true
          }
        }
      })
    },
    toPoint(str) {
      var data = str.replace('%', '')
      if (data.lastIndexOf('.') > 2) {
        return data.substring(0, data.indexOf('.') + 3)
      } else {
        return data
      }
      // var data = str.replace('%', '')
      // return (data / 100).toFixed(2)
    },
    activeTop5Event(type, val) {
      $('#top5Week').removeClass('active')
      $('#top5Month').removeClass('active')
      $('#top5Year').removeClass('active')
      $(type).addClass('active')
      this.getMaintenanceMattersTop5(val)
    },
    activeDeptEvent(type, val) {
      $('#deptWeek').removeClass('active')
      $('#deptMonth').removeClass('active')
      $('#deptYear').removeClass('active')
      $(type).addClass('active')
      this.getDepartmentRepairInfo(val)
    },
    configCloseDialog() {
      this.workOrderListChange()
    },
    workOrderListChange() {
      this.workOrderListShow = !this.workOrderListShow
      this.$nextTick().then(() => {
        if (this.workOrderListShow) {
          this.$refs.bottomContent.style.height = 0
          this.$refs.rightContent.style.width = 0
          this.$nextTick(() => {
            this.$refs.workOrderCenterList.getWorkOrderTableData()
          })
        } else {
          this.$refs.bottomContent.style.height = '30%'
          this.$refs.rightContent.style.width = '20%'
        }
        try {
          window.chrome.webview.hostObjects.sync.bridge.ShowTable(this.workOrderListShow)
        } catch (error) {}
      })
    },
    setWPFborder() {
      const json = {
        right: 22,
        bottom: 32
      }
      window.chrome.webview.hostObjects.sync.bridge.SetLocation(JSON.stringify(json))
    }
  }
}
</script>
<style lang="scss" scoped>
.content {
  width: 100%;
  height: 100%;
  position: relative;
  .active {
    background: url('~@/assets/images/center/light-yellow.png') no-repeat;
    background-size: 100% 100%;
    color: #ffe3a6 !important;
    outline: none;
  }
  .sham-content {
    pointer-events: none;
  }
  .right-content {
    position: absolute;
    top: 0;
    right: 0;
    width: 20%;
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    transition: width 0.3s;
    overflow: hidden;
    .echarts-top {
      width: 100%;
      height: 35%;
      background: url('~@/assets/images/peace/emergency-disposal-left.png') no-repeat;
      background-size: 100% 100%;
      padding: 2px;
      box-sizing: border-box;
      .alarm-analysis {
        display: flex;
        justify-content: space-around;
        box-sizing: border-box;
        width: 100%;
        height: 50%;
        .case-img {
          width: 18%;
          height: 85%;
          margin: auto;
          position: relative;
          img {
            width: 100%;
            height: 100%;
          }
          .case-num {
            position: absolute;
            width: 4rem;
            text-align: center;
            top: 8%;
            color: #fff;
            font-size: 1.1rem;

            font-family: DIN-Bold, DIN;
            font-weight: bold;
            span {
              font-weight: 400;
              font-size: 0.75rem;
            }
          }
          .case-anim-icon {
            position: absolute;
            bottom: 10%;
            left: calc(50% - 1rem);
            width: 2rem;
            height: 2rem;
            background: url('~@/assets/images/peace/icon-workorder.png') no-repeat;
            background-size: 100% 100%;
            animation: jump 1s ease-out infinite alternate-reverse;
          }
        }
      }
      .workorder-analysis {
        box-sizing: border-box;
        width: 100%;
        height: 50%;
        // padding: 0 1rem;
        display: flex;
        flex-direction: column;
        justify-content: space-evenly;
        .analysis-box-top {
          height: calc(60% - 5px);
        }
        .analysis-box-bottom {
          margin-top: 1%;
          height: calc(40% - 5px);
        }
        .analysis-box-top,
        .analysis-box-bottom {
          display: flex;
          justify-content: space-between;
        }
        .day-analysis,
        .month-analysis {
          width: 55%;
          height: 100%;
        }
        .out-time-order,
        .month-average-order {
          width: 43%;
          height: 100%;
        }
        .day-analysis,
        .out-time-order,
        .month-average-order,
        .month-analysis {
          background: url('~@/assets/images/peace/people-left-center.png') no-repeat;
          background-size: 100% 100%;
          padding: 0.3125rem;
          box-sizing: border-box;
        }
        .day-analysis,
        .out-time-order {
          display: flex;
          justify-content: space-around;
        }
        .day-analysis-left {
          width: 90%;
          display: flex;
          flex-direction: column;
          justify-content: space-evenly;
          padding: 0 0.5rem 0 1.2rem;
          box-sizing: border-box;
          p {
            display: flex;
            justify-content: space-between;
            font-size: 0.75rem;
            :first-child {
              color: #7eaef9;
              text-align: center;
            }
            :last-child {
              color: #fff;
              text-align: center;
              font-weight: bold;
            }
          }
        }
        .day-analysis-right {
          width: 40%;
          display: flex;
          justify-content: space-around;
          flex-direction: column;
          p {
            text-align: center;
            color: #fff;
            font-size: 0.75rem;
          }
          .circle-box {
            margin: auto;
            ::v-deep .el-progress__text {
              color: #fff;
            }
          }
        }
        .out-time-order-left {
          width: 100%;
          display: flex;
          flex-direction: column;
          justify-content: space-evenly;
          // padding: 0 0.5rem 0 1.2rem;
          box-sizing: border-box;
          color: #7eaef9;
          p {
            text-align: center;
            font-size: 0.75rem;
          }
          :nth-child(2) {
            color: #fff;
            font-size: 1.1rem;
            font-family: DIN-Bold, DIN;
            span {
              font-size: 0.75rem;
              // color: #fff;
            }
          }
          :last-child {
            color: #fff;
          }
          ::v-deep .el-progress {
            width: 80%;
            margin-left: 20%;
            .el-progress__text {
              font-size: 12px !important;
            }
            .el-progress-bar__outer {
              border-radius: 0;
              background-color: #3b4977;
              height: 4px !important;
            }
            .el-progress-bar__inner {
              background: linear-gradient(90deg, #268aff 0%, #71ffe2 100%);
              border-radius: 0;
            }
          }
        }
        .month-average-order {
          display: flex;
          flex-direction: column;
          justify-content: space-evenly;
          padding: 0 0.5rem 0 1.2rem;
          box-sizing: border-box;
          p {
            text-align: center;
            font-size: 0.75rem;
          }
          :first-child {
            color: #fff;
            font-size: 1.1rem;
            font-family: DIN-Bold, DIN;
            span {
              font-size: 0.75rem;
              color: #fff;
            }
          }
          :last-child {
            color: #7eaef9;
          }
        }
        .month-analysis {
          display: flex;
          justify-content: space-around;
          // flex-direction: column;
          // justify-content: space-evenly;
          // padding: 0 0.5rem 0 1.2rem;
          // box-sizing: border-box;
          .month-analysis-left,
          .month-analysis-right {
            display: flex;
            flex-direction: column;
            justify-content: space-evenly;
            // padding: 0 0.5rem 0 1.2rem;
            box-sizing: border-box;
            p {
              text-align: center;
              font-size: 0.75rem;
            }
            :first-child {
              color: #fff;
              font-size: 1.1rem;
              font-family: DIN-Bold, DIN;
              span {
                font-size: 0.75rem;
                color: #fff;
              }
            }
            :last-child {
              color: #7eaef9;
            }
          }
        }
      }
    }
    .echarts-center {
      width: 100%;
      height: 35%;
      background: url('~@/assets/images/peace/emergency-disposal-left.png') no-repeat;
      background-size: 100% 100%;
      padding: 2px;
      box-sizing: border-box;
      .right-search {
        span {
          width: 35px;
        }
      }
      // .services-item {
      //   display: flex;
      //   flex-direction: column;
      //   justify-content: space-around;
      // }
      .servive-item-div {
        height: 20%;
        padding: 5px 3%;
        box-sizing: border-box;
        .item-div-title {
          div {
            display: inline-block;
            font-size: 12px;
            width: 40px;
            height: 16px;
            text-align: center;
            background: url('~@/assets/images/center/services-top.png') no-repeat;
            background-size: 100% 100%;
            font-family: DIN-Medium, DIN;
            color: #ffe3a6;
            line-height: 16px;
            margin-right: 10px;
            margin-bottom: 3px;
          }
          span {
            font-size: 13px;
            font-family: PingFang-SC-Medium, PingFang-SC;
            color: #dceaff;
          }
        }
        .services-item-progress {
          width: 88%;
          display: inline-block;
          margin-right: 5px;
          ::v-deep .el-progress-bar {
            .el-progress-bar__outer {
              height: 13px;
              background: #0d2253;
              opacity: 0.5;
              border: 1px solid;
              border-image: linear-gradient(90deg, rgba(0, 133, 253, 1), rgba(9, 244, 196, 1)) 1 1;
              .el-progress-bar__inner {
                border-radius: 0;
                // height: 8px;
                background: linear-gradient(90deg, rgba(9, 244, 196, 0) 0%, rgba(9, 244, 196, 0.59) 100%);
                box-shadow: 1px 0px 0px 0px #08f3c4;
                // border: 1px solid;
                // border-image: linear-gradient(90deg, rgba(0, 133, 253, 1), rgba(9, 244, 196, 1)) 1 1;
              }
            }
          }
        }
        .progress-num {
          display: inline-block;
          width: calc(12% - 5px);
          text-align: right;
          overflow: hidden;
          white-space: nowrap;
          text-overflow: ellipsis;
          font-size: 13px;
          font-family: PingFang-SC-Medium, PingFang-SC;
          color: #ffffff;
        }
      }
    }
    .echarts-bottom {
      width: 100%;
      height: 30%;
      background: url('~@/assets/images/peace/emergency-disposal-left.png') no-repeat;
      background-size: 100% 100%;
      .right-search {
        span {
          width: 35px;
        }
      }
    }
  }
  .bottom-content {
    height: 30%;
    width: 80%;
    position: absolute;
    top: 70%;
    background: url('~@/assets/images/peace/bg-24.png') no-repeat;
    background-size: 100% 100%;
    .bg-title {
      height: 23%;
    }
    transition: height 0.3s;
    overflow: hidden;
    // pointer-events: none;
  }
  .bg-title {
    margin-top: 5px;
    // height: 23%;
    height: 2.5rem;
    line-height: 2.5rem;
    color: #d4e3f9;
    padding: 0 5rem;
    font-family: TRENDS;
    /*设置为伸缩容器*/
    display: -webkit-box;
    display: -moz-box;
    display: -ms-flexbox;
    display: -webkit-flex;
    display: flex;
    /*垂直居中*/
    -webkit-box-align: center; /*旧版本*/
    -moz-box-align: center; /*旧版本*/
    -ms-flex-align: center; /*混合版本*/
    -webkit-align-items: center; /*新版本*/
    align-items: center; /*新版本*/
    justify-content: space-between;
    .right-search {
      span {
        display: inline-block;
        width: 50px;
        height: 30px;
        font-size: 14px;
        text-align: center;
        line-height: 26px;
        margin: 0 4px;
        color: #7eaef9;
        cursor: pointer;
      }
      span:hover {
        background: url('~@/assets/images/center/light-yellow.png') no-repeat;
        background-size: 100% 100%;
        color: #ffe3a6;
      }
      // span:focus {
      //   background: url('~@/assets/images/center/light-yellow.png') no-repeat;
      //   background-size: 100% 100%;
      //   color: #ffe3a6;
      //   outline: none;
      // }
      i {
        color: #7eaef9;
      }
    }
  }
  .bg-content {
    position: relative;
    box-sizing: border-box;
    padding: 10px 10px 15px 10px;
    width: 100%;
    height: 77%;
    #workOrderEcharts,
    #deptRepairEcharts {
      width: 100%;
      height: 100%;
      z-index: 2;
      // background: red;
    }
  }
}
</style>
