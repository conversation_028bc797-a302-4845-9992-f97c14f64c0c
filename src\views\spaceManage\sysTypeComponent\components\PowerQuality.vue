<template>
  <div class="PowerQuality" style="height: 100%; 'overflow-y': 'auto'">
    <div v-if="showPageType == 6" class="event-box">
      <div class="search-form">
        <el-select v-model="searchForm.disposeResult" placeholder="所有类型">
          <el-option v-for="item in alarmStateOption" :key="item.id" :label="item.labelName" :value="item.id">
          </el-option>
        </el-select>
        <el-date-picker key="date" v-model="searchForm.daterange" popper-class="date-style" type="date" placeholder="日期"
          :clearable="false" value-format="yyyy-MM-dd" format="yyyy-MM-dd">
        </el-date-picker>
        <el-input v-model="searchForm.surveyName" placeholder="请输入关键字"></el-input>
      </div>
      <el-table :data="tableData" :cell-style="$tools.setCell(3)" :header-cell-style="$tools.setHeaderCell(3)"
        style="width: 100%" height="calc(100% - 50px)" element-loading-background="rgba(0, 0, 0, 0.2)">
        <el-table-column prop="eventType" show-overflow-tooltip label="事件类型"></el-table-column>
        <el-table-column prop="eventOccurred" show-overflow-tooltip label="发生事件"></el-table-column>
        <el-table-column prop="eventDescription" show-overflow-tooltip label="事件描述"></el-table-column>
        <el-table-column prop="waveForm" show-overflow-tooltip label="波形">
          <template slot-scope="scope">
            <svg-icon v-if="scope.row.waveForm" style="font-size: 20px" name="wave-form" />
            <span v-else>无波形</span>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <div v-else-if="showPageType == 7">
      <table class="base-table" v-if="incidentList.length">
        <tr v-for="(item, index) in incidentList" :key="index" class="base-tr">
          <td class="label-td">{{ item.incidentName }}</td>
          <td class="value-td">{{ item.num }}</td>
        </tr>
      </table>
      <div>暂无数据</div>
    </div>
  </div>
</template>

<script>
import { getIncidentNameGroupCountData } from '@/utils/spaceManage'
export default {
  name: 'PowerQuality',
  props: {
    showPageType: {
      type: String,
      default: '6'
    },
    objectId: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      searchForm: {
        daterange: '',
        surveyName: '',
        disposeResult: ''
      },
      alarmStateOption: [
        {
          id: 1,
          labelName: '所有类型'
        }
      ],
      baseInfo: [
        {
          label: '暂态事件',
          value: 4,
          unit: '次'
        },
        {
          label: '冲击电流事件',
          value: 24,
          unit: '次'
        },
        {
          label: '电压暂升',
          value: 0,
          unit: '次'
        },
        {
          label: '瞬间暂降',
          value: 0,
          unit: '次'
        },
        {
          label: '电压中断',
          value: 0,
          unit: '次'
        },
        {
          label: '瞬间电压',
          value: 0,
          unit: '次'
        },
        {
          label: '快速电压变动',
          value: 0,
          unit: '次'
        },
        {
          label: '冲击电流',
          value: 0,
          unit: '次'
        }
      ],
      incidentList: [],
      tableData: [
        {
          eventType: '暂态事件',
          eventOccurred: '2024-01-22 11:52:49',
          eventDescription: '过电压返回'
        },
        {
          eventType: '暂态事件',
          eventOccurred: '2024-01-10 05:03:30',
          eventDescription: '过电压启动',
          waveForm: true
        },
        {
          eventType: '暂态事件',
          eventOccurred: '2024-01-10 04:56:34',
          eventDescription: '过电压返回'
        },
        {
          eventType: '暂态事件',
          eventOccurred: '2023-11-27 20:21:09',
          eventDescription: '过电压启动',
          waveForm: true
        },
        {
          eventType: '冲击电流事件',
          eventOccurred: '2023-11-18 12:55:25',
          eventDescription: '冲击电流返回',
          waveForm: true
        },
        {
          eventType: '冲击电流事件',
          eventOccurred: '2023-11-18 12:55:25',
          eventDescription: '冲击电流事件',
          waveForm: true
        },
        {
          eventType: '冲击电流事件',
          eventOccurred: '2023-11-17 13:56:52',
          eventDescription: '冲击电流返回'
        },
        {
          eventType: '冲击电流事件',
          eventOccurred: '2023-11-17 13:56:52',
          eventDescription: '冲击电流启动'
        },
        {
          eventType: '冲击电流事件',
          eventOccurred: '2023-11-17 10:09:19',
          eventDescription: '冲击电流返回'
        },
        {
          eventType: '冲击电流事件',
          eventOccurred: '2023-11-17 10:09:19',
          eventDescription: '冲击电流启动'
        },
        {
          eventType: '冲击电流事件',
          eventOccurred: '2023-11-16 12:23:35',
          eventDescription: '冲击电流返回'
        },
        {
          eventType: '冲击电流事件',
          eventOccurred: '2023-11-16 01:19:48',
          eventDescription: '冲击电流返回'
        },
        {
          eventType: '冲击电流事件',
          eventOccurred: '2023-11-16 01:19:48',
          eventDescription: '冲击电流启动'
        }
      ]
    }
  },
  mounted() {
    this.getIncidentData()
  },
  methods: {
    getIncidentData() {
      const params = {
        objectId: this.objectId
        // objectId: '2ba6b90cf6974350afbaf60cef19f42c'//测试
      }
      getIncidentNameGroupCountData(params).then((res) => {
        if (res.data.code === '200') {
          this.incidentList = res.data.data
        }
      })
    },

  }
}
</script>

<style lang="scss" scoped>
.PowerQuality {
  width: 100%;
  height: 100%;
  .event-box {
    width: 100%;
    height: 100%;
    .search-form {
      height: 40px;
      width: 100%;
      margin-bottom: 10px;
      display: flex;
      & > div {
        margin-right: 10px;
        width: 180px;
        height: 35px;
      }
      ::v-deep .el-input__icon {
        line-height: 35px;
      }
      ::v-deep .el-select {
        .el-input .el-input__inner {
          height: 35px;
        }
      }
    }
  }
  .base-table {
    border: 1px solid rgba(168, 172, 171, 0.08);
    width: 100%;
    // height: 100%;
    tr:nth-child(even) {
      // background-color: rgba(133,145,206,0.15);
    }
    .base-tr {
      width: 100%;
      td {
        line-height: 20px;
        padding: 9px 0;
      }
      td:nth-child(1) {
        color: #b0e3fa;
      }
      .label-td {
        width: 75px;
        padding-left: 40px;
        text-align: left;
        font-size: 0.875rem;
        font-family: PingFang-SC-Medium, PingFang-SC;
        font-weight: 500;
        color: #7eaef9;
        white-space: nowrap;
        vertical-align: top;
      }
      .value-td {
        text-align: left;
        padding-left: 20px !important;
        font-size: 0.875rem;
        font-family: PingFang-SC-Medium, PingFang-SC;
        font-weight: 500;
        color: #ffffff;
      }
    }
  }
}
</style>
