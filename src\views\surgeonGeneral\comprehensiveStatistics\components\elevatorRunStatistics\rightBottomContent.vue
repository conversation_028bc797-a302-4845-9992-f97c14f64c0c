<template>
  <div class="statistics_item_right">
    <div class="item_title">运载人员趋势</div>
    <div id="runUserTrend" ref="runUserTrend"></div>
  </div>
</template>
<script>
import * as echarts from 'echarts'
import { elevatorRunCount } from '@/utils/comprehensiveStatistics'

export default {
  props: {
    elevatorId: {
      type: String,
      default: ''
    },
    date: {
      type: String,
      default: 'day'
    }
  },
  data () {
    return {
      myChart: null
    }
  },
  watch: {
    elevatorId (val) {
      if (val) {

        this.getelevatorRunCount()
      }
    },
    date (val) {
      if (val) {
        this.getelevatorRunCount()
      }
    }
  },

  methods: {
    getelevatorRunCount () {
      let params = {
        monitorDeviceId: this.elevatorId,
        dateType: this.date
      }
      elevatorRunCount(params).then(res => {
        if (res.data.code == 200) {
          if (res.data.result) {
            this.initLine(res.data.result.adsLiftRunCountVOList || '')
          }
        }
      })
    },
    initLine (data) {
      if (this.myChart) {
        this.myChart.dispose()
        this.myChart = null
      }
      let chartDom = document.getElementById('runUserTrend')
      this.myChart = echarts.init(chartDom)
      let option = {}
      this.myChart.showLoading()
      if (data && data.length) {
        option = {
          tooltip: {
            show: true,
            formatter: function (params) {
              let currentTooltipData = data[params.dataIndex]
              return `${currentTooltipData.dateTime}<br>
              累计载人量：${currentTooltipData.peopleSum}<br>
              运行次数：${currentTooltipData.runNumSum}<br>
              开门次数：${currentTooltipData.doorOpenedCntSum}<br>
              运行时长：${currentTooltipData.runTimeSum}<br>
              运行距离：${currentTooltipData.runDistanceSum}`
            }
          },
          grid: {
            left: '0',  // 调整左侧边距
            right: '10px', // 调整右侧边距
            top: '20px',  // 调整顶部边距，为图例留出空间
            bottom: '5%', // 调整底部边距
            containLabel: true
          },
          xAxis: {
            type: 'category',
            data: data.map(el => el.dateTime)
          },
          yAxis: {
            type: 'value',
            splitLine: {
              show: true,
              lineStyle: {
                color: 'rgba(230,247,255,0.2)'
              }
            }

          },
          series: [
            {
              data: data.map(el => el.peopleSum),
              type: 'line'
            }
          ]
        }
      } else {
        option = {
          title: {
            text: '暂无数据',
            x: 'center',
            y: 'center',
            textStyle: {
              fontSize: 14,
              fontWeight: 'normal',
              color: '#999'
            }
          }
        }
      }
      this.myChart.setOption(option)
      // 图例点击事件
      this.myChart.on('click', (params) => {
        this.$emit('showDetail', params.data)
      })
      this.myChart.hideLoading()
    }
  }
}
</script>
<style lang="scss" scoped>
.statistics_item_right{
  width: calc(50% - 4px);
  height: 100%;
  background: rgba(133,145,206,0.05);
  padding: 8px 8px 0;
}
#runUserTrend {
  width: 100%;
  height: calc(100% - 26px);
}
.item_title {
  font-weight: 500;
  font-size: 16px;
  color: #b0e3fa;
  margin-bottom: 10px;
}
</style>
