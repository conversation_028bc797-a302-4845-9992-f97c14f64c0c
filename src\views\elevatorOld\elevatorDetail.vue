<template>
  <div class="monitorDetails">
    <div class="monitorDetails-heade" @click="backEvent"><i class="el-icon-arrow-left"></i>电梯设备详情</div>
    <div class="content-details">
      <div class="content_top">
        <div class="content_top_left">
          <div class="detail_list">
            <div>
              <p>设备名称</p>
              <span>{{ elevatorDetailData.assetsName }}</span>
            </div>
          </div>
          <div class="detail_list">
            <div>
              <p>设备型号</p>
              <span>{{ elevatorDetailData.model }}</span>
            </div>
          </div>
          <div class="detail_list">
            <div>
              <p>注册代码</p>
              <span>{{ elevatorDetailData.registrationNum }}</span>
            </div>
          </div>
          <div class="detail_list">
            <div>
              <p>使用单位</p>
              <span>{{ elevatorDetailData.useDepartmentName }}</span>
            </div>
          </div>
          <div class="detail_list">
            <div>
              <p>维保单位</p>
              <span>{{ elevatorDetailData.supplierName }}</span>
            </div>
          </div>
        </div>
        <div class="content_top_center">
          <div class="top_center_top">
            <div>
              <img src="@/assets/images/elevator/bg-elevator.png" alt="" />
            </div>
            <div>
              <div v-for="(item, index) in serverData" :key="index">
                <p>{{ item.name }}</p>
                <p>
                  <span v-if="item.formatter">{{ item.formatter(item) }}</span>
                  <span v-else>{{ item.value }}{{ item.unit }}</span>
                </p>
              </div>
            </div>
          </div>
          <div class="top_center_bottom">
            <div>
              <div v-for="(item, index) in elevatorServerData" :key="index" class="left_parameter">
                <div>
                  {{ item.name }}<i class="el-icon-caret-right right-icon"></i>
                  <div class="point_cri"></div>
                  <div class="left_line"></div>
                </div>
                <div>
                  <div>
                    <span>{{ item.value }}</span
                    ><span>{{ item.unit }}</span>
                  </div>
                </div>
              </div>
            </div>
            <div>
              <div class="top_red_warn">
                <div class="red_warn_left">
                  <p>七日报警次数</p>
                  <span class="red_warn_span">{{ elevatorDetailData.alarm }}</span>
                </div>
                <div class="red_warn_right">
                  <p>七日预警次数</p>
                  <span class="red_warn_span">{{ elevatorDetailData.proAlarm }}</span>
                </div>
              </div>
              <div class="bottom_green_warn">
                <div class="green_warn_left">
                  <p>上次维保时间</p>
                  <span class="green_warn_span">{{ elevatorDetailData.lastMaintenance || '无' }}</span>
                </div>
                <div class="green_warn_right">
                  <p>下次维保时间</p>
                  <span class="green_warn_span">{{ elevatorDetailData.nextMaintenance || '无' }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="content_top_right">
          <div class="top_video_camera">
            <rtspCavas ref="rtspCavas" :rtspUrl="videoUrl" :videoName="videoName" :hasCavas="Boolean(videoTabsList.length)" class="video_preview"></rtspCavas>
            <!-- <div class="Intercom_box" @click="openCameraTalkDialog">
              <span> 开启对讲</span>
            </div> -->
            <!-- <div class="video_tab">
              <el-tabs v-model="videoTabsValue" type="card" @tab-click="videoChange">
                <el-tab-pane v-for="item in videoTabsList" :key="item.vidiconId" :label="item.vidiconName" :name="item.vidiconId"> </el-tab-pane>
              </el-tabs>
            </div> -->
          </div>
          <div class="bottom_warn_table">
            <el-table
              v-loading="tableLoading"
              class="table-center-transfer"
              :data="tableData"
              height="100%"
              :cell-style="$tools.setCell(3)"
              :header-cell-style="$tools.setHeaderCell(3)"
              style="width: 100%"
            >
              <el-table-column fixed prop="iphPoliceTime" show-overflow-tooltip label="报警时间"></el-table-column>
              <el-table-column fixed label="" width="1"></el-table-column>
              <el-table-column fixed prop="iphParameterName" show-overflow-tooltip label="报警类型"></el-table-column>
              <el-table-column fixed prop="disposeResultName" show-overflow-tooltip label="处置状态"></el-table-column>
            </el-table>
          </div>
        </div>
      </div>
      <div class="content_bottom">
        <div class="content_bottom_pie">
          <div class="echarts_title">报警类型统计</div>
          <div v-if="!warnTypeShow" class="echart-null">
            <img src="@/assets/images/null.png" alt="" />
            <div>暂无数据~</div>
          </div>
          <div v-else style="width: 100%; height: calc(100% - 20px); position: relative">
            <div id="warnTypePieEcharts"></div>
            <div class="warnTypeCenter">
              <img src="@/assets/images/elevator/icon-warn-type-pie.png" alt="" />
            </div>
          </div>
        </div>
        <div class="content_bottom_col">
          <div class="echarts_title echarts_title2">开关门次数统计</div>
          <div v-if="!doorNumsShow" class="echart-null">
            <img src="@/assets/images/null.png" alt="" />
            <div>暂无数据~</div>
          </div>
          <div v-else style="width: 100%; height: calc(100% - 20px); position: relative">
            <div id="doorNumsBarEcharts"></div>
          </div>
        </div>
        <div class="content_bottom_line">
          <div class="echarts_title">偏移趋势</div>
          <div v-if="!offsetTrendShow" class="echart-null">
            <img src="@/assets/images/null.png" alt="" />
            <div>暂无数据~</div>
          </div>
          <div v-else style="width: 100%; height: calc(100% - 20px); position: relative">
            <div id="offsetTrendLineEcharts"></div>
          </div>
        </div>
      </div>
      <template v-if="cameraTalkDialogShow">
        <cameraTalkDialog :visible.sync="cameraTalkDialogShow" :rowData="dialogData" @update:visible="()=> {}" />
      </template>
    </div>
  </div>
</template>

<script>
import moment from 'moment'
// import videoFlv from './components/videoFlv.vue'
import * as echarts from 'echarts'
import { getElevatorParticulars, getAssetDetailsByAssetIds, getReasonStatisticPie, getStaticPoliceList, getVideoList, getHlvAddress, getOpenNumByDays, getDeviationByDays } from '@/utils/elevatorApi'
export default {
  name: 'elevatorDetail',
  components: {
    // 'video-flv': videoFlv,
    rtspCavas: () => import('./components/rtspCavas.vue'),
    cameraTalkDialog: () => import('./cameraTalkDialog.vue')
  },
  data() {
    return {
      dialogData: {},
      warnTypeShow: false,
      doorNumsShow: false,
      offsetTrendShow: false,
      tableLoading: false,
      selectVideoTabs: {},
      videoTabsList: [],
      videoUrl: '',
      videoName: '',
      tableData: [],
      serverData: [
        {
          name: '运行状态',
          value: null,
          field: '2741'
        },
        {
          name: '是否载人',
          value: null,
          field: '2745'
        },
        {
          name: '当前速度',
          value: '',
          unit: 'm/s',
          field: '2711'
        },
        {
          name: '左右偏移',
          value: '',
          field: '2698'
        },
        {
          name: '前后偏移',
          value: '',
          field: '2696'
        },
        {
          name: '当前楼层',
          value: null,
          field: '2700'
        },
        {
          name: '梯门状态',
          value: null,
          field: '2713'
        },
        {
          name: '运行振幅',
          value: '',
          field: '2694'
        },
        {
          name: '温度',
          value: '',
          field: '2748'
        },
        {
          name: '湿度',
          value: '',
          field: '2750'
        }
      ], // 电梯运行数据
      elevatorDetailData: {
        surveyEntityName: '',
        alarm: 0,
        proAlarm: 0,
        lastMaintenance: '',
        nextMaintenance: '',
        assetsName: '',
        model: '',
        registrationNum: '',
        useDepartmentName: '',
        supplierName: ''
      },
      elevatorServerData: [
        {
          name: '运行时长',
          value: '',
          field: '2641',
          unit: ''
        },
        {
          name: '运行距离',
          value: '',
          field: '2639',
          unit: ''
        },
        {
          name: '运行次数',
          value: '',
          field: '2637',
          unit: ''
        },
        {
          name: '开门次数',
          value: '',
          field: '2634',
          unit: ''
        }
      ],
      videoList: [],
      queryData: {
        surveyEntityCode: '',
        projectCode: ''
      },
      cameraTalkDialogShow: false,
      refreshTimer: null
    }
  },
  // 监听侧边栏缩放 动态改变echarts图表大小
  watch: {
    '$store.state.settings.sidebarCollapse': {
      handler(val) {
        this.$nextTick(() => {
          const echartsDom = ['warnTypePieEcharts', 'doorNumsBarEcharts', 'offsetTrendLineEcharts']
          setTimeout(() => {
            echartsDom.forEach((item) => {
              if (document.getElementById(item)) {
                echarts.init(document.getElementById(item)).resize()
              }
            })
          }, 250)
        })
      },
      deep: true
    }
  },
  created() {
    this.queryData.surveyEntityCode = this.$route.query.surveyEntityCode
    this.queryData.projectCode = this.$route.query.projectCode
  },
  destroyed() {
    clearInterval(this.refreshTimer)
  },
  mounted() {
    this.getWarnTypePieData()
    this.getContorlDoorNumData()
    this.getOffsetTrendData()
    this.getElevatorParticulars()
    this.getData()
    this.getVideoList()
    this.refreshTimer = setInterval(() => {
      this.getElevatorParticulars('refresh')
    }, 10000)
  },
  methods: {
    getLegendLength() {
      let legendLength = 0
      const innerHeight = window.innerHeight
      if (innerHeight < 768) {
        legendLength = 2
      }
      if (innerHeight >= 768 && innerHeight < 900) {
        legendLength = 3
      }
      if (innerHeight >= 900 && innerHeight < 1080) {
        legendLength = 5
      }
      if (innerHeight >= 1080) {
        legendLength = 5
      }
      return legendLength
    },
    // 获取电梯详情
    getElevatorParticulars(type = 'init') {
      const params = {
        projectCode: this.queryData.projectCode,
        // entityMenuCode: "********************************",
        surveyCode: this.queryData.surveyEntityCode,
        startTime: moment().format('YYYY-MM-DD'),
        endTime: moment().format('YYYY-MM-DD')
      }
      getElevatorParticulars(params).then((resp) => {
        const res = resp.data
        if (res.code === '200') {
          const data = res.data?.resRealTable ?? false
          if (!data) return
          if (type === 'init' && data.assetsNumber) {
            this.getAssetDetailsById(data.assetsNumber)
          }
          this.dialogData = {
            surveyEntityName: data.surveyEntityName,
            surveyEntityCode: this.queryData.surveyEntityCode
          }
          Object.assign(this.elevatorDetailData, {
            surveyEntityName: data.surveyEntityName,
            alarm: res.data.alarm,
            proAlarm: res.data.proAlarm
          })
          data.parameterList && data.parameterList.forEach((item) => {
            this.serverData.forEach((e) => {
              if (e.field === item.parameterId) {
                e.value = !this.isNumber(item.parameterValue) ? Number(item.parameterValue) : item.parameterValue
              }
            })
            this.elevatorServerData.forEach((e) => {
              if (e.field === item.parameterId) {
                e.value = !this.isNumber(item.parameterValue) ? Number(item.parameterValue) : item.parameterValue
                e.unit = item.parameterUnit
                console.log(this.isNumber(item.parameterValue), e.value)
              }
            })
          })
          // console.log(this.serverData);
        }
      })
    },
    // 获取资产详情
    getAssetDetailsById(assetsId) {
      getAssetDetailsByAssetIds({ assetsId }).then(res => {
        const data = res.data.data
        Object.assign(this.elevatorDetailData, {
          assetsName: data.assetName || '',
          model: data.assetModel || '',
          registrationNum: '',
          useDepartmentName: '',
          supplierName: '',
          lastMaintenance: '',
          nextMaintenance: ''
        })
      })
    },
    isNumber(str) {
      return parseFloat(str).toString() === 'NaN'
    },
    // 获取报警类型统计数据
    getWarnTypePieData() {
      getReasonStatisticPie({
        projectCode: this.queryData.projectCode,
        surveyCode: this.queryData.surveyEntityCode
      }).then((resp) => {
        const res = resp.data
        if (res.code === '200') {
          if (res.data.length > 0) {
            this.warnTypeShow = true
            this.$nextTick(() => {
              this.setWarnTypePieEcharts(res.data)
            })
          } else {
            this.warnTypeShow = false
          }
        }
      })
    },
    // 报警列表
    getData() {
      this.tableLoading = true
      getStaticPoliceList({
        projectCode: this.queryData.projectCode,
        surveyCode: this.queryData.surveyEntityCode,
        pageNo: 1,
        pageSize: 30,
        startTime: '',
        endTime: ''
      }).then((resp) => {
        const res = resp.data
        this.tableLoading = false
        if (res.code === '200') {
          this.tableData = res.data.list
        }
      })
    },
    // 摄像机列表
    getVideoList() {
      this.videoTabsList = [
        {
          videoUrl: ''
        }
      ]
      this.videoUrl = 'rtsp://admin:yjl@2021@************:554/ch1/sub/av/stream'
      this.videoName = '31号电梯'
      return
      getVideoList({
        surveyCode: this.queryData.surveyEntityCode
      }).then((resp) => {
        const res = resp.data
        if (res.code === '200') {
          this.videoTabsList = res.data
          console.log(this.videoTabsList)
          if (res.data.length) {
            this.selectVideoTabs = res.data[0]
            this.getHlvAddress(this.selectVideoTabs)
          } else {
            // const videoUrl = 'https://sf1-hscdn-tos.pstatp.com/obj/media-fe/xgplayer_doc_video/flv/xgplayer-demo-360p.flv'
            // this.$refs.videoflvBox.initFlvjs(videoUrl)
          }
        }
      })
    },
    // 获取摄像机地址
    getHlvAddress(item) {
      const params = {
        cameraId: item.vidiconId
      }
      getHlvAddress(params).then((resp) => {
        const res = resp.data
        if (res.code === '200') {
          this.videoName = item.vidiconName
          this.videoUrl = res.data
          // const videoUrl = 'http://************:18080/flv?port=1935&app=live&stream=rtmpStream'
          // this.$nextTick(() => {
          //   this.$refs.videoflv.initFlvjs(videoUrl)
          // })
          // this.hlvAddress = res.data;
        }
      })
    },
    // 改变摄像机地址
    videoChange() {
      this.getHlvAddress(this.selectVideoTabs)
    },
    // 获取开关门次数统计数据
    getContorlDoorNumData() {
      const params = {
        surveyCode: this.queryData.surveyEntityCode
      }
      getOpenNumByDays(params).then((resp) => {
        const res = resp.data
        if (res.code === '200') {
          this.doorNumsShow = true
          this.$nextTick(() => {
            this.setContorlDoorNumBarEcharts(res.data)
          })
        } else {
          this.doorNumsShow = false
        }
      })
    },
    // 获取偏移趋势
    getOffsetTrendData() {
      const params = {
        surveyCode: this.queryData.surveyEntityCode
      }
      getDeviationByDays(params).then((resp) => {
        const res = resp.data
        if (res.code === '200') {
          this.offsetTrendShow = true
          this.$nextTick(() => {
            this.setOffsetTrendLineEcharts(res.data)
          })
        } else {
          this.offsetTrendShow = false
        }
      })
    },
    // 报警类型统计Echarts
    setWarnTypePieEcharts(chartdata) {
      const getchart = echarts.init(document.getElementById('warnTypePieEcharts'))
      const nameList = Array.from(chartdata, (item) => item.name)
      let sum = 0
      const warnData = []
      chartdata.forEach((item) => {
        sum += Number(item.value)
      })
      const colorIn = ['rgba(246, 180, 58, 1)', 'rgba(183, 187, 202, 1)', 'rgba(81, 136, 252, 1)', 'rgba(0, 221, 197, 1)', 'rgba(255, 86, 84, 1)', 'rgba(255, 84, 56, 1)']
      const colorOut = ['rgba(246, 180, 58, .4)', 'rgba(183, 187, 202, .4)', 'rgba(81, 136, 252, .4)', 'rgba(0, 221, 197, .4)', 'rgba(255, 86, 84, .4)']
      chartdata.forEach((item, index) => {
        const randomRgbColor = this.$tools.randomRgbColor('array')
        if (!colorIn[index] || !colorOut[index]) {
          colorIn.push(randomRgbColor[1])
          colorOut.push(randomRgbColor[0])
        }
        warnData.push(item, {
          value: sum / 100,
          labelLine: {
            show: false,
            lineStyle: {
              color: 'transparent'
            }
          },
          itemStyle: {
            color: 'transparent'
          }
        })
      })
      // colorIn数组每个元素后插入空字符
      colorIn.forEach((item, index) => {
        colorIn.splice(2 * index + 1, 0, '')
        colorOut.splice(2 * index + 1, 0, '')
      })
      const that = this
      const option = {
        legend: {
          type: 'scroll',
          orient: 'vertical',
          top: 'center',
          right: '0%',
          data: nameList,
          itemWidth: 8,
          itemHeight: 10,
          itemGap: 16,
          // padding: [2, 2, 0, 2],
          // backgroundColor: 'rgba(107,195,236,0.12)',
          textStyle: {
            // height: '25',
            // backgroundColor: 'red',
            // shadowOffsetX: 300,
            // borderRadius: '82px',
            rich: {
              name: {
                color: '#fff',
                backgroundColor: 'transparent'
              },
              percentage: {
                color: '#7EAEF9',
                backgroundColor: 'transparent'
              }
            }
          },
          tooltip: {
            show: true,
            confine: true // 限制tootip在容器内
          },
          formatter: function (name) {
            var oa = option.series[0].data
            var num = oa.reduce((sum, e) => sum + e.value, 0)
            for (var i = 0; i < option.series[0].data.length; i++) {
              if (name === oa[i].name) {
                return ' ' + '{name|' + (name.length > that.getLegendLength() ? name.substr(0, that.getLegendLength()) + '...' : name) + '' + ' (' + oa[i].value + ')}   {percentage|' + ((oa[i].value / num) * 100).toFixed(2) + '%}'
                // return `<span>${name.length > that.getLegendLength() ? name.substr(0, that.getLegendLength()) + '...' : name} </span><span>${((oa[i].value / num) * 100).toFixed(2)}</span>`
              }
            }
          }
        },
        tooltip: {
          trigger: 'item',
          // formatter: '{b} : {c} ({d}%)',
          formatter: function (params) {
            if (params.name) {
              return params.marker + params.name + ' : ' + params.value + '    (' + ((params.value / sum) * 100).toFixed(2) + '%)'
            }
          },
          confine: true // 限制tootip在容器内
        },
        series: [
          {
            type: 'pie',
            radius: ['50%', '70%'],
            center: ['30%', '50%'],
            hoverAnimation: false,
            startAngle: 180,
            selectedMode: 'single',
            label: {
              show: false
            },
            itemStyle: {
              normal: {
                color: function (params) {
                  return colorOut[params.dataIndex]
                }
              }
            },
            data: warnData,
            z: 666
          },
          {
            type: 'pie',
            radius: ['70%', '75%'],
            center: ['30%', '50%'],
            hoverAnimation: false,
            startAngle: 180,
            selectedMode: 'single',
            label: {
              show: false
            },
            itemStyle: {
              normal: {
                color: function (params) {
                  return colorIn[params.dataIndex]
                }
              }
            },
            data: warnData,
            z: 1
          }
        ]
      }
      getchart.clear()
      getchart.setOption(option)
      // 随着屏幕大小调节图表
      window.addEventListener('resize', () => {
        getchart.resize()
      })
    },
    // 开关门次数统计Echarts
    setContorlDoorNumBarEcharts(chartdata) {
      const getchart = echarts.init(document.getElementById('doorNumsBarEcharts'))
      const nameList = chartdata.seriesData
      const valueList = chartdata.yAxisData
      const option = {
        // backgroundColor: '#fff',
        color: [
          new echarts.graphic.LinearGradient(
            0,
            1,
            0,
            0,
            [
              {
                offset: 0,
                color: '#5ADFFF'
              },
              {
                offset: 1,
                color: '#5ADFFF'
              }
            ],
            false
          )
        ],
        grid: {
          left: '0%',
          right: '0%',
          top: '20%',
          bottom: '0%',
          containLabel: true
        },
        tooltip: {
          show: 'true',
          trigger: 'axis',
          axisPointer: {
            type: 'shadow'
          }
        },
        yAxis: {
          type: 'value',
          name: '单位：次',
          axisTick: {
            show: false
          },
          axisLine: {
            show: false,
            lineStyle: {
              color: 'rgba(255, 255, 255, .5)'
            }
          },
          axisLabel: {
            show: true,
            color: 'rgba(255, 255, 255, .5)',
            fontSize: 14
          },
          splitLine: {
            show: true,
            lineStyle: {
              type: 'solid',
              color: ['#fff'],
              opacity: 0.1
            }
          }
        },
        xAxis: [
          {
            axisTick: {
              show: false
            },

            type: 'category',
            axisLine: {
              show: false
            },
            axisLabel: {
              show: true,
              color: 'rgba(255, 255, 255, .5)',
              interval: 0,
              rotate: -30
            },

            splitLine: {
              show: false
            },
            data: nameList
          }
        ],
        series: [
          {
            name: '',
            type: 'bar',
            barWidth: 10,
            label: {
              normal: {
                show: false,
                position: 'right',
                textStyle: {
                  color: '#ccc',
                  fontSize: 14
                }
              }
            },
            data: valueList
          }
        ]
      }
      getchart.clear()
      getchart.setOption(option)
      // 随着屏幕大小调节图表
      window.addEventListener('resize', () => {
        getchart.resize()
      })
    },
    // 偏移趋势Echarts
    setOffsetTrendLineEcharts(chartdata) {
      const getchart = echarts.init(document.getElementById('offsetTrendLineEcharts'))
      const nameList = chartdata.deviationDates
      const valueList = chartdata.aroundDeviations
      const value2List = chartdata.lrDeviations
      const option = {
        tooltip: {
          trigger: 'axis'
        },
        legend: {
          data: ['前后偏移', '左右偏移'],
          right: 5,
          textStyle: {
            color: 'rgba(255, 255, 255, .5)'
          },
          lineStyle: {
            width: 0
          },
          pageTextStyle: {
            color: 'red'
          }
        },
        grid: {
          left: '2%',
          right: '1%',
          bottom: '0%',
          top: '15%',
          containLabel: true
        },
        xAxis: {
          axisTick: {
            show: false
          },
          axisLine: {
            show: false
          },
          splitLine: {
            show: true,
            lineStyle: {
              type: 'solid',
              color: ['#fff'],
              opacity: 0.1
            }
          },
          // 轴线上的字
          axisLabel: {
            show: true,
            textStyle: {
              fontSize: '14',
              color: 'rgba(255, 255, 255, .5)'
            }
          },
          data: nameList
        },
        yAxis: [
          {
            type: 'value',
            splitNumber: 4,
            axisTick: {
              show: false
            },
            // 轴线上的字
            axisLabel: {
              textStyle: {
                fontSize: '14',
                color: 'rgba(255, 255, 255, .5)'
              }
            },
            axisLine: {
              show: false
            },
            splitLine: {
              show: true,
              lineStyle: {
                type: 'solid',
                color: ['#fff'],
                opacity: 0.1
              }
            }
          }
        ],
        series: [
          {
            name: '前后偏移',
            type: 'line',
            smooth: false, // 是否平滑曲线显示
            showAllSymbol: true,
            symbol: 'none',
            // symbolSize: 8,
            itemStyle: {
              color: 'rgba(255, 227, 166, 1)',
              borderColor: '#e3e3e3',
              borderWidth: 1
            },
            lineStyle: {
              normal: {
                width: 2,
                color: {
                  type: 'linear',
                  colorStops: [
                    {
                      offset: 0,
                      color: 'rgba(255, 227, 166, 0.6)' // 0% 处的颜色
                    },
                    {
                      offset: 1,
                      color: 'rgba(255, 227, 166, 0.6)' // 100% 处的颜色
                    }
                  ],
                  globalCoord: false // 缺省为 false
                },
                shadowColor: 'rgba(255, 227, 166, 1))',
                shadowBlur: 30,
                shadowOffsetY: 5
              }
            },
            areaStyle: {
              // 区域填充样式
              normal: {
                color: new echarts.graphic.LinearGradient(
                  0,
                  0,
                  0,
                  1,
                  [
                    {
                      offset: 0,
                      color: 'rgba(255, 227, 166, 0.6)'
                    },
                    {
                      offset: 0.6,
                      color: 'rgba(255, 227, 166, 0.2)'
                    },
                    {
                      offset: 0.8,
                      color: 'rgba(255, 227, 166, 0.01)'
                    }
                  ],
                  false
                ),
                shadowColor: 'rgba(255, 227, 166, 0.1)',
                shadowBlur: 6
              }
            },
            data: valueList
          },
          {
            name: '左右偏移',
            type: 'line',
            smooth: false, // 是否平滑曲线显示
            showAllSymbol: true,
            symbol: 'none',
            // symbolSize: 8,
            itemStyle: {
              color: 'rgba(90, 223, 255, 1)',
              borderColor: '#e3e3e3',
              borderWidth: 1
            },
            lineStyle: {
              normal: {
                width: 2,
                color: {
                  type: 'linear',

                  colorStops: [
                    {
                      offset: 0,
                      color: 'rgba(90, 223, 255, 1)' // 0% 处的颜色
                    },
                    {
                      offset: 1,
                      color: 'rgba(90, 223, 255, 1)' // 100% 处的颜色
                    }
                  ],
                  globalCoord: false // 缺省为 false
                },
                shadowColor: 'rgba(91, 143, 249, .5)',
                shadowBlur: 12,
                shadowOffsetY: 5
              }
            },
            areaStyle: {
              // 区域填充样式
              normal: {
                color: new echarts.graphic.LinearGradient(
                  0,
                  0,
                  0,
                  1,
                  [
                    {
                      offset: 0,
                      color: 'rgba(90, 223, 255, .6)'
                    },
                    {
                      offset: 0.6,
                      color: 'rgba(90, 223, 255, .2)'
                    },
                    {
                      offset: 1,
                      color: 'rgba(90, 223, 255, 0.01)'
                    }
                  ],
                  false
                ),
                shadowColor: 'rgba(90, 223, 255, .4)',
                shadowBlur: 6
              }
            },
            data: value2List
          }
        ]
      }
      getchart.clear()
      getchart.setOption(option)
      // 随着屏幕大小调节图表
      window.addEventListener('resize', () => {
        getchart.resize()
      })
    },
    openCameraTalkDialog() {
      this.cameraTalkDialogShow = true
    },
    backEvent() {
      try {
        window.chrome.webview.hostObjects.sync.bridge.ElevatorBack()
      } catch (error) {}
      // this.$emit('closeElevator')
    }
  }
}
</script>

<style lang="scss" scoped>
.monitorDetails {
  position: relative;
  width: 100%;
  height: 100%;
  background: url('~@/assets/images/elevator/page-bg.png') no-repeat;
  background-size: 100% 100%;
  padding: 10px 20px 4rem 20px;
  box-sizing: border-box;
  .monitorDetails-heade {
    cursor: pointer;
    position: absolute;
    top: 16px;
    left: 19px;
    width: 80px;
    height: 30px;
    line-height: 30px;
    padding-left: 16px;
    text-align: center;
    color: #618ad3;
    font-size: 0.875rem;
    white-space: nowrap;
    background-image: url('~@/assets/images/peace/btn-back.png');
    background-size: 100% 100%;
    background-repeat: no-repeat;
  }
  .content-details {
    height: 100%;
    width: 100%;
    .content_top {
      height: calc(70% - 10px);
      margin-bottom: 10px;
      display: flex;
      .content_top_left {
        width: 14%;
        height: 100%;
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        padding-top: 50px;
        box-sizing: border-box;
        .detail_list {
          font-family: HarmonyOS_Sans_SC;
          height: 20%;
          div {
            height: calc(100% - 50px);
            display: flex;
            flex-direction: column;
            align-items: center;
            box-sizing: border-box;
            overflow: hidden;
            white-space: nowrap;
            text-overflow: ellipsis;
            font-family: PingFang SC-Medium, PingFang SC;
            background: url('~@/assets/images/elevator/elevator_details_1.png') no-repeat;
            background-size: 100% 100%;
            p {
              font-size: 14px;
              margin: 8px 0;
              color: #7EAEF9;
            }
            span {
              font-size: 16px;
              color: #FFFFFF;
            }
          }
        }
      }
      .content_top_center {
        width: 60%;
        height: 100%;
        padding: 50px 70px 0 50px;
        margin-left: 20px;
        box-sizing: border-box;
        background: url('~@/assets/images/elevator/bg_decoration.png') no-repeat;
        background-size: 100% 100%;
        .top_center_top {
          height: 40%;
          width: 100%;
          display: flex;
          > div:first-child {
            width: 20%;
            height: 100%;
            display: flex;
            position: relative;
            img {
              width: 165px;
              height: 165px;
              margin: auto 0;
            }
            &::before {
              width: 28px;
              height: 28px;
              display: block;
              position: absolute;
              background: url('~@/assets/images/elevator/point-big.png') no-repeat;
              background-size: 28px 28px;
              bottom: calc(10%);
              left: 35px;
              z-index: 2;
              content: '';
            }
          }
          > div:last-child {
            width: 80%;
            height: 100%;
            display: flex;
            flex-wrap: wrap;
            justify-content: space-between;
            > div {
              width: calc(20% - 10px);
              height: calc(50% - 12px);
              background: url('~@/assets/images/elevator/bg-blue-select.png') no-repeat;
              background-size: 100% 100%;
              background-position: center;
              display: flex;
              justify-content: space-around;
              flex-direction: column;
              padding: 8px 0;
              box-sizing: border-box;
              p {
                text-align: center;
                font-size: 14px;
                font-family: HarmonyOS_Sans_SC;
                color: #7EAEF9;
                span{
                  color: #fff;
                }
              }
              p:last-child {
                font-size: 18px;
              }
            }
          }
        }
        .top_center_bottom {
          height: calc(60% - 20px);
          margin-top: 20px;
          display: flex;
          padding-left: 5%;
          > div:first-child {
            width: 45%;
            height: 100%;
            display: flex;
            flex-direction: column;
            justify-content: space-between;
            .left_parameter {
              height: calc(25% - 20px);
              display: flex;
              justify-content: space-between;
              > div:first-child {
                width: 30%;
                height: fit-content;
                text-align: center;
                font-size: 15px;
                font-family: HarmonyOS_Sans_SC_Medium;
                color: #888;
                padding: 10px;
                border-bottom: 1px solid #d9dfe8;
                position: relative;
                .right-icon {
                  position: absolute;
                  bottom: calc(0% - 8px);
                  right: -8px;
                  color: #7EAEF9;
                }
                .point_cri {
                  width: 22px;
                  height: 22px;
                  background: url('~@/assets/images/elevator/point-small.png') no-repeat;
                  background-size: 100% 100%;
                  position: absolute;
                  left: -11px;
                  bottom: calc(0% - 11px);
                  z-index: 2;
                }
                .left_line {
                  width: 1px;
                  height: 250%;
                  background: #d9dfe8;
                  position: absolute;
                  left: -1px;
                  bottom: 0;
                }
              }
              > div:last-child {
                width: calc(70% - 20px);
                background: url('~@/assets/images/elevator/bg-rect.png') no-repeat;
                background-size: 100% 100%;
                box-sizing: border-box;
                padding: 5px 20px;
                display: flex;
                div {
                  margin: auto 0;
                  width: 100%;
                  // border-bottom: 1px solid;
                  display: flex;
                  justify-content: space-between;
                  align-items: baseline;
                  padding: 5px 20px 5px 20px;
                  // border渐变线
                  border-image: linear-gradient(270deg, rgba(219, 223, 226, 0), rgba(219, 223, 226, 1)) 1 1;
                  :first-child {
                    font-size: 22px;
                    font-family: HarmonyOS_Sans_SC_Medium;
                    color: #FFE3A6;
                  }
                  :last-child {
                    font-size: 14px;
                    font-family: HarmonyOS_Sans_SC;
                    color: #7EAEF9;
                    margin-left: 20px;
                  }
                }
              }
            }
          }
          > div:last-child {
            width: 55%;
            height: 100%;
            display: flex;
            justify-content: space-between;
            flex-direction: column;
            .top_red_warn {
              background: url('~@/assets/images/elevator/ic-rect-red.png') no-repeat;
              background-size: 100% 100%;
            }
            .bottom_green_warn {
              background: url('~@/assets/images/elevator/ic-rect-green.png') no-repeat;
              background-size: 100% 100%;
            }
            .top_red_warn,
            .bottom_green_warn {
              width: 95%;
              height: calc(50% - 8px);
              aspect-ratio: 73/27;
              display: flex;
              justify-content: space-between;
              padding: 0 25px;
            }
            .red_warn_left,
            .red_warn_right,
            .green_warn_left,
            .green_warn_right {
              display: flex;
              justify-content: space-evenly;
              flex-direction: column;
              text-align: left;
              p {
                font-size: 14px;
                font-family: HarmonyOS_Sans_SC;
                color: #7EAEF9;
              }
              .red_warn_span {
                font-size: 26px;
                font-family: HarmonyOS_Sans_SC_Medium;
                color: #FFFFFF;
              }
              .green_warn_span {
                font-size: 18px;
                font-family: HarmonyOS_Sans_SC_Medium;
                color: #FFFFFF;
              }
            }
            .red_warn_right,
            .green_warn_right {
              text-align: right;
            }
          }
        }
      }
      .content_top_right {
        padding-left: 30px;
        width: 30%;
        height: 100%;
        .top_video_camera {
          height: 55%;
          width: 100%;
          .video_preview {
            height: calc(100% - 0px);
            // height: calc(100% - 50px);
            background: #000;
          }
          .video_tab {
            padding-top: 10px;
          }
          .Intercom_box {
            height: 36px;
            font-size: 14px;
            font-family: PingFang HK-Regular, PingFang HK;
            font-weight: 400;
            color: #fff;
            cursor: pointer;
            background: #1A2F76;
            border-radius: 4px;
            opacity: 1;
            display: flex;
            justify-content: center;
            align-items: center;
            margin-top: 20px;
            span::before{
              content: '';
              margin-right: 5px;
              width: 20px;
              height: 20px;
              background: url('~@/assets/images/elevator/Phone_in_talk.png');
              color: #FFFFFF;
            }
            span{
              display: flex;
              align-items: center;
              color: #FFFFFF;
            }
            svg {
              font-size: 18px;
              vertical-align: middle;
            }
          }
        }
        .bottom_warn_table {
          margin-top: 5%;
          height: 40%;
          width: 100%;
        }
      }
    }
    .content_bottom {
      height: 30%;
      width: 100%;
      display: flex;
      justify-content: space-between;
      .content_bottom_pie {
        width: 20%;
        position: relative;
        .warnTypeCenter {
          position: absolute;
          width: 25%;
          aspect-ratio: 1/1;
          // background: #fff;
          border-radius: 50px;
          // background: linear-gradient(112deg, rgba(81, 136, 252, 0), rgba(81, 136, 252, 1));
          left: 30%;
          top: 50%;
          transform: translateX(-50%) translateY(-50%);
          display: flex;
          img {
            margin: auto;
          }
          &::before {
            content: '';
            display: block;
            position: absolute;
            width: calc(100% - 1px);
            height: calc(100% - 1px);
            border-radius: 50px;
            // background: #fff;
            z-index: -1;
          }
        }
      }
      .content_bottom_col {
        width: 50%;
        padding-left: 20px;
      }
      .content_bottom_line {
        width: 30%;
        padding-left: 30px;
      }
      .echarts_title {
        background-image: url('~@/assets/images/elevator/ba_title_1.png');
        background-repeat: no-repeat;
        background-size: 100% 100%;
        height: 30px;
        display: flex;
        align-items: center;
        padding-left: 40px;
        font-size: 14px;
        font-family: NotoSansHans-Medium, NotoSansHans;
        font-weight: 600;
        color: #B7CFFF;
        line-height: 18px;
      }
      .echarts_title2{
        background-image: url('~@/assets/images/elevator/ba_title_2.png')!important;
      }
      .echart-null {
        margin: 0 auto;
        height: calc(100% - 20px);
        width: 50%;
        text-align: center;
        color: #8a8c8f;
        img {
          max-width: 80%;
          max-height: 80%;
        }
        div {
          font-size: 14px;
        }
      }
      #warnTypePieEcharts,
      #doorNumsBarEcharts,
      #offsetTrendLineEcharts {
        width: 100%;
        height: 100%;
        z-index: 2;
      }
    }
  }
}
</style>
<style lang="scss" scoped>
::v-deep .video_tab {
  .el-tabs__header {
    margin: 0;
    .el-tabs__item:first-child {
      // border-left: 1px solid #d9dfe8;
    }
    .el-tabs__nav {
      border: none !important;
    }
    .el-tabs__nav-next,
    .el-tabs__nav-prev {
      line-height: 35px !important;
    }
    .el-tabs__item {
      margin-right: 10px;
      border: 1px solid #b9b9b9 !important;
      background: #fcfcfc;
      height: 32px !important;
      line-height: 32px !important;
    }
    .is-active {
      background: #f4f8ff;
      border: 1px solid #5188fc !important;
    }
  }
  .el-tabs__nav-wrap {
    border-bottom: none !important;
  }
}
::v-deep .table-center-transfer {
  .el-table__fixed-header-wrapper{
    border-bottom: 1px solid rgba(255, 255, 255, .2) !important;
  }
  .el-table__fixed-header-wrapper .el-table__header thead {
    tr {
      background: center !important;
      .el-table__cell {
        border-right: none !important;
        .cell {
          font-size: 16px;
          font-family: NotoSansHans-Medium, NotoSansHans;
          font-weight: 500;
          color: #FFF;
        }
      }
    }
  }
  .el-table__body tr.hover-row.current-row>td.el-table__cell, .el-table__body tr.hover-row.el-table__row--striped.current-row>td.el-table__cell, .el-table__body tr.hover-row.el-table__row--striped>td.el-table__cell, .el-table__body tr.hover-row>td.el-table__cell{
    background: center;
  }
  // div.el-table{
  //   border: none !important;
  tr {
    background: center!important;;
  }
  // }
  .el-table__row{
    .el-table__cell:nth-child(1){
      background: #1A2F76;
      color: #7EAEF9;
    }
    .el-table__cell:nth-child(3),.el-table__cell:nth-child(4){
      background: #1A2F76;
      color: #FFFFFF;
    }
  }
  .el-table__row:hover {
    .el-table__cell:nth-child(1){
      background: rgba(255, 227, 166, .3) !important;
      color: #FFFFFF;
    }
    .el-table__cell:nth-child(2){
      background: rgba(255, 227, 166, 0) !important;
    }
    .el-table__cell:nth-child(3),.el-table__cell:nth-child(4){
      background: rgba(255, 227, 166, .1) !important;
      color: #FFE3A6;
    }
  }

  .el-table__header .el-table__cell{
    text-align: center;
  }
  .el-table__cell{
    border-bottom: none !important;
  }
  table.el-table__header thead th {
    background: transparent !important;
  }
  .el-table__row {
    display: inline-block;
    margin: 10px 0 !important; /* 设置行的下边距 */
  }
  .el-table__fixed-body-wrapper {
    // border-left: 1px solid #d8dee7;
    // border-right: 1px solid #d8dee7;
    width: calc(100% - 1px);
  }
  table.el-table__body {
  tr.hover-row,
  tr.current-row,
  tr:hover {
    box-sizing: border-box;
    border: none!important;
    border-bottom: 0;
    td.el-table__cell {
      background-color: none!important;
      border-top: none;
      border-bottom: none;
    }
  }
}
}
</style>
