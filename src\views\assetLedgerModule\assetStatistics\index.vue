<template>
  <div class="statistics-view">
    <div class="sic-search-box">
      <div class="sic-search-left">
        <el-form :model="searchForm" class="search-form" inline>
          <el-form-item>
            <el-select
              v-model="searchForm.deptCode"
              size="small"
              placeholder="所属科室"
            >
              <el-option
                v-for="item in deptOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              >
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-select
              v-model="searchForm.brandCode"
              size="small"
              placeholder="品牌"
            >
              <el-option
                v-for="item in brandOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              >
              </el-option>
            </el-select>
          </el-form-item>
        </el-form>
      </div>
      <div class="sic-search-right">
        <el-button class="sino-button-sure" @click="resetForm">重置</el-button>
        <el-button class="sino-button-sure" @click="handleSearchForm"
          >查询</el-button
        >
      </div>
    </div>
    <div class="sic-content">
      <div class="sic-content-5column">
        <div class="sic-content-5column-box">
          <div class="sic-title-box">
            <span>资产状态</span>
          </div>
          <div class="sic-charts-box">
            <div id="pie_status" style="width: 100%; height: 100%"></div>
          </div>
        </div>
        <div class="sic-content-5column-box">
          <div class="sic-title-box">
            <span>资产品牌分布</span>
          </div>
          <div class="sic-charts-box">
            <div id="closePie_brand" style="width: 100%; height: 100%"></div>
          </div>
        </div>
        <div class="sic-content-5column-box">
          <div class="sic-title-box">
            <span>资产使用年限</span>
          </div>
          <div class="sic-charts-box">
            <div id="bar_serviceLife" style="width: 100%; height: 100%"></div>
          </div>
        </div>
        <div class="sic-content-5column-box">
          <div class="sic-title-box">
            <span>设备进口统计</span>
          </div>
          <div class="sic-charts-box">
            <div id="closePie_import" style="width: 100%; height: 100%"></div>
          </div>
        </div>
        <div class="sic-content-5column-box">
          <div class="sic-title-box">
            <span>资产状态排行</span>
          </div>
          <div class="sic-charts-box">
            <div id="bar_assetsStatus" style="width: 100%; height: 100%"></div>
          </div>
        </div>
        <div class="sic-content-5column-box">
          <div class="sic-title-box">
            <span>在保资产统计</span>
          </div>
          <div class="sic-charts-box">
            <div id="pie_insurance" style="width: 100%; height: 100%"></div>
          </div>
        </div>
        <div class="sic-content-5column-box">
          <div class="sic-title-box">
            <span>资产风险等级</span>
          </div>
          <div class="sic-charts-box">
            <div id="pie_riskLevel" style="width: 100%; height: 100%"></div>
          </div>
        </div>
        <div class="sic-content-5column-box">
          <div class="sic-title-box">
            <span>主副设备数量</span>
          </div>
          <div class="sic-charts-box">
            <div
              id="closePie_assetsNum"
              style="width: 100%; height: 100%"
            ></div>
          </div>
        </div>
        <div class="sic-content-5column-box">
          <div class="sic-title-box">
            <span>资产分类统计</span>
          </div>
          <div class="sic-charts-box">
            <div id="closePie_class" style="width: 100%; height: 100%"></div>
          </div>
        </div>
        <div class="sic-content-5column-box">
          <div class="sic-title-box">
            <span>资产分布排行</span>
          </div>
          <div class="sic-charts-box">
            <div id="bar_distribution" style="width: 100%; height: 100%"></div>
          </div>
        </div>
      </div>
      <div class="sic-content-4column">
        <div class="sic-content-4column-box">
          <div class="sic-title-box">
            <span>资产价值统计</span>
          </div>
          <div class="sic-charts-box">
            <div id="closePie_merit" style="width: 100%; height: 100%"></div>
          </div>
        </div>
        <div class="sic-content-4column-box">
          <div class="sic-title-box">
            <span>资产金额占比</span>
          </div>
          <div class="sic-charts-box">
            <div id="bar_sumProportion" style="width: 100%; height: 100%"></div>
          </div>
        </div>
        <div class="sic-content-4column-box">
          <div class="sic-title-box">
            <span>账内资产统计</span>
          </div>
          <div class="sic-charts-box">
            <div id="pie_inAccount" style="width: 100%; height: 100%"></div>
          </div>
        </div>
        <div class="sic-content-4column-box">
          <div class="sic-title-box">
            <span>资产价值排行</span>
          </div>
          <div class="sic-charts-box">
            <div id="bar_merit" style="width: 100%; height: 100%"></div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import * as echarts from "echarts";
import insurance from "@/assets/images/insurance.png";
import assets_icon from "@/assets/images/assets_icon.png";
export default {
  data() {
    return {
      insurance,
      assets_icon,
      searchForm: {},
      deptOptions: [],
      brandOptions: [],
      closePieIdData: [
        "closePie_brand",
        "closePie_import",
        "closePie_assetsNum",
        "closePie_class",
        "closePie_merit",
      ],
      pieIdData: [
        "pie_status",
        "pie_insurance",
        "pie_riskLevel",
        "pie_inAccount",
      ],
      barIdData: [
        "bar_serviceLife",
        "bar_distribution",
        "bar_sumProportion",
        "bar_merit",
      ],
    };
  },
  // 监听全屏缩放 动态改变echarts图表大小
  watch: {
    "$store.state.dialogFullScreenState": {
      handler(val) {
        this.$nextTick(() => {
          setTimeout(() => {
            this.pieIdData.forEach((item) => {
              this.initPie(item);
            });
            this.closePieIdData.forEach((item) => {
              this.initClosePie(item);
            });
            this.barIdData.forEach((item) => {
              this.initBarEchart(item);
            });
            this.initStackBarChart();
          }, 200);
        });
      },
      deep: true,
    },
  },
  mounted() {
    this.pieIdData.forEach((item) => {
      this.initPie(item);
    });
    this.closePieIdData.forEach((item) => {
      this.initClosePie(item);
    });
    this.barIdData.forEach((item) => {
      this.initBarEchart(item);
    });
    this.initStackBarChart();
  },
  methods: {
    /** 重置 */
    resetForm() {},
    /** 查询 */
    handleSearchForm() {},
    /** 初始化饼图 */
    initPie(item) {
      let _this = this;
      var chartDom = document.getElementById(item);
      var myChart = echarts.init(chartDom);
      var option;
      let dataObj = {
        pie_status: [
          { value: 111, name: "使用中", color: "#FFCA64" },
          { value: 12, name: "闲置中", color: "#61E29D" },
          { value: 6, name: "维修中", color: "#0A84FF" },
          { value: 4, name: "已报废", color: "#FFCAD4" },
          { value: 30, name: "已外借", color: "#8BDDF5" },
          { value: 33, name: "已冲红", color: "#FF2D55" },
        ],
        pie_insurance: [
          { value: 111, name: "在保", color: "#61E29D" },
          { value: 12, name: "未保", color: "#FF2D55" },
        ],
        pie_riskLevel: [
          { value: 36, name: "低风险", color: "#61E29D" },
          { value: 30, name: "中风险", color: "#FFCA64" },
          { value: 4, name: "高风险", color: "#FF2D55" },
        ],
        pie_inAccount: [
          { value: 111, name: "账内资产", color: "#61E29D" },
          { value: 12, name: "账外资产", color: "#FF2D55" },
        ],
      };
      let titleObj = {
        pie_status: { name: "资产总数", value: "3429", img: "" },
        pie_insurance: { name: "", value: "", img: insurance },
        pie_riskLevel: { name: "风险等级", value: "129", img: "" },
        pie_inAccount: { name: "", value: "", img: assets_icon },
      };
      option = {
        color: dataObj[item].map((item) => item.color),
        title: [
          {
            text: titleObj[item].name ? titleObj[item].name : "{img|}",
            x: "center",
            top: "50%",
            textStyle: {
              color: "#FFFFFF",
              fontSize: 12,
              fontWeight: "100",
              rich: {
                img: {
                  backgroundColor: {
                    image: titleObj[item] ? titleObj[item].img : "", // 图片的 URL
                  },
                  width: 25,
                  height: 25,
                },
              },
            },
          },
          {
            text: titleObj[item].value ? titleObj[item].value : "",
            x: "center",
            top: "60%",
            textStyle: {
              fontSize: "12",
              color: "#FFFFFF",
              foontWeight: "600",
            },
          },
        ],
        tooltip: {
          trigger: "item",
          formatter: function (params) {
            let marker = params.marker || "",
              name = params.name || "",
              value = params.value || "",
              percent = params.percent || "0";
            let spanVal = `<span style="padding: 0px 10px 0 5px;font-weight: bolder;color: #fff;">${value}</span>`;
            return `${marker}${name}${spanVal}${percent}%`;
          },
          backgroundColor: '#0D2169',
          borderColor: '#727382',
          textStyle: {
            color: '#fff'
          },
        },
        legend: {
          top: "0%",
          left: "5%",
          textStyle: {
            color: "#fff",
            fontSize: 12,
          },
          itemWidth: 8,
          itemHeight: 8,
          itemGap: 5,
          selectedMode: false,
        },
        series: [
          {
            name: "",
            type: "pie",
            center: ["50%", "60%"],
            radius: ["50%", "70%"],
            avoidLabelOverlap: false,
            label: {
              show: false,
              position: "center",
            },
            emphasis: {
              label: {
                show: false,
                fontWeight: "bold",
              },
            },
            labelLine: {
              show: false,
            },
            data: dataObj[item],
          },
          {
            type: "gauge",
            center: ["50%", "60%"], // 饼状图的位置
            radius: "70%",
            // 如何是版本是4 这里是359.999；不能是360；否则圆环不能正常显示
            // 如果是版本是5，这里可以是360
            startAngle: 360,
            endAngle: 0,
            splitNumber: 25,
            zlevel: 10,
            axisLine: {
              show: false,
            },
            splitLine: {
              show: true, // 是否显示分隔线。 如果设置为true,外层虚线才能看见
              length: 1, // 分隔线与轴线的距离。这里表现是虚线的宽度
              lineStyle: {
                width: 5, // 分隔线线长。支持相对半径的百分比。
                color: "rgba(230,247,255,0.2)", // 线的颜色
              },
            },
            axisLabel: {
              //刻度标签
              show: false,
            },
            axisTick: {
              //刻度样式
              show: false,
            },
            detail: {
              show: false,
            },
            //仪表盘指针。
            pointer: {
              // 不显示仪表盘中的指针
              show: false,
            },
            data: [],
          },
        ],
      };
      myChart.resize();
      myChart.clear();
      option && myChart.setOption(option);
      myChart.on("click", function (params) {
        _this.$emit("openDetailComponent", "assetsListIndex");
      });
      window.addEventListener("resize", () => {
        myChart.resize();
      });
    },
    /** 初始化闭合饼图 */
    initClosePie(item) {
      let _this = this;
      var chartDom = document.getElementById(item);
      var myChart = echarts.init(chartDom);
      var option;
      let dataObj = {
        closePie_brand: [
          { value: 1048, name: "品牌名1" },
          { value: 735, name: "品牌名2" },
          { value: 580, name: "品牌名3" },
          { value: 484, name: "品牌名4" },
          { value: 300, name: "品牌名5" },
        ],
        closePie_import: [
          { value: 300, name: "国产" },
          { value: 735, name: "进口" },
          { value: 580, name: "其他" },
        ],
        closePie_assetsNum: [
          { value: 1048, name: "主设备" },
          { value: 735, name: "副设备" },
        ],
        closePie_class: [
          { value: 1048, name: "特种设备" },
          { value: 735, name: "检验类设备" },
          { value: 580, name: "灭菌类设备" },
          { value: 484, name: "辐射类设备" },
          { value: 300, name: "中医诊疗类设备" },
        ],
        closePie_merit: [
          { value: 1048, name: "低值资产" },
          { value: 735, name: "大型资产" },
          { value: 580, name: "其他资产" },
        ],
      };
      option = {
        tooltip: {
          trigger: "item",
          backgroundColor: '#0D2169',
          borderColor: '#727382',
          textStyle: {
            color: '#fff'
          },
        },
        legend: {
          top: "0%",
          left: "5%",
          textStyle: {
            color: "#fff",
            fontSize: 12,
          },
          itemWidth: 8,
          itemHeight: 8,
          itemGap: 5,
          selectedMode: false,
        },
        series: [
          {
            name: "",
            type: "pie",
            radius: "70%",
            center: ["50%", "60%"],
            data: dataObj[item],
            label: {
              show: false,
            },
            emphasis: {
              label: {
                show: false,
              },
            },
          },
        ],
      };
      myChart.resize();
      myChart.clear();
      option && myChart.setOption(option);
      myChart.on("click", function (params) {
        _this.$emit("openDetailComponent", "assetsListIndex");
      });
      window.addEventListener("resize", () => {
        myChart.resize();
      });
    },
    /** 初始化 柱状图 */
    initBarEchart(item) {
      let _this = this;
      var chartDom = document.getElementById(item);
      var myChart = echarts.init(chartDom);
      var option;
      let dataObj = {
        bar_serviceLife: [
          { value: 1048, name: "3年以下" },
          { value: 735, name: "3-5年" },
          { value: 580, name: "5-8年" },
          { value: 484, name: "8年以上" },
          { value: 300, name: "其他" },
        ],
        bar_distribution: [
          { value: 735, name: "科室名称" },
          { value: 580, name: "科室名称" },
          { value: 560, name: "科室名称" },
          { value: 480, name: "科室名称" },
          { value: 450, name: "科室名称" },
          { value: 300, name: "科室名称" },
        ],
        bar_sumProportion: [
          { value: 1048, name: "5千以上" },
          { value: 735, name: "5千-1万" },
          { value: 600, name: "1-10万" },
          { value: 300, name: "10万以上" },
          { value: 100, name: "其他" },
        ],
        bar_merit: [
          { value: 1048, name: "科室名称" },
          { value: 735, name: "科室名称" },
          { value: 580, name: "科室名称" },
          { value: 484, name: "科室名称" },
          { value: 300, name: "科室名称" },
        ],
      };
      option = {
        tooltip: {
          trigger: "axis",
          axisPointer: {
            type: "shadow",
          },
          backgroundColor: '#0D2169',
          borderColor: '#727382',
          textStyle: {
            color: '#fff'
          },
        },
        legend: {},
        grid: {
          top: "0%",
          left: "3%",
          right: "8%",
          bottom: "3%",
          containLabel: true,
        },
        xAxis: {
          type: "value",
          axisLine: {
            show: true,
            lineStyle: {
              color: ["rgba(233, 233, 233,0.1)"], //分割线的颜色
              type: "dashed",
            },
          },
          interval: "auto",
          axisLabel: {
            textStyle: {
              color: "rgba(230,247,255,0.6)", //坐标值的具体的颜色
            },
          },
          splitLine: {
            show: true,
            lineStyle: {
              color: ["rgba(233, 233, 233,0.1)"], //分割线的颜色
              type: "dashed",
            },
          },
        },
        yAxis: {
          type: "category",
          data: dataObj[item].map((item) => item.name),
          axisLine: {
            show: true,
            lineStyle: {
              color: ["rgba(233, 233, 233,0.1)"], //分割线的颜色
              type: "dashed",
            },
          },
          axisLabel: {
            textStyle: {
              color: "rgba(230,247,255,0.6)", //坐标值的具体的颜色
            },
          },
          splitLine: {
            show: true,
            lineStyle: {
              color: ["rgba(233, 233, 233,0.1)"], //分割线的颜色
              type: "dashed",
            },
          },
        },
        series: [
          {
            name: "",
            type: "bar",
            data: dataObj[item].map((item) => item.value).reverse(),
            barWidth: 10,
            itemStyle: {
              normal: {
                color: new echarts.graphic.LinearGradient(0, 0, 1, 0, [
                  { offset: 0, color: "#00E4FF" },
                  { offset: 1, color: "#00B4FF" },
                ]),
              },
            },
          },
        ],
      };

      myChart.resize();
      myChart.clear();
      option && myChart.setOption(option);
      myChart.on("click", function (params) {
        _this.$emit("openDetailComponent", "assetsListIndex");
      });
      window.addEventListener("resize", () => {
        myChart.resize();
      });
    },
    /** 初始化 堆叠柱状图 */
    initStackBarChart() {
      let _this = this;
      var chartDom = document.getElementById("bar_assetsStatus");
      var myChart = echarts.init(chartDom);
      var option;

      option = {
        legend: {
          textStyle: {
            color: "#fff",
            fontSize: 12,
          },
          itemWidth: 8,
          itemHeight: 8,
          itemGap: 5,
          selectedMode: false,
        },
        tooltip: {
          trigger: "axis",
          axisPointer: {
            type: "shadow",
          },
          itemSize: 12,
          itemGap: 5,
          backgroundColor: '#0D2169',
          borderColor: '#727382',
          textStyle: {
            color: '#fff'
          },
        },
        grid: {
          top: "25%",
          left: "3%",
          right: "10%",
          bottom: "3%",
          containLabel: true,
        },
        xAxis: {
          type: "value",
          interval: "auto",
          axisLine: {
            show: false,
          },
          splitLine: {
            show: false,
          },
        },
        yAxis: {
          type: "category",
          data: [
            "麻醉机",
            "CT",
            "核磁机",
            "腹腔镜系统",
            "宫腔镜系统",
            "显微镜",
            "吸氧机",
          ],
          axisLine: {
            show: true,
          },
          splitLine: {
            show: false,
          },
          axisLabel: {
            textStyle: {
              color: "rgba(230,247,255,0.6)", //坐标值的具体的颜色
            },
          },
        },
        series: [
          {
            name: "使用中",
            type: "bar",
            stack: "total",
            label: {
              show: false,
            },
            emphasis: {
              focus: "series",
            },
            data: [5, 7, 9, 10, 15, 20, 25],
          },
          {
            name: "闲置中",
            type: "bar",
            stack: "total",
            label: {
              show: false,
            },
            emphasis: {
              focus: "series",
            },
            data: [5, 7, 9, 10, 15, 20, 25],
          },
          {
            name: "维修中",
            type: "bar",
            stack: "total",
            label: {
              show: false,
            },
            emphasis: {
              focus: "series",
            },
            data: [5, 7, 9, 10, 15, 20, 25],
          },
          {
            name: "已报废",
            type: "bar",
            stack: "total",
            label: {
              show: false,
            },
            emphasis: {
              focus: "series",
            },
            data: [5, 7, 9, 10, 15, 20, 25],
          },
          {
            name: "已外借",
            type: "bar",
            stack: "total",
            label: {
              show: false,
            },
            emphasis: {
              focus: "series",
            },
            data: [5, 7, 9, 10, 15, 20, 25],
          },
          {
            name: "已冲红",
            type: "bar",
            stack: "total",
            label: {
              show: false,
            },
            emphasis: {
              focus: "series",
            },
            data: [5, 7, 9, 10, 15, 20, 25],
          },
          {
            name: "",
            type: "bar",
            stack: "total",
            barGap: "-100%",
            label: {
              show: true,
              position: "right",
              formatter: function (params) {
                return `${params.value}台`;
              },
              fontSize: 12,
              color: "#DADEE1",
            },
            emphasis: {
              focus: "series",
            },
            data: [35, 49, 63, 70, 105, 140, 175],
          },
        ],
      };

      myChart.resize();
      myChart.clear();
      option && myChart.setOption(option);
      myChart.on("click", function (params) {
        _this.$emit("openDetailComponent", "assetsListIndex");
      });
      window.addEventListener("resize", () => {
        myChart.resize();
      });
    },
  },
};
</script>
<style lang="scss" scoped>
%sic-common-styles {
  flex-shrink: 0;
  margin-right: 1rem;
  box-sizing: border-box;
  background: rgba(53, 98, 219, 0.06);
}
.statistics-view {
  height: 100%;
  padding: 0px 40px;
  .sic-search-box {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 1.25rem;
  }
  .sic-content {
    height: calc(100% - 60px);
    width: 100%;
    .sic-content-5column {
      width: 100%;
      height: 66%;
      display: flex;
      flex-wrap: wrap;
      &-box {
        @extend %sic-common-styles;
        width: calc(20% - 1rem);
        height: calc(50% - 1rem);
        margin-bottom: 1rem;
        display: flex;
        flex-direction: column;
        .sic-charts-box {
          flex: 1;
        }
      }
      &-box:nth-child(5n) {
        margin-right: 0px;
      }
    }
    .sic-content-4column {
      height: 33%;
      display: flex;
      flex-wrap: wrap;
      &-box {
        width: calc(25% - 1rem);
        height: 100%;
        @extend %sic-common-styles;
        display: flex;
        flex-direction: column;
        .sic-charts-box {
          flex: 1;
        }
      }
    }
    .sic-title-box {
      padding: 0.75rem;
      font-size: 0.875rem;
    }
  }
}
.el-form-item {
  margin-bottom: 0px;
}
</style>