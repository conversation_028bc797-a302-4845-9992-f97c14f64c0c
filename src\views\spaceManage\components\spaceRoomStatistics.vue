<template>
  <div class="dialog-content">
    <!-- 空间统计弹窗 -->
    <el-dialog :title="dialogTitle" :visible.sync="dialogVisible" width="60.1042%" :before-close="handleClose" class="dialog-url" :show-close="false">
      <div class="dialog-right">
        <div class="dialog-fx" @click="statisticalExport"></div>
        <div class="dialog-tc" @click="handleClose"></div>
      </div>
      <div class="dialog-div">
        <el-table :data="tableData" style="width: 100%" v-loading="tableLoading">
          <!-- <el-table-column prop="roomCode" label="房间号" min-width="122" show-overflow-tooltip> </el-table-column> -->
          <el-table-column prop="ssmName" label="房间名称" min-width="122" show-overflow-tooltip> </el-table-column>
          <el-table-column prop="buildName" label="楼栋" min-width="122" show-overflow-tooltip> </el-table-column>
          <el-table-column prop="floorName" label="楼层" min-width="122" show-overflow-tooltip> </el-table-column>
          <el-table-column prop="dmName" label="部门" min-width="122" show-overflow-tooltip> </el-table-column>
          <el-table-column prop="functionDictName" label="用途" min-width="122" show-overflow-tooltip> </el-table-column>
          <el-table-column prop="area" label="建筑面积（m²）" min-width="123" show-overflow-tooltip> </el-table-column>
          <el-table-column prop="useArea" label="使用面积（m²）" min-width="123" show-overflow-tooltip> </el-table-column>
          <el-table-column prop="publicArea" label="公区面积（m²）" min-width="123" show-overflow-tooltip> </el-table-column>
        </el-table>
      </div>
      <div class="dialog-foot">
        <div class="foot-zs">共{{ total }}条</div>
        <div>
          <el-pagination layout="prev, pager, next" :total="total" :page-size="pageSize" @current-change="handleCurrentChange"> </el-pagination>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getSpaceInfoPageByModelCode, exportSpaceInfoPageByModelCode } from '@/utils/spaceManage.js'
export default {
  name: '',
  components: {},
  props: {
    dialogVisible: {
      type: Boolean,
      default: false
    },
    modelCode: {
      type: String,
      default: ''
    },
    dialogTitle: {
      type: String,
      default: ''
    },
    spaceState: {
      type: String,
      default: ''
    },
    paramsData: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      tableData: [],
      //   分页
      currentPage: 1,
      pageSize: 10,
      total: 0,
      tableLoading: false
    }
  },
  created() {
    this.getSpaceInfoPageByModelCode()
  },
  mounted() {},
  methods: {
    // 推出
    handleClose() {
      this.$emit('dialogVisible')
    },
    // 导出
    statisticalExport() {
      let params = {
        current: this.currentPage, // 当前页
        // haveModel: 0,
        // deptId: '', // 部门id
        // fullCode: '', // 空间全编码
        // functionDictId: '', // 功能字典id
        modelCode: this.modelCode, // 模型编码
        // publicSpace: '', //公共区域的查询条件：输入字符：publicSpace，目前没有值返回空列表
        size: this.pageSize, // 每页条数
        spaceState: this.spaceState // 空间状态：1使用中，0闲置
      }
      exportSpaceInfoPageByModelCode(params).then((res) => {
        if (res.data) {
          // 将文件流转成文件下载
          const blob = new Blob([res.data])
          let downloadElement = document.createElement('a')
          let href = window.URL.createObjectURL(blob)
          downloadElement.href = href
          downloadElement.download = decodeURIComponent(res.headers.filename) //设置下载的文件名
          document.body.appendChild(downloadElement)
          downloadElement.click()
          document.body.removeChild(downloadElement) //下载完成移除元素
        } else {
          //   this.$message({
          //     message: '导出失败',
          //     type: 'error'
          //   })
        }
      })
    },
    // 获取列表
    getSpaceInfoPageByModelCode() {
      this.tableLoading = true
      let params = {
        current: this.currentPage, // 当前页
        // haveModel: 0,
        // deptId: '', // 部门id
        // fullCode: '', // 空间全编码
        // functionDictId: '', // 功能字典id
        modelCode: this.modelCode, // 模型编码
        // publicSpace: '', //公共区域的查询条件：输入字符：publicSpace，目前没有值返回空列表
        size: this.pageSize, // 每页条数
        spaceState: this.spaceState, // 空间状态：1使用中，0闲置
        ...this.paramsData
      }
      getSpaceInfoPageByModelCode(params).then((res) => {
        const { data } = res
        if (data.code === 200) {
          this.tableLoading = false
          this.tableData = data.data.records
          this.total = data.data.total
        } else {
          this.tableLoading = false
        }
      })
    },
    handleCurrentChange(val) {
      this.currentPage = val
      this.getSpaceInfoPageByModelCode()
    }
  },
  computed: {},
  watch: {}
}
</script>
<style lang="scss" scoped>
.dialog-content {
  position: relative;
  .dialog-url {
    ::v-deep .el-dialog {
      height: 603px;
      background: url('@/assets/images/qhdsys/bg-tc.png') no-repeat;
      .el-dialog__header {
        padding: 11px 20px 10px;
        text-align: center;
        .el-dialog__title {
          height: 20px;
          font-size: 18px;
          font-family: Source Han Sans CN-Regular, Source Han Sans CN;
          font-weight: 400;
          color: #cbdeed;
          line-height: 20px;
        }
      }
    }
    .dialog-div {
      padding: 0 65px;
      ::v-deep .el-table {
        border: 1px solid #203254 !important;
        .el-table__header-wrapper {
          .el-table__header {
            .has-gutter {
              tr {
                background: rgba(133, 145, 206, 0.15);
                // border-bottom: 2px solid #ffffff;
                th {
                  padding: 0;
                  height: 44px;
                  .cell {
                    font-size: 14px;
                    font-family: HarmonyOS Sans SC-Medium, HarmonyOS Sans SC;
                    font-weight: 500;
                    color: #8bddf5;
                  }
                }
              }
            }
          }
        }
        .el-table__body-wrapper {
          background: transparent;
          height: 360px;
          overflow: hidden;
          overflow-y: auto;
          .el-table__body {
            tbody {
              .el-table__row {
                background: transparent;
                border: 0;
                td {
                  padding: 0;
                  height: 40px;
                  border: 0;
                  .cell {
                    font-size: 14px;
                    font-family: HarmonyOS Sans SC-Regular, HarmonyOS Sans SC;
                    font-weight: 400;
                    color: #ffffff;
                  }
                }
              }
              .el-table__row:nth-child(2n - 1) {
                background: rgba(168, 172, 171, 0.08);
              }
              .el-table__row:hover {
                border: 0;
                opacity: 1;
                cursor: pointer;
                td div {
                  color: rgba(255, 202, 100, 1);
                }
              }
            }
          }
        }
      }
    }
    .dialog-foot {
      padding: 0 65px;
      margin-top: 41px;
      display: flex;
      align-items: center;
      justify-content: space-between;
      .foot-zs {
        font-size: 14px;
        font-family: HarmonyOS Sans SC-Regular, HarmonyOS Sans SC;
        font-weight: 400;
        color: #ffffff;
        line-height: 14px;
      }
      ::v-deep .el-pagination {
        .btn-prev {
          background: transparent;
          .el-icon {
            color: #ffffff;
          }
        }
        .btn-next {
          background: transparent;
          .el-icon {
            color: #ffffff;
          }
        }
      }
    }
    .dialog-right {
      position: absolute;
      top: 17px;
      right: 60px;
      display: flex;
      .dialog-fx {
        width: 36px;
        height: 36px;
        margin-right: 8px;
        background: url('@/assets/images/qhdsys/export.png') no-repeat;
      }
      .dialog-fx:hover {
        cursor: pointer;
      }
      .dialog-tc {
        width: 36px;
        height: 36px;
        background: url('@/assets/images/qhdsys/bg-icon-gb.png') no-repeat;
      }
      .dialog-tc:hover {
        cursor: pointer;
      }
    }
  }
}
::v-deep .el-loading-mask {
  background: rgb(13, 27, 54, 0.9) !important;
}
</style>
