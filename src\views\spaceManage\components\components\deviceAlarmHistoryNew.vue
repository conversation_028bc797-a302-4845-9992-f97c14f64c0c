<template>
  <div class="deviceAlarmHistory">
    <div class="search-box">
      <el-date-picker
        v-model="dateRange"
        type="daterange"
        unlink-panels
        popper-class="date-style"
        range-separator="至"
        start-placeholder="开始日期"
        end-placeholder="结束日期"
        value-format="yyyy-MM-dd"
        @change="changeDataEvent"
      >
      </el-date-picker>
      <div class="date-type">
        <p v-for="item in dateTypeList" :key="item.value" class="type-item" :class="{'active-type': dateType == item.value}" @click="activeTabEvent(item.value)">{{ item.name }}</p>
      </div>
    </div>
    <div style="flex: 1;">
      <el-table
        v-loading="tableLoading"
        :data="tableData"
        height="calc(100%)"
        :cell-style="$tools.setCell(3)"
        :header-cell-style="$tools.setHeaderCell(3)"
        style="width: 100%"
        element-loading-background="rgba(0, 0, 0, 0.2)"
      >
        <el-table-column prop="incidentName" label="事件类型" show-overflow-tooltip></el-table-column>
        <el-table-column prop="alarmObjectName" label="对象"></el-table-column>
        <el-table-column prop="alarmStartTime" label="报警时间" width="180"></el-table-column>
        <el-table-column prop="alarmLevel" label="报警等级">
          <span slot-scope="scope" class="alarmLevel" :style="{ color: alarmLevelItem[scope.row.alarmLevel].color }">
            {{ alarmLevelItem[scope.row.alarmLevel].text }}
          </span>
        </el-table-column>
        <el-table-column prop="alarmSpaceName" label="位置" show-overflow-tooltip></el-table-column>
        <el-table-column prop="alarmSource" label="报警来源"></el-table-column>
        <el-table-column v-if="isDispose" prop="alarmStatus" label="报警处理状态">
          <div slot-scope="scope" :style="{ color: scope.row.alarmStatus == 0 ? '#FA403C' : scope.row.alarmStatus == 1 ? '#fd9434' : '#999' }">
            <span class="alarmStatusIcon" :style="{ background: scope.row.alarmStatus == 0 ? '#FA403C' : scope.row.alarmStatus == 1 ? '#ff9435' : '#999' }"></span>
            {{ scope.row.alarmStatus == 0 ? '未处理' : scope.row.alarmStatus == 1 ? '处理中' : '已关闭' }}
          </div>
        </el-table-column>
        <el-table-column v-if="isDispose" prop="alarmAffirm" label="警情确认">
          <template slot-scope="scope">{{ alarmAffirmItem[scope.row.alarmAffirm] || '-' }}</template>
        </el-table-column>
      </el-table>
    </div>
    <el-pagination
      :current-page="currentPage"
      :page-sizes="[15, 20, 30, 40]"
      :page-size="pageSize"
      layout="total, sizes, prev, pager, next, jumper"
      :total="total"
      class="pagination"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
    ></el-pagination>
  </div>
</template>

<script>
import { GetAlarmRecord } from '@/utils/centerScreenApi'
import moment from 'moment'
moment.locale('zh-cn')
export default {
  name: 'deviceAlarmHistory',
  props: {
    deviceId: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      tableLoading: false,
      tableData: [],
      dateRange: [],
      dateType: 1,
      dateTypeList: [
        { name: '今天', value: 1 },
        { name: '近7天', value: 2 },
        { name: '近30天', value: 3 },
        { name: '本年', value: 4 }
      ],
      alarmLevelItem: {
        0: { text: '通知', color: '#61E29D' },
        1: { text: '一般', color: '#61E29D' },
        2: { text: '紧急', color: '#FF9A2E' },
        3: { text: '重要', color: '#FF2D55' }
      },
      alarmAffirmItem: { 0: '未确认', 1: '真实报警', 2: '误报', 3: '演练', 4: '调试' },
      currentPage: 1,
      pageSize: 15,
      total: 0,
      isDispose: __PATH.VUE_APP_HOSPITAL_NODE != 'sjydkqyy'
    }
  },
  computed: {

  },
  created() {
    this.activeTabEvent(1)
  },
  methods: {
    activeTabEvent(val) {
      const dateList = {
        1: [moment().startOf('day').format('YYYY-MM-DD'), moment().format('YYYY-MM-DD')],
        2: [moment().subtract(6, 'days').format('YYYY-MM-DD'), moment().format('YYYY-MM-DD')],
        3: [moment().subtract(29, 'days').format('YYYY-MM-DD'), moment().format('YYYY-MM-DD')],
        4: [moment().startOf('year').format('YYYY-MM-DD'), moment().endOf('year').format('YYYY-MM-DD')]
      }
      this.dateType = val
      this.dateRange = dateList[val]
      this.getAlarmRecord()
    },
    changeDataEvent() {
      this.dateType = 0
      this.getAlarmRecord()
    },
    getAlarmRecord() {
      const param = {
        objectId: this.deviceId,
        pageNo: this.currentPage,
        pageSize: this.pageSize,
        startTime: this.dateRange[0],
        endTime: this.dateRange[1]
      }
      this.tableLoading = true
      GetAlarmRecord(param).then(res => {
        this.tableLoading = false
        if (res.data.code == 200) {
          this.tableData = res.data.data.records
          this.total = res.data.data.total
        }
      })
    },
    handleSizeChange(val) {
      this.pageSize = val
      this.getAlarmRecord()
    },
    handleCurrentChange(val) {
      this.currentPage = val
      this.getAlarmRecord()
    }
  }
}

</script>

<style lang="scss" scoped>
.deviceAlarmHistory {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  .search-box {
    padding: 8px 0px 24px 0px;
    display: flex;
    align-items: center;
    .date-type {
      margin-left: 15px;
      display: flex;
      .type-item {
        cursor: pointer;
        padding: 8px 12px;
        font-weight: 400;
        font-size: 14px;
        color: #B0E3FA;
        margin-right: 8px;
        border: 1px solid transparent;
        background: linear-gradient( 90deg, rgba(10,132,255,0) 0%, rgba(10,132,255,0) 100%), rgba(255,255,255,0.1);
      }
      .active-type {
        color: #8BDDF5;
        background: linear-gradient( 90deg, rgba(10,132,255,0.4) 0%, rgba(10,132,255,0) 100%), rgba(255,255,255,0.1);
        border: 1px solid;
        border-image: radial-gradient(circle, rgba(171, 240, 255, 1), rgba(226, 254, 255, 1), rgba(132, 196, 203, 1), rgba(48, 151, 166, 0)) 1 1;
      }
    }
  }
  ::v-deep(.el-date-editor) {
    width: 300px;
    height: 35px;
    background-color: #14233e;
    border-color: rgba(133, 145, 206, 0.5);
    .el-input__icon {
      transform: translateY(-2px);
    }
    .el-range-input {
      background-color: #14233e;
      color: rgba(255, 255, 255, 0.8);
    }
    .el-range-separator {
      color: rgba(255, 255, 255, 0.8);
      transform: translateY(-2px);
    }
  }

}
</style>
