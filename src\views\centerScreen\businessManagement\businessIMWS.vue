<template>
  <div class="content">
    <div class="sham-content"></div>
    <div class="right-content">
      <div class="echarts-top">
        <div class="bg-title" style="padding: 0 3rem"><span>医废处置动态</span><span class="right-title-unit">单位：kg</span></div>
        <div class="bg-content">
          <div class="alarm-analysis">
            <div class="case-img">
              <img src="../../../assets/images/peace/histogram.png" />
              <div class="case-num" style="color: #ffe3a6">
                <p>{{ disposeData.waitWeigh }}</p>
                <p style="font-size: 0.75rem; margin-top: 0.8rem">待入库</p>
              </div>
              <div class="case-anim-icon"></div>
            </div>
            <div class="case-img">
              <img src="../../../assets/images/peace/histogram.png" />
              <div class="case-num" style="color: #ffe3a6">
                <p>{{ disposeData.confirmWeigh }}</p>
                <p style="font-size: 0.75rem; margin-top: 0.8rem">入库待确认</p>
              </div>
              <div class="case-anim-icon"></div>
            </div>
            <div class="case-img">
              <img src="../../../assets/images/peace/histogram.png" />
              <div class="case-num" style="color: #ffe3a6">
                <p>{{ disposeData.stockInWeigh }}</p>
                <p style="font-size: 0.75rem; margin-top: 0.8rem">已入库</p>
              </div>
              <div class="case-anim-icon"></div>
            </div>
          </div>
          <div class="alarm-analysis">
            <div class="case-img">
              <img src="../../../assets/images/peace/histogram.png" />
              <div class="case-num" style="color: #ff6b6b">
                <p>{{ disposeData.timeoutWeigh }}</p>
                <p style="font-size: 0.75rem; margin-top: 0.8rem">超时预警</p>
              </div>
              <div class="case-anim-icon"></div>
            </div>
            <div class="case-img">
              <img src="../../../assets/images/peace/histogram.png" />
              <div class="case-num" style="color: #7bffc1">
                <p>{{ disposeData.issuedWeigh }}</p>
                <p style="font-size: 0.75rem; margin-top: 0.8rem">已出库</p>
              </div>
              <div class="case-anim-icon"></div>
            </div>
            <div class="case-img">
              <img src="../../../assets/images/peace/histogram.png" />
              <div class="case-num" style="color: #cfe1f7">
                <p>{{ disposeData.processEndWeigh }}</p>
                <p style="font-size: 0.75rem; margin-top: 0.8rem">累计处理</p>
              </div>
              <div class="case-anim-icon"></div>
            </div>
          </div>
        </div>
      </div>
      <div class="echarts-center">
        <div class="bg-title" style="padding: 0 1rem 0 3rem">
          <span>医废基本情况分析</span><span class="center-legend"></span
          ><span class="right-search"
            ><span @click="activeAnalysisEvent('#deptWeek', 'week')" id="deptWeek">本周</span><i>|</i><span @click="activeAnalysisEvent('#deptMonth', 'month')" id="deptMonth">本月</span><i>|</i
            ><span id="deptYear" @click="activeAnalysisEvent('#deptYear', 'year')">本年</span></span
          >
        </div>
        <div class="bg-content">
          <div v-if="analysisShow" class="center-center">暂无数据</div>
          <div v-if="!analysisShow" class="basic-bg"></div>
          <div v-if="!analysisShow" id="BasicAnalysisEcharts"></div>
        </div>
      </div>
      <div class="echarts-bottom">
        <div class="bg-title" style="padding: 0 1rem 0 3rem">
          <span>科室产生医废Top5</span><span class="center-legend"></span
          ><span class="right-search"
            ><span @click="activeTop5Event('#top5Week', 'week')" id="top5Week">本周</span><i>|</i><span @click="activeTop5Event('#top5Month', 'month')" id="top5Month">本月</span><i>|</i
            ><span id="top5Year" @click="activeTop5Event('#top5Year', 'year')">本年</span></span
          >
        </div>
        <div class="bg-content">
          <div v-if="deptTopShow" class="center-center">暂无数据</div>
          <div v-else style="width: 100; height: 100%"><div id="deptProduceEcharts"></div></div>
        </div>
      </div>
    </div>
    <div class="bottom-content">
      <div class="bg-title">
        <span>医废产生趋势图</span>
        <span class="title-unit">单位：kg</span>
      </div>
      <div class="bg-content">
        <div v-if="productAnalysisShow" class="center-center">暂无数据</div>
        <div v-else style="width: 100%; height: 100%">
          <div id="productEcharts"></div>
        </div>
      </div>
    </div>
    <div class="content-right">
      <!-- <el-button class="sino-button-sure" @click="collectRecordChange">showDialog</el-button> -->
    </div>
    <template v-if="collectRecordShow">
      <collectRecord ref="collectRecord" :location="location" :ssmType="ssmType" :dialogShow="collectRecordShow" @configCloseDialog="configCloseDialog"></collectRecord>
    </template>
  </div>
</template>
<script>
import $ from 'jquery'
import * as echarts from 'echarts'
import collectRecord from './component/collectRecord.vue'
import { getDepartmentMedicalWasteInfo, getTypeAnalysisInfo, getMedicalWasteDisposalInfo, getMedicalWasteTrendInfo } from '@/utils/centerScreenApi'
export default {
  name: 'businessIMWS',
  components: {
    collectRecord
  },
  data() {
    return {
      disposeData: {
        confirmWeigh: 0,
        issuedWeigh: 0,
        processEndWeigh: 0,
        stockInWeigh: 0,
        timeoutWeigh: 0,
        waitWeigh: 0
      },
      collectRecordShow: false,
      analysisShow: true,
      deptTopShow: true,
      productAnalysisShow: true,
      mTime: null,
      getPiechart: null,
      timer: null,
      location: '',
      ssmType: ''
    }
  },
  mounted() {
    // 初始化 获取定位信息
    if (Object.hasOwn(this.$route.query, 'localtion') || Object.hasOwn(this.$route.query, 'ssmType')) {
      this.location = this.$route.query.localtion || ''
      this.ssmType = this.$route.query.ssmType || ''
    }
    try {
      window.chrome.webview.addEventListener('message', (event) => {
        // alert(event.data)
        const data = JSON.parse(event.data)
        this.location = data.localtion || ''
        this.ssmType = data.ssmType || ''
        if (data.type === 'area') {
          this.search()
        } else if (data.type === 'tag') {
          this.collectRecordShow = true
          this.$nextTick(() => {
            this.$refs.collectRecord.getDepartMedicalWasteTableList()
          })
          window.chrome.webview.hostObjects.sync.bridge.ShowTable(true)
        }
      })
    } catch (errpr) {}
    this.search()
    this.timer = setInterval(() => {
      setTimeout(() => {
        this.search()
      }, 0)
    }, 1000 * timerNum)
    this.$once('hook:beforeDestroy', () => {
      clearInterval(this.timer)
      this.timer = null
    })
  },
  methods: {
    search() {
      this.activeTop5Event('#top5Week', 'week')
      this.activeAnalysisEvent('#deptWeek', 'week')
      this.getMedicalWasteTrendInfo()
      this.getMedicalWasteDisposalInfo()
    },
    getMedicalWasteDisposalInfo() {
      getMedicalWasteDisposalInfo({}).then((res) => {
        const data = res.data
        if (data.code === '200') {
          if (data.data.list.length) {
            this.disposeData = data.data.list[0]
          }
        }
      })
    },
    // 医废基本情况分析
    getTypeAnalysisInfo(type) {
      getTypeAnalysisInfo({ dateType: type }).then((res) => {
        const data = res.data
        if (data.code === '200') {
          if (data.data.list.length) {
            this.analysisShow = false
            const arr = data.data.list
            const total = data.data.sumWeigh
            this.$nextTick(() => {
              this.departmentEchart(arr, total)
            })
          } else {
            this.analysisShow = true
          }
        }
      })
    },
    // 分析echarts
    departmentEchart(arr, total) {
      this.getPiechart = echarts.init(document.getElementById('BasicAnalysisEcharts'))
      const data = []
      var color = [
        'rgba(31, 250, 255, 0.3)',
        'rgba(255, 227, 166, 0.28)',
        'rgba(147, 130, 255, 0.26)',
        'rgba(0, 248, 114, 0.24)',
        'rgba(46, 119, 251, 0.3)',
        'rgba(255, 84, 84, 0.3)',
        'rgba(123, 255, 193, 0.3)'
      ]
      var borderColor = ['rgba(31, 250, 255, 1)', 'rgba(255, 227, 166, 1)', 'rgba(147, 130, 255, 1)', 'rgba(0, 248, 114, 1)', 'rgba(46, 119, 251, 1)', 'rgba(255, 84, 84, 1)', 'rgba(123, 255, 193, 1)']
      var xdata = arr.map((item) => {
        return item.wasteType
      })
      for (var i = 0; i < arr.length; i++) {
        data.push({
          name: arr[i].wasteType,
          value: arr[i].gatherWeigh,
          itemStyle: {
            normal: {
              borderWidth: 1,
              shadowBlur: 200,
              borderColor: borderColor[i],
              color: color[i],
              label: {
                normal: { show: true }
              }
            }
          }
        })
      }
      var seriesObj = [
        {
          name: '',
          type: 'pie',
          clockWise: false,
          center: ['50%', '40%'],
          radius: ['40%', '55%'],
          // hoverAnimation: false,
          label: {
            normal: {
              show: false,
              position: 'outside',
              formatter: '{b}\n {c}kg\n {d}%',
              textStyle: {
                fontSize: '12',
                color: '#fff'
              }
            },
            emphasis: {
              show: true,
              textStyle: {
                fontSize: '12',
                color: '#fff'
              }
            }
          },
          data: data
        }
      ]
      const option = {
        backgroundColor: '',
        // backgroundColor: {
        //   type: 'pattern',
        //   repeat: 'no-repeat',
        //   image: require('../../../assets/images/peace/people-right-top-bg.png') // 赋值
        // },
        title: {
          text: '总重量',
          subtext: total + 'kg',
          textStyle: {
            color: '#CFE1F7',
            fontSize: 12
          },
          subtextStyle: {
            fontSize: 16,
            color: '#FFFFFF'
          },
          x: 'center',
          y: '30%'
        },
        legend: {
          orient: 'horizontal',
          left: 'center',
          bottom: '0',
          data: xdata,
          itemWidth: 8,
          itemHeight: 8,
          itemGap: 16,
          textStyle: {
            color: '#B3C2DD' //  字体颜色
          }
        },
        // formatter: function (name) {
        //   var oa = option.series[0].data
        //   for (var i = 0; i < option.series[0].data.length; i++) {
        //     if (name === oa[i].name) {
        //       return ' ' + name + ' (' + oa[i].value + ')     '
        //     }
        //   }
        // },
        series: seriesObj
      }
      this.getPiechart.setOption(option)
      if (data.length > 1) {
        clearInterval(this.mTime)
        this.pieEchartSetTimeOut(data)
      }
      // 鼠标移出后默认高亮
      this.getPiechart.on('mouseout', () => {
        clearInterval(this.mTime)
        this.pieEchartSetTimeOut(data)
      })
      this.getPiechart.clear()
      this.getPiechart.setOption(option)
      // 随着屏幕大小调节图表
      window.addEventListener('resize', () => {
        this.getPiechart.resize()
      })
    },
    pieEchartSetTimeOut(data) {
      let index = -1 // 高亮所在下标
      const dataLength = data.length // 当前饼图有多少个扇形
      // 用定时器控制饼图高亮
      this.mTime = setInterval(() => {
        // 清除之前的高亮
        this.getPiechart.dispatchAction({
          type: 'downplay',
          seriesIndex: 0,
          dataIndex: index
        })
        index = (index + 1) % dataLength
        // 当前下标高亮
        this.getPiechart.dispatchAction({
          type: 'highlight',
          seriesIndex: 0,
          dataIndex: index
        })
        if (index > dataLength) {
          index = 0
        }
      }, 3000)
      // 鼠标划入
      this.getPiechart.on('mouseover', () => {
        // 停止定时器，清除之前的高亮
        clearInterval(this.mTime)
        this.getPiechart.dispatchAction({
          type: 'downplay',
          seriesIndex: 0,
          dataIndex: index
        })
      })
    },
    getMedicalWasteTrendInfo() {
      getMedicalWasteTrendInfo({}).then((res) => {
        const data = res.data
        if (data.code === '200') {
          const arr = data.data.list
          if (arr.length) {
            this.productAnalysisShow = false
            this.$nextTick(() => {
              this.analysisLineEchart(arr)
            })
          } else {
            this.productAnalysisShow = true
          }
        }
      })
    },
    analysisLineEchart(arr) {
      const getchart = echarts.init(document.getElementById('productEcharts'))
      getchart.resize()
      const name = Array.from(arr, ({ gatherTime }) => gatherTime)
      // console.log(arr, name)
      const Infectedweigh = Array.from(arr, ({ Infectedweigh }) => Infectedweigh)
      const chemistryweigh = Array.from(arr, ({ chemistryweigh }) => chemistryweigh)
      const damageweigh = Array.from(arr, ({ damageweigh }) => damageweigh)
      const epidemicweigh = Array.from(arr, ({ epidemicweigh }) => epidemicweigh)
      const medicineweigh = Array.from(arr, ({ medicineweigh }) => medicineweigh)
      const pathoweigh = Array.from(arr, ({ pathoweigh }) => pathoweigh)
      const otherweigh = Array.from(arr, ({ otherweigh }) => otherweigh)
      const option = {
        backgroundColor: '',
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            lineStyle: {
              color: '#FFE3A6'
            }
          },
          backgroundColor: '#0C2269',
          borderColor: '#5B617C',
          textStyle: {
            color: '#719DE7'
          }
          // ,
          // formatter: function (params) {
          //   console.log(params)
          //   let html = params[0].name + '<br/>'
          //   params.forEach(e => {
          //     html += e.marker + e.seriesName + '：' + e.value + 'kg' + '<br/>'
          //   })
          //   return html
          // }
        },
        grid: {
          top: '6%',
          left: '4%',
          right: '10%',
          bottom: '12%'
        },
        legend: {
          textStyle: { color: '#fff' },
          right: 30,
          y: 'center',
          orient: 'vertical'
        },
        // 保存为图片
        // toolbox: {
        //   feature: {
        //     saveAsImage: {}
        //   }
        // },
        xAxis: {
          type: 'category',
          data: name,
          boundaryGap: false,
          axisTick: {
            show: false
          },
          axisLine: {
            lineStyle: {
              color: '#303F69'
            }
          },
          axisLabel: {
            textStyle: {
              fontSize: 10,
              color: '#8C9EB5'
            }
          }
        },
        yAxis: {
          type: 'value',
          axisTick: {
            show: false
          },
          // axisLine: {
          //   lineStyle: {
          //     color: '#609ee9'
          //   }
          // },
          axisLabel: {
            margin: 10,
            textStyle: {
              fontSize: 10,
              color: '#8C9EB5'
            }
          },
          splitLine: {
            show: true,
            lineStyle: {
              color: ['#303F69'],
              width: 1,
              type: 'solid'
            }
          }
        },
        series: [
          {
            name: '损伤类',
            type: 'line',
            smooth: true,
            showSymbol: false,
            symbol: 'circle',
            symbolSize: 6,
            data: damageweigh,
            itemStyle: {
              normal: {
                color: '#2AF598'
              }
            },
            lineStyle: {
              normal: {
                width: 2
              }
            }
          },
          {
            name: '病理类',
            type: 'line',
            smooth: true,
            showSymbol: false,
            symbol: 'circle',
            symbolSize: 6,
            data: pathoweigh,
            itemStyle: {
              normal: {
                color: '#FFE58F'
              }
            },
            lineStyle: {
              normal: {
                width: 2
              }
            }
          },
          {
            name: '化学类',
            type: 'line',
            smooth: true,
            showSymbol: false,
            symbol: 'circle',
            symbolSize: 6,
            data: chemistryweigh,
            itemStyle: {
              normal: {
                color: '#7CAEFF'
              }
            },
            lineStyle: {
              normal: {
                width: 2
              }
            }
          },
          {
            name: '感染类',
            type: 'line',
            smooth: true,
            showSymbol: false,
            symbol: 'circle',
            symbolSize: 6,
            data: Infectedweigh,
            itemStyle: {
              normal: {
                color: '#FFC690'
              }
            },
            lineStyle: {
              normal: {
                width: 2
              }
            }
          },
          {
            name: '药物类',
            type: 'line',
            smooth: true,
            showSymbol: false,
            symbol: 'circle',
            symbolSize: 6,
            data: medicineweigh,
            itemStyle: {
              normal: {
                color: '#FF6060'
              }
            },
            lineStyle: {
              normal: {
                width: 2
              }
            }
          },
          {
            name: '涉疫类',
            type: 'line',
            smooth: true,
            showSymbol: false,
            symbol: 'circle',
            symbolSize: 6,
            data: epidemicweigh,
            itemStyle: {
              normal: {
                color: 'rgba(0, 248, 114, 1)'
              }
            },
            lineStyle: {
              normal: {
                width: 2
              }
            }
          },
          {
            name: '其他类',
            type: 'line',
            smooth: true,
            showSymbol: false,
            symbol: 'circle',
            symbolSize: 6,
            data: otherweigh,
            itemStyle: {
              normal: {
                color: 'rgba(147, 130, 255, 1)'
              }
            },
            lineStyle: {
              normal: {
                width: 2
              }
            }
          }
        ]
      }
      getchart.clear()
      getchart.setOption(option)
      // 随着屏幕大小调节图表
      window.addEventListener('resize', () => {
        getchart.resize()
      })
    },
    // top5
    getDepartmentMedicalWasteInfo(type) {
      getDepartmentMedicalWasteInfo({ dateType: type, ssmType: 1, spatialId: '', currentPage: 1, pageSize: 5 }).then((res) => {
        const data = res.data
        if (data.code === '200') {
          const arr = data.data.list
          if (arr.length) {
            const sortArr = arr.sort((a, b) => a.totalweigh - b.totalweigh)
            this.deptTopShow = false
            this.$nextTick(() => {
              this.getDeptProduceEcharts(sortArr)
            })
          } else {
            this.deptTopShow = true
          }
        }
      })
    },
    // top5 echarts
    getDeptProduceEcharts(arr) {
      const getchart = echarts.init(document.getElementById('deptProduceEcharts'))
      const name = Array.from(arr, ({ officeName }) => officeName)
      const Infectedweigh = Array.from(arr, ({ Infectedweigh }) => Infectedweigh)
      const chemistryweigh = Array.from(arr, ({ chemistryweigh }) => chemistryweigh)
      const damageweigh = Array.from(arr, ({ damageweigh }) => damageweigh)
      const epidemicweigh = Array.from(arr, ({ epidemicweigh }) => epidemicweigh)
      const medicineweigh = Array.from(arr, ({ medicineweigh }) => medicineweigh)
      const pathoweigh = Array.from(arr, ({ pathoweigh }) => pathoweigh)
      const otherweigh = Array.from(arr, ({ otherweigh }) => otherweigh)
      const option = {
        backgroundColor: '',
        tooltip: {
          trigger: 'axis',
          backgroundColor: '#0D2169',
          borderColor: '#727382',
          textStyle: {
            color: '#fff'
          },
          axisPointer: {
            // 坐标轴指示器，坐标轴触发有效
            type: 'shadow' // 默认为直线，可选为：'line' | 'shadow'
          }
        },
        grid: {
          x: '12%',
          width: '75%',
          // y: '22%'
          bottom: '18%'
        },
        legend: {
          orient: 'horizontal',
          left: 'center',
          top: '0',
          // data: xdata,
          itemWidth: 8,
          itemHeight: 8,
          itemGap: 16,
          textStyle: {
            color: '#B3C2DD' //  字体颜色
          }
        },
        xAxis: {
          type: 'category',
          axisLine: {
            show: true,
            lineStyle: {
              color: '#878EA9',
              width: 1,
              type: 'solid'
            }
          },
          // boundaryGap: true,
          axisTick: {
            show: false
          },
          axisLabel: {
            margin: 8,
            interval: 0,
            formatter: function (val) {
              var strs = val.split('') // 字符串数组
              var str = ''
              for (var i = 0, s; (s = strs[i++]);) {
                // 遍历字符串数组
                // 遍历字符串数组
                str += s
                if (!(i % 10)) str += '\n' // 按需要求余
              }
              return str
            }
          },
          data: name
        },
        yAxis: {
          type: 'value',
          axisLabel: {
            show: true,
            textStyle: {
              color: '#878EA9'
            }
          },
          axisLine: {
            lineStyle: {
              type: 'solid',
              color: '#3862B7',
              width: '1'
            }
          },
          splitLine: {
            lineStyle: {
              color: '#314A89',
              width: 1,
              type: 'solid'
            }
          },
          axisTick: {
            show: false
          }
        },
        series: [
          {
            name: '损伤类',
            data: damageweigh,
            type: 'bar',
            stack: 'one',
            barWidth: '6',
            itemStyle: {
              color: new echarts.graphic.LinearGradient(0, 1, 0, 0, [
                {
                  offset: 0,
                  color: 'rgba(175, 255, 217, 0.2)'
                },
                {
                  offset: 1,
                  color: '#2AF598 '
                }
              ])
            }
          },
          {
            name: '病理类',
            data: pathoweigh,
            type: 'bar',
            stack: 'one',
            barWidth: '6',
            itemStyle: {
              color: new echarts.graphic.LinearGradient(0, 1, 0, 0, [
                {
                  offset: 0,
                  color: 'rgba(255, 229, 143, 0.2)'
                },
                {
                  offset: 1,
                  color: '#FFE58F '
                }
              ])
            }
          },
          {
            name: '化学类',
            data: chemistryweigh,
            type: 'bar',
            stack: 'one',
            barWidth: '6',
            itemStyle: {
              color: new echarts.graphic.LinearGradient(0, 1, 0, 0, [
                {
                  offset: 0,
                  color: 'rgba(124, 174, 255, 0.2)'
                },
                {
                  offset: 1,
                  color: '#7CAEFF'
                }
              ])
            }
          },
          {
            name: '感染类',
            data: Infectedweigh,
            type: 'bar',
            stack: 'one',
            barWidth: '6',
            itemStyle: {
              color: new echarts.graphic.LinearGradient(0, 1, 0, 0, [
                {
                  offset: 0,
                  color: 'rgba(255, 198, 144, 0.2)'
                },
                {
                  offset: 1,
                  color: '#FFC690'
                }
              ])
            }
          },
          {
            name: '药物类',
            data: medicineweigh,
            type: 'bar',
            stack: 'one',
            barWidth: '6',
            itemStyle: {
              color: new echarts.graphic.LinearGradient(0, 1, 0, 0, [
                {
                  offset: 0,
                  color: 'rgba(255, 96, 96, 0.2)'
                },
                {
                  offset: 1,
                  color: '#FF6060'
                }
              ])
            }
          },
          {
            name: '涉疫类',
            data: epidemicweigh,
            type: 'bar',
            stack: 'one',
            barWidth: '6',
            itemStyle: {
              color: new echarts.graphic.LinearGradient(0, 1, 0, 0, [
                {
                  offset: 0,
                  color: 'rgba(0, 248, 114, 0.2)'
                },
                {
                  offset: 1,
                  color: 'rgba(0, 248, 114, 1) '
                }
              ])
            }
          },
          {
            name: '其他类',
            data: otherweigh,
            type: 'bar',
            stack: 'one',
            barWidth: '6',
            itemStyle: {
              color: new echarts.graphic.LinearGradient(0, 1, 0, 0, [
                {
                  offset: 0,
                  color: 'rgba(147, 130, 255, 0.2)'
                },
                {
                  offset: 1,
                  color: 'rgba(147, 130, 255, 1)'
                }
              ])
            }
          }
        ],
        dataZoom: [
          {
            xAxisIndex: 0,
            show: true,
            type: 'slider',
            startValue: 0,
            endValue: 0,
            height: 8,
            bottom: '0%',
            borderColor: 'rgba(43,48,67,.1)',
            fillerColor: '#6580b8',
            zoomLock: false,
            showDataShadow: false,
            backgroundColor: 'rgba(43,48,67,.1)',
            showDetail: false,
            realtime: true,
            filterMode: 'filter',
            handleIcon: 'M-9.35,34.56V42m0-40V9.5m-2,0h4a2,2,0,0,1,2,2v21a2,2,0,0,1-2,2h-4a2,2,0,0,1-2-2v-21A2,2,0,0,1-11.35,9.5Z',
            handleStyle: {
              color: '#6580b8',
              borderColor: '#6580b8'
            },
            moveOnMouseMove: true,
            maxValueSpan: name.length,
            minValueSpan: 2,
            brushSelect: false
          },
          {
            type: 'inside',
            show: false,
            height: 8,
            start: 100,
            end: 100
          }
        ]
      }
      getchart.clear()
      getchart.setOption(option)
      // 随着屏幕大小调节图表
      window.addEventListener('resize', () => {
        getchart.resize()
      })
    },
    activeTop5Event(type, val) {
      $('#top5Week').removeClass('active')
      $('#top5Month').removeClass('active')
      $('#top5Year').removeClass('active')
      $(type).addClass('active')
      this.getDepartmentMedicalWasteInfo(val)
    },
    activeAnalysisEvent(type, val) {
      $('#deptWeek').removeClass('active')
      $('#deptMonth').removeClass('active')
      $('#deptYear').removeClass('active')
      $(type).addClass('active')
      this.getTypeAnalysisInfo(val)
    },
    collectRecordChange() {
      this.collectRecordShow = !this.collectRecordShow
      this.$nextTick(() => {
        this.$refs.collectRecord.getDepartMedicalWasteTableList()
      })
    },
    configCloseDialog(dialogName) {
      this[dialogName] = false
      try {
        window.chrome.webview.hostObjects.sync.bridge.ShowTable(false)
      } catch (error) {}
    },
    setWPFborder() {
      const json = {
        right: 22,
        bottom: 32
      }
      window.chrome.webview.hostObjects.sync.bridge.SetLocation(JSON.stringify(json))
    }
  }
}
</script>
<style lang="scss" scoped>
.content {
  width: 100%;
  height: 100%;
  position: relative;
  .active {
    background: url('~@/assets/images/center/light-yellow.png') no-repeat;
    background-size: 100% 100%;
    color: #ffe3a6 !important;
    outline: none;
  }
  .center-center {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #4d5880;
    font-size: 16px;
  }
  .sham-content {
    pointer-events: none;
  }
  .right-content {
    position: absolute;
    top: 0;
    right: 0;
    width: 20%;
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    .echarts-top {
      width: 100%;
      height: 35%;
      background: url('~@/assets/images/peace/emergency-disposal-left.png') no-repeat;
      background-size: 100% 100%;
      padding: 2px;
      box-sizing: border-box;
      .alarm-analysis {
        display: flex;
        justify-content: space-around;
        box-sizing: border-box;
        width: 100%;
        height: 50%;
        .case-img {
          width: 18%;
          height: 85%;
          margin: auto;
          position: relative;
          img {
            width: 100%;
            height: 100%;
          }
          .case-num {
            position: absolute;
            width: 4rem;
            text-align: center;
            top: 8%;
            color: #fff;
            font-size: 1.1rem;

            font-family: DIN-Bold, DIN;
            font-weight: bold;
            span {
              font-weight: 400;
              font-size: 0.75rem;
            }
          }
          .case-anim-icon {
            position: absolute;
            bottom: 10%;
            left: calc(50% - 1rem);
            width: 2rem;
            height: 2rem;
            background: url('~@/assets/images/peace/icon-weight.png') no-repeat;
            background-size: 100% 100%;
            animation: jump 1s ease-out infinite alternate-reverse;
          }
        }
      }
      .right-title-unit {
        font-size: 12px;
        font-family: PingFang-SC-Medium, PingFang-SC;
      }
    }
    .echarts-center {
      width: 100%;
      height: 35%;
      background: url('~@/assets/images/peace/emergency-disposal-left.png') no-repeat;
      background-size: 100% 100%;
      padding: 2px;
      box-sizing: border-box;
      .right-search {
        span {
          width: 35px;
        }
      }
    }
    .echarts-bottom {
      width: 100%;
      height: 30%;
      background: url('~@/assets/images/peace/emergency-disposal-left.png') no-repeat;
      background-size: 100% 100%;
      .right-search {
        span {
          width: 35px;
        }
      }
    }
  }
  .bottom-content {
    height: 30%;
    width: 80%;
    position: absolute;
    top: 70%;
    background: url('~@/assets/images/peace/bg-24.png') no-repeat;
    background-size: 100% 100%;
    .bg-title {
      height: 23%;
      justify-content: flex-start;
      .title-unit {
        font-size: 12px;
        font-family: PingFang-SC-Medium, PingFang-SC;
        margin-left: 18px;
      }
    }
    // pointer-events: none;
  }
  .bg-title {
    margin-top: 5px;
    // height: 23%;
    height: 3.2rem;
    line-height: 3.2rem;
    // color: #d4e3f9;
    color: #dceaff;
    padding: 0 5rem;
    font-family: TRENDS;
    /*设置为伸缩容器*/
    display: -webkit-box;
    display: -moz-box;
    display: -ms-flexbox;
    display: -webkit-flex;
    display: flex;
    /*垂直居中*/
    -webkit-box-align: center; /*旧版本*/
    -moz-box-align: center; /*旧版本*/
    -ms-flex-align: center; /*混合版本*/
    -webkit-align-items: center; /*新版本*/
    align-items: center; /*新版本*/
    justify-content: space-between;
    .right-search {
      span {
        display: inline-block;
        width: 50px;
        height: 30px;
        font-size: 14px;
        text-align: center;
        line-height: 26px;
        margin: 0 4px;
        color: #7eaef9;
        cursor: pointer;
      }
      span:hover {
        background: url('~@/assets/images/center/light-yellow.png') no-repeat;
        background-size: 100% 100%;
        color: #ffe3a6;
      }
      i {
        color: #7eaef9;
      }
    }
  }
  .bg-content {
    position: relative;
    box-sizing: border-box;
    padding: 10px 10px 15px 10px;
    width: 100%;
    height: calc(100% - 3.2rem);
    #BasicAnalysisEcharts,
    #deptProduceEcharts,
    #productEcharts {
      width: 100%;
      height: 100%;
    }
    .basic-bg {
      // width: 15rem;
      height: 80%;
      aspect-ratio: 1/1;
      position: absolute;
      top: 0;
      left: 50%;
      transform: translate(-50%, 0);
      background: url('~@/assets/images/peace/people-right-top-bg.png') no-repeat;
      background-size: 100% 100%;
    }
  }
}
</style>
